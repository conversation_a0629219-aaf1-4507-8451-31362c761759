// <copyright file="ActionPerEntityStrategy.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore.ChangeTracking;
using SOS.CalAccess.Data.EntityFramework.Audit.Diffing;

namespace SOS.CalAccess.Data.EntityFramework.Audit.Strategies;

/// <summary>
/// Auditing strategy implementation that generates an action to be logged
/// for each instance of a changed entity.
/// </summary>
public sealed class ActionPerEntityStrategy : IAuditingStrategy
{
    /// <inheritdoc />
    public ValueTask<IReadOnlyList<DataAction>> Audit(
        IEnumerable<EntityEntry> entries,
        IAuditDiffSerializer diffSerializer,
        DateTime issuedAt,
        CancellationToken token = default)
    {
        var actions = entries.Select(e => new DataAction(e, issuedAt, diffSerializer)).ToList();

        return new(actions);
    }
}
