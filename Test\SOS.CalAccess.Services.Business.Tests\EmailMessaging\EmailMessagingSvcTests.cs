using System.Collections.ObjectModel;
using NSubstitute;
using SOS.CalAccess.Data.Email;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Email;
using SOS.CalAccess.Services.Common.Email.Model;
using SOS.CalAccess.Services.Common.EmailMessaging;
using SOS.CalAccess.Services.Common.Queuing;

namespace SOS.CalAccess.Services.Business.Tests.EmailMessaging;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(EmailSvc))]
public class EmailMessagingSvcTests
{
    private EmailSvc _emailSvc;
    private IEmailMessageRepository _messageRepository;
    private IMessageQueueSvc _messageQueueSvc;
    private IDateTimeSvc _dateTimeSvc;
    private EmailMessagingOptions _emailMessagingOptions;
    private const long FilerId = 123456;
    private const string Subject = "New email";
    private const string Message = "Test SMS message";
    private const string TemplateId = "123";
    private readonly object TemplateData = /*lang=json,strict*/ @"{""Subject"": ""test subject"", ""Body"": ""test body"", ""Template"":""test template""}";
    private DateTime _dateNow;


    /// <summary>
    /// Setting up objects
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _messageQueueSvc = Substitute.For<IMessageQueueSvc>();
        _messageRepository = Substitute.For<IEmailMessageRepository>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _emailMessagingOptions = new EmailMessagingOptions("<EMAIL>");
        _emailSvc = new EmailSvc(_messageQueueSvc, _messageRepository, _emailMessagingOptions, _dateTimeSvc);
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

    }

    /// <summary>
    /// Create, Send and Update email message. Throw falilure on exception
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_EmailMessage_ThrowFailureOnException()
    {
        //Arrange
        var emailRecipient = new Collection<CalAccessEmailRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      EmailAddress= "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                }
            };

        var request = new EmailMessageRequest(emailRecipient, FilerId, Subject, Message);

        _messageRepository.Create(Arg.Any<EmailMessage>()).Returns(args => Task.FromResult((EmailMessage)args[0]));

        //Act
        await _emailSvc.SendPlainEmail(request);

        // Assert
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Create, send and update email message throws an exception when message passed as null
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_EmailMessage_ThrowMessagePassNull()
    {
        //Arrange
        var emailRecipient = new Collection<CalAccessEmailRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      EmailAddress= "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                }
            };

        var request = new EmailMessageRequest(emailRecipient, FilerId, Subject, "");

        _messageRepository.Create(Arg.Any<EmailMessage>()).Returns(args => Task.FromResult((EmailMessage)args[0]));

        //Act
        try
        {
            await _emailSvc.SendPlainEmail(request);
            Assert.Fail("Expected an exception to be thrown.");
        }
        catch (Exception)
        {
        }

        // Assert
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Create, send and update email message throws an exception when any email address passed as empty
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_EmailMessage_ThrowEmailAddressPassNull()
    {
        //Arrange
        var emailRecipient = new Collection<CalAccessEmailRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      EmailAddress = "",
                      Name = "Jon Doe"
                }
            };

        var request = new EmailMessageRequest(emailRecipient, FilerId, Subject, Message);

        _messageRepository.Create(Arg.Any<EmailMessage>()).Returns(args => Task.FromResult((EmailMessage)args[0]));

        //Act
        try
        {
            await _emailSvc.SendPlainEmail(request);
            Assert.Fail("Expected an exception to be thrown.");
        }
        catch (Exception)
        {
        }

        // Assert
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Create, send and update email message throws an exception when any subject passed as empty
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_EmailMessage_ThrowEmailSubjectPassNull()
    {
        //Arrange
        var emailRecipient = new Collection<CalAccessEmailRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                }
            };

        var request = new EmailMessageRequest(emailRecipient, FilerId, "", Message);

        _messageRepository.Create(Arg.Any<EmailMessage>()).Returns(args => Task.FromResult((EmailMessage)args[0]));

        //Act
        try
        {
            await _emailSvc.SendPlainEmail(request);
            Assert.Fail("Expected an exception to be thrown.");
        }
        catch (Exception)
        {
        }

        // Assert
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Create, Send and Update email template message. Throw falilure on exception
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_EmailTemplateMessage_ThrowFailureOnException()
    {
        //Arrange
        var emailRecipient = new Collection<CalAccessEmailRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      EmailAddress= "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                }
            };

        var request = new EmailTemplateRequest(emailRecipient, FilerId, TemplateId, TemplateData);

        _messageRepository.Create(Arg.Any<EmailMessage>()).Returns(args => Task.FromResult((EmailMessage)args[0]));

        //Act
        await _emailSvc.SendTemplatedEmail(request);

        // Assert
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Create, send and update email message throws an exception when any email address passed as empty
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_EmailTemplateMessage_ThrowEmailAddressPassNull()
    {
        //Arrange
        var emailRecipient = new Collection<CalAccessEmailRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      EmailAddress = "<EMAIL>",
                      Name = "Jon Doe"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      EmailAddress = "",
                      Name = "Jon Doe"
                }
            };

        var request = new EmailTemplateRequest(emailRecipient, FilerId, TemplateId, TemplateData);

        _messageRepository.Create(Arg.Any<EmailMessage>()).Returns(args => Task.FromResult((EmailMessage)args[0]));

        //Act
        try
        {
            await _emailSvc.SendTemplatedEmail(request);
            Assert.Fail("Expected an exception to be thrown.");
        }
        catch (Exception)
        {
        }

        // Assert
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.EmailRequest, Arg.Is<EmailMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Update email status with valid provider id with status sent
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateEmailStatus_WithValidProviderId_UpdatesEmailMessage()
    {
        // Arrange
        var providerId = "12345";
        var emailMessage = new EmailMessage
        {
            Id = 1,
            FromEmailAddress = "<EMAIL>",
            ToEmailAddress = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            DateUpdated = _dateNow
        };

        _messageRepository.FindByProviderId(providerId).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(providerId, "Sent", "None", "error", "1", "STMP_Id", "sg_event_Id", new DateTime(2025, 6, 1, 12, 0, 1, DateTimeKind.Local));

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.Status == "Sent"
        ));
    }

    /// <summary>
    /// Update email status with valid provider id with status delivered
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateEmailStatus_WithValidProviderId_StatusDelivered()
    {
        // Arrange
        var providerId = "12345";
        var emailMessage = new EmailMessage
        {
            Id = 1,
            FromEmailAddress = "<EMAIL>",
            ToEmailAddress = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            DateUpdated = _dateNow
        };

        _messageRepository.FindByProviderId(providerId).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(providerId, "delivered", "None", "error", "1", "STMP_Id", "sg_event_Id", new DateTime(2025, 6, 1, 12, 0, 1, DateTimeKind.Local));

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.Status == "delivered"
        ));
    }

    /// <summary>
    /// Update email status with valid provider id with status bounce
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateEmailStatus_WithValidProviderId_StatusBounce()
    {
        // Arrange
        var providerId = "12345";
        var emailMessage = new EmailMessage
        {
            Id = 1,
            FromEmailAddress = "<EMAIL>",
            ToEmailAddress = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            DateUpdated = _dateNow
        };

        _messageRepository.FindByProviderId(providerId).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(providerId, "bounce", "None", "error", "1", "STMP_Id", "sg_event_Id", new DateTime(2025, 6, 1, 12, 0, 1, DateTimeKind.Local));

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.Status == "bounce"
        ));
    }

    /// <summary>
    /// Update email status with valid provider id with status dropped
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateEmailStatus_WithValidProviderId_StatusDropped()
    {
        // Arrange
        var providerId = "12345";
        var emailMessage = new EmailMessage
        {
            Id = 1,
            FromEmailAddress = "<EMAIL>",
            ToEmailAddress = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            DateUpdated = _dateNow
        };

        _messageRepository.FindByProviderId(providerId).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(providerId, "dropped", "None", "error", "1", "STMP_Id", "sg_event_Id", new DateTime(2025, 6, 1, 12, 0, 1, DateTimeKind.Local));

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.Status == "dropped"
        ));
    }

    /// <summary>
    /// Update email status with valid provider id with status deferred
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateEmailStatus_WithValidProviderId_StatusDeferred()
    {
        // Arrange
        var providerId = "12345";
        var emailMessage = new EmailMessage
        {
            Id = 1,
            FromEmailAddress = "<EMAIL>",
            ToEmailAddress = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            DateUpdated = _dateNow
        };

        _messageRepository.FindByProviderId(providerId).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(providerId, "deferred", "None", "error", "1", "STMP_Id", "sg_event_Id", new DateTime(2025, 6, 1, 12, 0, 1, DateTimeKind.Local));

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.Status == "deferred"
        ));
    }

    /// <summary>
    /// Update email status with valid provider id with current utc date updated 
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task UpdateEmailStatus_WithValidProviderId_DateUpdated()
    {
        // Arrange
        var providerId = "12345";
        var emailMessage = new EmailMessage
        {
            Id = 1,
            FromEmailAddress = "<EMAIL>",
            ToEmailAddress = "<EMAIL>",
            Subject = "Test Subject",
            Body = "Test Body",
            DateUpdated = _dateNow
        };

        _messageRepository.FindByProviderId(providerId).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(providerId, "deferred", "None", "error", "1", "STMP_Id", "sg_event_Id", new DateTime(2025, 6, 1, 12, 0, 1, DateTimeKind.Local));

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.DateUpdated != default)
        );
    }

    [Test]
    public void UpdateEmailStatus_WhenRepositoryThrowsException_ThrowsInvalidOperationException()
    {
        // Arrange
        var providerId = "12345";
        _messageRepository.FindByProviderId(providerId).Returns(Task.FromException<EmailMessage>(new Exception("Database error")));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _emailSvc.UpdateEmailStatus(providerId, "Sent", "None", null, "1", "STMP_Id", "sg_event_Id", _dateNow));

        Assert.That(ex.Message, Is.EqualTo("Could not update the EmailStatus."));
    }


    [Test]
    public async Task UpdateEmailStatus_WithValidRequest_UpdatesEmailMessage()
    {
        // Arrange
        var updateRequest = new UpdateEmailRequest
        {
            Id = 1,
            Status = "Delivered",
            SendGridMessageId = "SG-67890",
            ErrorResponse = null
        };
        var emailMessage = new EmailMessage { Id = 1 };

        _messageRepository.FindById(updateRequest.Id).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(updateRequest);

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.Status == "Delivered" &&
                   msg.SendGridMessageId == "SG-67890" &&
                   msg.ErrorResponse == null
        ));
    }

    [Test]
    public async Task UpdateEmailStatus_WhenEmailMessageNotFound_ThrowsKeyNotFoundException_SendGrid()
    {
        // Arrange
        var updateRequest = new UpdateEmailRequest { Id = 1 };
        _messageRepository.FindById(updateRequest.Id).Returns((EmailMessage)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _emailSvc.UpdateEmailStatus(updateRequest));

        Assert.That(ex.Message, Is.EqualTo($"No email message found for ID: {updateRequest.Id}"));
    }


    [Test]
    public async Task UpdateEmailStatus_WhenRequestHasEmptyStatus_DoesNotUpdateStatusOrSendGridId()
    {
        // Arrange
        var updateRequest = new UpdateEmailRequest
        {
            Id = 1,
            Status = "",
            SendGridMessageId = "",
            ErrorResponse = "Some error"
        };
        var emailMessage = new EmailMessage { Id = 1 };

        _messageRepository.FindById(updateRequest.Id).Returns(emailMessage);

        // Act
        await _emailSvc.UpdateEmailStatus(updateRequest);

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<EmailMessage>(
            msg => msg.Status == null && // Should not update status
                   msg.SendGridMessageId == null && // Should not update SendGridMessageId
                   msg.ErrorResponse == "Some error"
        ));
    }

    [Test]
    public void UpdateEmailStatus_WhenRepositoryThrowsException_ThrowsInvalidOperationExceptionForSendGrid()
    {
        // Arrange
        var updateRequest = new UpdateEmailRequest { Id = 1 };
        _messageRepository.FindById(updateRequest.Id).Returns(Task.FromException<EmailMessage>(new Exception("Database error")));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _emailSvc.UpdateEmailStatus(updateRequest));

        Assert.That(ex.Message, Is.EqualTo("Could not update the EmailStatus."));
    }


}

