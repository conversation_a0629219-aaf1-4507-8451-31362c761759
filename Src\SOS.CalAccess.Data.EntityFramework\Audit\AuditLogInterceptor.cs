// <copyright file="AuditLogInterceptor.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using SOS.CalAccess.Data.EntityFramework.Audit.Attribution;
using SOS.CalAccess.Data.EntityFramework.Audit.Diffing;
using SOS.CalAccess.Data.EntityFramework.Audit.Sinks;
using SOS.CalAccess.Data.EntityFramework.Audit.Strategies;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Data.EntityFramework.Audit;

/// <summary>
/// Interceptor class that encapsulates the process that we enforce before changes are persisted to
/// the database to keep track and audit changes.
/// </summary>
/// <param name="attributionProvider">The source for attributing change authorship.</param>
/// <param name="auditingStrategy">The implementation to use to generate auditable actions.</param>
/// <param name="logSink">The destination to forward any create logs to.</param>
/// <param name="diffSerializer">The implementation to use to calculate diffs.</param>
public sealed class AuditLogInterceptor(
    IActionAttributionProvider attributionProvider,
    IAuditingStrategy auditingStrategy,
    IAuditLogSink logSink,
    IAuditDiffSerializer diffSerializer,
    IDateTimeSvc dateTimeSvc) : SaveChangesInterceptor
{
    private IReadOnlyList<DataAction>? _actions;

    /// <summary>
    /// Extract auditable changes from a set entity change tracking entries.
    /// </summary>
    /// <param name="entries">The set of changes to check.</param>
    /// <returns>the set of entries that qualifies for auditing.</returns>
    public static IEnumerable<EntityEntry> GetAuditableChanges(IEnumerable<EntityEntry> entries)
    {
        return entries
            .Where(e => e.State is not (EntityState.Detached or EntityState.Unchanged))
            .Where(e => e.Metadata.IsNotExcludedFromAudits());
    }

    /// <inheritdoc />
    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result)
    {
        throw new InvalidOperationException("Synchronous database operations are disallowed");
    }

    /// <inheritdoc />
    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        if (eventData.Context is { } context)
        {
            var auditableChanges = GetAuditableChanges(context.ChangeTracker.Entries());

            _actions = await auditingStrategy
                .Audit(auditableChanges, diffSerializer, dateTimeSvc.GetCurrentDateTime(), cancellationToken);
        }

        return result;
    }

    /// <inheritdoc />
    public override async Task SaveChangesFailedAsync(
        DbContextErrorEventData eventData,
        CancellationToken cancellationToken = default)
    {
        if (_actions is { Count: > 0 } payload)
        {
            var errorMessage = eventData.Exception.Message;
            var actor = attributionProvider.Performer;

            var logs = payload
                .Select(a => new AuditLog(a, dateTimeSvc, attributedTo: actor, error: errorMessage))
                .ToList();

            await logSink.ConsumeLogs(logs, cancellationToken);
        }
    }

    /// <inheritdoc />
    public override async ValueTask<int> SavedChangesAsync(
        SaveChangesCompletedEventData eventData,
        int result,
        CancellationToken cancellationToken = default)
    {
        if (_actions is { Count: > 0 } payload)
        {
            var actor = attributionProvider.Performer;

            var logs = payload
                .Select(a => new AuditLog(a, dateTimeSvc, attributedTo: actor))
                .ToList();

            await logSink.ConsumeLogs(logs, cancellationToken);
        }

        return result;
    }
}
