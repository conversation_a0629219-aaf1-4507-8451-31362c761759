using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using SOS.CalAccess.FilerPortal.Alerts;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.AmendSmoRegistration;
using SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;


[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(AmendCandidateRegistrationController))]
internal sealed class AmendSmoRegistrationControllerTests : IDisposable
{
    private ISmoRegistrationSvc _smoRegistrationSvcMock;
    private IStringLocalizer<SharedResources> _localizerMock;
    private ISmoRegistrationCtlSvc _smoRegistrationCtlSvcMock;
    private IAmendSmoRegistrationCtlSvc _amendSmoRegistrationCtlSvcMock;
    private IToastService _toastServiceMock;
    private IAccuMailValidatorService _accuMailValidatorSvcMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private DateTime DateNow;

    private AmendSmoRegistrationController _controller;

    public void Dispose()
    {
        _controller.Dispose();
    }

    [SetUp]
    public void Setup()
    {
        _smoRegistrationSvcMock = Substitute.For<ISmoRegistrationSvc>();
        _localizerMock = Substitute.For<IStringLocalizer<SharedResources>>();
        _amendSmoRegistrationCtlSvcMock = Substitute.For<IAmendSmoRegistrationCtlSvc>();
        _smoRegistrationCtlSvcMock = Substitute.For<ISmoRegistrationCtlSvc>();
        _toastServiceMock = Substitute.For<IToastService>();
        _accuMailValidatorSvcMock = Substitute.For<IAccuMailValidatorService>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();

        var localizedString = new LocalizedString("key", "text");
        _localizerMock[Arg.Any<string>()].Returns(localizedString);

        _localizerMock[ResourceConstants.Verification].Returns(new LocalizedString(ResourceConstants.Verification, "Verification"));
        _localizerMock[ResourceConstants.FieldIsRequired].Returns(new LocalizedString(CommonResourceConstants.FieldIsRequired, "{0} is required."));
        _localizerMock[CommonResourceConstants.InvalidSubmission].Returns(new LocalizedString(CommonResourceConstants.InvalidSubmission, "InvalidSubmission"));

        var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
        _controller = new AmendSmoRegistrationController(
            _smoRegistrationCtlSvcMock,
            _amendSmoRegistrationCtlSvcMock,
            _smoRegistrationSvcMock,
            _accuMailValidatorSvcMock,
            _localizerMock,
            _toastServiceMock,
            _authorizationSvcMock)
        {
            TempData = tempData
        };
        DateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local); ;
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #region Shared
    [Test]
    public void OnActionExecuting_ShouldCallRedirectToDashboard()
    {
        // Arrange
        var actionContext = new ActionExecutingContext(
            new ActionContext
            {
                HttpContext = new DefaultHttpContext(),
                RouteData = new Microsoft.AspNetCore.Routing.RouteData(),
                ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
            },
            new List<IFilterMetadata>(),
            new Dictionary<string, object>()!,
            _controller
        );

        // Act & Assert
        Assert.DoesNotThrow(() => _controller.OnActionExecuting(actionContext));
    }

    [Test]
    public void Index_InvalidModelState_RedirectsToDashboard()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "Invalid input");
        long id = 123;

        // Act
        var result = _controller.Index(id);

        // Assert
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public void Index_ValidModelState_RedirectsToPage01()
    {
        // Arrange
        long id = 456;
        _controller.ModelState.Clear();

        // Act
        var result = _controller.Index(id);

        // Assert
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirectResult.ActionName, Is.EqualTo(nameof(AmendSmoRegistrationController.Page01)));
            Assert.That(redirectResult.RouteValues, Is.Not.Null); // Ensure RouteValues is not null
            Assert.That(redirectResult.RouteValues!["id"], Is.EqualTo(id)); // Use null-forgiving operator
        });
    }

    [Test]
    public void Edit_InvalidModelState_RedirectsToDashboard()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "Invalid input");
        long id = 123;

        // Act
        var result = _controller.Edit(id);

        // Assert
        var redirectResult = (NotFoundResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<NotFoundResult>());
        });
    }

    [Test]
    public void Edit_ValidModelState_RedirectsToPage01()
    {
        // Arrange
        long id = 456;
        _controller.ModelState.Clear();

        // Act
        var result = _controller.Edit(id);

        // Assert
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirectResult.ActionName, Is.EqualTo(nameof(AmendSmoRegistrationController.Page01)));
            Assert.That(redirectResult.RouteValues, Is.Not.Null); // Ensure RouteValues is not null
            Assert.That(redirectResult.RouteValues!["id"], Is.EqualTo(id)); // Use null-forgiving operator
        });
    }

    #region ViewOrViewAndComplete
    [Test]
    public async Task ViewOrViewAndComplete_IsValid_RedirectsToSummary()
    {
        _authorizationSvcMock.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(true);

        var result = await _controller.ViewOrViewAndComplete(1) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.EqualTo("SmoRegistration"));
            Assert.That(result.ActionName, Is.EqualTo("ViewOrViewAndComplete"));
        });
    }

    [Test]
    [TestCase(true, false)]
    [TestCase(false, false)]
    public async Task ViewOrViewAndComplete_NotValid_RedirectsToDashboard(bool modelStateError, bool isAuthorized)
    {
        _authorizationSvcMock.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(isAuthorized);

        if (modelStateError)
        {
            _controller.ModelState.AddModelError("key", "error");
        }

        var result = await _controller.ViewOrViewAndComplete(1) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result.ActionName, Is.EqualTo("Index"));
        });
    }
    #endregion

    [Test]
    public void Close_ActionIsCancel_DisplaysToastAndRedirects()
    {
        // Arrange
        var action = FormAction.Cancel;

        // Act
        var result = _controller.Close(action);

        // Assert
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Cancel_ValidModelState_CallsServiceAndRedirects()
    {
        // Arrange
        long id = 789;
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Cancel(id);

        // Assert
        var redirectResult = (RedirectToActionResult)result;
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Cancel_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        long id = 789;
        _controller.ModelState.AddModelError("error", "Invalid");

        // Act
        var result = await _controller.Cancel(id);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    #endregion

    #region Page01_Get
    [Test]
    public async Task Page01_Get_ModelStateValid_ReturnsViewWithModel()
    {
        // Arrange
        long id = 111;
        _controller.ModelState.Clear();
        var expectedModel = new AmendSmoRegistrationStep01ViewModel
        {
            OrganizationAddress = new() { Country = "Canada" }
        };

        _amendSmoRegistrationCtlSvcMock
            .GetPage01ViewModel(id, Arg.Any<CancellationToken>())
            .Returns(expectedModel);

        // Act
        var result = await _controller.Page01(id);

        // Assert
        var viewResult = (ViewResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(viewResult.Model, Is.EqualTo(expectedModel));
        });
    }

    [Test]
    public async Task Page01_Get_ModelIsNull_ReturnsDefaultViewModel()
    {
        // Arrange
        long id = 222;
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.Page01(id);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page01_Post
    [Test]
    public async Task Page01_Post_WithSaveAndCloseAction_SaveAndReturnToDashboard()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep01ViewModel { Id = 333, Action = FormAction.SaveAndClose };
        _controller.ModelState.Clear();
        _amendSmoRegistrationCtlSvcMock.Page01Submit(Arg.Any<AmendSmoRegistrationStep01ViewModel>(), Arg.Any<ModelStateDictionary>()).Returns(Task.FromResult(model.Id));

        // Act
        var result = await _controller.Page01(model);

        // Assert
        var actionResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(actionResult.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(actionResult.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public async Task Page01_Post_WithContinueAction_ReturnsSameView()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep01ViewModel { Id = 444, Action = FormAction.Continue };
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page01(model);

        // Assert
        var redirectResult = (RedirectToActionResult)result;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirectResult.ActionName, Is.EqualTo("Page02"));
        });
    }

    [Test]
    public async Task Page01_Post_WithCancelAction_CallsCancelAndRedirects()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep01ViewModel { Id = 555, Action = FormAction.Cancel };
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page01(model);

        // Assert
        var redirectResult = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Page01_Post_InvalidModelState_ReturnsViewWithModelErrors()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep01ViewModel { Id = 666, Action = FormAction.SaveAndClose };
        _controller.ModelState.AddModelError("error", "Invalid");

        // Act
        var result = await _controller.Page01(model);

        // Assert
        var viewResult = (ViewResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    [Test]
    public async Task Page01_Post_ValidModelState_HasAccuMailSuggestions_ReturnCurrentView()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep01ViewModel
        {
            Id = 666,
            Action = FormAction.SaveAndClose,
            Addresses = new List<AddressViewModel>
            {
                new ()
                {
                    Purpose = "Mailing"
                },
                new ()
                {
                    Purpose = "Organization"
                }
            }
        };
        _accuMailValidatorSvcMock
            .AccuMailValidationHandler(Arg.Any<IAccuMailValidation>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(true));

        // Act
        var result = await _controller.Page01(model);

        // Assert
        var viewResult = (ViewResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.True);
        });
    }

    [Test]
    public async Task Page01_Post_InvalidModelState_OnSave_ReturnCurrentView()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep01ViewModel
        {
            Id = 666,
            Action = FormAction.SaveAndClose,
        };
        _accuMailValidatorSvcMock
            .AccuMailValidationHandler(Arg.Any<IAccuMailValidation>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(false));
        _amendSmoRegistrationCtlSvcMock
            .Page01Submit(Arg.Any<AmendSmoRegistrationStep01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(call =>
            {
                var modelState = call.Arg<ModelStateDictionary>();
                modelState.AddModelError("Test", "Test error message");

                return Task.FromResult(model.Id);
            });

        // Act
        var result = await _controller.Page01(model);

        // Assert
        var viewResult = (ViewResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(viewResult.ViewName, Is.EqualTo("Page01"));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    [Test]
    public async Task Page01_Post_InvalidModelState_OnContinue_ReturnCurrentView()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep01ViewModel
        {
            Id = 666,
            Action = FormAction.Continue,
        };
        _accuMailValidatorSvcMock
            .AccuMailValidationHandler(Arg.Any<IAccuMailValidation>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(false));
        _amendSmoRegistrationCtlSvcMock
            .Page01Submit(Arg.Any<AmendSmoRegistrationStep01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(call =>
            {
                var modelState = call.Arg<ModelStateDictionary>();
                modelState.AddModelError("Test", "Test error message");

                return Task.FromResult(model.Id);
            });

        // Act
        var result = await _controller.Page01(model);

        // Assert
        var viewResult = (ViewResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(viewResult.ViewName, Is.EqualTo("Page01"));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }
    #endregion

    [Test]
    public async Task Page02_Get_WithValidId_ReturnsViewWithModel()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 1 };
        _smoRegistrationCtlSvcMock.GetPage04ViewModel(Arg.Any<long>(), Arg.Any<CancellationToken>())
            .Returns(model);

        // Act
        var result = await _controller.Page02(123);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page02_Get_WithInvalidModelState_ReturnsView()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 1 };
        _controller.ModelState.AddModelError("test", "error");
        _amendSmoRegistrationCtlSvcMock.GetPage02ViewModel(Arg.Any<long>(), Arg.Any<CancellationToken>())
            .Returns(model);

        // Act
        var result = await _controller.Page02(123);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page02_Post_WithValidModelAndContinue_RedirectsToPage03()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 123, Action = FormAction.Continue };
        _amendSmoRegistrationCtlSvcMock.Page02ContinueSubmit(Arg.Any<AmendSmoRegistrationDetailsStep01ViewModel>(), Arg.Any<ModelStateDictionary>(), true)
            .Returns(Task.FromResult((long?)123));
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public async Task Page02_Post_WithPreviousAction_RedirectsToPage01()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 123, Action = FormAction.Previous };
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public async Task Page02_Post_WithSaveAndCloseAction_RedirectsToDashboard()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 123, Action = FormAction.SaveAndClose };
        _amendSmoRegistrationCtlSvcMock.Page02ContinueSubmit(Arg.Any<AmendSmoRegistrationDetailsStep01ViewModel>(), Arg.Any<ModelStateDictionary>(), false)
            .Returns(Task.FromResult((long?)123));
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page02(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page02_Post_WithInvalidAction_AddsModelError()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 123, Action = (FormAction)999 };
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var view = (ViewResult)result;
        Assert.That(view.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page02_Post_WithInvalidModelState_ReturnsSameView()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 123, Action = FormAction.Continue };

        // Act
        var result = await _controller.Page02(model);

        // Assert
        var view = (ViewResult)result;
        Assert.That(view.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_Get_ShouldReturnView()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id };

        // Act
        var result = await _controller.Page03(id, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page03_Get_ShouldReturnInvalidModelStateView()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id };
        _controller.ModelState.AddModelError("Test", "Invalid");

        // Act
        var result = await _controller.Page03(id, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page03_Post_WithContinueAction_ShouldReturnViewModelStateError()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, TreasurerAmendOption = null };

        // Act
        var result = _controller.Page03(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page03_Post_WithContinueAction_ShouldReturnRedirectOnUpdate()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, TreasurerAmendOption = "update" };

        // Act
        var result = _controller.Page03(model);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.ActionName, Is.EqualTo("Page05"));
    }

    [Test]
    public void Page03_Post_WithContinueAction_ShouldReturnRedirectOnReplaceWithExistingOfficer()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, TreasurerAmendOption = "replaceWithExistingOfficer" };

        // Act
        var result = _controller.Page03(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page03_Post_WithContinueAction_ShouldReturnRedirectOnReplaceWithNewTreasurer()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, TreasurerAmendOption = "replaceWithNewTreasurer" };

        // Act
        var result = _controller.Page03(model);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.ActionName, Is.EqualTo("Page04"));
    }

    [Test]
    public void Page03_Post_WithContinueAction_ShouldReturnRedirectOnSkip()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, TreasurerAmendOption = "skip" };

        // Act
        var result = _controller.Page03(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page03_Post_WithContinueAction_ShouldReturnRedirectOnDefaultOption()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, TreasurerAmendOption = "" };

        // Act
        var result = _controller.Page03(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page03_Post_WithPreviousAction_ShouldReturnRedirect()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Previous };

        // Act
        var result = _controller.Page03(model);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.ActionName, Is.EqualTo("Page02"));
    }

    [Test]
    public void Page03_Post_WithDefaultAction_ShouldReturnAddModelError()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id };
        _controller.ModelState.AddModelError("test", "error");


        // Act
        var result = _controller.Page03(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page04_Get_Redirects_To_Page05()
    {
        // Arrange
        long id = 123;
        _amendSmoRegistrationCtlSvcMock.IsUserTreasurerAssistantTreasurerOfficer(id).Returns(true);

        var model = new AmendSmoRegistrationStep02ViewModel { Id = id };

        // Act
        var result = await _controller.Page04(id, default);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.ActionName, Is.EqualTo("Page05"));
    }

    [Test]
    public async Task Page04_Get_ShouldReturnView()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id };

        // Act
        var result = await _controller.Page04(id, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page04_Get_ShouldReturnInvalidView()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id };
        _controller.ModelState.AddModelError("Test", "Invalid");

        // Act
        var result = await _controller.Page04(id, default);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void Page04_Post_WithContinueAction_ShouldReturnViewIfIsUserTreasurerNull()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, IsUserTreasurer = null };

        // Act
        var result = _controller.Page04(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page04_Post_WithContinueAction_ShouldReturnViewIfIsUserTreasurerHasValue()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, IsUserTreasurer = true };

        // Act
        var result = _controller.Page04(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page04_Post_WithPreviousAction_ShouldReturnViewIfIsUserTreasurerHasValue()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Previous, IsUserTreasurer = true };

        // Act
        var result = _controller.Page04(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page04_Post_WithDefaultAction_ShouldReturnViewIfIsUserTreasurerHasValue()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, IsUserTreasurer = true };

        // Act
        var result = _controller.Page04(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page05_Get_ShouldReturnView_WhenIsUserTreasurerFalse()
    {
        // Arrange
        long id = 123;

        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(id).Returns(true);
        _amendSmoRegistrationCtlSvcMock.Page05GetEmptyViewModel(id)
            .Returns(new AmendSmoRegistrationStep02ViewModel { Id = id });

        // Act
        var result = await _controller.Page05(id, false, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }


    [Test]
    public async Task Page05_Get_ShouldReturnView_WhenIsUserTreasuerTrue()
    {
        // Arrange
        long id = 123;

        var prefill = new AmendSmoRegistrationStep02ViewModel { Id = id };

        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(id)
            .Returns(true);

        _amendSmoRegistrationCtlSvcMock.PagePrefill(id, null, true, Arg.Any<CancellationToken>())
            .Returns(prefill);

        _amendSmoRegistrationCtlSvcMock.Page05GetEmptyViewModel(id)
            .Returns(new AmendSmoRegistrationStep02ViewModel { Id = id });

        // Act
        var result = await _controller.Page05(id, true, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }



    [Test]
    public async Task Page05_Get_ShouldReturnView_WhenIsUserTreasuerIsNull()
    {
        // Arrange
        long id = 123;

        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(id).Returns(true);

        _amendSmoRegistrationCtlSvcMock.GetPage05ViewModel(id, Arg.Any<CancellationToken>())
            .Returns((AmendSmoRegistrationStep02ViewModel?)null);

        _amendSmoRegistrationCtlSvcMock.Page05GetEmptyViewModel(id)
            .Returns(new AmendSmoRegistrationStep02ViewModel { Id = id });

        // Act
        var result = await _controller.Page05(id, null, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task Page05_Get_ShouldReturnInvalidView()
    {
        // Arrange
        long id = 123;
        _controller.ModelState.AddModelError("Test", "Invalid");

        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(id).Returns(true);
        _amendSmoRegistrationCtlSvcMock.Page05GetEmptyViewModel(id)
            .Returns(new AmendSmoRegistrationStep02ViewModel { Id = id });

        // Act
        var result = await _controller.Page05(id, null, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task Page05_Post_WithSaveAndCloseAction_ShouldReturnRedirectToDashboard()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.SaveAndClose, IsUserTreasurer = null };
        _accuMailValidatorSvcMock.AccuMailValidationHandler(Arg.Any<AmendSmoRegistrationStep02ViewModel>(), Arg.Any<ModelStateDictionary>(), false)
            .Returns(Task.FromResult(true));

        // Act
        var result = await _controller.Page05(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page05_Post_WithInvalidAccumail_ShouldReturnView()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.SaveAndClose, IsUserTreasurer = null };
        _accuMailValidatorSvcMock.AccuMailValidationHandler(Arg.Any<AmendSmoRegistrationStep02ViewModel>(), Arg.Any<ModelStateDictionary>(), false)
            .Returns(Task.FromResult(false));

        // Act
        var result = await _controller.Page05(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page05_Post_WithContinueAction_ShouldReturnRedirectToPage06()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Continue, IsUserTreasurer = null };

        // Act
        var result = await _controller.Page05(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page05_Post_WithPreviousAction_ShouldReturnRedirectToPage04()
    {
        // Arrange
        long id = 123;
        var model = new AmendSmoRegistrationStep02ViewModel { Id = id, Action = FormAction.Previous, IsUserTreasurer = null };

        // Act
        var result = await _controller.Page05(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page05_Get_ShouldReturnView_WhenIsUserTreasurerTrue_AndPrefillDataIsNull()
    {
        // Arrange
        long id = 123;

        var fallbackModel = new AmendSmoRegistrationStep02ViewModel { Id = id };

        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(id)
            .Returns(true);

        _amendSmoRegistrationCtlSvcMock.Page05GetEmptyViewModel(id)
            .Returns(fallbackModel);

        // Force the path: isUserTreasurer == true, but prefill returns null
        _amendSmoRegistrationCtlSvcMock.PagePrefill(id, null, true, Arg.Any<CancellationToken>())
            .Returns((AmendSmoRegistrationStep02ViewModel?)null);

        // Act
        var result = await _controller.Page05(id, true, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(fallbackModel));
    }


    [Test]
    public void PageClose_ShouldSetToastAndRedirect()
    {
        var result = _controller.PageClose() as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(_controller.TempData["ToastMessage"], Is.EqualTo("text"));
            Assert.That(_controller.TempData["ToastType"], Is.EqualTo("e-toast-success"));
            Assert.That(_controller.TempData["ToastX"], Is.EqualTo("Right"));
            Assert.That(_controller.TempData["ToastY"], Is.EqualTo("Bottom"));
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_WithValidModelState_ReturnsExpectedResults()
    {
        // Arrange
        var expected = new List<CommitteeSearchResultDto>
        {
            new() { Id = 1, Name = "Test Committee", Addresses = new List<AddressDto>() }
        };

        _controller.ModelState.Clear();

        _smoRegistrationSvcMock
            .SearchCommitteeByIdOrName("Test")
            .Returns(Task.FromResult<IEnumerable<CommitteeSearchResultDto>>(expected));

        // Act
        var result = await _controller.SearchRecipientCommitteeByIdOrName("Test", CancellationToken.None);
        var actual = result?.Value as List<CommitteeSearchResultDto>;

        // Assert
        Assert.That(actual, Is.Not.Null);
        Assert.That(actual!, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(actual[0].Id, Is.EqualTo(1));
            Assert.That(actual[0].Name, Is.EqualTo("Test Committee"));
        });
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_InvalidModelState_ReturnsEmptyList()
    {
        _controller.ModelState.AddModelError("search", "error");

        var result = await _controller.SearchRecipientCommitteeByIdOrName("Test", CancellationToken.None);

        var list = result.Value as List<CommitteeSearchResultDto>;
        Assert.That(list, Is.Empty);
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_ShouldReturnEmptyResult_WhenSearchQueryParamIsEmpty()
    {
        // Arrange
        var search = "";

        // Act
        var result = await _controller.SearchRecipientCommitteeByIdOrName(search, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result.Value, Is.Empty);
    }

    [Test]
    public async Task Page06_Get_ModelStateValid_AndDtoNotNull_ReturnsViewWithViewModel()
    {
        // Arrange
        _controller.ModelState.Clear();
        var model = new SmoRegistrationStep02ViewModel { Id = 1 };
        _smoRegistrationCtlSvcMock.GetPage08ViewModel(Arg.Any<long>(), Arg.Any<CancellationToken>())
            .Returns(model);
        var tempData = Substitute.For<ITempDataDictionary>();
        var errorMessages = new Dictionary<string, List<string>>
        {
            { "Test", new List<string> {"Test"} },
            { "Test1", new List<string> {"Test1"} }
        };
        var serializedErrors = JsonSerializer.Serialize(errorMessages);
        tempData
            .TryGetValue("ErrorMessages", out Arg.Any<object>()!)
            .Returns(x =>
            {
                x[1] = serializedErrors;
                return true;
            });

        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page06(1, 2, CancellationToken.None) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(_controller.ModelState.ContainsKey("Test"), Is.True);
            Assert.That(_controller.ModelState["Test"]?.Errors[0].ErrorMessage, Is.EqualTo("Test"));
            Assert.That(_controller.ModelState.ContainsKey("Test1"), Is.True);
            Assert.That(_controller.ModelState["Test1"]?.Errors[0].ErrorMessage, Is.EqualTo("Test1"));
        });
    }

    [Test]
    public async Task Page06_Get_ModelStateInvalid_ReturnsViewWithEmptyViewModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("id", "Required");

        // Act
        var result = await _controller.Page06(1, 2, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task Page06_Get_WithValidModelStateAndDto_ReturnsViewModel()
    {
        // Arrange
        long id = 123;
        long contactId = 456;

        var dto = new AmendSmoRegistrationStep02ViewModel
        {
            Id = id,
            OfficerContactsGridModel = new SmallDataGridModel(),
            IsTerminating = true,
        };

        _amendSmoRegistrationCtlSvcMock.GetPage06ViewModel(id, Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(dto));

        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(id)
            .Returns(true);

        // Act
        var result = await _controller.Page06(id, contactId) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());

        var model = result.Model as AmendSmoRegistrationStep02ViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(model!.Id, Is.EqualTo(id));
            Assert.That(model.OfficerContactId, Is.EqualTo(contactId));
            Assert.That(model.IsTerminating, Is.True);
        });
    }

    [Test]
    public void Page06_Post_ModelStateValid_SaveAndClose_ReturnsView()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep02ViewModel { Action = FormAction.SaveAndClose };
        _controller.ModelState.Clear();

        // Act
        var result = _controller.Page06(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public void Page06_Post_ModelStateValid_Continue_RedirectsToPage09()
    {
        var model = new AmendSmoRegistrationStep02ViewModel { Id = 101, Action = FormAction.Continue };
        _controller.ModelState.Clear();

        var result = _controller.Page06(model) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page09"));
    }

    [Test]
    public void Page06_Post_ModelStateValid_Previous_RedirectsToPage03()
    {
        var model = new AmendSmoRegistrationStep02ViewModel { Id = 202, Action = FormAction.Previous };
        _controller.ModelState.Clear();

        var result = _controller.Page06(model) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Page06_Post_ModelStateValid_InvalidAction_AddsModelError()
    {
        var model = new AmendSmoRegistrationStep02ViewModel { Action = (FormAction)999 };
        _controller.ModelState.Clear();

        var result = _controller.Page06(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ViewData.ModelState[string.Empty]?.Errors, Has.Count.EqualTo(1));
    }

    [Test]
    public void Page06_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        var model = new AmendSmoRegistrationStep02ViewModel();
        _controller.ModelState.AddModelError("test", "error");

        var result = _controller.Page06(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.SameAs(model));
    }

    [Test]
    public void Page06Previous_ModelStateValid_ReturnsRedirectToPage03()
    {
        var model = new AmendSmoRegistrationStep02ViewModel { Id = 123 };
        _controller.ModelState.Clear();

        var result = _controller.Page06Previous(model) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Page06Previous_ModelStateInvalid_StillRedirectsToPage03()
    {
        var model = new AmendSmoRegistrationStep02ViewModel { Id = 456 };
        _controller.ModelState.AddModelError("someField", "error");

        var result = _controller.Page06Previous(model) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void MakeTreasurer_ModelStateValid_ReturnsRedirectToPage06()
    {
        _controller.ModelState.Clear();

        var result = _controller.MakeTreasurer(123, 456) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public void MakeTreasurer_ModelStateInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("id", "Required");

        var result = _controller.MakeTreasurer(0, 0);

        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task MakeTreasurerAction_ModelStateInvalidOrNullOfficer_RedirectsToPage06()
    {
        _controller.ModelState.AddModelError("officerContactId", "Required");

        var result = await _controller.MakeTreasurerAction(123, null, "remove", "Treasurer", CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public async Task MakeTreasurerAction_ChangeOption_WithoutTitle_ReturnsRedirectWithError()
    {
        _controller.ModelState.Clear();

        var result = await _controller.MakeTreasurerAction(123, 456, "change", null, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public async Task MakeTreasurerAction_CancelOption_RedirectsToPage06()
    {
        _controller.ModelState.Clear();

        var result = await _controller.MakeTreasurerAction(123, 456, "cancel", null, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result?.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public async Task MakeTreasurerAction_WithRemoveOption_CallsTransferTreasurer_WithKeepOldTreasurerFalse()
    {
        // Arrange
        long registrationId = 1001;
        long officerContactId = 2002;
        string treasurerOption = "remove";

        _controller.ModelState.Clear();

        // Act
        var result = await _controller.MakeTreasurerAction(registrationId, officerContactId, treasurerOption, null, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page06"));

        await _amendSmoRegistrationCtlSvcMock.Received(1).TransferTreasurer(
            registrationId,
            officerContactId,
            keepOldTreasurer: false,
            newTitle: null,
            Arg.Any<ModelStateDictionary>());
    }

    [Test]
    public async Task MakeTreasurerAction_WithChangeOption_CallsTransferTreasurer_WithKeepOldTreasurerTrue()
    {
        // Arrange
        long registrationId = 1001;
        long officerContactId = 2002;
        string treasurerOption = "change";
        string contactTitle = "New Treasurer";

        _controller.ModelState.Clear();

        // Act
        var result = await _controller.MakeTreasurerAction(registrationId, officerContactId, treasurerOption, contactTitle, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page06"));

        await _amendSmoRegistrationCtlSvcMock.Received(1).TransferTreasurer(
            registrationId,
            officerContactId,
            keepOldTreasurer: true,
            newTitle: contactTitle,
            Arg.Any<ModelStateDictionary>());
    }

    [Test]
    public async Task MakeTreasurerAction_WithChangeOption_AndMissingTitle_AddsModelError_AndRedirects()
    {
        // Arrange
        long registrationId = 1001;
        long officerContactId = 2002;
        string treasurerOption = "change";
        string? contactTitle = null;

        _controller.ModelState.Clear();

        // Act
        var result = await _controller.MakeTreasurerAction(registrationId, officerContactId, treasurerOption, contactTitle, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page06"));

        await _amendSmoRegistrationCtlSvcMock.DidNotReceive().TransferTreasurer(
            Arg.Any<long>(), Arg.Any<long>(), Arg.Any<bool>(), Arg.Any<string>(), Arg.Any<ModelStateDictionary>());
    }

    [Test]
    public async Task DeleteOfficer_WithValidModelState_ReturnsOkObjectResult()
    {
        // Arrange
        long registrationId = 123;
        long contactId = 456;
        var expectedResponse = new RegistrationResponseDto();

        _controller.ModelState.Clear();

        _smoRegistrationSvcMock
            .DeleteSmoRegistrationContactsPage06(registrationId, contactId)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await _controller.DeleteOfficer(registrationId, contactId) as OkObjectResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.EqualTo(expectedResponse));
    }

    [Test]
    public async Task DeleteOfficer_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        long registrationId = 123;
        long contactId = 456;

        _controller.ModelState.AddModelError("TestError", "Some error");

        // Act
        var result = await _controller.DeleteOfficer(registrationId, contactId) as NotFoundResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }



    [Test]
    public async Task EditOfficer_Treasurer_ReturnsRedirectToPage08()
    {
        _controller.ModelState.Clear();
        _smoRegistrationCtlSvcMock
            .IsTreasurer(123, 456, Arg.Any<CancellationToken>())
            .Returns(Task.FromResult<bool?>(true));

        var result = await _controller.EditOfficer(123, 456) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page05"));
    }

    [Test]
    public async Task EditOfficer_NotTreasurer_ReturnsRedirectToPage08()
    {
        _controller.ModelState.Clear();
        _smoRegistrationCtlSvcMock
            .IsTreasurer(123, 456, Arg.Any<CancellationToken>())
            .Returns(Task.FromResult<bool?>(false));

        var result = await _controller.EditOfficer(123, 456) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page08"));
    }

    [Test]
    public async Task EditOfficer_ModelStateInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("test", "error");

        var result = await _controller.EditOfficer(0, 0);

        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task DeleteOfficer_ModelStateInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("registrationId", "Required");

        var result = await _controller.DeleteOfficer(0, 0);

        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task EditIndividualAuthorizer_ModelStateInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("registrationId", "Required");

        var result = await _controller.EditIndividualAuthorizer(0, 0);

        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task EditIndividualAuthorizer_ModelStateValid_RedirectsToPage08()
    {
        _controller.ModelState.Clear();
        var officers = new List<SmoOfficerGridDto>
        {
            new() { Id = 456, ContactId = 789 }
        };
        _smoRegistrationSvcMock
            .GetSmoOfficers(123)
            .Returns(Task.FromResult<IEnumerable<SmoOfficerGridDto>>(officers));

        var result = await _controller.EditIndividualAuthorizer(123, 456) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page10"));
    }

    [Test]
    public async Task Page07_Get_Redirects_To_Page08()
    {
        // Arrange
        long id = 123;
        _amendSmoRegistrationCtlSvcMock.IsUserTreasurerAssistantTreasurerOfficer(id).Returns(true);

        var model = new AmendSmoRegistrationStep02ViewModel { Id = id };

        // Act
        var result = await _controller.Page07(id);

        // Assert
        var redirect = (RedirectToActionResult)result;
        Assert.That(redirect.ActionName, Is.EqualTo("Page08"));
    }

    [Test]
    public async Task Page07_Get_ModelStateValid_ReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.Clear();
        _amendSmoRegistrationCtlSvcMock.InitStep02EmptyViewModelAsync(Arg.Any<long>()).Returns(new AmendSmoRegistrationStep02ViewModel());

        // Act
        var result = await _controller.Page07(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task Page07_Get_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("id", "Required");

        // Act
        var result = await _controller.Page07(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }

    [Test]
    public void Page07_Post_ModelStateInvalid_ReturnsViewWithSameModel()
    {
        var model = new AmendSmoRegistrationStep02ViewModel();
        _controller.ModelState.AddModelError("Any", "Error");

        var result = _controller.Page07(model, CancellationToken.None) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.SameAs(model));
    }

    [Test]
    public void Page07_Post_ModelStateValid_ActionContinue_CallsPage07Continue()
    {
        var model = new AmendSmoRegistrationStep02ViewModel
        {
            Id = 123,
            Action = FormAction.Continue,
            IsOfficer = true
        };
        _controller.ModelState.Clear();

        var result = _controller.Page07(model, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page08"));
    }

    [Test]
    public void Page07_Post_ModelStateValid_ActionPrevious_ReturnsRedirectToPage06()
    {
        var model = new AmendSmoRegistrationStep02ViewModel
        {
            Id = 123,
            Action = FormAction.Previous
        };
        _controller.ModelState.Clear();

        var result = _controller.Page07(model, CancellationToken.None) as RedirectToActionResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public void Page07_Post_ModelStateValid_InvalidAction_AddsModelError()
    {
        var model = new AmendSmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = (FormAction)999
        };
        _controller.ModelState.Clear();

        var result = _controller.Page07(model, CancellationToken.None) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.ViewData.ModelState[string.Empty]?.Errors, Has.Count.EqualTo(1));
    }

    [Test]
    public async Task Page08_Get_ModelStateValid_ReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.Clear();

        var prefillData = new AmendSmoRegistrationStep02ViewModel
        {
            Addresses = new()
            {
                new AddressViewModel { Purpose = CommonConstants.Address.PurposeOfficer }
            }
        };

        _amendSmoRegistrationCtlSvcMock
            .PagePrefill(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<bool>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult<AmendSmoRegistrationStep02ViewModel?>(prefillData));

        // Act
        var result = await _controller.Page08(1, 1, true) as ViewResult;

        // Assert
        Assert.That(result, Is.TypeOf<ViewResult>());
        var model = result?.Model as AmendSmoRegistrationStep02ViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model?.Addresses.FirstOrDefault()?.Purpose, Is.EqualTo(FilerRole.SlateMailerOrg_Officer.Name));
    }

    [Test]
    public async Task Page08_Get_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        long registrationId = 1;
        _controller.ModelState.AddModelError("Any", "Error");

        var fallbackModel = new AmendSmoRegistrationStep02ViewModel { Id = registrationId };

        _amendSmoRegistrationCtlSvcMock.Page08GetEmptyViewModel(registrationId)
            .Returns(fallbackModel);

        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(registrationId)
            .Returns(true);

        // Act
        var result = await _controller.Page08(registrationId, null, false) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<AmendSmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task Page08_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep02ViewModel();
        _controller.ModelState.AddModelError("field", "error");

        // Act
        var result = await _controller.Page08(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.TypeOf<ViewResult>());
        Assert.That(result!.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page08_Post_ActionIsSubmit_ValidModel_RedirectsToGet()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep02ViewModel { Action = FormAction.Submit, Id = 123 };
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page08(model, CancellationToken.None);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect!.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public async Task Page08_Post_ActionInvalid_AddsModelError()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep02ViewModel { Action = null };
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.Page08(model, CancellationToken.None) as ViewResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.TypeOf<ViewResult>());
            Assert.That(_controller.ModelState[string.Empty]!.Errors, Is.Not.Empty);
        });
    }

    [Test]
    public void CloseOfficer_ModelStateValid_RedirectsToPage06()
    {
        // Arrange
        _controller.ModelState.Clear();

        // Act
        var result = _controller.CloseOfficer(5);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect!.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public void CloseOfficer_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("any", "error");

        // Act
        var result = _controller.CloseOfficer(5);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public void CancelOfficer_ModelStateValid_RedirectsToPage06()
    {
        // Arrange
        _controller.ModelState.Clear();

        // Act
        var result = _controller.CancelOfficer(5);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect!.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public void CancelOfficer_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("any", "error");

        // Act
        var result = _controller.CancelOfficer(5);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ModelStateInvalid_Returns404()
    {
        // Arrange
        _controller.ModelState.AddModelError("id", "error");

        // Act
        var result = await _controller.DeleteIndividualAuthorizer(1, 1);

        // Assert
        Assert.That(result!.StatusCode, Is.EqualTo(404));
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ModelIsNull_Returns404()
    {
        // Arrange
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.DeleteIndividualAuthorizer(1, 1);

        // Assert
        Assert.That(result!.StatusCode, Is.EqualTo(404));
    }

    [Test]
    public async Task Page09_Get_ModelStateInvalid_ReturnsEmptyViewModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("any", "error");

        // Act
        var result = await _controller.Page09(1) as ViewResult;

        // Assert
        Assert.That(result!.Model, Is.TypeOf<AmendSmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page09_Get_ValidModelStateAndModelNotNull_ReturnsPopulatedView()
    {
        // Arrange
        long id = 123;
        var amendModel = new AmendSmoRegistrationStep03ViewModel
        {
            Id = id,
            IsTerminating = true,
        };

        _amendSmoRegistrationCtlSvcMock
            .GetPage09ViewModel(Arg.Any<long>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult<AmendSmoRegistrationStep03ViewModel?>(amendModel));

        // Act
        var result = await _controller.Page09(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var viewModel = result!.Model as AmendSmoRegistrationStep03ViewModel;
        Assert.That(viewModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewModel!.Id, Is.EqualTo(id));
            Assert.That(viewModel.IsTerminating, Is.True);
        });
    }

    [Test]
    public async Task Page09_Get_ValidModelStateButModelNull_ReturnsEmptyViewModel()
    {
        // Arrange
        long id = 123;

        _amendSmoRegistrationCtlSvcMock
            .GetPage09ViewModel(Arg.Any<long>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromResult<AmendSmoRegistrationStep03ViewModel?>(null));

        // Act
        var result = await _controller.Page09(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var viewModel = result!.Model as AmendSmoRegistrationStep03ViewModel;
        Assert.That(viewModel, Is.Not.Null);
    }

    [Test]
    public async Task Page09_Get_InvalidModelState_ReturnsEmptyViewModel()
    {
        // Arrange
        long id = 123;
        _controller.ModelState.AddModelError("any", "invalid");

        // Act
        var result = await _controller.Page09(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var viewModel = result!.Model as AmendSmoRegistrationStep03ViewModel;
        Assert.That(viewModel, Is.Not.Null);
    }

    [Test]
    public void Page09_Post_ModelStateValid_Continue_RedirectsToPage11()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep03ViewModel { Action = FormAction.Continue, Id = 1 };
        _controller.ModelState.Clear();

        // Act
        var result = _controller.Page09(model) as RedirectToActionResult;

        // Assert
        Assert.That(result!.ActionName, Is.EqualTo("Termination01"));
    }

    [Test]
    public void Page09_Post_ModelStateValid_Previous_RedirectsToPage06()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep03ViewModel { Action = FormAction.Previous, Id = 1 };
        _controller.ModelState.Clear();

        // Act
        var result = _controller.Page09(model) as RedirectToActionResult;

        // Assert
        Assert.That(result!.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public void Page09_Post_ModelStateValid_InvalidAction_AddsModelError()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep03ViewModel { Action = null };
        _controller.ModelState.Clear();

        // Act
        var result = _controller.Page09(model) as ViewResult;

        // Assert
        Assert.That(_controller.ModelState[string.Empty]!.Errors, Is.Not.Empty);
    }

    [Test]
    public async Task Page10_ModelStateValid_ContactIdIsNull_ReturnsViewWithEmptyViewModel()
    {
        // Arrange
        _controller.ModelState.Clear();
        long id = 1;
        _amendSmoRegistrationCtlSvcMock.InitStep03EmptyViewModelAsync(id)
            .Returns(new AmendSmoRegistrationStep03ViewModel());

        // Act
        var result = await _controller.Page10(id, null) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<AmendSmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page10_ModelStateValid_ContactIdNotNull_ReturnsViewWithPrefillData()
    {
        // Arrange
        _controller.ModelState.Clear();
        long id = 1;
        long contactId = 123;

        var expectedModel = new SmoRegistrationStep03ViewModel
        {
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            }
        };
        var emptyModel = new AmendSmoRegistrationStep03ViewModel
        {
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            }
        };
        _smoRegistrationCtlSvcMock
            .GetPage13ViewModel(id, contactId, Arg.Any<CancellationToken>())
            .Returns(expectedModel);
        _amendSmoRegistrationCtlSvcMock
             .InitStep03EmptyViewModelAsync(Arg.Any<long>())
             .Returns(emptyModel);

        // Act
        var result = await _controller.Page10(id, contactId) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<AmendSmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page10_ModelStateValid_ContactIdNotNull_PrefillNull_ReturnsEmptyView()
    {
        // Arrange
        _controller.ModelState.Clear();
        long id = 1;
        long contactId = 123;
        var emptyModel = new AmendSmoRegistrationStep03ViewModel
        {
            Id = id,
            // Addresses list must contain an empty address record so that the page renders inputs for it.
            Addresses = new()
            {
                new() { Purpose = CommonConstants.Address.PurposeOfficer }
            }
        };

        _smoRegistrationCtlSvcMock
            .GetPage13ViewModel(id, contactId, Arg.Any<CancellationToken>())
            .ReturnsNull();
        _amendSmoRegistrationCtlSvcMock
            .InitStep03EmptyViewModelAsync(Arg.Any<long>())
            .Returns(emptyModel);

        // Act
        var result = await _controller.Page10(id, contactId) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<AmendSmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page10_ModelStateInvalid_ReturnsViewWithEmptyViewModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid");
        long id = 1;
        var emptyModel = new AmendSmoRegistrationStep03ViewModel();

        _amendSmoRegistrationCtlSvcMock
            .InitStep03EmptyViewModelAsync(Arg.Any<long>())
            .Returns(emptyModel);

        // Act
        var result = await _controller.Page10(id, null) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<AmendSmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page10_Post_ModelStateValid_ActionSaveAndClose_CallsSaveMethod()
    {
        // Arrange
        _controller.ModelState.Clear();
        var model = new AmendSmoRegistrationStep03ViewModel { Action = FormAction.SaveAndClose };

        // Act
        var result = await _controller.Page10(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page09"));
    }

    [Test]
    public async Task Page10_Post_ModelStateInvalid_ReturnsView()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid");
        var model = new AmendSmoRegistrationStep03ViewModel();

        // Act
        var result = await _controller.Page10(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page10Save_ModelStateValid_ReturnsRedirectToPage09()
    {
        // Arrange
        _controller.ModelState.Clear();
        var model = new AmendSmoRegistrationStep03ViewModel { Id = 1 };

        // Act
        var result = await _controller.Page10Save(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page09"));
    }

    [Test]
    public async Task Page10Save_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid");
        var model = new AmendSmoRegistrationStep03ViewModel();

        // Act
        var result = await _controller.Page10Save(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.ViewName, Is.EqualTo("Page10"));
            Assert.That(result.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public void CloseAuthorizer_ModelStateValid_RedirectsToPage09()
    {
        // Arrange
        _controller.ModelState.Clear();
        long id = 1;

        // Act
        var result = _controller.CloseAuthorizer(id) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page09"));
    }

    [Test]
    public void CloseAuthorizer_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");
        long id = 1;

        // Act
        var result = _controller.CloseAuthorizer(id);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }


    [Test]
    public void CancelAuthorizer_ModelStateValid_RedirectsToPage09()
    {
        // Arrange
        _controller.ModelState.Clear();
        long id = 1;

        // Act
        var result = _controller.CancelAuthorizer(id) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page09"));
    }

    [Test]
    public void CancelAuthorizer_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");
        long id = 1;

        // Act
        var result = _controller.CancelAuthorizer(id);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }


    [Test]
    public async Task Page11_Get_ShouldReturnViewModel_OfficerCountExistsAndAgreementAccepted()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            IsAgreementAccepted = true,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            }
        };
        _amendSmoRegistrationCtlSvcMock
            .GetPage11ViewModel(1, Arg.Any<bool>(), default)
            .Returns(Task.FromResult(model));

        // Act
        var result = await _controller.Page11(model.Id, false, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    [Test]
    public async Task Page11_Get_ShouldReturnViewModel_OfficerCountExistsAndIsNotTreasurer()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = false,
            IsAgreementAccepted = false,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            }
        };
        _amendSmoRegistrationCtlSvcMock
            .GetPage11ViewModel(1, Arg.Any<bool>(), default)
            .Returns(Task.FromResult(model));

        // Act
        var result = await _controller.Page11(model.Id, false, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    [Test]
    public async Task Page11_Get_ShouldRedirectToAction_OfficerCount0AndAgreementAccepted()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            IsAgreementAccepted = true,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel> { }
        };
        _amendSmoRegistrationCtlSvcMock
            .GetPage11ViewModel(1, Arg.Any<bool>(), default)
            .Returns(Task.FromResult(model));

        // Act
        var result = await _controller.Page11(model.Id, false, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        });
    }

    [Test]
    public async Task Page11_Get_ShouldRedirectToAction_OfficerCount0AndIsNotTreasurer()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = false,
            IsAgreementAccepted = false,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel> { }
        };
        _amendSmoRegistrationCtlSvcMock
            .GetPage11ViewModel(1, Arg.Any<bool>(), default)
            .Returns(Task.FromResult(model));

        // Act
        var result = await _controller.Page11(model.Id, false, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        });
    }

    [Test]
    public async Task Page11_Get_ShouldReturnNotFound_InvalidModelState()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page11(123, false, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Page11_Post_ShouldReturnViewPage12_WhenSendForAcknowledgement_OnFormActionContinue()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Continue,
            IsRequiredOtherAcknowledgements = true,
        };

        // Act
        var result = await _controller.Page11(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }
    [Test]
    public async Task Page11_Post_ShouldReturnViewPage11_WhenHasUncompletedAcknowledgement_OnFormActionContinue()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Continue,
        };
        var expected = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            IsRequiredOtherAcknowledgements = true,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new()
                {
                    FirstName = "Test",
                    LastName = "Test",
                    Title = "Test",
                }
            },
            Action = FormAction.Continue,
        };
        _amendSmoRegistrationCtlSvcMock
            .Page11Submit(model)
            .Returns(expected);

        // Act
        var result = await _controller.Page11(model);


        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page11_Post_ShouldRedirect_OnFormActionPrevious()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Previous,
        };

        // Act
        var result = await _controller.Page11(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page11_Post_ShouldRedirect_OnFormActionClose()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Close,
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page11(model) as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page11_Post_ShouldReturnError_NoFormAction()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
        };

        // Act
        await _controller.Page11(model);

        // Assert
        Assert.That(_controller.ModelState.IsValid, Is.False);
    }


    [Test]
    public async Task Page11_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page11(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page12_Get_ShouldReturnViewModel_AmendmentAuthorization()
    {
        // Arrange
        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(123).Returns(true);
        _amendSmoRegistrationCtlSvcMock
            .GetPage12ViewModel(123, Arg.Any<bool>())
            .Returns(new AmendSmoRegistrationStep04ViewModel());

        // Act
        var result = await _controller.Page12(123, CancellationToken.None) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    [Test]
    public async Task Page12_Get_ShouldReturnViewModel_TerminateAuthorization()
    {
        // Arrange
        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(123).Returns(false);
        _amendSmoRegistrationCtlSvcMock
            .GetPage12ViewModel(123, Arg.Any<bool>())
            .Returns(new AmendSmoRegistrationStep04ViewModel());

        // Act
        var result = await _controller.Page12(123, CancellationToken.None) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }


    [Test]
    public async Task Page12_Get_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page12(123, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task Page12_Post_ShouldReturnViewPage13_OnFormActionContinue()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = true,
            IsVerificationCertified = true,
        };

        // Act
        var result = await _controller.Page12(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(result?.ActionName, Is.EqualTo("Page13"));
        });
    }

    [Test]
    public async Task Page12_Post_ShouldRemainPage12_IfHaveError_OnFormActionContinue()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
            IsVerificationCertified = false,
        };

        _amendSmoRegistrationCtlSvcMock
            .Page12SendForAttestation(Arg.Any<AmendSmoRegistrationStep04ViewModel>(), Arg.Any<ModelStateDictionary>())
            .Returns(callInfo =>
            {
                var modelState = callInfo.ArgAt<ModelStateDictionary>(1);
                modelState.AddModelError("Test", "Test");

                return Task.CompletedTask;
            });

        // Act
        var result = await _controller.Page12(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
            Assert.That(_controller.ModelState["Test"]?.Errors.Count, Is.GreaterThan(0));
        });
    }

    [Test]
    public async Task Page12_Post_ShouldRedirect_OnFormActionPrevious()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Previous,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };

        // Act
        var result = await _controller.Page12(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page12_Post_ShouldRedirect_OnFormActionClose()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Close,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page12(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page12_Post_ShouldError_AddModelError()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = null,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };

        // Act
        var result = await _controller.Page12(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page12_Post_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = null,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = DateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page12(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page12Previous_ModelValid_NotRequiredAck_Authorized_And_AgreementAccepted_ShouldRedirectToTermination01()
    {
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 100,
            Action = FormAction.Previous,
            IsRequiredOtherAcknowledgements = false,
            IsAgreementAccepted = true
        };

        _authorizationSvcMock
            .IsAuthorized(Arg.Any<AuthorizationRequest>())
            .Returns(true);

        var result = await _controller.Page12(model);

        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect!.ActionName, Is.EqualTo("Termination01"));
            Assert.That(redirect.RouteValues!["Id"], Is.EqualTo(100));
        });
    }

    [Test]
    public async Task Page12Previous_ModelValid_NotRequiredAck_NotAuthorized_ShouldRedirectToTermination01()
    {
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 101,
            Action = FormAction.Previous,
            IsRequiredOtherAcknowledgements = false,
            IsAgreementAccepted = false
        };

        _authorizationSvcMock
            .IsAuthorized(Arg.Any<AuthorizationRequest>())
            .Returns(false);

        var result = await _controller.Page12(model);

        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect!.ActionName, Is.EqualTo("Termination01"));
            Assert.That(redirect.RouteValues!["Id"], Is.EqualTo(101));
        });
    }

    [Test]
    public async Task Page12Previous_ModelValid_RequiredAck_ShouldRedirectToPage11()
    {
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 102,
            Action = FormAction.Previous,
            IsRequiredOtherAcknowledgements = true,
            IsAgreementAccepted = true
        };

        _authorizationSvcMock
            .IsAuthorized(Arg.Any<AuthorizationRequest>())
            .Returns(true);

        var result = await _controller.Page12(model);

        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect!.ActionName, Is.EqualTo("Page11"));
            Assert.That(redirect.RouteValues!["id"], Is.EqualTo(102));
            Assert.That(redirect.RouteValues["isPrevious"], Is.True);
        });
    }

    [Test]
    public async Task Page12Previous_ModelValid_NotRequiredAck_Authorized_ButNoAgreement_ShouldRedirectToPage11()
    {
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 103,
            Action = FormAction.Previous,
            IsRequiredOtherAcknowledgements = false,
            IsAgreementAccepted = false
        };

        _authorizationSvcMock
            .IsAuthorized(Arg.Any<AuthorizationRequest>())
            .Returns(true);

        var result = await _controller.Page12(model);

        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect!.ActionName, Is.EqualTo("Page11"));
            Assert.That(redirect.RouteValues!["id"], Is.EqualTo(103));
            Assert.That(redirect.RouteValues["isPrevious"], Is.True);
        });
    }

    [Test]
    public async Task Page13_Get_ShouldReturnView()
    {
        // Arrange
        // Update when CtlSvc methods are implemented

        // Act
        var result = await _controller.Page13(123, default) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    [Test]
    public async Task Page13_Get_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        var model = new ConfirmationViewModel
        {
            ExecutedOn = DateNow,
            IsSubmission = true,
            PendingItems = new List<PendingItemSharedViewModel>
            {
                new() { Item = "Mock Treasurer Acknowledgement", Status = "In Progress"},
                new() { Item = "Mock Assistance Treasurer Acknowledgement", Status = "Complete"}
            }
        };

        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page13(123, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public void Constructor_SetsAllPropertiesCorrectly_WhenDataIsValid()
    {
        // Arrange
        var addressDto = new AddressDto();
        var phoneDto = new PhoneNumberDto
        {
            CountryCode = "+63",
            Extension = "02",
            Number = "1234567"
        };

        var contactDto = new SmoRegistrationContactDto
        {
            Id = 99,
            FirstName = "Juan",
            MiddleName = "M",
            LastName = "Dela Cruz",
            Email = "<EMAIL>",
            PhoneNumber = phoneDto,
            CanAuthorize = true,
            IsUserTreasurer = true,
            Address = addressDto
        };

        long? id = 1;

        // Act
        var vm = new AmendSmoRegistrationStep02ViewModel(id, contactDto);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(vm.Id, Is.EqualTo(1));
            Assert.That(vm.ContactId, Is.EqualTo(99));
            Assert.That(vm.FirstName, Is.EqualTo("Juan"));
            Assert.That(vm.MiddleName, Is.EqualTo("M"));
            Assert.That(vm.LastName, Is.EqualTo("Dela Cruz"));
            Assert.That(vm.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(vm.TelephoneNumber, Is.EqualTo(phoneDto));
            Assert.That(vm.CanAuthorize, Is.True);
            Assert.That(vm.IsUserTreasurer, Is.True);
            Assert.That(vm.Addresses, Has.Count.EqualTo(1));
            Assert.That(vm.Addresses[0], Is.TypeOf<AddressViewModel>());
        });
    }

    [Test]
    public void AmendSmoRegistrationStep03ViewModel_Constructor_SetsAllPropertiesCorrectly()
    {
        // Arrange
        var alerts = new PortalAlerts();

        var source = new SmoRegistrationStep03ViewModel
        {
            Id = 100,
            ContactId = 200,
            FirstName = "Jane",
            MiddleName = "Q",
            LastName = "Doe",
            Email = "<EMAIL>",
            TelephoneNumber = new PhoneNumberDto
            {
                Number = "9876543",
                Type = CommonConstants.PhoneNumber.TypeHome,
            },
            CanAuthorize = true,
            Messages = alerts,
        };

        // Act
        var vm = new AmendSmoRegistrationStep03ViewModel(source);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(vm.Id, Is.EqualTo(100));
            Assert.That(vm.ContactId, Is.EqualTo(200));
            Assert.That(vm.FirstName, Is.EqualTo("Jane"));
            Assert.That(vm.MiddleName, Is.EqualTo("Q"));
            Assert.That(vm.LastName, Is.EqualTo("Doe"));
            Assert.That(vm.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(vm.TelephoneNumber, Is.EqualTo(source.TelephoneNumber));
            Assert.That(vm.CanAuthorize, Is.True);
            Assert.That(vm.Messages, Is.SameAs(alerts));
        });
    }

    [Test]
    public void AmendSmoRegistrationStep03ViewModel_Constructor_InitializesMessages_WhenSourceMessagesIsNull()
    {
        // Arrange
        var source = new SmoRegistrationStep03ViewModel();

        typeof(SmoRegistrationStep03ViewModel)
            .GetProperty(nameof(SmoRegistrationStep03ViewModel.Messages))!
            .SetValue(source, null);

        // Act
        var vm = new AmendSmoRegistrationStep03ViewModel(source);

        // Assert
        Assert.That(vm.Messages, Is.Not.Null);
        Assert.That(vm.Messages, Is.TypeOf<PortalAlerts>());
    }

    [Test]
    public async Task Termination01_Get_ShouldReturnViewWithModel_WhenModelStateIsValid()
    {
        // Act
        var result = await _controller.Termination01(123) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<AmendSmoRegistrationStep04ViewModel>());
            Assert.That(((AmendSmoRegistrationStep04ViewModel)result.Model!).Id, Is.EqualTo(123));
        });
    }

    [Test]
    public async Task Termination01_Get_ShouldReturnViewWithModel_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("id", "Invalid");

        // Act
        var result = await _controller.Termination01(0) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<AmendSmoRegistrationStep04ViewModel>());
            Assert.That(((AmendSmoRegistrationStep04ViewModel)result.Model!).Id, Is.EqualTo(0));
        });
    }

    [Test]
    public async Task Termination01_Post_ShouldRedirectToTermination02_WhenOptionYes()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Continue, TerminateRegistrationOption = "yes" };

        var result = await _controller.Termination01(model) as RedirectToActionResult;

        Assert.That(result?.ActionName, Is.EqualTo("Termination02"));
    }

    [Test]
    public async Task Termination01_Post_ShouldRedirectToPage11_WhenOptionNo()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Continue, TerminateRegistrationOption = "no" };

        var result = await _controller.Termination01(model) as RedirectToActionResult;

        Assert.That(result?.ActionName, Is.EqualTo("Page11"));
    }

    [Test]
    public async Task Termination01_Post_ShouldReturnView_WhenOptionIsNull()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Continue, TerminateRegistrationOption = null };

        var result = await _controller.Termination01(model) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result?.ViewName, Is.EqualTo("Termination01"));
            Assert.That(_controller.ModelState.ContainsKey("TerminateRegistrationOption"));
        });
    }

    [Test]
    public async Task Termination01_Post_ShouldRedirectToPage09_WhenActionIsPrevious()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Previous };

        var result = await _controller.Termination01(model) as RedirectToActionResult;

        Assert.That(result?.ActionName, Is.EqualTo("Page09"));
    }

    [Test]
    public async Task Termination01_Post_ShouldReturnView_WhenActionIsInvalid()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = (FormAction)999 };

        var result = await _controller.Termination01(model) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(actual: _controller!.ModelState[string.Empty]!.Errors, expression: Is.Not.Empty);
        });
    }

    [Test]
    public async Task Termination01_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        _controller.ModelState.AddModelError("Id", "Invalid");

        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Continue };

        var result = await _controller.Termination01(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Termination02_Post_ShouldRedirectToPage11_WhenActionIsContinue()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Continue };

        _amendSmoRegistrationCtlSvcMock
            .Termination02ContinueSubmit(model, Arg.Any<ModelStateDictionary>(), Arg.Any<CancellationToken>(), true)
            .Returns(model);

        var result = await _controller.Termination02(model) as RedirectToActionResult;

        Assert.That(result?.ActionName, Is.EqualTo("Page11"));
    }

    [Test]
    public async Task Termination02_Post_ShouldRedirectToTermination01_WhenActionIsPrevious()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Previous };

        _amendSmoRegistrationCtlSvcMock
            .Termination02ContinueSubmit(model, Arg.Any<ModelStateDictionary>(), Arg.Any<CancellationToken>(), false)
            .Returns(model);

        var result = await _controller.Termination02(model) as RedirectToActionResult;

        Assert.That(result?.ActionName, Is.EqualTo("Termination01"));
    }

    [Test]
    public async Task Termination02_Post_ShouldRedirectToDashboard_WhenActionIsSaveAndClose()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.SaveAndClose };

        var result = await _controller.Termination02(model) as RedirectToActionResult;

        Assert.That(result?.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task Termination02_Post_ShouldReturnView_WhenActionIsInvalid()
    {
        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = (FormAction)999 };

        var result = await _controller.Termination02(model) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(_controller!.ModelState[string.Empty]!.Errors, Is.Not.Empty);
        });
    }

    [Test]
    public async Task Termination02_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        _controller.ModelState.AddModelError("Id", "Invalid");

        var model = new AmendSmoRegistrationStep04ViewModel { Id = 123, Action = FormAction.Continue };

        var result = await _controller.Termination02(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.That(result?.ViewName, Is.Null); // Returns default view
    }

    [Test]
    public async Task Termination02_Get_ShouldReturnViewWithModel_WhenModelStateIsValid()
    {
        // Act
        var result = await _controller.Termination02(123) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<AmendSmoRegistrationStep04ViewModel>());
            Assert.That(((AmendSmoRegistrationStep04ViewModel)result.Model!).Id, Is.EqualTo(123));
            Assert.That(result.ViewName, Is.Null); // default view name
        });
    }

    [Test]
    public async Task Termination02_Get_ShouldReturnTermination01View_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("Id", "Invalid");

        // Act
        var result = await _controller.Termination02(123) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<AmendSmoRegistrationStep04ViewModel>());
            Assert.That(((AmendSmoRegistrationStep04ViewModel)result.Model!).Id, Is.EqualTo(123));
            Assert.That(result.ViewName, Is.EqualTo("Termination01"));
        });
    }

}

