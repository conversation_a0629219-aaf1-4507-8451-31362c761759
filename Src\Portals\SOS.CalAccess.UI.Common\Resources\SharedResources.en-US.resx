<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Common.Help" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="Messages" xml:space="preserve">
    <value>Messages</value>
  </data>
  <data name="Notification.Channels" xml:space="preserve">
    <value>Notification Channels</value>
  </data>
  <data name="Notification.Title" xml:space="preserve">
    <value>Notification Title</value>
  </data>
  <data name="StatusType" xml:space="preserve">
    <value>StatusType</value>
  </data>
  <data name="Urgency" xml:space="preserve">
    <value>Urgency</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="Common.Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="CandidateIntentionStatement.FirstName" xml:space="preserve">
    <value>Candidate first name</value>
  </data>
  <data name="CandidateIntentionStatement.MiddleName" xml:space="preserve">
    <value>Candidate middle name</value>
  </data>
  <data name="CandidateIntentionStatement.LastName" xml:space="preserve">
    <value>Candidate last name</value>
  </data>
  <data name="CandidateIntentionStatement.EmailAddress" xml:space="preserve">
    <value>TBD</value>
  </data>
  <data name="CandidateIntentionStatement.PhoneNumber" xml:space="preserve">
    <value>Candidate telephone number</value>
  </data>
  <data name="CandidateIntentionStatement.FaxNumber" xml:space="preserve">
    <value>Candidate fax number</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Street" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Street2" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Zip" xml:space="preserve">
    <value>ZIP code</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.TypeOfAddress" xml:space="preserve">
    <value>Type of address</value>
  </data>
  <data name="CandidateIntentionStatement.SameAsCandidateAddress" xml:space="preserve">
    <value>Same as Candidate Address</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Street" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Street2" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Zip" xml:space="preserve">
    <value>ZIP code</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.TypeOfAddress" xml:space="preserve">
    <value>Type of address</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.FilingSummary01" xml:space="preserve">
    <value>Filing Summary</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.FilingSummary02" xml:space="preserve">
    <value>Filing summary</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.TransactionEntry" xml:space="preserve">
    <value>Transaction Entry</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.PreviewPdf" xml:space="preserve">
    <value>Preview PDF</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Submit" xml:space="preserve">
    <value>Submit lobbying report</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.NotStarted" xml:space="preserve">
    <value>Not Started</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.InProgress" xml:space="preserve">
    <value>In Progress</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.DontHaveAnything" xml:space="preserve">
    <value>I don't have anything in this category</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.FilingPeriod" xml:space="preserve">
    <value>Filing Period</value>
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.Title" xml:space="preserve">
    <value>Temporary Disclosure Dashboard</value>
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.ReportName" xml:space="preserve">
    <value>Report Name</value>
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.CreatedDate" xml:space="preserve">
    <value>Created Date</value>
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.ReportPeriod" xml:space="preserve">
    <value>Report Period</value>
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.ReportDetails" xml:space="preserve">
    <value>Report details</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LegislativeSession" xml:space="preserve">
    <value>Legislative session</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.ReportCoversPeriod" xml:space="preserve">
    <value>Report covers period from</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.CumulativePeriod" xml:space="preserve">
    <value>Cumulative period beginning</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.ReportType" xml:space="preserve">
    <value>Type of report</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.EmployerName" xml:space="preserve">
    <value>Employer name</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.FilerID" xml:space="preserve">
    <value>Filer ID no.</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.BusinessAddress" xml:space="preserve">
    <value>Business address</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Phone" xml:space="preserve">
    <value>Phone number</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Email" xml:space="preserve">
    <value>Email address</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Fax" xml:space="preserve">
    <value>Fax number</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.MailingAddress" xml:space="preserve">
    <value>Mailing address (if different than business address)</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Through" xml:space="preserve">
    <value>through</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.EmployerSubtext" xml:space="preserve">
    <value>To update, please amend your lobbyist registration.</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.SaveAndClose" xml:space="preserve">
    <value>Save and close</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageDate" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageSubject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageDueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessagePriority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageActionRequired" xml:space="preserve">
    <value>Action Required</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageCenterTitle" xml:space="preserve">
    <value>Message Center</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="CandidateIntentionStatement.HasCandidateRegisteredBefore" xml:space="preserve">
    <value>Has the candidate registered for office before?</value>
  </data>
  <data name="CandidateIntentionStatement.RegisteringYourselfOrSomeoneElse" xml:space="preserve">
    <value>Are you registering for yourself or someone else?</value>
  </data>
  <data name="Common.Myself" xml:space="preserve">
    <value>Myself</value>
  </data>
  <data name="Common.SomeoneElse" xml:space="preserve">
    <value>Someone Else</value>
  </data>
  <data name="Common.SelectCandidate" xml:space="preserve">
    <value>Select Candidate</value>
  </data>
  <data name="Common.Jurisdiction" xml:space="preserve">
    <value>Jurisdiction</value>
  </data>
  <data name="Common.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Common.Year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="Common.Election" xml:space="preserve">
    <value>Election</value>
  </data>
  <data name="Common.Office" xml:space="preserve">
    <value>Office</value>
  </data>
  <data name="Common.District" xml:space="preserve">
    <value>District</value>
  </data>
  <data name="Common.Party" xml:space="preserve">
    <value>Party</value>
  </data>
  <data name="CandidateIntentionStatement.DiligenceAgreement" xml:space="preserve">
    <value>I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="Common.Residential" xml:space="preserve">
    <value>Residential</value>
  </data>
  <data name="Common.Business" xml:space="preserve">
    <value>Business</value>
  </data>
  <data name="Common.SelectCountry" xml:space="preserve">
    <value>Select Country</value>
  </data>
  <data name="Common.SelectState" xml:space="preserve">
    <value>Select State</value>
  </data>
  <data name="Common.SelectFilingPeriod" xml:space="preserve">
    <value>Select filing period</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Title" xml:space="preserve">
    <value>Payments to in-house lobbyists</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Body" xml:space="preserve">
    <value>“Payments” include salary, reimbursement of expenses, an advance for expenses or a salary advance or any other payments made in connection with lobbying activities. (Salary includes gross wages paid, plus any fringe benefits which are in lieu of wages such as the granting of stock options or purchase of annuities. Salary does not include routine fringe benefits such as the employer’s contributions to a health plan, retirement plan, or payroll taxes.)</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.ReviewInstructions" xml:space="preserve">
    <value>Review Instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Subtitle" xml:space="preserve">
    <value>Payments to in-house lobbyists</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Subtotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.TotalPaymentsLabel" xml:space="preserve">
    <value>Total payments to in-house lobbyists:</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.EnterAmount" xml:space="preserve">
    <value>Enter amount</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.SaveAndClose" xml:space="preserve">
    <value>Save and Close</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SaveAndClose" xml:space="preserve">
    <value>Save and close</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Title" xml:space="preserve">
    <value>Campaign contributions</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.ReviewInstruction" xml:space="preserve">
    <value>Review Instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.DisclosureStatements" xml:space="preserve">
    <value>Contributions contained in existing disclosure statements</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.ContainedInDisclosureStatements" xml:space="preserve">
    <value> Are any of the contributions you made during the period covered by this report — or made by a committee
    you sponsor — already contained in a compaign disclosure statement which is on file with the Secretary of
  State?</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.MajorDonorOrRecipientCommittee" xml:space="preserve">
    <value> Was the statement filed by a major donor or recipient committee?</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Title" xml:space="preserve">
    <value>Campaign Statement</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Subtitle" xml:space="preserve">
    <value>Slate Mailer Organization</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.Title" xml:space="preserve">
    <value>Slate Mailer Campaign Statement</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.SearchHeader" xml:space="preserve">
    <value>Select a slate mailer</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.SearchPlaceholder" xml:space="preserve">
    <value>Search by name or ID</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Selection.ErrorMessage" xml:space="preserve">
    <value>Please select a slate mailer</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.Title" xml:space="preserve">
    <value>Review Your Statement</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.GeneralInformation" xml:space="preserve">
    <value>General information</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingSummaryHeader" xml:space="preserve">
    <value>Filing Summary</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingSummary" xml:space="preserve">
    <value>Filing summary</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsReceived" xml:space="preserve">
    <value>Payments received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsMade" xml:space="preserve">
    <value>Payments made</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PaymentsMadeByAgentOrContractor" xml:space="preserve">
    <value>Payments made by an agent or independent contractor</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.PersonsReceiving1000OrMore" xml:space="preserve">
    <value>Persons receiving $1000 or more</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.CandidatesMeasuresNotListed" xml:space="preserve">
    <value>Candidates and measures not listed on Payment received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.Title" xml:space="preserve">
    <value>Amendment Explanation</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendmentExplanation.AmendmentExplanationText" xml:space="preserve">
    <value>Please provide any additional description or explanation of the changes you’ve made in this amendment</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Title" xml:space="preserve">
    <value>General Information</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Smo" xml:space="preserve">
    <value>Slate Mailer Organization</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.SmoName" xml:space="preserve">
    <value>Name of slate mailer organization</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.SmoId" xml:space="preserve">
    <value>ID#</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.StreetAddress" xml:space="preserve">
    <value>Street Address</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.MailingAddress" xml:space="preserve">
    <value>Mailing Address (if different)</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Phone" xml:space="preserve">
    <value>Phone Number</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Email" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Fax" xml:space="preserve">
    <value>Fax Number (optional)</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.Treasurer" xml:space="preserve">
    <value>Treasurer</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.TreasurerName" xml:space="preserve">
    <value>Name of treasurer</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.RecipientCommittee" xml:space="preserve">
    <value>Recipient Committee</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.CommitteeName" xml:space="preserve">
    <value>Committee Name</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.GeneralInformation.CommitteeId" xml:space="preserve">
    <value>ID#</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.Title" xml:space="preserve">
    <value>Filing Summary</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.ThisStatement" xml:space="preserve">
    <value>This Statement</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalYTD" xml:space="preserve">
    <value>Total Year to Date</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalPaymentsReceived" xml:space="preserve">
    <value>Total Payments Received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.FilingSummary.TotalPaymentsMade" xml:space="preserve">
    <value>Total Payments Made</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsReceivedTitle" xml:space="preserve">
    <value>Payments Received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeTitle" xml:space="preserve">
    <value>Payments Made</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeByAgentOrIndependentContractorTitle" xml:space="preserve">
    <value>Payments Made By An Agent or Independent Contractor on Behalf of A Slate Mailer Organization</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PersonsReceiving1000OrMore" xml:space="preserve">
    <value>Persons Receiving $1000 or More</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedTitle" xml:space="preserve">
    <value>Candidates and Measures Not Listed on Payments Received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.ReviewInstructions" xml:space="preserve">
    <value>Review Instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsReceived100OrMoreGridTitle" xml:space="preserve">
    <value>Payments Received of $100 or More</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMade100OrMoreGridTitle" xml:space="preserve">
    <value>Payments Made of $100 or More</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PaymentsMadeByAgentOrIndependentContractorGridTitle" xml:space="preserve">
    <value>Payments Made By An Agent or Independent Contractor on Behalf of A Slate Mailer Organization</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.PersonsReceiving1000OrMoreGridTitle" xml:space="preserve">
    <value>Persons Receiving $1000 or More</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedGridTitle" xml:space="preserve">
    <value>Candidates and Measures Not Listed on Payments Received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridDateHeader" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridNameHeader" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridCandidateOrMeasureHeader" xml:space="preserve">
    <value>Candidate/Measure</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridSupportOpposeHeader" xml:space="preserve">
    <value>Support/Oppose</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridAmountHeader" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridAgentOrContractorHeader" xml:space="preserve">
    <value>Agent/Contractor</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridDescriptionHeader" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridCumulativeAmountHeader" xml:space="preserve">
    <value>Cumulative Amount</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridOfficeOrMeasureHeader" xml:space="preserve">
    <value>Office or Measure</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.GridJurisdictionHeader" xml:space="preserve">
    <value>Jurisdiction</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsReceivedTitle" xml:space="preserve">
    <value>Unitemized Payments Received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsMadeTitle" xml:space="preserve">
    <value>Unitemized Payments Made</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.DeletePaymentConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this? This transaction will be removed from the current statement.</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.Subtotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsLessThan100" xml:space="preserve">
    <value>Unitemimized payments of less than $100</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.UnitemizedPaymentsLessThan100Placeholder" xml:space="preserve">
    <value>00.00</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Title" xml:space="preserve">
    <value>Add New Candidate or Ballot Measure</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.ChooseContact" xml:space="preserve">
    <value>Choose an existing contact</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.EnterIdOrName" xml:space="preserve">
    <value>Enter ID# or name</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Verification.Title" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Verification.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AddNewContact" xml:space="preserve">
    <value>Or add a new contact</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed02.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Individual" xml:space="preserve">
    <value>Individual (IND)</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Committee" xml:space="preserve">
    <value>Committee (COM)</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Candidate" xml:space="preserve">
    <value>Candidate without a committee (CAN)</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Other" xml:space="preserve">
    <value>Other (OTH)</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.FirstName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.MiddleName" xml:space="preserve">
    <value>Middle name</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.LastName" xml:space="preserve">
    <value>Last name</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AddressOfPayor" xml:space="preserve">
    <value>Address of payor</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.Employer" xml:space="preserve">
    <value>Employer</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EmployerDescription" xml:space="preserve">
    <value>If self-employed, enter name of business</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.SearchForCommittee" xml:space="preserve">
    <value>Search for a committee</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterCommitteeNameOrId" xml:space="preserve">
    <value>Enter committee name or ID#</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.CandidateMiddleName" xml:space="preserve">
    <value>Candidate middle name</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Transactors.CandidateLastName" xml:space="preserve">
    <value>Candidate last name</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.OfficeSought" xml:space="preserve">
    <value>Office sought</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.JurisdictionName" xml:space="preserve">
    <value>Jurisdiction name</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.District" xml:space="preserve">
    <value>District</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.OrganizationName" xml:space="preserve">
    <value>Organization name</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Title" xml:space="preserve">
    <value>About The Payment</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.PaymentPertain" xml:space="preserve">
    <value>Does this payment pertain to a candidate or ballot measure?</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Candidate" xml:space="preserve">
    <value>Candidate</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.BallotMeasure" xml:space="preserve">
    <value>Ballot Measure</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Jurisdiction" xml:space="preserve">
    <value>Jurisdiction</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Local" xml:space="preserve">
    <value>Local</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.AmountReceived" xml:space="preserve">
    <value>Amount received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.DateReceived" xml:space="preserve">
    <value>Date received</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.AttachFiles" xml:space="preserve">
    <value>Attach file(s)</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.AddCandidate" xml:space="preserve">
    <value>Add a candidate</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.AddMeasure" xml:space="preserve">
    <value>Add a measure</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.EnterIdOrBallotOrTitle" xml:space="preserve">
    <value>Enter #ID or ballot number/letter or title</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.BallotLetter" xml:space="preserve">
    <value>Ballot letter</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.FullTitleBallotMeasure" xml:space="preserve">
    <value>Full title of ballot measure</value>
  </data>
  <data name="FilerPortal.ChooseOfficer" xml:space="preserve">
    <value>Choose an existing officer</value>
  </data>
  <data name="FilerPortal.EnterName" xml:space="preserve">
    <value>Enter name</value>
  </data>
  <data name="FilerPortal.Transaction.AboutThePayment.AmountPaid" xml:space="preserve">
    <value>Amount paid</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.StatementReview.FilingPeriod" xml:space="preserve">
    <value>Filing period</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Title" xml:space="preserve">
    <value>Lobbying Advertisement</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.PublicationDate" xml:space="preserve">
    <value>Issue lobbying ad date of publication </value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.DistributionMethod" xml:space="preserve">
    <value>Distribution Method</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.DistributionMethodListSelectedHeader" xml:space="preserve">
    <value>Select Distribution Method</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.OtherDescription" xml:space="preserve">
    <value>Description [If Other is selected]</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Legislator" xml:space="preserve">
    <value>Legislator(s) or legislative candidates identified in ad</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.AdditionalInformation" xml:space="preserve">
    <value>Describe additional information (if any)</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyingAdvertisement.Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.GeneralInformation" xml:space="preserve">
    <value>General Information</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.PayFees" xml:space="preserve">
    <value>Pay Fees</value>
  </data>
  <data name="Common.StartRegistration" xml:space="preserve">
    <value>Start Registration</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Title" xml:space="preserve">
    <value>Slate Mailer Organization Registration</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.Title" xml:space="preserve">
    <value>New registration</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Organization" xml:space="preserve">
    <value>Organization</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Officers" xml:space="preserve">
    <value>Officers</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Common.IndividualAuthorizers" xml:space="preserve">
    <value>Individual Authorizers</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Common.Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Common.Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page02.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Body" xml:space="preserve">
    <value>UI Text Explanation under development and revieww</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.FirstName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.MiddleName" xml:space="preserve">
    <value>Middle name</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.LastName" xml:space="preserve">
    <value>Last name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page02.Title" xml:space="preserve">
    <value>First, we need to collect some general information.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Title" xml:space="preserve">
    <value>First, we need to collect some general information about the lobbyist.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Question" xml:space="preserve">
    <value>Are you registering for yourself or for the lobbyist?</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.Lobbyist" xml:space="preserve">
    <value>Lobbyist</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Search" xml:space="preserve">
    <value>Look up lobbyist by ID# or name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.EnterInformation" xml:space="preserve">
    <value>Or enter lobbyist information</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Add" xml:space="preserve">
    <value>Add lobbyist information</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Title" xml:space="preserve">
    <value>General Information</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.FirstName" xml:space="preserve">
    <value>Lobbyist first name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.MiddleName" xml:space="preserve">
    <value>Lobbyist middle name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.LastName" xml:space="preserve">
    <value>Lobbyist last name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Email" xml:space="preserve">
    <value>Lobbyist email address</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Title" xml:space="preserve">
    <value>In-house lobbyists</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Body" xml:space="preserve">
    <value>Please identify all of your in-house lobbyists</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Certification" xml:space="preserve">
    <value>Initiate Certification</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Add" xml:space="preserve">
    <value>Add lobbyist</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Title" xml:space="preserve">
    <value>Please identify all of the in-house lobbyists</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Id" xml:space="preserve">
    <value>ID#</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.Certified" xml:space="preserve">
    <value>Certified</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step02InitialLobbyistList.NotCertified" xml:space="preserve">
    <value>Not certified</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Title" xml:space="preserve">
    <value>Lobbying firms</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Description" xml:space="preserve">
    <value>Please identify all lobbying firms with which you contract.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Add" xml:space="preserve">
    <value>Add lobbying firm</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Id" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Step03LobbyingFirms.Table.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Title" xml:space="preserve">
    <value>Organization Name and Contact Information</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.OrganizationName" xml:space="preserve">
    <value>Full name of slate mailer organization</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.TelephoneNumber" xml:space="preserve">
    <value>Telephone number</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.FaxNumber" xml:space="preserve">
    <value>Fax number</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.OrganizationAddress" xml:space="preserve">
    <value>Organization Address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Street1" xml:space="preserve">
    <value>Street Address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Street1Caption" xml:space="preserve">
    <value>Cannot be a PO Box or PBM (Private Mailbox)</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Street2" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.County" xml:space="preserve">
    <value>County</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.TypeOfAddress" xml:space="preserve">
    <value>Type of Address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Residential" xml:space="preserve">
    <value>Residential</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.Business" xml:space="preserve">
    <value>Business</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.MailingAddress" xml:space="preserve">
    <value>Mailing Address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.SameAddress" xml:space="preserve">
    <value>Same as Organization Address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.Title" xml:space="preserve">
    <value>Organization Details</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.OrganizationLevelOfActivity" xml:space="preserve">
    <value>Organization's level of activity</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationQualified" xml:space="preserve">
    <value>Is the organization qualified?</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.DateQualifiedAsSMO" xml:space="preserve">
    <value>Date qualified as slate mailer organization</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommittee" xml:space="preserve">
    <value>Is the organization a campaign committee?</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommitteeCaption" xml:space="preserve">
    <value>[Instructional copy.]</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.CommitteeIDOrName" xml:space="preserve">
    <value>Provide the committee ID# OR name of the recipient committee</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.CommitteeIDOrNameMessage" xml:space="preserve">
    <value>Enter ID# or Name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page05.Title" xml:space="preserve">
    <value>We need to know about your organization's treasurer and other officers.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page05.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page06.Title" xml:space="preserve">
    <value>Treasurer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page06.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page06.IsTreasurer" xml:space="preserve">
    <value>Are you the treasurer?</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TitleAdd" xml:space="preserve">
    <value>Who is the treasurer?</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TitleEdit" xml:space="preserve">
    <value>Edit Treasurer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.FirstName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.MiddleName" xml:space="preserve">
    <value>Middle name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.LastName" xml:space="preserve">
    <value>Last name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TelephoneNumber" xml:space="preserve">
    <value>Telephone number</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Address" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.Address2" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.ZipCode" xml:space="preserve">
    <value>ZIP code</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TypeOfAddress" xml:space="preserve">
    <value>Type of address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.IsUserAuthorized" xml:space="preserve">
    <value>This user is able to authorize the contents of slate mailers.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.Title" xml:space="preserve">
    <value>Treasurer and other officers</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.Body" xml:space="preserve">
    <value>If your organization has an assistant treasurer or other principals officers, please add them now.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.AddOfficerMessage" xml:space="preserve">
    <value>If you do not want to add anyone, you may skip this step.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.AddOfficer" xml:space="preserve">
    <value>Add officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.AddAnotherOfficer" xml:space="preserve">
    <value>Add another officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerTitle" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerStartDate" xml:space="preserve">
    <value>Start date</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.OfficerPhoneNumber" xml:space="preserve">
    <value>Phone number</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.DeleteOfficerModalTitle" xml:space="preserve">
    <value>Delete Officers</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page08.DeleteOfficerModalMessage" xml:space="preserve">
    <value>Are you sure you want to delete this? Information regarding this officer will be permanently removed.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page09.Title" xml:space="preserve">
    <value>Add new officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page09.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page09.IsOfficer" xml:space="preserve">
    <value>Are you the officer?</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TitleAdd" xml:space="preserve">
    <value>Add new officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TitleEdit" xml:space="preserve">
    <value>Edit officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.OfficerTitle" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.SelectOfficerTitle" xml:space="preserve">
    <value>Select title</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.FirstName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.MiddleName" xml:space="preserve">
    <value>Middle name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.LastName" xml:space="preserve">
    <value>Last name</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TelephoneNumber" xml:space="preserve">
    <value>Telephone number</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Address" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.Address2" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.ZipCode" xml:space="preserve">
    <value>ZIP code</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.TypeOfAddress" xml:space="preserve">
    <value>Type of address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.IsUserAuthorized" xml:space="preserve">
    <value>This user is able to authorize the contents of slate mailers.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.CancelOfficerText" xml:space="preserve">
    <value>Are you sure? The entry will be deleted and all data will be lost.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page10.CancelOfficerHeaderText" xml:space="preserve">
    <value>Cancel and Delete Officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page11.Title" xml:space="preserve">
    <value>Finally, we need to know who authorizes the content for the slate mailers.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page11.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.Title" xml:space="preserve">
    <value>Individuals who authorize content of slate mailers</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.AddAuthorizerMessage" xml:space="preserve">
    <value>Please add at least one authorizer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.AddAuthorizerButton" xml:space="preserve">
    <value>Add individual authorizer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.DeleteAuthorizerModalTitle" xml:space="preserve">
    <value>Delete Individual Authorizer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page12.DeleteAuthorizerModalMessage" xml:space="preserve">
    <value>Are you sure you want to delete this? The authorizer will be removed from the list of content authorizers.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page13.AuthorizerAddress" xml:space="preserve">
    <value>Authorizer's address</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Title" xml:space="preserve">
    <value>Treasurer</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.AreYouAmending" xml:space="preserve">
    <value>Are you amending the treasurer information?</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option1" xml:space="preserve">
    <value>Yes, update the current treasurer information</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option2" xml:space="preserve">
    <value>Yes, replace the current treasurer with an existing officer</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option3" xml:space="preserve">
    <value>Yes, replace the current treasurer with a new treasurer</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page03.Option4" xml:space="preserve">
    <value>No, skip this section</value>
  </data>
  <data name="FilerPortal.Registration.TerminateRegistrationDisplayText" xml:space="preserve">
    <value>If you want to terminate the registration without making any changes, please click on step 4 - Termination.</value>
  </data>
  <data name="Common.AmendTerminiateRegistration" xml:space="preserve">
    <value>Amend/Terminate Registration</value>
  </data>
  <data name="Common.Termination" xml:space="preserve">
    <value>Termination</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Common.Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="Common.SaveAndClose" xml:space="preserve">
    <value>Save and Close</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.SectionTitle" xml:space="preserve">
    <value>State Candidate Expenditure Limit Statement</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.SectionDescription" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.YearText" xml:space="preserve">
    <value>FPPC expenditure ceiling amount for the selected office for </value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.RadioLabel" xml:space="preserve">
    <value>State candidate expenditure limit</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Accept" xml:space="preserve">
    <value>I accept the voluntary expenditure ceiling for the election stated above</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Decline" xml:space="preserve">
    <value>I do not accept the voluntary expenditure ceiling for the election stated above</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.ActionLobbied" xml:space="preserve">
    <value>Actions Lobbied</value>
  </data>
  <data name="CandidateIntentionStatement.VerifyHeading" xml:space="preserve">
    <value>The Candidate Intention Statement must be verified and attested to by the Candidate.</value>
  </data>
  <data name="CandidateIntentionStatement.VerifyInstructions" xml:space="preserve">
    <value>When the Statement is ready to submit, click Send for Candidate Attestation and a notification will be sent to the Candidate to allow them to verify and submit.
  </value>
  </data>
  <data name="CandidateIntentionStatement.VerifyNotice" xml:space="preserve">
    <value>The Candidate Intention Statement will not be submitted until after the verification is complete</value>
  </data>
  <data name="CandidateIntentionStatement.VerifySendforAttestation" xml:space="preserve">
    <value>Send for Candidate Attestation</value>
  </data>
  <data name="Common.SavedMessage" xml:space="preserve">
    <value>Saved</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.Subtotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page03.ZipCode" xml:space="preserve">
    <value>ZIP Code</value>
  </data>
  <data name="Common.FieldIsRequired" xml:space="preserve">
    <value>{0} is required.</value>
  </data>
  <data name="Common.Optional" xml:space="preserve">
    <value>Optional</value>
  </data>
  <data name="Common.Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="progress-status-pending" xml:space="preserve">
    <value>pending step</value>
  </data>
  <data name="progress-status-completed" xml:space="preserve">
    <value>completed step</value>
  </data>
  <data name="progress-status-inprogress" xml:space="preserve">
    <value>current step</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Common.View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="Common.Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Common.AddNew" xml:space="preserve">
    <value>Add new</value>
  </data>
  <data name="Common.UploadTransactions" xml:space="preserve">
    <value>Upload transactions</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="Common.CancelConfirmationHeader" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.NotificationType" xml:space="preserve">
    <value>Notification Type</value>
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.NotificationName" xml:space="preserve">
    <value>Notification Name</value>
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.FilerType" xml:space="preserve">
    <value>Filer Type</value>
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.ActionRequired" xml:space="preserve">
    <value>Action Required</value>
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.Title" xml:space="preserve">
    <value>Manage Notifications</value>
  </data>
  <data name="PRDPortal.NotificationTemplate.Manage.DeleteConfirmationMessage" xml:space="preserve">
    <value>Are you sure you want to delete the notification {0}?</value>
  </data>
  <data name="CandidateIntentionStatement.Email" xml:space="preserve">
    <value>Candidate email</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAddress.Type" xml:space="preserve">
    <value>Type of address</value>
  </data>
  <data name="CandidateIntentionStatement.MailingAddress.Type" xml:space="preserve">
    <value>Type of address</value>
  </data>
  <data name="FilerPortal.Home.ReferencePage.RegistrationDate" xml:space="preserve">
    <value>Registration Date</value>
  </data>
  <data name="FilerPortal.Home.ReferencePage.FilingDate" xml:space="preserve">
    <value>Filing Date</value>
  </data>
  <data name="FilerPortal.Home.ReferencePage.ExpenditureErrorMessage" xml:space="preserve">
    <value>Value must be between 0 and 500</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ElectionYear" xml:space="preserve">
    <value>Election Year</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.OfficeSought" xml:space="preserve">
    <value>Office sought</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.DistrictNumber" xml:space="preserve">
    <value>District number</value>
  </data>
  <data name="Common.County" xml:space="preserve">
    <value>County</value>
  </data>
  <data name="Common.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.AgencyName" xml:space="preserve">
    <value>Agency name</value>
  </data>
  <data name="CandidateIntentionStatement.Election02.Title" xml:space="preserve">
    <value>Election information</value>
  </data>
  <data name="CandidateIntentionStatement.Election02.Body01" xml:space="preserve">
    <value>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</value>
  </data>
  <data name="CandidateIntentionStatement.Election02.Body02" xml:space="preserve">
    <value>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CancelReportSuccessMessage" xml:space="preserve">
    <value>Report cancelled successfully</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.UpdateTransactionSuccessMessage" xml:space="preserve">
    <value>Transaction updated successfully</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.UpdateFilingPeriodSuccessMessage" xml:space="preserve">
    <value>Filing Period updated successfully</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.UpdateFailMessage" xml:space="preserve">
    <value>Failed to update. Please try again</value>
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.UserGroupTitle" xml:space="preserve">
    <value>User Groups</value>
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.DeleteUserGroupConfirmationMessage" xml:space="preserve">
    <value>Are you sure you want to delete the user group {0}?</value>
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.UserGroupName" xml:space="preserve">
    <value>User Group Name</value>
  </data>
  <data name="PRDPortal.UserGroup.ListGroups.UserGroupDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="FilerPortal.Common.Title" xml:space="preserve">
    <value>Filer Portal</value>
  </data>
  <data name="CandidateIntentionStatement.Common.Title" xml:space="preserve">
    <value>Candidate Intention Statement</value>
  </data>
  <data name="CandidateIntentionStatement.Common.Breadcrumb" xml:space="preserve">
    <value>Candidate Registration</value>
  </data>
  <data name="Common.Candidate" xml:space="preserve">
    <value>Candidate</value>
  </data>
  <data name="Common.Verification" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="CandidateIntentionStatement.New.Title" xml:space="preserve">
    <value>Candidate Intention Statement</value>
  </data>
  <data name="CandidateIntentionStatement.New.Body" xml:space="preserve">
    <value>For State and Judicial Candidates</value>
  </data>
  <data name="CandidateIntentionStatement.Page01.Title" xml:space="preserve">
    <value>Candidate Intention Statement</value>
  </data>
  <data name="CandidateIntentionStatement.Page01.Body" xml:space="preserve">
    <value>For State and Judicial Candidates</value>
  </data>
  <data name="CandidateIntentionStatement.Page02.Title" xml:space="preserve">
    <value>First, we need to collect some general information about the candidate.</value>
  </data>
  <data name="CandidateIntentionStatement.Page02.Body" xml:space="preserve">
    <value>Candidates are required to identify the office and the election they are seeking.</value>
  </data>
  <data name="Common.Step" xml:space="preserve">
    <value>Step</value>
  </data>
  <data name="CandidateIntentionStatement.Page03.Title" xml:space="preserve">
    <value>Candidate information</value>
  </data>
  <data name="CandidateIntentionStatement.Page03.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="CandidateIntentionStatement.Page04.Title" xml:space="preserve">
    <value>Candidate information</value>
  </data>
  <data name="CandidateIntentionStatement.Page04.Body" xml:space="preserve">
    <value>Confirm the information provided below is correct and up to date.</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.AddNewContact" xml:space="preserve">
    <value>Or add a new contact</value>
  </data>
  <data name="CandidateIntentionStatement.SubmissionReceived" xml:space="preserve">
    <value>Submission Received</value>
  </data>
  <data name="Common.Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="CandidateIntentionStatement.VerifyTitle" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="CandidateIntentionStatement.VerifyMessage" xml:space="preserve">
    <value>You are ready to submit your registration</value>
  </data>
  <data name="CandidateIntentionStatement.SubmissionMessageNonCandidate" xml:space="preserve">
    <value>This Candidate Intention Statement is pending until the candidate completes the verification.</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.AboutThePayee" xml:space="preserve">
    <value>About the payee</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.EnterPayee" xml:space="preserve">
    <value>Enter payee information</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.TransactionDate" xml:space="preserve">
    <value>Date of contribution</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.IsRecipientCommittee" xml:space="preserve">
    <value>Is the recipient a committee?</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.NonCommitteeRecipentName" xml:space="preserve">
    <value>Name of recipient</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.RecipientCommitteeFilerId" xml:space="preserve">
    <value>What committees?</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.NonFilerContributorName" xml:space="preserve">
    <value>Name of Contributor</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.NewTransaction.SeparateAccountName" xml:space="preserve">
    <value>Name of Separate Account (if applicable)</value>
  </data>
  <data name="CandidateIntentionStatement.Page05.Title" xml:space="preserve">
    <value>Now, we need to collect some  information about the election.</value>
  </data>
  <data name="CandidateIntentionStatement.Page05.Body" xml:space="preserve">
    <value>Candidates are required to provide the election’s year, jurisdiction, office sought, and district number if any.</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage" xml:space="preserve">
    <value>Transaction cancelled successfully</value>
  </data>
  <data name="CandidateIntentionStatement.Page06.Title" xml:space="preserve">
    <value>Election information</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Amount" xml:space="preserve">
    <value>Amount this period</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.ID" xml:space="preserve">
    <value>ID#</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.LobbyingCoalition" xml:space="preserve">
    <value>Lobbying Coalition</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Title" xml:space="preserve">
    <value>Payments made to lobbying coalitions</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Instruction" xml:space="preserve">
    <value>Review Instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Subtotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.SaveAndClose" xml:space="preserve">
    <value>Save and close</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.LobbyingCoalitionMember" xml:space="preserve">
    <value>Are you a member of a lobbying coalition?</value>
  </data>
  <data name="FilerPortal.Disclosure.Index.Title" xml:space="preserve">
    <value>Lobbying Report</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCampaignContribution.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyistCampaignContribution.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Filing.Lobbyist.Title" xml:space="preserve">
    <value>Lobbyist</value>
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.Title" xml:space="preserve">
    <value>Lobbyist Employer</value>
  </data>
  <data name="CandidateIntentionStatement.Page07.Title" xml:space="preserve">
    <value>State Candidate Expenditure Limit Statement</value>
  </data>
  <data name="CandidateIntentionStatement.Page07.Body" xml:space="preserve">
    <value>All state candidates must specify whether the candidate will accept or reject the voluntary expenditure ceiling applicable to the office set forth in FPPC Regulation 18545.</value>
  </data>
  <data name="CandidateIntentionStatement.Page08.Title" xml:space="preserve">
    <value>You are ready to submit your registration</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.DistrictOrCounty" xml:space="preserve">
    <value>District / County</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.MultipleTransaction" xml:space="preserve">
    <value>Upload Transactions</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MessageFilerName" xml:space="preserve">
    <value>Filer Name</value>
  </data>
  <data name="CandidateIntentionStatement.ElectionInformation.ExpenditureLimit.Title" xml:space="preserve">
    <value>State Candidate Expenditure Limit Statement</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Common.InvalidSubmission" xml:space="preserve">
    <value>Invalid Submission</value>
  </data>
  <data name="CandidateIntentionStatement.Common.CreatedSuccess" xml:space="preserve">
    <value>Candidate registration created successfully.</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.NameOfLobbyingCoalition" xml:space="preserve">
    <value>Name of lobbying coalition</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page02.Title" xml:space="preserve">
    <value>Lobbying coalition information</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page02.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Title" xml:space="preserve">
    <value>Payments received by lobbying coalitions [if lobbying coalition]</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Body" xml:space="preserve">
    <value>Disclose payments received from coalition members. Enter zero if a member has not made a payment during the quarter. </value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Instructions" xml:space="preserve">
    <value>Review instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Instructions" xml:space="preserve">
    <value>Review instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Subtitle" xml:space="preserve">
    <value>Payments received by lobbying coalitions</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Subtitle" xml:space="preserve">
    <value>Payments to lobbying firms</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Subtotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Subtotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.AddNew" xml:space="preserve">
    <value>Add new</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.SaveAndClose" xml:space="preserve">
    <value>Save and close</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.SaveAndClose" xml:space="preserve">
    <value>Save and close</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Common.Attest" xml:space="preserve">
    <value>Attest</value>
  </data>
  <data name="Common.Terminate" xml:space="preserve">
    <value>Terminate</value>
  </data>
  <data name="Common.SendForAttestation" xml:space="preserve">
    <value>Send For Attestation</value>
  </data>
  <data name="FilerPortal.Contact.CommonFields.Title" xml:space="preserve">
    <value>Address of payee</value>
  </data>
  <data name="FilerPortal.Contact.CommonFields.Street2" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="Common.Street" xml:space="preserve">
    <value>Street</value>
  </data>
  <data name="Common.StreetAddress" xml:space="preserve">
    <value>Street Address</value>
  </data>
  <data name="Common.ZipCode" xml:space="preserve">
    <value>ZIP Code</value>
  </data>
  <data name="Common.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.Title" xml:space="preserve">
    <value>Payee Information</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.PayeeType" xml:space="preserve">
    <value>Payee type</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.Individual" xml:space="preserve">
    <value>Individual</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.Organization" xml:space="preserve">
    <value>Organization</value>
  </data>
  <data name="Common.FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="Common.MiddleName" xml:space="preserve">
    <value>Middle Name</value>
  </data>
  <data name="Common.LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.OrganizationName" xml:space="preserve">
    <value>Organization Name</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.CreateSuccessMessage" xml:space="preserve">
    <value>Contact created successfully</value>
  </data>
  <data name="FilerPortal.Contact.LobbyingFirm.CreateSuccessMessage" xml:space="preserve">
    <value>Lobbying Firm created successfully</value>
  </data>
  <data name="FilerPortal.Contact.LobbyingFirm.UpdateSuccessMessage" xml:space="preserve">
    <value>Lobbying Firm updated successfully</value>
  </data>
  <data name="Common.APIRequestError" xml:space="preserve">
    <value>An error occurred while processing your request</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.CreateErrorMessage" xml:space="preserve">
    <value>There was an error creating the new Contact. Please try again.</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.UploadTransaction" xml:space="preserve">
    <value>Upload Transactions</value>
  </data>
  <data name="Common.TelephoneNumber" xml:space="preserve">
    <value>Telephone number</value>
  </data>
  <data name="Common.FaxNumber" xml:space="preserve">
    <value>Fax number</value>
  </data>
  <data name="Common.EmailAddress" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="Common.Street2" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.PermissionName" xml:space="preserve">
    <value>Permission Name</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.PermissionDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.ChooseExistingContact" xml:space="preserve">
    <value>Choose an existing contact</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="Common.Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.SubmitExpense" xml:space="preserve">
    <value>Submit expense</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AboutTheExpense" xml:space="preserve">
    <value>About the expense</value>
  </data>
  <data name="Common.Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ChargedOnCreditCard" xml:space="preserve">
    <value>Was activity charged on credit card?</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.CreditCardCompany" xml:space="preserve">
    <value>Name of credit card company</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ActivityExpenseType" xml:space="preserve">
    <value>Activity expense type</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AdditionalInfo" xml:space="preserve">
    <value>Additional information</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ReportablePersonsTitle" xml:space="preserve">
    <value>Reportable Persons Benefiting from Activity</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AddReportablePerson" xml:space="preserve">
    <value>Add reportable person</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AdditionalInfoTip" xml:space="preserve">
    <value>If more than one payee per activity expense, list the payees in the explanation field.</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.ActivityDescription" xml:space="preserve">
    <value>Activity Description</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.CreateErrorMessage" xml:space="preserve">
    <value>There was an error creating the new Activity Expense. Please try again.</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyingFirm" xml:space="preserve">
    <value>Lobbying firm (if employee of lobbying firm)</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer" xml:space="preserve">
    <value>Lobbyist employer (if in-house lobbyist employer)</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage" xml:space="preserve">
    <value>Report successfully submitted.</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.GroupPageTitle" xml:space="preserve">
    <value>Add User Group</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.PermissionGridTitle" xml:space="preserve">
    <value>Permissions</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.EntraIdGroupIdentifier" xml:space="preserve">
    <value>Entra Id Group Identifier</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.AuthorizationGroupName" xml:space="preserve">
    <value>User Group Name</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.AuthorizationGroupDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Common.Notifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="CandidateIntentionStatement.CancelText" xml:space="preserve">
    <value>Are you sure? The draft registration will be deleted and all data will be lost.</value>
  </data>
  <data name="CandidateIntentionStatement.CancelHeaderText" xml:space="preserve">
    <value>Cancel and Delete Draft Application</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.CancelText" xml:space="preserve">
    <value>Are you sure you want to exit? Any changes will be discarded.</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.CancelHeaderText" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="PRDPortal.UserGroup.AddGroup.EditGroupPageTitle" xml:space="preserve">
    <value>View/Edit User Group</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.SubTitle" xml:space="preserve">
    <value>Payment made to lobbying coalition</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.DeleteFailureMessage" xml:space="preserve">
    <value>This notification has an unresolved action required and cannot be deleted</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this notification? ({0})</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.DateReceived" xml:space="preserve">
    <value>Date Received</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.DueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.FilerType" xml:space="preserve">
    <value>Filer Type</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.NotificationType" xml:space="preserve">
    <value>Notification Type</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.ActionRequired" xml:space="preserve">
    <value>Action Required</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.PriorityNotification" xml:space="preserve">
    <value>Priority Notification</value>
  </data>
  <data name="Common.DeleteFailureMessage" xml:space="preserve">
    <value>An error occured while attempting to delete the record</value>
  </data>
  <data name="Common.CancelHeaderText" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="Common.CancelBodyText" xml:space="preserve">
    <value>Are you sure you want to exit? Any changes will be discarded.</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionReceived.MultipleTransaction" xml:space="preserve">
    <value>Upload transactions</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.MultipleTransaction" xml:space="preserve">
    <value>Upload transactions</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page02.Body2" xml:space="preserve">
    <value>A slate mailer organization filer must include the legal name of the individual or business in the name of the slate mailer organization, except a slate mailer organization that is not an individual or business.</value>
  </data>
  <data name="Common.Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.Title" xml:space="preserve">
    <value>About the member of the coalition</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.SubTitle" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.SubTitle" xml:space="preserve">
    <value>Payments received by lobbying coalition</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.OrAddContact" xml:space="preserve">
    <value>Or add a new contact</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.EnterContactInfo" xml:space="preserve">
    <value>Enter contact information</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.02_EnterContact.Title" xml:space="preserve">
    <value>Coalition member information</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.02_EnterContact.SubTitle" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.Title" xml:space="preserve">
    <value>About the payment</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.SubTitle" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Description" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Title" xml:space="preserve">
    <value>Filing Summary</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Description" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Preview" xml:space="preserve">
    <value>Preview PDF</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.ReportPeriod" xml:space="preserve">
    <value>This reporting period</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.CumulativeDate" xml:space="preserve">
    <value>Cumulative to date</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.TotalActivityExpenses" xml:space="preserve">
    <value>Total activity expenses</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.TotalCampaignContributions" xml:space="preserve">
    <value>Total campaign contributions</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.SaveAndClose" xml:space="preserve">
    <value>Save and close</value>
  </data>
  <data name="FilerPortal.Lobbyist.FillingSummary.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.TotalActivityExpenses" xml:space="preserve">
    <value>Total activity expenses</value>
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.TotalCampaignContributions" xml:space="preserve">
    <value>Total campaign contributions</value>
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.ReportPeriod" xml:space="preserve">
    <value>This reporting period</value>
  </data>
  <data name="FilerPortal.Lobbyist.FilingSummary.CumulativeDate" xml:space="preserve">
    <value>Cumulative to date</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Title" xml:space="preserve">
    <value>Activity Expenses</value>
  </data>
  <data name="Common.ProgressBar.Modal.Title" xml:space="preserve">
    <value>Cancel Confirmation</value>
  </data>
  <data name="Common.ProgressBar.Modal.Body" xml:space="preserve">
    <value>Are you sure you want to leave the current screen? Your changes will be lost.</value>
  </data>
  <data name="Common.ProgressBar.Modal.CloseButtonText" xml:space="preserve">
    <value>No, stay</value>
  </data>
  <data name="Common.ProgressBar.Modal.SubmitButtonText" xml:space="preserve">
    <value>Yes, leave</value>
  </data>
  <data name="Common.ProgressBar.NavigableDisplayText" xml:space="preserve">
    <value>Click any step above to proceed to the corresponding section</value>
  </data>
  <data name="Common.DatePlaceHolder" xml:space="preserve">
    <value>Select a Date</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page03.Title" xml:space="preserve">
    <value>About the payment</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page03.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.EmailAddress" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.ContactType" xml:space="preserve">
    <value>Contact type</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.AddressOfCoalitionMember" xml:space="preserve">
    <value>Address of Coalition Member</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.ContactTypeTooltip" xml:space="preserve">
    <value>Cumque quis tempora aliquid iusto nostrum ad.</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.ReviewInstructions" xml:space="preserve">
    <value>Review instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.UploadTransactions" xml:space="preserve">
    <value>Upload transactions</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Subtotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.PersonalFunds.Header" xml:space="preserve">
    <value>Personal funds declaration (must file within 24 hours of contribution, if applicable):</value>
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.PersonalFunds.Checkbox" xml:space="preserve">
    <value>I  contributed personal funds in excess of the expenditure ceiling shown above for the election on:</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CreateTransactionSuccessMessage" xml:space="preserve">
    <value>Transaction created successfully</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.03_EnterAmount.AmountOfPayment" xml:space="preserve">
    <value>Amount of Payment</value>
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.Amendment.Body" xml:space="preserve">
    <value>Amendment for candidates advancing to General or Run-off election:
(available for the 14 days following the Primary or Special election)</value>
  </data>
  <data name="FilerPortal.AmendCandidateRegistration.Page03.Amendment.Checkbox" xml:space="preserve">
    <value>I did not exceed the expenditure ceiling in the primary or special election stated above and I accept the voluntary expenditure ceiling for the General or Run-off election</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationQualifiedTooltip" xml:space="preserve">
    <value>Date qualified is the date the organization received or was promised payments totaling $500 or more in a calendar year for the production of one or more slate mailers.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page04.IsOrganizationCampaignCommitteeTooltip" xml:space="preserve">
    <value>A slate mailer organization may also qualify as a recipient committee if it receives contributions of $2,000 or more in a calendar year, NOT including payments it receives from candidates or measures appearing on the slate mailer.</value>
  </data>
  <data name="CopyrightStatement" xml:space="preserve">
    <value>Copyright &amp;copy; 2024 California Secretary of State</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.Title" xml:space="preserve">
    <value>Address Confirmation</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.StreetAddress" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.MailingAddress" xml:space="preserve">
    <value>Mailing address</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.Entered" xml:space="preserve">
    <value>Entered</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.Suggested" xml:space="preserve">
    <value>Suggested</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.UseSuggested" xml:space="preserve">
    <value>Use suggested</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.SaveAsEntered" xml:space="preserve">
    <value>Save as entered</value>
  </data>
  <data name="FilerPortal.AccuMailValidation.ContinueEditing" xml:space="preserve">
    <value>Continue editing</value>
  </data>
  <data name="Common.Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="CandidateIntentionStatement.Page06.Body" xml:space="preserve">
    <value>Provide the candidate’s election year. Select if the candidate’s election falls under primary/general or special/runoff, the office sought, district number or county if applicable and identify the agency name.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.IsTreasurerTitle" xml:space="preserve">
    <value>You are ready to complete your treasurer acknowledgment.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.IsNotTreasurerTitle" xml:space="preserve">
    <value>Treasurer Acknowledgment</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.TreasurerAcknowledgement" xml:space="preserve">
    <value>By checking the box next to my signature, I acknowledge under penalty of perjury that I must comply with all applicable duties stated in the Political Reform Act and the regulations of the Commission and that a violation of these duties could result in criminal, civil, or administrative penalties.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page14.SendForAcknowledgement" xml:space="preserve">
    <value>Send for Acknowledgment</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page14And15.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page14And15.Body2" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page14And15.ResponsibleOfficers" xml:space="preserve">
    <value>Name and title of responsible officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.Title" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.VerificationCertification" xml:space="preserve">
    <value>I have used all reasonable diligence in preparing this statement. I have reviewed this statement and, to the best of my knowledge, the information contained in it is true and complete. I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.OfficerSelectRequirement" xml:space="preserve">
    <value>At least one officer must be selected.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.OfficerDropdownHeader" xml:space="preserve">
    <value>Name and title of responsible officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.OfficerDropdown" xml:space="preserve">
    <value>Select officer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.Attest" xml:space="preserve">
    <value>Attest</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page15.SendForAttestation" xml:space="preserve">
    <value>Send for Attestation</value>
  </data>
  <data name="FilerPortal.SmoRegistration.SubmissionReceived" xml:space="preserve">
    <value>Submission received.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.SubmissionBody" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.SmoRegistration.PendingItems" xml:space="preserve">
    <value>Pending items (if necessary)</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.MakeTreasurer" xml:space="preserve">
    <value>Make Treasurer</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.MakeOfficerTreasurer" xml:space="preserve">
    <value>Are you sure you want to make this officer the treasurer?</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.RemoveCurrentTreasurer" xml:space="preserve">
    <value>Yes and remove the current treasurer from the organization</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.ReplaceCurrentTreasurer" xml:space="preserve">
    <value>Yes and the current treasurer becomes</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page06.Cancel" xml:space="preserve">
    <value>No, cancel</value>
  </data>
  <data name="Common.PreviewPDF" xml:space="preserve">
    <value>Preview PDF</value>
  </data>
  <data name="CandidateIntentionStatement.SubmissionMessageCandidate" xml:space="preserve">
    <value>Please note: to change, update or correct the information provided, an amendment must be submitted.</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.FullName" xml:space="preserve">
    <value>Full name</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.OfficialPosition" xml:space="preserve">
    <value>Official position</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.OfficialPositionDescription" xml:space="preserve">
    <value>Official position description</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Agency" xml:space="preserve">
    <value>Agency</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.AgencyDescription" xml:space="preserve">
    <value>Agency description</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Amount" xml:space="preserve">
    <value>Portion of the total activity expense attributable to the person</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.PayeeInfo" xml:space="preserve">
    <value>Payee Information</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.NameOfPayee" xml:space="preserve">
    <value>Name of Payee</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.PayeeType" xml:space="preserve">
    <value>Payee Type</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.AddressOfPayee" xml:space="preserve">
    <value>Address of Payee</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Header" xml:space="preserve">
    <value>Activity Expense</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.PersonBenefittingActivity" xml:space="preserve">
    <value>Add reportable person benefiting from activity</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpensePayee.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Contact.ReportablePersonForm.Header" xml:space="preserve">
    <value>Activity Expense</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.Header" xml:space="preserve">
    <value>Activity Expense</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.Header" xml:space="preserve">
    <value>Activity Expense</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.PersonBenefitingActivity" xml:space="preserve">
    <value>Add reportable person benefiting from activity</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.AddNewContact" xml:space="preserve">
    <value>Or add a new contact</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpenseRP.EnterInformation" xml:space="preserve">
    <value>Enter information</value>
  </data>
  <data name="PRDPortal.FilerRole.ListGroups.FilingRole" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="PRDPortal.FilerRole.ListGroups.DeleteUserFilingRoleConfirmationMessage" xml:space="preserve">
    <value>Are you sure you want to delete the Filing Role {0}?</value>
  </data>
  <data name="Common.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MarkAsRead" xml:space="preserve">
    <value>Mark as Read</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Messages.MarkUnread" xml:space="preserve">
    <value>Mark as Unread</value>
  </data>
  <data name="FilerPortal.NotificationMessage.Details.FilerName" xml:space="preserve">
    <value>Filer Name</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.CancelSuccessMessage" xml:space="preserve">
    <value>Cancelled successfully</value>
  </data>
  <data name="FilerPortal.HeaderBar.Title" xml:space="preserve">
    <value>CAL-ACCESS Filer Portal</value>
  </data>
  <data name="FilerPortal.HomeScreen.Text" xml:space="preserve">
    <value>The California Automated Lobbyist and Campaign Contribution and Expenditure Search System (CAL-ACCESS) serves as the public's window into California's campaign disclosure and lobbying financial activity. It provides financial information from state candidates, donors, lobbyists, lobbyist employers, and others in an easy-to-use, data-driven system. Filers subject to the Political Reform Act's Chapter 4.6, Online Disclosure Act of 1997 (§ 84600 – 84616), may submit required filings free of charge.</value>
  </data>
  <data name="PRDPortal.FilerRole.ListFilerRoles.Title" xml:space="preserve">
    <value>User Filing Roles</value>
  </data>
  <data name="PRDPortal.FilerRole.AddRole.Title" xml:space="preserve">
    <value>Create Filing Role</value>
  </data>
  <data name="PRDPortal.FilerRole.AddRole.FilingRoleName" xml:space="preserve">
    <value>Filing Role Name</value>
  </data>
  <data name="PRDPortal.FilerRole.AddRole.FilerType" xml:space="preserve">
    <value>Filer Type</value>
  </data>
  <data name="PRDPortal.FilerRole.EditRole.Title" xml:space="preserve">
    <value>Edit Filing Role</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Title" xml:space="preserve">
    <value>Filing summary</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Body01" xml:space="preserve">
    <value>Verify the amount listed in the reporting period for total activity expenses and campaign contributions. </value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Body02" xml:space="preserve">
    <value>The transaction history recorded can be viewed in this filing summary.</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Title" xml:space="preserve">
    <value>Your statement</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Body01" xml:space="preserve">
    <value>Review the general information to ensure it is correct and up to date. Select whether or not you have reportable activity expenses or campaign contributions. Any reportable expenses or contributions can be reviewed in the filing summary.</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Body02" xml:space="preserve">
    <value>Review the general information to ensure it is correct and up to date. Identify the actions lobbied, if any. Additionally, select whether or not you have reportable transactions. Reportable transactions can be viewed in the filing summary. </value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Title" xml:space="preserve">
    <value>General information</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Body01" xml:space="preserve">
    <value>An “activity expense” is any payment that benefits, in whole or in part, a reportable person. Gifts are the most commonly reported activity expenses, including food, beverages, travel and entertainment tickets. Activity expenses can also include any form of compensation, such as consulting fees and salaries. The instructions provide more information on valuing gifts and the exceptions to those reported. </value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.GeneralInformation" xml:space="preserve">
    <value>General information</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Body02" xml:space="preserve">
    <value>[Reportable Expenses]</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Body03" xml:space="preserve">
    <value>Provide only the activity expenses incurred, arranged or paid by you during this reporting period. </value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.Title" xml:space="preserve">
    <value>Lobbyist reports</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.NonRegisteredLobbyistsTitle" xml:space="preserve">
    <value>Non-Registered Lobbyists</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.Body" xml:space="preserve">
    <value>All the names of in-house lobbyists registered should appear here.</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.RegisteredLobbyistsTitle" xml:space="preserve">
    <value>Registered Lobbyists</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.RegisteredLobbyistsBody" xml:space="preserve">
    <value>Additional registration forms must be submitted to add or remove an in-house lobbyist.</value>
  </data>
  <data name="FilerPortal.Disclosure.PUCActivity.Title" xml:space="preserve">
    <value>PUC Activity</value>
  </data>
  <data name="FilerPortal.Disclosure.PUCActivity.Body" xml:space="preserve">
    <value>Report payments made in connection with administrative testimony in PUC ratemaking or quasi-legislative proceedings, unless payments are made to a lobbyists or lobbying firm.</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionPayments.Body" xml:space="preserve">
    <value>Disclose payments made to each lobbying coalition. If no payment was made, enter zero. </value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Title" xml:space="preserve">
    <value>Other payments to influence legislative or administrative action</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Title" xml:space="preserve">
    <value>Payments to lobbying firms</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.AddNew" xml:space="preserve">
    <value>Add new</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionConfirm.Title" xml:space="preserve">
    <value>Payments to Lobbying Coalitions</value>
  </data>
  <data name="FilerPortal.Disclosure.LumpPayment.Title" xml:space="preserve">
    <value>Other payments to influence legislative or administrative action</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Body" xml:space="preserve">
    <value>Total payments of $250 or more or $2,500 or more, as applicable, during the calendar quarter for lobbying activity (excluding overhead). Itemize payments below.</value>
  </data>
  <data name="FilerPortal.Disclosure.FirmPayments.Body" xml:space="preserve">
    <value>Including individual contract lobbyists.</value>
  </data>
  <data name="FilerPortal.Disclosure.CoalitionConfirm.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.LumpPayment.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Filing.Verification.Title" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.Filing.Verification.DiligenceStatement" xml:space="preserve">
    <value>I have used all reasonable diligence in preparing this Statement. I have reviewed the statement and to the best of my knowledge the information contained herein is true and complete. I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="FilerPortal.Filing.Verification.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.Filing.Verification.ExecutedOn" xml:space="preserve">
    <value>Executed on</value>
  </data>
  <data name="FilerPortal.Filing.Verification.PreviewPDF" xml:space="preserve">
    <value>Preview PDF</value>
  </data>
  <data name="FilerPortal.Filing.Verification.TitleRow" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="FilerPortal.Filing.Verification.Attest" xml:space="preserve">
    <value>Attest</value>
  </data>
  <data name="FilerPortal.Filing.Submitted.SubmissionReceived" xml:space="preserve">
    <value>Submission received.</value>
  </data>
  <data name="Common.SubmissionReceived" xml:space="preserve">
    <value>Submission Received</value>
  </data>
  <data name="FilerPortal.Filing.Submitted.Note" xml:space="preserve">
    <value>Please note: to change, update or correct the information provided, an amendment must be submitted.</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SubmitReportErrorMessage" xml:space="preserve">
    <value>There was an error submitting the report.</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.Payee" xml:space="preserve">
    <value>Payee</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.PayeeType" xml:space="preserve">
    <value>Payee Type</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.PersonBenefiting" xml:space="preserve">
    <value>Person Benefiting</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.AmountBenefiting" xml:space="preserve">
    <value>Amount Benefiting</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page13.CancelAuthorizerHeaderText" xml:space="preserve">
    <value>Cancel and Delete Authorizer</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page13.CancelAuthorizerText" xml:space="preserve">
    <value>Are you sure? The entry will be deleted and all data will be lost.</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page01.Title" xml:space="preserve">
    <value>About the lobbying coalition</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Transaction.LobbyingCoalitionTransaction.Page01.ContinueValidation" xml:space="preserve">
    <value>Adding lobbying coalition is required</value>
  </data>
  <data name="FilerPortal.Disclosure.InHousePayment.Description" xml:space="preserve">
    <value>“Payments” include salary, reimbursement of expenses, an advance for expenses or a salary advance or any other payments made in connection with lobbying activities. (Salary includes gross wages paid, plus any fringe benefits which are in lieu of wages such as the granting of stock options or purchase of annuities. Salary does not include routine fringe benefits such as the employer’s contributions to a health plan, retirement plan, or payroll taxes.)</value>
  </data>
  <data name="Common.ToastCancelled" xml:space="preserve">
    <value>Cancelled!</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.CampaignContributionSummary" xml:space="preserve">
    <value>Campaign contributions</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.LobbyistReportsSummary" xml:space="preserve">
    <value>Lobbyist reports</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.MadeToLobbyingFirmsSummary" xml:space="preserve">
    <value>Payments to lobbying firms</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.ActivityExpenseSummary" xml:space="preserve">
    <value>Activity Expenses</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.PucActivitySummary" xml:space="preserve">
    <value>PUC Activity</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.PaymentsToInHouseLobbyists" xml:space="preserve">
    <value>Payments to in-house lobbyists</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.OtherPaymentsToInfluenceSummary" xml:space="preserve">
    <value>Other payments to influence</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.ToLobbyingCoalitionSummary" xml:space="preserve">
    <value>Payments made to lobbying coalitions</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.AdHocFilingSummary" xml:space="preserve">
    <value>AdHocFilingSummary</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.RecieveLobbyingCoalitionSummary" xml:space="preserve">
    <value>RecieveLobbyingCoalitionSummary</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.RecieveLobbyingCoalitionSummary" xml:space="preserve">
    <value>Payments received by coalition members</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.LumpSums" xml:space="preserve">
    <value>Lump Sums</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ReviewInstructions" xml:space="preserve">
    <value>Review Instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ItemizedPayments" xml:space="preserve">
    <value>Itemized Payments</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ItemizedTotal" xml:space="preserve">
    <value>Itemized Total</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.UnitemizedTotal" xml:space="preserve">
    <value>Unitemized Total</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ItemizedDescription" xml:space="preserve">
    <value>Government agencies must itemize payments of $250 or more, and all other lobbyist employers and 
$5,000 filers must itemize payments of $2,500 or more, made during the quarter for lobbying activity.</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.OverheadExpenses" xml:space="preserve">
    <value>Total payments for overhead expenses related to lobbying activity</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.ForLobbyingActivity" xml:space="preserve">
    <value>Total payments of less than $250 or $2,500, as applicable, during the calendar quarter for lobbying activity (excluding overhead)</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.SaveAndClose" xml:space="preserve">
    <value>Save and close</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Disclosure.ItemizedPayments.EnterAmount" xml:space="preserve">
    <value>Enter Amount</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.BodyLobbyistEmployer" xml:space="preserve">
    <value>Monetary and non-monetary campaign contributions of $100 or more made to or on behalf of state candidates, elected state officers and any of their controlled committees, or committees supporting such candidates or officers must be reported</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.BodyLobbyist" xml:space="preserve">
    <value>Disclose all monetary and nonmonetary campaign contributions of $100 or more made from your personal funds during the quarter, or from a separate account under your control, to state candidates, elected state officers, their controlled committees, or committees primarily formed to support such officers or candidates, or delivered in person by you to state candidates or elected state officers.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Page07.TitleConfirmTreasurer" xml:space="preserve">
    <value>Confirm your information as treasurer</value>
  </data>
  <data name="Common.Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.AboutThePayee" xml:space="preserve">
    <value>About the payee</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.Description" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.ChooseExistingContact" xml:space="preserve">
    <value>Choose an existing contact</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.AddNewContact" xml:space="preserve">
    <value>Or add a new contact</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPaymentPayee.EnterPayee" xml:space="preserve">
    <value>Enter payee information</value>
  </data>
  <data name="CandidateIntentionStatement.CandidateAttestationRequestSent" xml:space="preserve">
    <value>Candidate Attestation Request Sent</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Title" xml:space="preserve">
    <value>Officeholder &amp; Candidate Campaign Statement</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.Term" xml:space="preserve">
    <value>I declare under penalty of perjury that to the best of my knowledge I anticipate that I will receive less than $2,000 and that I will spend less than $2,000 during the calendar year and that I have used all reasonable diligence in preparing this statement. I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Confirmation.SubmissionReceived" xml:space="preserve">
    <value>Submission received</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.Preview" xml:space="preserve">
    <value>Preview PDF</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Common.ExecutedOn" xml:space="preserve">
    <value>Executed on</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Description" xml:space="preserve">
    <value>Short form</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.SubTitle" xml:space="preserve">
    <value>Payment made to lobbying firm</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.Title" xml:space="preserve">
    <value>About the lobbying firm</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.SubTitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.SearchContact" xml:space="preserve">
    <value>Look up lobbying firm by ID# or name</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.OrAddContact" xml:space="preserve">
    <value>Or enter lobbying firm information</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.OrAddContactButton" xml:space="preserve">
    <value>Enter lobbying firm information</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.Title" xml:space="preserve">
    <value>Lobbying firm information</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.SubTitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.AddressTitle" xml:space="preserve">
    <value>Lobbying Firm Address</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.Title" xml:space="preserve">
    <value>About the payment</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.SubTitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.FeesAndRetainersAmount" xml:space="preserve">
    <value>Fees and retainers</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.ReimbursementOfExpensesAmount" xml:space="preserve">
    <value>Reimbursement of expenses</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.AdvancesOrOtherPaymentsAmount" xml:space="preserve">
    <value>Advances or other payments</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.AdvancesOrOtherpaymentsExplanation" xml:space="preserve">
    <value>Explanation for advances or other payments</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.03_EnterAmount.TotalAmount" xml:space="preserve">
    <value>Total this period</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.02_EnterContact.NameLabel" xml:space="preserve">
    <value>Name of lobbying firm</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Header" xml:space="preserve">
    <value>Other payments</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AboutThePayment" xml:space="preserve">
    <value>About the payment</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.NameOfPayee" xml:space="preserve">
    <value>Name of payee</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.PayeeType" xml:space="preserve">
    <value>Payee Type</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.PaymentCode" xml:space="preserve">
    <value>Payment Code</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AdminActions" xml:space="preserve">
    <value>Administrative actions</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Legislation" xml:space="preserve">
    <value>Legislation</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AdvertCheckboxes" xml:space="preserve">
    <value>Identify the Subject of Lobbying Advertisement</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AssemblyBills" xml:space="preserve">
    <value>Assembly Bills</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.BillNumber" xml:space="preserve">
    <value>Bill Number</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.BillTitle" xml:space="preserve">
    <value>Bill Title</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.SenateBills" xml:space="preserve">
    <value>Senate Bills</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.AgencyOffice" xml:space="preserve">
    <value>Agency/Office</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.OtherActionsLobbied" xml:space="preserve">
    <value>Other actions lobbied</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.Amount" xml:space="preserve">
    <value>Amount this quarter</value>
  </data>
  <data name="FilerPortal.Transaction.NewOtherPayment.CumulativeAmount" xml:space="preserve">
    <value>Cumulative amount paid since January 1 of the current year:</value>
  </data>
  <data name="CandidateIntentionStatement.Page09.SubHeader" xml:space="preserve">
    <value>Next steps</value>
  </data>
  <data name="Common.NextSteps" xml:space="preserve">
    <value>Next steps</value>
  </data>
  <data name="CandidateIntentionStatement.Page09.Body" xml:space="preserve">
    <value>Candidates who have spent or anticipate spending $2,000 or more and/or have received or anticipate receiving contributions totaling $2,000 or more during the calendar year must register a recipient committee.</value>
  </data>
  <data name="CandidateIntentionStatement.Page09.Label" xml:space="preserve">
    <value>Do you anticipate spending or receiving $2,000 or more during the calendar year?</value>
  </data>
  <data name="CandidateIntentionStatement.Page09.Action" xml:space="preserve">
    <value>Click Close to return to dashboard</value>
  </data>
  <data name="Common.ReturnToDashboard.Message" xml:space="preserve">
    <value>Click Close to return to dashboard</value>
  </data>
  <data name="FilerPortal.LinkCommittee.Title" xml:space="preserve">
    <value>Link to the Committee</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.SubTitle" xml:space="preserve">
    <value>Committee Registered</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.Description" xml:space="preserve">
    <value>In some cases, an officeholder or candidate is required to register a controlled committee (if they spend or raise over $2,000).</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.Label" xml:space="preserve">
    <value>Have you registered a controlled committee for this candidacy?</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.Yes" xml:space="preserve">
    <value>Yes, I have already registered a controlled committee</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.No" xml:space="preserve">
    <value>No, I have not registered a controlled committee</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.SubSectionTitle" xml:space="preserve">
    <value>Selected committee information</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.Body" xml:space="preserve">
    <value>By clicking “Continue” at the bottom of this screen, you will confirm that the committee named below is a candidate-controlled
committee controlled by the candidate named in this Candidate Intention Statement. To select a different committee, please search
again using the search box above.</value>
  </data>
  <data name="FilerPortal.LinkCommittee01.Body2" xml:space="preserve">
    <value>If you have selected the intended committee, but any contact information listed appears inaccurate, please amend the registration
for your committee to reflect up-to-date contact information.</value>
  </data>
  <data name="FilerPortal.LinkCommittee.CommitteeName" xml:space="preserve">
    <value>Committee Name</value>
  </data>
  <data name="FilerPortal.LinkCommittee.Id" xml:space="preserve">
    <value>ID#</value>
  </data>
  <data name="FilerPortal.LinkCommittee.Email" xml:space="preserve">
    <value>Email address</value>
  </data>
  <data name="FilerPortal.LinkCommittee.Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="FilerPortal.LinkCommittee.Phone" xml:space="preserve">
    <value>Telephone number</value>
  </data>
  <data name="FilerPortal.LinkCommittee.MailAddress" xml:space="preserve">
    <value>Mailing address</value>
  </data>
  <data name="FilerPortal.LinkCommittee02.SubTitle" xml:space="preserve">
    <value>Committee selection recorded</value>
  </data>
  <data name="FilerPortal.LinkCommittee02.Description" xml:space="preserve">
    <value>Your committee has been linked to your candidate registration.
You may return to the dashboard to work on additional filings,
as needed.</value>
  </data>
  <data name="Common.AmendRegistration" xml:space="preserve">
    <value>Amend Registration</value>
  </data>
  <data name="Common.Amend" xml:space="preserve">
    <value>Amend</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page05.Title" xml:space="preserve">
    <value>Treasurer Information</value>
  </data>
  <data name="FilerPortal.AmendSmoRegistration.Page05.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.SelectAgencies" xml:space="preserve">
    <value>Select Agencies</value>
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.SearchAgencies" xml:space="preserve">
    <value>Type to search agencies...</value>
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.SelectedAgencies" xml:space="preserve">
    <value>Selected Agencies</value>
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.NoAgenciesSelected" xml:space="preserve">
    <value>No agencies selected yet</value>
  </data>
  <data name="FilerPortal.Transaction.AgencyMultiSelectPopUp.AddSelected" xml:space="preserve">
    <value>Add Selected</value>
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.AddAssyBill" xml:space="preserve">
    <value>Add Assembly Bill</value>
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.SelectedAssyBills" xml:space="preserve">
    <value>Selected Assembly Bills</value>
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.NoBillsSelected" xml:space="preserve">
    <value>No bills selected yet</value>
  </data>
  <data name="FilerPortal.Transaction.LegAssyBillPopUp.AddSelected" xml:space="preserve">
    <value>Add Selected</value>
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.AddSenateBill" xml:space="preserve">
    <value>Add Senate Bill</value>
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.SelectedSenateBills" xml:space="preserve">
    <value>Selected Senate Bills</value>
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.NoBillsSelected" xml:space="preserve">
    <value>No bills selected yet</value>
  </data>
  <data name="FilerPortal.Transaction.LegSenateBillPopUp.AddSelected" xml:space="preserve">
    <value>Add Selected</value>
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.Submit" xml:space="preserve">
    <value>Send for Attestation</value>
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.ResponsibleOfficer" xml:space="preserve">
    <value>Name and title of responsible officer</value>
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.Subtitle" xml:space="preserve">
    <value>UI text explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SendForAttest.OfficerMustBeSelected" xml:space="preserve">
    <value>At least one officer must be selected.</value>
  </data>
  <data name="FilerPortal.Form470.Title" xml:space="preserve">
    <value>Officeholder &amp; Candidate Campaign Statement</value>
  </data>
  <data name="FilerPortal.Form470.Subtitle" xml:space="preserve">
    <value>Short Form</value>
  </data>
  <data name="FilerPortal.Form470.Page01.Title" xml:space="preserve">
    <value>General information</value>
  </data>
  <data name="FilerPortal.Form470S.Page01.Title" xml:space="preserve">
    <value>Form Supplement</value>
  </data>
  <data name="FilerPortal.Form470S.Page01.DateContributions.Title" xml:space="preserve">
    <value>Date contributions or expenditures reached $2,000 or more</value>
  </data>
  <data name="FilerPortal.Form470.Page01.Body" xml:space="preserve">
    <value>Please select the calendar year for which you are submitting the campaign statement, and then review the information associated with your officeholder/candidate record. If any listed information is inaccurate, you may need to submit an amendment to your Candidate Intention Statement to ensure all contact and candidacy information is current.</value>
  </data>
  <data name="FilerPortal.Form470S.Page01.Body" xml:space="preserve">
    <value>By filing this statement, you are providing written notification that the officeholder or candidate listed below has received contributions totaling $2,000 or more or has made expenditures of $2,000 or more during the indicated calendar year. This statement supersedes the Officeholder &amp; Candidate Campaign Statement you previously submitted stating that you did not anticipate spending or receiving $2,000 or more.</value>
  </data>
  <data name="FilerPortal.Form470S.Page01.DateContributions.Body" xml:space="preserve">
    <value>Date contribution amount exceeded</value>
  </data>
  <data name="FilerPortal.Form470.Page01.CalendarYearCovered" xml:space="preserve">
    <value>Calendar Year Covered</value>
  </data>
  <data name="FilerPortal.Form470.Page01.CandidateHeader" xml:space="preserve">
    <value>Officeholder/Candidate Information</value>
  </data>
  <data name="FilerPortal.Form470.Page01.CandidateName" xml:space="preserve">
    <value>Name of Officeholder or Candidate</value>
  </data>
  <data name="FilerPortal.Form470.Page01.OfficeSoughtHeader" xml:space="preserve">
    <value>Office Sought or Held</value>
  </data>
  <data name="FilerPortal.Form470.Page01.OfficeSoughtName" xml:space="preserve">
    <value>Office Sought or Held</value>
  </data>
  <data name="Common.PhoneNumber" xml:space="preserve">
    <value>Phone number</value>
  </data>
  <data name="Common.AddressNotFound" xml:space="preserve">
    <value>Address was not recognized</value>
  </data>
  <data name="Common.DistrictNumber" xml:space="preserve">
    <value>District Number</value>
  </data>
  <data name="FilerPortal.Form470.Page01.DateOfElection" xml:space="preserve">
    <value>Date of Election</value>
  </data>
  <data name="PRDPortal.Banners.List.Title" xml:space="preserve">
    <value>System Banners</value>
  </data>
  <data name="PRDPortal.Banners.List.BannerMessage" xml:space="preserve">
    <value>Banner Message</value>
  </data>
  <data name="PRDPortal.Banners.List.DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this banner? It will be permanently removed.</value>
  </data>
  <data name="PRDPortal.Banners.List.FilerType" xml:space="preserve">
    <value>Filer Type</value>
  </data>
  <data name="PRDPortal.Banners.List.EventDate" xml:space="preserve">
    <value>Event Date</value>
  </data>
  <data name="PRDPortal.Banners.List.EndDate" xml:space="preserve">
    <value>End At</value>
  </data>
  <data name="PRDPortal.Banners.Edit.AddNewBannerTitle" xml:space="preserve">
    <value>Add New Banner</value>
  </data>
  <data name="PRDPortal.Banners.Edit.EditBannerTitle" xml:space="preserve">
    <value>Edit Banner</value>
  </data>
  <data name="PRDPortal.Banners.Edit.EventDate" xml:space="preserve">
    <value>Event Date</value>
  </data>
  <data name="PRDPortal.Banners.Edit.EventDatePlaceHolder" xml:space="preserve">
    <value>Select a date and time"</value>
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayBefore" xml:space="preserve">
    <value>Before</value>
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayAfter" xml:space="preserve">
    <value>After</value>
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayUntil" xml:space="preserve">
    <value>Display Until Removed</value>
  </data>
  <data name="PRDPortal.Banners.Edit.DisplayDuration" xml:space="preserve">
    <value>Banner Display Duration</value>
  </data>
  <data name="PRDPortal.Banners.Edit.DurationDays" xml:space="preserve">
    <value>Duration (Days)</value>
  </data>
  <data name="PRDPortal.Banners.Edit.DurationDaysPlaceholder" xml:space="preserve">
    <value># of days</value>
  </data>
  <data name="PRDPortal.Banners.Edit.FilerTypes" xml:space="preserve">
    <value>Filer Types</value>
  </data>
  <data name="PRDPortal.Banners.Edit.Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="PRDPortal.Banners.Edit.SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="PRDPortal.Banners.Edit.CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.ActionsLobbiedSummary" xml:space="preserve">
    <value>Actions Lobbied</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.LobbyingAdvertisementSummary" xml:space="preserve">
    <value>Lobbying Advertisement</value>
  </data>
  <data name="FilerPortal.Filing.Report72H.Title" xml:space="preserve">
    <value>72 Hour Issue Lobbying Advertisement Report</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.ExecutedOn" xml:space="preserve">
    <value>Executed on</value>
  </data>
  <data name="FilerPortal.Form470.Page02.Title" xml:space="preserve">
    <value>Committees Primarily Formed to Support</value>
  </data>
  <data name="FilerPortal.Form470.Page02.Description" xml:space="preserve">
    <value>List all committees of which you have knowledge that are primarily formed to receive contributions or to make expenditures on behalf of your candidacy.</value>
  </data>
  <data name="FilerPortal.Form470.Page02.TableCommitteeName" xml:space="preserve">
    <value>Committee Name</value>
  </data>
  <data name="FilerPortal.Form470.Page02.TableId" xml:space="preserve">
    <value>ID#</value>
  </data>
  <data name="FilerPortal.Form470.Page02.TableAddress" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="FilerPortal.Form470.Page02.TableTreasurerName" xml:space="preserve">
    <value>Treasurer Name</value>
  </data>
  <data name="FilerPortal.Form470.Page02.AddButton" xml:space="preserve">
    <value>Add committee</value>
  </data>
  <data name="FilerPortal.Form470.Page03.Title" xml:space="preserve">
    <value>Committees Primarily Formed to Support</value>
  </data>
  <data name="FilerPortal.Form470.Page03.Description1" xml:space="preserve">
    <value>UI Text Explanation under development and review (Explanatory text if needed)</value>
  </data>
  <data name="FilerPortal.Form470.Page03.Description2" xml:space="preserve">
    <value>Add a committee via search below. You’ll be able to identify additional committees on the next screen.</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.NonCandidate.Header" xml:space="preserve">
    <value>The officeholder &amp; candidate campaign statement must be verified and attested to by the candidate.</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.NonCandidate.Body2" xml:space="preserve">
    <value>The officeholder &amp; candidate campaign statement will not be submitted until after the verification is complete.</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Verification.NonCandidate.Body1" xml:space="preserve">
    <value>When the Statement is ready to submit, click Send for Candidate Attestation and a notification will be sent to the Candidate to allow them to verify and submit.</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Confirmation.AttestationRequestSent" xml:space="preserve">
    <value>Candidate Attestation Request Sent</value>
  </data>
  <data name="FilerPortal.Form470Attestation.Confirmation.AttestationRequestSent.Message" xml:space="preserve">
    <value>This officeholder &amp; candidate campaign statement is pending until the candidate completes the verification.</value>
  </data>
  <data name="Common.PendingItems" xml:space="preserve">
    <value>Pending Items</value>
  </data>
  <data name="FilerPortal.Transaction.NewPayee.OtherPayments" xml:space="preserve">
    <value>Other Payments</value>
  </data>
  <data name="FilerPortal.Transaction.EditActivityExpense.EditErrorMessage" xml:space="preserve">
    <value>There was an error updating the new Activity Expense. Please try again.</value>
  </data>
  <data name="Common.ToastSaved" xml:space="preserve">
    <value>Saved</value>
  </data>
  <data name="FilerPortal.Transaction.NewActivityExpense.CreateSuccessMessage" xml:space="preserve">
    <value>Activity Expense created successfully!</value>
  </data>
  <data name="Common.NotPubliclyAvailable" xml:space="preserve">
    <value>Not publicly available</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.AmendmentExplanation" xml:space="preserve">
    <value>Amendment Explanation</value>
  </data>
  <data name="FilerPortal.Form470.Page02.DeleteMessage" xml:space="preserve">
    <value>Are you sure you want to delete this? Information regarding this committee will be permanently removed.</value>
  </data>
  <data name="FilerPortal.Form470S.Page02.NextSteps.Body" xml:space="preserve">
    <value>You are required to register a controlled committee within 10 days, or in some cases within 24 hours.</value>
  </data>
  <data name="FilerPortal.Transaction.EditActivityExpense.UpdateSuccessMessage" xml:space="preserve">
    <value>Activity Expense updated successfully!</value>
  </data>
  <data name="FilerPortal.FirmPaymentTransaction.01_SelectContact.ContinueValidation" xml:space="preserve">
    <value>Adding lobbying firm is required</value>
  </data>
  <data name="FilerPortal.CoalitionReceivedTransaction.01_SelectContact.ContinueValidation" xml:space="preserve">
    <value>Adding lobbying coalition is required</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SaveSuccessMessage" xml:space="preserve">
    <value>Report saved successfully.</value>
  </data>
  <data name="FilerPortal.Filing.SendForAttestationLobbyistReport.SendSuccess" xml:space="preserve">
    <value>Lobbyist report was successfully sent for attestation!</value>
  </data>
  <data name="FilerPortal.Filing.SendForAttestationLobbyistReport.SendError" xml:space="preserve">
    <value>There was an issue with the report. Please doublecheck and try again.</value>
  </data>
  <data name="FilerPortal.Filing.SendForAttestation.Exception" xml:space="preserve">
    <value>Failed to send this report for attestation.</value>
  </data>
  <data name="FilerPortal.Disclosure.AmendmentExplanation.Body" xml:space="preserve">
    <value>Please provide any additional description or explanation of the changes you’ve made in this amendment</value>
  </data>
  <data name="FilerPortal.Disclosure.AmendmentExplanation.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.AmendmentExplanation.Title" xml:space="preserve">
    <value>Amendment explanation</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterTransaction" xml:space="preserve">
    <value>Enter A Transaction</value>
  </data>
  <data name="FilerPortal.Filing.Report48H.Title" xml:space="preserve">
    <value>48 Hour End-of-Session Lobbying Report</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.Summary.EndOfSessionLobbyingSummary" xml:space="preserve">
    <value>End-of-Session Lobbying</value>
  </data>
  <data name="FilerPortal.Disclosure.TemporaryDashboard.DocumentId" xml:space="preserve">
    <value>Document ID</value>
  </data>
  <data name="FilerPortal.Disclosure.PUCActivity.TotalPaymentsLabel" xml:space="preserve">
    <value>Total payments in connection with PUC administrative testimony</value>
  </data>
  <data name="FilerPortal.Disclosure.ActivityExpenses.LobbyistEmployer.Body03" xml:space="preserve">
    <value>Activity expenses arranged, incurred, or paid by the lobbying firm, not including those paid or incurred by a lobbyist. Lobbyist expenses will be reported on a later screen.</value>
  </data>
  <data name="Common.CancelConfirmationTitle" xml:space="preserve">
    <value>Confirm Cancellation</value>
  </data>
  <data name="Common.CancelConfirmationBody" xml:space="preserve">
    <value>Are you sure you want to cancel? Any unsaved changes will be lost.</value>
  </data>
  <data name="Common.CancelConfirmationClose" xml:space="preserve">
    <value>No, Stay Here</value>
  </data>
  <data name="Common.CancelConfirmationSubmit" xml:space="preserve">
    <value>Yes, Cancel</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.CandidateLastName" xml:space="preserve">
    <value>Candidate last name</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Support" xml:space="preserve">
    <value>Support</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.Oppose" xml:space="preserve">
    <value>Oppose</value>
  </data>
  <data name="FilerPortal.CandidateBallotMeasure.CandidateFirstName" xml:space="preserve">
    <value>Candidate first name</value>
  </data>
  <data name="Common.Accept" xml:space="preserve">
    <value>Accept</value>
  </data>
  <data name="Common.Reject" xml:space="preserve">
    <value>Reject</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Header" xml:space="preserve">
    <value>Review Linkage Request</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Description" xml:space="preserve">
    <value>UI Text Explaination under development and review</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.InputLabel" xml:space="preserve">
    <value>Invitation code</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Btn" xml:space="preserve">
    <value>Retrieve Request</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.Title" xml:space="preserve">
    <value>Review Linkage Request</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.RequestedBy" xml:space="preserve">
    <value>Requested by</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.AssignedRole" xml:space="preserve">
    <value>Assigned Role</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.DateOfRequest" xml:space="preserve">
    <value>Date of Request</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.Filer" xml:space="preserve">
    <value>Filer</value>
  </data>
  <data name="FilerPortal.Registration.TerminateRegistration" xml:space="preserve">
    <value>Do you want to terminate this registration?</value>
  </data>
  <data name="FilerPortal.Registration.YesTerminate" xml:space="preserve">
    <value>Yes, terminate.</value>
  </data>
  <data name="FilerPortal.Registration.NoContinueAmendment" xml:space="preserve">
    <value>No, continue with amendment.</value>
  </data>
  <data name="FilerPortal.Registration.NoticeOfTermination.Title" xml:space="preserve">
    <value>Notice of termination</value>
  </data>
  <data name="FilerPortal.Registration.NoticeOfTermination.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Registration.NoticeOfTermination.EffectiveDate" xml:space="preserve">
    <value>Effective date of termination</value>
  </data>
  <data name="FilerPortal.Registration.AmendmentVerification.Title" xml:space="preserve">
    <value>Amendment Verification</value>
  </data>
  <data name="FilerPortal.Registration.TerminationVerification.Title" xml:space="preserve">
    <value>Termination Verification</value>
  </data>
  <data name="FilerPortal.Registration.TerminationVerification.Certification" xml:space="preserve">
    <value>I have used all reasonable diligence in preparing this Statement. This Slate Mailer Organization has ceased to receive payments and make disbursements in connection with
    producing "slate mailers," does not anticipate receiving payments or making disbursements in the future, and has filed all campaign statements required by the Political Reform Act
    disclosing all reportable transactions. I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.
    </value>
  </data>
  <data name="Common.SelectOption" xml:space="preserve">
    <value>Please select an option</value>
  </data>
  <data name="FilerPortal.AccountManagement.Title" xml:space="preserve">
    <value>Account management</value>
  </data>
  <data name="FilerPortal.Linkages.Title" xml:space="preserve">
    <value>Linkages</value>
  </data>
  <data name="FilerPortal.Linkages.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.MyLinkages.MyLinkagesTable.Title" xml:space="preserve">
    <value>My Linkages</value>
  </data>
  <data name="FilerPortal.MyLinkages.PendingLinkageRequests.Title" xml:space="preserve">
    <value>Pending Linkage Requests</value>
  </data>
  <data name="FilerPortal.MyLinkages.RequestNewLinkage.Title" xml:space="preserve">
    <value>Request New Linkage</value>
  </data>
  <data name="Common.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Common.Current" xml:space="preserve">
    <value>Current</value>
  </data>
  <data name="Common.Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="FilerPortal.MyLinkages.RequestNewLinkage.Description" xml:space="preserve">
    <value>UI Text Explaination under development and review</value>
  </data>
  <data name="FilerPortal.MyLinkages.RequestNewLinkage.RequestLinkageBtn" xml:space="preserve">
    <value>Request Linkage</value>
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Title" xml:space="preserve">
    <value>Actions Lobbied</value>
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.SubDescription" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.OtherSubDescription" xml:space="preserve">
    <value>Other not listed above</value>
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Description" xml:space="preserve">
    <value>You must report the legislative bills and state agency administrative actions which you “actively” influenced or attempted to influence. “Actively” lobbied means that you or your agent have engaged in direct communication with a qualifying official for the purpose of influencing legislative or administrative action during the reporting period. (See the “Lobbying Disclosure Manual” for the definition of “direct communication.”) Do not list bills or administrative actions which have died prior to the reporting period, or those which are only being watched or monitored, or those which you have not attempted to influence during the reporting period.</value>
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Bill.DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete the bill? ({0})</value>
  </data>
  <data name="FilerPortal.Filing.LobbyistEmployer.ActionsLobbied.Agency.DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete the agency? ({0})</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Lobbyist.Subtitle" xml:space="preserve">
    <value>Any modifications or changes to this information requires an amendment to your registration report.</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer.Subtitle" xml:space="preserve">
    <value>Review for any modifications or changes to this information.</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report72h.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer.Name" xml:space="preserve">
    <value>Employer name</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Lobbyist.Name" xml:space="preserve">
    <value>Name of lobbyist</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report72h.Name" xml:space="preserve">
    <value>Name of Filer</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.LobbyistEmployer.FilerDetails" xml:space="preserve">
    <value>Lobbyist employer details</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Lobbyist.FilerDetails" xml:space="preserve">
    <value>Lobbyist details</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report72h.FilerDetails" xml:space="preserve">
    <value>Lobbying filer details</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report48h.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report48h.FilerDetails" xml:space="preserve">
    <value>Lobbying filer details</value>
  </data>
  <data name="FilerPortal.Disclosure.GeneralInfo.Report48h.Name" xml:space="preserve">
    <value>Name of filer</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.Title" xml:space="preserve">
    <value>End-of-Session Lobbying</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review.</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.ReviewInstructions" xml:space="preserve">
    <value>Review Instructions</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbyingSummary.Subtitle" xml:space="preserve">
    <value>Payments to lobbying firms</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.Id" xml:space="preserve">
    <value>Lobbying firm ID#</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.LobbyingFirmName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.LobbyingFirmHiringDate" xml:space="preserve">
    <value>Date Lobbying Firm Hired</value>
  </data>
  <data name="FilerPortal.Disclosure.EndOfSessionLobbying.AmountIncurred" xml:space="preserve">
    <value>Incurred Amount</value>
  </data>
  <data name="FilerPortal.MyLinkages.MyLinkagesTable.TerminateLinkageMessage" xml:space="preserve">
    <value>Are you sure you want to terminate this linkage?</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Subtitle" xml:space="preserve">
    <value>Candidate or Ballot Measure Information</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.CandidatesMeasuresNotListed.Description" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.SelectAnOption" xml:space="preserve">
    <value>Please select an option:</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.Title" xml:space="preserve">
    <value>About The Payee</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade01.EnterPayeeInfo" xml:space="preserve">
    <value>Enter payee information</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade02.Title" xml:space="preserve">
    <value>Payee Information</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade02.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.EnterPayorInfo" xml:space="preserve">
    <value>Enter payor information</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived02.Title" xml:space="preserve">
    <value>Payor Information</value>
  </data>
  <data name="Common.ViewReport" xml:space="preserve">
    <value>View Report</value>
  </data>
  <data name="Common.IdNumberHeader" xml:space="preserve">
    <value>ID#</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.LobbyistName" xml:space="preserve">
    <value>Lobbyist name</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this? This lobbyist will be removed from the current report.</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived02.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Contact.ContactForm.PayorType" xml:space="preserve">
    <value>Payor type</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.Title" xml:space="preserve">
    <value>About The Payor</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentReceived01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="Common.Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.Title" xml:space="preserve">
    <value>Send Linkage Request</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.LinkageRequest" xml:space="preserve">
    <value>Linkage Request</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerType" xml:space="preserve">
    <value>Filer Type</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerLabel" xml:space="preserve">
    <value>Search filer by name or ID number</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerPlaceholder" xml:space="preserve">
    <value>Enter filer name or ID number</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.LobbyistEmployerFirmLabel" xml:space="preserve">
    <value>Lobbyist Employer or Firm</value>
  </data>
  <data name="Common.SelectOfficer" xml:space="preserve">
    <value>Select officer</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SendForAttestationFailMessage" xml:space="preserve">
    <value>Failed to send the report to Attestation. Please try again.</value>
  </data>
  <data name="FilerPortal.Disclosure.Dashboard.SendForAttestationSuccessMessage" xml:space="preserve">
    <value>The report was successfully sent to Attestation.</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.DatePaid" xml:space="preserve">
    <value>Date paid</value>
  </data>
  <data name="Common.Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="Common.PaymentCode" xml:space="preserve">
    <value>Payment code</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade03.Checkbox" xml:space="preserve">
    <value>Payment made by agent or independent contractor</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.PaymentMade03.TextboxNameOf" xml:space="preserve">
    <value>Name of agent or independent contractor</value>
  </data>
  <data name="Common.SelectCode" xml:space="preserve">
    <value>Select code</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Title" xml:space="preserve">
    <value>Enter a transaction</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Header" xml:space="preserve">
    <value>48 Hour End-of-Session Lobbying Report</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.AboutThePayment" xml:space="preserve">
    <value>About the payment</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Subtitle" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.LobbyingFirm" xml:space="preserve">
    <value>Lobbying Firm</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.FirmAddress" xml:space="preserve">
    <value>Firm Address</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.DateHired" xml:space="preserve">
    <value>Date Lobbying Firm Hired</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.Amount" xml:space="preserve">
    <value>Amount Paid/Incurred to Lobbying Firm</value>
  </data>
  <data name="FilerPortal.Transaction.Report48hEosLobbyingTransaction.AssemblyBills" xml:space="preserve">
    <value>Assembly Bills</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page02.RegisteringQuestion" xml:space="preserve">
    <value>Are you registering for yourself or for the lobbyist?</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Title" xml:space="preserve">
    <value>Lobbyist Information</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Zip" xml:space="preserve">
    <value>ZIP code</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.UploadRules" xml:space="preserve">
    <value>The photograph must be recent and must show the person from the shoulders up.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Upload" xml:space="preserve">
    <value>Upload a photograph of the lobbyist</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.WillYouLobby" xml:space="preserve">
    <value>Will you lobby the state legislature?</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.FullList" xml:space="preserve">
    <value>Show full list of agencies</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied1" xml:space="preserve">
    <value>I will lobby the agencies identified on the Lobbyist Employer or Lobbying Firm Registration Statement and subsequent amendments.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied2" xml:space="preserve">
    <value>I will only lobby the agencies identified below</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.StateAgenciesLobbied" xml:space="preserve">
    <value>State agencies lobbied</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.ChooseOption" xml:space="preserve">
    <value>Please choose one option below</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.AgenciesLobbied" xml:space="preserve">
    <value>Agencies Lobbied</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsNew" xml:space="preserve">
    <value>New Certification – Within the next 12 months</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsNotTaken" xml:space="preserve">
    <value>I have not taken the course within the previous 12 months. I will attend the course:</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsRenewal" xml:space="preserve">
    <value>Renewal – By June 30 of the next calendar year</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsCompleted" xml:space="preserve">
    <value>I have completed the course on</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsCourse" xml:space="preserve">
    <value>Ethics Orientation Course</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.PlacementAgent" xml:space="preserve">
    <value>Is the lobbyist a placement agent?</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.QualificationDate" xml:space="preserve">
    <value>Date of qualification</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.LegislativeSession" xml:space="preserve">
    <value>Legislative Session</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Street" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Street2" xml:space="preserve">
    <value>Apartment, unit, floor, etc. (If applicable)</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.PhoneNumber" xml:space="preserve">
    <value>Telephone number</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.FaxNumber" xml:space="preserve">
    <value>Fax number</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.BusinessAddress" xml:space="preserve">
    <value>Business Address</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.MailingAddress" xml:space="preserve">
    <value>Mailing Address</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.SameAs" xml:space="preserve">
    <value>Same as Business Address</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Apartment" xml:space="preserve">
    <value>Apartment, unit, floor, etc.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EthicsRenewal1" xml:space="preserve">
    <value>I completed the course on</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.Title" xml:space="preserve">
    <value>You need to pay a lobby registration fee.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page05.Title" xml:space="preserve">
    <value>Lobbyist registration fee</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page05.Body" xml:space="preserve">
    <value>This is a placeholder to indicate where a lobbyist will be assed their registration fee.</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PreviewPDF" xml:space="preserve">
    <value>Preview PDF</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Overall" xml:space="preserve">
    <value>Overall</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.ThisReportingPeriod" xml:space="preserve">
    <value>This reporting period</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.CumulativeToDate" xml:space="preserve">
    <value>Cumulative to date</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.SummaryGrandTotal" xml:space="preserve">
    <value>Summary Grand Total</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsPUCActivities" xml:space="preserve">
    <value>Payments in Connection with PUC Activities</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.Totals" xml:space="preserve">
    <value>Totals</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsToLobbyingFirms" xml:space="preserve">
    <value>Payments made to lobbying firms</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.TotalsAllFirms" xml:space="preserve">
    <value>Totals – All firms</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsToLobbyingCoalitions" xml:space="preserve">
    <value>Payments made to lobbying coalitions</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.TotalsAllLobbyingCoalitions" xml:space="preserve">
    <value>Totals – All lobbying coalitions</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.PaymentsReceivedByCoalitionMember" xml:space="preserve">
    <value>Payments received, by coalition member</value>
  </data>
  <data name="FilerPortal.Disclosure.FilingSummary.TotalsAllCoalitionMembers" xml:space="preserve">
    <value>Totals – All coalition members</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.Title" xml:space="preserve">
    <value>Manage Notifications</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.SystemNotificationHeading" xml:space="preserve">
    <value>System Notifications</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.FilingNotificationHeading" xml:space="preserve">
    <value>Filing Notifications</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.AllFilersHeading" xml:space="preserve">
    <value>All Filers</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.SystemNotifications" xml:space="preserve">
    <value>System notifications such as outages</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.ReminderNotifications" xml:space="preserve">
    <value>Reminder notifications</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.PriorityNotifications" xml:space="preserve">
    <value>Priority notifications (filing deadlines and official correspondence)</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.OtherNotifications" xml:space="preserve">
    <value>Other notifications</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.CancelDialog" xml:space="preserve">
    <value>Changes will not be saved. Are you sure you want to cancel?</value>
  </data>
  <data name="FilerPortal.NotificationPrefs.Edit.SaveSuccess" xml:space="preserve">
    <value>Preferences were saved.</value>
  </data>
  <data name="Common.UploadFile.Title" xml:space="preserve">
    <value>Attach file(s)</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.Street1" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="Common.CountryCode" xml:space="preserve">
    <value>Country Code</value>
  </data>
  <data name="Common.Extension" xml:space="preserve">
    <value>Extension</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.SectionTitle" xml:space="preserve">
    <value>Filer Dashboard</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.SectionBody" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentOfficers.Title" xml:space="preserve">
    <value>Current Officers</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.Title" xml:space="preserve">
    <value>Current Authorized Users</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateTitle" xml:space="preserve">
    <value>Terminate Linkage</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateBody" xml:space="preserve">
    <value>Are you sure you want to terminate this linkage?</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateSuccessToast" xml:space="preserve">
    <value>Linkage terminated successfully</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.TerminateFailureToast" xml:space="preserve">
    <value>Failed to terminate linkage</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.Title" xml:space="preserve">
    <value>Pending User Linkages</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RequestType" xml:space="preserve">
    <value>Request Type</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.AcceptTitle" xml:space="preserve">
    <value>Accept Linkage</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.AcceptBody" xml:space="preserve">
    <value>Are you sure you want to accept this linkage?</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.AcceptSuccessToast" xml:space="preserve">
    <value>Linkage added successfully</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.AcceptFailureToast" xml:space="preserve">
    <value>Failed to add linkage</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RejectTitle" xml:space="preserve">
    <value>Reject Linkage</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.PendingUserLinkages.RejectBody" xml:space="preserve">
    <value>Are you sure you want to reject this linkage?</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.RejectSuccessToast" xml:space="preserve">
    <value>Linkage rejected</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.CurrentAuthorizedUsers.RejectFailureToast" xml:space="preserve">
    <value>Failed to reject linkage</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.Title" xml:space="preserve">
    <value>Request New Linkage</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.RequestLinkage" xml:space="preserve">
    <value>Request Linkage</value>
  </data>
  <data name="Common.CurrencyInputPlaceHolder" xml:space="preserve">
    <value>Enter the amount</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.EnterNameOrId" xml:space="preserve">
    <value>Enter name or ID#</value>
  </data>
  <data name="Common.ToastCanceled" xml:space="preserve">
    <value>Canceled!</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page06.Title" xml:space="preserve">
    <value>You are ready to verify and submit.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page06.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Title" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SendSuccessToast" xml:space="preserve">
    <value>Linkage request sent successfully</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SendFailureToast" xml:space="preserve">
    <value>Linkage request failed to send</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerRoleRequiredError" xml:space="preserve">
    <value>Filer Role is required</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerRole" xml:space="preserve">
    <value>Filer Role</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.SearchFilerRequiredError" xml:space="preserve">
    <value>Filer is required</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerTypeRequiredError" xml:space="preserve">
    <value>Filer Type is required</value>
  </data>
  <data name="Common.FilerName" xml:space="preserve">
    <value>Filer Name</value>
  </data>
  <data name="Common.Officer" xml:space="preserve">
    <value>Officer</value>
  </data>
  <data name="Common.MailingAddress" xml:space="preserve">
    <value>Mailing Address</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.InvitationCodeRequiredError" xml:space="preserve">
    <value>Invitation Code is required</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.NotFound" xml:space="preserve">
    <value>Invalid code, please double check the code and try again</value>
  </data>
  <data name="FilerPortal.MyLinkages.ReviewRequest.Modal.LookupError" xml:space="preserve">
    <value>Error encountered during lookup. Please try again.</value>
  </data>
  <data name="Common.EmailAddressRequired" xml:space="preserve">
    <value>Email Address is required</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.FilerRoleRequiredError" xml:space="preserve">
    <value>Role is required</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.SendSuccessToast" xml:space="preserve">
    <value>Linkage request sent successfully</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.SendFailureToast" xml:space="preserve">
    <value>Linkage request failed to send</value>
  </data>
  <data name="Common.EmailAddressInvalidError" xml:space="preserve">
    <value>Enter a valid e-mail address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Header" xml:space="preserve">
    <value>Registration Details</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.FilingHeader" xml:space="preserve">
    <value>Filing Information</value>
  </data>
  <data name="Common.FilerId" xml:space="preserve">
    <value>Filer ID</value>
  </data>
  <data name="Common.DocumentId" xml:space="preserve">
    <value>Document ID</value>
  </data>
  <data name="Common.Form" xml:space="preserve">
    <value>Form</value>
  </data>
  <data name="Common.Submitted" xml:space="preserve">
    <value>Submitted</value>
  </data>
  <data name="Common.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.Header" xml:space="preserve">
    <value>Organization Name and Contact Information</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.Body" xml:space="preserve">
    <value>A slate mailer organization filer must include the legal name of the individual or business in the name of the slate mailer organization, except a slate mailer organization that is not an individual or business.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.SmoName" xml:space="preserve">
    <value>Full name of slate mailer organization</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderOrganizationAddress" xml:space="preserve">
    <value>Organization Address</value>
  </data>
  <data name="Common.TypeOfAddress" xml:space="preserve">
    <value>Type of address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderMailingAddress" xml:space="preserve">
    <value>Mailing Address</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.HeaderOrganizationDetails" xml:space="preserve">
    <value>Organization Details</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.LevelOfActivity" xml:space="preserve">
    <value>Organization's level of activity</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.IsQualified" xml:space="preserve">
    <value>Is the organization qualified?</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.DateQualified" xml:space="preserve">
    <value>Date qualified as slate mailer organization</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.IsCampaignCommittee" xml:space="preserve">
    <value>Is the organization a campaign committee?</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.RegistrationInfo.CommitteeIdentifier" xml:space="preserve">
    <value>Provide the committee ID# OR name of the recipient committee</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Officers.Header" xml:space="preserve">
    <value>Officers</value>
  </data>
  <data name="Common.Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Common.StartDate" xml:space="preserve">
    <value>Start date</value>
  </data>
  <data name="Common.EndDate" xml:space="preserve">
    <value>End date</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.IndividualAuthorizers.Header" xml:space="preserve">
    <value>Individual Authorizers</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.Header" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.PendingItems" xml:space="preserve">
    <value>Pending Items</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.TreasurerAcknowledgement" xml:space="preserve">
    <value>Treasurer Acknowledgment</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.TreasurerAcknowledgementCheckbox" xml:space="preserve">
    <value>By checking the box next to my signature, I acknowledge under penalty of perjury that I must comply with all applicable duties stated in the Political Reform Act and the regulations of the Commission and that a violation of these duties could result in criminal, civil, or administrative penalties.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.Verify" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.VerifyCheckbox" xml:space="preserve">
    <value>I have used all reasonable diligence in preparing this statement. I have reviewed this statement and, to the best of my knowledge, the information contained in it is true and complete. I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.Verificaiton.HelperText" xml:space="preserve">
    <value>Click Continue to go to proceed to the next acknowledgment and attestation screen.</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.EditModel.Title" xml:space="preserve">
    <value>Edit Registration</value>
  </data>
  <data name="FilerPortal.SmoRegistration.Summary.EditModel.Body" xml:space="preserve">
    <value>Editing this [registration/amendment] will cancel the previous attestation. Please confirm if you want to proceed.</value>
  </data>
  <data name="Common.NoCancel" xml:space="preserve">
    <value>No, cancel</value>
  </data>
  <data name="FilerPortal.AccountManagement.UserDetails" xml:space="preserve">
    <value>Manage user details</value>
  </data>
  <data name="FilerPortal.AccountManagement.UserName" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="FilerPortal.AccountManagement.EmailAddress" xml:space="preserve">
    <value>Email address</value>
  </data>
  <data name="FilerPortal.AccountManagement.FullName" xml:space="preserve">
    <value>Full name</value>
  </data>
  <data name="FilerPortal.AccountManagement.AddressType" xml:space="preserve">
    <value>Address type</value>
  </data>
  <data name="FilerPortal.AccountManagement.Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="FilerPortal.AccountManagement.StreetAddress" xml:space="preserve">
    <value>Street address</value>
  </data>
  <data name="FilerPortal.AccountManagement.StreetAddress2" xml:space="preserve">
    <value>Apartment, unit, floor, etc. (If applicable)</value>
  </data>
  <data name="FilerPortal.AccountManagement.City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="FilerPortal.AccountManagement.ZipCode" xml:space="preserve">
    <value>ZIP code</value>
  </data>
  <data name="FilerPortal.AccountManagement.State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="FilerPortal.AccountManagement.TypeOfPhone" xml:space="preserve">
    <value>Type of phone number</value>
  </data>
  <data name="FilerPortal.AccountManagement.TelephoneNumber" xml:space="preserve">
    <value>Telephone number</value>
  </data>
  <data name="FilerPortal.AccountManagement.Residential" xml:space="preserve">
    <value>Residential</value>
  </data>
  <data name="FilerPortal.AccountManagement.Business" xml:space="preserve">
    <value>Business</value>
  </data>
  <data name="FilerPortal.AccountManagement.SelectCountry" xml:space="preserve">
    <value>Select Country</value>
  </data>
  <data name="FilerPortal.AccountManagement.SelectState" xml:space="preserve">
    <value>Select a state</value>
  </data>
  <data name="FilerPortal.AccountManagement.Cell" xml:space="preserve">
    <value>Cell</value>
  </data>
  <data name="FilerPortal.AccountManagement.Office" xml:space="preserve">
    <value>Office</value>
  </data>
  <data name="FilerPortal.AccountManagement.Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="FilerPortal.AccountManagement.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.AccountManagement.Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="FilerPortal.AccountManagement.MailingAddress" xml:space="preserve">
    <value>Mailing address</value>
  </data>
  <data name="FilerPortal.AccountManagement.PhoneNumber" xml:space="preserve">
    <value>Phone number(s)</value>
  </data>
  <data name="FilerPortal.AccountManagement.SameAsAbove" xml:space="preserve">
    <value>Same as above</value>
  </data>
  <data name="FilerPortal.AccountManagement.SetAsPrimaryPhoneNumber" xml:space="preserve">
    <value>Set as primary phone number</value>
  </data>
  <data name="FilerPortal.Disclosure.LobbyistReports.Instruction" xml:space="preserve">
    <value>Review instructions</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Subtitle1" xml:space="preserve">
    <value>By signing the verification below, I certify that I have read and understand that I am subject to the prohibitions contained in Government Code Sections 86203 and 86205.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Verification" xml:space="preserve">
    <value>I have used all reasonable diligence in preparing this Statement. I have reviewed this Statement and to the best of my knowledge the information contained herein is true and complete. I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page07.ExecutedOn" xml:space="preserve">
    <value>Executed on</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page08.SubmissionReceived" xml:space="preserve">
    <value>Submission received.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page08.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page08.PendingItems" xml:space="preserve">
    <value>Pending items</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.AddMajorDonor" xml:space="preserve">
    <value>Add Major Donor</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.AddRecipientCommittee" xml:space="preserve">
    <value>Add Recipient Committee</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.FilerId" xml:space="preserve">
    <value>Filer ID</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.NoRecordAddedYet" xml:space="preserve">
    <value>No record added yet</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchMajorDonorLabel" xml:space="preserve">
    <value>Search for the major donor which has filed the campaign disclosure statement</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchMajorDonorPlaceholder" xml:space="preserve">
    <value>Enter ID# or Major Donor Name</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchRecipientCommitteeLabel" xml:space="preserve">
    <value>Search for the recipient committee which has filed the campaign disclosure statement</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.SearchRecipientCommitteePlaceholder" xml:space="preserve">
    <value>Enter ID# or Recipient Committee Name</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Common.UploadFile.Instructions" xml:space="preserve">
    <value>(Instructions for submitting files for upload here)</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.LobbyistEmployerOrLobbyingFirm" xml:space="preserve">
    <value>Lobbyist employer or lobbying firm</value>
  </data>
  <data name="FilerPortal.MyLinkages.SendLinkageRequest.FilerInformation" xml:space="preserve">
    <value>Selected filer information</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.TransactionSummary.CandidatesAndMeasuresNotListedBody" xml:space="preserve">
    <value>For which the Slate Mailer Organization did not receive a payment of $100 or more (either from the candidate or ballot measure committee or from any other person).</value>
  </data>
  <data name="Common.NotApplicable" xml:space="preserve">
    <value>Not Applicable</value>
  </data>
  <data name="Common.WithdrawRegistration" xml:space="preserve">
    <value>Withdraw Registration</value>
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.NoticeOfWithdrawal" xml:space="preserve">
    <value>Notice of withdrawal</value>
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.Verification" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.EffectiveDateOfWithdrawal" xml:space="preserve">
    <value>Effective date of withdrawal</value>
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.LobbyistName" xml:space="preserve">
    <value>Lobbyist name</value>
  </data>
  <data name="FilerPortal.WithdrawLobbyistRegistration.FirmOrEmployerName" xml:space="preserve">
    <value>Name of lobbying firm or lobbyist employer</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.ConfirmTitle" xml:space="preserve">
    <value>Confirm Linkage Request</value>
  </data>
  <data name="FilerPortal.FilerAuthorizedUsers.RequestNewLinkage.ConfirmBody" xml:space="preserve">
    <value>Are you sure you want to send this linkage request?</value>
  </data>
  <data name="FilerPortal.TerminationLobbyistRegistration.NoticeOfTermination" xml:space="preserve">
    <value>Notice of termination</value>
  </data>
  <data name="FilerPortal.TerminationLobbyistRegistration.Verification" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Title" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Body01" xml:space="preserve">
    <value>I certify that all activities which required registration under Government Code Section 86100, et seq. have ceased. If this notice is filed more than 20 days after the effective date for which all activities were terminated, I understand that I must file quarterly reports covering the entire period until the filing of this notice.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Body02" xml:space="preserve">
    <value>I understand that a lobbyist or lobbying firm remains subject to the gift prohibition in Government Code Section 86203 for six months after filing this notice of termination.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Body03" xml:space="preserve">
    <value>I have used all reasonable diligence in preparing this Statement. I have reviewed this Statement and to the best of my knowledge the information contained herein is true and complete. I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.WithdrawalVerification.Body01" xml:space="preserve">
    <value>I have not met the qualification requirements to register as a lobbyist within the meaning of Government Code Section 82039 and Regulation 18239.</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.ExecutedOn" xml:space="preserve">
    <value>Executed On</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.LobbyistEmail" xml:space="preserve">
    <value>Lobbyist Email</value>
  </data>
  <data name="FilerPortal.Filing.Verification.Attestation" xml:space="preserve">
    <value>Send for Attestation</value>
  </data>
  <data name="Common.TerminationRegistration" xml:space="preserve">
    <value>Terminate registration</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.TerminationVerification.UIText" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Termination.Confirmation.Title" xml:space="preserve">
    <value>Termination received</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Withdraw.Confirmation.Title" xml:space="preserve">
    <value>Withdraw notice received</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Amend.Confirmation.Title" xml:space="preserve">
    <value>Submisson received</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Renewal.Confirmation.Title" xml:space="preserve">
    <value>Submission received</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Confirmation.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="Common.WithdrawalRegistration" xml:space="preserve">
    <value>Notice of withdrawal</value>
  </data>
  <data name="Common.RenewalRegistration" xml:space="preserve">
    <value>Renewal registration</value>
  </data>
  <data name="FilePortal.Disclosure.Dashboard.LobbyingAdvertisment.InstructionsTooltip" xml:space="preserve">
    <value>UI text in development progress</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.SendForAttestation.Success" xml:space="preserve">
    <value>Registration successfully sent for attestation.</value>
  </data>
  <data name="Common.CandidateName" xml:space="preserve">
    <value>Candidate name</value>
  </data>
  <data name="Common.CandidateEmail" xml:space="preserve">
    <value>Candidate email</value>
  </data>
  <data name="Common.CandidateAddress" xml:space="preserve">
    <value>Candidate address</value>
  </data>
  <data name="Common.OfficeSought" xml:space="preserve">
    <value>Office sought</value>
  </data>
  <data name="Common.ElectionYear" xml:space="preserve">
    <value>Election year</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page01.Header" xml:space="preserve">
    <value>Notice of withdrawal</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Step01" xml:space="preserve">
    <value>Notice of withdrawal</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Step02" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Title" xml:space="preserve">
    <value>Withdraw Registration</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Breadcrumb" xml:space="preserve">
    <value>Candidate</value>
  </data>
  <data name="PRDPortal.FilerRole.ListGroups.DeleteFailureMessage" xml:space="preserve">
    <value>This role is in use and cannot be deleted</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.AmendTitle" xml:space="preserve">
    <value>Amend Campaign Statement</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page03.EffectiveDateOfChanges" xml:space="preserve">
    <value>Effective date of changes</value>
  </data>
  <data name="Common.UnauthorizedActionToast" xml:space="preserve">
    <value>You are not authorized to take this action.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.Title" xml:space="preserve">
    <value>New registration</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page01.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.GeneralInformation" xml:space="preserve">
    <value>General Information</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.InHouseLobbyists" xml:space="preserve">
    <value>In-house Lobbyists</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.LobbyingFirms" xml:space="preserve">
    <value>Lobbying Firms</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.ResponsibleOfficers" xml:space="preserve">
    <value>Responsible Officers</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page02.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page02.Title" xml:space="preserve">
    <value>First, we need to collect some general information.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Common.Title2" xml:space="preserve">
    <value>Lobbyist employer / lobbying coalition</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.LobbyistName" xml:space="preserve">
    <value>Lobbyist Name</value>
  </data>
  <data name="FilerPortal.TerminateLobbyistRegistration.RequiredTerminatedAt" xml:space="preserve">
    <value>Effective date of termination is required</value>
  </data>
  <data name="FilerPortal.TerminateLobbyistRegistration.TerminatedAt" xml:space="preserve">
    <value>Effective date of termination</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.IsCumulativeAmount" xml:space="preserve">
    <value>The cumulative amount for this payment includes a previously unitemized amount.</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.PreviouslyUnitemizedAmount" xml:space="preserve">
    <value>Previously unitemized amount</value>
  </data>
  <data name="FilerPortal.Disclosure.SmoCampaignStatement.Transactors.AboutThePayment.CumulativeAmountToDate" xml:space="preserve">
    <value>Cumulative amount to date</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.Header" xml:space="preserve">
    <value>Verification</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.Checkbox" xml:space="preserve">
    <value>I certify under penalty of perjury under the laws of the State of California that the foregoing is true and correct.</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.CandHeader" xml:space="preserve">
    <value>Withdrawal notice received</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.CandBody" xml:space="preserve">
    <value>Please note: to change, update or correct the information provided, an amendment must be submitted. </value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Title" xml:space="preserve">
    <value>General Information</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.Name" xml:space="preserve">
    <value>Name of lobbyist employer / lobbying coalition</value>
  </data>
  <data name="Common.BusinessAddress" xml:space="preserve">
    <value>Business Address</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.SameAs" xml:space="preserve">
    <value>Same as business address</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.LegislativeSession" xml:space="preserve">
    <value>Legislative session</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.QualificationDate" xml:space="preserve">
    <value>Qualification date</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.FirstName" xml:space="preserve">
    <value>Lobbyist first name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.LastName" xml:space="preserve">
    <value>Lobbist last name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.MiddleName" xml:space="preserve">
    <value>Lobbyist middle name</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Detail" xml:space="preserve">
    <value>Lobbyist details</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.Creation.Body" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.NonCand.Body01" xml:space="preserve">
    <value>The Withdrawal Notice must be verified and attested to by the Candidate.</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.NonCand.Body02" xml:space="preserve">
    <value>When the Statement is ready to submit, click Send for Attestation and a notification will be sent to the Candidate to allow them to verify and submit.</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page02.NonCand.Body03" xml:space="preserve">
    <value>The Candidate Withdrawal Notice will not be submitted until after the verification is complete.</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.NonCandHeader" xml:space="preserve">
    <value>Withdrawal Attestation Request Sent</value>
  </data>
  <data name="FilerPortal.WithdrawalCis.Page03.NonCandBody" xml:space="preserve">
    <value>This Candidate Withdrawal Notice is pending until the candidate completes the verification.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page04.Subtitle" xml:space="preserve">
    <value>State agencies to be influenced</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page03.IsLobbyingCoalition" xml:space="preserve">
    <value>This registration is for a lobbying coalition</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.StateAgenciesLobbied" xml:space="preserve">
    <value>State agencies whose actions you will attempt to influence</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.FullList" xml:space="preserve">
    <value>Show full list of agencies</value>
  </data>
  <data name="FilerPortal.LobbyistRegistration.Page04.StateLegislatureLobbied" xml:space="preserve">
    <value>Will the state legislature be lobbied?</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Subtitle" xml:space="preserve">
    <value>Description of lobbying interests</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example1" xml:space="preserve">
    <value>Example 1</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example2" xml:space="preserve">
    <value>Example 2</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.DescribeInterests" xml:space="preserve">
    <value>Describe your filing interests</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example1Body" xml:space="preserve">
    <value>ABC Corporation develops, manufactures and distributes pharmaceuticals. It would not be sufficient to describe the corporation’s lobbying interests as “Legislation relating to business,” or “Legislation relating to manufacturing.” The description should say “Legislation relating to the development, manufacturing and distribution of pharmaceuticals.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Page05.Example2Body" xml:space="preserve">
    <value>A group of real estate companies decides to share the cost of hiring a lobbying firm to influence a specific regulation being considered by the Department of Fair Employment and Housing. Because there are 10 or more companies pooling funds to hire a lobbyist, the group qualifies as a “lobbying coalition.” The registration should specifically describe the administrative action to be lobbied, such as “Regulations of the Department of Fair Employment and Housing relating to adults-only rental policies (Section 12-8, 12-9).</value>
  </data>
  <data name="Common.NoSelection.ErrorMessage" xml:space="preserve">
    <value>No selection made. Please choose one of the options to proceed.</value>
  </data>
  <data name="FilerPortal.AccountManagement.AddAnotherPhoneButton" xml:space="preserve">
    <value>Add another phone number</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.InitiateCertifyLobbyist.Title" xml:space="preserve">
    <value>Lobbyist Certification Statement</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.InitiateCertifyLobbyist.SummaryText" xml:space="preserve">
    <value>You are now initiating a lobbyist certification statement (equivalent of the Form 604) on behalf of the indicated lobbyist. Once completed, this statement will be sent to the named lobbyist for verification and payment for any relevant fees will be collected.</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.TableTitle" xml:space="preserve">
    <value>Itemized campaign contributions</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.TableBody" xml:space="preserve">
    <value>Itemize contributions of $100 or more that have not been reported on a campaign disclosure statement, including contributions made by your sponsored committee(s), below.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.Lobbyist.DeleteConfirm" xml:space="preserve">
    <value>Are you sure you want to delete this lobbyist from the employer/coalition? This lobbyist will be terminated and receive a notification.</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.PayLobbyistFee.Title" xml:space="preserve">
    <value>Pay lobbyist fee</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.PayLobbyistFee.SummaryText" xml:space="preserve">
    <value>UI Text Explanation under development and review</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.PayLobbyistFee.Question" xml:space="preserve">
    <value>Who will pay the lobbyist fee?</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.PayLobbyistFee.ByEmployer" xml:space="preserve">
    <value>The lobbyist employer will pay on behalf of the lobbyist</value>
  </data>
  <data name="FilerPortal.LobbyistEmployerRegistration.PayLobbyistFee.ByLobbyist" xml:space="preserve">
    <value>The lobbyist will pay the fee as part of their review and verification</value>
  </data>
  <data name="FilerPortal.Disclosure.CampaignContributions.AddTitle" xml:space="preserve">
    <value>Contribution information</value>
  </data>
</root>
