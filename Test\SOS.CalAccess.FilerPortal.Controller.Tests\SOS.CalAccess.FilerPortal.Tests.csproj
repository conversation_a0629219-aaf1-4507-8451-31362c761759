<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <PropertyGroup>
    <SonarQubeTestProject>true</SonarQubeTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="NSubstitute" Version="5.3.0" />
    <PackageReference Include="NUnit" Version="3.14.0" />
    <PackageReference Include="NUnit.Analyzers" Version="3.9.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="System.Collections" Version="4.3.0" />
    <PackageReference Include="System.Diagnostics.Debug" Version="4.3.0" />
    <PackageReference Include="System.IO" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Src\Contracts\SOS.CalAccess.Data\SOS.CalAccess.Data.Contracts.csproj" />
    <ProjectReference Include="..\..\Src\Portals\SOS.CalAccess.FilerPortal\SOS.CalAccess.FilerPortal.csproj" />
    <ProjectReference Include="..\..\Src\SOS.CalAccess.Services.Common\SOS.CalAccess.Services.Common.csproj" />
    <ProjectReference Include="..\SOS.CalAccess.Tests.Common\SOS.CalAccess.Tests.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="NUnit.Framework" />
  </ItemGroup>

</Project>
