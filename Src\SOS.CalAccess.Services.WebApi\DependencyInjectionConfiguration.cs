using System.Diagnostics.CodeAnalysis;
using SOS.CalAccess.Data.Authorization;
using SOS.CalAccess.Data.Common;
using SOS.CalAccess.Data.Contracts.Authorization;
using SOS.CalAccess.Data.Contracts.Common;
using SOS.CalAccess.Data.Contracts.DataValidation;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Contacts;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Filings;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Elections;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.Contracts.UserAccountMaintenance;
using SOS.CalAccess.Data.Efile;
using SOS.CalAccess.Data.Email;
using SOS.CalAccess.Data.EntityFramework;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Audit.Sinks;
using SOS.CalAccess.Data.EntityFramework.Repositories;
using SOS.CalAccess.Data.EntityFramework.Repositories.Authorization;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filers;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Messaging;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Registrations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Users;
using SOS.CalAccess.Data.EntityFramework.Repositories.Efile;
using SOS.CalAccess.Data.EntityFramework.Repositories.EmailMessaging;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Contacts;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Elections;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Filers;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Registrations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Notifications;
using SOS.CalAccess.Data.EntityFramework.Repositories.SeedData;
using SOS.CalAccess.Data.EntityFramework.Repositories.SmsMessaging;
using SOS.CalAccess.Data.EntityFramework.Repositories.UploadFile;
using SOS.CalAccess.Data.EntityFramework.Repositories.UserAccountMaintenance;
using SOS.CalAccess.Data.FilerDisclosure.Contacts;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Data.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.FilerRegistration;
using SOS.CalAccess.Data.FilerRegistration.Elections;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Data.FilerRegistration.Lobbyists;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Data.SeedData;
using SOS.CalAccess.Data.SmsMessaging;
using SOS.CalAccess.Data.UserAccountMaintenance;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.Efile;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Notification;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.SeedData;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.DataValidation;
using SOS.CalAccess.Services.Common.Email;
using SOS.CalAccess.Services.Common.EmailMessaging;
using SOS.CalAccess.Services.Common.FileSystem;
using SOS.CalAccess.Services.Common.Notification;
using SOS.CalAccess.Services.Common.Queuing;
using SOS.CalAccess.Services.Common.SmsMessaging;
using SOS.CalAccess.Services.Common.UserAccountMaintenance;
using SOS.CalAccess.Services.Technical.SystemAdministration;
using SOS.CalAccess.Services.WebApi.Authentication;
using SOS.CalAccess.Services.WebApi.Users;

namespace SOS.CalAccess.Services.WebApi;

[ExcludeFromCodeCoverage]
public class DependencyInjectionConfiguration
{
    /// <summary>
    /// Setup and configure dependency injection for the data layer repositories
    /// </summary>
    /// <param name="builder"></param>
    public static void AddRepositories(WebApplicationBuilder builder)
    {
        builder.Services.AddDataLayer(builder.Configuration, options =>
        {
            options.UseLogSink<SelfContainedSink<DatabaseContext>>(ServiceLifetime.Scoped);
            options.UseDedicatedHighLevelLogSink<LoggerSink>(ServiceLifetime.Scoped);
            options.UseAttributionProvider<AuthAttributionProvider>(ServiceLifetime.Scoped);
        });

        // Seperted out to allow use Processing Function App
        AddRepositories(builder.Services);
    }

    /// <summary>
    /// Setup and configure dependency injection for the data layer repositories
    /// Seperted out to allow use Processing Function App
    /// </summary>
    /// <param name="builder"></param>
    public static void AddRepositories(IServiceCollection service)
    {
        service.AddScoped<IActionsLobbiedRepository, ActionsLobbiedRepository>();
        service.AddScoped<IActivityExpenseRepository, ActivityExpenseRepository>();
        service.AddScoped<IAgencyRepository, AgencyRepository>();
        service.AddScoped<IAmendmentRepository, AmendmentRepository>();
        service.AddScoped<IApiRequestRepository, ApiRequestRepository>();
        service.AddScoped<IAttestationRepository, AttestationRepository>();
        service.AddScoped<IAuthorizationGroupRepository, AuthorizationGroupRepository>();
        service.AddScoped<IBallotMeasureRepository, BallotMeasureRepository>();
        service.AddScoped<IBannerMessageRepository, BannerMessageRepository>();
        service.AddScoped<IBillRepository, BillRepository>();
        service.AddScoped<ICandidateRepository, CandidateRepository>();
        service.AddScoped<ICountryRepository, CountryRepository>();
        service.AddScoped<IDashboardQueryRepository, DashboardQueryRepository>();
        service.AddScoped<IDisclosureFilingNonRegisteredLobbyistRepository, DisclosureFilingNonRegisteredLobbyistRepository>();
        service.AddScoped<IDisclosureWithoutPaymentReceivedRepository, DisclosureWithoutPaymentReceivedRepository>();
        service.AddScoped<IDistrictRepository, DistrictRepository>();
        service.AddScoped<IElectionRepository, ElectionRepository>();
        service.AddScoped<IElectionCycleRepository, ElectionCycleRepository>();
        service.AddScoped<IElectionTypeRepository, ElectionTypeRepository>();
        service.AddScoped<IElectionRaceRepository, ElectionRaceRepository>();
        service.AddScoped<IEmailMessageRepository, EmailMessageRepository>();
        service.AddScoped<IExpenditureCodeRepository, ExpenditureCodeRepository>();
        service.AddScoped<IFilerRepository, FilerRepository>();
        service.AddScoped<IFilerContactRepository, FilerContactRepository>();
        service.AddScoped<IFilerContactTypeRepository, FilerContactTypeRepository>();
        service.AddScoped<IFilerRoleRepository, FilerRoleRepository>();
        service.AddScoped<IFilerTypeRepository, FilerTypeRepository>();
        service.AddScoped<IFilerUserRepository, FilerUserRepository>();
        service.AddScoped<IFilerLinkRepository, FilerLinkRepository>();
        service.AddScoped<IFilingRepository, FilingRepository>();
        service.AddScoped<IFilingPeriodRepository, FilingPeriodRepository>();
        service.AddScoped<IFilingRelatedFilerRepository, FilingRelatedFilerRepository>();
        service.AddScoped<IFilingStatusRepository, FilingStatusRepository>();
        service.AddScoped<IFilingSummaryRepository, FilingSummaryRepository>();
        service.AddScoped<IFilingSummaryStatusRepository, FilingSummaryStatusRepository>();
        service.AddScoped<IFilingTypeRepository, FilingTypeRepository>();
        service.AddScoped<IJsonSchemaRepository, JsonSchemaRepository>();
        service.AddScoped<ILinkageRequestRepository, LinkageRequestRepository>();
        service.AddScoped<IMultipurposeOrganizationRepository, MultipurposeOrganizationRepository>();
        service.AddScoped<INotificationMessageRepository, NotificationMessageRepository>();
        service.AddScoped<INotificationTemplateRepository, NotificationTemplateRepository>();
        service.AddScoped<INotificationTemplateTranslationRepository, NotificationTemplateTranslationRepository>();
        service.AddScoped<INotificationTypeRepository, NotificationTypeRepository>();
        service.AddScoped<IOfficeRepository, OfficeRepository>();
        service.AddScoped<IOfficialPositionRepository, OfficialPositionsRepository>();
        service.AddScoped<IPaymentCodeRepository, PaymentCodeRepository>();
        service.AddScoped<IAdvertisementDistributionMethodRepository, AdvertisementDistributionMethodRepository>();
        service.AddScoped<IPermissionRepository, PermissionRepository>();
        service.AddScoped<IPoliticalPartyRepository, PoliticalPartyRepository>();
        service.AddScoped<IRegistrationContactRepository, RegistrationContactRepository>();
        service.AddScoped<IRegistrationRepository, RegistrationRepository>();
        service.AddScoped<IRegistrationRegistrationContactRepository, RegistrationRegistrationContactRepository>();
        service.AddScoped<IRegistrationStatusRepository, RegistrationStatusRepository>();
        service.AddScoped<ISeedCommonRepository, SeedCommonRepository>();
        service.AddScoped<ISeedContactRepository, SeedContactRepository>();
        service.AddScoped<ISeedRegistrationRepository, SeedRegistrationRepository>();
        service.AddScoped<ISeedTransactionRepository, SeedTransactionRepository>();
        service.AddScoped<ISmsMessageRepository, SmsMessageRepository>();
        service.AddScoped<ITransactionRepository, TransactionRepository>();
        service.AddScoped<ITransactionReportablePersonRepository, TransactionReportablePersonRepository>();
        service.AddScoped<IUserNotificationPreferenceRepository, UserNotificationPrefenceRepository>();
        service.AddScoped<IUserRepository, UserRepository>();
        service.AddScoped<IUploadedFileRepository, UploadedFileRepository>();
        service.AddSingleton<IPersistenceErrorMapper, SqlServerErrorMapper>();
        service.AddScoped<IFilingContactSummaryRepository, FilingContactSummaryRepository>();
        service.AddScoped<INatureAndInterestTypeRepository, NatureAndInterestTypeRepository>();
        service.AddScoped<IIndustryGroupClassificationTypeRepository, IndustryGroupClassificationTypeRepository>();
        service.AddScoped<ILobbyingInterestRepository, LobbyingInterestRepository>();
        service.AddScoped<IBusinessSubcategoryRepository, BusinessSubcategoryRepository>();
    }

    /// <summary>
    /// Setup and configure dependency injection for the Common Services layer 
    /// </summary>
    /// <param name="builder"></param>
    public static void AddCommonServices(WebApplicationBuilder builder)
    {
        builder.Services.AddSingleton(new DecisionsSvcOptions(
            builder.Configuration.GetValue<string>("Decisions:Host"),
            builder.Configuration.GetValue<string>("Decisions:SessionId")
        ));
        // Seperated out to allow use Processing Function App
        AddCommonServices(builder.Services, builder.Configuration);
    }

    /// <summary>
    /// Setup and configure dependency injection for the Common Services layer
    /// Seperated out to allow use Processing Function App
    /// </summary>
    /// <param name="builder"></param>
    public static void AddCommonServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton(new DateTimeSvcOptions(
            configuration.GetValue<bool>("DateTime:EnableOverride"),
            configuration.GetValue<DateTime?>("DateTime:OverrideDate"),
            configuration.GetValue<string?>("DateTime:TimeZoneId")
        ));
        services.AddSingleton<IDateTimeSvc, DateTimeSvc>();

        services.AddSingleton(new AddressValidationOptions(
            configuration.GetValue<string>("AccuMail:Host") ?? "",
            configuration.GetValue<string>("AccuMail:Path") ?? ""
        ));

        services.AddHttpClient(nameof(AddressValidationSvc), c => c.BaseAddress = new Uri(configuration.GetValue<string>("AccuMail:Host") ?? ""));

        services.AddSingleton(new EmailMessagingOptions(
            configuration.GetValue<string>("EmailMessaging:FromEmailAddress") ?? ""
        ));
        services.AddSingleton(new SmsMessagingOptions(
             configuration.GetValue<string>("SmsMessaging:FromPhoneNumber") ?? ""
         ));

        services.AddSingleton(new AuthorizationSvcOptions(
           configuration.GetValue("Authorization.Enabled", true)
       ));

        services.AddSingleton<IAddressValidationSvc, AddressValidationSvc>();
        services.AddScoped<IBannerMessageMaintenanceSvc, BannerMessageMaintenanceSvc>();
        services.AddSingleton<IDecisionsSvc, DecisionsSvc>();
        services.AddScoped<IEmailSvc, EmailSvc>();
        services.AddSingleton<IFileSvc, FileSvc>();
        services.AddSingleton<IMessageQueueSvc, MessageQueueSvc>();
        services.AddScoped<IReferenceDataSvc, ReferenceDataSvc>();
        services.AddScoped<ISmsMessagingSvc, SmsMessagingSvc>();
        services.AddScoped<IActivityExpenseSvc, ActivityExpenseSvc>();
        services.AddScoped<IUploadFileSvc, UploadFileSvc>();
    }

    /// <summary>
    /// Setup and configure dependency injection for the Business layer services
    /// </summary>
    /// <param name="builder"></param>
    public static void AddBusinessServices(WebApplicationBuilder builder)
    {
        // Seperated out to allow use Processing Function App
        AddBusinessServices(builder.Services);
    }

    /// <summary>
    /// Setup and configure dependency injection for the Business layer services
    /// Seperated out to allow use Processing Function App
    /// </summary>
    /// <param name="builder"></param>
    public static void AddBusinessServices(IServiceCollection services)
    {
        services.AddScoped<IActionsLobbiedSvc, ActionsLobbiedSvc>();
        services.AddScoped<IApiRequestSvc, ApiRequestSvc>();
        services.AddScoped<IAuthorizationGroupMaintenanceSvc, AuthorizationGroupMaintenanceSvc>();
        services.AddScoped<IAuthorizationSvc, AuthorizationSvc>();
        services.AddScoped<IBallotMeasureSvc, BallotMeasureSvc>();
        services.AddScoped<ICandidateIntentionDependencies, CandidateIntentionDependencies>();
        services.AddScoped<ICandidateIntentionRegistrationSvc, CandidateIntentionRegistrationSvc>();
        services.AddScoped<ICisRegistrationWithdrawalSvc, CisRegistrationWithdrawalSvc>();
        services.AddScoped<ICandidateSvc, CandidateSvc>();
        services.AddScoped<IDashboardQuerySvc, DashboardQuerySvc>();
        services.AddScoped<IEfileSubmissionSvc, EfileSubmissionSvc>();
        services.AddScoped<IEfileValidationsSvc, EfileValidationsSvc>();
        services.AddScoped<IElectionRaceSvc, ElectionRaceSvc>();
        services.AddScoped<IElectionSvc, ElectionSvc>();
        services.AddScoped<IFilerContactSvc, FilerContactSvc>();
        services.AddScoped<IFilerRoleMaintenanceSvc, FilerRoleMaintenanceSvc>();
        services.AddScoped<IFilerSvc, FilerSvc>();
        services.AddScoped<IFilingSummarySvc, FilingSummarySvc>();
        services.AddScoped<IFilingSvc, FilingSvc>();
        services.AddScoped<IForm470Svc, Form470Svc>();
        services.AddScoped<IForm470AmendSvc, Form470AmendSvc>();
        services.AddScoped<IForm470SSvc, Form470SSvc>();
        services.AddScoped<IForm470SAmendSvc, Form470SAmendSvc>();
        services.AddScoped<IIdentityProviderSynchronizationSvc, IdentityProviderSynchronizationSvc>();
        services.AddScoped<IJsonSchemaValidationSvc, JsonSchemaValidationSvc>();
        services.AddScoped<ILinkageSvc, LinkageSvc>();
        services.AddScoped<ILobbyingFirmRegistrationSvc, LobbyingFirmRegistrationSvc>();
        services.AddScoped<ILobbyistEmployerRegistrationSvc, LobbyistEmployerRegistrationSvc>();
        services.AddScoped<ILobbyistRegistrationSvc, LobbyistRegistrationSvc>();
        services.AddScoped<INotificationMaintenanceSvc, NotificationMaintenanceSvc>();
        services.AddScoped<INotificationSvc, NotificationSvc>();
        services.AddScoped<INotificationWrapperSvc, NotificationWrapperSvc>();
        services.AddScoped<IRegistrationSvc, RegistrationSvc>();
        services.AddScoped<IReport72HSvc, Report72HSvc>();
        services.AddScoped<ISmoRegistrationSvc, SmoRegistrationSvc>();
        services.AddScoped<ISeedDataSvc, SeedDataSvc>();
        services.AddScoped<ISmoCampaignStatementSvc, SmoCampaignStatementSvc>();
        services.AddScoped<ITransactionReportablePersonSvc, TransactionReportablePersonSvc>();
        services.AddScoped<ITransactionSvc, TransactionSvc>();
        services.AddScoped<IUserMaintenanceSvc, UserMaintenanceSvc>();
        services.AddScoped<IUserNotificationPreferenceSvc, UserNotificationPreferenceSvc>();
        services.AddScoped<ITransactionHelperSvc, TransactionHelperSvc>();

        // Add Dependencies
        services.AddScoped<SmoRegistrationSvcDependencies>();
        services.AddScoped<SmoCampaignStatementSvcDependencies>();
        services.AddScoped<Form470SvcDependencies>();
        services.AddScoped<FilingSvcDependencies>();
        services.AddScoped<FilingSharedRepositoriesDependencies>();
        services.AddScoped<FilingSharedServicesDependencies>();
        services.AddScoped<LobbyistRegistrationSvcDependencies>();
        services.AddScoped<Report72HDependencies>();
        services.AddScoped<LobbyistEmployerRegistrationSvcDependencies>();

        //Add Mappers
        services.AddScoped<IRegistrationModelMapper, RegistrationModelMapper>();
    }

    /// <summary>
    /// Setup of Services provided by Maplight Accelerator project
    ///
    /// These classes should evenutally be able to be removed when functionality is fully implemented in the CARS applicaiton
    /// </summary>
    /// <param name="builder"></param>
    public static void AddMaplightAcceleratorClasses(WebApplicationBuilder builder)
    {
        // Seperated out to allow use Processing Function App
        AddMaplightAcceleratorClasses(builder.Services);
    }

    /// <summary>
    /// Setup of Services provided by Maplight Accelerator project
    /// These classes should evenutally be able to be removed when functionality is fully implemented in the CARS applicaiton
    /// Seperated out to allow use Processing Function App
    /// </summary>
    /// <param name="builder"></param>
    public static void AddMaplightAcceleratorClasses(IServiceCollection services)
    {
        services.AddTransactionOperations();
        services.AddMessagingOperations();
        services.AddFilingOperations();
        services.AddDecisionsIntegration();
        services.AddSingleton<ModelMapper<CreateUserRequest, User>>(UserModelExtensions.AsUser);
        services.AddScoped<ICreateUser<CreateUserRequest>, CreateUser<CreateUserRequest>>();
        services.AddScoped<IGetAllUsers, GetAllUsers>();
        services.AddScoped<IGetUser, GetUser>();
        services.AddScoped<IGetAllRegistrations, GetAllRegistrations>();
        services.AddScoped<IGetRegistration, GetRegistration>();
        services.AddScoped<IGetCurrentRegistration, GetCurrentRegistration>();
        services.AddScoped<ITerminateFilerRegistration, TerminateFilerRegistration>();
        services.AddScoped<ICreateContact, CreateContact>();
        services.AddScoped<ICreateFilerContact, CreateFilerContact>();
        services.AddScoped<IGetContact, GetContact>();
        services.AddScoped<IGetAllContacts, GetAllContacts>();
        services.AddScoped<IUpdateContact, UpdateContact>();
        services.AddScoped<IUpdateFilerContact, UpdateFilerContact>();
        services.AddScoped<ICreatePayee, CreatePayee>();
        services.AddScoped<ICheckOwnership, CheckOwnership>();
    }
}
