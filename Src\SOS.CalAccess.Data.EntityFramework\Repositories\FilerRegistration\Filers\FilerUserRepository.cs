using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Filers;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Filers;
public class FilerUserRepository(DatabaseContext dbContext) : Repository<FilerUser, long>(dbContext), IFilerUserRepository
{
    public async Task<IEnumerable<long>> GetUserIdsByFilerId(long filerId)
    {
        var users = dbContext.Set<FilerUser>()
            .Where(fu => fu.FilerId == filerId)
            .Select(fu => fu.UserId);

        return await users.ToListAsync();
    }

    /// <inheritdoc />
    public async Task<FilerUser?> FindFilerUserByFilerId(long filerId)
    {
        return await dbContext.Set<FilerUser>()
            .Include(fu => fu.FilerRole)
            .FirstOrDefaultAsync(fu => fu.FilerId == filerId);
    }

    /// <inheritdoc />
    public async Task<List<FilerUser>> FindFilerUsersByFilerId(long filerId)
    {
        return await dbContext.Set<FilerUser>()
            .AsNoTracking()
            .Include(fu => fu.User)
            .Include(fu => fu.FilerRole)
            .Where(fu => fu.FilerId == filerId)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<List<FilerUser>> FindFilerUsersByUserId(long userId)
    {
        return await dbContext.Set<FilerUser>()
            .Include(f => f.User)
            .Include(f => f.Filer)
                .ThenInclude(filer => filer!.CurrentRegistration)
            .Include(fu => fu.FilerRole)
            .Where(fu => fu.UserId == userId)
            .AsNoTracking()
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<FilerUser?> FindFilerUserByFilerIdAndUserId(long filerId, long userId)
    {
        return await dbContext.Set<FilerUser>()
            .AsNoTracking()
            .Include(fu => fu.FilerRole)
            .Include(fu => fu.User)
            .FirstOrDefaultAsync(fu => fu.FilerId == filerId && fu.UserId == userId);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<FilerUser>> GetFilerUsersByUserId(long userId)
    {
        return await dbSet
            .AsNoTracking()
            .Include(fu => fu.Filer!.CurrentRegistration)
            .Include(fu => fu.FilerRole)
            .Where(fu => fu.UserId == userId)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<FilerUser?> GetFilerUserById(long filerUserId)
    {
        return await dbContext.Set<FilerUser>()
            .Include(fu => fu.User)
            .Include(f => f.Filer)
                .ThenInclude(f => f!.CurrentRegistration!)
            .FirstOrDefaultAsync(fu => fu.Id == filerUserId);
    }

    /// <inheritdoc />
    public async Task<List<FilerUser>> TerminateRegistrationAmendmentContactLinkages(long registrationId)
    {
        // Get the UserIds linked to deleted RegistrationContacts (Active = false)
        // within a Registration amendment (registrationId)
        // that were copied from the parent Registration (UserId match check)
        // TD: Need to narrow this to specific FilerRoleIds for cases where user has more than one role for the same filer
        var filerUsers = await (
                from fu in dbContext.FilerUsers
                join prc in dbContext.RegistrationContacts on fu.UserId equals prc.UserId
                join prrc in dbContext.RegistrationsRegistrationContacts on prc.Id equals prrc.RegistrationContactId
                join reg in dbContext.Registrations on prrc.RegistrationId equals reg.ParentId
                join crc in dbContext.RegistrationContacts on prc.UserId equals crc.UserId // Used to determine which RegistrationContactIds are copies
                join crrc in dbContext.RegistrationsRegistrationContacts on crc.Id equals crrc.RegistrationContactId
                where reg.Id == registrationId &&
                fu.FilerId == reg.FilerId && // Consider FilerUsers for this registration's filer
                reg.Id == crrc.RegistrationId && // Consider the current RegistrationContacts
                prrc.Active && // Consider records from parent that were active
                !crrc.Active // Consider records from current that were deleted
                select fu
             ).ToListAsync();

        return await DeleteFilerUsers(filerUsers);
    }

    /// <inheritdoc />
    public async Task<List<FilerUser>> FindFilerUsersLinkedToRegistrationContacts(long registrationId)
    {
        return await (
            from fu in dbContext.FilerUsers
            join fr in dbContext.FilerRoles on fu.FilerRoleId equals fr.Id
            join rc in dbContext.RegistrationContacts on fu.UserId equals rc.UserId
            join rrc in dbContext.RegistrationsRegistrationContacts on rc.Id equals rrc.RegistrationContactId
            join r in dbContext.Registrations on rrc.RegistrationId equals r.Id
            where r.FilerId == fu.FilerId &&
                r.Id == registrationId &&
                fr.Name == rrc.Role // TD: Replace with more stable link
            select fu
        ).ToListAsync();
    }

    /// <inheritdoc />
    public async Task<List<FilerUser>> DeleteFilerUsers(List<FilerUser> filerUsers)
    {
        var filerUsersToReturn = await dbContext.FilerUsers
            .Include(fu => fu.Filer!)
            .Include(fu => fu.User!)
            .Where(fu => filerUsers.Select(x => x.Id).Contains(fu.Id))
            .ToListAsync();

        dbContext.FilerUsers.RemoveRange(filerUsers);
        await dbContext.SaveChangesAsync();

        return filerUsersToReturn;
    }
}
