using System.Globalization;
using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Filings;


[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[NonParallelizable]
//[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(Form470SSvc))]
public class Form470SSvcTests
{
    private IRegistrationRepository _registrationRepository;
    private IAttestationRepository _attestationRepository;
    private IFilingRepository _filingRepository;
    private IAuthorizationSvc _authorizationSvc;
    private IDecisionsSvc _decisionsSvc;
    private INotificationSvc _notificationSvc;
    private IFilerSvc _filerSvc;
    private IUserMaintenanceSvc _userMaintenanceSvc;
    private IDateTimeSvc _dateTimeSvcMock;
    private Form470SvcDependencies _dependenices;
    private DateTime _dateNow;

    private Form470SSvc _service;

    [SetUp]
    public void SetUp()
    {
        _registrationRepository = Substitute.For<IRegistrationRepository>();
        _attestationRepository = Substitute.For<IAttestationRepository>();
        _filingRepository = Substitute.For<IFilingRepository>();
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _notificationSvc = Substitute.For<INotificationSvc>();
        _filerSvc = Substitute.For<IFilerSvc>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();


        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _dependenices = new Form470SvcDependencies
        (
            _decisionsSvc,
            _authorizationSvc,
            _filerSvc,
            _notificationSvc,
            _userMaintenanceSvc,
            _dateTimeSvcMock,
            _registrationRepository,
            _filingRepository
        );

        _service = new Form470SSvc(_filingRepository, _registrationRepository, _decisionsSvc, _notificationSvc, _authorizationSvc, _userMaintenanceSvc, _filerSvc, _dateTimeSvcMock);
    }

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsMappedDto_WhenDataExists()
    {
        // Arrange
        var filerId = 1L;
        var candidateIntention = new CandidateIntentionStatement
        {
            StatusId = 1,
            Id = 100,
            FilerId = filerId,
            Name = "Sri S",
            Email = "<EMAIL>",
            ElectionOfficeSought = "Governor",
            ElectionJurisdiction = "CA",
            ElectionDistrictNumber = "12",
            ElectionRace = new()
            {
                Election = new()
                {
                    ElectionDate = new DateTime(2024, 11, 5, 0, 0, 0, 0),
                    Name = "Sri election"
                }
            },
            AddressList = new()
            {
                Addresses = new()
                {
                    new()
                    {
                        Purpose = "Candidate",
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Type = "Home"
                    }
                }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                {
                    new() { Type = "Home", Number = "555-1234" },
                    new() { Type = "Fax", Number = "555-5678" }
                }
            }
        };

        _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
            .Returns(candidateIntention);

        // Act
        var result = await _service.GetCisWithElection470SByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.RegistrationId, Is.EqualTo(candidateIntention.Id));
            Assert.That(result.FilerId, Is.EqualTo(candidateIntention.FilerId));
            Assert.That(result.Name, Is.EqualTo(candidateIntention.Name));
            Assert.That(result.EmailAddress, Is.EqualTo(candidateIntention.Email));
            Assert.That(result.OfficeSought, Is.EqualTo(candidateIntention.ElectionOfficeSought));
            Assert.That(result.ElectionDate, Is.EqualTo(candidateIntention.ElectionRace.Election.ElectionDate));
            Assert.That(result.PhoneNumber, Is.EqualTo("555-1234"));
            Assert.That(result.FaxNumber, Is.EqualTo("555-5678"));
            Assert.That(result.AddressDto?.City, Is.EqualTo("Sacramento"));
        });
    }

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsEmptyObj_WhenDataIsMissing()
    {
        // Arrange
        var filerId = 2L;
        _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
            .Returns((CandidateIntentionStatement?)null);

        // Act
        var result = await _service.GetCisWithElection470SByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsMappedDto_WithNullAddress_WhenNoAddressAndPhoneNumbersList()
    {
        // Arrange
        var filerId = 1L;
        var candidateIntention = new CandidateIntentionStatement
        {
            StatusId = 1,
            Id = 100,
            FilerId = filerId,
            Name = "Sri S",
            Email = "<EMAIL>",
            ElectionOfficeSought = "Governor",
            ElectionJurisdiction = "CA",
            ElectionDistrictNumber = "12",
            ElectionRace = new()
            {
                Election = new()
                {
                    ElectionDate = new DateTime(2024, 11, 5, 0, 0, 0, 0),
                    Name = "Sri election"
                }
            },
            AddressList = null,
            PhoneNumberList = null
        };

        _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
            .Returns(candidateIntention);

        // Act
        var result = await _service.GetCisWithElection470SByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.RegistrationId, Is.EqualTo(candidateIntention.Id));
            Assert.That(result.FilerId, Is.EqualTo(candidateIntention.FilerId));
            Assert.That(result.Name, Is.EqualTo(candidateIntention.Name));
            Assert.That(result.EmailAddress, Is.EqualTo(candidateIntention.Email));
            Assert.That(result.OfficeSought, Is.EqualTo(candidateIntention.ElectionOfficeSought));
            Assert.That(result.ElectionDate, Is.EqualTo(candidateIntention.ElectionRace.Election.ElectionDate));
            Assert.That(result.PhoneNumber, Is.Null);
            Assert.That(result.FaxNumber, Is.Null);
            Assert.That(result.AddressDto, Is.Null);
        });
    }

    [Test]
    public async Task GetForm470Overview_ReturnsResponse_WhenFilingAndRegistrationExist()
    {
        // Arrange
        long filingId = 1;
        long filerId = 10;

        var filing = new OfficeHolderCandidateSupplementForm
        {
            Id = filingId,
            FilerId = filerId,
            FilingPeriodId = 100,
            StatusId = FilingStatus.Pending.Id,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
            StartDate = _dateNow,
            EndDate = _dateNow.AddDays(1)
        };

        var registration = new CandidateIntentionStatement
        {
            Id = 2,
            FilerId = filerId,
            Name = "Sri S",
            Email = "<EMAIL>",
            ElectionOfficeSought = "Mayor",
            ElectionJurisdiction = "City",
            ElectionDistrictNumber = "1",
            StatusId = 1,
        };

        _filingRepository.GetForm470SById(filingId).Returns(filing);
        _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId).Returns(registration);

        // Act
        var result = await _service.GetForm470SOverview(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.CandidateIntentionStatement470S, Is.Not.Null);
            Assert.That(result.Form470SFiling, Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.Form470SFiling.Id, Is.EqualTo(filingId));
            Assert.That(result.CandidateIntentionStatement470S?.FilerId, Is.EqualTo(filerId));
        });
    }

    [Test]
    public async Task GetForm470Overview_ReturnsEmptyObj_WhenFilingNotFound()
    {
        // Arrange
        _filingRepository.GetForm470ById(Arg.Any<long>()).Returns((OfficeHolderCandidateShortForm?)null);

        // Act
        var result = await _service.GetForm470SOverview(1);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetForm470Overview_ReturnsEmptyObj_WhenCandidateIntentionStatementNotFound()
    {
        // Arrange
        var filing = new OfficeHolderCandidateShortForm { Id = 1, FilerId = 10, StatusId = 1 };
        _filingRepository.GetForm470ById(1).Returns(filing);
        _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(10)
            .Returns((CandidateIntentionStatement?)null);

        // Act
        var result = await _service.GetForm470SOverview(1);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    #region CreateOfficeHolderCandidateSupplementFormFiling - 470S

    [Test]
    public async Task CreateOfficeHolderCandidateSupplementFormFiling_WhenDecisionHasErrors_ReturnsValidationResponse()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = _dateNow.AddDays(2),
            IsSubmission = true,
            FilerId = 123
        };

        var errors = new List<WorkFlowError> { new("ContributionsOrExpenditureOverOn", "ErrGlobal0007", "Validation", "A date in the future is not valid") };
        var decisionResponse = new DecisionsForm470SResponse(errors);

        //_decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsForm470S>(), Arg.Is<bool>(b => b))
        //    .Returns(decisionResponse);

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
           Arg.Is<DecisionsWorkflow>(w => w == DecisionsWorkflow.Form470SRuleSet),
           Arg.Is<DecisionsForm470S>(x => x.ContributionsOrExpenditureOverOn == request.ContributionsOrExpenditureOverOn),
           Arg.Is<bool>(b => b))
           .Returns(decisionResponse);

        // Act
        var result = await _service.CreateOfficeHolderCandidateSupplementFormFiling(request);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EqualTo(errors));
        });
    }

    [Test]
    public async Task CreateOfficeHolderCandidateSupplementFormFiling_WhenValidApprovedStatus_SendsNotificationAndReturnsDto()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = new DateTime(2024, 04, 30, 12, 0, 0, 0),
            IsSubmission = true,
            FilerId = 123
        };

        var decisionsForm470S = new DecisionsForm470S { ContributionsOrExpenditureOverOn = _dateNow };
        var decisionResponse = new DecisionsForm470SResponse(new List<WorkFlowError>())
        {
            Notifications = new List<NotificationTrigger> { new(true, 5, null) }
        };

        var filerUser = new FilerUserDto();

        var filing = new OfficeHolderCandidateSupplementForm { Id = 10, StatusId = 5 };

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
            Arg.Is<DecisionsWorkflow>(w => w == DecisionsWorkflow.Form470SRuleSet),
            Arg.Is<DecisionsForm470S>(x => x.ContributionsOrExpenditureOverOn == request.ContributionsOrExpenditureOverOn),
            Arg.Is<bool>(b => b))
            .Returns(decisionResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(42);
        _filerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _filingRepository.Create(Arg.Any<OfficeHolderCandidateSupplementForm>()).Returns(filing);

        _notificationSvc.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);
        // Act
        var result = await _service.CreateOfficeHolderCandidateSupplementFormFiling(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.StatusId, Is.EqualTo(5));
        });

        //await _notificationSvc.Received(1).SendDecisionsNotifications(decisionResponse.Notifications, filerUser);
    }

    [Test]
    public async Task CreateOfficeHolderCandidateSupplementFormFiling_WhenValidDraftStatus_SendsNotificationAndReturnsDto()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = new DateTime(2024, 04, 30, 12, 0, 0, 0),
            IsSubmission = false,
            FilerId = 123
        };

        var decisionsForm470S = new DecisionsForm470S { ContributionsOrExpenditureOverOn = _dateNow };
        var decisionResponse = new DecisionsForm470SResponse(new List<WorkFlowError>())
        {
            Notifications = new List<NotificationTrigger> { new(true, 5, null) }
        };

        var filerUser = new FilerUserDto();

        var filing = new OfficeHolderCandidateSupplementForm { Id = 10, StatusId = FilingStatus.Draft.Id };

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
            Arg.Is<DecisionsWorkflow>(w => w == DecisionsWorkflow.Form470SRuleSet),
            Arg.Is<DecisionsForm470S>(x => x.ContributionsOrExpenditureOverOn == request.ContributionsOrExpenditureOverOn),
            Arg.Is<bool>(b => b))
            .Returns(decisionResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(42);
        _filerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _filingRepository.Create(Arg.Any<OfficeHolderCandidateSupplementForm>()).Returns(filing);

        _notificationSvc.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);
        // Act
        var result = await _service.CreateOfficeHolderCandidateSupplementFormFiling(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.StatusId, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task CreateOfficeHolderCandidateSupplementFormFiling_WhenErrorsExist_DoesNotSendNotifications()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = _dateNow,
            IsSubmission = true,
            FilerId = 123
        };

        var decisionResponse = new DecisionsForm470SResponse(new List<WorkFlowError>())
        {
            Notifications = new List<NotificationTrigger> { new(false, null, null) }
        };

        var filerUser = new FilerUserDto { FilerId = 123 };
        var filing = new OfficeHolderCandidateSupplementForm { Id = 99, StatusId = 7 };

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
            Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsForm470S>(), Arg.Any<bool>())
            .Returns(decisionResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(55);
        _filerSvc.GetFilerUserByUserIdAsync(123, 55).Returns(filerUser);
        _filingRepository.Create(Arg.Any<OfficeHolderCandidateSupplementForm>()).Returns(filing);
        _notificationSvc.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);

        // Act
        var result = await _service.CreateOfficeHolderCandidateSupplementFormFiling(request);


        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(99));
        });
    }

    [Test]
    public void CreateOfficeHolderCandidateSupplementFormFiling_WhenFilerUserNotFound_ThrowsInvalidOperationException()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = _dateNow,
            IsSubmission = true,
            FilerId = 123
        };

        var decisionResponse = new DecisionsForm470SResponse(new List<WorkFlowError>());

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
             Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsForm470S>(), Arg.Any<bool>())
            .Returns(decisionResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(456);

        _filerSvc.GetFilerUserByUserIdAsync(123, 456)
            .Returns((FilerUserDto?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<NullReferenceException>(() =>
            _service.CreateOfficeHolderCandidateSupplementFormFiling(request));

        Assert.That(ex!.Message, Does.Contain("Object reference not set to an instance"));
    }

    #endregion

    #region UpdateOfficeHolderCandidateSupplementFormFiling - 470S

    [Test]
    public async Task UpdateOfficeHolderCandidateSupplementFormFiling_WhenDecisionHasErrors_ReturnsValidationResponse()
    {
        var filerUser = new FilerUserDto();
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = _dateNow.AddDays(2),
            IsSubmission = true,
            FilerId = 123,
            FilingId = 10
        };

        var errors = new List<WorkFlowError> { new("ContributionsOrExpenditureOverOn", "ErrGlobal0007", "Validation", "A date in the future is not valid") };
        var decisionResponse = new DecisionsForm470SResponse(errors);


        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
           Arg.Is<DecisionsWorkflow>(w => w == DecisionsWorkflow.Form470SRuleSet),
           Arg.Is<DecisionsForm470S>(x => x.ContributionsOrExpenditureOverOn == request.ContributionsOrExpenditureOverOn),
           Arg.Is<bool>(b => b))
           .Returns(decisionResponse);

        var officeHolderSupplementForm470S = new OfficeHolderCandidateSupplementForm { StatusId = FilingStatus.Pending.Id, FilingType = FilingType.OfficeHolderCandidateSupplement };

        _filingRepository.GetForm470SById(Arg.Any<long>()).Returns(officeHolderSupplementForm470S);
        _authorizationSvc.GetInitiatingUserId().Returns(42);
        _filerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);

        // Act
        var result = await _service.UpdateOfficeHolderCandidateSupplementFormFiling(request);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EqualTo(errors));
        });
    }

    [Test]
    public async Task UpdateOfficeHolderCandidateSupplementFormFiling_WhenValidApprovedStatus_SendsNotificationAndReturnsDto()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = new DateTime(2024, 04, 30, 12, 0, 0, 0),
            IsSubmission = true,
            FilerId = 123,
            FilingId = 10
        };

        var decisionsForm470S = new DecisionsForm470S { ContributionsOrExpenditureOverOn = _dateNow };
        var decisionResponse = new DecisionsForm470SResponse(new List<WorkFlowError>())
        {
            Notifications = new List<NotificationTrigger> { new(true, 5, null) }
        };

        var filerUser = new FilerUserDto();

        var filing = new OfficeHolderCandidateSupplementForm { Id = 10, StatusId = 5 };

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
            Arg.Is<DecisionsWorkflow>(w => w == DecisionsWorkflow.Form470SRuleSet),
            Arg.Is<DecisionsForm470S>(x => x.ContributionsOrExpenditureOverOn == request.ContributionsOrExpenditureOverOn),
            Arg.Is<bool>(b => b))
            .Returns(decisionResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(42);
        _filerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _filingRepository.GetForm470SById(Arg.Any<long>()).Returns(filing);
        _filingRepository.Update(Arg.Any<OfficeHolderCandidateSupplementForm>()).Returns(filing);

        _notificationSvc.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);
        // Act
        var result = await _service.UpdateOfficeHolderCandidateSupplementFormFiling(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.StatusId, Is.EqualTo(3));
        });

    }

    [Test]
    public async Task UpdateOfficeHolderCandidateSupplementFormFiling_WhenValidDraftStatus_SendsNotificationAndReturnsDto()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = new DateTime(2024, 04, 30, 12, 0, 0, 0),
            IsSubmission = false,
            FilerId = 123,
            FilingId = 10
        };

        var decisionsForm470S = new DecisionsForm470S { ContributionsOrExpenditureOverOn = _dateNow };
        var decisionResponse = new DecisionsForm470SResponse(new List<WorkFlowError>())
        {
            Notifications = new List<NotificationTrigger> { new(true, 5, null) }
        };

        var filerUser = new FilerUserDto();

        var filing = new OfficeHolderCandidateSupplementForm { Id = 10, StatusId = FilingStatus.Draft.Id };

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
            Arg.Is<DecisionsWorkflow>(w => w == DecisionsWorkflow.Form470SRuleSet),
            Arg.Is<DecisionsForm470S>(x => x.ContributionsOrExpenditureOverOn == request.ContributionsOrExpenditureOverOn),
            Arg.Is<bool>(b => b))
            .Returns(decisionResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(42);
        _filerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _filingRepository.GetForm470SById(Arg.Any<long>()).Returns(filing);
        _filingRepository.Update(Arg.Any<OfficeHolderCandidateSupplementForm>()).Returns(filing);

        _notificationSvc.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);
        // Act
        var result = await _service.UpdateOfficeHolderCandidateSupplementFormFiling(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.StatusId, Is.EqualTo(1));
        });

    }

    [Test]
    public async Task UpdateOfficeHolderCandidateSupplementFormFiling_DoesNotSendNotifications()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            ContributionsOrExpenditureOverOn = _dateNow,
            IsSubmission = true,
            FilerId = 123,
            FilingId = 99
        };

        var decisionResponse = new DecisionsForm470SResponse(new List<WorkFlowError>())
        {
            Notifications = new List<NotificationTrigger> { new(false, null, null) }
        };

        var filerUser = new FilerUserDto { FilerId = 123 };
        var filing = new OfficeHolderCandidateSupplementForm { Id = 99, StatusId = 7 };

        _decisionsSvc.InitiateWorkflow<DecisionsForm470S, DecisionsForm470SResponse>(
            Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsForm470S>(), Arg.Any<bool>())
            .Returns(decisionResponse);

        _authorizationSvc.GetInitiatingUserId().Returns(42);
        _filerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _filingRepository.GetForm470SById(Arg.Any<long>()).Returns(filing);
        _filingRepository.Update(Arg.Any<OfficeHolderCandidateSupplementForm>()).Returns(filing);
        _notificationSvc.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);

        // Act
        var result = await _service.UpdateOfficeHolderCandidateSupplementFormFiling(request);

        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(99));
        });
    }

    [Test]
    public void UpdateOfficeHolderCandidateSupplementFormFiling_WhenFilingNotFound_ThrowsInvalidOperationException()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            FilingId = 999,
            FilerId = 123,
            ContributionsOrExpenditureOverOn = _dateNow
        };

        _filingRepository.GetForm470SById(Arg.Any<long>()).Returns((OfficeHolderCandidateSupplementForm?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.UpdateOfficeHolderCandidateSupplementFormFiling(request));
        Assert.That(ex!.Message, Does.Contain("Unable to find filing record to update"));
    }

    [Test]
    public void UpdateOfficeHolderCandidateSupplementFormFiling_WhenFilerUserNotFound_ThrowsInvalidOperationException()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            FilingId = 10,
            FilerId = 123,
            ContributionsOrExpenditureOverOn = _dateNow
        };

        _filingRepository.GetForm470SById(Arg.Any<long>()).Returns(new OfficeHolderCandidateSupplementForm { StatusId = FilingStatus.Pending.Id });
        _authorizationSvc.GetInitiatingUserId().Returns(42);
        _filerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns((FilerUserDto?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.UpdateOfficeHolderCandidateSupplementFormFiling(request));
        Assert.That(ex!.Message, Does.Contain("No filer user found with ID"));
    }

    #endregion

    [Test]
    public async Task GetFilerIdOfLatestAcceptedCis_FilerFound_ShouldReturnId()
    {
        // Arrange
        var user = new BasicUserDto(1, "<EMAIL>", "Test", "Test");
        var filer = new Filer
        {
            Id = 100,
        };
        _userMaintenanceSvc.GetCurrentUser().Returns(user);
        _registrationRepository.GetFilerOfLatestAcceptedCisRegistration(Arg.Any<long>()).Returns(filer);
        // Act
        var result = await _service.GetFilerIdOfLatestAcceptedCis();
        // Assert
        Assert.That(result, Is.EqualTo(100));
    }
    [Test]
    public async Task GetFilerIdOfLatestAccepted501_NoFiler_ShouldReturn0()
    {
        // Arrange
        var user = new BasicUserDto(1, "<EMAIL>", "Test", "Test");
        _userMaintenanceSvc.GetCurrentUser().Returns(user);
        // Act
        var result = await _service.GetFilerIdOfLatestAcceptedCis();
        // Assert
        Assert.That(result, Is.EqualTo(0));
    }
    [Test]
    public async Task GetLatestRegistrationIdByFilerId_FilerFound_ShouldReturnId()
    {
        // Arrange
        var cis = new CandidateIntentionStatement()
        {
            StatusId = 1,
            Id = 100,
            FilerId = 1,
            Name = "Sri S",
        };
        var filer = new Filer
        {
            Id = 100,
        };
        _registrationRepository.GetLatestAcceptedCisRegistrationByFilerId(Arg.Any<long>()).Returns(cis);
        // Act
        var result = await _service.GetLatestRegistrationIdByFilerId(1);
        // Assert
        Assert.That(result, Is.EqualTo(100));
    }
    [Test]
    public async Task GetLatestRegistrationIdByFilerId1_NoCis_ShouldReturn0()
    {
        // Act
        var result = await _service.GetLatestRegistrationIdByFilerId(1);
        // Assert
        Assert.That(result, Is.EqualTo(0));
    }

    #region SubmitCandidateStatementSupplementForEfile

    [Test]
    public async Task SubmitCandidateStatementSupplementForEfile_ValidSubmission_CreatesFilingAndAttestationAndReturnsSuccess()
    {
        // Arrange
        var filing = new OfficeHolderCandidateSupplementForm
        {
            ContributionsOrExpenditureOverOn = DateTime.Parse("2025-05-18", CultureInfo.InvariantCulture),
            Id = 10,
            FilerId = 20,
            StatusId = FilingStatus.Draft.Id
        };

        var attestation = new CandidateCampaignStatementSupplementAttestation
        {
            FirstName = "John",
            LastName = "Doe",
            Role = "Candidate"
        };

        var submission = new CandidateStatementSupplementSubmissionDto
        {
            CandidateStatementSupplement = filing,
            Attestation = attestation
        };

        var decisionsResponse = new DecisionsForm470AttestationResponse(new List<WorkFlowError>());
        _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(
                DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true).Returns(decisionsResponse);

        _filingRepository.Create(filing).Returns(filing);
        _attestationRepository.Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>())
            .Returns(new Models.FilerRegistration.Registrations.Attestation { Id = 1 });
        _filingRepository.Update(filing).Returns(filing);

        // Act
        var result = await _service.SubmitCandidateSupplementForEfile(submission);

        // Assert
        await _filingRepository.Received(1).Create(filing);

        // dtb: look
        //await _attestationRepository.Received(1).Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>());

        await _filingRepository.Received(1).Update(filing);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            // dtb: look
            // Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));

            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task SubmitCandidateStatementSupplementForEfile_WithValidationErrors_DoesNotCreateFilingOrAttestation()
    {
        // Arrange
        var filing = new OfficeHolderCandidateSupplementForm
        {
            ContributionsOrExpenditureOverOn = DateTime.Parse("2025-05-18", CultureInfo.InvariantCulture),
            Id = 10,
            FilerId = 20,
            StatusId = FilingStatus.Draft.Id
        };

        var attestation = new CandidateCampaignStatementSupplementAttestation
        {
            FirstName = "John",
            LastName = "Doe",
            Role = "Candidate"
        };

        var submission = new CandidateStatementSupplementSubmissionDto
        {
            CandidateStatementSupplement = filing,
            Attestation = attestation
        };

        var errors = new List<WorkFlowError> { new("field", "type", "code", "message") };
        var decisionsResponse = new DecisionsForm470AttestationResponse(errors);
        _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(
                DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true).Returns(decisionsResponse);

        // Act
        var result = await _service.SubmitCandidateSupplementForEfile(submission);

        // Assert
        //await _filingRepository.DidNotReceive().Create(Arg.Any<Filing>());
        await _attestationRepository.DidNotReceive().Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>());
        //await _filingRepository.DidNotReceive().Update(Arg.Any<Filing>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusId, Is.EqualTo(filing.StatusId));
            Assert.That(result.Valid, Is.True); // The method always returns true for Valid
            //Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
    }

    [Test]
    public void SubmitCandidateStatementSupplementForEfile_ThrowsIfAttestationIsNull()
    {
        // Arrange
        var filing = new OfficeHolderCandidateSupplementForm
        {
            ContributionsOrExpenditureOverOn = DateTime.Parse("2025-05-18", CultureInfo.InvariantCulture),

            Id = 10,
            FilerId = 20,
            StatusId = FilingStatus.Draft.Id
        };

        var submission = new CandidateStatementSupplementSubmissionDto
        {
            CandidateStatementSupplement = filing,
            Attestation = null
        };

        // Act & Assert
        Assert.ThrowsAsync<InvalidOperationException>(async () =>
        {
            await _service.SubmitCandidateSupplementForEfile(submission);
        });
    }

    [Test]
    public void SubmitCandidateStatementSupplementForEfile_ThrowsIfCandidateStatementSupplementIsNull()
    {
        // Arrange
        var attestation = new CandidateCampaignStatementSupplementAttestation
        {
            FirstName = "John",
            LastName = "Doe",
            Role = "Candidate"
        };
        var submission = new CandidateStatementSupplementSubmissionDto
        {
            CandidateStatementSupplement = null,
            Attestation = attestation
        };

        // Act & Assert
        Assert.ThrowsAsync<NullReferenceException>(async () =>
        {
            await _service.SubmitCandidateSupplementForEfile(submission);
        });
    }

    #endregion
}
