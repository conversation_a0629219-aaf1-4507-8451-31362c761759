
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NSubstitute.ReturnsExtensions;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;

namespace SOS.CalAccess.Services.Business.Contracts.Tests.FilerRegistration.Filers;

/// <summary>
/// Unit tests for the <see cref="FilerSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(FilerSvc))]
public sealed class FilerSvcTest
{
    private IFilerRepository _filerRepositoryMock;
    private IFilerUserRepository _filerUserRepositoryMock;
    private IFilerLinkRepository _filerLinkRepositoryMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private FilerSvc _filerSvc;
    private DateTime _dateNow;

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _filerRepositoryMock = Substitute.For<IFilerRepository>();
        _filerUserRepositoryMock = Substitute.For<IFilerUserRepository>();
        _filerLinkRepositoryMock = Substitute.For<IFilerLinkRepository>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvc = new FilerSvc(
            _filerRepositoryMock,
            _filerUserRepositoryMock,
            _filerLinkRepositoryMock,
            _authorizationSvcMock
            );
    }

    /// <summary>
    /// Tests that the <see cref="FilerSvc.GetFiler(long)"/> method returns <c>null</c>
    /// when the provided <paramref name="filerId"/> does not exist in the repository.
    /// This test simulates a scenario where the filer ID is invalid and ensures that the method
    /// returns <c>null</> when no matching filer is found.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task GetFiler_InvalidId_ReturnsNull()
    {
        // Arrange
        var filerId = 999L; // Invalid filer ID
        _filerRepositoryMock.FindById(filerId).Returns(Task.FromResult<Filer?>(null));

        // Act
        var result = await _filerSvc.GetFiler(filerId);

        // Assert
        Assert.Null(result);
    }

    [Test]
    public async Task GetFilerDto_Valid_ReturnsDtoRecord()
    {
        var filer = new Filer
        {
            Id = 10,
            CurrentRegistrationId = 11,
            FilerStatusId = 12,
            FilerTypeId = 13,
            EffectiveDate = DateTime.UtcNow,
        };
        _filerRepositoryMock.FindById(Arg.Is<long>(1)).Returns(filer);
        var result = await _filerSvc.GetFilerDto(1);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.CurrentRegistrationId, Is.EqualTo(11));
            Assert.That(result.FilerStatusId, Is.EqualTo(12));
            Assert.That(result.FilerTypeId, Is.EqualTo(13));
            Assert.That(result.EffectiveDate, Is.InstanceOf<DateTime>());
        });
    }

    [Test]
    public void GetFilerDto_NoRecord_ThrowsException()
    {
        _filerRepositoryMock.FindById(Arg.Is<long>(1)).Throws(new KeyNotFoundException());
        Assert.ThrowsAsync<KeyNotFoundException>(() => _filerSvc.GetFilerDto(1));
    }

    /// <summary>
    /// Tests that the <see cref="FilerSvc.GetAllFilers"/> method returns an empty list when the repository has no filers.
    /// This test ensures that the method handles empty data scenarios correctly.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task GetAllFilers_ReturnsEmptyList_WhenNoFilersExist()
    {
        // Arrange
        var emptyFilersList = new List<Filer>();  // Empty list to simulate no filers in the repository
        _filerRepositoryMock.GetAll().Returns(Task.FromResult<IEnumerable<Filer>>(emptyFilersList));

        // Act
        var result = await _filerSvc.GetAllFilers();

        // Assert
        Assert.NotNull(result);  // Ensure the result is not null
        Assert.IsEmpty(result);  // Ensure the result is an empty collection
    }

    [Test]
    public async Task AddFilerAsync_ShouldCreateSuccessfully()
    {
        // Arrange
        var filerRequest = new Filer
        {
            Users = new List<FilerUser>
            {

            }
        };
        var expected = new Filer
        {
            Id = 1
        };

        _ = _filerRepositoryMock.Create(Arg.Any<Filer>()).Returns(Task.FromResult(expected));

        // Act
        var result = await _filerSvc.AddFilerAsync(filerRequest);

        // Asserts
        Assert.That(result, Is.EqualTo(expected.Id));
    }

    [Test]
    public async Task GetFilerLinkByFilerIdAndLinkTypeAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var filerId = 1;
        var filerLinkTypeId = 1;
        var expected = new FilerLink
        {
            Id = 1,
            FilerId = filerId,
            FilerLinkTypeId = filerLinkTypeId,
            LinkedEntityId = 1,
            CreatedBy = 1,
            EffectiveDate = _dateNow,
            ModifiedBy = 1,
        };

        _ = _filerLinkRepositoryMock.FindByFilerIdAndLinkType(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<FilerLink?>(expected));

        // Act
        var result = await _filerSvc.GetFilerLinkByFilerIdAndLinkTypeAsync(filerId, filerLinkTypeId);

        // Asserts
        Assert.That(result, Is.InstanceOf<FilerLink>());
        Assert.That(result.Id, Is.EqualTo(expected.Id));
    }

    [Test]
    public async Task GetFilerLinkByFilerIdAndLinkTypeAsync_NotFound_ShouldReturnNull()
    {
        // Arrange
        var filerId = 1;
        var filerLinkTypeId = 1;

        _ = _filerLinkRepositoryMock.FindByFilerIdAndLinkType(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();

        // Act
        var result = await _filerSvc.GetFilerLinkByFilerIdAndLinkTypeAsync(filerId, filerLinkTypeId);

        // Asserts
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task AddFilerLinkAsync_ValidRequest_ShouldCreateSuccessfully()
    {
        // Arrange
        var filerLink = new FilerLink
        {
            FilerId = 1,
            LinkedEntityId = 1,
            FilerLinkTypeId = 1,
            EffectiveDate = _dateNow,
            CreatedBy = 1,
            ModifiedBy = 0,
        };

        _ = _filerLinkRepositoryMock.Create(Arg.Any<FilerLink>());

        // Act
        await _filerSvc.AddFilerLinkAsync(filerLink);

        // Asserts
        await _filerLinkRepositoryMock.Received(1).Create(Arg.Any<FilerLink>());
        Assert.DoesNotThrowAsync(async () => await _filerSvc.AddFilerLinkAsync(filerLink));
    }

    [Test]
    public async Task UpdateFilerLinkAsync_ShouldUpdateSuccessfully()
    {
        // Arrange
        var filerLink = new FilerLink
        {
            Id = 1,
            FilerId = 1,
            LinkedEntityId = 1,
            FilerLinkTypeId = 1,
            EffectiveDate = _dateNow,
            CreatedBy = 1,
            ModifiedBy = 0,
        };

        _filerLinkRepositoryMock.Update(Arg.Any<FilerLink>()).Returns(Task.FromResult(filerLink));

        // Act
        await _filerSvc.UpdateFilerLinkAsync(filerLink);

        // Asserts
        await _filerLinkRepositoryMock.Received(1).Update(Arg.Any<FilerLink>());
        Assert.DoesNotThrowAsync(async () => await _filerSvc.UpdateFilerLinkAsync(filerLink));
    }

    [Test]
    public async Task UpdateFilerRoleAsync_ShouldUpdateSuccessfully()
    {
        // Arrange
        var filerUser = new FilerUser
        {
            Id = 1,
            FilerId = 1,
            UserId = 1,
            FilerRoleId = 1,
        };

        _ = _filerUserRepositoryMock.Update(Arg.Any<FilerUser>());
        _ = _filerUserRepositoryMock.FindById(Arg.Any<long>()).Returns(Task.FromResult<FilerUser?>(filerUser));

        // Act
        await _filerSvc.UpdateFilerUserRoleAsync(filerUser.Id, 2);

        // Asserts
        await _filerUserRepositoryMock.Received(1).Update(Arg.Any<FilerUser>());
        Assert.DoesNotThrowAsync(async () => await _filerSvc.UpdateFilerUserRoleAsync(filerUser.Id, 2));
    }

    [Test]
    public async Task GetFilerUserAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var filerId = 1;
        var expected = new FilerUser
        {
            Id = 1,
            FilerId = 1,
            UserId = 1,
            FilerRoleId = 1,
        };

        _filerUserRepositoryMock.FindFilerUserByFilerId(filerId).Returns(Task.FromResult<FilerUser?>(expected));

        // Act
        var result = await _filerSvc.GetFilerUserAsync(filerId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<FilerUserDto>());
            Assert.That(result!.Id, Is.EqualTo(expected.Id));
        });
    }

    [Test]
    public async Task GetFilerUserAsync_NotFound_ShouldReturnNull()
    {
        // Arrange
        var filerId = 1;
        _filerUserRepositoryMock.FindFilerUserByFilerId(filerId).ReturnsNull();

        // Act
        var result = await _filerSvc.GetFilerUserAsync(filerId);

        // Asserts
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetFilerUsersAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var filerId = 1;
        var expected = new List<FilerUser>
        {
            new ()
            {
                Id = 1,
                FilerId = 1,
                UserId = 1,
                FilerRoleId = 1,
            }
        };

        _filerUserRepositoryMock.FindFilerUsersByFilerId(filerId).Returns(Task.FromResult(expected));

        // Act
        var result = await _filerSvc.GetFilerUsersAsync(filerId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<FilerUserDto>>());
            Assert.That(result.Any(x => x.Id == 1), Is.True);
        });
    }

    [Test]
    public async Task GetFilerUsersAsync_NotFound_ShouldReturnNull()
    {
        // Arrange
        var filerId = 1;
        _filerUserRepositoryMock.FindFilerUsersByFilerId(filerId).Returns(Task.FromResult(new List<FilerUser>()));

        // Act
        var result = await _filerSvc.GetFilerUsersAsync(filerId);

        // Asserts
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetFilerUserByUserIdAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var filerId = 1;
        var userId = 1;
        var expected = new FilerUser
        {
            Id = 1,
            FilerId = 1,
            UserId = 1,
            FilerRoleId = 1,
        };

        _filerUserRepositoryMock.FindFilerUserByFilerIdAndUserId(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<FilerUser?>(expected));

        // Act
        var result = await _filerSvc.GetFilerUserByUserIdAsync(filerId, userId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<FilerUserDto>());
            Assert.That(result!.Id, Is.EqualTo(expected.Id));
        });
    }

    [Test]
    public async Task GetFilerUserByUserIdAsync_NotFound_ShouldReturnNull()
    {
        // Arrange
        var filerId = 1;
        var userId = 1;
        _filerUserRepositoryMock.FindFilerUserByFilerIdAndUserId(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();

        // Act
        var result = await _filerSvc.GetFilerUserByUserIdAsync(filerId, userId);

        // Asserts
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task AddFilerUserAsync_ValidRequest_ShouldCreateSuccessfully()
    {
        // Arrange
        var filerUser = new FilerUser
        {
            FilerId = 1,
            FilerRoleId = 1,
            UserId = 1,
        };

        _ = _filerUserRepositoryMock.Create(Arg.Any<FilerUser>()).Returns(Task.FromResult(filerUser));

        // Act
        await _filerSvc.AddFilerUserAsync(filerUser);

        // Asserts
        await _filerUserRepositoryMock.Received(1).Create(Arg.Any<FilerUser>());
        Assert.DoesNotThrowAsync(async () => await _filerSvc.AddFilerUserAsync(filerUser));
    }

    [Test]
    public async Task CurrentUserHasFilerRole_NoUserId_ShouldReturnFalse()
    {
        // Arrange
        // Act
        var result = await _filerSvc.CurrentUserHasFilerRole(1, 2);
        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CurrentUserHasFilerRole_NoFilerUser_ShouldReturnFalse()
    {
        // Arrange
        _authorizationSvcMock.GetInitiatingUserId().Returns(10);
        // Act
        var result = await _filerSvc.CurrentUserHasFilerRole(1, 2);
        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CurrentUserHasFilerRole_ValidNoMatch_ShouldReturnFalse()
    {
        // Arrange
        var filerUser = new FilerUser
        {
            FilerRoleId = 3
        };
        _authorizationSvcMock.GetInitiatingUserId().Returns(10);
        _filerUserRepositoryMock.FindFilerUserByFilerIdAndUserId(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        // Act
        var result = await _filerSvc.CurrentUserHasFilerRole(1, 2);
        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CurrentUserHasFilerRole_ValidMatch_ShouldReturnTrue()
    {
        // Arrange
        var filerUser = new FilerUser
        {
            FilerRoleId = 2
        };
        _authorizationSvcMock.GetInitiatingUserId().Returns(10);
        _filerUserRepositoryMock.FindFilerUserByFilerIdAndUserId(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        // Act
        var result = await _filerSvc.CurrentUserHasFilerRole(1, 2);
        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public async Task GetFilerRegistrationAsOfAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var filerId = 1L;
        var pointInTime = _dateNow;
        var expected = 1L;

        _filerRepositoryMock.FindFilerRegistrationAsOf(Arg.Any<long>(), Arg.Any<DateTime>()).Returns(Task.FromResult(expected));

        // Act
        var result = await _filerSvc.GetFilerRegistrationAsOfAsync(filerId, pointInTime);

        // Assert
        Assert.That(result, Is.EqualTo(expected));
    }
}
