// <copyright file="ContactsController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Lookups;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Authorization;
using SOS.CalAccess.Services.WebApi.Authorization.ThirdParty.Decisions;
using SOS.CalAccess.Services.WebApi.Shared;

namespace SOS.CalAccess.Services.WebApi.Contacts;

/// <summary>
/// API controller responsible for routing requests related to contacts.
/// </summary>
[ApiController]
[Route("api")]
public class ContactsController(
    IAuthorizationService authorization,
    IFilerContactSvc filerContactSvc,
    IDecisionsSvc decisionsSvc,
    IDateTimeSvc dateTimeSvc) : AuthorizationAwareControllerBase(authorization)
{
    private const string UnexpectedCommondMessage = "Unexpected command result";

    /// <summary>
    /// Creates a new contact for a filer.
    /// </summary>
    /// <param name="id">The id of the filer.</param>
    /// <param name="body">The contact details to create.</param>
    /// <param name="command">The command to execute the creation of the contact.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{id:long}/[controller]", Name = nameof(CreateContact))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<ActionResult<ContactItemResponse>> CreateContact(
        [FromRoute] long id,
        [FromBody] UpsertContactRequest body,
        [FromServices] ICreateContact command,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.ToFiler(id),
            RulesAllowAction.Create<FilerContact>(new { FilerId = id })]);

        var result = await command.Execute(body.IntoCreateContactCommand(id), cancellationToken);

        if (result.Unwrap(out var created, out var failure))
        {
            var n = failure as Failure<FilerContact>.NotFound
                    ?? throw new InvalidOperationException(UnexpectedCommondMessage);

            return NotFound(n);
        }

        return CreatedAtAction(nameof(GetContact), new { id = created.Id }, created.AsFilerContactItemResponse());
    }

    /// <summary>
    /// Creates a new payee for a filer.
    /// </summary>
    /// <param name="id">The id of the filer.</param>
    /// <param name="body">The contact details to create.</param>
    /// <param name="command">The command to execute the creation of the contact.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/Payee/{id:long}/[controller]", Name = nameof(CreatePayee))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<ActionResult<ContactItemResponse>> CreatePayee(
        [FromRoute] long id,
        [FromBody] UpsertPayeeRequest body,
        [FromServices] ICreatePayee command,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.ToFiler(id),
            RulesAllowAction.Create<FilerContact>(new { FilerId = id })]);

        WebApiHelper.NormalizeEmptyStringsToNull(body);

        DecisionServicePayee decisionsData = PopulateDecisionServicePayee(body);

        var decisionsResponse = await decisionsSvc.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(DecisionsWorkflow.PayeeRuleset, decisionsData, true);
        /*
         * Expected errors:
         * 
         * Fields:
         * If ContactType - Individual (Required)
         * FirstName, LastName
         * 
         * If ContactType - Organization (Required)
         * OrganizationName
         * 
         * Address
         * (Required)
         * Address1.Country, Address1.Street
         * 
         * If Country = US 
         * Address1.State, Address1.Zip, Address1.City
         * Street needed for City validation
         */
        if (decisionsResponse != null && decisionsResponse.Count > 0)
        {
            // Return 422 with validation errors
            return UnprocessableEntity(decisionsResponse);
        }

        var result = await command.Execute(body.IntoCreatePayeeCommand(id), cancellationToken);

        if (result.Unwrap(out var created, out var failure))
        {
            var n = failure as Failure<FilerContact>.NotFound
                    ?? throw new InvalidOperationException(UnexpectedCommondMessage);

            return NotFound(n);
        }

        return CreatedAtAction(nameof(GetContact), new { id = created.Id }, created.AsFilerContactItemResponse());
    }

    private static DecisionServicePayee PopulateDecisionServicePayee(UpsertPayeeRequest payee)
    {
        return payee switch
        {
            UpsertIndividualPayeeRequest individual => new DecisionServicePayee
            {
                FirstName = individual.FirstName,
                LastName = individual.LastName,
                MiddleName = individual.MiddleName,
                ContactType = FilerContactType.Individual,
                Address1 = new()
                {
                    Country = individual.Country,
                    Street = individual.Street,
                    Street2 = individual.Street2,
                    City = individual.City,
                    State = individual.State,
                    Zip = individual.ZipCode,
                }
            },
            UpsertOrganizationPayeeRequest organization => new DecisionServicePayee
            {
                OrganizationName = organization.OrganizationName,
                ContactType = FilerContactType.Organization,
                Address1 = new()
                {
                    Country = organization.Country,
                    Street = organization.Street,
                    Street2 = organization.Street2,
                    City = organization.City,
                    State = organization.State,
                    Zip = organization.ZipCode
                }
            },
            _ => throw new NotSupportedException($"Unsupported payee type: {payee.GetType().Name}")
        };
    }

    /// <summary>
    /// Retrieves a single contact by id.
    /// </summary>
    /// <param name="id">The id of the contact to retrieve.</param>
    /// <param name="query">The query handler to execute the retrieval.</param>
    /// <param name="auditService">High-level audit facilities.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/{id:long}", Name = nameof(GetContact))]
    [AllowAnonymous]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<ContactItemResponse>> GetContact(
        [FromRoute] long id,
        [FromServices] IGetContact query,
        [FromServices] IAuditService auditService,
        CancellationToken cancellationToken = default)
    {
        var result = await query.Execute(new IGetContact.ById(id), cancellationToken);

        await auditService.LogAction(BusinessAction.Read<FilerContact>(id.ToString(), dateTimeSvc), cancellationToken);

        return result is not null ? result.AsFilerContactItemResponse() : NotFound();
    }

    /// <summary>
    /// Retrieves a list of contacts given a filer id.
    /// </summary>
    /// <param name="id">The id of the filer.</param>
    /// <param name="query">The query handler to execute the retrieval.</param>
    /// <param name="auditService">High-level audit facilities.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("Filers/{id:long}/[controller]", Name = nameof(GetContacts))]
    [AllowAnonymous]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<IEnumerable<ContactItemResponse>>> GetContacts(
        [FromRoute] long id,
        [FromServices] IGetAllContacts query,
        [FromServices] IAuditService auditService,
        CancellationToken cancellationToken = default)
    {
        var result = await query.Execute(new IGetAllContacts.ByFilerId(id), cancellationToken);

        await auditService.LogAction(BusinessAction.ReadAll<Filer, FilerContact>(id.ToString(), dateTimeSvc), cancellationToken);

        if (result.Unwrap(out var contacts, out var failure))
        {
            var n = failure as Failure<IReadOnlyList<FilerContact>>.NotFound
                    ?? throw new InvalidOperationException("Unexpected query result");

            return NotFound(n);
        }

        return Ok(contacts.Select(c => c.AsFilerContactItemResponse()));
    }

    /// <summary>
    /// Updates a contact by id.
    /// </summary>
    /// <param name="id">The id of the contact to update.</param>
    /// <param name="body">The contact details to update.</param>
    /// <param name="handler">The command handler to execute the update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPut("[controller]/{id:long}", Name = nameof(UpdateContact))]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Edit))]
    public async Task<ActionResult> UpdateContact(
        [FromRoute] long id,
        [FromBody] UpsertContactRequest body,
        [FromServices] IUpdateContact handler,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.To<FilerContact>(id),
            RulesAllowAction.Update<FilerContact>(new { Id = id })]);

        var command = body.IntoUpdateContactCommand(id);

        return await handler.Execute(command, cancellationToken) switch
        {
            Success<FilerContact> => NoContent(),
            Failure<FilerContact>.NotFound n => NotFound(n),
            Failure<FilerContact>.InvalidState c => Conflict(c),
            _ => throw new InvalidOperationException(UnexpectedCommondMessage),
        };
    }

    /// <summary>
    /// Gets Filer Contact by Id
    /// </summary>
    /// <param name="id">Filer Contact Id</param>
    /// <returns></returns>
    [HttpGet(IFilerContactSvc.GetFilerContactByIdPath, Name = nameof(GetFilerContactById))]
    public async Task<ContactResponseDto?> GetFilerContactById([FromRoute] long id)
    {
        return await filerContactSvc.GetFilerContactById(id);
    }

    /// <summary>
    /// Retrieves a list lobbying coalitions.
    /// </summary>
    /// <param name="search">The id or name of the Lobbying Coalition to search for.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/SearchLobbyingCoalitionByNameOrId", Name = nameof(SearchLobbyingCoalitionByNameOrId))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<IEnumerable<ContactSearchResultDto>> SearchLobbyingCoalitionByNameOrId([FromServices] IFilerContactSvc filerContactSvc, [FromQuery] string query, [FromQuery] long filerId)
    {
        return await filerContactSvc.SearchLobbyingCoalitionsByNameOrId(query, filerId);
    }

    /// <summary>
    ///  Retrieves a list lobbying firms.
    /// </summary>
    /// <param name="query">The id or name of the Lobbying Firm to search for.</param>
    /// <param name="filerId">The id of the filer.</param>
    /// <returns></returns>
    [HttpGet(IFilerContactSvc.SearchLobbyingFirmsByNameOrIdPath, Name = nameof(SearchLobbyingFirmsByNameOrId))]
    public async Task<IEnumerable<ContactSearchResultDto>> SearchLobbyingFirmsByNameOrId([FromQuery] string query, [FromQuery] long filerId)
    {
        return await filerContactSvc.SearchLobbyingFirmsByNameOrId(query, filerId);
    }

    /// <summary>
    /// Retrieves a list of filer contact.
    /// </summary>
    /// <param name="search">The id or name of the Contact to search for.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/SearchContactsByNameOrId", Name = nameof(SearchContactsByNameOrId))]
    [AllowAnonymous]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<IEnumerable<ContactSearchResultDto>> SearchContactsByNameOrId(
        [FromServices] IFilerContactSvc filerContactSvc,
        [FromQuery] long filerId,
        [FromQuery] string query,
        [FromQuery] List<string> contactTypes)
    {
        return await filerContactSvc.SearchFilerContactsByNameOrId(filerId, query, contactTypes);
    }

    /// <summary>
    /// Creates a new Filer contact.
    /// </summary>
    /// <param name="id">The id of the filer.</param>
    /// <param name="body">The contact details to create.</param>
    /// <param name="command">The command to execute the creation of the contact.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{id:long}/[controller]/FilerContact", Name = nameof(CreateFilerContact))]
    [AllowAnonymous]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<ActionResult<ContactItemResponse>> CreateFilerContact(
        [FromRoute] long id,
        [FromBody] UpsertFilerContactRequest body,
        [FromServices] ICreateFilerContact command,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.ToFiler(id),
            RulesAllowAction.Create<FilerContact>(new { FilerId = id })]);


        var decisionsResponse = await ValidateCreateFilerContact(body);

        if (decisionsResponse.Count > 0)
        {
            return CreatedAtAction(nameof(CreateFilerContact), new { Valid = false, ValidationErrors = decisionsResponse });
        }

        var result = await command.Execute(body.IntoCreateFilerContactCommand(id), cancellationToken);

        if (result.Unwrap(out var created, out var failure))
        {
            var n = failure as Failure<FilerContact>.NotFound
                    ?? throw new InvalidOperationException(UnexpectedCommondMessage);

            return NotFound(n);
        }
        return CreatedAtAction(nameof(CreateFilerContact), new { id = created.Id, Valid = true, ValidationErrors = new List<WorkFlowError>() }, created.AsFilerContactItemResponse());
    }

    /// <summary>
    /// Update a filer contact by filer contact id.
    /// </summary>
    /// <param name="id">The id of the filer contact.</param>
    /// <param name="body">The contact details to create.</param>
    /// <param name="command">The command to execute the creation of the contact.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(typeof(ContactItemResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ContactItemResponse), StatusCodes.Status422UnprocessableEntity)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("Filers/{id:long}/[controller]/FilerContact", Name = nameof(UpdateFilerContact))]
    [AllowAnonymous]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Edit))]
    public async Task<ActionResult<ContactItemResponse>> UpdateFilerContact(
        [FromRoute] long id,
        [FromBody] UpsertFilerContactRequest body,
        [FromServices] IUpdateFilerContact command,
        CancellationToken cancellationToken = default)
    {
        var decisionsResponse = await ValidateUpsertFilerContact(body);

        if (decisionsResponse.Count > 0)
        {
            return UnprocessableEntity(decisionsResponse);
        }

        var result = await command.Execute(body.IntoUpdateFilerContactCommand(id), cancellationToken);

        if (result.Unwrap(out var updated, out var failure))
        {
            var n = failure as Failure<FilerContact>.NotFound
                    ?? throw new InvalidOperationException(UnexpectedCommondMessage);

            return NotFound(n);
        }

        return CreatedAtAction(nameof(GetContact), new { id = updated.Id }, updated.AsFilerContactItemResponse());
    }

    [HttpGet]
    public async Task<List<WorkFlowError>> ValidateCreateFilerContact(UpsertFilerContactRequest body)
    {
        var address = body.Addresses.FirstOrDefault();

        switch (body.FilerContactForm)
        {
            case "PaymentToLobbyingFirm":
                var firmDecisionsData = new PaymentMadeToLobbyingFirmContactDs
                {
                    Address1 = new PaymentMadeToLobbyingFirmContactAddress
                    {
                        Country = address?.Country,
                        Street = address?.Street,
                        Street2 = address?.Street2,
                        City = address?.City,
                        State = address?.State,
                        Zip = address?.Zip
                    },
                };

                if (body is UpsertOrganizationFilerContactRequest orgRequest)
                {
                    firmDecisionsData.LobbyingFirmName = orgRequest.OrganizationName;
                }

                return await decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingFirmContactDs, List<WorkFlowError>>(
                    DecisionsWorkflow.PaymentMadeToLobbyingFirmsContactRuleset, firmDecisionsData, true);

            case "PaymentReceiveLobbyingCoalition":
                var decisionsData = PopulateDecisionPaymentReceiveLobbyingCoalitionMember(body);

                return await decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionMemberDs, List<WorkFlowError>>(
                    DecisionsWorkflow.PaymentReceiveLobbyingCoalitionMemberRuleset, decisionsData, true);

            case "UpsertPayee":
                DecisionServicePayee payeeDecisionsData = PopulateDecisionServicePayeeForFilerContact(body);
                return await decisionsSvc.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(DecisionsWorkflow.PayeeRuleset, payeeDecisionsData, true);

            case "EndOfSession":
                EndOfSessionLobbyingContactDs endOfSessionDecisionsData = PopulateEndOfSessionLobbyingContact(body);
                return await decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingContactDs, List<WorkFlowError>>(DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransaction, endOfSessionDecisionsData, true);

            case "UpsertPayor":
                DecisionServiceTransactor payorDecisionsData = PopulateDecisionServiceTransactorForFilerContact(body);
                return await decisionsSvc.InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(DecisionsWorkflow.TransactionPayorInformationRuleSet, payorDecisionsData, true);
            case "PayeeInformation":
                DecisionServiceTransactor payeeInformationDecisionsData = PopulateDecisionServiceTransactorForFilerContact(body);
                return await decisionsSvc.InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(DecisionsWorkflow.TransactionPayeeInformationRuleSet, payeeInformationDecisionsData, true);
            default:
                return new List<WorkFlowError>();
        }
    }

    private async Task<List<WorkFlowError>> ValidateUpsertFilerContact(UpsertFilerContactRequest body)
    {
        switch (body.FilerContactForm)
        {
            case "UpsertPayee":
                WebApiHelper.NormalizeEmptyStringsToNull(body);
                DecisionServicePayee decisionsData = PopulateDecisionServicePayeeForFilerContact(body);
                return await decisionsSvc.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(DecisionsWorkflow.PayeeRuleset, decisionsData, true);
            case "UpsertPayor":
                DecisionServiceTransactor payorDecisionsData = PopulateDecisionServiceTransactorForFilerContact(body);
                return await decisionsSvc.InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(DecisionsWorkflow.TransactionPayorInformationRuleSet, payorDecisionsData, true);
            case "PayeeInformation":
                DecisionServiceTransactor payeeInformationDecisionsData = PopulateDecisionServiceTransactorForFilerContact(body);
                return await decisionsSvc.InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(DecisionsWorkflow.TransactionPayeeInformationRuleSet, payeeInformationDecisionsData, true);
            case "EndOfSession":
                WebApiHelper.NormalizeEmptyStringsToNull(body);
                EndOfSessionLobbyingContactDs endOfSessionDecisionsData = PopulateEndOfSessionLobbyingContact(body);
                return await decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingContactDs, List<WorkFlowError>>(DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransaction, endOfSessionDecisionsData, true);
            case "PaymentReceiveLobbyingCoalition":
                PaymentReceiveLobbyingCoalitionMemberDs paymentReceiveLobbyingCoalitionMemberDs = PopulateDecisionPaymentReceiveLobbyingCoalitionMember(body);
                return await decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionMemberDs, List<WorkFlowError>>(
                    DecisionsWorkflow.PaymentReceiveLobbyingCoalitionMemberRuleset, paymentReceiveLobbyingCoalitionMemberDs, true);
            default:
                break;
        }
        return [];
    }

    private static DecisionServicePayee PopulateDecisionServicePayeeForFilerContact(UpsertFilerContactRequest payee)
    {
        var address = payee.Addresses[0];
        return payee switch
        {
            UpsertIndividualFilerContactRequest individual => new DecisionServicePayee
            {
                FirstName = individual.FirstName,
                LastName = individual.LastName,
                MiddleName = individual.MiddleName,
                ContactType = FilerContactType.Individual,
                Address1 = new()
                {
                    Country = address.Country,
                    Street = address.Street,
                    Street2 = address.Street2,
                    City = address.City,
                    State = address.State,
                    Zip = address.Zip,
                }
            },
            UpsertOrganizationFilerContactRequest organization => new DecisionServicePayee
            {
                OrganizationName = organization.OrganizationName,
                ContactType = FilerContactType.Organization,
                Address1 = new()
                {
                    Country = address.Country,
                    Street = address.Street,
                    Street2 = address.Street2,
                    City = address.City,
                    State = address.State,
                    Zip = address.Zip
                }
            },
            _ => throw new NotSupportedException($"Unsupported payee type: {payee.GetType().Name}")
        };
    }

    private static EndOfSessionLobbyingContactDs PopulateEndOfSessionLobbyingContact(UpsertFilerContactRequest request)
    {
        if (request is not UpsertOrganizationFilerContactRequest organizationContact)
        {
            throw new NotSupportedException($"Unsupported payee type: {request.GetType().Name}");
        }

        var address = organizationContact.Addresses.FirstOrDefault();
        var phone = organizationContact.PhoneNumbers.FirstOrDefault();
        return new EndOfSessionLobbyingContactDs
        {
            LobbyingFirmName = organizationContact.OrganizationName,
            PhoneNumber = phone is not null ? FormatPhoneNumberForValidation(phone) : null,
            Address1 = new EndOfSessionLobbyingAddress
            {
                Country = address?.Country,
                Street = address?.Street,
                Street2 = address?.Street2,
                City = address?.City,
                State = address?.State,
                Zip = address?.Zip,
            }
        };
    }

    private static DecisionServiceTransactor PopulateDecisionServiceTransactorForFilerContact(UpsertFilerContactRequest transactor)
    {
        var address = transactor.Addresses?.FirstOrDefault();

        var mappedAddress = address == null
            ? null
            : new DecisionServiceAddress
            {
                Country = address.Country,
                Street = address.Street,
                Street2 = address.Street2,
                City = address.City,
                State = address.State,
                Zip = address.Zip
            };

        // Determine if dto is populating a Payor or a Payee
        bool isPayor = transactor.FilerContactForm == DisclosureConstants.Transactions.PayorFilerContactForm;
        bool isPayee = transactor.FilerContactForm == DisclosureConstants.Transactions.PayeeFilerContactForm;

        var type = transactor switch
        {
            UpsertIndividualFilerContactRequest => FilerContactType.Individual,
            UpsertOrganizationFilerContactRequest => FilerContactType.Organization,
            UpsertFilerCommitteeFilerContactRequest => FilerContactType.Filer,
            UpsertCandidateFilerContactRequest => FilerContactType.Candidate,
            _ => throw new NotSupportedException($"Unsupported contact type: {transactor.GetType().Name}")
        };

        var dto = transactor switch
        {
            UpsertIndividualFilerContactRequest individual => new DecisionServiceTransactor
            {
                FirstName = individual.FirstName,
                LastName = individual.LastName,
                MiddleName = individual.MiddleName,
                Address = mappedAddress,
                Employer = individual.Employer,
            },
            UpsertOrganizationFilerContactRequest organization => new DecisionServiceTransactor
            {
                OrganizationName = organization.OrganizationName,
                Address = mappedAddress,
            },
            UpsertFilerCommitteeFilerContactRequest committee => new DecisionServiceTransactor
            {
                CommitteeName = committee.CommitteeName,
                CommitteeId = committee.CommitteeId,
                Address = mappedAddress,
            },
            UpsertCandidateFilerContactRequest candidate => new DecisionServiceTransactor
            {
                FirstName = candidate.FirstName,
                LastName = candidate.LastName,
                MiddleName = candidate.MiddleName,
                OfficeSought = candidate.OfficeSought,
                JurisdictionName = candidate.Jurisdiction,
                District = candidate.District,
                Address = mappedAddress,
            },
            _ => throw new NotSupportedException($"Unsupported contact type: {transactor.GetType().Name}")
        };

        if (isPayor)
        {
            dto.PayorType = type;
        }
        else if (isPayee)
        {
            dto.PayeeType = type;
        }

        return dto;
    }

    private static PaymentReceiveLobbyingCoalitionMemberDs PopulateDecisionPaymentReceiveLobbyingCoalitionMember(
        UpsertFilerContactRequest body)
    {
        var address = body.Addresses.FirstOrDefault();
        var decisionsData = new PaymentReceiveLobbyingCoalitionMemberDs
        {
            Address = new PaymentReceiveLobbyingCoalitionMemberAddress
            {
                Country = address?.Country,
                Street = address?.Street,
                Street2 = address?.Street2,
                City = address?.City,
                State = address?.State,
                Zip = address?.Zip
            },
            Email = body.EmailAddresses.FirstOrDefault()?.Email
        };

        switch (body)
        {
            case UpsertIndividualFilerContactRequest individual:
                decisionsData.ContactFirstName = individual.FirstName;
                decisionsData.ContactLastName = individual.LastName;
                decisionsData.ContactType = FilerContactType.Individual;
                break;
            case UpsertOrganizationFilerContactRequest organization:
                decisionsData.OrganizationName = organization.OrganizationName;
                decisionsData.ContactType = FilerContactType.Organization;
                break;
        }

        return decisionsData;
    }

    // TODO: refactor once we align country code and number in db to team standard
    private static string FormatPhoneNumberForValidation(PhoneNumberDto phoneNumber)
    {
        var countryCode = phoneNumber?.CountryCode?.Split('-')[0];
        var number = phoneNumber?.Number;

        return (countryCode ?? "") + (number ?? "");
    }
}
