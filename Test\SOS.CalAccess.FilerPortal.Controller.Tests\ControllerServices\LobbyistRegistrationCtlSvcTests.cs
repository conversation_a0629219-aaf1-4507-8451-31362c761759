using System.Globalization;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Moq;
using NSubstitute;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[TestFixture]
public class LobbyistRegistrationCtlSvcTests
{
    private Mock<ILobbyistRegistrationSvc> _lobbyistRegistrationSvcMock;
    private Mock<IUserMaintenanceSvc> _userMaintenanceSvcMock;
    private Mock<IDecisionsValidationMapService> _decisionsValidationMapServiceMock;
    private Mock<ISmoCampaignStatementSvc> _smoCampaignStatementSvcMock;
    private IDateTimeSvc _dateTimeSvc;
    private LobbyistRegistrationCtlSvc _svc;

    [SetUp]
    public void SetUp()
    {
        _lobbyistRegistrationSvcMock = new Mock<ILobbyistRegistrationSvc>();
        _userMaintenanceSvcMock = new Mock<IUserMaintenanceSvc>();
        _decisionsValidationMapServiceMock = new Mock<IDecisionsValidationMapService>();
        _smoCampaignStatementSvcMock = new Mock<ISmoCampaignStatementSvc>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _svc = new LobbyistRegistrationCtlSvc(_lobbyistRegistrationSvcMock.Object, _userMaintenanceSvcMock.Object, _decisionsValidationMapServiceMock.Object, _smoCampaignStatementSvcMock.Object, _dateTimeSvc);
    }

    [Test]
    public async Task GetPage03ViewModel_WhenSelfRegisterFalse_ShouldReturnDefaultModel()
    {
        LobbyistRegistrationStep01ViewModel result = await _svc.GetPage03ViewModel(null, false, default);

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Addresses, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(result.Addresses[0].Purpose, Is.EqualTo("Business"));
            Assert.That(result.Addresses[1].Purpose, Is.EqualTo("Mailing"));
        });
    }

    [Test]
    public async Task GetPage03ViewModel_WhenSelfRegisterTrue_ShouldReturnModel()
    {
        var id = 1;
        var firstName = "Sample";
        var lastName = "Samplelastname";
        var email = "<EMAIL>";

        var userResponse = new Services.Business.UserAccountMaintenance.Models.BasicUserDto(id, email, firstName, lastName);

        _ = _userMaintenanceSvcMock
            .Setup(x => x.GetCurrentUser())
            .ReturnsAsync(userResponse);

        LobbyistRegistrationStep01ViewModel result = await _svc.GetPage03ViewModel(null, true, default);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Addresses, Has.Count.EqualTo(2));
            Assert.That(result.Addresses[0].Purpose, Is.EqualTo("Business"));
            Assert.That(result.Addresses[1].Purpose, Is.EqualTo("Mailing"));
            Assert.That(result.FirstName, Is.EqualTo(firstName));
            Assert.That(result.LastName, Is.EqualTo(lastName));
            Assert.That(result.Email, Is.EqualTo(email));
            Assert.That(result.SelfRegister, Is.EqualTo(true));
        });
    }

    [Test]
    public async Task GetPage07ViewModel_WhenSelfRegisterFalse_ShouldReturnDefaultModel()
    {
        int id = 1;
        string email = "<EMAIL>";
        bool selfRegister = false;
        LobbyistResponseDto model = new()
        {
            Email = email,
            SelfRegister = selfRegister,
        };

        _ = _lobbyistRegistrationSvcMock
                .Setup(x => x.GetLobbyistRegistration(It.IsAny<long>()))
                .ReturnsAsync(model);

        LobbyistRegistrationStep03ViewModel result = await _svc.GetPage07ViewModel(id, default);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Email, Is.EqualTo(email));
            Assert.That(result.SelfRegister, Is.EqualTo(selfRegister));
        });
    }

    [Test]
    public void Page08GetViewModel_WithId_ShouldReturnModel()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _dateTimeSvc.GetCurrentDateTime().Returns(new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local));
        var before = date;
        long id = 1;

        FilerPortal.Models.Registrations.ConfirmationViewModel result = _svc.Page08GetViewModel(id);

        var after = date;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.IsSubmission, Is.EqualTo(true));
            Assert.That(result.ExecutedOn, Is.GreaterThanOrEqualTo(before).And.LessThanOrEqualTo(after));
        });
    }

    [Test]
    public void Page08GetViewModel_WithoutId_ShouldThrowError()
    {
        _ = Assert.Throws<KeyNotFoundException>(() =>
        {
            _ = _svc.Page08GetViewModel(null);
        });
    }

    [Test]
    public void MapLobbyistViewModelToRequest_ShouldReturnMappedDto()
    {
        // Arrange
        LobbyistRegistrationStep01ViewModel model = new()
        {
            FirstName = "Jane",
            MiddleName = "L",
            LastName = "Smith",
            Email = "<EMAIL>",
            SelfRegister = true,
            PhoneNumber = "7654321",
            PhoneNumberCountryCode = "+44",
            FaxNumber = "1234567",
            FaxNumberCountryCode = "+1",
            LobbyistEmployerOrLobbyingFirmId = 123,
            IsSameAsBusinessAddress = false,
            LegislativeSessionId = 456,
            QualificationDate = new DateTime(2023, 5, 15, 0, 0, 0, DateTimeKind.Local),
            PlacementAgent = true,
            EthicsCourseCompleted = true,
            EthicsCourseCompletedOn = new DateTime(2023, 4, 10, 0, 0, 0, DateTimeKind.Local),
            LobbyOnlySpecifiedAgencies = true,
            SelectedAgencies = [1],
            IsLobbyingStateLegislature = true,
            Addresses =
            [
                new() { Purpose = "Business", Country = "USA", City = "Sacramento", State = "CA", Street = "123 Capitol Ave" },
                new() { Purpose = "Mailing", Country = "USA", City = "San Francisco", State = "CA", Street = "456 Market St" }
            ]
        };

        // Act
        LobbyistRegistrationRequestDto dto = _svc.MapLobbyistViewModelToRequest(model);

        // Assert
        Assert.Multiple(() =>
        {
            // Personal information
            Assert.That(dto.FirstName, Is.EqualTo("Jane"));
            Assert.That(dto.MiddleName, Is.EqualTo("L"));
            Assert.That(dto.LastName, Is.EqualTo("Smith"));
            Assert.That(dto.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(dto.SelfRegister, Is.EqualTo(true));

            // Contact information
            Assert.That(dto.PhoneNumber?.CountryCode, Is.EqualTo("+44"));
            Assert.That(dto.PhoneNumber?.Number, Is.EqualTo("7654321"));
            Assert.That(dto.FaxNumber?.CountryCode, Is.EqualTo("+1"));
            Assert.That(dto.FaxNumber?.Number, Is.EqualTo("1234567"));

            // Employer/Firm
            Assert.That(dto.LobbyistEmployerOrLobbyingFirmId, Is.EqualTo(123));

            // Addresses
            Assert.That(dto.BusinessAddress, Is.Not.Null);
            Assert.That(dto.BusinessAddress!.Street, Is.EqualTo("123 Capitol Ave"));
            Assert.That(dto.BusinessAddress.City, Is.EqualTo("Sacramento"));
            Assert.That(dto.BusinessAddress.State, Is.EqualTo("CA"));
            Assert.That(dto.BusinessAddress.Country, Is.EqualTo("USA"));

            Assert.That(dto.MailingAddress, Is.Not.Null);
            Assert.That(dto.MailingAddress!.Street, Is.EqualTo("456 Market St"));
            Assert.That(dto.MailingAddress.City, Is.EqualTo("San Francisco"));
            Assert.That(dto.MailingAddress.State, Is.EqualTo("CA"));
            Assert.That(dto.MailingAddress.Country, Is.EqualTo("USA"));

            // Session and qualification
            Assert.That(dto.LegislativeSessionId, Is.EqualTo(456));
            Assert.That(dto.DateOfQualification, Is.EqualTo(new DateTime(2023, 5, 15, 0, 0, 0, DateTimeKind.Local)));

            // Additional information
            Assert.That(dto.IsPlacementAgent, Is.True);
            Assert.That(dto.CompletedEthicsCourse, Is.True);
            Assert.That(dto.CompletedCourseDate, Is.EqualTo(new DateTime(2023, 4, 10, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(dto.LobbyOnlySpecifiedAgencies, Is.True);
            Assert.That(dto.IsLobbyingStateLegislature, Is.True);

            // Agency information
            Assert.That(dto.Agencies, Is.Not.Null);
            Assert.That(dto.Agencies, Has.Count.EqualTo(1));
            Assert.That(dto.Agencies![0].AgencyId, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task SubmitLobbyistRegistrationForm_ShouldDefaultSelfRegisterAndReturnResponse()
    {
        var model = new LobbyistRegistrationStep01ViewModel { Id = 0 };
        var dto = new LobbyistRegistrationRequestDto();
        var modelState = new ModelStateDictionary();

        _ = _lobbyistRegistrationSvcMock
            .Setup(x => x.CreateLobbyistRegistrationPage03(It.IsAny<LobbyistRegistrationRequestDto>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = true });

        RegistrationResponseDto result = await _svc.SubmitLobbyistRegistrationForm(model, modelState, dto);

        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public async Task SubmitLobbyistRegistrationForm_WhenResponseIsInvalid_ShouldApplyErrorsToModelState()
    {
        // Arrange
        LobbyistRegistrationStep01ViewModel model = new()
        {
            Id = 42,
            Addresses =
            [
                new() { Purpose = "Business" },
                new() { Purpose = "Mailing" }
            ]
        };
        ModelStateDictionary modelState = new();
        LobbyistRegistrationRequestDto request = new();

        List<WorkFlowError> validationErrors =
        [
            new("SomeField", "SomeErrorCode", "ValidationError", "Test error")
        ];

        RegistrationResponseDto response = new(42, false, validationErrors);

        _ = _lobbyistRegistrationSvcMock
            .Setup(x => x.UpdateLobbyistRegistration(42, request))
            .ReturnsAsync(response);

        _decisionsValidationMapServiceMock
            .Setup(x => x.ApplyErrorsToModelState(
                It.IsAny<Dictionary<string, FieldProperty>>(),
                validationErrors,
                modelState))
            .Verifiable();

        // Act
        RegistrationResponseDto result = await _svc.SubmitLobbyistRegistrationForm(model, modelState, request);

        // Assert
        Assert.That(result, Is.EqualTo(response));
        _decisionsValidationMapServiceMock.Verify(
            x => x.ApplyErrorsToModelState(
                It.IsAny<Dictionary<string, FieldProperty>>(),
                validationErrors,
                modelState),
            Times.Once);
    }

    [Test]
    public async Task UpdateLobbyistRegistrationForm_ShouldReturnResponse()
    {
        LobbyistRegistrationStep01ViewModel model = new()
        {
            Id = 1
        };
        LobbyistRegistrationRequestDto dto = new();
        ModelStateDictionary modelState = new();
        _ = _lobbyistRegistrationSvcMock
            .Setup(x => x.UpdateLobbyistRegistration(It.IsAny<long>(), It.IsAny<LobbyistRegistrationRequestDto>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77 });
        RegistrationResponseDto result = await _svc.SubmitLobbyistRegistrationForm(model, modelState, dto);
        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public async Task SubmitLobbyistRegistration_ShouldReturnResponse()
    {
        var model = new LobbyistRegistrationStep03ViewModel { Id = 0 };

        _ = _lobbyistRegistrationSvcMock
            .Setup(x => x.SubmitLobbyistRegistration(It.IsAny<long>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = true });

        RegistrationResponseDto result = await _svc.SubmitLobbyistRegistration(model);

        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(77));
            Assert.That(result.Valid, Is.EqualTo(true));
        });
    }

    [Test]
    public async Task Page03Submit_ShouldMapModelAndReturnId()
    {
        LobbyistRegistrationStep01ViewModel model = new()
        {
            Id = 0,
            FirstName = "Test",
            LastName = "User",
            Email = "<EMAIL>",
            FaxNumber = "1234",
            FaxNumberCountryCode = "1-",
            PhoneNumber = "5678",
            PhoneNumberCountryCode = "2-",
            Addresses =
            [
                new() { Purpose = "Business", Country = "USA" },
                new() { Purpose = "Mailing", Country = "USA" }
            ]
        };
        ModelStateDictionary modelState = new();

        _ = _lobbyistRegistrationSvcMock
            .Setup(svc => svc.CreateLobbyistRegistrationPage03(It.IsAny<LobbyistRegistrationRequestDto>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 88, Valid = true });

        long? result = await _svc.Page03Submit(model, modelState, true);

        Assert.That(result, Is.EqualTo(88));
    }

    [Test]
    public void MapLobbyistViewModelToRequest_WhenPhoneNumbersAreEmpty_ShouldReturnNull()
    {
        // Arrange
        LobbyistRegistrationStep01ViewModel model = new()
        {
            FaxNumber = "",
            PhoneNumber = "",
            FaxNumberCountryCode = "",
            PhoneNumberCountryCode = "",
            Addresses =
        [
            new() { Purpose = "Business", Country = "USA" },
            new() { Purpose = "Mailing", Country = "USA" }
        ]
        };
        // Act
        LobbyistRegistrationRequestDto result = _svc.MapLobbyistViewModelToRequest(model);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.FaxNumber, Is.Null);
            Assert.That(result.PhoneNumber, Is.Null);
        });
    }

    [Test]
    public void MappingViewModelFromDto_ShouldReturnFullResponse()
    {
        // Arrange
        LobbyistRegistrationStep01ViewModel model = new()
        {
            FaxNumber = "",
            PhoneNumber = "",
            FaxNumberCountryCode = "",
            PhoneNumberCountryCode = "",
            Addresses =
            [
                new() { Purpose = "Business", Country = "USA" },
                new() { Purpose = "Mailing", Country = "USA" }
            ]
        };

        Generated.LobbyistResponseDto dto = new(
            addresses: new List<Generated.AddressDtoModel>
            {
                new(
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Mailing",
                    zip: "12345"
                ),
                new(
                    city: "Test City",
                    country: "USA",
                    purpose: "Business",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Business",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 789,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<Generated.PhoneNumberDto>
            {
                new("+1", null, "987", 1, false, "1011231234", 1, false, "Home"),
                new("+1", null, "987", 1, false, "1011231234", 1, false, "Fax"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"
        );

        // Act
        _svc.MappingViewModelFromDto(model, dto);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.FaxNumber, Is.Not.Null);
            Assert.That(model.PhoneNumber, Is.Not.Null);
        });
    }

    [Test]
    public void LobbyistRegistrationStep01_PropertyAssignments_ShouldSetAndGetValues()
    {
        // Arrange
        var contactModel = new GenericContactViewModel { FirstName = "Alice", LastName = "Johnson" };

        var model = new LobbyistRegistrationStep01
        {
            FilerId = 1001,
            ContactId = 2002,
            RegistrationFilingId = 3003,
            Contact = contactModel
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.FilerId, Is.EqualTo(1001));
            Assert.That(model.ContactId, Is.EqualTo(2002));
            Assert.That(model.RegistrationFilingId, Is.EqualTo(3003));
            Assert.That(model.Contact, Is.EqualTo(contactModel));
            Assert.That(model.Contact.FirstName, Is.EqualTo("Alice"));
            Assert.That(model.Contact.LastName, Is.EqualTo("Johnson"));
        });
    }

    [Test]
    public void WithdrawLobbyistRegistration_MapViewModel_ShouldReturnValues()
    {
        // Arrange
        var lobbyistModel = new LobbyistResponseDto()
        {
            Id = 1,
            LegislativeSessionId = 1,
            Name = "Name 1",
            Addresses = new List<AddressDtoModel>()
            {
                new()
                {
                    Purpose = "Business",
                    City = "SD",
                    State = "CA",
                    Country = "US",
                    Street = "Str 1",
                    Street2 = "Str 2",
                    Zip = "12345"
                }
            }
        };

        // Act
        var model = _svc.MapWithdrawLobbyistRegistrationViewModel(lobbyistModel);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(lobbyistModel.Id));
            Assert.That(model.LegislativeSessionId, Is.EqualTo(lobbyistModel.LegislativeSessionId));
            Assert.That(model.LobbyistName, Is.EqualTo(lobbyistModel.Name));
        });
    }

    [Test]
    public void TerminateLobbyistRegistration_MapViewModel_ShouldReturnValues()
    {
        // Arrange
        var lobbyistResponse = new LobbyistResponseDto()
        {
            Id = 1,
            Name = "Name 1",
            FilerId = 1,
            Email = "<EMAIL>",
            PhoneNumbers = new List<PhoneNumberDto>()
            {
                new()
                {
                    Id = 1,
                    Extension = "testPhone",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Home"
                },
                new()
                {
                    Id = 2,
                    Extension = "testFax",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "456456",
                    Type = "Fax"
                }
            },
            TerminatedAt = DateTime.UtcNow,
            LegislativeSessionId = 2,
            Addresses = new List<AddressDtoModel>()
            {
                new()
                {
                    Purpose = "Business",
                    City = "SD",
                    State = "CA",
                    Country = "US",
                    Street = "Str 1",
                    Street2 = "Str 2",
                    Zip = "12345"
                }
            }
        };

        // Act
        var model = _svc.MapTerminateLobbyistRegistrationViewModel(lobbyistResponse);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(lobbyistResponse.Id));
            Assert.That(model.Name, Is.EqualTo(lobbyistResponse.Name));
            Assert.That(model.LobbyistEmployerOrLobbyingFirmName, Is.EqualTo(lobbyistResponse.LobbyistEmployerOrLobbyingFirmName));
            Assert.That(model.FilerId, Is.EqualTo(lobbyistResponse.FilerId));
            Assert.That(model.Email, Is.EqualTo(lobbyistResponse.Email));
            Assert.That(model.PhoneNumber, Is.EqualTo($"{lobbyistResponse.PhoneNumbers[0].CountryCode}{lobbyistResponse.PhoneNumbers[0].Number}"));
            Assert.That(model.FaxNumber, Is.EqualTo($"{lobbyistResponse.PhoneNumbers[1].CountryCode}{lobbyistResponse.PhoneNumbers[1].Number}"));
            Assert.That(model.LegislativeSessionId, Is.EqualTo(lobbyistResponse.LegislativeSessionId));
        });
    }

    [Test]
    public void TerminateLobbyistRegistration_MapViewModel_WithoutPhoneNumber()
    {
        // Arrange
        var lobbyistResponse = new LobbyistResponseDto()
        {
            Id = 1,
            Name = "Name 1",
            FilerId = 1,
            Email = "<EMAIL>",
            TerminatedAt = DateTime.UtcNow,
            LegislativeSessionId = 2,
        };

        // Act
        var model = _svc.MapTerminateLobbyistRegistrationViewModel(lobbyistResponse);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(lobbyistResponse.Id));
            Assert.That(model.Name, Is.EqualTo(lobbyistResponse.Name));
            Assert.That(model.FilerId, Is.EqualTo(lobbyistResponse.FilerId));
            Assert.That(model.Email, Is.EqualTo(lobbyistResponse.Email));
            Assert.That(model.PhoneNumber, Is.Null);
            Assert.That(model.FaxNumber, Is.Null);
            Assert.That(model.LegislativeSessionId, Is.EqualTo(lobbyistResponse.LegislativeSessionId));
        });
    }

    [Test]
    public void GetTerminationVerificationViewModel_ShouldThrowException()
    {
        // Arrange
        var id = 9999;

        // Act & Assert
        var result = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _svc.GetLobbyistVerificationViewModel(id));

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Message, Is.EqualTo($"Lobbyist registration with Filer ID {id} not found."));
    }

    [Test]
    public async Task GetTerminationVerificationViewModel_ShouldReturnViewModel()
    {
        // Arrange
        long id = 1;
        var data = new LobbyistResponseDto
        {
            Id = id,
            Email = "<EMAIL>",
            FirstName = "test",
            LastName = "test",
        };

        _ = _lobbyistRegistrationSvcMock
            .Setup(x => x.GetRegistrationById(It.IsAny<long>()))
            .ReturnsAsync(data);

        // Act
        LobbyistVerificationViewModel result = await _svc.GetLobbyistVerificationViewModel(id);

        //Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(data.Id));
            Assert.That(result.FirstName, Is.EqualTo(data.FirstName));
            Assert.That(result.LastName, Is.EqualTo(data.LastName));
            Assert.That(result.Email, Is.EqualTo(data.Email));
        });
    }

    [Test]
    public async Task GetConfirmationViewModel_ShouldReturnViewModel()
    {
        // Arrange
        long id = 1;

        var pendingItem = new PendingItemDto()
        {
            Item = "Lobbying firm notified",
            Status = "Comnpleted",
        };

        // Act
        var result = await _svc.GetConfirmationViewModel(id);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.PendingItems, Is.Not.Null);
    }
}
