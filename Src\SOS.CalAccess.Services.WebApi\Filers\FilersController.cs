// <copyright file="FilersController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filers;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.WebApi.Authorization;
using SOS.CalAccess.Services.WebApi.Authorization.ThirdParty.Decisions;
using SOS.CalAccess.Services.WebApi.Registrations;
using SOS.CalAccess.Services.WebApi.Shared;

namespace SOS.CalAccess.Services.WebApi.Filers;

/// <summary>
/// API controller responsible for routing requests related to filers.
/// </summary>
[Route("api/[controller]")]
[ApiController]
[ApiConventionType(typeof(DefaultApiConventions))]
public class FilersController(
    IAuthorizationService authorization,
    IAuditService auditService,
    IFilerSvc filerSvc,
    IDateTimeSvc dateTimeSvc) : AuthorizationAwareControllerBase(authorization), IFilerSvc
{
    /// <summary>
    /// Gets the current registration for a filer.
    /// </summary>
    /// <param name="query">The query to get the current registration.</param>
    /// <param name="id">The id of the filer.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("{id:long}/current-registration", Name = nameof(GetCurrentRegistration))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<RegistrationResponse>> GetCurrentRegistration(
        [FromServices] IGetCurrentRegistration query,
        [FromRoute] long id,
        CancellationToken cancellationToken = default)
    {
        var result = await query.Execute(new IGetCurrentRegistration.ForFiler(id), cancellationToken);

        return result switch
        {
            Success<Registration> r => Ok(new RegistrationResponse(r.Value)),
            Failure<Registration>.NotFound n => NotFound(n),
            _ => throw new InvalidOperationException("Unexpected query result"),
        };
    }

    /// <summary>
    ///  Terminates a filer registration by id.
    /// </summary>
    /// <param name="command">Command handler.</param>
    /// <param name="id">The id of the filer registration to terminate.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [HttpPut("{id:long}/terminate", Name = nameof(TerminateRegistration))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Edit))]
    public async Task<ActionResult<RegistrationItemResponse>> TerminateRegistration(
        [FromServices] ITerminateFilerRegistration command,
        [FromRoute] long id,
        CancellationToken cancellationToken = default)
    {
        //        AuthorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Terminate, User, id));

        await ThrowIfRequirementNotMet([
            HasAccess.ToFiler(id),
            RulesAllowAction.Update<Registration>(new { FilerId = id })
        ]);

        await auditService.LogAction(
            BusinessAction.ChangeStatus<Registration>(id.ToString(), dateTimeSvc, "Terminated"), cancellationToken);

        return await command.Execute(id, cancellationToken) switch
        {
            Success<Registration> r => Ok(new RegistrationItemResponse(r.Value)),
            Failure<Registration>.NotFound n => NotFound(n),
            Failure<Registration>.InvalidState i => BadRequest(i),
            _ => BadRequest(),
        };
    }

    Task<long> IFilerSvc.AddFilerAsync(Filer filerRequest)
    {
        throw new NotImplementedException();
    }

    Task IFilerSvc.AddFilerLinkAsync(FilerLink filerLink)
    {
        throw new NotImplementedException();
    }

    Task<IEnumerable<Filer>> IFilerSvc.GetAllByUserId(long userId)
    {
        throw new NotImplementedException();
    }

    Task<IEnumerable<Filer>> IFilerSvc.GetAllFilers()
    {
        throw new NotImplementedException();
    }

    Task<Filer?> IFilerSvc.GetFiler(long filerId)
    {
        throw new NotImplementedException();
    }

    [HttpGet(IFilerSvc.GetFilerDtoPath, Name = nameof(GetFilerDto))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<FilerDto> GetFilerDto(long filerId)
    {
        return await filerSvc.GetFilerDto(filerId);
    }

    Task<FilerLink?> IFilerSvc.GetFilerLinkByFilerIdAndLinkTypeAsync(long filerId, long filerLinkTypeId)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    ///  Get Filer User information by Filer ID.
    /// </summary>
    /// <param name="filerId">The id of the filer registration.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>Filer User information.</returns>
    [HttpGet(IFilerSvc.GetFilerUserAsyncPath, Name = nameof(GetFilerUserAsync))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<FilerUserDto?> GetFilerUserAsync(long filerId)
    {
        return await filerSvc.GetFilerUserAsync(filerId);
    }

    /// <summary>
    ///  Get Filer User information by Filer ID and User Id.
    /// </summary>
    /// <param name="filerId">The id of the filer registration.</param>
    /// <param name="userId">The id of the current user.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>Filer User information.</returns>
    [HttpGet(IFilerSvc.GetFilerUserByUserIdAsyncPath, Name = nameof(GetFilerUserByUserIdAsync))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<FilerUserDto?> GetFilerUserByUserIdAsync(long filerId, long userId)
    {
        return await filerSvc.GetFilerUserByUserIdAsync(filerId, userId);
    }

    /// <summary>
    /// Returns true if the user initiating request has the filer role for the filer passed.
    /// </summary>
    /// <param name="filerId">The id of the filer</param>
    /// <param name="filerRoleId">The id of the filer role the user should have</param>
    /// <returns></returns>
    [HttpGet(IFilerSvc.CurrentUserHasFilerRolePath, Name = nameof(CurrentUserHasFilerRole))]
    public async Task<bool> CurrentUserHasFilerRole(long filerId, long filerRoleId)
    {
        return await filerSvc.CurrentUserHasFilerRole(filerId, filerRoleId);
    }

    void IFilerSvc.TerminateFiler(long filerId)
    {
        throw new NotImplementedException();
    }

    void IFilerSvc.UpdateFiler(Filer filer)
    {
        throw new NotImplementedException();
    }

    Task IFilerSvc.UpdateFilerLinkAsync(FilerLink filerLink)
    {
        throw new NotImplementedException();
    }

    Task IFilerSvc.UpdateFilerUserRoleAsync(long filerUserId, long filerRoleId)
    {
        throw new NotImplementedException();
    }

    void IFilerSvc.UpdateFilerStatus(long filerId, FilerStatus status)
    {
        throw new NotImplementedException();
    }

    Task<FilerUserDto?> IFilerSvc.GetFilerUserAsync(long filerId)
    {
        throw new NotImplementedException();
    }

    Task<List<FilerUserDto>> IFilerSvc.GetFilerUsersAsync(long filerId)
    {
        throw new NotImplementedException();
    }

    Task<FilerUserDto> IFilerSvc.AddFilerUserAsync(FilerUser filerUser)
    {
        throw new NotImplementedException();
    }

    Task<long> IFilerSvc.GetFilerRegistrationAsOfAsync(long filerId, DateTime pointInTime)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public Task<bool> MergeFiler(long primaryFilerId, long secondaryFilerId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public Task<bool> DeleteFiler(long filerId)
    {
        throw new NotImplementedException();
    }

    [NonAction] //temporary until implemented
    public Task<bool> ReferFilerToFPPC(long filerId)
    {
        throw new NotImplementedException();
    }
}
