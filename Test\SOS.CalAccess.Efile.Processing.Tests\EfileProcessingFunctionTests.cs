using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Reflection;
using System.Text;
using Azure;
using Azure.Messaging.ServiceBus;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Efile;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Common.FileSystem;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Efile.Processing.Tests;

/// <summary>
/// Tests of the Efile Processing Run() method
/// </summary>
[TestFixture]
public class EfileProcessingFunctionTests
{
    #region Properties

    /// <summary>
    /// Fake service used for db operations on ApiRequest records
    /// </summary>
    private IApiRequestSvc _apiRequestSvc;

    /// <summary>
    /// Configuration settings passed to the Azure Function during unit tests.
    /// Since we're instantiating EfileValidationFunction() directly (outside the Azure Functions runtime),
    /// we must flatten the configuration keys by omitting the "AzureValues:" wrapper used in local.settings.json.
    /// </summary>
    private static IConfiguration _configuration;

    /// <summary>
    /// Fake file service used for Blob Storage operations
    /// </summary>
    private IFileSvc _fileSvc;

    /// <summary>
    /// Fake logging object.
    /// </summary>
    private ILogger<EfileProcessingFunction> _logger;

    /// <summary>
    /// Maximum allowed execution time (in milliseconds).
    /// </summary>
    private const int MaxTimeAllowed = 90000;

    /// <summary>
    /// Fake service for processing (validating & saving) the form submission
    /// </summary>
    private ICandidateIntentionRegistrationSvc _registrationSvc;

    /// <summary>
    /// The System Under Test (SUT) by this test class. This instance is used by all
    /// tests in which special setup is not required; otherwise the test in question will
    /// create its own EfileProcessingFunction instance.
    /// </summary>
    private EfileProcessingFunction _sut;

    /// <summary>
    /// The class is used to generate a submission object.
    /// </summary>
    private IEfileSubmissionSvc _submissionSvc;

    /// <summary>
    ///Fake service for processing (validating & saving) the form submission
    /// </summary>
    private ISmoRegistrationSvc _smoRegistrationSvc;

    /// <summary>
    /// Reference to an object that can perform actions on the service bus message
    /// </summary>
    private IForm470Svc _form470Svc;

    /// <summary>
    /// Fake service for processing (validating & saving) the form submission
    /// </summary>
    private IForm470SSvc _form470SSvc;

    /// <summary>
    /// Reference to an object that can perform actions on the service bus message
    /// </summary>
    private IFilingSvc _filingSvc;

    private ILobbyistRegistrationSvc _lobbyistRegistrationSvc;
    #endregion

    /// <summary>
    /// Create common items to be used across all tests
    /// </summary>
    [SetUp]
    public void Setup()
    {
        _apiRequestSvc = Substitute.For<IApiRequestSvc>();
        _configuration = BuildConfiguration();
        _logger = Substitute.For<ILogger<EfileProcessingFunction>>();
        _registrationSvc = Substitute.For<ICandidateIntentionRegistrationSvc>();
        _fileSvc = Substitute.For<IFileSvc>();
        _submissionSvc = Substitute.For<IEfileSubmissionSvc>();
        _smoRegistrationSvc = Substitute.For<ISmoRegistrationSvc>();
        _form470Svc = Substitute.For<IForm470Svc>();
        _form470SSvc = Substitute.For<IForm470SSvc>();
        _filingSvc = Substitute.For<IFilingSvc>();
        _lobbyistRegistrationSvc = Substitute.For<ILobbyistRegistrationSvc>();
        _sut = new EfileProcessingFunction(
            _logger,
            _configuration,
            _apiRequestSvc,
            _registrationSvc,
            _fileSvc,
            _submissionSvc,
            _smoRegistrationSvc,
            _form470Svc,
            _filingSvc,
            _form470SSvc,
            _lobbyistRegistrationSvc
        );
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void AcceptAndCompleteRequest()
    {
        // Arrange
        string filingId = "1";
        BinaryData data = BinaryData.FromString("{}");  // "{}" deserialzes to a new ApiRequest with all fields having default values
        var emptyMessage = ServiceBusModelFactory.ServiceBusReceivedMessage(data);
        PrivateMemberAccessor.SetPrivate(_sut, "_message", emptyMessage);

        ApiRequest apiRequest = new() { ReceivedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local) };
        PrivateMemberAccessor.SetPrivate(_sut, "_apiRequest", apiRequest);

        var actionsSub = Substitute.For<ServiceBusMessageActions>();
        PrivateMemberAccessor.SetPrivate(_sut, "_messageActions", actionsSub);

        // Act & Assert
        Assert.DoesNotThrowAsync(() =>
            PrivateMemberAccessor.InvokePrivateAsync(_sut, "AcceptAndCompleteRequest", [filingId]));
    }

    /// <summary>
    /// Verify that Efile Processing can run successfully with different queue names.
    /// </summary>
    /// <param name="queueName">Different queue names to swap into the Configuration</param>
    [TestCase("efileapi-request-small")]
    [TestCase("efileapi-request-medium")]
    [TestCase("efileapi-request-large")]
    public async Task RunShouldProcessMessageWithDifferentQueueNames(string queueName)
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                { "AzureValues:BlobStorageConnectionString", "test" },
                { "AzureValues:BlobStorageContainerName", "test" },
                { "AzureValues:IsProcessingRetryEnabledOnSystemError", "true" },
                { "AzureValues:QueueName", queueName }
            })
            .Build();

        EfileProcessingFunction function = new(
            _logger,
            configuration,
            _apiRequestSvc,
            _registrationSvc,
            _fileSvc,
            _submissionSvc,
            _smoRegistrationSvc,
           _form470Svc,
            _filingSvc,
            _form470SSvc,
            _lobbyistRegistrationSvc
        );

        ServiceBusReceivedMessage? message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            messageId: "test-message-id",
            body: BinaryData.FromString("Test message body"),
            contentType: "application/json");

        var messageActionsSub = Substitute.For<ServiceBusMessageActions>();

        messageActionsSub
            .AbandonMessageAsync(
                Arg.Any<ServiceBusReceivedMessage>(),
                Arg.Any<IDictionary<string, object>>(),
                Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        await function.Run(message, messageActionsSub);

        // Assert
        await messageActionsSub.Received(1).AbandonMessageAsync(
            Arg.Any<ServiceBusReceivedMessage>(),
            Arg.Any<IDictionary<string, object>>(),
            Arg.Any<CancellationToken>());
    }

    [Test, Category("Unit")]
    public async Task RunShouldProcessCandidateIntentionStatementSuccessfully()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Campaign-Statement-CandidateIntention_Guid123\",\"ReceivedAt\":\"02/02/2024\" }"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 1,
            BlobFilename = "Campaign-Statement-CandidateIntention_Guid123",
            ReceivedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local)
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"CandidateInformation\": {\"FirstName\": \"John\"}}")));

        var submissionDto = new CandidateIntentionStatementDto();
        _submissionSvc.CreateSubmissionDto(Arg.Any<EfileCandidateIntentionStatement>(), apiRequest).Returns(submissionDto);

        var registrationResponse = new RegistrationResponseDto { Id = 123 };
        _registrationSvc.SubmitCandidateIntentionStatementForEfile(submissionDto).Returns(registrationResponse);

        // Act
        await _sut.Run(message, messageActions);

        // Assert
        await _apiRequestSvc.Received(1).AcceptRequest(apiRequest.Id, registrationResponse.Id?.ToString(CultureInfo.InvariantCulture) ?? "-1");
        await messageActions.Received(1).CompleteMessageAsync(message);
    }

    [Test, Category("Unit")]
    public async Task RunShouldProcessSlateMailerOrganizationSuccessfully()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Campaign-Registration-SlateMailer_Guid123\",\"ReceivedAt\":\"02/02/2024\"}"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 2,
            BlobFilename = "Campaign-Registration-SlateMailer_Guid123",
            ReceivedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local)
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"SlateMailerOrg\": {\"Name\": \"Test Org\"}}")));

        var submissionDto = new EfileSlateMailerOrganizationDto();
        _submissionSvc.CreateSubmissionDtoSMO(Arg.Any<EfileSlateMailerOrganization>(), apiRequest, false).Returns(submissionDto);

        var registrationResponse = new RegistrationResponseDto { Id = 456 };
        _smoRegistrationSvc.SubmitSmoRegistrationForEfile(submissionDto).Returns(registrationResponse);

        // Act
        await _sut.Run(message, messageActions);

        // Assert
        await _apiRequestSvc.Received(1).AcceptRequest(apiRequest.Id, registrationResponse.Id?.ToString(CultureInfo.InvariantCulture) ?? "-1");
        await messageActions.Received(1).CompleteMessageAsync(message);
    }
    [Test, Category("Unit")]
    public async Task RunShouldProcessSlateMailerOrganizationTerminationSuccessfully()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Campaign-Termination-SlateMailer_Guid123\",\"ReceivedAt\":\"02/02/2024\"}"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 2,
            BlobFilename = "Campaign-Termination-SlateMailer_Guid123",
            ReceivedAt = DateTime.UtcNow
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"SlateMailerOrg\": {\"Name\": \"Test Org\"}}")));

        var submissionDto = new EfileSlateMailerOrganizationDto();
        _submissionSvc.CreateSubmissionDtoSMO(Arg.Any<EfileSlateMailerOrganization>(), apiRequest, true).Returns(submissionDto);

        var registrationResponse = new RegistrationResponseDto { Id = 456 };
        _smoRegistrationSvc.SmoTerminationForEfile(submissionDto).Returns(registrationResponse);

        // Act
        await _sut.Run(message, messageActions);

        // Assert
        await _apiRequestSvc.Received(1).AcceptRequest(apiRequest.Id, registrationResponse.Id?.ToString(CultureInfo.InvariantCulture) ?? "-1");
        await messageActions.Received(1).CompleteMessageAsync(message);
    }

    [Test, Category("Unit")]
    public async Task RunShouldProcessCandidateCampaignStatementShortSuccessfully()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Campaign-Disclosure-Candidate-CampaignStatement-Short_Guid123\",\"ReceivedAt\":\"02/02/2024\" }"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 1,
            BlobFilename = "Campaign-Disclosure-Candidate-CampaignStatement-Short_Guid123",
            ReceivedAt = DateTime.UtcNow
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"attestation\": {\"FirstName\": \"John\"}}")));

        var submissionDto = new CandidateStatementShortSubmissionDto();
        _submissionSvc.CreateCandidateStatementShortSubmissionDto(Arg.Any<EfileCandidateCampaignStatementShort>(), apiRequest).Returns(submissionDto);

        var registrationResponse = new ValidatedForm470ResponseDto { Id = 123 };
        _form470Svc.SubmitCandidateStatementShortForEfile(submissionDto).Returns(registrationResponse);

        // Act
        await _sut.Run(message, messageActions);

        // Assert
        await _apiRequestSvc.Received(1).AcceptRequest(apiRequest.Id, registrationResponse.Id?.ToString(CultureInfo.InvariantCulture) ?? "-1");
        await messageActions.Received(1).CompleteMessageAsync(message);
    }

    // dtb: validate supplement
    [Test, Category("Unit")]
    public async Task RunShouldProcessCandidateCampaignStatementSupplementSuccessfully()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Campaign-Disclosure-Candidate-CampaignStatement-Supplement_Guid123\",\"ReceivedAt\":\"02/02/2024\" }"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 1,
            BlobFilename = "Campaign-Disclosure-Candidate-CampaignStatement-Supplement_Guid123",
            ReceivedAt = DateTime.UtcNow
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"attestation\": {\"FirstName\": \"John\"}}")));

        var submissionDto = new CandidateStatementSupplementSubmissionDto();
        _submissionSvc.CreateCandidateStatementSupplementSubmissionDto(Arg.Any<EfileCandidateCampaignStatementSupplement>(), apiRequest).Returns(submissionDto);

        var registrationResponse = new ValidatedForm470SResponseDto { Id = 123 };
        _form470SSvc.SubmitCandidateSupplementForEfile(submissionDto).Returns(registrationResponse);

        // Act
        await _sut.Run(message, messageActions);

        // Assert
        await _apiRequestSvc.Received(1).AcceptRequest(apiRequest.Id, registrationResponse.Id?.ToString(CultureInfo.InvariantCulture) ?? "-1");
        await messageActions.Received(1).CompleteMessageAsync(message);
    }

    [Test, Category("Unit")]
    public async Task RunShouldProcessLobbyistReportSuccessfully()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Lobbying-Disclosure-Lobbyist_Guid123\",\"ReceivedAt\":\"02/02/2024\"}"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 2,
            BlobFilename = "Lobbying-Disclosure-Lobbyist_Guid123",
            ReceivedAt = DateTime.UtcNow
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"SlateMailerOrg\": {\"Name\": \"Test Org\"}}")));

        var submissionDto = new LobbyistReportDto();
        _submissionSvc.CreateSubmissionDtoLR(Arg.Any<EfileLobbyistReport>(), apiRequest).Returns(submissionDto);

        var registrationResponse = new RegistrationResponseDto { Id = 123 };
        _filingSvc.SubmitLobbyistReportForEfile(submissionDto).Returns(registrationResponse);

        // Act
        await _sut.Run(message, messageActions);

        // Assert
        await _apiRequestSvc.Received(1).AcceptRequest(apiRequest.Id, registrationResponse.Id?.ToString(CultureInfo.InvariantCulture) ?? "-1");
        await messageActions.Received(1).CompleteMessageAsync(message);
    }

    ////TODO : SK : Commented out because the unit test was failing need to check with the team (Ryan)

    ///// <summary>
    ///// All data should map/convert correctly from EfileCandidateIntentionStatement to
    ///// CandidateIntentionStatementDto.
    ///// </summary>
    //[Obsolete]
    //[Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    //public void CreateSubmissionDtoShouldMapCorrectly()
    //{
    //    // Arrange
    //    EfileCandidateIntentionStatement jsonData = new()
    //    {
    //        Attestation = new Attestation()
    //        {
    //            ExecutedAt = _dateNow,
    //            Signature = "signature"
    //        },
    //        CandidateInformation = new CandidateInformation()
    //        {
    //            FirstName = "First",
    //            MiddleName = "Middle",
    //            LastName = "Last",
    //            CandidateAddress = new Address()
    //            {
    //                Street = "456 Campaign Blvd",
    //                Street2 = "",
    //                City = "Sacramento",
    //                State = "CA",
    //                ZipCode = "95814",
    //                County = "Sacramento",
    //                Type = "Business",
    //                Country = "United States"
    //            },
    //            MailingAddress = new Address()
    //            {
    //                Street = "123 Front St",
    //                Street2 = "Appt 8",
    //                City = "Lafayette",
    //                State = "LA",
    //                ZipCode = "70506",
    //                County = "Lafayette",
    //                Type = "Residential",
    //                Country = "United States"
    //            },
    //            ElectionRaceId = 1,
    //            ElectionJurisdiction = "State",
    //            IsNonPartisanOffice = true,
    //            PartyAffiliation = "American Independent Party",
    //            Phone = "3375551234",
    //            Email = "<EMAIL>"
    //        },
    //        StateCandidateExpenditureLimit = new StateCandidateExpenditureLimit()
    //        {
    //            ExpenditureLimitAccepted = true,
    //            ExpenditureExceeded = true,
    //            ContributedPersonalExcessFundsOn = _dateNow
    //        }
    //    };

    //    // All DTO fields in the new object we're creating are loaded from the object above, with the exeption of
    //    // SubmittedAt, which comes from ApiRequest.ReceivedAt.
    //    ApiRequest apiRequest = new() { ReceivedAt = _dateNow };
    //    PrivateMemberAccessor.SetPrivate(_sut, "_apiRequest", apiRequest);

    //    // Act
    //    CandidateIntentionStatementDto? submission =
    //        PrivateMemberAccessor.InvokePrivate(_sut, "CreateSubmissionDto", [jsonData]) as CandidateIntentionStatementDto;

    //    // Assert
    //    Assert.That(submission, Is.Not.Null);
    //    Assert.Multiple(() =>
    //    {
    //        Assert.That(submission!.Race!.Id, Is.EqualTo(jsonData.CandidateInformation.ElectionRaceId));
    //        Assert.That(submission!.Registration!.FirstName, Is.EqualTo(jsonData.CandidateInformation.FirstName));
    //        Assert.That(submission!.Registration!.VerificationSignature, Is.EqualTo(jsonData.Attestation.Signature));
    //    });
    //}

    //TODO : SK : Commented out because the unit test was failing need to check with the team (Ryan)

    /// <summary>
    /// All data should map/convert correctly from EfileCandidateIntentionStatement to
    /// CandidateIntentionStatementDto.
    /// </summary>
    //[Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    //public void CreateSubmissionDtoShouldMapCorrectlyApi()
    //{
    //    // Arrange
    //    EfileCandidateIntentionStatement jsonData = new()
    //    {
    //        Attestation = new Attestation()
    //        {
    //            ExecutedAt = _dateNow,
    //            Signature = "signature"
    //        },
    //        CandidateInformation = new CandidateInformation()
    //        {
    //            FirstName = "First",
    //            MiddleName = "Middle",
    //            LastName = "Last",
    //            CandidateAddress = new Address()
    //            {
    //                Street = "456 Campaign Blvd",
    //                Street2 = "",
    //                City = "Sacramento",
    //                State = "CA",
    //                ZipCode = "95814",
    //                County = "Sacramento",
    //                Type = "Business",
    //                Country = "United States"
    //            },
    //            MailingAddress = new Address()
    //            {
    //                Street = "123 Front St",
    //                Street2 = "Appt 8",
    //                City = "Lafayette",
    //                State = "LA",
    //                ZipCode = "70506",
    //                County = "Lafayette",
    //                Type = "Residential",
    //                Country = "United States"
    //            },
    //            ElectionRaceId = 1,
    //            ElectionJurisdiction = "State",
    //            IsNonPartisanOffice = true,
    //            PartyAffiliation = "American Independent Party",
    //            Phone = "3375551234",
    //            Email = "<EMAIL>"
    //        },
    //        StateCandidateExpenditureLimit = new StateCandidateExpenditureLimit()
    //        {
    //            ExpenditureLimitAccepted = true,
    //            ExpenditureExceeded = true,
    //            ContributedPersonalExcessFundsOn = DateNow
    //        }
    //    };

    //    // All DTO fields in the new object we're creating are loaded from the object above, with the exeption of
    //    // SubmittedAt, which comes from ApiRequest.ReceivedAt.
    //    ApiRequest apiRequest = new() { ReceivedAt = _dateNow, UserId = string.Empty };
    //    PrivateMemberAccessor.SetPrivate(_sut, "_apiRequest", apiRequest);

    //    // Act
    //    CandidateIntentionStatementDto? submission =
    //        PrivateMemberAccessor.InvokePrivate(_sut, "CreateSubmissionDto", [jsonData]) as CandidateIntentionStatementDto;

    //    // Assert
    //    Assert.That(submission, Is.Not.Null);
    //    Assert.Multiple(() =>
    //    {
    //        Assert.That(submission!.UserId, Is.EqualTo(-1));
    //        Assert.That(submission!.Race!.Id, Is.EqualTo(jsonData.CandidateInformation.ElectionRaceId));
    //        Assert.That(submission!.Registration!.FirstName, Is.EqualTo(jsonData.CandidateInformation.FirstName));
    //        Assert.That(submission!.Registration!.VerificationSignature, Is.EqualTo(jsonData.Attestation.Signature));
    //    });
    //}

    /// <summary>
    /// Verify that exception is thrown if the Registration Service is null.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CtorShouldThrowWhenRegistrationSvcIsNull()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() =>
        new EfileProcessingFunction(_logger, _configuration, _apiRequestSvc, null!, _fileSvc, _submissionSvc, _smoRegistrationSvc, _form470Svc, _filingSvc, _form470SSvc, _lobbyistRegistrationSvc));
    }

    /// <summary>
    /// Verify that GetApiRequestFromMessageBody() fails and throws a specific exception
    /// if the BlobFilename field is empty.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void GetApiRequestFromMessageBodyThrowsWhenBlobFilenameIsEmpty()
    {
        // Arrange
        BinaryData data = BinaryData.FromString("{}");  // "{}" deserialzes to a new ApiRequest with all fields having default values
        var emptyMessage = ServiceBusModelFactory.ServiceBusReceivedMessage(data);
        PrivateMemberAccessor.SetPrivate(_sut, "_message", emptyMessage);

        // Act & Assert
        var ex = Assert.Throws<TargetInvocationException>(() =>
            PrivateMemberAccessor.InvokePrivate(_sut, "GetApiRequestFromMessageBody"));

        Assert.Multiple(() =>
        {
            Assert.That(ex.InnerException, Is.TypeOf<InvalidOperationException>());
            Assert.That(ex.InnerException?.Message, Is.EqualTo("Can't proceed with processing. BlobFilename is empty or missing."));
        });
    }

    /// <summary>
    /// Verify that GetApiRequestFromMessageBody() fails and throws a specific exception
    /// if the service bus message is empty.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void GetApiRequestFromMessageBodyThrowsWhenMessageIsEmpty()
    {
        // Arrange
        BinaryData data = BinaryData.FromString("");  // Attempting to deserialize an empty string will give an exception
        var emptyMessage = ServiceBusModelFactory.ServiceBusReceivedMessage(data);
        PrivateMemberAccessor.SetPrivate(_sut, "_message", emptyMessage);

        // Act & Assert
        var ex = Assert.Throws<TargetInvocationException>(() =>
            PrivateMemberAccessor.InvokePrivate(_sut, "GetApiRequestFromMessageBody"));

        Assert.Multiple(() =>
        {
            Assert.That(ex.InnerException, Is.TypeOf<InvalidOperationException>());
            Assert.That(ex.InnerException?.Message, Is.EqualTo("Unable to obtain ApiRequest from the Service Bus message"));
        });
    }

    /// <summary>
    /// Verify that GetApiRequestFromMessageBody() fails and throws a specific exception
    /// if the ReceivedAt field is empty.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void GetApiRequestFromMessageBodyThrowsWhenReceivedAtIsEmpty()
    {
        // Arrange
        // BlobFilename must be provided to bypass initial validation
        var json = /*lang=json,strict*/ @"{""BlobFilename"":""blob-filename.txt""}";
        BinaryData data = BinaryData.FromString(json);
        var emptyMessage = ServiceBusModelFactory.ServiceBusReceivedMessage(data);
        PrivateMemberAccessor.SetPrivate(_sut, "_message", emptyMessage);

        // Act & Assert
        var ex = Assert.Throws<TargetInvocationException>(() =>
            PrivateMemberAccessor.InvokePrivate(_sut, "GetApiRequestFromMessageBody"));

        Assert.Multiple(() =>
        {
            Assert.That(ex.InnerException, Is.TypeOf<InvalidOperationException>());
            Assert.That(ex.InnerException?.Message, Is.EqualTo("Can't proceed with processing. ReceivedAt has not been set."));
        });
    }

    /// <summary>
    /// Verify that message is extracted from the message body and deserialized successfully.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void GetApiRequestFromMessageBodySuccessfulDeserialization()
    {
        // Arrange
        var currentDateTime = new DateTime(2025, 1, 1, 0, 0, 0, 0);
        var json = $@"
        {{
            ""BlobFilename"": ""BlobFilename"",
            ""Id"": 0,
            ""ReceivedAt"": ""{currentDateTime}""
        }}";

        BinaryData data = BinaryData.FromString(json);
        var apiRequestMsg = ServiceBusModelFactory.ServiceBusReceivedMessage(data);
        PrivateMemberAccessor.SetPrivate(_sut, "_message", apiRequestMsg);

        // Act
        ApiRequest? apiRequest = (ApiRequest?)PrivateMemberAccessor.InvokePrivate(_sut, "GetApiRequestFromMessageBody");

        // Assert
        Assert.That(apiRequest, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(apiRequest.BlobFilename, Is.EqualTo("BlobFilename"));
            Assert.That(apiRequest.Id, Is.EqualTo(0));
            Assert.That(apiRequest.ReceivedAt, Is.EqualTo(currentDateTime));
        });
    }

    /// <summary>
    /// GetFileContentsFromBlobStorage() should successfully get the file, but throw a
    /// RequestFailedException with a specific message if the file is empty.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void GetFileContentsFromBlobStorageExceptionWhenFileIsEmpty()
    {
        // Arrange
        var mockFileSvc = Substitute.For<IFileSvc>();
        using var emptyFileStream = new MemoryStream(Encoding.UTF8.GetBytes(""));
        mockFileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), default)
            .ReturnsForAnyArgs(_ => emptyFileStream);

        var function = new EfileProcessingFunction(
            _logger,
            _configuration,
            _apiRequestSvc,
            _registrationSvc,
            mockFileSvc,
            _submissionSvc,
            _smoRegistrationSvc,
            _form470Svc,
            _filingSvc,
            _form470SSvc,
            _lobbyistRegistrationSvc
        );

        PrivateMemberAccessor.SetPrivate(function, "_apiRequest", new ApiRequest { BlobFilename = "fake name" });

        // Act & Assert
        var ex = Assert.ThrowsAsync<RequestFailedException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<string>(function, "GetFileContentsFromBlobStorage"));

        Assert.That(ex.Message, Is.EqualTo("File from Blob Storage was empty"));
    }

    /// <summary>
    /// Verify that we can successfully read a file from Blob Storage and return its
    /// contents as a string.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task GetFileContentsFromBlobStorageReadsSuccessfully()
    {
        // Arrange
        var mockFileSvc = Substitute.For<IFileSvc>();
        using var helloWorldFileStream = new MemoryStream(Encoding.UTF8.GetBytes("hello world"));
        mockFileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), default)
            .ReturnsForAnyArgs(_ => helloWorldFileStream);

        var function = new EfileProcessingFunction(
            _logger,
            _configuration,
            _apiRequestSvc,
            _registrationSvc,
            mockFileSvc,
            _submissionSvc,
            _smoRegistrationSvc,
            _form470Svc,
            _filingSvc,
            _form470SSvc,
            _lobbyistRegistrationSvc
        );

        PrivateMemberAccessor.SetPrivate(function, "_apiRequest", new ApiRequest { BlobFilename = "fake name" });

        // Act
        string? result = await PrivateMemberAccessor.InvokePrivateAsync<string>(function, "GetFileContentsFromBlobStorage");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo("hello world"));
    }

    /// <summary>
    /// Verify behavior of HandleFailure() when it encounters a ValidationException. It should
    /// remove message from the queue and set ApiRequest to RejectedForBusinessRuleViolation.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task HandleFailureShouldCompleteOnValidationException()
    {
        // Arrange
        IConfiguration configuration = BuildConfiguration(new()
        {
            ["AzureValues:IsProcessingRetryEnabledOnSystemError"] = "true",
        });

        EfileProcessingFunction function = new(
            _logger,
            configuration,
            _apiRequestSvc,
            _registrationSvc,
            _fileSvc,
            _submissionSvc,
            _smoRegistrationSvc,
            _form470Svc,
            _filingSvc,
            _form470SSvc,
            _lobbyistRegistrationSvc
        );

        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(BinaryData.FromString("{}"));
        var actionsSub = Substitute.For<ServiceBusMessageActions>();
        var apiRequestSvc = Substitute.For<IApiRequestSvc>();
        PrivateMemberAccessor.SetPrivate(function, "_message", message);
        PrivateMemberAccessor.SetPrivate(function, "_messageActions", actionsSub);
        PrivateMemberAccessor.SetPrivate(function, "_apiRequest", new ApiRequest { Id = 1 });
        PrivateMemberAccessor.SetPrivate(function, "_apiRequestSvc", apiRequestSvc);

        // Act
        await (Task)PrivateMemberAccessor.InvokePrivate(function, "HandleFailure", new ValidationException())!;

        // Assert
        await actionsSub.Received(1).CompleteMessageAsync(message);
        await apiRequestSvc.Received(1).RejectRequestForBusinessRuleViolation(1, Arg.Any<List<ApiError>>());
    }

    /// <summary>
    /// Verify that the Efile function "abandons" the request, triggering a retry, when RetryEnabled
    /// is set to true and the function encounters a system error.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task HandleFailureShouldAbandonWhenSystemErrorAndRetryEnabled()
    {
        // Arrange
        IConfiguration configuration = BuildConfiguration(new()
        {
            ["AzureValues:IsProcessingRetryEnabledOnSystemError"] = "true",
        });

        EfileProcessingFunction function = new(
            _logger,
            configuration,
            _apiRequestSvc,
            _registrationSvc,
            _fileSvc,
            _submissionSvc,
            _smoRegistrationSvc,
            _form470Svc,
            _filingSvc,
            _form470SSvc,
            _lobbyistRegistrationSvc
        );

        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(BinaryData.FromString("{}"));
        var actionsSub = Substitute.For<ServiceBusMessageActions>();
        PrivateMemberAccessor.SetPrivate(function, "_message", message);
        PrivateMemberAccessor.SetPrivate(function, "_messageActions", actionsSub);
        PrivateMemberAccessor.SetPrivate(function, "_apiRequest", new ApiRequest { Id = 2 });

        // Act
        await (Task)PrivateMemberAccessor.InvokePrivate(function, "HandleFailure", new InvalidOperationException())!;

        // Assert
        await actionsSub.Received(1)
            .AbandonMessageAsync(message, Arg.Any<IDictionary<string, object>>(), Arg.Any<CancellationToken>());

        await _apiRequestSvc.Received(1)
            .RejectRequestForSystemError(2, Arg.Any<List<ApiError>>());
    }

    /// <summary>
    /// Verify that HandleFailure() completes the message and rejects the request
    /// when a system error occurs. RetryEnabled must be false for this to work.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task HandleFailureShouldCompleteWhenSystemErrorAndRetryDisabled()
    {
        // Arrange
        IConfiguration configuration = BuildConfiguration(new()
        {
            ["AzureValues:IsProcessingRetryEnabledOnSystemError"] = "false",
        });

        EfileProcessingFunction function = new(
            _logger,
            configuration,
            _apiRequestSvc,
            _registrationSvc,
            _fileSvc,
            _submissionSvc,
            _smoRegistrationSvc,
            _form470Svc,
            _filingSvc,
            _form470SSvc,
            _lobbyistRegistrationSvc
        );

        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(BinaryData.FromString("{}"));
        var actionsSub = Substitute.For<ServiceBusMessageActions>();
        PrivateMemberAccessor.SetPrivate(function, "_message", message);
        PrivateMemberAccessor.SetPrivate(function, "_messageActions", actionsSub);
        PrivateMemberAccessor.SetPrivate(function, "_apiRequest", new ApiRequest { Id = 3 });

        // Act
        await (Task)PrivateMemberAccessor.InvokePrivate(function, "HandleFailure", new InvalidOperationException())!;

        // Assert
        await actionsSub.Received(1).CompleteMessageAsync(message);
        await _apiRequestSvc.Received(1).RejectRequestForSystemError(3, Arg.Any<List<ApiError>>());
    }

    /// <summary>
    /// Verify that settings are pulled successfully and from the correct settings file
    /// when running locally.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void InitializeConfigurationSuccessForRunningLocally()
    {
        // Arrange
        // If environment is "Development" then it should load from "local.settings.json"
        Environment.SetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT", "Development");

        // Act & Assert
        Assert.DoesNotThrow(() => PrivateMemberAccessor.InvokePrivateStatic(_sut, "InitializeConfiguration"));
        IConfiguration configuration = PrivateMemberAccessor.GetPrivate<IConfiguration>(_sut, "_configuration");
        bool isRetryEnabled = Convert.ToBoolean(
            configuration["IsProcessingRetryEnabledOnSystemError"],
            CultureInfo.InvariantCulture);

        Assert.That(isRetryEnabled, Is.False);
    }

    ////TODO : SK : Commented out because the unit test was failing need to check with the team (Ryan)
    ///// <summary>
    ///// SubmitForm should throw if the registration service returns null.
    ///// </summary>
    //[Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    //public void SubmitFormThrowsWhenRegistrationServiceReturnsNull()
    //{
    //    // Arrange
    //    var submission = new CandidateIntentionStatementDto();
    //    _registrationSvc
    //        .SubmitCandidateIntentionStatementForEfile(submission)
    //        .Returns(Task.FromResult<RegistrationResponseDto>(null!));

    //    // Act & Assert
    //    var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
    //        await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
    //            _sut, "SubmitForm", new object[] { submission })
    //    );
    //    Assert.That(ex.Message, Is.EqualTo("Received a null response from the registration service"));
    //}

    ////TODO : SK : Commented out because the unit test was failing need to check with the team (Ryan)
    ///// <summary>
    ///// SubmitForm should throw a ValidationException and collect errors when ValidationErrors are present.
    ///// </summary>
    //[Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    //public Task SubmitFormThrowsValidationExceptionWhenValidationErrorsExist()
    //{
    //    // Arrange
    //    var submission = new CandidateIntentionStatementDto();
    //    var workflowErrors = new List<WorkFlowError>
    //    {
    //        new(
    //            FieldName: "TestField",
    //            ErrorCode: "ERR01",
    //            ErrorType: "BusinessRule",
    //            Message: "Test error message")
    //    };
    //    var response = new RegistrationResponseDto
    //    {
    //        ValidationErrors = workflowErrors,
    //        Id = 1
    //    };
    //    _registrationSvc
    //        .SubmitCandidateIntentionStatementForEfile(submission)
    //        .Returns(Task.FromResult(response));

    //    // Act
    //    var ex = Assert.ThrowsAsync<ValidationException>(async () =>
    //        await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
    //            _sut, "SubmitForm", new object[] { submission })
    //    );
    //    Assert.That(ex.Message, Is.EqualTo("Form submission failed"));

    //    var errors = PrivateMemberAccessor.GetPrivate<List<ApiError>>(_sut, "_errors");
    //    Assert.That(errors, Is.Not.Null.And.Not.Empty);
    //    Assert.Multiple(() =>
    //    {
    //        Assert.That(errors[0].ApiErrorField, Is.EqualTo("TestField"));
    //        Assert.That(errors[0].ApiErrorDescription, Is.EqualTo("Test error message"));
    //    });
    //    return Task.CompletedTask;
    //}

    ////TODO : SK : Commented out because the unit test was failing need to check with the team(Ryan)

    ///// <summary>
    ///// SubmitForm should throw if the returned RegistrationResponseDto.Id is null.
    ///// </summary>
    //[Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    //public void SubmitFormThrowsWhenFilingIdNotFound()
    //{
    //    // Arrange
    //    var submission = new CandidateIntentionStatementDto();
    //    var response = new RegistrationResponseDto
    //    {
    //        ValidationErrors = new List<WorkFlowError>(), // no validation errors
    //        Id = null
    //    };
    //    _registrationSvc
    //        .SubmitCandidateIntentionStatementForEfile(submission)
    //        .Returns(Task.FromResult(response));

    //    // Act & Assert
    //    var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
    //        await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
    //            _sut, "SubmitForm", new object[] { submission })
    //    );
    //    Assert.That(ex.Message, Is.EqualTo("Filing Id not found in Decisions response"));
    //}

    //// TODO : SK : Commented out because the unit test was failing need to check with the team(Ryan)
    ///// <summary>
    ///// SubmitForm should return the RegistrationResponseDto when there are no errors and a valid Id.
    ///// </summary>
    //[Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    //public async Task SubmitFormReturnsResponseWhenSuccessful()
    //{
    //    // Arrange
    //    var submission = new CandidateIntentionStatementDto();
    //    var expected = new RegistrationResponseDto
    //    {
    //        ValidationErrors = new List<WorkFlowError>(),
    //        Id = 42
    //    };
    //    _registrationSvc
    //        .SubmitCandidateIntentionStatementForEfile(submission)
    //        .Returns(Task.FromResult(expected));

    //    // Act
    //    var actual = await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
    //        _sut, "SubmitForm", new object[] { submission });

    //    // Assert
    //    Assert.That(actual, Is.SameAs(expected));
    //}

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitSlateMailerOrganizationFormShouldReturnResponseWhenSubmissionIsValid()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var expectedResponse = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _smoRegistrationSvc
            .SubmitSmoRegistrationForEfile(submission)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
            _sut, "SubmitSlateMailerOrganizationForm", [submission, false]);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitSlateMailerOrganizationTerminationFormShouldReturnResponseWhenSubmissionIsValid()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                TerminatedAt = DateTime.UtcNow
            }
        };

        var expectedResponse = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _smoRegistrationSvc
            .SmoTerminationForEfile(submission)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
            _sut, "SubmitSlateMailerOrganizationForm", [submission, true]);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitLobbyistReportFormShouldReturnResponseWhenSubmissionIsValid()
    {
        // Arrange
        var submission = new LobbyistReportDto
        {
            LobbyistReport = new LobbyistReport
            {
                FilerId = 123,
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var expectedResponse = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _filingSvc
            .SubmitLobbyistReportForEfile(submission)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
            _sut, "SubmitLobbyistReportForm", [submission]);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitSlateMailerOrganizationFormShouldThrowWhenRegistrationServiceReturnsNull()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        _smoRegistrationSvc
            .SubmitSmoRegistrationForEfile(submission)
            .Returns(Task.FromResult<RegistrationResponseDto>(null!));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitSlateMailerOrganizationForm", new object[] { submission, false })
        );

        Assert.That(ex.Message, Is.EqualTo("Received a null response from the registration service"));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitLobbyistReportFormShouldThrowWhenRegistrationServiceReturnsNull()
    {
        // Arrange
        var submission = new LobbyistReportDto
        {
            LobbyistReport = new LobbyistReport
            {
                FilerId = 123,
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        _filingSvc
            .SubmitLobbyistReportForEfile(submission)
            .Returns(Task.FromResult<RegistrationResponseDto>(null!));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitLobbyistReportForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Received a null response from the registration service"));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitSlateMailerOrganizationFormShouldThrowValidationExceptionWhenValidationErrorsExist()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var validationErrors = new List<WorkFlowError>
    {
        new("FieldName", "ErrorCode", "ErrorType", "Validation error message")
    };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _smoRegistrationSvc
            .SubmitSmoRegistrationForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<ValidationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitSlateMailerOrganizationForm", new object[] { submission, false })
        );

        Assert.That(ex.Message, Is.EqualTo("Form submission failed"));

        var errors = PrivateMemberAccessor.GetPrivate<List<ApiError>>(_sut, "_errors");
        Assert.That(errors, Is.Not.Null.And.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(errors[0].ApiErrorField, Is.EqualTo("FieldName"));
            Assert.That(errors[0].ApiErrorDescription, Is.EqualTo("Validation error message"));
        });
    }
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitSlateMailerOrganizationTerminationFormShouldThrowValidationExceptionWhenValidationErrorsExist()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var validationErrors = new List<WorkFlowError>
    {
        new("FieldName", "ErrorCode", "ErrorType", "Validation error message")
    };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _smoRegistrationSvc
            .SmoTerminationForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<ValidationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitSlateMailerOrganizationForm", new object[] { submission, true })
        );

        Assert.That(ex.Message, Is.EqualTo("Form submission failed"));

        var errors = PrivateMemberAccessor.GetPrivate<List<ApiError>>(_sut, "_errors");
        Assert.That(errors, Is.Not.Null.And.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(errors[0].ApiErrorField, Is.EqualTo("FieldName"));
            Assert.That(errors[0].ApiErrorDescription, Is.EqualTo("Validation error message"));
        });
    }



    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitLobbyistReportFormShouldThrowValidationExceptionWhenValidationErrorsExist()
    {
        // Arrange
        var submission = new LobbyistReportDto
        {
            LobbyistReport = new LobbyistReport
            {
                FilerId = 123,
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var validationErrors = new List<WorkFlowError>
    {
        new("FieldName", "ErrorCode", "ErrorType", "Validation error message")
    };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _filingSvc
            .SubmitLobbyistReportForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<ValidationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitLobbyistReportForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Form submission failed"));

        var errors = PrivateMemberAccessor.GetPrivate<List<ApiError>>(_sut, "_errors");
        Assert.That(errors, Is.Not.Null.And.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(errors[0].ApiErrorField, Is.EqualTo("FieldName"));
            Assert.That(errors[0].ApiErrorDescription, Is.EqualTo("Validation error message"));
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitSlateMailerOrganizationFormShouldThrowWhenFilingIdIsNull()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _smoRegistrationSvc
            .SubmitSmoRegistrationForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitSlateMailerOrganizationForm", new object[] { submission, false })
        );

        Assert.That(ex.Message, Is.EqualTo("Filing Id not found in Decisions response"));
    }


    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitSlateMailerOrganizationTerminationFormShouldThrowWhenFilingIdIsNull()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _smoRegistrationSvc
            .SmoTerminationForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitSlateMailerOrganizationForm", new object[] { submission, true })
        );

        Assert.That(ex.Message, Is.EqualTo("Filing Id not found in Decisions response"));
    }
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitLobbyistReportFormShouldThrowWhenFilingIdIsNull()
    {
        // Arrange
        var submission = new LobbyistReportDto
        {
            LobbyistReport = new LobbyistReport
            {
                FilerId = 123,
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _filingSvc
            .SubmitLobbyistReportForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitLobbyistReportForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Filing Id not found in Decisions response"));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitCandidateIntentionStatementFormShouldReturnResponseWhenSubmissionIsValid()
    {
        // Arrange
        var submission = new CandidateIntentionStatementDto
        {
            Registration = new CandidateIntentionStatement
            {
                Name = "Valid Candidate",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var expectedResponse = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _registrationSvc
            .SubmitCandidateIntentionStatementForEfile(submission)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
            _sut, "SubmitCandidateIntentionStatementForm", new object[] { submission });

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateIntentionStatementFormShouldThrowWhenRegistrationServiceReturnsNull()
    {
        // Arrange
        var submission = new CandidateIntentionStatementDto
        {
            Registration = new CandidateIntentionStatement
            {
                Name = "Invalid Candidate",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        _registrationSvc
            .SubmitCandidateIntentionStatementForEfile(submission)
            .Returns(Task.FromResult<RegistrationResponseDto>(null!));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitCandidateIntentionStatementForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Received a null response from the registration service"));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateIntentionStatementFormShouldThrowValidationExceptionWhenValidationErrorsExist()
    {
        // Arrange
        var submission = new CandidateIntentionStatementDto
        {
            Registration = new CandidateIntentionStatement
            {
                Name = "Invalid Candidate",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var validationErrors = new List<WorkFlowError>
    {
        new("FieldName", "ErrorCode", "ErrorType", "Validation error message")
    };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _registrationSvc
            .SubmitCandidateIntentionStatementForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<ValidationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitCandidateIntentionStatementForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Form submission failed"));

        var errors = PrivateMemberAccessor.GetPrivate<List<ApiError>>(_sut, "_errors");
        Assert.That(errors, Is.Not.Null.And.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(errors[0].ApiErrorField, Is.EqualTo("FieldName"));
            Assert.That(errors[0].ApiErrorDescription, Is.EqualTo("Validation error message"));
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateIntentionStatementFormShouldThrowWhenFilingIdIsNull()
    {
        // Arrange
        var submission = new CandidateIntentionStatementDto
        {
            Registration = new CandidateIntentionStatement
            {
                Name = "Invalid Candidate",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id
            }
        };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _registrationSvc
            .SubmitCandidateIntentionStatementForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitCandidateIntentionStatementForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Filing Id not found in Decisions response"));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitCandidateStatementShortFormShouldReturnResponseWhenSubmissionIsValid()
    {
        // Arrange
        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = new Models.FilerDisclosure.Filings.Filing
            {
                StatusId = RegistrationStatus.Draft.Id
            },
            Amendment = new CandidateCampaignStatementShortAmendment
            {
                IsAmendment = true
            },
            Attestation = new CandidateCampaignStatementShortAttestation
            {
                FirstName = "First",
                LastName = "Last",
                ExecutedAt = DateTime.UtcNow,
                Role = "Candidate",
                Signature = "signature"
            },
        };

        var expectedResponse = new ValidatedForm470ResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _form470Svc.SubmitCandidateStatementShortForEfile(submission).Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await PrivateMemberAccessor.InvokePrivateAsync<ValidatedForm470ResponseDto>(_sut, "SubmitCandidateStatementShortForm", [submission]);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateStatementShortFormShouldThrowWhenSubmitCandidateStatementShortForEfileReturnsNull()
    {
        // Arrange
        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = new Models.FilerDisclosure.Filings.Filing
            {
                StatusId = RegistrationStatus.Draft.Id
            },
            Amendment = new CandidateCampaignStatementShortAmendment
            {
                IsAmendment = true
            },
            Attestation = new CandidateCampaignStatementShortAttestation
            {
                FirstName = "First",
                LastName = "Last",
                ExecutedAt = DateTime.UtcNow,
                Role = "Candidate",
                Signature = "signature"
            },
        };

        _form470Svc.SubmitCandidateStatementShortForEfile(submission).Returns(Task.FromResult<ValidatedForm470ResponseDto>(null!));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<ValidatedForm470ResponseDto>(_sut, "SubmitCandidateStatementShortForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Received a null response from the registration service"));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateStatementShortFormShouldThrowValidationExceptionWhenValidationErrorsExist()
    {
        // Arrange
        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = new Models.FilerDisclosure.Filings.Filing
            {
                StatusId = RegistrationStatus.Draft.Id
            },
            Amendment = new CandidateCampaignStatementShortAmendment
            {
                IsAmendment = true
            },
            Attestation = new CandidateCampaignStatementShortAttestation
            {
                FirstName = "First",
                LastName = "Last",
                ExecutedAt = DateTime.UtcNow,
                Role = "Candidate",
                Signature = "signature"
            },
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("FieldName", "ErrorCode", "ErrorType", "Validation error message")
        };

        var response = new ValidatedForm470ResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _form470Svc.SubmitCandidateStatementShortForEfile(submission).Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<ValidationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<ValidatedForm470ResponseDto>(_sut, "SubmitCandidateStatementShortForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Form submission failed"));

        var errors = PrivateMemberAccessor.GetPrivate<List<ApiError>>(_sut, "_errors");
        Assert.That(errors, Is.Not.Null.And.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(errors[0].ApiErrorField, Is.EqualTo("FieldName"));
            Assert.That(errors[0].ApiErrorDescription, Is.EqualTo("Validation error message"));
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateStatementShortFormShouldThrowWhenFilingIdIsNull()
    {
        // Arrange
        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = new Models.FilerDisclosure.Filings.Filing
            {
                StatusId = RegistrationStatus.Draft.Id
            },
            Amendment = new CandidateCampaignStatementShortAmendment
            {
                IsAmendment = true
            },
            Attestation = new CandidateCampaignStatementShortAttestation
            {
                FirstName = "First",
                LastName = "Last",
                ExecutedAt = DateTime.UtcNow,
                Role = "Candidate",
                Signature = "signature"
            },
        };

        var response = new ValidatedForm470ResponseDto
        {
            Id = null,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _form470Svc.SubmitCandidateStatementShortForEfile(submission).Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<ValidatedForm470ResponseDto>(_sut, "SubmitCandidateStatementShortForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Filing Id not found in Decisions response"));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitLobbyistRegistrationFormShouldReturnResponseWhenSubmissionIsValid()
    {
        // Arrange
        var submission = new LobbyistRegistrationSubmissionDto()
        {
            Amendment = new EfileAmendment { SupercededFilingId = null },
            Attestation = new EfileAttestation(),
            IsSubmission = true,
            Lobbyist = new Lobbyist { Name = "UniqueName", StatusId = RegistrationStatus.Draft.Id },
            UserId = 20,
            LobbyistEmployerOrLobbyingFirmId = 100,
        };

        var expectedResponse = new RegistrationResponseDto
        {
            Id = 42,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _lobbyistRegistrationSvc.SubmitLobbyistRegistrationForEfile(submission).Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
            _sut, "SubmitLobbyistRegistrationForm", new object[] { submission });

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitLobbyistRegistrationFormShouldThrowValidationExceptionWhenValidationErrorsExist()
    {
        // Arrange
        var submission = new LobbyistRegistrationSubmissionDto()
        {
            Amendment = new EfileAmendment { SupercededFilingId = null },
            Attestation = new EfileAttestation(),
            IsSubmission = true,
            Lobbyist = new Lobbyist { Name = "UniqueName", StatusId = RegistrationStatus.Draft.Id },
            UserId = 20,
            LobbyistEmployerOrLobbyingFirmId = 100,
        };

        var validationErrors = new List<WorkFlowError>
    {
        new("FieldName", "ErrorCode", "ErrorType", "Validation error message")
    };

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _lobbyistRegistrationSvc
            .SubmitLobbyistRegistrationForEfile(submission)
            .Returns(Task.FromResult(response));

        // Act & Assert
        var ex = Assert.ThrowsAsync<ValidationException>(async () =>
            await PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _sut, "SubmitLobbyistRegistrationForm", new object[] { submission })
        );

        Assert.That(ex.Message, Is.EqualTo("Form submission failed"));

        var errors = PrivateMemberAccessor.GetPrivate<List<ApiError>>(_sut, "_errors");
        Assert.That(errors, Is.Not.Null.And.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(errors[0].ApiErrorField, Is.EqualTo("FieldName"));
            Assert.That(errors[0].ApiErrorDescription, Is.EqualTo("Validation error message"));
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task RunShouldProcessLobbyistRegistrationSuccessfully()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Lobbying-Certification-Lobbyist_Guid123\",\"ReceivedAt\":\"02/02/2024\"}"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 5,
            BlobFilename = "Lobbying-Certification-Lobbyist_Guid123",
            ReceivedAt = DateTime.UtcNow
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"lobbyist\": {\"Name\": \"Test Lobbyist\"}}")));

        // Simulate deserialization
        Substitute.ForPartsOf<EfileLobbyingCertificationLobbyist>();

        var submissionDto = new LobbyistRegistrationSubmissionDto();
        _submissionSvc.CreateLobbyistRegistrationSubmissionDto(Arg.Any<EfileLobbyingCertificationLobbyist>(), apiRequest)
            .Returns(submissionDto);

        var registrationResponse = new RegistrationResponseDto { Id = 789 };
        _lobbyistRegistrationSvc.SubmitLobbyistRegistrationForEfile(submissionDto).Returns(registrationResponse);

        // Act
        await _sut.Run(message, messageActions);

        // Assert
        await _apiRequestSvc.Received(1).AcceptRequest(apiRequest.Id, registrationResponse.Id?.ToString(CultureInfo.InvariantCulture) ?? "-1");
        await messageActions.Received(1).CompleteMessageAsync(message);
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task RunShouldThrowIfLobbyistRegistrationDeserializationFails()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Lobbying-Certification-Lobbyist_Guid123\",\"ReceivedAt\":\"02/02/2024\"}"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 6,
            BlobFilename = "Lobbying-Certification-Lobbyist_Guid123",
            ReceivedAt = DateTime.UtcNow
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(""))); // Empty content to force deserialization failure

        // Act
        await _sut.Run(message, messageActions);

        // Assert: Should call HandleFailure and CompleteMessageAsync
        await messageActions.Received(1).CompleteMessageAsync(message);
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task RunShouldHandleValidationExceptionFromLobbyistRegistration()
    {
        // Arrange
        var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
            body: BinaryData.FromString(/*lang=json,strict*/ "{\"BlobFilename\":\"Lobbying-Certification-Lobbyist_Guid123\",\"ReceivedAt\":\"02/02/2024\"}"));
        var messageActions = Substitute.For<ServiceBusMessageActions>();

        var apiRequest = new ApiRequest
        {
            Id = 7,
            BlobFilename = "Lobbying-Certification-Lobbyist_Guid123",
            ReceivedAt = DateTime.UtcNow
        };

        _apiRequestSvc.ProcessRequest(default).ReturnsForAnyArgs(apiRequest);
        _fileSvc.ReadFile(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new MemoryStream(Encoding.UTF8.GetBytes(/*lang=json,strict*/ "{\"lobbyist\": {\"Name\": \"Test Lobbyist\"}}")));

        var submissionDto = new LobbyistRegistrationSubmissionDto();
        _submissionSvc.CreateLobbyistRegistrationSubmissionDto(Arg.Any<EfileLobbyingCertificationLobbyist>(), apiRequest)
            .Returns(submissionDto);

        _lobbyistRegistrationSvc.SubmitLobbyistRegistrationForEfile(submissionDto)
            .Returns<Task<RegistrationResponseDto>>(x => throw new ValidationException("Form submission failed"));

        // Act
        await _sut.Run(message, messageActions);

        // Assert: Should call CompleteMessageAsync on validation error
        await messageActions.Received(1).CompleteMessageAsync(message);
    }


    #region Private Methods

    /// <summary>
    /// Builds the configuration object for testing from the local.settings.json file in the Efile project. 
    /// Because local.settings.json wraps all settings under a "Values" section, we extract that section
    /// to match the flattened key format expected by the Azure Functions runtime.
    /// </summary>
    /// <param name="overrides">List of settings to override the original values</param>
    private static IConfiguration BuildConfiguration(Dictionary<string, string?>? overrides = null)
    {
        string relativeSettingsPath = @"local.settings.json";
        string fullSettingsPath = Path.GetFullPath(relativeSettingsPath);
        var builder = new ConfigurationBuilder().AddJsonFile(fullSettingsPath);

        if (overrides?.Count > 0)
        {
            builder.AddInMemoryCollection(overrides);
        }

        return builder.Build();
    }

    #endregion
}
