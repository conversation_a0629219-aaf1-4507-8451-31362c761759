using System.Globalization;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Registrations;

[TestFixture]
public class DashboardQueryRepositoryTests
{
    private DatabaseContext _dbContext;
    private DashboardQueryRepository _repository;
    private long _testUserId;
    private IDateTimeSvc _dateTimeSvc;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseSqlite("DataSource=file::memory:?cache=shared;Foreign Keys=False")
            .Options;


        _dbContext = new DatabaseContext(options);

        // Ensure database is seeded
        _dbContext.Database.EnsureCreated();

        // Create User
        var user = new User()
        {
            FirstName = "John",
            LastName = "Wick",
            EmailAddress = "<EMAIL>",
            AddressList = new(),
            PhoneNumberList = new(),
            EmailAddressList = new(),
            EntraOid = "TestOid"
        };
        var user2 = new User()
        {
            FirstName = "John",
            LastName = "Wick",
            EmailAddress = "<EMAIL>",
            AddressList = new(),
            PhoneNumberList = new(),
            EmailAddressList = new(),
            EntraOid = "TestOid"
        };
        _dbContext.Users.Add(user);
        _dbContext.Users.Add(user2);

        // Draft Reg
        var addressList = BuildAddressList();
        var phoneList = BuildPhoneList();

        _dbContext.SaveChanges();
        _testUserId = user.Id;

        var statement1 =
            new CandidateIntentionStatement()
            {
                Name = "Jack Straw",
                StatusId = RegistrationStatus.Draft.Id,
                FirstName = "James",
                LastName = "Bond",
                MiddleName = "S",
                Email = "<EMAIL>",
                IsSameAsCandidateAddress = true,
                AddressList = addressList,
                PhoneNumberList = phoneList,
                Filer = new()
                {
                    Users = new()
                    {
                        new()
                        {
                            UserId = user.Id
                        }
                    }
                }
            };
        var entry = _dbContext.CandidateIntentionStatements.Add(statement1);
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _dateTimeSvc.GetCurrentDateTime().Returns(date);

        // add AuditLog Entry
        var entryAction = new DataAction(entry, date);
        _dbContext.AuditLogs.Add(
            new AuditLog(entryAction, _dateTimeSvc, user.Id)
        );

        var statement3 =
            new CandidateIntentionStatement()
            {
                Name = "Justin Straw",
                StatusId = RegistrationStatus.Draft.Id,
                FirstName = "James",
                LastName = "Bond",
                MiddleName = "S",
                Email = "<EMAIL>",
                IsSameAsCandidateAddress = true,
                AddressList = addressList,
                PhoneNumberList = phoneList
            };

        var entry3 = _dbContext.CandidateIntentionStatements.Add(statement3);

        // add AuditLog Entry
        var entry3Action = new DataAction(entry3, date);
        _dbContext.AuditLogs.Add(
            new AuditLog(entry3Action, _dateTimeSvc, user2.Id)
        );

        // Canceled Reg
        addressList = BuildAddressList();
        phoneList = BuildPhoneList();

        _dbContext.SaveChanges();


        var statement2 =
            new CandidateIntentionStatement()
            {
                Name = "Jenny Straw",
                StatusId = RegistrationStatus.Canceled.Id,
                FirstName = "Jenny",
                LastName = "Straw",
                MiddleName = "S",
                Email = "<EMAIL>",
                IsSameAsCandidateAddress = true,
                AddressList = addressList,
                PhoneNumberList = phoneList
            };


        var canceledEntry = _dbContext.CandidateIntentionStatements.Add(statement2);
        // add AuditLog Entry
        var canceledAction = new DataAction(canceledEntry, date);
        _dbContext.AuditLogs.Add(
            new AuditLog(canceledAction, _dateTimeSvc, user.Id)
        );

        _dbContext.SaveChanges();

        _repository = new DashboardQueryRepository(_dbContext);
    }

    [TearDown]
    public void TearDown()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }

    [Test]
    public async Task GetRegistrationsAsync_ShouldReturnExpectedData()
    {
        // Act
        var result = await _repository.GetDashboardRegistrationsForUserAsync(_testUserId).ToListAsync();
        Assert.Multiple(() =>
        {
            var registrations = _dbContext.Registrations.ToList();
            Assert.That(registrations, Has.Count.EqualTo(3));

            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result[0].Name, Is.EqualTo("Jack Straw"));
            Assert.That(result[0].StatusId, Is.EqualTo(RegistrationStatus.Draft.Id.ToString(CultureInfo.InvariantCulture)));
        });
    }

    private AddressList BuildAddressList()
    {
        var addressList = new AddressList()
        {
            Addresses =
            [
                new Address
                {
                    Type = "Residential",
                    City = "City",
                    Purpose = "Candidate",
                    Country = "USA",
                    State = "PA",
                    Street = "Street",
                    Zip = "19355"
                }
            ]
        };
        _dbContext.AddressLists.Add(addressList);
        return addressList;
    }

    private PhoneNumberList BuildPhoneList()
    {
        var phoneList = new PhoneNumberList()
        {
            PhoneNumbers = [new PhoneNumber { Number = "1234567890", Type = "Home", }]
        };
        _dbContext.PhoneNumberLists.Add(phoneList);
        return phoneList;
    }
}
