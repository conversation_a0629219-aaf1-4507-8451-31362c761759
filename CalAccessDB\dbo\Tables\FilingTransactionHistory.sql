﻿CREATE TABLE [dbo].[FilingTransactionHistory] (
    [Id]            BIGINT        NOT NULL,
    [FilingId]      BIGINT        NOT NULL,
    [TransactionId] BIGINT        NOT NULL,
    [CreatedBy]     BIGINT        NOT NULL,
    [ModifiedBy]    BIGINT        NOT NULL,
    [PeriodEnd]     DATETIME2 (7) NOT NULL,
    [PeriodStart]   DATETIME2 (7) NOT NULL,
    [Active]        BIT           NOT NULL
);






GO
CREATE CLUSTERED INDEX [ix_FilingTransactionHistory]
    ON [dbo].[FilingTransactionHistory]([PeriodEnd] ASC, [PeriodStart] ASC) WITH (DATA_COMPRESSION = PAGE);

