using NSubstitute;
using SendGrid.Helpers.Errors.Model;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Transactions;

/// <summary>
/// Unit tests for the <see cref="ActivityExpenseSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(ActivityExpenseSvc))]
public sealed class ActivityExpenseSvcTest
{
    private IActivityExpenseRepository _activityExpenseRepository;
    private ITransactionRepository _transactionRepository;
    private ActivityExpenseSvc _activityExpenseSvc;
    private IFilingSvc _filingSvc;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _activityExpenseRepository = Substitute.For<IActivityExpenseRepository>();
        _transactionRepository = Substitute.For<ITransactionRepository>();
        _filingSvc = Substitute.For<IFilingSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _activityExpenseSvc = new ActivityExpenseSvc(_activityExpenseRepository, _transactionRepository, _filingSvc);
    }

    private static ActivityExpense NewActivityExpense()
    {
        return new ActivityExpense
        {
            FilerId = 1,
            ActivityExpenseTypeId = 1,
            Amount = (Currency)100.00m,
            Notes = "Test Activity Expense",
            TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        };
    }


    [Test]
    public async Task CreateActivityExpense_WithValidData_CreatesExpenseAndReturnsIt()
    {
        // Arrange
        var dto = new ActivityExpenseDto
        {
            TransactionDate = _dateNow,
            Notes = "Test expense",
            ActivityExpenseTypeId = 1,
            MonetaryTypeId = 2,
            Amount = 100.00m,
            FilerId = 123,
            ContactId = 456,
            FilingId = 789
        };

        var expectedExpense = new ActivityExpense
        {
            Id = 1,
            TransactionDate = dto.TransactionDate,
            Notes = dto.Notes,
            ActivityExpenseTypeId = dto.ActivityExpenseTypeId,
            MonetaryTypeId = dto.MonetaryTypeId,
            Amount = (Currency)dto.Amount,
            FilerId = dto.FilerId,
            ContactId = dto.ContactId
        };

        _activityExpenseRepository.Create(Arg.Any<ActivityExpense>())
            .Returns(expectedExpense);

        _filingSvc.OnTransactionCreated(expectedExpense, 789).Returns(Task.CompletedTask);

        // Act
        var result = await _activityExpenseSvc.CreateActivityExpense(dto);

        // Assert
        await _activityExpenseRepository.Received(1).Create(Arg.Is<ActivityExpense>(x =>
            x.TransactionDate == dto.TransactionDate &&
            x.Notes == dto.Notes &&
            x.ActivityExpenseTypeId == dto.ActivityExpenseTypeId &&
            x.MonetaryTypeId == dto.MonetaryTypeId &&
            x.Amount == (Currency)dto.Amount &&
            x.FilerId == dto.FilerId &&
            x.ContactId == dto.ContactId));

        await _transactionRepository.Received(1).AddTransactionToFiling(expectedExpense.Id, dto.FilingId);

        Assert.That(result, Is.EqualTo(expectedExpense));
    }

    [Test]
    public async Task CreateActivityExpense_WithFilerContacts_AddsContactsToTransaction()
    {
        // Arrange
        var dto = new ActivityExpenseDto
        {
            TransactionDate = _dateNow,
            ActivityExpenseTypeId = 1,
            MonetaryTypeId = 2,
            Amount = 100.00m,
            FilerId = 123,
            ContactId = 456,
            FilingId = 789,
            TransactionReportablePersons = new List<TransactionReportablePersonDto>
            {
                new() { FilerContactId = 1 },
            },
        };

        var createdExpense = NewActivityExpense();
        createdExpense.Id = 1;
        _activityExpenseRepository.Create(Arg.Any<ActivityExpense>())
            .Returns(createdExpense);

        _filingSvc.OnTransactionCreated(createdExpense, 789).Returns(Task.CompletedTask);

        // Act
        await _activityExpenseSvc.CreateActivityExpense(dto);

        // Assert
        await _activityExpenseRepository.Received(1).AddReportablePersonsToTransaction(
            createdExpense.Id,
            Arg.Is<List<TransactionReportablePersonDto>>(x =>
                x.Count == 1 &&
                x.First().FilerContactId == 1));
    }

    [Test]
    public async Task GetActivityExpenseForUpdate_ReturnsDto_WhenRepositoryReturnsData()
    {
        // Arrange
        var testId = 1L;
        var expectedDto = new ActivityExpenseDto
        {
            Id = testId,
            FilerId = 123,
            FilingId = 456,
            Notes = "Test Expense",
            TransactionReportablePersons = new List<TransactionReportablePersonDto>()
        };

        _activityExpenseRepository.GetActivityExpenseForUpdate(testId)
                 .Returns(expectedDto);

        // Act
        var result = await _activityExpenseRepository.GetActivityExpenseForUpdate(testId);

        // Assert
        Assert.That(result, Is.EqualTo(expectedDto));
        await _activityExpenseRepository.Received(1).GetActivityExpenseForUpdate(testId);
    }

    [Test]
    public async Task GetActivityExpenseForUpdate_ReturnsNull_WhenRepositoryReturnsNull()
    {
        // Arrange
        var testId = 999L;
        _activityExpenseRepository.GetActivityExpenseForUpdate(testId)
                 .Returns((ActivityExpenseDto?)null);

        // Act
        var result = await _activityExpenseRepository.GetActivityExpenseForUpdate(testId);

        // Assert
        Assert.That(result, Is.Null);
        await _activityExpenseRepository.Received(1).GetActivityExpenseForUpdate(testId);
    }

    [Test]
    public async Task UpdateActivityExpense_WithValidData_UpdatesAndReturnsExpense()
    {
        // Arrange
        var expenseId = 1L;
        var filingId = 789L;
        var originalExpense = new ActivityExpense
        {
            Id = expenseId,
            Notes = "Original notes",
            Amount = (Currency)50.00m,
            TransactionDate = _dateNow.AddDays(-1)
        };

        var dto = new ActivityExpenseDto
        {
            Id = expenseId,
            FilingId = filingId,
            Notes = "Updated notes",
            Amount = 100.00m,
            TransactionDate = _dateNow,
            ActivityExpenseTypeId = 2,
            MonetaryTypeId = 3,
            FilerId = 123,
            ContactId = 456
        };

        _activityExpenseRepository.FindById(expenseId).Returns(originalExpense);
        _activityExpenseRepository.Update(Arg.Any<ActivityExpense>()).Returns(ci => ci.Arg<ActivityExpense>());
        _filingSvc.OnTransactionUpdated(Arg.Any<ActivityExpense>(), Arg.Any<ActivityExpense>(), filingId)
                 .Returns(Task.CompletedTask);

        // Act
        var result = await _activityExpenseSvc.UpdateActivityExpense(dto);

        // Assert
        await _activityExpenseRepository.Received(1).Update(Arg.Is<ActivityExpense>(x =>
            x.Id == expenseId &&
            x.Notes == dto.Notes &&
            x.Amount == (Currency)dto.Amount &&
            x.TransactionDate == dto.TransactionDate &&
            x.ActivityExpenseTypeId == dto.ActivityExpenseTypeId &&
            x.MonetaryTypeId == dto.MonetaryTypeId &&
            x.FilerId == dto.FilerId &&
            x.ContactId == dto.ContactId));

        await _filingSvc.Received(1).OnTransactionUpdated(
            Arg.Any<ActivityExpense>(),
            Arg.Any<ActivityExpense>(),
            filingId);

        Assert.That(result.Notes, Is.EqualTo(dto.Notes));
    }

    [Test]
    public void UpdateActivityExpense_ThrowsBadRequest_WhenIdIsMissing()
    {
        // Arrange
        var dto = new ActivityExpenseDto { Id = null };

        // Act & Assert
        var ex = Assert.ThrowsAsync<BadRequestException>(() =>
            _activityExpenseSvc.UpdateActivityExpense(dto));
        Assert.That(ex.Message, Is.EqualTo("ID is required for updates"));
    }

    [Test]
    public void UpdateActivityExpense_ThrowsNotFound_WhenExpenseDoesntExist()
    {
        // Arrange
        var dto = new ActivityExpenseDto { Id = 999 };
        _activityExpenseRepository.FindById(999).Returns((ActivityExpense?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<NotFoundException>(() =>
            _activityExpenseSvc.UpdateActivityExpense(dto));
        Assert.That(ex.Message, Is.EqualTo("Activity expense with ID 999 not found"));
    }
}
