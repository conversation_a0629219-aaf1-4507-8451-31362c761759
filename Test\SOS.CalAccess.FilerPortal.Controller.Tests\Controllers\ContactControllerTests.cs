using System.Net;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Moq;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;
using static SOS.CalAccess.FilerPortal.Controllers.ContactController;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
using TransactionReportablePerson = SOS.CalAccess.Models.FilerDisclosure.Contacts.TransactionReportablePerson;


namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class ContactControllerTests
{
    #region Setup

    private Mock<IFirmPaymentTransactionCtlSvc> _firmPaymentTransactionCtlSvcMock;
    private Mock<IRegistrationsApi> _registrationsApiMock;
    private Mock<IActivityExpenseApi> _activityExpenseApiMock;
    private Mock<IReferenceDataApi> _referenceDataApiMock;
    private Mock<ITempDataDictionaryFactory> _tempDataFactoryMock;
    private Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private Mock<IToastService> _toastService;
    private Mock<IStringLocalizer<SharedResources>> _localizerMock;
    private Mock<IContactsApi> _contactsApiMock;
    private ContactController _controller;

    [SetUp]
    public void Setup()
    {
        _registrationsApiMock = new Mock<IRegistrationsApi>();
        _activityExpenseApiMock = new Mock<IActivityExpenseApi>();
        _referenceDataApiMock = new Mock<IReferenceDataApi>();
        _firmPaymentTransactionCtlSvcMock = new Mock<IFirmPaymentTransactionCtlSvc>();

        _tempDataFactoryMock = new Mock<ITempDataDictionaryFactory>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        var tempDataMock = new Mock<ITempDataDictionary>();
        var httpContextMock = new Mock<HttpContext>();

        _tempDataFactoryMock.Setup(x => x.GetTempData(httpContextMock.Object)).Returns(tempDataMock.Object);
        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

        // Mock the keys for Toast success/danger/warning
        tempDataMock.Setup(td => td["ToastType"]).Returns("");
        tempDataMock.Setup(td => td["ToastMessage"]).Returns("");
        tempDataMock.Setup(td => td["ToastShowCloseButton"]).Returns("");
        tempDataMock.Setup(td => td["ToastX"]).Returns("");
        tempDataMock.Setup(td => td["ToastY"]).Returns("");
        tempDataMock.Setup(td => td["ToastTimeOut"]).Returns("");

        _toastService = new Mock<IToastService>();

        _localizerMock = new Mock<IStringLocalizer<SharedResources>>();
        _contactsApiMock = new Mock<IContactsApi>();

        _controller = new ContactController(_toastService.Object, _localizerMock.Object, _contactsApiMock.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #endregion

    #region Create
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
    [Test]
    public void Create_InvalidModel_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");
        long filerId = 1;
        long filingId = 1;

        // Act
        var result = _controller.Create(filerId, filingId);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void Create_Get_ValidModel_ShouldShowContactFormView()
    {
        // Arrange
        long filerId = 1;
        long filingId = 1;

        // Act
        var result = _controller.Create(filerId, filingId) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactForm"), "Expected ContactFormView or default view.");
            Assert.That(result.Model, Is.InstanceOf<GenericContactViewModel>(), "Expected GenericContactViewModel.");
        });
    }

    [Test]
    public async Task Create_InvalidModel_ShouldStayOnCreate()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            FilerId = 123,
            TypeId = InteropConstants.FilerContactType.Individual,
            ReturnUrl = "/return",
            Context = "Context"
        };

        _controller.ModelState.AddModelError("Error", "Invalid model state");
        _ = _contactsApiMock.Setup(api => api.CreatePayee(It.IsAny<long>(), It.IsAny<UpsertPayeeRequest>(), It.IsAny<CancellationToken>()));

        // Act
        var result = await _controller.Create(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactForm"));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(result.ViewData["IsEditing"], Is.False, "Expected ViewBag.IsEditing to be false.");
        });
    }

    [Test]
    public async Task Create_ApiCallFail_ShouldRedirectToCreate()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            FilerId = 123,
            TypeId = InteropConstants.FilerContactType.Individual,
            ReturnUrl = "/return",
            Country = "United States",
            City = "Test City",
            State = "HI",
            Street = "123 Street Ave.",
            Street2 = "APT A",
            FirstName = "Testfirstname",
            MiddleName = "Testmiddlename",
            LastName = "Testlastname",
            ZipCode = "12345",
            OrganizationName = null,
        };

        var workflowErrors = new List<WorkFlowError> { new("Email", "ErrGlobal0001", "Validation", "{{Field Name}} is required.") };
        var jsonContent = JsonConvert.SerializeObject(workflowErrors);

        var responseMessage = new HttpResponseMessage(HttpStatusCode.UnprocessableEntity)
        {
            Content = new StringContent(jsonContent)
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            responseMessage,
            new RefitSettings());

        _contactsApiMock
            .Setup(api => api.CreatePayee(It.IsAny<long>(), It.IsAny<UpsertPayeeRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Act
        var result = await _controller.Create(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactForm"));
            Assert.That(result.Model, Is.EqualTo(model));

            var typedModel = result.Model as GenericContactViewModel;
            Assert.That(typedModel, Is.Not.Null);
            Assert.That(typedModel!.Messages, Is.Not.Null);
            Assert.That(result.ViewData["IsEditing"], Is.False, "Expected ViewBag.IsEditing to be false.");
        });
    }

    [Test]
    public async Task Create_ApiCallSuccess_ShouldRedirectToNewActivityExpense()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            FilerId = 123,
            TypeId = InteropConstants.FilerContactType.Organization,
            ReturnUrl = "/return",
            Country = "United States",
            City = "Test City",
            State = "HI",
            Street = "123 Street Ave.",
            Street2 = "APT A",
            ZipCode = "12345",
            OrganizationName = "Yes",
            Context = "ActivityExpense"
        };

        _ = _contactsApiMock
            .Setup(api => api.CreatePayee(It.IsAny<long>(), It.IsAny<UpsertPayeeRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
                , 1
                , 1
                , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , "12345"));

        // Act
        var result = await _controller.Create(model, It.IsAny<CancellationToken>()) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("NewActivityExpense"));
            Assert.That(result.ControllerName, Is.EqualTo("Transaction"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(result.RouteValues["returnUrl"], Is.EqualTo(model.ReturnUrl));
            Assert.That(result.RouteValues["contactId"], Is.EqualTo(1));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(model.ReportType));
        });
    }

    [Test]
    public async Task Create_ApiCallSuccess_ShouldRedirectToNewOtherPayment()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            FilerId = 123,
            TypeId = InteropConstants.FilerContactType.Organization,
            ReturnUrl = "/return",
            Country = "United States",
            City = "Test City",
            State = "HI",
            Street = "123 Street Ave.",
            Street2 = "APT A",
            ZipCode = "12345",
            OrganizationName = "Yes",
            Context = "OtherPayment"
        };

        _ = _contactsApiMock
            .Setup(api => api.CreatePayee(It.IsAny<long>(), It.IsAny<UpsertPayeeRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
                , 1
                , 1
                , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , "12345"));

        // Act
        var result = await _controller.Create(model, It.IsAny<CancellationToken>()) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("NewOtherPayment"));
            Assert.That(result.ControllerName, Is.EqualTo("Transaction"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(result.RouteValues["returnUrl"], Is.EqualTo(model.ReturnUrl));
            Assert.That(result.RouteValues["contactId"], Is.EqualTo(1));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(model.ReportType));
        });
    }

    #endregion

    #region Edit

    [Test]
    public async Task Edit_InvalidGet_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.Edit("1");

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Edit_ValidGet_ShouldReturnContactView_ForIndividualContact()
    {
        // Arrange
        var transactionId = 1;
        var contactId = "123";
        var returnUrl = "/return";
        var context = "sampleContext";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();

        var individualResponse = new IndividualContactResponse(
            1,
            "Test City",
            "US",
            emails,
            "Employer",
            123,
            "Testfirstname",
            123,
            "Testlastname",
            "Testmiddlename",
            "Occupation",
            phoneNumbers,
            "HI",
            "street",
            "street2",
            InteropConstants.FilerContactType.Individual,
            true,
            workflowErrors,
            "www.website.com",
            "12345");

        _ = _contactsApiMock.Setup(api => api.GetContact(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualResponse);

        // Act
        var result = await _controller.Edit(contactId, returnUrl, context, transactionId);
        var viewResult = result as ViewResult;
        var viewModel = viewResult!.Model as GenericContactViewModel;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("ContactForm"));
            Assert.That(viewResult.Model, Is.InstanceOf<GenericContactViewModel>());
            Assert.That(viewModel!.ReturnUrl, Is.EqualTo(returnUrl));
            Assert.That(viewModel!.Context, Is.EqualTo(context));
            Assert.That(_controller.ViewBag.IsEditing, Is.EqualTo(true));
        });
    }

    [Test]
    public async Task Edit_ValidGet_ShouldReturnContactView_ForOrganizationContact()
    {
        // Arrange
        var transactionId = 1;
        var contactId = "123";
        var returnUrl = "/return";
        var context = "sampleContext";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var individualResponse = new OrganizationContactResponse(
            1,
            "Test City",
            "US",
            emails,
            123,
            123,
            "Organization Name",
            phoneNumbers,
            "HI",
            "street",
            "street2",
            InteropConstants.FilerContactType.Organization,
            true,
            workflowErrors,
            "www.website.com",
            "12345");

        _ = _contactsApiMock.Setup(api => api.GetContact(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualResponse);

        // Act
        var result = await _controller.Edit(contactId, returnUrl, context, transactionId);
        var viewResult = result as ViewResult;
        var viewModel = viewResult!.Model as GenericContactViewModel;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("ContactForm"));
            Assert.That(viewResult.Model, Is.InstanceOf<GenericContactViewModel>());
            Assert.That(viewModel!.ReturnUrl, Is.EqualTo(returnUrl));
            Assert.That(viewModel!.Context, Is.EqualTo(context));
            Assert.That(_controller.ViewBag.IsEditing, Is.EqualTo(true));
        });
    }

    [Test]
    public async Task Edit_ValidGet_ShouldReturnContactView_ForCommitteeContact()
    {
        // Arrange
        var contactId = "123";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var individualResponse = new CommitteeContactResponse(
            1,
            "Test City",
            "Committee Name",
            "Committee Type",
            "US",
            emails,
            123,
            123,
            phoneNumbers,
            "HI",
            "street",
            "street2",
            InteropConstants.FilerContactType.Committee,
            true,
            workflowErrors,
            "www.website.com",
            "12345");

        _ = _contactsApiMock.Setup(api => api.GetContact(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individualResponse);

        // Act
        var result = await _controller.Edit(contactId);
        var viewResult = result as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("ContactForm"));
            Assert.That(viewResult.Model, Is.InstanceOf<CommitteeContactViewModel>());
            Assert.That(_controller.ViewBag.IsEditing, Is.EqualTo(true));
        });
    }

    [Test]
    public async Task Edit_InvalidPost_ShouldReturnNotFound()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            FilerId = 123,
            TypeId = InteropConstants.FilerContactType.Individual,
            ReturnUrl = "/return"
        };
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.Edit(model) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Edit_PostFails_ShouldReturnViewWithModelErrors()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            Id = 456,
            FilerId = 123,
            AddressId = 1,
            TypeId = InteropConstants.FilerContactType.Individual,
            ReturnUrl = "/return"
        };

        var workflowErrors = new List<WorkFlowError>
        {
            new("Email", "ErrGlobal0001", "Validation", "Email is required.")
        };
        var jsonContent = JsonConvert.SerializeObject(workflowErrors);

        var responseMessage = new HttpResponseMessage(HttpStatusCode.UnprocessableEntity)
        {
            Content = new StringContent(jsonContent)
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            responseMessage,
            new RefitSettings());

        _ = _contactsApiMock
            .Setup(api => api.GetFilerContactById(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactResponseDto([], [], 123, 456, [], InteropConstants.FilerContactType.Individual));

        _ = _contactsApiMock
            .Setup(api => api.UpdateFilerContact(It.IsAny<long>(), It.IsAny<UpsertFilerContactRequest>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Act
        var result = await _controller.Edit(model) as ViewResult;
        var typedModel = result!.Model as GenericContactViewModel;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactForm"));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(typedModel!.Messages, Is.Not.Null);
        });
    }

    [Test]
    public async Task Edit_PostFailForContactTypeUpdate_ShouldReturnViewWithModelErrors()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            Id = 456,
            FilerId = 123,
            AddressId = 1,
            TypeId = InteropConstants.FilerContactType.Organization,
            ReturnUrl = "/return"
        };

        var workflowErrors = new List<WorkFlowError>
        {
            new("Email", "ErrGlobal0001", "Validation", "Organization Name is required.")
        };

        _ = _contactsApiMock
            .Setup(api => api.GetFilerContactById(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactResponseDto([], [], 123, 456, [], InteropConstants.FilerContactType.Individual));

        _ = _contactsApiMock
            .Setup(api => api.CreateFilerContact(It.IsAny<long>(), It.IsAny<UpsertFilerContactRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactItemResponse(1, "", "", [], 123, 457, [], "", "", "", InteropConstants.FilerContactType.Organization, false, workflowErrors, "", ""));

        // Act
        var result = await _controller.Edit(model) as ViewResult;
        var typedModel = result!.Model as GenericContactViewModel;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactForm"));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(typedModel!.Messages, Is.Not.Null);
            Assert.That(result.ViewData["IsEditing"], Is.True, "Expected ViewBag.IsEditing to be true.");
        });
    }

    [Test]
    public async Task Edit_PostSuccess_ShouldRedirectToNewActivityExpense()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            Id = 456,
            FilerId = 123,
            AddressId = 1,
            TypeId = InteropConstants.FilerContactType.Organization,
            ReturnUrl = "/return",
            Context = "ActivityExpense"
        };

        var fakeResponse = new ContactItemResponse(
           addressId: 1,
           city: "New York",
           country: "US",
           emailAddresses: [],
           filerId: 12345,
           id: 1,
           phoneNumbers: [],
           state: "NY",
           street: "123 Main St",
           street2: "Apt 4B",
           typeId: 5,
           valid: true,
           validationErrors: [],
           website: "http://example.com",
           zipCode: "10001"
       );

        _ = _contactsApiMock
            .Setup(api => api.GetFilerContactById(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactResponseDto([], [], 123, 456, [], InteropConstants.FilerContactType.Organization));

        _contactsApiMock
            .Setup(api => api.UpdateFilerContact(It.IsAny<long>(), It.IsAny<UpsertFilerContactRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fakeResponse);


        // Act
        var result = await _controller.Edit(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("NewActivityExpense"));
            Assert.That(result.ControllerName, Is.EqualTo("Transaction"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(result.RouteValues["returnUrl"], Is.EqualTo(model.ReturnUrl));
        });
    }

    [Test]
    public async Task Edit_PostSuccess_ShouldRedirectToNewOtherPayment()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            Id = 456,
            FilerId = 123,
            AddressId = 1,
            TypeId = InteropConstants.FilerContactType.Organization,
            ReturnUrl = "/return",
            Context = "OtherPayment",
            TransactionId = 1
        };

        var fakeResponse = new ContactItemResponse(
           addressId: 1,
           city: "New York",
           country: "US",
           emailAddresses: [],
           filerId: 12345,
           id: 1,
           phoneNumbers: [],
           state: "NY",
           street: "123 Main St",
           street2: "Apt 4B",
           typeId: 5,
           valid: true,
           validationErrors: [],
           website: "http://example.com",
           zipCode: "10001"
       );

        _ = _contactsApiMock
            .Setup(api => api.GetFilerContactById(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactResponseDto([], [], 123, 456, [], InteropConstants.FilerContactType.Organization));

        _contactsApiMock
            .Setup(api => api.UpdateFilerContact(It.IsAny<long>(), It.IsAny<UpsertFilerContactRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fakeResponse);

        // Act
        var result = await _controller.Edit(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("NewOtherPayment"));
            Assert.That(result.ControllerName, Is.EqualTo("Transaction"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(result.RouteValues["returnUrl"], Is.EqualTo(model.ReturnUrl));
        });
    }

    [Test]
    public async Task Edit_PostSuccess_WithEditActivityExpenseContext_ShouldRedirectToEditActivityExpense()
    {
        // Arrange
        var model = new GenericContactViewModel
        {
            Id = 456,
            FilerId = 123,
            AddressId = 1,
            TypeId = InteropConstants.FilerContactType.Organization,
            ReturnUrl = "/return",
            Context = "EditActivityExpense/1"
        };

        var fakeResponse = new ContactItemResponse(
           addressId: 1,
           city: "New York",
           country: "US",
           emailAddresses: [],
           filerId: 12345,
           id: 1,
           phoneNumbers: [],
           state: "NY",
           street: "123 Main St",
           street2: "Apt 4B",
           typeId: 5,
           valid: true,
           validationErrors: [],
           website: "http://example.com",
           zipCode: "10001"
       );

        _ = _contactsApiMock
            .Setup(api => api.GetFilerContactById(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ContactResponseDto([], [], 123, 456, [], InteropConstants.FilerContactType.Individual));

        _contactsApiMock
            .Setup(api => api.UpdateFilerContact(It.IsAny<long>(), It.IsAny<UpsertFilerContactRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fakeResponse);

        _contactsApiMock
            .Setup(api => api.CreateFilerContact(It.IsAny<long>(), It.IsAny<UpsertFilerContactRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fakeResponse);

        // Act
        var result = await _controller.Edit(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("EditActivityExpense"));
            Assert.That(result.ControllerName, Is.EqualTo("Transaction"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    #endregion

    #region View
    [Test]
    public async Task View_InvalidModel_ShouldReturnNotFound()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Error", "Invalid model state");
        _contactsApiMock.Setup(api => api.GetContact(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync((CommitteeContactResponse?)null);

        // Act
        var result = await _controller.View(id);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task View_ValidCommitteeModel_ShouldReturnCommitteeView()
    {
        // Arrange
        long id = 1;
        var response = new CommitteeContactResponse(
            addressId: 1001,
            city: "Sacramento",
            committeeName: "Sample Committee",
            committeeType: "Lobbyist",
            country: "USA",
            emailAddresses: new List<EmailAddress> { },
            filerId: 2001,
            id: 3001,
            phoneNumbers: new List<PhoneNumber> { },
            state: "CA",
            street: "123 Main St",
            street2: "Suite 200",
            typeId: 4001,
            valid: true,
            validationErrors: new List<WorkFlowError>(),
            website: "https://www.committee.org",
            zipCode: "95814"
        );
        _contactsApiMock.Setup(api => api.GetContact(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.View(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected ViewResult for valid committee model.");
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactDetailsView"), "Expected ContactDetailsView or default view.");
            Assert.That(result.Model, Is.InstanceOf<CommitteeContactViewModel>(), "Expected CommitteeContactViewModel.");
        });
    }
    [Test]
    public async Task View_ValidIndividualModel_ShouldReturnIndividualView()
    {
        // Arrange
        long id = 1;
        var response = new IndividualContactResponse(
            addressId: 1002,
            city: "Los Angeles",
            country: "USA",
            emailAddresses: new List<EmailAddress> { },
            employer: "Acme Corp",
            filerId: 2002,
            firstName: "John",
            id: 3002,
            lastName: "Doe",
            middleName: "Michael",
            occupation: "Consultant",
            phoneNumbers: new List<PhoneNumber> { },
            state: "CA",
            street: "456 Oak St",
            street2: "Apt 101",
            typeId: 4002,
            valid: true,
            validationErrors: new List<WorkFlowError>(),
            website: "https://www.johndoe.com",
            zipCode: "90001"
        );
        _contactsApiMock.Setup(api => api.GetContact(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.View(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected ViewResult for valid individual model.");
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactDetailsView"), "Expected ContactDetailsView or default view.");
            Assert.That(result.Model, Is.InstanceOf<IndividualContactViewModel>(), "Expected IndividualContactViewModel.");
        });
    }

    [Test]
    public async Task View_ValidOrganizationModel_ShouldReturnOrganizationView()
    {
        // Arrange
        long id = 1;
        var response = new OrganizationContactResponse(
            addressId: 123,
            city: "New York",
            country: "USA",
            emailAddresses: new List<EmailAddress> { },
            filerId: 456,
            id: 789,
            organizationName: "Acme Corp",
            phoneNumbers: new List<PhoneNumber> { },
            state: "NY",
            street: "123 Main St",
            street2: null,
            typeId: 1,
            valid: true,
            validationErrors: new List<WorkFlowError>(),
            website: "www.acmecorp.com",
            zipCode: "10001"
        );
        _contactsApiMock.Setup(api => api.GetContact(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.View(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected ViewResult for valid organization model.");
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ContactDetailsView"), "Expected ContactDetailsView or default view.");
            Assert.That(result.Model, Is.InstanceOf<OrganizationContactViewModel>(), "Expected OrganizationContactViewModel.");
        });
    }

    #endregion View

    #region Reportable Person

    [Test]
    public async Task CreateReportablePerson_Get_InvalidModel_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Test error");

        // Act
        var result = await _controller.CreateReportablePerson(null, null, null, null, null, _referenceDataApiMock.Object) as NotFoundResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task CreateReportablePerson_Get_ValidModel_ReturnsViewWithViewModel()
    {
        // Arrange
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        _ = httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        var activityExpenseData = /*lang=json,strict*/ """{"Property1":"Value1", "Property2": "Value2"}""";
        var byteArray = Encoding.UTF8.GetBytes(activityExpenseData);

        _ = sessionMock.Setup(s => s.TryGetValue("ActivityExpenseData", out byteArray)).Returns(true);

        _ = _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
                new(0L, 1L, 0L, "Position")
            });

        _ = _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
                new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.CreateReportablePerson(null, null, null, null, null, _referenceDataApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.InstanceOf<ReportablePersonViewModel>());
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(false));

            var model = result.Model as ReportablePersonViewModel;
            Assert.That(model!.Agencies!.Any(x => x.Value == "Agency"), Is.EqualTo(true));
            Assert.That(model.OfficialPositions!.Any(x => x.Value == "Position"), Is.EqualTo(true));
        });
    }

    [Test]
    public async Task CreateReportablePerson_Post_InvalidModel_ReturnsViewWithError()
    {
        // Arrange
        var model = new ReportablePersonViewModel();
        _controller.ModelState.AddModelError("Error", "Test error");

        // Act
        var result = await _controller.CreateReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(false));
        });
    }

    [Test]
    public async Task CreateReportablePerson_Post_ApiSuccess_RedirectsWithSuccess()
    {
        // Arrange
        var model = new ReportablePersonViewModel
        {
            FilerId = 123,
            ReturnUrl = "/return",
            Agency = "TEST",
            Amount = 100,
            Name = "Test Name",
            OfficialPosition = "TEST_POS"
        };

        var testActivityExpense = new ActivityExpenseViewModel();
        var serializedData = JsonConvert.SerializeObject(testActivityExpense);
        var byteData = Encoding.UTF8.GetBytes(serializedData);
        byte[] outVal = byteData;

        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);
        sessionMock.Setup(s => s.TryGetValue("ActivityExpense", out outVal!))
                  .Returns(true);
        _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
                        new(0L, 1L, 0L, "Position")
            });

        _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
                new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.CreateReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("NewActivityExpense"));
            Assert.That(result.ControllerName, Is.EqualTo("Transaction"));
        });
    }

    [Test]
    public async Task CreateReportablePerson_Post_ApiValidationError_ReturnsViewWithErrors()
    {
        // Arrange
        var model = new ReportablePersonViewModel
        {
            FilerId = 123,
            ReturnUrl = "/return",
            Agency = "TEST",
            Amount = 100,
            Name = "Test Name",
            OfficialPosition = "TEST_POS"
        };

        var workflowErrors = new List<WorkFlowError> {
        new ("Field", "ErrCode", "Type", "Error message")
        };
        var jsonContent = JsonConvert.SerializeObject(workflowErrors);

        var responseMessage = new HttpResponseMessage((HttpStatusCode)422)
        {
            Content = new StringContent(jsonContent)
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            responseMessage,
            new RefitSettings());

        _activityExpenseApiMock
            .Setup(api => api.ValidateReportablePersons(
                It.IsAny<long>(),
                It.IsAny<List<ValidateReportablePersonDs>>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Mock the session
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
                new(0L, 1L, 0L, "Position")
            });

        _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
                new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.CreateReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model, It.IsAny<CancellationToken>()) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(false));

            var typedModel = result.Model as ReportablePersonViewModel;
            Console.WriteLine(JsonConvert.SerializeObject(typedModel));
            Assert.That(typedModel!.Messages.Validations, Is.Not.Null);
        });
    }

    [Test]
    public async Task CreateReportablePerson_Post_ApiOtherError_ReturnsViewWithError_ZeroAmount()
    {
        // Arrange
        var model = new ReportablePersonViewModel
        {
            FilerId = 123,
            ReturnUrl = "/return",
            Agency = "TEST",
            Amount = 0,
            Name = "Test Name",
            OfficialPosition = "TEST_POS"
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            new HttpResponseMessage(HttpStatusCode.InternalServerError),
            new RefitSettings());

        _activityExpenseApiMock
            .Setup(api => api.ValidateReportablePersons(It.IsAny<long>(), It.IsAny<List<ValidateReportablePersonDs>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);
        _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
                new(0L, 1L, 0L, "Position")
            });

        _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
                new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.CreateReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(false));
        });
    }

    [Test]
    public async Task CreateReportablePerson_Post_ApiOtherError_ReturnsViewWithError_NullAmount()
    {
        // Arrange
        var model = new ReportablePersonViewModel
        {
            FilerId = 123,
            ReturnUrl = "/return",
            Agency = "TEST",
            Amount = null,
            Name = "Test Name",
            OfficialPosition = "TEST_POS"
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            new HttpResponseMessage(HttpStatusCode.InternalServerError),
            new RefitSettings());

        _activityExpenseApiMock
            .Setup(api => api.ValidateReportablePersons(It.IsAny<long>(), It.IsAny<List<ValidateReportablePersonDs>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);
        _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
                new(0L, 1L, 0L, "Position")
            });

        _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
                new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.CreateReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(false));
        });
    }

    [Test]
    public async Task EditReportablePerson_Get_InvalidModel_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Test error");

        // Act
        var result = await _controller.EditReportablePerson(null, null, null, null, null, null, null, _referenceDataApiMock.Object) as NotFoundResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task EditReportablePerson_Get_ValidModel_ReturnsViewWithViewModel()
    {
        // Arrange
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        _ = httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        var activityExpense = new ActivityExpenseViewModel
        {
            FilerId = 123,
            ReturnUrl = "/return",
            TransactionReportablePeople = new List<TransactionReportablePerson>
            {
                new ()
                {
                    Id = 1,
                    Name = "Existing",
                    OfficialPosition = "POS",
                    Agency = "AGY",
                    Amount = (Currency)100,
                }
            }
        };
        var activityExpenseJson = JsonConvert.SerializeObject(activityExpense);

        var byteArray = Encoding.UTF8.GetBytes(activityExpenseJson);

        _ = sessionMock.Setup(s => s.TryGetValue("ActivityExpenseData", out byteArray)).Returns(true);

        _ = _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
            new(0L, 1L, 0L, "Position")
            });

        _ = _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
            new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.EditReportablePerson(
            "Existing", "POS", null, "AGY", null, 1, 100,
            _referenceDataApiMock.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.InstanceOf<ReportablePersonViewModel>());
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(true));

            var model = result.Model as ReportablePersonViewModel;
            Assert.That(model!.Id, Is.EqualTo(1));
            Assert.That(model.Name, Is.EqualTo("Existing"));
            Assert.That(model.OfficialPosition, Is.EqualTo("POS"));
            Assert.That(model.Agency, Is.EqualTo("AGY"));
            Assert.That(model.Amount, Is.EqualTo(100));
            Assert.That(model.Agencies!.Any(x => x.Value == "Agency"), Is.EqualTo(true));
            Assert.That(model.OfficialPositions!.Any(x => x.Value == "Position"), Is.EqualTo(true));
        });
    }

    [Test]
    public async Task EditReportablePerson_Post_InvalidModel_ReturnsViewWithError()
    {
        // Arrange
        var model = new ReportablePersonViewModel { Id = 1 };
        _controller.ModelState.AddModelError("Error", "Test error");

        // Act
        var result = await _controller.EditReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(true));
        });
    }

    [Test]
    public async Task EditReportablePerson_Post_ApiValidationError_ReturnsViewWithErrors()
    {
        // Arrange
        var model = new ReportablePersonViewModel
        {
            Id = 1,
            FilerId = 123,
            ReturnUrl = "/return",
            Agency = "TEST",
            Amount = 100,
            Name = "Test Name",
            OfficialPosition = "TEST_POS"
        };

        var workflowErrors = new List<WorkFlowError> {
        new ("Field", "ErrCode", "Type", "Error message")
    };
        var jsonContent = JsonConvert.SerializeObject(workflowErrors);

        var responseMessage = new HttpResponseMessage((HttpStatusCode)422)
        {
            Content = new StringContent(jsonContent)
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            responseMessage,
            new RefitSettings());

        _activityExpenseApiMock
            .Setup(api => api.ValidateReportablePersons(
                It.IsAny<long>(),
                It.IsAny<List<ValidateReportablePersonDs>>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Mock the session
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
            new(0L, 1L, 0L, "Position")
            });

        _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
            new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.EditReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model, It.IsAny<CancellationToken>()) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo(ContactController.ReportablePersonFormView));
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(result.ViewData["IsEditingReportablePerson"], Is.EqualTo(true));

            var typedModel = result.Model as ReportablePersonViewModel;
            Assert.That(typedModel!.Messages.Validations, Is.Not.Null);
        });
    }

    [Test]
    public async Task EditReportablePerson_Post_Successful_RemovesOriginalPerson()
    {
        // Arrange
        var model = new ReportablePersonViewModel
        {
            Id = 1,
            FilerId = 123,
            ReturnUrl = "/return",
            Agency = "TEST",
            Amount = 100,
            Name = "Test Name",
            OfficialPosition = "TEST_POS"
        };

        var testData = new ActivityExpenseViewModel
        {
            FilerId = 123,
            TransactionReportablePeople = new()
            {
                new() { Id = 1, Name = "Person1", Amount = (Currency)100, Agency = "AGY", AgencyDescription = "Agency Desc", OfficialPosition = "POS", OfficialPositionDescription = "Position Desc"}
            },
            ReturnUrl = "/return"
        };

        var serializedData = JsonConvert.SerializeObject(testData);
        var byteData = Encoding.UTF8.GetBytes(serializedData);
        var key = "ActivityExpenseData";

        var sessionMock = new Mock<ISession>();

        sessionMock.Setup(s => s.TryGetValue(key, out It.Ref<byte[]>.IsAny!))
            .Returns((string _, out byte[] value) =>
            {
                value = byteData;
                return true;
            });

        var httpContextMock = new Mock<HttpContext>();
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };

        _activityExpenseApiMock
            .Setup(api => api.ValidateReportablePersons(
                It.IsAny<long>(),
                It.IsAny<List<ValidateReportablePersonDs>>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(new OkResult()));

        _referenceDataApiMock.Setup(x => x.GetAllOfficialPositions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<OfficialPosition>
            {
            new(0L, 1L, 0L, "Position")
            });

        _referenceDataApiMock.Setup(x => x.GetAllAgencies(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agency>
            {
            new(0L, 1L, 0L, "Agency", [])
            });

        // Act
        var result = await _controller.EditReportablePerson(
            _activityExpenseApiMock.Object,
            _referenceDataApiMock.Object,
            model, It.IsAny<CancellationToken>()) as ActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result, Is.Not.Null);

        var redirectResult = (RedirectToActionResult)result;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        // Assert
        Assert.Multiple(() =>
        {
            // Verify redirect
            Assert.That(redirectResult.ActionName, Is.EqualTo("EditActivityExpense"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Transaction"));

            // Verify route values
            Assert.That(redirectResult.RouteValues["FilerId"], Is.EqualTo(123));
            Assert.That(redirectResult.RouteValues["ReturnUrl"], Is.EqualTo("/return"));
        });
    }

    #endregion

    #region Lobbying Firm

    [Test]
    public async Task SearchLobbyingFirmsByIdOrName_WithValidParameters_ReturnsExpectedResults()
    {
        // Arrange
        string search = "TestFirm";
        string address1 = "1234 Address";
        string address2 = "APT A";
        long contactId = 123;
        long filerId = 456;
        string name = "Sample name";
        long registrationId = 789;

        var contactSearchResult = new List<ContactSearchResultDto>
        {
            new(address1, address2, contactId, filerId, name, registrationId),
        };

        _ = _contactsApiMock
            .Setup(api => api.SearchLobbyingFirmsByNameOrId(search, filerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(contactSearchResult);

        // Act
        var result = await _controller.SearchLobbyingFirmsByIdOrName(search, filerId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());

        var resultList = result?.Value as List<ContactSearchResultDto>;
        Assert.That(resultList, Is.Not.Null);

        var firstResult = resultList[0];
        Assert.Multiple(() =>
        {
            Assert.That(firstResult.AddressLine1, Is.EqualTo(address1));
            Assert.That(firstResult.AddressLine2, Is.EqualTo(address2));
            Assert.That(firstResult.ContactId, Is.EqualTo(contactId));
            Assert.That(firstResult.FilerId, Is.EqualTo(filerId));
            Assert.That(firstResult.Name, Is.EqualTo(name));
            Assert.That(firstResult.RegistrationFilingId, Is.EqualTo(registrationId));
        });

        _contactsApiMock.Verify(api =>
            api.SearchLobbyingFirmsByNameOrId(search, filerId, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Test]
    public void SelectLobbyingFirm_Get_WithValidModel_ReturnsViewWithModel()
    {
        // Arrange
        string reportType = FilingTypeModel.Report48h.Name;
        long filingId = 123;
        long filerId = 456;
        long contactId = 1;
        long registrationFilingId = 2;

        // Act
        var result = _controller.SelectLobbyingFirm(reportType, filingId, filerId, contactId, registrationFilingId);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = (ViewResult)result;

        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("SelectLobbyingFirm"));

        var model = viewResult.Model as FirmPaymentTransactionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.ReportType, Is.EqualTo(reportType));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId));
        });
    }

    [Test]
    public void SelectLobbyingFirm_Get_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        string reportType = FilingTypeModel.Report48h.Name;
        long filingId = 123;
        long filerId = 456;

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = _controller.SelectLobbyingFirm(reportType, filingId, filerId, null, null);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void SelectLobbyingFirm_Post_WithValidModel_RedirectsToSelectContact()
    {
        // Arrange
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };

        // Act
        var result = _controller.SelectLobbyingFirm(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("CreateEditLobbyingFirm"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues!["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(redirectResult.RouteValues["registrationFilingId"], Is.EqualTo(model.RegistrationFilingId));
        });
    }

    [Test]
    public void SelectLobbyingFirm_Post_PreviousAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };

        // Act
        var result = _controller.SelectLobbyingFirm(model, "Previous") as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.RouteValues!["viewName"], Is.EqualTo(FilingSummaryTypeModel.EndOfSessionLobbyingSummary.Name));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(model.ReportType));
        });
    }

    [Test]
    public void SelectLobbyingFirm_Post_ModalAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };

        // Act
        var result = _controller.SelectLobbyingFirm(model, "SelectLobbyingFirm") as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.RouteValues!["viewName"], Is.EqualTo(FilingSummaryTypeModel.EndOfSessionLobbyingSummary.Name));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(model.ReportType));
        });
    }

    [Test]
    public void SelectLobbyingFirm_Post_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = _controller.SelectLobbyingFirm(model);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Get_WithValidParameters_ReturnsViewWithModel()
    {
        // Arrange
        string reportType = FilingTypeModel.Report48h.Name;
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;

        var expectedViewModel = new FirmPaymentTransactionViewModel
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Lobbying Firm"
            }
        };

        _ = _firmPaymentTransactionCtlSvcMock
            .Setup(api => api.GetViewModel(It.IsAny<string>(), It.IsAny<long>(), It.IsAny<long>(), It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedViewModel);

        var parameters = new CreateEditLobbyingFirmParamaeters
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId
        };

        // Act
        var result = await _controller.CreateEditLobbyingFirm(parameters, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;

        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("LobbyingFirmForm"));

        var model = viewResult.Model as FirmPaymentTransactionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.ReportType, Is.EqualTo(reportType));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId));
            Assert.That(model.Contact, Is.Not.Null);
            Assert.That(model.Contact.OrganizationName, Is.EqualTo("Test Lobbying Firm"));
        });

        _firmPaymentTransactionCtlSvcMock.Verify(api =>
            api.GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Get_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        string reportType = FilingTypeModel.Report48h.Name;
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;

        _controller.ModelState.AddModelError("error", "test error");

        var parameters = new CreateEditLobbyingFirmParamaeters
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId
        };
        // Act
        var result = await _controller.CreateEditLobbyingFirm(parameters, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        _firmPaymentTransactionCtlSvcMock.Verify(api =>
            api.GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_InvalidModelState_ReturnNotFound()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };
        string action = "Previous";
        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_WithPreviousAction_RedirectsToSelectContact()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };
        string action = "Previous";

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("SelectLobbyingFirm"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_WithContinueAction_RedirectsToSelectContact()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };
        string action = "Continue";

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Report48HEosTransaction"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(redirectResult.RouteValues["registrationFilingId"], Is.EqualTo(model.RegistrationFilingId));
        });
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_WithContinueActionAndNoRegistrationFilingId_SavesContactAndRedirects()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Organization",
                Street = "123 Main St",
                City = "Sacramento",
                State = "CA",
                ZipCode = "95814",
                Country = "USA"
            }
        };
        string action = "Continue";

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Report48HEosTransaction"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
        });

        _firmPaymentTransactionCtlSvcMock.Verify(api =>
            api.SaveFilerContact(It.Is<FirmPaymentTransactionViewModel>(m => m == model), It.IsAny<ModelStateDictionary>(), "EndOfSession"),
            Times.Once);
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_WithContinueActionAndNoRegistrationFilingIdContactId_CreateContactAndRedirects()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Organization",
                Street = "123 Main St",
                City = "Sacramento",
                State = "CA",
                ZipCode = "95814",
                Country = "USA"
            }
        };
        string action = "Continue";

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Report48HEosTransaction"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
        });

        _firmPaymentTransactionCtlSvcMock.Verify(api =>
            api.SaveFilerContact(It.Is<FirmPaymentTransactionViewModel>(m => m == model), It.IsAny<ModelStateDictionary>(), "EndOfSession"),
            Times.Once);
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_WithContinueActionAndNullFilingOrFiler_ReturnsNotFound()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = null, // Missing required field
            FilerId = 456,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Organization"
            }
        };
        string action = "Continue";

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        _firmPaymentTransactionCtlSvcMock.Verify(api =>
            api.SaveFilerContact(It.Is<FirmPaymentTransactionViewModel>(m => m == model), It.IsAny<ModelStateDictionary>(), "PaymentToLobbyingFirm"),
            Times.Never);
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_WithCancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456
        };
        string action = "Cancel";

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["viewName"], Is.EqualTo(FilingSummaryTypeModel.EndOfSessionLobbyingSummary.Name));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    [Test]
    public async Task CreateEditLobbyingFirm_Post_WithContinueActionAndExistingContact_RedirectsToEditReport48HEosTransaction()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            Id = 111, // triggers EditReport48HEosTransaction
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = null // important: this ensures SaveFilerContact is called
        };

        string action = "Continue";

        _firmPaymentTransactionCtlSvcMock
            .Setup(svc => svc.SaveFilerContact(model, It.IsAny<ModelStateDictionary>(), "EndOfSession"))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.CreateEditLobbyingFirm(model, action, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect.RouteValues, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("EditReport48HEosTransaction"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Transaction"));
            Assert.That(redirect.RouteValues["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirect.RouteValues["transactionId"], Is.EqualTo(model.Id));
            Assert.That(redirect.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirect.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirect.RouteValues["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(redirect.RouteValues["registrationFilingId"], Is.EqualTo(model.RegistrationFilingId));
        });

        _firmPaymentTransactionCtlSvcMock.Verify(svc =>
            svc.SaveFilerContact(model, It.IsAny<ModelStateDictionary>(), "EndOfSession"),
            Times.Once);
    }

    #endregion

#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
}
