using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Lobbyists;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerRegistration;
public partial class LobbyistEmployerRegistrationSvcDependencies
{
    public IDecisionsSvc DecisionsSvc { get; }
    public IAuthorizationSvc AuthorizationSvc { get; }
    public IFilerSvc FilerSvc { get; }
    public INotificationSvc NotificationSvc { get; }
    public IUserMaintenanceSvc UserMaintenanceSvc { get; }
    public IRegistrationRepository RegistrationRepository { get; }
    public IRegistrationRegistrationContactRepository RegistrationRegistrationContactRepository { get; }
    public IFilerLinkRepository FilerLinkRepository { get; }
    public ILinkageSvc LinkageSvc { get; }
    public IAttestationRepository AttestationRepository { get; }
    public ILobbyingInterestRepository LobbyingInterestRepository { get; }

    public LobbyistEmployerRegistrationSvcDependencies(
            IDecisionsSvc decisionsSvc,
            IAuthorizationSvc authorizationSvc,
            IFilerSvc filerSvc,
            INotificationSvc notificationSvc,
            IUserMaintenanceSvc userMaintenanceSvc,
            IRegistrationRepository registrationRepository,
            IRegistrationRegistrationContactRepository registrationRegistrationContactRepository,
            IFilerLinkRepository filerLinkRepository,
            ILinkageSvc linkageSvc,
            IAttestationRepository attestationRepository,
            ILobbyingInterestRepository lobbyingInterestRepository
        )
    {
        DecisionsSvc = decisionsSvc;
        AuthorizationSvc = authorizationSvc;
        FilerSvc = filerSvc;
        NotificationSvc = notificationSvc;
        UserMaintenanceSvc = userMaintenanceSvc;
        RegistrationRepository = registrationRepository;
        RegistrationRegistrationContactRepository = registrationRegistrationContactRepository;
        FilerLinkRepository = filerLinkRepository;
        LinkageSvc = linkageSvc;
        AttestationRepository = attestationRepository;
        LobbyingInterestRepository = lobbyingInterestRepository;
    }
}
