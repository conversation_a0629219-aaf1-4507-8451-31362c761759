using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;

namespace SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines database interactions for the Transaction table.
/// </p>
/// <p>
/// Architectural Design: This repository represents a Data Service invoked by Business Services.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Every entity has an Id field containing a unique identifier that can be used to retrieve a single record.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this repository is to handle data persistence functions acting as an intermediary between business logic and data storage.
/// </p>
/// <h4>Feature</h4>
/// <p>
/// <ul>
/// <li>FD-01: Enter an Activity Report</li>
/// <li>FD-02: Modify an Activity Report</li>
/// <li>FD-03: Upload an Activity Report</li>
/// </ul>
/// </p>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this repository.
/// </p>
/// | Service                 | Operation                    | Description                         |
/// | ----------------------- | ---------------------------- | ----------------------------------- |
/// | N/A | N/A | N/A |
#endregion

public interface ITransactionRepository : IRepository<Transaction, long>
{
    /// <summary>
    /// Add attachments to a transaction.
    /// </summary>
    /// <param name="attachments"></param>
    /// <param name="transactionId"></param>
    void AddAttachments(IEnumerable<Attachment> attachments, long transactionId);

    /// <summary>
    /// Get all transactions for a specific filer id.
    /// </summary>
    /// <param name="filerId"></param>
    /// <returns>A collection of transactions.</returns>
    Task<IEnumerable<Transaction>> GetAllByFilerId(long filerId);

    /// <summary>
    /// Get all transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of transactions.</returns>
    Task<IEnumerable<Transaction>> GetAllByFilingId(long filingId);

    /// <summary>
    /// Remove attachments from a transaction.
    /// </summary>
    /// <param name="attachmentIds"></param>
    /// <param name="transactionId"></param>
    Task RemoveAttachments(IEnumerable<long> attachmentIds, long transactionId);

    /// <summary>
    /// Add a transaction to a filing.
    /// </summary>
    /// <param name="transactionId"></param>
    /// <param name="filingId"></param>
    Task AddTransactionToFiling(long transactionId, long filingId);

    /// <summary>
    /// Get all lobbyist employer campaign contribution transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of lobbyist employer campaign contribution transactions.</returns>
    Task<IEnumerable<LobbyingCampaignContribution>> GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(long filingId);

    /// <summary>
    /// Get all activity expense transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of activity expense transactions for a specific filing id.</returns>
    Task<IEnumerable<ActivityExpense>>
        GetAllActivityExpenseTransactionsForFiling(long filingId);

    /// <summary>
    /// Get all lobbyist employer coalition payment transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of lobbyist employer coalition payment transactions.</returns>
    Task<IEnumerable<PaymentMadeToLobbyingCoalitionResponse>> GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(long filingId, DateTime legislativeStartDate);

    /// <summary>
    /// Get all other payments to influence variants for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of influence payment transactions.</returns>
    Task<IEnumerable<OtherPaymentsToInfluenceResponse>> GetAllOtherPaymentsToInfluenceTransactionsForFiling(long filingId, DateTime legislativeStartDate);

    /// <summary>
    /// Get  other payments to influence transaction by id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of influence payment transactions.</returns>
    Task<OtherPaymentsToInfluence> GetOtherPaymentsToInfluenceTransactionById(long transactionId);

    /// <summary>
    /// Get cumulative amount for other payments to influence variants for a specific filing id and contact id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <param name="contactId"></param>
    /// <param name="legislativeStartDate"></param>
    /// <returns>A response with CumulativeAmount.</returns>
    Task<CumulativeAmountResponse> GetOtherPaymentsCumulativeAmountForFilingAndContact(long filingId, long contactId, DateTime legislativeStartDate);

    /// <summary>
    /// Get all end of session lobbying transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of end of session lobbying transactions.</returns>
    Task<IEnumerable<EndOfSessionLobbyingDto>> GetAllEndOfSessionLobbyingTransactionsForFiling(long filingId);

    /// <summary>
    /// Get an end of session lobbying transaction by its id.
    /// </summary>
    /// <param name="transactionId"></param>
    /// <returns>A end of session lobbying transaction.</returns>
    Task<EndOfSessionLobbyingDto?> GetAllEndOfSessionLobbyingTransactionById(long transactionId);

    /// <summary>
    /// Get all lobbyist employer firm payment transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of lobbyist employer firm payment transactions.</returns>
    Task<IEnumerable<PaymentMadeToLobbyingFirms>> GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(long filingId, DateTime legislativeStartDate);

    /// <summary>
    /// Get all transactions for a specific filing id and legislative dates.
    /// </summary>
    /// <param name="filingId"></param>
    /// <param name="legislativeStartDate"></param>
    /// <param name="legislativeEndDate"></param>
    /// <returns>A collection of transaction summaries</returns>
    Task<IEnumerable<TransactionSummaryDto>> GetTransactionsForFilingSummary(long filingId, DateTime legislativeStartDate, DateTime legislativeEndDate);

    /// <summary>
    /// Get all monetary types.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of monetary types.</returns>
    Task<IEnumerable<MonetaryType>> GetAllMonetaryTypes();

    /// <summary>
    /// Get all lobbyist employer payment received by coalition transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of lobbyist employer payment received by coalition transactions.</returns>
    Task<IEnumerable<PaymentReceiveLobbyingCoalition>> GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(long filingId);

    /// <summary>
    /// Get lobbyist employer payment received by coalition transaction by transaction id
    /// </summary>
    /// <param name="transactionId"></param>
    /// <returns></returns>
    Task<PaymentReceiveLobbyingCoalition?> GetPaymentReceivedLobbyingCoalitionTransactionById(long transactionId);

    /// <summary>
    /// Get lobbyist campaign contribution transaction for a specific transaction id.
    /// </summary>
    /// <param name="transactionId"></param>
    /// <returns>A lobbyist campaign contribution transaction by id.</returns>
    Task<LobbyingCampaignContribution?> GetLobbyistCampaignContributionTransactionById(long transactionId);

    /// <summary>
    /// Get lobbyist employer payment made to lobbying coalition transaction for a specific transaction id.
    /// </summary>
    /// <param name="transactionId"></param>
    /// <returns>A payment made to lobbying coalition transaction by id.</returns>
    Task<PaymentMadeToLobbyingCoalition?> GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(long transactionId);

    /// <summary>
    /// Get lobbying advertisement transaction by a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A lobbying advertisement transaction by filing id.</returns>
    Task<LobbyingAdvertisement?> GetLobbyingAdvertisementTransactionByFilingId(long filingId);

    /// <summary>
    /// Find all active transactions by filing
    /// </summary>
    /// <typeparam name="TTransaction">Transaction type to find</typeparam>
    /// <param name="filingId">ID of Filing to which the transaction belongs</param>
    /// <param name="isPaymentMadeByAgent">Custom flag for payment made by agent</param>
    /// <returns>A collection of transaction</returns>
    Task<List<TTransaction>> FindAllTransactionsByFiling<TTransaction>(long filingId, bool isPaymentMadeByAgent = false) where TTransaction : Transaction;

    /// <summary>
    /// Find an active transactions by ID and filing's ID
    /// </summary>
    /// <typeparam name="TTransaction">Transaction type to find</typeparam>
    /// <param name="id">ID of transaction</param>
    /// <param name="filingId">ID of Filing to which the transaction belongs</param>
    /// <returns>Transaction information</returns>
    Task<TTransaction?> FindByIdAndFilingId<TTransaction>(long id, long filingId) where TTransaction : Transaction;

    /// <summary>
    /// Find all filing transactions that associated with a filing by filing ID
    /// </summary>
    /// <param name="filingId">ID of filing</param>
    /// <returns>A collection of filing transaction</returns>
    Task<List<FilingTransaction>> FindAllFilingTransactionsByFilingId(long filingId);

    Task<List<CumulativeAmountResponse>> GetCumulativeAmountPaymentMadeToLobbyingFirms(List<long>? contactIds, DateTime legislativeStartDate);

    Task<List<CumulativeAmountResponse>> GetCumulativeAmountPaymentReceiveLobbyingCoalition(List<long>? contactIds, DateTime legislativeStartDate);

    /// <summary>
    /// Calculates the total transaction amount for a specific contact across multiple filings,
    /// filtered by the given transaction type.
    /// </summary>
    /// <param name="filingIds">A list of filing IDs to include in the aggregation.</param>
    /// <param name="contactId">The unique identifier of the contact whose transactions will be summed.</param>
    /// <param name="transactionTypeId">The ID of the transaction type used to filter the transactions.</param>
    /// <returns>
    /// The total sum of the transaction amounts that match the specified filing IDs, contact ID, and transaction type.
    /// </returns>
    Task<decimal> SumTransactionAmountsByFilingsAndContact(List<long> filingIds, long contactId, long transactionTypeId);

    /// <summary>
    /// Gets the PaymentReceived by Id
    /// </summary>
    /// <param name="id">Identifier for the DisclosureTransaction</param>
    /// <returns></returns>
    Task<PaymentReceived?> GetSmoCampaignStatementPaymentRecievedByIdAsync(long id);

    /// <summary>
    /// Gets a list of transactions that matches the search criteria
    /// </summary>
    /// <param name="filingIds"></param>
    /// <param name="contactId"></param>
    /// <param name="position"></param>
    /// <param name="candidateId"></param>
    /// <param name="ballotMeasureId"></param>
    /// <returns></returns>
    Task<List<PaymentReceived>> GetMatchingTransactionByFilingId(List<long> filingIds, long contactId, string position, long? candidateId, long? ballotMeasureId);

}
