// <copyright file="ICreateFiling.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerRegistration.Elections;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;

/// <summary>
/// Command handler interface for creating a new filing in the system.
/// </summary>
public interface ICreateFiling : ICommand<CreateFilingCommand, IResult<Filing>>;

/// <summary>
/// Command handler implementation for creating a new filing in the system.
/// </summary>
/// <param name="db">The database context.</param>
/// <param name="auditService">High level audit facilities.</param>
public sealed class CreateFiling(
    DatabaseContext db,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc) : ICreateFiling
{
    /// <inheritdoc />
    public async ValueTask<IResult<Filing>> Execute(
        CreateFilingCommand input,
        CancellationToken cancellationToken = default)
    {
        var filer = await db.Filers
            .FirstOrDefaultAsync(f => f.Id == input.FilerId, cancellationToken);

        if (filer is null)
        {
            return new Failure<Filing>.NotFound("No filer was found with the requested id.");
        }

        if (input.Build().Unwrap(out var filing, out var failure))
        {
            return failure;
        }

        //TODO: Remove this once we have a real filing periods
        var testFilingPeriod = await db.FilingPeriods.FirstOrDefaultAsync();
        if (testFilingPeriod is null)
        {
            testFilingPeriod = new FilingPeriod
            {
                Name = "Test",
                Description = "Test Filing Period",
                StartDate = dateTimeSvc.GetCurrentDateTime(),
                EndDate = dateTimeSvc.GetCurrentDateTime().AddDays(30),
                DueDate = dateTimeSvc.GetCurrentDateTime().AddDays(15),
                ElectionCycle = new ElectionCycle
                {
                    Name = "Test",
                    Description = "Test Election Cycle",
                    StartDate = dateTimeSvc.GetCurrentDateTime(),
                    EndDate = dateTimeSvc.GetCurrentDateTime().AddDays(30),
                }
            };
        }

        //TODO: Remove this once we have a real filing types
        filing.FilingPeriod = testFilingPeriod;
        filing.FilingType = db.FilingTypes.FirstOrDefault();


        db.Filings.Add(filing);

        await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Create", filing.GetType().Name, filing.Id.ToString(), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<Filing>(filing);
    }
}
