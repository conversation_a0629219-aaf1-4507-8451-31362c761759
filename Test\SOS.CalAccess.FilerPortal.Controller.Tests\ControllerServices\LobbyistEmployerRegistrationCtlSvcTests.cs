using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using Moq;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;
using IFilingsApi = SOS.CalAccess.FilerPortal.Generated.IFilingsApi;
using LegislativeSessionResponseList = SOS.CalAccess.FilerPortal.Generated.LegislativeSessionResponseList;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[TestFixture]
public class LobbyistEmployerRegistrationCtlSvcTests
{
    private Mock<IFilingsApi> _filingsApiMock;
    private Mock<IDecisionsValidationMapService> _decisionsValidationMapSvcMock;
    private Mock<ILobbyistEmployerRegistrationSvc> _lobbyistEmployerRegistrationSvc;
    private Mock<ILobbyistRegistrationSvc> _lobbyistRegistrationSvc;
    private Mock<IReferenceDataSvc> _referenceDataSvc;
    private Mock<IStringLocalizer<SharedResources>> _localizer;
    private LobbyistEmployerRegistrationCtlSvc _svc;

    [SetUp]
    public void SetUp()
    {
        _filingsApiMock = new Mock<IFilingsApi>();
        _decisionsValidationMapSvcMock = new Mock<IDecisionsValidationMapService>();
        _lobbyistEmployerRegistrationSvc = new Mock<ILobbyistEmployerRegistrationSvc>();
        _lobbyistRegistrationSvc = new Mock<ILobbyistRegistrationSvc>();
        _referenceDataSvc = new Mock<IReferenceDataSvc>();
        _localizer = new Mock<IStringLocalizer<SharedResources>>();
        _svc = new LobbyistEmployerRegistrationCtlSvc(_filingsApiMock.Object, _decisionsValidationMapSvcMock.Object, _lobbyistEmployerRegistrationSvc.Object, _lobbyistRegistrationSvc.Object, _referenceDataSvc.Object, _localizer.Object);
    }

    [Test]
    public async Task GetStep01GenInfoViewModel_ShouldReturnDefaultModel()
    {
        // Arrange
        var id = 0;

        _ = _filingsApiMock.Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList([]));

        // Act
        LobbyistEmployerRegistrationStep01GenInfo result = await _svc.GetStep01GenInfoViewModel(id, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Addresses, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Addresses[0].Purpose, Is.EqualTo("Business"));
            Assert.That(result.Addresses[0].Country, Is.EqualTo("United States"));
            Assert.That(result.Addresses[1].Purpose, Is.EqualTo("Mailing"));
            Assert.That(result.LegislativeSessionOptions, Has.Count.EqualTo(0));
            Assert.That(result, Is.TypeOf<LobbyistEmployerRegistrationStep01GenInfo>());
        });
    }

    [Test]
    public async Task GetStep01GenInfoViewModel_WithId_ShouldReturnDefaultModelWithExisting()
    {
        // Arrange
        var id = 123;

        _ = _filingsApiMock.Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new LegislativeSessionResponseList([]));

        _ = _lobbyistEmployerRegistrationSvc.Setup(x => x.GetLobbyistEmployer(It.IsAny<long>()))
            .ReturnsAsync(new LobbyistEmployerResponseDto()
            {
                Id = id,
                Addresses = new List<AddressDtoModel>()
                {
                    new()
                    {
                        Purpose = "Business",
                        City = "SD",
                        State = "CA",
                        Country = "United States",
                        Street = "Str 1",
                        Street2 = "Str 2",
                        Zip = "12345"
                    },
                    new()
                    {
                        Purpose = "Mailing",
                        City = "SD",
                        State = "CA",
                        Country = "United States",
                        Street = "Str 1",
                        Street2 = "Str 2",
                        Zip = "12345"
                    }
                }
            });

        // Act
        LobbyistEmployerRegistrationStep01GenInfo result = await _svc.GetStep01GenInfoViewModel(id, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Addresses, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Addresses[0].Purpose, Is.EqualTo("Business"));
            Assert.That(result.Addresses[0].Country, Is.EqualTo("United States"));
            Assert.That(result.Addresses[1].Purpose, Is.EqualTo("Mailing"));
            Assert.That(result.LegislativeSessionOptions, Has.Count.EqualTo(0));
            Assert.That(result, Is.TypeOf<LobbyistEmployerRegistrationStep01GenInfo>());
        });
    }

    [Test]
    public async Task GetStep01AgenciesViewModel_ShouldReturnDefaultModel()
    {
        // Arrange
        var id = 123;

        var agencies = new List<Agency>
        {
            new() { Id = 1, Name = "City Agency" }
        };

        _ = _referenceDataSvc.Setup(x => x.GetAllAgencies()).Returns(Task.FromResult<IEnumerable<Agency>>(agencies));

        _ = _lobbyistEmployerRegistrationSvc.Setup(x => x.GetLobbyistEmployer(It.IsAny<long>()))
            .ReturnsAsync(new LobbyistEmployerResponseDto()
            {
                Id = id,
                Version = 0,
                Agencies = new List<RegistrationAgencyDto>()
                {
                    new()
                    {
                        RegistrationId = id,
                        AgencyId = 1,
                        AgencyName = "Tom Ford"
                    }
                },
                StateLegislatureLobbying = true
            });

        // Act
        var result = await _svc.GetStep01AgenciesViewModel(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Agencies, Is.Not.Null);
            Assert.That(result.SelectedAgencies, Is.Not.Null);
            Assert.That(result.StateLegislatureLobbied, Is.EqualTo(true));
            Assert.That(result, Is.TypeOf<LobbyistEmployerRegistrationStep01Agencies>());
        });
    }

    [Test]
    public async Task GetStep01NatureInterestsViewModel_ShouldReturnDefaultModel()
    {
        // Arrange
        var id = 123;

        _ = _lobbyistEmployerRegistrationSvc.Setup(x => x.GetLobbyistEmployer(It.IsAny<long>()))
            .ReturnsAsync(new LobbyistEmployerResponseDto()
            {
                Id = id,
                Version = 0,
                LobbyingInterest = "interests"
            });

        // Act
        var result = await _svc.GetStep01NatureInterestsViewModel(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.FilingInterestsDescription, Is.EqualTo("interests"));
            Assert.That(result, Is.TypeOf<LobbyistEmployerRegistrationStep01NatureInterests>());
        });
    }

    [Test]
    public async Task SubmitLobbyistRegistrationForm_ShouldReturnResponse()
    {
        var model = new LobbyistEmployerRegistrationStep01GenInfo { Id = 0 };
        var dto = new LobbyistEmployerGeneralInfoRequest();
        var modelState = new ModelStateDictionary();

        _ = _lobbyistEmployerRegistrationSvc
            .Setup(x => x.CreateLobbyistEmployerRegistrationPage03(It.IsAny<LobbyistEmployerGeneralInfoRequest>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = true });

        RegistrationResponseDto result = await _svc.SubmitStep01GenInfoViewModel(model, modelState);

        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public async Task SubmitLobbyistRegistrationForm_InvalidReturnResponse_WithLegislativeSessions()
    {
        var model = new LobbyistEmployerRegistrationStep01GenInfo { Id = 0 };
        var dto = new LobbyistEmployerGeneralInfoRequest();
        var modelState = new ModelStateDictionary();

        _ = _lobbyistEmployerRegistrationSvc
            .Setup(x => x.CreateLobbyistEmployerRegistrationPage03(It.IsAny<LobbyistEmployerGeneralInfoRequest>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = false });

        _ = _filingsApiMock.Setup(x => x.GetLegistlativeSessions(It.IsAny<CancellationToken>()))
        .ReturnsAsync(new LegislativeSessionResponseList([new(id: 1, name: "", startDate: DateTime.UtcNow, endDate: DateTime.UtcNow)]));

        RegistrationResponseDto result = await _svc.SubmitStep01GenInfoViewModel(model, modelState);

        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public async Task GetInHouseLobbyistListViewModel_ReturnsEmptyData()
    {
        // Arrange
        long employerId = -1;

        // Act
        var result = await _svc.GetInHouseLobbyistListViewModel(employerId);
        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Lobbyists, Is.Null);
    }

    [Test]
    public async Task GetInHouseLobbyistListViewModel_ReturnsExpectedViewModel()
    {
        // Arrange
        long employerId = 123;

        var response = new List<InHouseLobbyistResponseDto>()
        {
            new () {
                Id = employerId,
                FilerId = 1,
                FirstName = "Test",
                LastName = "Unit",
                Email = "<EMAIL>",
                EmployerName = "Employer",
                Name = "Unit Test",
            },
            new () {
                Id = employerId,
                FilerId = 2,
                FirstName = "Lobbyist",
                LastName = "Inhouse",
                Email = "<EMAIL>",
                EmployerName = "Employer",
                Name = "Lobbyist InHouse",
            },
        };

        var lobbyists = _lobbyistEmployerRegistrationSvc.Setup(m => m.GetLobbyistRegistrationByEmployerRegistration(employerId))
                                                                .ReturnsAsync(response);

        // Act
        var result = await _svc.GetInHouseLobbyistListViewModel(employerId);
        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Lobbyists, Is.Not.Null);
        var lobbyist = result.Lobbyists.FirstOrDefault();
        Assert.That(lobbyist, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(employerId));
            Assert.That(result.Lobbyists, Has.Count.EqualTo(2));
            Assert.That(lobbyist.Name, Is.EqualTo(response[0].Name));
            Assert.That(result.InHouseLobbyistsGridModel, Is.Not.Null);
            Assert.That(result.InHouseLobbyistsGridModel?.GridId, Is.EqualTo("InHouseLobbyistsGrid"));
        });
    }

    [Test]
    public async Task GetInHouseLobbyistViewModel_ReturnModelWithEmptyData()
    {
        // Arrange
        var lobbyistEmployerRegistrationId = 1;
        long? lobbyistRegistrationId = null;
        // Act
        var result = await _svc.GetInHouseLobbyistViewModel(lobbyistEmployerRegistrationId, lobbyistRegistrationId);

        // Asset
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.LobbyistEmployerRegistrationId, Is.EqualTo(lobbyistEmployerRegistrationId));
            Assert.That(result.LobbyistRegistrationId, Is.EqualTo(lobbyistRegistrationId));
            Assert.That(result.FirstName, Is.EqualTo(string.Empty));
            Assert.That(result.LastName, Is.EqualTo(string.Empty));
            Assert.That(result.MiddleName, Is.Null);
            Assert.That(result.Email, Is.EqualTo(string.Empty));
        });
    }

    [Test]
    public async Task GetInHouseLobbyistViewModel_ReturnModelWithData()
    {
        // Arrange
        var lobbyistEmployerRegistrationId = 1;
        long? lobbyistRegistrationId = 123;
        var response = new LobbyistResponseDto
        {
            FirstName = "Test",
            LastName = "Unit",
            MiddleName = null,
            Email = "<EMAIL>"
        };
        // Act
        _lobbyistRegistrationSvc.Setup(x => x.GetLobbyistRegistration(It.IsAny<long>()))
                                .ReturnsAsync(response);
        var result = await _svc.GetInHouseLobbyistViewModel(lobbyistEmployerRegistrationId, lobbyistRegistrationId);

        // Asset
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.LobbyistEmployerRegistrationId, Is.EqualTo(lobbyistEmployerRegistrationId));
            Assert.That(result.LobbyistRegistrationId, Is.EqualTo(lobbyistRegistrationId));
            Assert.That(result.FirstName, Is.EqualTo(response.FirstName));
            Assert.That(result.LastName, Is.EqualTo(response.LastName));
            Assert.That(result.MiddleName, Is.EqualTo(response.MiddleName));
            Assert.That(result.Email, Is.EqualTo(response.Email));
        });
    }

    [Test]
    public void CreateOrUpdateInHouseLobbyist_InvalidEmployerId_ThrowException()
    {
        // Arrange
        long lobbyistEmployerId = -1;
        _lobbyistEmployerRegistrationSvc
                    .Setup(x => x.GetLobbyistEmployer(It.IsAny<long>()))
                    .ThrowsAsync(new KeyNotFoundException());

        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = lobbyistEmployerId,
            FirstName = "Test",
            LastName = "Unit",
            Email = "<EMAIL>",
            IsNotAllowToEdit = false,
        };

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _svc.CreateOrUpdateInHouseLobbyist(model));
        Assert.That(ex.Message, Is.EqualTo($"The given key was not present in the dictionary."));
    }

    [Test]
    public async Task CreateOrUpdateInHouseLobbyist_ValidEmployerId_Create_ShouldSuccess()
    {
        // Arrange
        long lobbyistEmployerId = 1;
        var response = new LobbyistEmployerResponseDto
        {
            Name = "Employer",
            Email = "<EMAIL>",
            FilerId = 1
        };
        _lobbyistEmployerRegistrationSvc
                    .Setup(x => x.GetLobbyistEmployer(It.IsAny<long>()))
                    .ReturnsAsync(response);

        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = lobbyistEmployerId,
            FirstName = "Test",
            LastName = "Unit",
            Email = "<EMAIL>",
            IsNotAllowToEdit = false,
        };

        var registrationResponse = new RegistrationResponseDto()
        {
            Id = 1000,
            FilerId = 2000,
            ApprovedAt = DateTime.Now
        };

        _lobbyistRegistrationSvc
        .Setup(x => x.CreateLobbyistRegistrationPage03(It.IsAny<LobbyistRegistrationRequestDto>()))
        .ReturnsAsync(registrationResponse);


        // Act & Assert
        var result = await _svc.CreateOrUpdateInHouseLobbyist(model);


        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(registrationResponse.Id));
            Assert.That(result.FilerId, Is.EqualTo(registrationResponse.FilerId));
        });
    }

    [Test]
    public async Task CreateOrUpdateInHouseLobbyist_ValidEmployerId_Update_ShouldSuccess()
    {
        // Arrange
        long lobbyistEmployerId = 1;
        var response = new LobbyistEmployerResponseDto
        {
            Name = "Employer",
            Email = "<EMAIL>",
            FilerId = 1
        };
        _lobbyistEmployerRegistrationSvc
                    .Setup(x => x.GetLobbyistEmployer(It.IsAny<long>()))
                    .ReturnsAsync(response);

        var model = new InHouseLobbyistViewModel
        {
            LobbyistEmployerRegistrationId = lobbyistEmployerId,
            LobbyistRegistrationId = 111,
            FirstName = "Test",
            LastName = "Unit",
            Email = "<EMAIL>",
            IsNotAllowToEdit = false,
        };

        var registrationResponse = new RegistrationResponseDto()
        {
            Id = model.LobbyistRegistrationId,
            FilerId = 2000,
            ApprovedAt = DateTime.Now
        };

        _lobbyistRegistrationSvc
        .Setup(x => x.UpdateLobbyistRegistration(It.IsAny<long>(), It.IsAny<LobbyistRegistrationRequestDto>()))
        .ReturnsAsync(registrationResponse);


        // Act & Assert
        var result = await _svc.CreateOrUpdateInHouseLobbyist(model);


        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(registrationResponse.Id));
            Assert.That(result.FilerId, Is.EqualTo(registrationResponse.FilerId));
        });
    }

    [Test]
    public void GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel_ShouldReturnValidViewModel()
    {
        // Arrange
        var id = 100L;

        // Act
        var result = _svc.GetLobbyistEmployerRegistrationStep03LobbyingFirmsViewModel(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.HasFirms, Is.True);
            Assert.That(result.LobbyingFirms, Is.Not.Null);
            Assert.That(result.LobbyingFirms?.Count, Is.EqualTo(5));
            Assert.That(result.LobbyingFirms?.First().Name, Is.EqualTo("Jane Smith"));

            Assert.That(result.LobbyingFirmsGridModel, Is.Not.Null);
            Assert.That(result.LobbyingFirmsGridModel?.GridId, Is.EqualTo("LobbyingFirmsGrid"));
            Assert.That(result.LobbyingFirmsGridModel?.Columns.Count, Is.EqualTo(3));
            Assert.That(result.LobbyingFirmsGridModel?.ActionItems.Count, Is.EqualTo(1));
            Assert.That(result.LobbyingFirmsGridModel?.DeleteConfirmationMessage, Is.EqualTo("Are you sure you want to delete this lobbying firm?"));
        });
    }


    [Test]
    public async Task SubmitLobbyistEmployerRegistrationFormStep01Agencies_ShouldReturnResponse()
    {
        var model = new LobbyistEmployerRegistrationStep01Agencies { Id = 77 };
        var modelState = new ModelStateDictionary();

        _ = _lobbyistEmployerRegistrationSvc
            .Setup(x => x.UpdateLobbyistEmployerRegistrationPage04(It.IsAny<long>(), It.IsAny<LobbyistEmployerStateAgenciesRequest>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = true });

        RegistrationResponseDto result = await _svc.SubmitStep01AgenciesViewModel(model, modelState);

        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public Task SubmitStep01AgenciesViewModel_WithoutId_ShouldThrowError()
    {
        var model = new LobbyistEmployerRegistrationStep01Agencies { Id = null };
        var modelState = new ModelStateDictionary();

        _ = Assert.ThrowsAsync<KeyNotFoundException>(
            async () => await _svc.SubmitStep01AgenciesViewModel(model, modelState)
        );
        return Task.CompletedTask;
    }

    [Test]
    public Task SubmitStep01NatureInterestsViewModel_WithoutId_ShouldThrowError()
    {
        var model = new LobbyistEmployerRegistrationStep01NatureInterests { Id = null };
        var modelState = new ModelStateDictionary();

        _ = Assert.ThrowsAsync<KeyNotFoundException>(
            async () => await _svc.SubmitStep01NatureInterestViewModel(model, modelState)
        );
        return Task.CompletedTask;
    }

    [Test]
    public async Task SubmitLobbyistEmployerRegistrationFormStep01Agencies_InvalidReturnResponse_WithAgencies()
    {
        var model = new LobbyistEmployerRegistrationStep01Agencies { Id = 77 };
        var modelState = new ModelStateDictionary();

        var agencies = new List<Agency>
        {
            new() { Id = 1, Name = "City Agency" }
        };

        _ = _lobbyistEmployerRegistrationSvc
            .Setup(x => x.UpdateLobbyistEmployerRegistrationPage04(It.IsAny<long>(), It.IsAny<LobbyistEmployerStateAgenciesRequest>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = false });

        _ = _referenceDataSvc.Setup(x => x.GetAllAgencies()).Returns(Task.FromResult<IEnumerable<Agency>>(agencies));

        RegistrationResponseDto result = await _svc.SubmitStep01AgenciesViewModel(model, modelState);

        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public async Task SubmitLobbyistEmployerRegistrationFormStep01NatureInterests_ShouldReturnResponse()
    {
        var model = new LobbyistEmployerRegistrationStep01NatureInterests { Id = 77 };
        var modelState = new ModelStateDictionary();

        _ = _lobbyistEmployerRegistrationSvc
            .Setup(x => x.UpdateLobbyistEmployerRegistrationPage05(It.IsAny<long>(), It.IsAny<LobbyistEmployerLobbyingInterestsRequest>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = true });

        RegistrationResponseDto result = await _svc.SubmitStep01NatureInterestViewModel(model, modelState);

        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public async Task SubmitLobbyistEmployerRegistrationFormStep01NatureInterests_InvalidReturnResponse()
    {
        var model = new LobbyistEmployerRegistrationStep01NatureInterests { Id = 77 };
        var modelState = new ModelStateDictionary();

        _ = _lobbyistEmployerRegistrationSvc
            .Setup(x => x.UpdateLobbyistEmployerRegistrationPage05(It.IsAny<long>(), It.IsAny<LobbyistEmployerLobbyingInterestsRequest>()))
            .ReturnsAsync(new RegistrationResponseDto { Id = 77, Valid = false });

        RegistrationResponseDto result = await _svc.SubmitStep01NatureInterestViewModel(model, modelState);

        Assert.That(result.Id, Is.EqualTo(77));
    }

    [Test]
    public void CreateInHouseLobbyistsGridModel_ShouldReturnsData()
    {
        // Arrange
        long employerId = 1;

        var response = new List<InHouseLobbyistResponseDto>()
        {
            new () {
                Id = employerId,
                FilerId = 1,
                FirstName = "Test",
                LastName = "Unit",
                Email = "<EMAIL>",
                EmployerName = "Employer",
                Name = "Unit Test",
            },
            new () {
                Id = employerId,
                FilerId = 2,
                FirstName = "Lobbyist",
                LastName = "Inhouse",
                Email = "<EMAIL>",
                EmployerName = "Employer",
                Name = "Lobbyist InHouse",
            },
        };

        // Act
        var result = _svc.CreateInHouseLobbyistsGridModel(employerId, response);
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.GridId, Is.Not.Null);
            Assert.That(result.ActionItems, Is.Not.Null);
            Assert.That(result.GridId, Is.EqualTo("InHouseLobbyistsGrid"));
            Assert.That(result.DataSource, Is.InstanceOf<List<InHouseLobbyistResponseDto>>());
            Assert.That(result.DataSource.Count, Is.EqualTo(2));
            Assert.That(result.ActionItems?.Count, Is.EqualTo(3));
        });
    }

    [Test]
    public void CreateInHouseLobbyistsGridModel_ShouldReturnsEmptyGrid()
    {
        // Arrange
        long employerId = 1;

        var response = new List<InHouseLobbyistResponseDto>();

        // Act
        var result = _svc.CreateInHouseLobbyistsGridModel(employerId, response);
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.GridId, Is.Not.Null);
            Assert.That(result.ActionItems, Is.Not.Null);
            Assert.That(result.GridId, Is.EqualTo("InHouseLobbyistsGrid"));
            Assert.That(result.DataSource, Is.InstanceOf<List<InHouseLobbyistResponseDto>>());
            Assert.That(result.DataSource.Count, Is.EqualTo(0));
            Assert.That(result.ActionItems?.Count, Is.EqualTo(3));
        });
    }

    [Test]
    public async Task UnlinkLobbyistFromEmployer_CallService()
    {
        // Arrange
        long employerId = 1;
        long lobbyistId = 1;

        // Act
        await _svc.UnlinkLobbyistFromEmployer(employerId, lobbyistId);

        // Assert
        _lobbyistEmployerRegistrationSvc.Verify(svc => svc.UnlinkLobbyistFromEmployer(It.IsAny<long>(), It.IsAny<long>()), Times.Once);
    }

    #region Initiate Certification Tests
    [Test]
    public async Task GetLobbyistCertificationViewModel_ValidId_ShouldReturnViewModel()
    {
        // Arrange
        long id = 123;
        var lobbyistResponse = new LobbyistResponseDto
        {
            Id = id,
            Version = 1,
            Email = "<EMAIL>",
            SelfRegister = true,
            FirstName = "F1",
            MiddleName = "M",
            LastName = "L1",
            LobbyistEmployerOrLobbyingFirmId = 456,
            LegislativeSessionId = 789,
            IsPlacementAgent = false,
            IsSameAsCandidateAddress = true,
            Agencies = new List<RegistrationAgencyDto>
            {
                new() { AgencyId = 1 },
                new() { AgencyId = 2 }
            },
            DateOfQualification = DateTime.Now.AddDays(-30),
            Addresses = new List<AddressDtoModel>()
            {
                new() {Purpose = "Business" , City = "SD"}
            },
            CompletedEthicsCourse = true
        };

        _lobbyistRegistrationSvc.Setup(x => x.GetLobbyistRegistration(id))
            .ReturnsAsync(lobbyistResponse);

        // Act
        var result = await _svc.GetLobbyistCertificationViewModel(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Version, Is.EqualTo(1));
            Assert.That(result.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(result.SelfRegister, Is.EqualTo(true));
            Assert.That(result.FirstName, Is.EqualTo("F1"));
            Assert.That(result.MiddleName, Is.EqualTo("M"));
            Assert.That(result.LastName, Is.EqualTo("L1"));
            Assert.That(result.LobbyistEmployerOrLobbyingFirmId, Is.EqualTo(456));
            Assert.That(result.LegislativeSessionId, Is.EqualTo(789));
            Assert.That(result.PlacementAgent, Is.EqualTo(false));
            Assert.That(result.IsSameAsBusinessAddress, Is.EqualTo(true));
            Assert.That(result.SelectedAgencies, Has.Count.EqualTo(2));
            Assert.That(result.SelectedAgencies, Contains.Item(1L));
            Assert.That(result.SelectedAgencies, Contains.Item(2L));
            Assert.That(result.QualificationDate, Is.EqualTo(lobbyistResponse.DateOfQualification));
            Assert.That(result.EthicsCourseCompleted, Is.EqualTo(true));
        });

        _lobbyistRegistrationSvc.Verify(x => x.GetLobbyistRegistration(id), Times.Once);
    }

    [Test]
    public void GetLobbyistCertificationViewModel_InvalidId_ShouldThrowKeyNotFoundException()
    {
        // Arrange
        long id = 999;
        _lobbyistRegistrationSvc.Setup(x => x.GetLobbyistRegistration(id))
            .ReturnsAsync((LobbyistResponseDto?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(
            () => _svc.GetLobbyistCertificationViewModel(id));

        Assert.That(ex.Message, Is.EqualTo($"Lobbyist not found. id = {id}"));
        _lobbyistRegistrationSvc.Verify(x => x.GetLobbyistRegistration(id), Times.Once);
    }

    [Test]
    public async Task GetLobbyistCertificationViewModel_WithNullAgencies_ShouldReturnEmptySelectedAgencies()
    {
        // Arrange
        long id = 123;
        var lobbyistResponse = new LobbyistResponseDto
        {
            Id = id,
            Version = 1,
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Cinema",
            Agencies = new List<RegistrationAgencyDto>() // Empty agencies list
        };

        _lobbyistRegistrationSvc.Setup(x => x.GetLobbyistRegistration(id))
            .ReturnsAsync(lobbyistResponse);

        // Act
        var result = await _svc.GetLobbyistCertificationViewModel(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.SelectedAgencies, Is.Not.Null);
        Assert.That(result.SelectedAgencies, Has.Count.EqualTo(0));
    }

    [Test]
    public async Task SaveLobbyistCertificationViewModel_ValidModel_ShouldReturnResponse()
    {
        // Arrange
        var model = new LobbyistCertificationViewModel
        {
            Id = 123,
            FirstName = "John",
            LastName = "Cinema",
            Email = "<EMAIL>",
            PhoneNumberCountryCode = "+1",
            PhoneNumber = "+13-1233-12312",
            FaxNumber = "*******-12312",
            SelectedAgencies = new List<long>() { 1 }
        };

        var expectedResponse = new RegistrationResponseDto()
        {
            Id = 123,
            Valid = true,
            FilerId = 456,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _lobbyistRegistrationSvc.Setup(x => x.UpdateLobbyistRegistration(
            It.IsAny<long>(), It.IsAny<LobbyistRegistrationRequestDto>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _svc.SaveLobbyistCertificationViewModel(model);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(123));
            Assert.That(result.Valid, Is.EqualTo(true));
            Assert.That(result.FilerId, Is.EqualTo(456));
        });

        _lobbyistRegistrationSvc.Verify(x => x.UpdateLobbyistRegistration(
            123, It.IsAny<LobbyistRegistrationRequestDto>()), Times.Once);
    }

    [Test]
    public async Task SaveLobbyistCertificationViewModel_ModelWithNullId_ShouldUseDefaultValue()
    {
        // Arrange
        var model = new LobbyistCertificationViewModel
        {
            Id = null, // Null ID
            FirstName = "John",
            LastName = "Cinema"
        };

        var expectedResponse = new RegistrationResponseDto()
        {
            Id = 0,
            Valid = true,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>()
        };

        _lobbyistRegistrationSvc.Setup(x => x.UpdateLobbyistRegistration(
            It.IsAny<long>(), It.IsAny<LobbyistRegistrationRequestDto>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _svc.SaveLobbyistCertificationViewModel(model);

        // Assert
        Assert.That(result, Is.Not.Null);
        _lobbyistRegistrationSvc.Verify(x => x.UpdateLobbyistRegistration(
            0, It.IsAny<LobbyistRegistrationRequestDto>()), Times.Once);
    }
    #endregion
}
