using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using NSubstitute;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.ControllerServices.SharedServices;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.AmendSmoRegistration;
using SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using AddressDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.AddressDto;
using FilerRole = SOS.CalAccess.Models.Authorization.FilerRole;
using PendingItemDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.PendingItemDto;
using PhoneNumberDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.PhoneNumberDto;
using RegistrationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto;
using SlateMailerOrganizationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SlateMailerOrganizationRequest;
using SmoContactRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoContactRequest;
using SmoOfficerGridDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoOfficerGridDto;
using SmoRegistrationAttestationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationAttestationResponseDto;
using SmoRegistrationContactDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationContactDto;
using SmoRegistrationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationResponseDto;
using SmoRegistrationSendForAttestationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationSendForAttestationRequest;
using TreasurerAcknowledgementContactResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.TreasurerAcknowledgementContactResponseDto;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[TestFixture]
public class AmendSmoRegistrationCtlSvcTests
{
    private AmendSmoRegistrationCtlSvc _service;
    private Generated.IUsersApi _usersApiMock;
    private Generated.IFilersApi _filersApiMock;
    private IStringLocalizer<SharedResources> _localizer;
    private ISmoRegistrationSvc _smoRegistrationSvcMock;
    private ISmoRegistrationCtlSvc _smoRegistrationCtlSvc;
    private IAccuMailValidatorService _accuMailValidatorSvc;
    private ISharedSmoRegistrationCtlSvc _sharedSmoRegistrationCtlSvcMock;
    private IDecisionsValidationMapService _decisionsValidationMapServiceMock;
    private IDateTimeSvc _mockDateTimeSvc;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _usersApiMock = Substitute.For<Generated.IUsersApi>();
        _filersApiMock = Substitute.For<Generated.IFilersApi>();
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _smoRegistrationSvcMock = Substitute.For<ISmoRegistrationSvc>();
        _accuMailValidatorSvc = Substitute.For<IAccuMailValidatorService>();
        _sharedSmoRegistrationCtlSvcMock = Substitute.For<ISharedSmoRegistrationCtlSvc>();
        _decisionsValidationMapServiceMock = Substitute.For<IDecisionsValidationMapService>();
        _mockDateTimeSvc = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _smoRegistrationCtlSvc = new SmoRegistrationCtlSvc(_smoRegistrationSvcMock, _mockDateTimeSvc, _localizer, _usersApiMock, _filersApiMock, _accuMailValidatorSvc, _sharedSmoRegistrationCtlSvcMock, _decisionsValidationMapServiceMock);

        _localizer[Arg.Any<string>()].Returns(call =>
        {
            var key = call.Arg<string>();
            return new LocalizedString(key, key);
        });

        _service = new AmendSmoRegistrationCtlSvc(_smoRegistrationSvcMock, _mockDateTimeSvc, _localizer, _usersApiMock, _smoRegistrationCtlSvc, _sharedSmoRegistrationCtlSvcMock, _decisionsValidationMapServiceMock);
    }

    [Test]
    public async Task GetPage01ViewModel_WhenRegistrationFilingResponseIsNull_ReturnsNull()
    {
        // Arrange
        long registrationId = 123;
        _ = _smoRegistrationSvcMock
                .CreateSmoAmendmentRegistrationAsync(registrationId);

        // Act
        var result = await _service.GetPage01ViewModel(registrationId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetPage01ViewModel_WhenRegistrationFilingResponseHasData_ReturnsViewModelWithMappedProperties()
    {
        // Arrange
        long registrationId = 456;
        var responseData = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            Address = new List<AddressDto>
            {
                new()
                {
                    Street = "101 Test",
                    Street2 = "Suite 1000",
                    City = "Sacramento",
                    State = "CA",
                    Zip = "94203",
                    Country = "United States",
                    Type = "Residential",
                    Purpose =  "Mailing"
                },
                new()
                {
                    Street = "100 Test",
                    Street2 = "Suite 100",
                    City = "Sacramento",
                    State = "CA",
                    Zip = "94203",
                    Country = "United States",
                    Type = "Residential",
                    Purpose =  "Organization"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>
            {
                new()
                {
                    Type = "Home",
                    Number = "9165551234"
                },
                new()
                {
                    Type = "Fax",
                    Number = "+9165555678"
                }
            },
        };

        // Arrange
        _smoRegistrationSvcMock
            .CreateSmoAmendmentRegistrationAsync(registrationId)
            .Returns(Task.FromResult(responseData));

        // Act
        var result = await _service.GetPage01ViewModel(registrationId, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.TypeOf<AmendSmoRegistrationStep01ViewModel>());
            Assert.That(result!.Id, Is.EqualTo(responseData.Id));
        });
    }

    [Test]
    [TestCase(null)]
    [TestCase(1)]
    public async Task GetPage02ViewModel_ValidId_ReturnsViewModel(long? filerId)
    {
        // Arrange
        var validId = 123; // A valid ID to simulate a proper response
        var mockResponse = new SmoRegistrationResponseDto
        {
            Id = 123,
            ActivityLevel = "city",
            QualifiedCommittee = true,
            CampaignCommittee = true,
            DateQualified = _dateNow,
            CommitteeType = "Political",
            FilerId = filerId,
        };

        _smoRegistrationSvcMock
            .GetRegistrationFilingById(validId)
            .Returns(Task.FromResult<SmoRegistrationResponseDto?>(mockResponse));

        var result = await _service.GetPage02ViewModel(validId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(validId));  // Ensure the Id matches the one provided
            Assert.That(result.OrganizationLevelOfActivity, Is.EqualTo(mockResponse.ActivityLevel));  // Verify ActivityLevel mapping
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOrganizationQualified, Is.EqualTo(mockResponse.QualifiedCommittee));  // Verify QualifiedCommittee mapping
            Assert.That(result.IsOrganizationCampaignCommittee, Is.EqualTo(mockResponse.CampaignCommittee));  // Verify CampaignCommittee mapping
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.DateQualifiedAsSMO, Is.EqualTo(mockResponse.DateQualified));  // Verify DateQualifiedAsSMO mapping
        });
    }

    [Test]
    [TestCase(null)]
    [TestCase(1)]
    public async Task GetPage02ViewModel_CatchError_ReturnsNull(long? filerId)
    {
        // Arrange
        var validId = 123; // A valid ID to simulate a proper response
        var mockResponse = new SmoRegistrationResponseDto
        {
            Id = 123,
            ActivityLevel = "city",
            QualifiedCommittee = true,
            CampaignCommittee = true,
            DateQualified = _dateNow,
            CommitteeType = "Political",
            FilerId = filerId,
        };

        var result = await _service.GetPage02ViewModel(validId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task Page02ContinueSubmit_WhenResponseIsInvalid_AddsErrorsToModelState()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel { Id = 100 };
        var modelState = new ModelStateDictionary();

        var response = new RegistrationResponseDto
        {
            Id = null,
            Valid = false,
            ValidationErrors = new List<CalAccess.Models.Common.WorkFlowError>
            {
                new("OrganizationLevelOfActivity", "ErrGlobal0001", "Validation", "Invalid organization activity level.")
            }
        };

        _smoRegistrationSvcMock
            .UpdateSmoRegistration(Arg.Any<long>(), Arg.Any<SlateMailerOrganizationRequest>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _service.Page02ContinueSubmit(model, modelState, true);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            var errors = modelState["OrganizationLevelOfActivity"]?.Errors;
            Assert.That(errors, Is.Not.Empty);
        });
    }

    [Test]
    public async Task Page01Submit_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Act
        var model = new AmendSmoRegistrationStep01ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var request = new SmoContactRequest();
        var response = new RegistrationResponseDto
        {
            Id = 1,
        };

        _sharedSmoRegistrationCtlSvcMock
            .MapSmoViewModelToRequest(Arg.Any<AmendSmoRegistrationStep01ViewModel>())
            .Returns(request);
        _sharedSmoRegistrationCtlSvcMock
            .SubmitSmoRegistrationForm(Arg.Any<AmendSmoRegistrationStep01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<SmoContactRequest>())
            .Returns(response);

        // Arrange
        var result = await _service.Page01Submit(model, modelState, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(1));
        });

    }
    

    [Test]
    public void Page02ContinueSubmit_WhenIdIs0_ThrowException()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel
        {
            Id = 0,
            DateQualifiedAsSMO = _dateNow.AddDays(1),
        };
        var modelState = new ModelStateDictionary();

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _service.Page02ContinueSubmit(model, modelState, true));
        Assert.That(ex.Message, Is.EqualTo($"Invalid Registration ID."));
    }

    [Test]
    public void Page02ContinueSubmit_CatchError_ThrowException()
    {
        // Arrange
        var model = new AmendSmoRegistrationDetailsStep01ViewModel
        {
            Id = 123,
        };
        var modelState = new ModelStateDictionary();


        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _service.Page02ContinueSubmit(model, modelState, true));
        Assert.That(ex.Message, Does.Contain("Invalid request:"));
    }

    [Test]
    public async Task Page07Or0GetEmptyViewModel_ReturnsDefaultViewModel()
    {
        // Arrange
        long id = 1;

        // Act
        var result = await _service.Page05GetEmptyViewModel(id);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Id, Is.EqualTo(1));
            Assert.That(result.Addresses, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task GetPage05ViewModel_WhenResponse_IdReturnsNull()
    {
        // Arrange
        var smoOfficer = new SmoRegistrationContactDto
        {
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        _smoRegistrationSvcMock
            .GetSmoTreasurerPage02(123)
            .Returns(smoOfficer);

        // Act
        var result = await _service.GetPage05ViewModel(123, default);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetPage05ViewModel_WhenResponse_IdReturnsViewModel()
    {
        // Arrange
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 1,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        _smoRegistrationSvcMock
            .GetSmoTreasurerPage02(123)
            .Returns(smoOfficer);

        // Act
        var result = await _service.GetPage05ViewModel(123, default);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
    }

    #region PagePrefill
    [Test]
    public async Task PagePrefill_WhenContactIdNotNull_ReturnsViewModel()
    {
        // Arrange
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 1,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        _smoRegistrationSvcMock
            .GetSmoOfficer(123, 456)
            .Returns(Task.FromResult(smoOfficer));

        // Act
        var result = await _service.PagePrefill(123, 456, false, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task PagePrefill_WhenContactIdIsNull_ReturnsViewModel()
    {
        // Arrange
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 1,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );
        _smoRegistrationSvcMock
            .GetSmoOfficer(123, 456)
            .Returns(Task.FromResult(smoOfficer));

        _usersApiMock
            .GetSelf(default)
            .Returns(Task.FromResult(userItemResponse));

        // Act
        var result = await _service.PagePrefill(123, 456, false, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task PagePrefill_WhenIsTreasurerOrOfficerIsTrue_ReturnViewModel()
    {
        // Arrange
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 1,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );
        _smoRegistrationSvcMock
            .GetSmoOfficer(123, 456)
            .Returns(Task.FromResult(smoOfficer));

        _usersApiMock
            .GetSelf(default)
            .Returns(Task.FromResult(userItemResponse));

        // Act
        var result = await _service.PagePrefill(123, null, true, default);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task PagePrefill_WhenContactIdIsNullAndUserIsNotOfficer_ReturnNull()
    {
        // Arrange
        var registrationId = 1L;

        // Act
        var result = await _service.PagePrefill(registrationId, null, null, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Null);
    }

    #endregion

    [Test]
    public async Task Page05GetEmptyViewModel_ReturnsViewModel()
    {
        // Act
        var result = await _service.Page05GetEmptyViewModel(123);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page05Submit_ReturnsId_WhenValid()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep02ViewModel { Id = 123, ContactId = 123, ContactTitle = "Treasurer" };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto { Id = 123, Valid = true, StatusId = 1 };
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 123,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );

        _smoRegistrationSvcMock
            .PostSmoRegistrationContactsPage05(Arg.Any<long>(), Arg.Any<SmoRegistrationContactDto>())
            .Returns(Task.FromResult(response));

        _usersApiMock
            .GetSelf(default)
            .Returns(Task.FromResult(userItemResponse));

        // Act
        var result = await _service.Page05Submit(model, modelState, default, true);

        // Assert
        Assert.That(result, Is.EqualTo(123));
    }

    [Test]
    public async Task Page05Submit_ReturnsId_WhenInvalid()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep02ViewModel { Id = 123, ContactId = 123, ContactTitle = "Treasurer" };
        var modelState = new ModelStateDictionary();
        var validationErrors = new List<CalAccess.Models.Common.WorkFlowError>
        {
            new("Email", "ErrGlobal0001", "Validation", "Invalid email format")
        };
        var response = new RegistrationResponseDto { Id = 123, Valid = false, StatusId = 1, ValidationErrors = validationErrors };
        var smoOfficer = new SmoRegistrationContactDto
        {
            Id = 123,
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                InternationalNumber = false,
                CountryCode = "+1-USA",
                Extension = "",
                Number = "1112222",
                Type = "Phone"
            },
            Address = new AddressDto
            {
                Id = 1,
                Street = "Street",
                Street2 = "Street2",
                City = "City",
                State = "CA",
                Zip = "90001",
                Type = "Residential",
                Country = "United States",
                Purpose = "Officer"
            }
        };
        var userItemResponse = new Generated.UserItemResponse
        (
            "First Name",
            "Last Name",
            1,
            "<EMAIL>"
        );

        _smoRegistrationSvcMock
            .PostSmoRegistrationContactsPage05(Arg.Any<long>(), Arg.Any<SmoRegistrationContactDto>())
            .Returns(Task.FromResult(response));

        _usersApiMock
            .GetSelf(default)
            .Returns(Task.FromResult(userItemResponse));

        // Act
        var result = await _service.Page05Submit(model, modelState, default, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(123));
            Assert.That(modelState.IsValid, Is.False);
        }); ;
    }

    [Test]
    public async Task GetPage06ViewModel_WithNoOfficers_ReturnsEmptyViewModel()
    {
        // Arrange
        long testId = 1;
        var officers = new List<SmoOfficerGridDto>
        {
            new() { Role = FilerRole.SlateMailerOrg_AccountManager.Name, OfficerName = "John Doe", ContactId = 123 }
        };

        _smoRegistrationSvcMock.GetSmoOfficers(testId).Returns(Task.FromResult<IEnumerable<SmoOfficerGridDto>>(officers));

        // Act
        var result = await _service.GetPage06ViewModel(testId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(testId));
    }

    [Test]
    public async Task GetPage06ViewModel_WithValidOfficers_ReturnsViewModel()
    {
        // Arrange
        long testId = 1;
        var officers = new List<SmoOfficerGridDto>
        {
            new() { Role = "SomeOtherRole", OfficerName = "John Doe", ContactId = 123 }
        };

        _smoRegistrationSvcMock.GetSmoOfficers(testId).Returns(Task.FromResult<IEnumerable<SmoOfficerGridDto>>(officers));

        // Act
        var result = await _service.GetPage06ViewModel(testId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(testId));
    }

    [Test]
    public async Task TransferTreasurer_WithInvalidResponse_AddsErrorsToModelState()
    {
        // Arrange
        var registrationId = 1L;
        var newTreasurerId = 2L;
        var keepOldTreasurer = true;
        var newTitle = "Assistant Treasurer";

        var modelState = new ModelStateDictionary();

        var validationErrors = new List<WorkFlowError>
        {
            new("Title", "ErrGlobal0002", "Validation", "An assistant treasurer already exists.")
        };

        var response = new RegistrationResponseDto(registrationId, false, validationErrors);

        _smoRegistrationSvcMock
            .PostSmoRegistrationTransferTreasurer(Arg.Any<long>(), Arg.Any<SmoTreasurerTransferDto>())
            .Returns(Task.FromResult(response));

        // Act
        await _service.TransferTreasurer(registrationId, newTreasurerId, keepOldTreasurer, newTitle, modelState);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.ContainsKey("ContactTitle"), Is.True);
            Assert.That(modelState["ContactTitle"]?.Errors[0].ErrorMessage, Is.EqualTo("An assistant treasurer already exists."));
        });
    }

    [Test]
    public async Task Page08GetEmptyViewModel_ReturnsViewModel_WithEmptyAddress()
    {
        // Act
        var result = await _service.Page08GetEmptyViewModel(123);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(123));
            Assert.That(result.Addresses, Is.Not.Null.And.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task GetPage09ViewModel_WithValidAuthorizers_ReturnsViewModelWithGrid()
    {
        // Arrange
        long testId = 1;
        var officers = new List<SmoOfficerGridDto>
    {
        new() { Id = 123, OfficerName = "Jane Doe", CanAuthorize = true, PhoneNumber = new PhoneNumberDto { Number = "123456789" } }
    };

        _smoRegistrationSvcMock.GetSmoOfficers(testId).Returns(officers);


        // Act
        var result = await _service.GetPage09ViewModel(testId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(testId));
    }

    [Test]
    public async Task Termination02ContinueSubmit_WithInvalidId_AddsModelError()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel { Id = null };
        var modelState = new ModelStateDictionary();

        // Act
        var result = await _service.Termination02ContinueSubmit(model, modelState, CancellationToken.None);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(modelState.ContainsKey(string.Empty), Is.True);
            Assert.That(modelState[string.Empty]?.Errors[0].ErrorMessage, Is.EqualTo("Missing registration ID."));
        });
    }

    [Test]
    public async Task Termination02ContinueSubmit_WithValidationErrors_AddsToModelState()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            EffectiveDateOfTermination = new DateTime(2025, 4, 30, 0, 0, 0, DateTimeKind.Local)
        };
        var modelState = new ModelStateDictionary();
        var validationErrors = new List<WorkFlowError>
    {
        new("EffectiveDateOfTermination", "E001", "Validation", "Effective date is required.")
    };
        _decisionsValidationMapServiceMock
            .When(s => s.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                Arg.Any<ModelStateDictionary>()))
            .Do(call =>
            {
                var errors = call.ArgAt<List<WorkFlowError>>(1);
                var modelState = call.ArgAt<ModelStateDictionary>(2);

                foreach (var error in errors)
                {
                    modelState.AddModelError(error.FieldName, error.Message);
                }
            });


        _smoRegistrationSvcMock
            .UpdateNoticeOfTerminationSmoRegistrationAsync(1, Arg.Any<NoticeOfTerminationRequest>())
            .Returns(new RegistrationResponseDto(1, false, validationErrors));

        // Act
        var result = await _service.Termination02ContinueSubmit(model, modelState, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(modelState.ContainsKey("EffectiveDateOfTermination"), Is.True);
            Assert.That(modelState["EffectiveDateOfTermination"]?.Errors[0].ErrorMessage, Is.Not.Null);
        });
    }

    [Test]
    public async Task Termination02ContinueSubmit_ValidFlow_ReturnsSameModel()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
            EffectiveDateOfTermination = new DateTime(2025, 04, 30, 0, 0, 0, DateTimeKind.Local)
        };
        var modelState = new ModelStateDictionary();

        _smoRegistrationSvcMock
            .UpdateNoticeOfTerminationSmoRegistrationAsync(1, Arg.Any<NoticeOfTerminationRequest>())
            .Returns(new RegistrationResponseDto(1, true, new List<WorkFlowError>()));

        // Act
        var result = await _service.Termination02ContinueSubmit(model, modelState, CancellationToken.None);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.EqualTo(model));
            Assert.That(modelState.IsValid, Is.True);
        });
    }

    [Test]
    public void GetPage11ViewModel_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var isAuthorizedToCompleteTreasurerAck = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetPage11ViewModel(null, isAuthorizedToCompleteTreasurerAck, CancellationToken.None));
        Assert.That(ex.Message, Is.EqualTo($"No registration id exists for registration"));
    }

    [Test]
    public void GetPage11ViewModel_WhenIdIsInvalid_ThrowException()
    {
        // Arrange
        var isAuthorizedToCompleteTreasurerAck = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _service.GetPage11ViewModel(1, isAuthorizedToCompleteTreasurerAck, CancellationToken.None));
        Assert.That(ex.Message, Is.EqualTo($"Invalid Registration ID."));
    }

    [TestCaseSource(nameof(GetPage11ViewModelTestCases))]
    public async Task GetPage11ViewModel_ReturnViewModel(List<SmoRegistrationContactDto> contacts)
    {
        // Arrange
        var registrationId = 1;
        var response = new TreasurerAcknowledgementContactResponseDto
        {
            FilerId = 1,
            Contacts = contacts
        };
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", registrationId, "Test");
        var smoResponse = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            FilerId = 1,
            Email = "Test",
        };
        var isAuthorizedToCompleteTreasurerAck = true;

        _smoRegistrationSvcMock
            .GetTreasurerAcknowledgementContactsAsync(Arg.Any<long>())
            .Returns(Task.FromResult(response));
        _usersApiMock
            .GetSelf(default)
            .Returns(Task.FromResult(userResponse));
        _ = _smoRegistrationSvcMock
            .GetRegistrationFilingById(Arg.Any<long>())
            .Returns(Task.FromResult<SmoRegistrationResponseDto?>(smoResponse));

        // Act
        var result = await _service.GetPage11ViewModel(registrationId, isAuthorizedToCompleteTreasurerAck, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<AmendSmoRegistrationStep04ViewModel>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public void Page11Submit_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel { };

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.Page11Submit(model));
        Assert.That(ex.Message, Is.EqualTo($"No registration id exists for registration"));
    }

    [TestCaseSource(nameof(GetPage11ViewModelTestCases))]
    public async Task Page11Submit_WhenModelIsValid_ShouldSubmitSuccessfullyAndReturnModel(List<SmoRegistrationContactDto> contacts)
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var response = new TreasurerAcknowledgementContactResponseDto
        {
            FilerId = 1,
            Contacts = contacts
        };

        await _smoRegistrationSvcMock.CompleteTreasurerAcknowledgementAsync(Arg.Any<long>());

        _smoRegistrationSvcMock
            .GetTreasurerAcknowledgementContactsAsync(Arg.Any<long>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _service.Page11Submit(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<AmendSmoRegistrationStep04ViewModel>());
            Assert.That(result.Id, Is.EqualTo(model.Id));
        });
    }

    [Test]
    public void Page11SendForAcknowledgement_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel { };

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.Page11SendForAcknowledgement(model));
        Assert.That(ex.Message, Is.EqualTo($"No registration id exists for registration"));
    }

    [Test]
    public void Page12GetViewModel_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var isAuthorizedToAttest = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetPage12ViewModel(null, isAuthorizedToAttest));
        Assert.That(ex.Message, Is.EqualTo($"No registration id exists for registration"));
    }

    [Test]
    public void Page12GetViewModel_WhenIdIsInvalid_ThrowException()
    {
        // Arrange
        var isAuthorizedToAttest = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _service.GetPage12ViewModel(1, isAuthorizedToAttest));
        Assert.That(ex.Message, Is.EqualTo($"Invalid Registration ID."));
    }

    [Test]
    public void Page15GetViewModel_WhenIdIsInvalid_ThrowException()
    {
        // Arrange
        var isAuthorizedToAttest = true;

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => _smoRegistrationCtlSvc.GetPage15ViewModel(1, isAuthorizedToAttest));
        Assert.That(ex.Message, Is.EqualTo($"Invalid ID."));
    }

    [TestCaseSource(nameof(GetPage11ViewModelTestCases))]
    public async Task GetPage12ViewModel_ReturnViewModel(List<SmoRegistrationContactDto?> contacts)
    {
        // Arrange
        var registrationId = 1;
        var userId = 1L;
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", userId, "Test");
        var smoResponse = new SmoRegistrationResponseDto { Id = registrationId, FilerId = 1, Email = "Test" };
        var attestationResponse = new SmoRegistrationAttestationResponseDto
        {
            FirstName = "Test",
            LastName = "Test",
            ExecutedAt = _dateNow,
            Title = "Treasurer",
        };
        var isAuthorizedToAttest = true;

        _usersApiMock.GetSelf(default).Returns(userResponse);
        _smoRegistrationSvcMock.GetRegistrationFilingById(registrationId).Returns(smoResponse);
        _smoRegistrationSvcMock.GetResponsibleOfficerContactsAsync(Arg.Any<long>())
            .Returns([.. contacts.Where(c => c != null).Select(c => c!)]);
        _smoRegistrationSvcMock.GetRegistrationAttestationAsync(Arg.Any<long>())
            .Returns(attestationResponse);

        // Act
        var result = await _service.GetPage12ViewModel(registrationId, isAuthorizedToAttest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<AmendSmoRegistrationStep04ViewModel>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public void Page12AttestRegistration_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel { };
        var modelState = new ModelStateDictionary();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.Page12AttestRegistration(model, modelState));
        Assert.That(ex.Message, Is.EqualTo($"No registration id exists for registration"));
    }

    [Test]
    public async Task Page12AttestRegistration_WhenModelIsValid_ShouldAttestRegistrationSuccessfully()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = true,
        };

        _smoRegistrationSvcMock
            .AttestRegistrationAsync(Arg.Any<long>())
            .Returns(Task.FromResult(response));

        // Act
        await _smoRegistrationCtlSvc.Page15AttestRegistration(model, modelState);

        // Assert
        Assert.That(modelState.IsValid, Is.True);
    }

    [Test]
    public async Task Page12AttestRegistration_WhenModelIsInvalid_ShouldAddErrorToModel()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = false,
            ValidationErrors =
            [
                new("Test", "ErrGlobal0001", "Validation", "Test." )
            ]
        };

        _smoRegistrationSvcMock
            .AttestRegistrationAsync(Arg.Any<long>())
            .Returns(Task.FromResult(response));

        // Act
        await _smoRegistrationCtlSvc.Page15AttestRegistration(model, modelState);

        // Assert
        Assert.That(modelState.IsValid, Is.False);
    }

    [Test]
    public void Page12SendForAttestation_WhenIdIsNull_ThrowException()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel { };
        var modelState = new ModelStateDictionary();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.Page12SendForAttestation(model, modelState));
        Assert.That(ex.Message, Is.EqualTo($"No registration id exists for registration"));
    }

    [Test]
    public async Task Page12SendForAttestation_WhenModelIsValid_ShouldSendForAttestationSuccessfully()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = true,
        };

        _smoRegistrationSvcMock
            .SendRegistrationForAttestationAsync(Arg.Any<long>(), Arg.Any<SmoRegistrationSendForAttestationRequest>())
            .Returns(Task.FromResult(response));

        // Act
        await _service.Page12SendForAttestation(model, modelState);

        // Assert
        Assert.That(modelState.IsValid, Is.True);
    }

    [Test]
    public async Task Page12SendForAttestation_WhenModelIsInvalid_ShouldAddErrorToModel()
    {
        // Arrange
        var model = new AmendSmoRegistrationStep04ViewModel
        {
            Id = 1,
        };
        var modelState = new ModelStateDictionary();
        var response = new RegistrationResponseDto
        {
            Valid = false,
            ValidationErrors =
            [
                new("Test", "ErrGlobal0001", "Validation", "Test." )
            ]
        };

        _smoRegistrationSvcMock
            .SendRegistrationForAttestationAsync(Arg.Any<long>(), Arg.Any<SmoRegistrationSendForAttestationRequest>())
            .Returns(Task.FromResult(response));

        // Act
        await _service.Page12SendForAttestation(model, modelState);

        // Assert
        Assert.That(modelState.IsValid, Is.False);
    }

    [Test]
    public async Task Page13GetViewModel_WhenModelIsValid_ShouldAttestRegistrationSuccessfully()
    {
        // Arrange
        var registrationId = 1;
        var smoRegistrationResponse = new SmoRegistrationResponseDto
        {
            Id = registrationId,
            SubmittedAt = _dateNow,
        };
        var pendingItemDtos = new List<PendingItemDto>
        {
            new()
            {
                Item = "Test",
                Status = "Test",
            }
        };

        _smoRegistrationSvcMock
            .GetRegistrationFilingById(registrationId)
            .Returns(smoRegistrationResponse);
        _smoRegistrationSvcMock
            .GetPendingItemsAsync(registrationId)
            .Returns(Task.FromResult(pendingItemDtos));

        // Act
        var result = await _service.Page13GetViewModel(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ConfirmationViewModel>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
            Assert.That(result.ExecutedOn, Is.EqualTo(smoRegistrationResponse.SubmittedAt));
            Assert.That(result.PendingItems, Is.Not.Empty);
        });
    }

    #region Shared
    [Test]
    public async Task InitStep02EmptyViewModelAsync_ShouldCreateEmptyViewModel_NoErrors()
    {
        // Arrange
        var id = 1L;
        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(Arg.Any<long>()).Returns(Task.FromResult(true));

        // Act
        var result = await _service.InitStep02EmptyViewModelAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<AmendSmoRegistrationStep02ViewModel>());
            Assert.That(result.IsTerminating, Is.True);
        });
    }

    [Test]
    public async Task InitStep03EmptyViewModelAsync_ShouldCreateEmptyViewModel_NoErrors()
    {
        // Arrange
        var id = 1L;
        _smoRegistrationSvcMock.IsRegistrationTerminatingAsync(Arg.Any<long>()).Returns(Task.FromResult(true));

        // Act
        var result = await _service.InitStep03EmptyViewModelAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<AmendSmoRegistrationStep03ViewModel>());
            Assert.That(result.IsTerminating, Is.True);
        });
    }


    #endregion

    private static IEnumerable<object[]> GetPage11ViewModelTestCases()
    {
        yield return new object[]
        {
            new List<SmoRegistrationContactDto>
            {
                new()
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Test1",
                },
                new()
                {
                    Id = 2,
                    FirstName = "Test",
                    LastName = "Test",
                    Role = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                    HasAcknowledged = false,
                    Title = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                    UserId = 2L,
                },
            },
        };

        yield return new object[]
        {
            new List<SmoRegistrationContactDto>
            {
                new()
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Test",
                    Role = FilerRole.SlateMailerOrg_Treasurer.Name,
                    HasAcknowledged = true,
                    Title = FilerRole.SlateMailerOrg_Treasurer.Name,
                    AcknowledgedOn = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                    UserId = 1,
                },
                new()
                {
                    Id = 2,
                    FirstName = "Test",
                    LastName = "Test",
                    Role = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                    HasAcknowledged = false,
                    Title = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                    UserId = 2L,
                },
            },
        };

        yield return new object[]
        {
            new List<SmoRegistrationContactDto>
            {
                new()
                {
                    Id = 1,
                    FirstName = "Test",
                    LastName = "Test",
                    Role = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                    HasAcknowledged = false,
                    Title = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                    UserId = 1L,
                },
            },
        };
    }

    [Test]
    public async Task IsTreasurerAssistantTreasurerOfficer_True()
    {
        // Arrange
        var registrationId = 1;

        _smoRegistrationSvcMock
            .GetCurrentUserRoleByIdAsync(registrationId)
            .Returns(93);

        // Act
        var result = await _service.IsUserTreasurerAssistantTreasurerOfficer(registrationId);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public async Task IsTreasurerAssistantTreasurerOfficer_False()
    {
        // Arrange
        var registrationId = 1;

        _smoRegistrationSvcMock
            .GetCurrentUserRoleByIdAsync(registrationId)
            .Returns(999);

        // Act
        var result = await _service.IsUserTreasurerAssistantTreasurerOfficer(registrationId);

        // Assert
        Assert.That(result, Is.False);
    }
}
