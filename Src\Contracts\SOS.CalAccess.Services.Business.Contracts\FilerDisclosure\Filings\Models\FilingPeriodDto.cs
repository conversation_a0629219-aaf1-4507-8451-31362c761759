namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;

/// <summary>
/// Represents a DTO for a filing period.
/// </summary>
public class FilingPeriodDto
{
    /// <summary>
    /// Gets or sets the unique identifier for the filing period.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the start date of the filing period.
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date of the filing period.
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Gets or sets whether the given filer has a filing for this period.
    /// </summary>
    public bool HasFiling { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether this filing period is marked as removed.
    /// </summary>
    public bool IsRemoved { get; set; }
}
