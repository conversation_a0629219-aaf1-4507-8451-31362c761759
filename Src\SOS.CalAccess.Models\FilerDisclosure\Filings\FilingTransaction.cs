// <copyright file="FilingTransaction.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations.Schema;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Models.FilerDisclosure.Filings;

/// <summary>
/// Class that represents the relationship between filings and transactions linked to that filing.
/// </summary>
[Table("FilingTransaction")]
[Documentation(
    "",
    Context = "This table holds the relationship between filings and transactions linked to that filing.")]
public class FilingTransaction : IIdentifiable<long>
{
    /// <summary>
    /// Gets or sets the relationship identifier.
    /// </summary>
    [Documentation("Relationship identifier.")]
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the filing id to which the transaction is linked.
    /// </summary>
    [Documentation("Filing to which the transaction is linked.", Context = "Reference to a filing.")]
    public long FilingId { get; set; }

    /// <summary>
    /// Gets or sets the filing to which the transaction is linked.
    /// </summary>
    public Filing? Filing { get; set; }

    /// <summary>
    /// Gets or sets the transaction id to which the filing is linked.
    /// </summary>
    [Documentation("Transaction linked to the filing.", Context = "Reference to a transaction.")]
    public long TransactionId { get; set; }

    /// <summary>
    /// Gets or sets the transaction to which the filing is linked.
    /// </summary>
    public Transaction? Transaction { get; set; }

    /// <summary>
    /// Gets or sets the active status of a filing transaction.
    /// </summary>
    [Documentation("The flag indicates the active status of this record (soft-delete). The default value is true(1).")]
    public bool Active { get; set; } = true;

    //TODO: Clean up models below this line

    /// <summary>
    /// Gets or sets the created by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who created this record.")]
    public long CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the modified by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who last modified this record.")]
    public long ModifiedBy { get; set; }

}
