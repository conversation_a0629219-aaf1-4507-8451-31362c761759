// <copyright file="DeleteTransactionTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Transactions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Transactions;

/// <summary>
/// Contains tests for the DeleteTransaction operation.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[TestOf(typeof(DeleteTransaction))]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public sealed class DeleteTransactionTests
{
    /// <summary>
    /// Tests that the DeleteTransaction operation fails with NotFound when the transaction does not exist.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [Test]
    public async Task DeleteTransaction_FailsWithNotFound_WhenTransactionDoesNotExist()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _ = await context.PrepareTransactionsData();
        var dateTimeSvc = Substitute.For<IDateTimeSvc>();
        var commandProcessor = new DeleteTransaction(context, dateTimeSvc);

        long nonExistentTransactionId = default;
        var result = await commandProcessor.Execute(nonExistentTransactionId);

        Assert.That(result, Is.AssignableFrom(typeof(Failure<Transaction>.NotFound)));
    }

    /// <summary>
    /// Tests that the DeleteTransaction operation fails with InvalidState when the transaction is already deleted.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [Test]
    public async Task DeleteTransaction_FailsWithInvalidState_WhenTransactionIsAlreadyDeleted()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var dateTimeSvc = Substitute.For<IDateTimeSvc>();
        var commandProcessor = new DeleteTransaction(context, dateTimeSvc);

        var data = (await context.PrepareTransactionsData())[0];
        var result = await commandProcessor.Execute(data.DeletedTransactionId);

        Assert.That(result, Is.AssignableFrom(typeof(Failure<Transaction>.InvalidState)));
    }

    /// <summary>
    /// Tests that the DeleteTransaction operation succeeds when the transaction is found.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [Test]
    public async Task DeleteTransaction_Succeeds_WhenIsFound()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var data = (await context.PrepareTransactionsData())[0];

        var dateTimeSvc = Substitute.For<IDateTimeSvc>();
        var commandProcessor = new DeleteTransaction(context, dateTimeSvc);

        var transactionId = data.TransactionId;
        var result = await commandProcessor.Execute(transactionId);

        Assert.That(result, Is.AssignableFrom(typeof(Success<Transaction>)));
    }
}
