using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Filings;

/// <summary>
/// Represents the repository for managing <see cref="FilingPeriod"/> entities in the database.
/// </summary>
/// <remarks>
/// This class provides specific data access methods for the <see cref="FilingPeriod"/> entity 
/// by extending the generic <see cref="Repository{T, TId}"/> class and implementing the 
/// <see cref="IFilingPeriodRepository"/> interface.
/// </remarks>
/// <param name="dbContext">The database context to be used for database operations.</param>
public class FilingPeriodRepository(DatabaseContext dbContext) : Repository<FilingPeriod, long>(dbContext), IFilingPeriodRepository
{
    public async Task<DateTime> GetFilingPeriodStartDateForFiling(long filingId)
    {
        // Retrieves the start date of the filing period associated with a specific filing.
        // If the pre-defined filing period is null, it returns the start date of the filing itself (custom period)
        return await dbContext.Set<Filing>()
            .Where(f => f.Id == filingId)
            .Select(f => f.FilingPeriod != null ? f.FilingPeriod.StartDate : f.StartDate)
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<List<FilingPeriod>> FindAllUnreportedFilingPeriodsSmoCampaignStatementByFiler(long filerId)
    {
        // TD: Currently this work for SMO Campaign Statement as the Name is not null
        // Need to update if there is any other FilingPriod (not belong to SMO Campaign Statement filing period) has name
        return await dbContext
            .Set<FilingPeriod>()
            .Include(i => i.Filings)
            .Where(x => x.Name != null &&
                        !x.Filings.Any(y => y.FilingTypeId == FilingType.SlateMailerOrganization.Id &&
                        y.StatusId != FilingStatus.Cancelled.Id &&
                        y.FilerId == filerId))
            .OrderBy(x => x.StartDate)
            .ToListAsync();
    }

    public async Task<long> GetFilingPeriodByStartDateAndEndDate(DateTime startDate, DateTime endDate)
    {
        FilingPeriod period = await dbContext.Set<FilingPeriod>()
            .FirstOrDefaultAsync(fp =>
                fp.StartDate == startDate &&
                fp.EndDate == endDate)
            ?? throw new InvalidOperationException(
                $"No Id found for the start date: {startDate} and end date {endDate}");

        return period.Id;
    }

    /// <inheritdoc/>
    public async Task<List<FilingPeriod>> GetFilingPeriodsByLegislativeSessionIds(List<long> ids)
    {
        return await dbContext.Set<FilingPeriod>()
            .Include(fp => fp.LegislativeSession)
            .Where(fp =>
                fp.LegislativeSessionId.HasValue && ids.Contains(fp.LegislativeSessionId.Value))
            .OrderBy(x => x.StartDate)
            .ThenBy(x => x.EndDate)
            .AsSplitQuery()
            .AsNoTracking()
            .ToListAsync();
    }


}
