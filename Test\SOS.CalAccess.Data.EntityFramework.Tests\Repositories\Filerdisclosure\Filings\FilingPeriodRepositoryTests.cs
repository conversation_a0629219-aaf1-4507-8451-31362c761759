using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerDisclosure.Filings;

[TestFixture]
[TestOf(typeof(FilingPeriodRepository))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class FilingPeriodRepositoryTests
{
    private FilingPeriodRepository _repository;

    [SetUp]
    public async Task SetUp()
    {
        DatabaseContextFactory factory = new();
        DatabaseContext context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new FilingPeriodRepository(context);
    }

    [Test]
    public async Task FindAllUnreportedFilingPeriodsSmoCampaignStatement_Found_ShouldReturnResult()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        // Get seed data
        int filerId = 1;
        List<FilingPeriod> data = GetData();

        // Add seed data into the database
        await context.Set<FilingPeriod>().AddRangeAsync(data);

        await context.SaveChangesAsync();

        FilingPeriodRepository repository = new(context);

        // Act
        List<FilingPeriod> result = await repository.FindAllUnreportedFilingPeriodsSmoCampaignStatementByFiler(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<FilingPeriod>>());
            Assert.That(!result.Any(x => x.Filings.Any(y => y.FilingTypeId == FilingType.SlateMailerOrganization.Id && y.StatusId != FilingStatus.Cancelled.Id)), Is.True);
        });
    }

    [Test]
    public async Task FindAllUnreportedFilingPeriodsSmoCampaignStatement_NotFound_ShouldReturnEmpty()
    {
        // Arrange
        // Create db context
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        int filerId = 1;
        FilingPeriodRepository repository = new(context);

        // Act
        List<FilingPeriod> result = await repository.FindAllUnreportedFilingPeriodsSmoCampaignStatementByFiler(filerId);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetFilingPeriodByStartDateAndEndDate_ReturnsId_WhenMatchExists()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;

        await using DatabaseContext context = new(options);
        FilingPeriodRepository repository = new(context);

        DateTime inputStart = new(2024, 1, 1, 10, 0, 0, DateTimeKind.Local); // e.g., PST
        DateTime inputEnd = new(2024, 6, 30, 23, 59, 59, DateTimeKind.Local); // e.g., PST

        DateTime utcStart = new(inputStart.Year, inputStart.Month, inputStart.Day, inputStart.Hour, inputStart.Minute, inputStart.Second, DateTimeKind.Local);
        DateTime utcEnd = new(inputEnd.Year, inputEnd.Month, inputEnd.Day, inputEnd.Hour, inputEnd.Minute, inputEnd.Second, DateTimeKind.Local);

        FilingPeriod filingPeriod = new()
        {
            Id = 1,
            StartDate = utcStart,
            EndDate = utcEnd
        };

        context.FilingPeriods.Add(filingPeriod);
        await context.SaveChangesAsync();

        // Act
        long result = await repository.GetFilingPeriodByStartDateAndEndDate(inputStart, inputEnd);

        // Assert
        Assert.That(result, Is.EqualTo(1));
    }
    [Test]
    public void GetFilingPeriodByStartDateAndEndDate_Throws_WhenNoMatchExists()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;

        DateTime inputStart = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime inputEnd = new(2023, 12, 31, 23, 59, 59, DateTimeKind.Local);

        // Act & Assert
        Assert.ThrowsAsync<InvalidOperationException>(async () =>
        {
            await using DatabaseContext context = new(options);
            FilingPeriodRepository repository = new(context);
            await repository.GetFilingPeriodByStartDateAndEndDate(inputStart, inputEnd);
        });
    }

    [Test]
    public async Task GetFilingPeriodsByLegislativeSessionIds_ReturnsOrderedPeriods_WhenIdsMatch()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        var sessionId1 = 100L;
        var sessionId2 = 200L;
        var periods = new List<FilingPeriod>
        {
            new()
            {
                Id = 1,
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 2, 1, 0, 0, 0, DateTimeKind.Utc),
                LegislativeSessionId = sessionId1
            },
            new()
            {
                Id = 2,
                StartDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 2, 2, 0, 0, 0, DateTimeKind.Utc),
                LegislativeSessionId = sessionId2
            }
        };
        await context.Set<FilingPeriod>().AddRangeAsync(periods);
        await context.SaveChangesAsync();

        var repository = new FilingPeriodRepository(context);

        // Act
        var result =
            await repository.GetFilingPeriodsByLegislativeSessionIds(new List<long> { sessionId1, sessionId2 });

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Has.Count.EqualTo(2));
            Assert.That(result[0].StartDate, Is.LessThan(result[1].StartDate));
        });
    }

    [Test]
    public async Task GetFilingPeriodsByLegislativeSessionIds_ReturnsEmptyList_WhenNoPeriodsMatch()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        var repository = new FilingPeriodRepository(context);

        // Act
        var result = await repository.GetFilingPeriodsByLegislativeSessionIds(new List<long> { 999L });

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetFilingPeriodsByLegislativeSessionIds_IgnoresPeriodsWithNullLegislativeSessionId()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        var periods = new List<FilingPeriod>
        {
            new()
            {
                Id = 1,
                StartDate = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Utc),
                EndDate = DateTime.SpecifyKind(DateTime.UtcNow.AddDays(1), DateTimeKind.Utc),
                LegislativeSessionId = null
            }
        };
        await context.Set<FilingPeriod>().AddRangeAsync(periods);
        await context.SaveChangesAsync();

        var repository = new FilingPeriodRepository(context);

        // Act
        var result = await repository.GetFilingPeriodsByLegislativeSessionIds(new List<long> { 1 });

        // Assert
        Assert.That(result, Is.Empty);
    }

    #region Private
    private static List<FilingPeriod> GetData()
    {
        return
        [
            new()
            {
                Id = 1,
                Name = "Test",
                StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            },
            new()
            {
                Id = 2,
                StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Name = "Test",
                Filings =
                [
                    new SmoCampaignStatement()
                    {
                        Id = 1,
                        StatusId = FilingStatus.Cancelled.Id,
                        FilerId = 1,
                    }
                ]
            }
        ];
    }
    #endregion
}
