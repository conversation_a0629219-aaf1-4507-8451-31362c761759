@using SOS.CalAccess.Foundation.Utils
@using SOS.CalAccess.Models.Common
@using SOS.CalAccess.UI.Common
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization;
@using SOS.CalAccess.UI.Common.Constants;
@inject IHtmlLocalizer<SharedResources> Localizer
@inject IDateTimeSvc DateTimeSvc

@model SmoCampaignStatementTransactionEntryViewModel
@{
    // FD-CF-Expenditure-3
    var leftButtons = new List<ButtonConfig>
    {
        ButtonBarModel.DefaultPrevious,
        new ()
        {
            CssClass = "btn btn-primary me-2",
            Type = ButtonType.Button,
            Action = FormAction.SaveAndClose,
            InnerTextKey = CommonResourceConstants.Save
        },
    };
    var buttonConfig = new ButtonBarModel
            {
                RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelPaymentEntryButton"),
            },
        },
                LeftButtons = leftButtons,
            };
    var allowedFileExtension = DisclosureConstants.Transaction.AllowedFileExtension;
    var relationshipType = Enum.GetName(typeof(RelationshipType), RelationshipType.DisclosureTransaction);
}
<div class="p-5">
    @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementEnterTransaction)
    <div class="p-5">
        <div class="mb-3">
            @Html.StepHeader(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentTitle)

            @Html.TextBlock(SharedLocalizer, ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentBody)
        </div>

        @using (Html.BeginForm("PaymentMade03", "SmoCampaignStatement", FormMethod.Post))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.Id)
            @Html.HiddenFor(m => m.ContactId)
            @Html.HiddenFor(m => m.TransactionId)
            @Html.HiddenFor(m => m.ScreenType)
            @Html.HiddenFor(m => m.AttachedFileGuidsJson)

            <div class="mb-3">
                <div class="col-sm-6">
                    @Html.DatePickerFor(
                        SharedLocalizer,
                        m => m.TransactionDate,
                        Localizer[ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentDatePaid].Value,
                        minDate: null,
                        maxDate: DateTimeSvc.GetCurrentDateTime(),
                        format: "MM/dd/yyyy",
                        isRequired: true,
                        isReadOnly: false,
                        cssClass: "w-100",
                        placeholderResourceKey: "MM/DD/YYYY"
                    )
                </div>
            </div>

            <div class="col-sm-6 mb-3">
                @Html.CurrencyInputFor(SharedLocalizer, m => m.TransactionAmount, ResourceConstants.Amount, true, true, widthInEmUnits: null)
            </div>

            <div class="col-sm-6 mb-3">
                @Html.DropdownFor(
                         SharedLocalizer,
                         m => m.CodeId,
                         ResourceConstants.PaymentCode,
                         Model.CodeOptions,
                         ResourceConstants.SelectCode,
                         true
                         )
            </div>

            <div class="mb-3 col-sm-6">
                @Html.TextAreaFor(Localizer, m => m.Description!, ResourceConstants.Description, required: true)
            </div>

            <div class="mb-3 col-sm-6">
                @Html.CheckBoxFor(
                         SharedLocalizer,
                         m => m.IsPaidByAgentOrContractor,
                         ResourceConstants.SmoCampaignStatementPaymentMade03Checkbox
                         )
            </div>

            <div class="mb-3 col-sm-6">
                @Html.TextFieldFor(
                         SharedLocalizer, m => m.AgentOrIndependentContractorName,
                         ResourceConstants.SmoCampaignStatementPaymentMade03TextboxNameOf,
                         required: true
                         )
            </div>

            <div class="mb-3 col-sm-6">
                @Html.FileUploader(
                         SharedLocalizer,
                         allowedFileExtension,
                         relationshipId: Model.TransactionId,
                         relationshipType: relationshipType,
                         titleResourceKey: ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentAttachFiles,
                         onUploadSuccess: "handlePaymentMadeUploadedFile",
                         handleMultipleFiles: true
                         )
            </div>

            <div class="mb-3 col-sm-6">
                @Html.TextAreaFor(
                         Localizer,
                         m => m.Notes!,
                         ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentNotes
                         )
            </div>

            <div class="mt-5">
                <partial name="_ButtonBar" model="buttonConfig" />
            </div>
        }
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const checkbox = document.getElementById('@Html.IdFor(m => m.IsPaidByAgentOrContractor)');
        const textField = document.getElementById('@Html.IdFor(m => m.AgentOrIndependentContractorName)');
        const transactionAmountField = document.getElementById('@Html.IdFor(m => m.TransactionAmount)');

        function toggleTextField() {
            textField.disabled = !checkbox.checked;
        }

        function disableListener(e) {
            e.stopImmediatePropagation();
        }

        // Toggle payment made by agent checkbox
        checkbox.addEventListener("change", toggleTextField);
        toggleTextField();

        // Disable mousewheel to changing value
        transactionAmountField.addEventListener("mousewheel", disableListener);
    });

    function handlePaymentMadeUploadedFile(args) {
        try {
            // Validate response value
            if (!args) return;

            if (!args.e) return;

            let paymentMadeOriginalFileName = args.file.name;
            let paymentMadeFileName = '';

            //Get current uploaded files
            const paymentMadeAttachedFileElement = document.getElementById('@Html.IdFor(m => m.AttachedFileGuidsJson)');
            let paymentMadeAttachmentFiles = [];
            if (paymentMadeAttachedFileElement.value) {
                paymentMadeAttachmentFiles = JSON.parse(paymentMadeAttachedFileElement.value);
            }

            // Add or remove uploaded file base on action
            switch (args.operation) {
                case "@CommonConstants.FileAction.Upload":
                    paymentMadeFileName = args.e.target.response;
                    if (!paymentMadeFileName) return;
                    if (!paymentMadeAttachmentFiles.includes(paymentMadeFileName)) {
                        paymentMadeAttachmentFiles.push(paymentMadeFileName);
                    }
                    break;

                case "@CommonConstants.FileAction.Remove":
                    paymentMadeFileName = paymentMadeOriginalFileName;
                    if (!paymentMadeFileName) return;
                    paymentMadeAttachmentFiles = paymentMadeAttachmentFiles.filter(f => f !== paymentMadeFileName);
                    break;

                default:
                    break;
            }

            // Update current uploaded files
            paymentMadeAttachedFileElement.value = JSON.stringify(paymentMadeAttachmentFiles);

        } catch (e) {
            
        }
    }
</script>
