using Newtonsoft.Json;
using SOS.CalAccess.Data.Email;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Email;
using SOS.CalAccess.Services.Common.Email;
using SOS.CalAccess.Services.Common.Email.Model;
using SOS.CalAccess.Services.Common.Queuing;

namespace SOS.CalAccess.Services.Common.EmailMessaging;
public class EmailSvc(IMessageQueueSvc messageQueueSvc, IEmailMessageRepository emailMessageRepository, EmailMessagingOptions emailMessagingOptions, IDateTimeSvc dateTimeSvc) : IEmailSvc
{
    /// <summary>
    /// Send plain email to queue in the Azure queue service
    /// </summary>
    /// <param name="emailMessageRequest"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task SendPlainEmail(EmailMessageRequest emailMessageRequest)
    {
        if (string.IsNullOrEmpty(emailMessageRequest.Message))
        {
            throw new ArgumentException("Message content cannot be blank");
        }

        if (string.IsNullOrEmpty(emailMessageRequest.Subject))
        {
            throw new ArgumentException("Message subject cannot be blank");
        }

        foreach (var recipient in emailMessageRequest.Recipients)
        {
            if (string.IsNullOrEmpty(recipient.EmailAddress))
            {
                throw new ArgumentException("Recipient email address is required");
            }
        }

        foreach (var recipient in emailMessageRequest.Recipients)
        {
            // Build email message object
            EmailMessage emailMessage = new()
            {
                ToEmailAddress = recipient.EmailAddress,
                Body = emailMessageRequest.Message,
                FromEmailAddress = emailMessagingOptions.From,
                DateUpdated = dateTimeSvc.GetCurrentDateTime(),
                Status = EmailMessageStatusEnum.InternallyCreated.ToString(),
                UserId = recipient.UserId,
                AttemptCount = "0",
                Subject = emailMessageRequest.Subject
            };

            if (emailMessageRequest.FilerId != 0)
            {
                emailMessage.FilerId = emailMessageRequest.FilerId;
            }
            if (recipient.NotificationId != 0)
            {
                emailMessage.NotificationMessageId = recipient.NotificationId;
            }

            // Create email entry into EmailMessages table
            emailMessage = await emailMessageRepository.Create(emailMessage);

            // Send email message to Azure Queue
            await messageQueueSvc.SendJsonMessage(QueueName.EmailRequest, emailMessage);

            // Update status to Queued
            if (emailMessage != null)
            {
                // Update status in the EmailMessages Table
                emailMessage.Status = EmailMessageStatusEnum.InternallyQueued.ToString();
                emailMessage = await emailMessageRepository.Update(emailMessage);
            }
        }
    }

    /// <summary>
    /// Send templated email to queue in the Azure queue service
    /// </summary>
    /// <param name="emailTemplateRequest"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task SendTemplatedEmail(EmailTemplateRequest emailTemplateRequest)
    {
        string body = string.Empty;
        string subject = string.Empty;

        if (string.IsNullOrEmpty(emailTemplateRequest.TemplateId))
        {
            throw new ArgumentException("Email template id is required");
        }
        if (emailTemplateRequest.TemplateData == null)
        {
            throw new ArgumentException("Email template data is required");
        }

        foreach (var recipient in emailTemplateRequest.Recipients)
        {
            if (string.IsNullOrEmpty(recipient.EmailAddress))
            {
                throw new ArgumentException("Recipient email address is required");
            }
        }

        foreach (var recipient in emailTemplateRequest.Recipients)
        {
            // Build email message object
            EmailMessage emailMessage = new()
            {
                ToEmailAddress = recipient.EmailAddress,
                FromEmailAddress = emailMessagingOptions.From,
                DateUpdated = dateTimeSvc.GetCurrentDateTime(),
                Status = EmailMessageStatusEnum.InternallyCreated.ToString(),
                UserId = recipient.UserId,
                AttemptCount = "0",
                TemplateId = emailTemplateRequest.TemplateId,
                TemplateData = JsonConvert.SerializeObject(emailTemplateRequest.TemplateData),
                Body = body,
                Subject = subject
            };

            if (emailTemplateRequest.FilerId != 0)
            {
                emailMessage.FilerId = emailTemplateRequest.FilerId;
            }
            if (recipient.NotificationId != 0)
            {
                emailMessage.NotificationMessageId = recipient.NotificationId;
            }

            // Create email entry into EmailMessages table
            emailMessage = await emailMessageRepository.Create(emailMessage);

            // Send email message to Azure Queue
            await messageQueueSvc.SendJsonMessage(QueueName.EmailRequest, emailMessage);

            // Update status to Queued
            if (emailMessage != null)
            {
                emailMessage.Status = EmailMessageStatusEnum.InternallyQueued.ToString();
            }

            // Update status in the EmailMessages Table
            emailMessage = await emailMessageRepository.Update(emailMessage!);
        }
    }

    public async Task UpdateEmailStatus(string providerId, string status, string issueClassification, string errorResponse, string attemptCount, string smtpId, string sendGridEventId, DateTime dateUpdated)
    {
        EmailMessage emailMessage;
        try
        {
            emailMessage = await emailMessageRepository.FindByProviderId(providerId).ConfigureAwait(true);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Could not update the EmailStatus.", ex);
        }
        try
        {
            if (emailMessage != null)
            {
                emailMessage.Status = status;
                emailMessage.SmtpId = smtpId;
                emailMessage.SendGridEventId = sendGridEventId;
                if (status.Equals(SendGridEventsEnum.delivered.ToString(), StringComparison.OrdinalIgnoreCase))
                {
                    emailMessage.DateSent = dateUpdated;
                }
                if (status.Equals(SendGridEventsEnum.bounce.ToString(), StringComparison.OrdinalIgnoreCase))
                {
                    emailMessage.IssueClassification = issueClassification;
                    emailMessage.ErrorResponse = errorResponse;
                }
                if (status.Equals(SendGridEventsEnum.dropped.ToString(), StringComparison.OrdinalIgnoreCase))
                {
                    emailMessage.ErrorResponse = errorResponse;
                }
                if (status.Equals(SendGridEventsEnum.deferred.ToString(), StringComparison.OrdinalIgnoreCase))
                {
                    emailMessage.AttemptCount = attemptCount;
                }
                if (emailMessage.DateUpdated < dateUpdated)
                {
                    emailMessage.DateUpdated = dateUpdated;
                    await emailMessageRepository.Update(emailMessage);
                }
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Could not update the EmailStatus.", ex);
        }
    }

    public async Task UpdateEmailStatus(UpdateEmailRequest updateEmailRequest)
    {
        EmailMessage emailMessage;
        try
        {
            emailMessage = await emailMessageRepository.FindById(updateEmailRequest.Id);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Could not update the EmailStatus.", ex);
        }

        if (emailMessage == null)
        {
            throw new KeyNotFoundException($"No email message found for ID: {updateEmailRequest.Id}");
        }

        try
        {
            if (!string.IsNullOrEmpty(updateEmailRequest.Status) && !string.IsNullOrEmpty(updateEmailRequest.SendGridMessageId))
            {
                emailMessage.Status = updateEmailRequest.Status;
                emailMessage.SendGridMessageId = updateEmailRequest.SendGridMessageId;
            }

            emailMessage.ErrorResponse = updateEmailRequest.ErrorResponse;
            await emailMessageRepository.Update(emailMessage);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Could not update the EmailStatus.", ex);
        }
    }
}
