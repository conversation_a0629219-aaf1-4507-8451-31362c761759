// <copyright file="ISubmitFiling.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Messaging;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;

/// <summary>
/// Command handler interface for setting the status of a filing to submitted.
/// </summary>
public interface ISubmitFiling : ICommand<long, IResult<Filing>>;

/// <summary>
/// Command handler implementation for <see cref="ISubmitFiling"/>.
/// </summary>
/// <param name="db">Database context.</param>
/// <param name="decisions">Business logic engine.</param>
/// <param name="auditService">High level audit facilities.</param>
/// <param name="getAllTransactions">Auxiliary query handler for getting at related transactions.</param>
/// <param name="autoApprovalCheck">Auxiliary query handler for checking auto-approval criteria.</param>
/// <param name="approveFiling">Auxiliary command handler for approving the filing.</param>
/// <param name="messaging">External communications handling.</param>
#pragma warning disable S107 // Methods should not have too many parameters
public sealed class SubmitFiling(
    DatabaseContext db,
    IDecisionsService decisions,
    IAuditService auditService,
    IGetAllTransactions getAllTransactions,
    IAutoApprovalCheck autoApprovalCheck,
    IApproveFiling approveFiling,
    IMessagingHub messaging,
    IDateTimeSvc dateTimeSvc) : ISubmitFiling
#pragma warning restore S107 // Methods should not have too many parameters
{
    private const string NotificationContent = "Filing with Id {0} has been submitted for review";

    /// <inheritdoc/>
    public async ValueTask<IResult<Filing>> Execute(
        long input, CancellationToken cancellationToken = default)
    {
        var filing = await db.Filings
            .FirstOrDefaultAsync(f => f.Id == input, cancellationToken);

        if (filing is null)
        {
            return new Failure<Filing>.NotFound("No filing was found with the requested id");
        }

        if (filing.StatusId != FilingStatus.Draft.Id)
        {
            return new Failure<Filing>.InvalidState(
                FilingStatus.Draft.Id, filing.StatusId, "Filing is not in the correct state");
        }

        _ = await decisions.Execute(new("Filing.Submit", Context: filing), cancellationToken);

        // link transactions to the filing
        var includedTransactions = await getAllTransactions.Execute(filing, cancellationToken);

        var mappings = includedTransactions
            .Select(t => new FilingTransaction { FilingId = filing.Id, TransactionId = t.Id });

        db.FilingsTransactions.AddRange(mappings);

        filing.StatusId = FilingStatus.Pending.Id;
        filing.SubmittedDate = dateTimeSvc.GetCurrentDateTime();

        await db.SaveChangesAsync(cancellationToken);

        var action = BusinessAction.ChangeStatus<Filing>(filing.Id.ToString(), dateTimeSvc, "Submission");
        await auditService.LogAction(action, cancellationToken);

        var content = string.Format(NotificationContent, filing.Id);
        await messaging.Execute(content, cancellationToken);

        if (!await autoApprovalCheck.Execute(filing, cancellationToken))
        {
            return new Success<Filing>(filing);
        }

        return await approveFiling.Execute(filing, cancellationToken);
    }
}
