using NSubstitute;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.WebApi.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filings;

[TestFixture]
public class UpsertFilingRequestTests
{
    private IDateTimeSvc _dateTimeSvc;
    [Test]
    public void IntoCreateCommand_WhenDatesAreNull_UsesCurrentDateTime()
    {
        // Arrange
        var request = new UpsertFilingRequest
        {
            StartDate = null,
            EndDate = null
        };
        var filerId = 123;
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _dateTimeSvc.GetCurrentDateTime().Returns(new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local));
        // Act
        var command = request.IntoCreateCommand(filerId, _dateTimeSvc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(command.FilerId, Is.EqualTo(filerId));
            Assert.That(command.StartDate, Is.Not.EqualTo(default(DateTime)));
            Assert.That(command.EndDate, Is.Not.EqualTo(default(DateTime)));
        });
        Assert.That(command.StartDate, Is.EqualTo(command.EndDate).Within(TimeSpan.FromSeconds(1)));
    }

    [Test]
    public void IntoCreateCommand_WithDates_SetsCorrectly()
    {
        // Arrange
        var startDate = new DateTime(2025, 4, 1, 0, 0, 0, 0);
        var endDate = new DateTime(2025, 5, 1, 0, 0, 0, 0);
        var request = new UpsertFilingRequest
        {
            StartDate = startDate,
            EndDate = endDate
        };
        var filerId = 456;
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        // Act
        var command = request.IntoCreateCommand(filerId, _dateTimeSvc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(command.FilerId, Is.EqualTo(filerId));
            Assert.That(command.StartDate, Is.EqualTo(startDate));
            Assert.That(command.EndDate, Is.EqualTo(endDate));
        });
    }

    [Test]
    public void IntoUpdateCommand_SetsOnlyProvidedValues()
    {
        // Arrange
        var startDate = new DateTime(2025, 6, 10, 0, 0, 0, 0);
        var endDate = new DateTime(2025, 7, 10, 0, 0, 0, 0);
        var request = new UpsertFilingRequest
        {
            StartDate = startDate,
            EndDate = endDate,
            AmendmentExplanation = null
        };

        var filingId = 789;

        // Act
        var command = request.IntoUpdateCommand(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(command.Id, Is.EqualTo(filingId));
            Assert.That(command.StartDate, Is.EqualTo(startDate));
            Assert.That(command.EndDate, Is.EqualTo(endDate));
            Assert.That(command.AmendmentExplanation, Is.Null);
        });
    }

    [Test]
    public void IntoUpdateCommand_SetsAmendmentExplanation_WhenProvided()
    {
        // Arrange
        var explanation = "Test amendment reason";
        var request = new UpsertFilingRequest
        {
            AmendmentExplanation = explanation
        };

        var filingId = 101;

        // Act
        var command = request.IntoUpdateCommand(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(command.Id, Is.EqualTo(filingId));
            Assert.That(command.AmendmentExplanation, Is.EqualTo(explanation));
        });
    }
}
