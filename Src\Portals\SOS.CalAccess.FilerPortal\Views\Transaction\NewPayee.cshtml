@using System.Text.Json
@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.UI.Common.Localization
@inject IHtmlLocalizer<SharedResources> Localizer
@using Microsoft.AspNetCore.Html

@{
    var modelType = Model.GetType();
    const string contactName = "";
    var viewRef = "";
    var context = "";
    var subtitle = "";
    var localizerRef = (string label) => Localizer["FilerPortal.Transaction." + viewRef + label].Value;

    if (modelType == typeof(OtherPaymentViewModel))
    {
        viewRef = "NewOtherPaymentPayee.";
        context = "OtherPayment";
        subtitle = Localizer["FilerPortal.Transaction.NewPayee.OtherPayments"].Value;
    }
    else if (modelType == typeof(ActivityExpenseViewModel))
    {
        viewRef = "NewActivityExpensePayee.";
        context = "ActivityExpense";
        subtitle = Localizer["FilerPortal.Transaction.NewActivityExpense.Header"].Value;
    }

    var returnUrl = Model.ReturnUrl ?? Url.Action("Index", "Filer", new { Action = "" });
    ViewData["Title"] = Localizer["FilerPortal.Transaction.NewOtherPaymentPayee.Title"].Value;

    var buttonBarModel = new ButtonBarModel
    {
        LeftButtons =
        [
            new ButtonConfig
            {
                Type = ButtonType.Link,
                CssClass = "btn btn-primary btn-sm ms-2 text-white",
                InnerTextKey = Localizer["Common.Previous"].Value,
                Url = returnUrl
            },
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                HtmlContent = new HtmlString(
                    "<a href='" + Url.Action("Edit", "Contact", new {
                       filerId = Model.FilerId,
                       filingId = Model.FilingId,
                       returnUrl = returnUrl,
                       reportType = ViewBag.ReportType,
                       context = context
                   }) + "' class='btn btn-warning btn-sm text-white' id='continueButton'>" +
                   Localizer["Common.Continue"].Value +
                   "</a>")
            }
        ],
        RightButtons =
        [
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                CssClass = "btn btn-flat-primary btn-sm ms-auto",
                HtmlContent = new HtmlString(
                   "<a asp-controller='Contact' asp-action='Index' asp-route-viewName='' class='btn btn-flat-primary btn-sm ms-auto' " +
                   "data-bs-toggle='modal' " +
                   "data-bs-target='#cancelConfirmModal'>" +
                   @SharedLocalizer[CommonResourceConstants.Cancel].Value +
                   "</a>"
                )
            },
        ]
    };
}

<h1>@localizerRef("Title")</h1>
<h2 class="mb-5">@subtitle</h2>
<div class="p-5 mt-4 d-flex align-items-center justify-content-center border border-gray">
    <div class="d-flex flex-column">
        <h2>@localizerRef("AboutThePayee")</h2>
        <p>
            @localizerRef("Description")
        </p>

        <div class="d-flex flex-row">
            <div class="form-group">
                @Html.InputLabel(SharedLocalizer, "ContactSearch", @localizerRef("ChooseExistingContact"), true)

                <input type="hidden" id="FilerId" name="FilerId" value="@Model.FilerId" />
                <input type="hidden" id="ContactId" name="ContactId" value="@Model.ContactId" />
                <input type="hidden" id="RegistrationFilingId" name="RegistrationFilingId" value="@Model.Id" />

                <div id="contactSearch" class="candidate-search">
                    <div class="search-container">
                        @Html.InputField(SharedLocalizer, "ContactSearch", "Contact Search", true, contactName, "Enter name or ID#")
                    </div>
                </div>
            </div>


            <div class="ps-5 ms-5 border-start border-gray d-flex flex-column">
                <p class="form-label">@localizerRef("AddNewContact")</p>
                <a asp-action="Create"
                   asp-controller="Contact"
                   asp-route-filerId="@Model.FilerId"
                   asp-route-filingId="@Model.FilingId"
                   asp-route-returnUrl="@returnUrl"
                   asp-route-reportType="@ViewBag.ReportType"
                   asp-route-context="@context"
                   class="btn btn-outline-primary btn">
                    <i class="bi bi-plus-circle-fill"></i>
                    @localizerRef("EnterPayee")
                </a>
            </div>
        </div>

        <div class="my-4"></div>

        <partial name="_ButtonBar" model="buttonBarModel" />

        <span id="contactError" class="text-danger" style="display:none; margin-top: 0.5rem;">Adding contact information is required</span>
    </div>
</div>

@{
    var cancelModal = new CancelConfirmModal(
        Title: "Confirm Cancellation",
        Body: "Are you sure you want to cancel? Any unsaved changes will be lost.",
        CloseButtonText: "No, Stay Here",
        SubmitButtonText: "Yes, Cancel",
        ActionUrl: returnUrl!
    );
}
<partial name="_CancelConfirmModal" model="cancelModal" />

<script>
    document.addEventListener("DOMContentLoaded", function () {

        // Update the Continue button link to include ContactId based on the contact selected
        $('#ContactId').change(function () {
            var contactId = $(this).val();
            var currentHref = $('#continueButton').attr('href');
            var newHref;

            if (contactId) {
                var newParam = '&contactId=' + contactId;
                newHref = currentHref.indexOf('contactId=') !== -1 ? currentHref.replace(/(&?contactId=[^&]*)/, newParam) : currentHref + newParam;
            } else {
                newHref = currentHref.replace(/(&?contactId=[^&]*)/, '');
            }

            $('#continueButton').attr('href', newHref);
        });
        const continueButton = document.getElementById('continueButton');
        const contactIdInput = document.getElementById('ContactId');
        const contactError = document.getElementById('contactError');

        if (continueButton) {
            continueButton.addEventListener('click', function (e) {
                if (!contactIdInput.value) {
                    e.preventDefault();
                    contactError.style.display = 'inline';
                } else {
                    contactError.style.display = 'none';
                }
            });
        }
    });
</script>

@* Handle Search Box *@
<script type="application/javascript">
    const SEARCH_URL = '@Url.Action("SearchFilerContactsByIdOrName", "CoalitionReceivedTransaction")';

    const INPUT_DEBOUNCE_MS = 500;
    const TEXT_NAME = 'Name';
    const TEXT_ADDRESS = 'Address'
    const TEXT_SELECT = 'Select';
    const TEXT_NO_RESULTS_FOUND = 'No results found.';
    const CONTACT_TYPES = ['Individual', 'Organization'];

    // Use class to isolate variables from global scope
    class Search extends EventTarget {
      activeResultItemElems = [];

      constructor(querySelector) {
        super();
        this.root = document.querySelector(querySelector);
        if (!this.root) {
          throw new Error('Unable to initialize search component.');
        }

        this.resultContainer = document.createElement('div');
        this.resultContainer.classList.add('result-container');
        this.root.appendChild(this.resultContainer);

        const resultAbsolute = document.createElement('div');
        resultAbsolute.classList.add('result-absolute');
        this.resultContainer.replaceChildren(resultAbsolute);

        this.results = document.createElement('div');
        this.results.classList.add('result-left');

        this.resultDetail = document.createElement('div');
        this.resultDetail.classList.add('result-right');

        resultAbsolute.replaceChildren(this.results, this.resultDetail);

        this.searchContainer = this.root.querySelector('.search-container');
        this.clearIcon = document.createElement('i');
        this.clearIcon.classList.add('clear-icon', 'e-icons', 'e-close');
        this.searchContainer.appendChild(this.clearIcon);

        this.selection = this.root.querySelector('.candidate-search input');

        this.clearIcon.onclick = () => {
          this.clearValue();
        }

        this.selection.onfocus = () => {
          if (this.selection.value) {
            this.resultContainer.classList.add('active');
          }
        };

        document.addEventListener('mousedown', (e) => {
          const outsideSearch = !e.composedPath().includes(this.selection)
          const outsideResult = !e.composedPath().includes(this.resultContainer)
          if (outsideSearch && outsideResult) {
            this.hideDropdown();
          }
        });

        const debounce = (func, delay = INPUT_DEBOUNCE_MS) => {
          let timeoutId;
          return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func(...args), delay);
          };
        };

        this.selection.oninput = debounce((event) => {
          const filerId = document.getElementById('FilerId').value;
          const searchValue = event.target.value;
          if (searchValue) {
              this.clearIcon.classList.add('active');
          }
          else {
              this.clearIcon.classList.remove('active');
          }

          const params = {
              search: searchValue,
              filerId: filerId
          }
          this.ajaxGet(SEARCH_URL, params, (err, data) => {
            this.clearResults();
            this.setResults(this.results, data);
            this.showDropdown();
          })
        });

        this.clearResults();
      }

      on(eventName, callback, options) {
          this.addEventListener(eventName, callback, options);
      }

      ajaxGet(url, params, callback) {
        const xhr = new XMLHttpRequest();
        const urlParams = new URLSearchParams(params);
        const fullUrl = `${url}?${urlParams.toString()}`;

        xhr.open('GET', fullUrl.toString());
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            callback(null, JSON.parse(xhr.responseText));
          }
          else {
            console.error('Request failed', xhr.status, xhr.responseText);
            callback(xhr.responseText);
          }
        }
        xhr.onerror = (e) => {
          callback(e);
        }
        xhr.send();
      }

      ajaxGetMock(url, params, callback) {
        callback(null, MOCK_DATA);
      }

      showDropdown() {
        this.resultContainer.classList.add('active');
      }
      hideDropdown() {
        this.resultContainer.classList.remove('active');
      }

      setValue(value) {
        this.selection.value = value;
      }
      clearValue() {
        this.selection.value = '';
        this.clearIcon.classList.remove('active');
        this.selection.focus();
      }

      clearResults = () => {
        this.results.replaceChildren();
        this.clearResultDetail();
      };
      clearActiveResultItemElems = () => {
        this.activeResultItemElems = [];
      }
      clearSelectedResult = () => {
        this.activeResultItemElems.forEach(resultItem => {
          resultItem.classList.remove('selected');
        })
      };
      resultOnClick = (element, record) => {
        return () => {
          // Remove 'selected' from all other results.
          this.clearSelectedResult();
          element.classList.add('selected');
          this.setResultDetail(record);
        }
      };
      clearResultDetail = () => {
        this.resultDetail.replaceChildren();
      }
      setResultDetail = (record) => {
        // content node
        const detailContent = document.createElement('div');
        detailContent.classList.add('result-detail-content');

        const contentGroupName = document.createElement('div');
        contentGroupName.classList.add('result-detail-content-group');
        const nameLabel = document.createElement('strong');
        nameLabel.innerText = TEXT_NAME;
        const nameContent = document.createElement('span');
        nameContent.innerText = record.Name;
        contentGroupName.replaceChildren(nameLabel, nameContent);

        const contentGroupAddress = document.createElement('div');
        contentGroupAddress.classList.add('result-detail-content-group');
        const addressLabel = document.createElement('strong');
        addressLabel.innerText = TEXT_ADDRESS;
        const addressLine1Content = document.createElement('span');
        addressLine1Content.innerText = record.AddressLine1;
        const addressLine2Content = document.createElement('span');
        addressLine2Content.innerText = record.AddressLine2;
        contentGroupAddress.replaceChildren(addressLabel, addressLine1Content, addressLine2Content)

        detailContent.replaceChildren(contentGroupName, contentGroupAddress);

        // action node
        const detailActions = document.createElement('div');
        detailActions.classList.add('result-detail-actions');
        const selectCandidateButton = document.createElement('button');
        selectCandidateButton.classList.add('button');
        selectCandidateButton.setAttribute('type', 'button')
        selectCandidateButton.innerText = TEXT_SELECT;
        selectCandidateButton.onclick = () => {
          this.dispatchEvent(new CustomEvent('selected', { detail: record }));
          this.setValue(record.Name);
          this.hideDropdown();
        }
        detailActions.replaceChildren(selectCandidateButton);

        this.resultDetail.replaceChildren(detailContent, detailActions);
      }
      createResultItem = (record) => {
        const resultItem = document.createElement('div');
        resultItem.classList.add('result-item');
        resultItem.onclick = this.resultOnClick(resultItem, record);

        const resultDesc = document.createElement('div');
        resultDesc.classList.add('result-description');

        const resultName = document.createElement('div');
        resultName.classList.add('result-name');
        resultName.innerText = record.Name;

        const resultCaption = document.createElement('div');
        resultCaption.classList.add('result-caption');
        resultCaption.innerText = record.FilerId ? `ID# ${record.FilerId}` : '';

        resultDesc.replaceChildren(resultName, resultCaption);

        const resultAction = document.createElement('div');
        resultAction.classList.add('result-action');

        const arrow = document.createElement('span');
        arrow.classList.add('e-icons');
        arrow.classList.add('e-chevron-right');
        resultAction.replaceChildren(arrow);

        resultItem.replaceChildren(resultDesc, resultAction);
        return resultItem;
      };
      noResultsElem = () => {
        const resultItem = document.createElement('div');
        resultItem.classList.add('result-item');
        resultItem.classList.add('disabled');

        const resultDesc = document.createElement('div');
        resultDesc.classList.add('result-description');

        const resultName = document.createElement('div');
        resultName.classList.add('italicized');
        resultName.innerText = TEXT_NO_RESULTS_FOUND;
        resultDesc.replaceChildren(resultName);

        resultItem.replaceChildren(resultDesc);

        return resultItem;
      }
      setResults = (resultsElem, resultData) => {
        this.clearActiveResultItemElems();
        const resultElements = [];
        resultData.forEach((result) => {
          const resultItem = this.createResultItem(result);
          resultElements.push(resultItem);
          this.activeResultItemElems.push(resultItem);
        });

        if (resultData.length === 0) {
          resultElements.push(this.noResultsElem());
        }

        resultsElem.replaceChildren(...resultElements);
      };
    }

    const search = new Search('#contactSearch');
    search.on('selected', (e) => {
      if (e.detail) {
        const contactId = document.getElementById('ContactId');
        contactId.value = 1;
        const registrationFilingId = document.getElementById('RegistrationFilingId');
        registrationFilingId.value = e.detail.RegistrationFilingId;

        // Update the Continue button href to include ContactId and RegistrationFilingId based on the contact selected
        const continueButton = document.getElementById('continueButton');

        const continueButtonUrl = new URL(continueButton.href);
        const continueButtonParams = new URLSearchParams(continueButtonUrl.search);

        if (contactId.value) {
          continueButtonParams.set('contactId', e.detail.ContactId);
        }
        if (registrationFilingId.value) {
          continueButtonParams.set('registrationFilingId', e.detail.RegistrationFilingId);
        }

        continueButtonUrl.search = continueButtonParams.toString();
        continueButton.href = continueButtonUrl.toString();
      }
    })
</script>
