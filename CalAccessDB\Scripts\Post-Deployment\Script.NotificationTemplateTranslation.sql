SET IDENTITY_INSERT [dbo].[NotificationTemplateTranslation] ON;
GO

MERGE INTO NotificationTemplateTranslation
USING (VALUES 
/*NF002*/(3, 3, 'en-US', '{{DocumentId}} Registration submitted successfully', '{{DocumentId}} Registration submitted successfully.', 'SMS Message', null, NEWID()),
/*NF001*/(4, 4, 'en-US', '{{DocumentId}} Registration ready for attestation', '{{DocumentId}} Registration ready for attestation.', 'SMS Message', null, NEWID()),
/*NF017*/(5, 5, 'en-US', '{{DocumentId}} Activity report submitted successfully', '{{DocumentId}} Activity report submitted successfully.', 'SMS Message', null, NEWID()),
/*NF016*/(6, 6, 'en-US', 'Disclosure Form Submitted', 'Activity report submitted successfully.', 'SMS Message', null, NEWID()),
/*NF015*/(7, 7, 'en-US', 'Disclosure Form Complete Attestation', 'Activity report ready for attestation.', 'SMS Message', null, NEWID()),
/*NF018*/(8, 8, 'en-US', '{{DocumentId}} A campaign committee must be established', '{{DocumentId}} A campaign committee must be established.', 'SMS Message', null, NEWID()),
/*NF---*/(9, 9, 'en-US', 'Amendment to registration ready for attestation', 'Amendment to registration ready for attestation.', 'SMS Message', null, NEWID()),
/*NF008*/(10, 10, 'en-US', '{{DocumentId}} Amendment to registration submitted successfully', '{{DocumentId}} Amendment to registration submitted successfully.', 'SMS Message', null, NEWID()),
/*NF006*/(16, 16, 'en-US', '{{DocumentId}} Treasurer acknowledgment ready for completion', '{{DocumentId}} Treasurer acknowledgment ready for completion.', 'SMS Message', null, NEWID()),
/*NF---*/(17, 17, 'en-US', 'Registration submitted successfully', 'Registration submitted successfully.', 'SMS Message', null, NEWID()),
/*NF---*/(18, 18, 'en-US', 'Registration ready for attestation', 'Registration ready for attestation.', 'SMS Message', null, NEWID()),
/*NF003*/(19, 19, 'en-US', '{{DocumentId}} Qualification date missing', '{{DocumentId}} Qualification date missing.', 'SMS Message', null, NEWID()),
/*NF---*/(20, 20, 'en-US', 'Attestation request', 'Your report for {{EntityName}} has been sent on {{SendDate}} and ready for attestation.', 'SMS Message', null, NEWID()),
/*NF---*/(21, 21, 'en-US', 'Lobbyist amendment report Accepted', 'Your report for {{EntityName}} has been sent on {{SendDate}} and ready for approval.', 'SMS Message', null, NEWID()),
/*NF---*/(22, 22, 'en-US', '72h report {{Status}}', 'Your 72h report for {{EntityName}} has been sent on {{SendDate}} and ready for approval.', 'SMS Message', null, NEWID()),
/*NF---*/(23, 23, 'en-US', 'Attestation request', 'Your 72h report for {{EntityName}} has been sent on {{SendDate}} and ready for attestation.', 'SMS Message', null, NEWID()),
/*NF---*/(24, 24, 'en-US', 'Registration Terminated', '{{FilerName}} registration terminated successfully. Please take proper actions.', 'SMS Message', null, NEWID()),
/*NF---*/(25, 25, 'en-US', 'Termination Held For PRD Review', 'Your submission to terminate the SMO Registration for {{EntityName}} and {{ID}} is currently being held for PRD review.', 'SMS Message', null, NEWID()),
/*NF---*/(26, 26, 'en-US', 'Lobbyist registration {{Status}}', 'Your report for {{EntityName}} has been sent on {{SendDate}} and ready for approval.', 'SMS Message', null, NEWID()),
/*NF---*/(27, 27, 'en-US', 'Registration ready for attestation', 'Registration ready for attestation.', 'SMS Message', null, NEWID()),
/*NF---*/(28, 28, 'en-US', '48h report {{Status}}', 'Your 48h report for {{EntityName}} has been sent on {{SendDate}} and ready for approval.', 'SMS Message', null, NEWID()),
/*NF---*/(29, 29, 'en-US', 'Attestation request', 'Your 48h report for {{EntityName}} has been sent on {{SendDate}} and ready for attestation.', 'SMS Message', null, NEWID()),
/*NF---*/(30, 30, 'en-US', 'Lobbyist Employer Report {{Status}}', 'Your Lobbyist Employer Report for {{EntityName}} has been sent on {{SendDate}} and ready for approval.', 'SMS Message', null, NEWID()),
/*NF016*/(31, 31, 'en-US', '{{DocumentId}} Activity report submitted successfully', '{{DocumentId}} Activity report submitted successfully.', 'SMS Message', null, NEWID()),
/*NF015*/(32, 32, 'en-US', '{{DocumentId}} Activity report ready for attestation', '{{DocumentId}} Activity report ready for attestations.', 'SMS Message', null, NEWID()),
/*NF---*/(33, 33, 'en-US', 'SMO Campaign Statement Incomplete', 'Your SMO Campaign Statement for {{EntityName}} and {{ID}} is incomplete.', 'SMS Message', null, NEWID()),
/*NF---*/(34, 34, 'en-US', '72h amendment report {{Status}}', 'Your 72h amendement report for {{EntityName}} has been sent on {{SendDate}} and ready for approval.', 'SMS Message', null, NEWID()),
/*NF---*/(35, 35, 'en-US', 'Attestation request', 'Your 72h amendment report for {{EntityName}} has been sent on {{SendDate}} and ready for attestation.', 'SMS Message', null, NEWID()),
/*NF019*/(36, 36, 'en-US', '{{DocumentId}} Activity report ready for attestation', '{{DocumentId}} Activity report ready for attestation.', 'SMS Message', null, NEWID()),
/*NF020*/(37, 37, 'en-US', '{{DocumentId}} Amendment report submitted successfully', '{{DocumentId}} Amendment report submitted successfully.', 'SMS Message', null, NEWID()),
/*NF031*/(38, 38, 'en-US', '{{DocumentId}} Notice of termination ready for attestation', '{{DocumentId}} Notice of termination ready for attestation.', 'SMS Message', null, NEWID()),
/*NF032*/(39, 39, 'en-US', '{{DocumentId}} Notice of termination submitted successfully', '{{DocumentId}} Notice of termination submitted successfully.', 'SMS Message', null, NEWID()),
/*NF---*/(40, 40, 'en-US', 'Lobbyist registration amendment report {{Status}}', 'Your Lobbyist registration amendment for {{EntityName}} has been sent on {{SendDate}} and ready for approval.', 'SMS Message', null, NEWID()),
/*NF---*/(41, 41, 'en-US', 'Attestation request', 'Your Lobbyist registration amendment report for {{EntityName}} has been sent on {{SendDate}} and ready for attestation.', 'SMS Message', null, NEWID()),
/*NF---*/(42, 42, 'en-US', 'Attestation request', 'Your Lobbyist Employer Report for {{EntityName}} has been sent on {{SendDate}} and ready for attestation.', 'SMS Message', null, NEWID()),
/*NF---*/(43, 43, 'en-US', 'Lobbyist Registration Terminated', '{{DocumentId}} Notice of termination submitted successfully.', 'SMS Message', null, NEWID()),
/*NF033*/(44, 44, 'en-US', 'Lobbyist Registration Terminated', '{{DocumentId}} {{FilerName}} notice of termination submitted successfully. Please take proper actions.', 'SMS Message', null, NEWID()),
/*NF---*/(45, 45, 'en-US', 'Attestation request for Lobbyist Registration Termination', '{{DocumentId}} Notice of termination ready for attestation.', 'SMS Message', null, NEWID()),
/*NF---*/(50, 50, 'en-US', 'Action Required: Account Linkage Request for {{FilerName}}', N'<html lang="en"><body><p>Hello {{FilerName}},<br>We received a request to link a user account to your filer in the California Secretary of State’s CAL-ACCESS system.</p><p><strong>Requested by</strong>: {{RequesterName}}<br><strong>Email</strong>: {{RequesterEmail}}<br><strong>Assigned Role</strong>: {{AssignedRole}}<br><strong>Date of Request</strong>: {{RequestDate}}<br></p><p>To approve this request:</p><ol><li>Log in to your CAL-ACCESS account <a href="{{CreateAnAccountUrl}}">{{CreateAnAccountUrl}}</a></li><li>Select this Filer to work on and go to the Authorizer User screen.</li><li>Accept or Reject the linkage request.</li></ol><p><i>If you do not recognize this request or believe it was made in error, simply disregard this message.</i></p><p>Thank you,<br>California Secretary of State</p><p>Please do not reply to this email. This mailbox is not monitored and you will not receive a response.<br>Copyright © 2024 {{OrganizationName}}. All rights reserved.</p></body></html>', 'SMS Message', null, NEWID()),
/*NF---*/(51, 51, 'en-US', 'Action Required: Account Linkage Request for {{OfficerName}}', N'<html lang="en"><body><p> Hello {{OfficerName}},<br> You have received a request to be associated with a filer in the California Secretary of State’s CAL-ACCESS system.</p><p> <strong>Requested by</strong>: {{RequesterName}}<br> <strong>Email</strong>: {{RequesterEmail}}<br> <strong>Assigned Role</strong>: {{AssignedRole}}<br> <strong>Date of Request</strong>: {{RequestDate}}<br> <strong>Filer</strong>: {{FilerName}}<br></p><p>To approve this request:</p><ol> <li>Log in to your CAL-ACCESS account or Create an Account <a href="{{CreateAnAccountUrl}}">{{CreateAnAccountUrl}}</a></li> <li>Once logged in, go to the User Account Management screen and Linkages section.</li> <li>Enter the invitation code below to accept the linkage:</li></ol><p style="font-size: 28px">{{InvitationCode}}</p><p><i>If you do not recognize this request or believe it was made in error, simply disregard this message.</i></p><p> Thank you,<br> California Secretary of State</p><p> Please do not reply to this email. This mailbox is not monitored and you will not receive a response.<br> Copyright © 2024 {{OrganizationName}}. All rights reserved.</p></body></html>', 'SMS Message', null, NEWID()),
/*NF---*/(52, 52, 'en-US', 'Action Required: Account Linkage Request to a CARS Filer', N'<html lang="en"><body><p> Hello,<br> You have received a request to be associated with a filer in the California Secretary of State’s CAL-ACCESS system.</p><p> <strong>Requested by</strong>: {{RequesterName}}<br> <strong>Email</strong>: {{RequesterEmail}}<br> <strong>Assigned Role</strong>: {{AssignedRole}}<br> <strong>Date of Request</strong>: {{RequestDate}}<br> <strong>Filer</strong>: {{FilerName}}<br></p><p>To approve this request:</p><ol> <li>Log in to your CAL-ACCESS account or Create an Account <a href="{{CreateAnAccountUrl}}">{{CreateAnAccountUrl}}</a></li> <li>Once logged in, go to the User Account Management screen and Linkages section.</li> <li>Enter the invitation code below to accept the linkage:</li></ol><p style="font-size: 28px">{{InvitationCode}}</p><p><i>If you do not recognize this request or believe it was made in error, simply disregard this message.</i></p><p> Thank you,<br> California Secretary of State</p><p> Please do not reply to this email. This mailbox is not monitored and you will not receive a response.<br> Copyright © 2024 {{OrganizationName}}. All rights reserved.</p></body></html>', 'SMS Message', null, NEWID()),
/*NF040*/(53, 53, 'en-US', 'Your linkage request with {{FilerName}} accepted', 'Your linkage request with {{FilerName}} accepted.', 'SMS Message', null, NEWID()),
/*NF041*/(54, 54, 'en-US', 'Linkage request with {{FilerName}} accepted', 'Linkage request with {{FilerName}} accepted.', 'SMS Message', null, NEWID()),
/*NF042*/(55, 55, 'en-US', 'Your linkage request with {{FilerName}} rejected', 'Your linkage request with {{FilerName}} rejected.', 'SMS Message', null, NEWID()),
/*NF043*/(56, 56, 'en-US', 'Linkage request with {{FilerName}} rejected', 'Linkage request with {{FilerName}} rejected.', 'SMS Message', null, NEWID()),
/*NF044*/(57, 57, 'en-US', 'Your linkage with {{FilerName}} terminated successfully', 'Your linkage with {{FilerName}} terminated successfully.', 'SMS Message', null, NEWID()),
/*NF045*/(58, 58, 'en-US', 'Linkage with {{UserName}} terminated successfully', 'Linkage with {{UserName}} terminated successfully.', 'SMS Message', null, NEWID()),
/*NF009*/(59, 59, 'en-US', 'Your linkage with {{FilerName}} terminated successfully', 'Your linkage with {{FilerName}} terminated successfully', 'SMS Message', null, NEWID()),
/*NF010*/(60, 60, 'en-US', 'Linkage with {{UserName}} terminated successfully', 'Linkage with {{UserName}} terminated successfully', 'SMS Message', null, NEWID()),
/*NF047*/(61, 61, 'en-US', '{{DocumentId}} Registration changed to Draft', '{{DocumentId}} Registration changed to Draft', 'SMS Message', null, NEWID()),
/*NF035*/(81, 81, 'en-US', 'Registration Withdrawal', '{{DocumentId}} Notice of withdrawal submitted successfully', 'SMS Message', null, NEWID()),
/*NF049*/(82, 82, 'en-US', 'Registration Withdrawal', 'Notice of withdrawal submitted ready for Attestation','SMS Message', null, NEWID()),
/*NF036*/(83, 83, 'en-US', 'Registration Withdrawal', '{{DocumentId}} {{Filer name}} notice of withdrawal submitted successfully. Please take proper actions.','SMS Message', null, NEWID()),
/*NF037*/(84, 84, 'en-US', 'Registration Renewal', '{{DocumentId}} Registration renewed successfully.','SMS Message', null, NEWID()),
/*NF038*/(85, 85, 'en-US', 'Registration Renewal', '{{DocumentId}} {{FilerName}} registration renewed successfully. Please take proper actions.','SMS Message', null, NEWID()),
/*NF039*/(86, 86, 'en-US', 'Registration Renewal', '{{DocumentId}} Registration renewal ready for attestation.','SMS Message', null, NEWID()),
/*NF035*/(87, 87, 'en-US', 'Registration Withdrawal', '{{DocumentId}} notice of withdrawal submitted successfully.','SMS Message', null, NEWID()),
/*NF031*/(88, 88, 'en-US', 'Registration Termination ', '{{DocumentId}} {{Filer name}} Notice of termination ready for Attestation.','SMS Message', null, NEWID()),
/*NF028*/(89, 89, 'en-US', '48h amendement report', '{{DocumentId}} Activity  report ready for attestation','SMS Message', null, NEWID()),
/*NF029*/(90, 90, 'en-US', '48h amendment report', '{{DocumentId}} Amendment report submitted successfully','SMS Message', null, NEWID()),
/*NF015*/(91, 91, 'en-US', 'Disclosure/ Amendment', '{{DocumentId}}  Activity report ready for Attestation','SMS Message', null, NEWID()),
/*NF020*/(92, 92, 'en-US', 'Disclosure/ Amendment', '{{DocumentId}}  Amendment report submitted successfully','SMS Message', null, NEWID()),
/*NF021*/(93, 93, 'en-US', '{{DocumentId}} Amendment report submitted successfully', '{{DocumentId}} Amendment report submitted successfully.', 'SMS Message', null, NEWID()),
/*NF022*/(94, 94, 'en-US', '{{DocumentId}} A campaign committee must be established', '{{DocumentId}} A campaign committee must be established.', 'SMS Message', null, NEWID())
) AS source (Id, NotificationTemplateId, Locale, Subject, Message, SmsMessage, EmailTemplateId, AuditableResourceTag)
ON NotificationTemplateTranslation.Id = source.Id
WHEN MATCHED THEN 
    UPDATE SET
        NotificationTemplateId = source.NotificationTemplateId,
        Locale = source.Locale,
        Subject = source.Subject,
        Message = source.Message,
        SmsMessage = source.SmsMessage,
        EmailTemplateId = source.EmailTemplateId
WHEN NOT MATCHED THEN 
    INSERT (Id, NotificationTemplateId, Locale, Subject, Message, SmsMessage, EmailTemplateId, AuditableResourceTag)
    VALUES (source.Id, source.NotificationTemplateId, source.Locale, source.Subject, source.Message, source.SmsMessage,
            source.EmailTemplateId, NEWID());
GO

SET IDENTITY_INSERT [dbo].[NotificationTemplateTranslation] OFF;
GO
