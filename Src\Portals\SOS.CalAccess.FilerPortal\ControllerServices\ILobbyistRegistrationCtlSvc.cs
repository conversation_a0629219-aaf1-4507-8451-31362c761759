using Microsoft.AspNetCore.Mvc.ModelBinding;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

public interface ILobbyistRegistrationCtlSvc
{

    /// <summary>
    /// Gets view model data for Page03
    /// </summary>
    /// <param name="id"></param>
    /// <param name="selfRegister"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<LobbyistRegistrationStep01ViewModel> GetPage03ViewModel(long? id, bool selfRegister, CancellationToken cancellationToken);

    /// <summary>
    /// Gets view model data for Page07
    /// </summary>
    /// <param name="id"></param>
    /// <param name="selfRegister"></param>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    Task<LobbyistRegistrationStep03ViewModel> GetPage07ViewModel(long id, CancellationToken cancellationToken);

    /// <summary>
    /// Gets view model for Page08
    /// </summary>
    /// <param name="id">Registration ID</param>
    /// <exception cref="KeyNotFoundException">Throw an error if the ID is not present</exception>
    ConfirmationViewModel Page08GetViewModel(long? id);

    /// <summary>
    /// Submits the Page 03 registration details.
    /// </summary>
    /// <param name="model">The view model containing the smo registration data for Page 03.</param>
    /// <param name="modelState">The model state dictionary to capture validation errors.</param>    
    /// <param name="isSubmission">Indicates whether this is a final submission (true) or a draft save (false).</param>
    /// <returns>Returns the smo registration ID if successful, otherwise null.</returns>
    Task<long?> Page03Submit(LobbyistRegistrationStep01ViewModel model, ModelStateDictionary modelState, bool isSubmission = true);

    /// <summary>
    /// Submit SMO registration form
    /// </summary>
    /// <param name="model">View model of Lobbyist registration step 01</param>
    /// <param name="modelState">ModelState property from View calling this method</param>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<RegistrationResponseDto> SubmitLobbyistRegistrationForm(LobbyistRegistrationStep01ViewModel model, ModelStateDictionary modelState, LobbyistRegistrationRequestDto request);

    /// <summary>
    /// Post the lobbyist registration for either sending for attestation or submission.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<RegistrationResponseDto> SubmitLobbyistRegistration(LobbyistRegistrationStep03ViewModel model);

    /// <summary>
    /// Gets view model data for lobbyist verification
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<LobbyistVerificationViewModel> GetLobbyistVerificationViewModel(long id);

    /// <summary>
    /// Map the view model to the Lobbyist contact request
    /// </summary>
    /// <param name="model">ModelState property from View calling this method</param>
    /// <returns>SMO contact request object</returns>
    LobbyistRegistrationRequestDto MapLobbyistViewModelToRequest(LobbyistRegistrationStep01ViewModel model);

    /// <summary>
    /// Get the view model of withdraw lobbyist registration
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    LobbyistRegistrationWithdrawalViewModel MapWithdrawLobbyistRegistrationViewModel(LobbyistResponseDto dto);

    /// <summary>
    /// Get the view model of terminate lobbyist registration
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    LobbyistTerminationNoticeViewModel MapTerminateLobbyistRegistrationViewModel(LobbyistResponseDto dto);

    void MappingViewModelFromDto(LobbyistRegistrationStep01ViewModel model, Generated.LobbyistResponseDto dto);

    /// <summary>
    /// Initialize a Lobbyist Registration Amendment record
    /// </summary>
    /// <param name="id">id</param>
    Task<long> CreateAmendLobbyistRegistration(long id);

    /// <summary>
    /// Gets the confirmation view model once post attestation
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A <see cref="LobbyistConfirmationViewModel"/> Submission date and pending items.</returns>
    Task<LobbyistConfirmationViewModel?> GetConfirmationViewModel(long filingId);
}
