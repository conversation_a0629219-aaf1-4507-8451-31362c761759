using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.Notifications;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Notifications;

/// <summary>
/// Notification Message Repository Tests
/// </summary>
[TestFixture]
public class NotificationMessageRepositoryTests
{
    private DatabaseContext _dbContext;
    private NotificationMessageRepository _notificationMessageRepository;
    private readonly ILogger<NotificationMessageRepository> _logger = Substitute.For<ILogger<NotificationMessageRepository>>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    [SetUp]
    public void SetUp()
    {
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase(databaseName: "CARS")
            .Options;

        _dbContext = new DatabaseContext(options);
        _ = _dbContext.Database.EnsureCreated();

        _notificationMessageRepository = new NotificationMessageRepository(_dbContext, _dateTimeSvc, _logger);

        SeedData();
    }

    private void SeedData()
    {
        //Ensure database is cleared before seeding
        _dbContext.NotificationMessages.RemoveRange(_dbContext.NotificationMessages);
        _dbContext.NotificationTemplates.RemoveRange(_dbContext.NotificationTemplates);
        _dbContext.Filertypes.RemoveRange(_dbContext.Filertypes);
        _dbContext.NotificationTypes.RemoveRange(_dbContext.NotificationTypes);
        _dbContext.Users.RemoveRange(_dbContext.Users);
        _dbContext.Registrations.RemoveRange(_dbContext.Registrations);
        _ = _dbContext.SaveChanges();

        var user1 = new Models.UserAccountMaintenance.User { Id = 1, UserName = "user1", FirstName = "TestFirstName", LastName = "TestLastName", EmailAddress = "<EMAIL>", EntraOid = "TestOid" };
        _ = _dbContext.Users.Add(user1);
        _ = _dbContext.SaveChanges();


        var filerType = new FilerType { Id = 101, Name = "Lobbyist" };
        var notificationType = new NotificationType { Id = 5, Name = "Reminder" };

        _ = _dbContext.Filertypes.Add(filerType);
        _ = _dbContext.NotificationTypes.Add(notificationType);
        _ = _dbContext.SaveChanges();

        var template = new NotificationTemplate
        {
            Id = 1,
            Name = "Test Template",
            Description = "This is a test template",
            FilerTypeId = filerType.Id,
            FilerType = filerType,
            IsActionRequired = true,
            IsPriorityMessage = false,
            NotificationTypeId = notificationType.Id,
            NotificationType = notificationType,
            Translations = new List<NotificationTemplateTranslation>
        {
            new() { Locale = "en-US", Message = "Test Message", Subject = "Test Subject", SmsMessage = "Test SMS Message" }
        }
        };

        var registration = new LobbyingFirm
        {
            Id = 10,
            Name = "Filer Corp",
            StatusId = 1,
            FilerId = 6,
            Email = "<EMAIL>",
            ResponsibleOfficerTitle = "OfficerTitle",
            LegislativeSession = new LegislativeSession() { Name = "Legislative Session Name" },
            LegislativeSessionId = 1
        };

        var registration1 = new LobbyingFirm
        {
            Id = 11,
            Name = "Filer Corp",
            StatusId = 1,
            FilerId = 7,
            Email = "<EMAIL>",
            ResponsibleOfficerTitle = "OfficerTitle",
            LegislativeSession = new LegislativeSession() { Name = "Legislative Session Name" },
            LegislativeSessionId = 1
        };

        _ = _dbContext.NotificationTemplates.Add(template);
        _ = _dbContext.Registrations.Add(registration);
        _ = _dbContext.Registrations.Add(registration1);
        _ = _dbContext.SaveChanges();

        var messages = new List<NotificationMessage>
        {
            new()
            {
                Id = 1, CreatedAt = new DateTime(2025,3,1,0,0,0,0), DueDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(5),
                FilerId = 6,
                Filer = new Filer { Id = 6, CurrentRegistrationId = registration.Id, CurrentRegistration = registration },
                NotificationTemplateId = template.Id, NotificationTemplate = template, ViewedAt = new DateTime(2025,3,1,0,0,0,0),
                ResolvedAt = new DateTime(2025,3,1,0,0,0,0), DeletedAt = null, UserId = user1.Id,
                User = user1, TemplateData = /*lang=json,strict*/ """{"key":"value"}"""
            },
            new()
            {
                Id = 2, CreatedAt = new DateTime(2025,3,2,0,0,0,0), DueDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(3),
                FilerId = 7, Filer = new Filer { Id = 7, CurrentRegistrationId = registration1.Id, CurrentRegistration = registration1 }, NotificationTemplateId = template.Id, NotificationTemplate = template,
                ViewedAt = new DateTime(2025,3,2,0,0,0,0), ResolvedAt = new DateTime(2025,3,2,0,0,0,0), DeletedAt = null, UserId = 1, User = user1, TemplateData = null
            },
            new()
            {
                Id = 3, CreatedAt = new DateTime(2025,3,3,0,0,0,0), DueDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(1),
                FilerId = 8, NotificationTemplateId = template.Id, NotificationTemplate = template,
                ResolvedAt = null, DeletedAt = null, UserId = 1, User= user1, TemplateData = null
            }
        };

        var notificationMessages = new List<NotificationMessage>
        {
            new(){ Id = 4, CreatedAt=new DateTime(2025,1,1,0,0,0,0), FilerId=6, NotificationTemplateId=4, ResolvedAt = null, DeletedAt = null },
            new(){ Id = 5, CreatedAt=new DateTime(2025,1,1,0,0,0,0), FilerId=6, NotificationTemplateId=4, ResolvedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local), DeletedAt = null },
            new(){ Id = 6, CreatedAt=new DateTime(2025,1,1,0,0,0,0), ViewedAt = new DateTime(2025,3,1,0,0,0,0), FilerId=6, NotificationTemplateId=4, ResolvedAt = new DateTime(2025,3,1,0,0,0,0), DeletedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local) },
            new(){ Id = 7, CreatedAt=new DateTime(2025,1,1,0,0,0,0), ViewedAt = new DateTime(2025,3,2,0,0,0,0), FilerId=5, NotificationTemplateId=4, ResolvedAt = new DateTime(2025,3,2,0,0,0,0), DeletedAt = null },
            new(){ Id = 8,User= user1, UserId =user1.Id, CreatedAt=new DateTime(2025,1,1,0,0,0,0), ViewedAt = new DateTime(2025,3,2,0,0,0,0), FilerId=5, NotificationTemplateId=4, ResolvedAt = null, DeletedAt = null }
        };

        _dbContext.NotificationMessages.AddRange(messages);
        _dbContext.NotificationMessages.AddRange(notificationMessages);

        _ = _dbContext.SaveChanges();
    }

    [TearDown]
    public void TearDown()
    {
        _ = _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }

    private async Task<List<NotificationMessageDto>> GetSortedResults(string column, SortDirection direction)
    {
        var request = new PagedUserDataRequest("user1", 0, 3, column, direction, null, null);
        return await _notificationMessageRepository.FindAllNotificationsByUser(request);
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldReturnPaginatedNotifications()
    {
        // Arrange
        var request = new PagedUserDataRequest("user1", 0, 2, "CreatedAt", SortDirection.Ascending, null, null);

        // Act
        var result = await _notificationMessageRepository.FindAllNotificationsByUser(request);

        // Assert
        Assert.That(result, Has.Count.EqualTo(2), "Should return exactly 2 notifications for user1.");
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldReturnEmptyList_WhenUserHasNoNotifications()
    {

        // Arrange
        var request = new PagedUserDataRequest("non_existing_user", 0, 5, "CreatedAt", SortDirection.Ascending, null, null);

        // Act
        var result = await _notificationMessageRepository.FindAllNotificationsByUser(request);

        // Assert
        Assert.That(result, Is.Empty, "Should return an empty list when no notifications exist for the user.");
    }

    [Test]
    public void ParseTemplateData_ShouldReplaceVariables()
    {
        // Arrange
        var dictionary = new Dictionary<string, string> { { "key1", "value1" }, { "key2", "value2" } };
        string text = "{{key1}} and {{key2}}";

        var methodInfo = typeof(NotificationMessageRepository)
            .GetMethod("ParseTemplateData", BindingFlags.NonPublic | BindingFlags.Static);

        // Act
        var result = methodInfo?.Invoke(null, [dictionary, text]);

        // Assert
        Assert.That(result, Is.EqualTo("value1 and value2"), "Value of parsed data should be 'value1 and value2'.");
    }

    [Test]
    public void GetFilerName_ShouldReturnFilerName_WhenFilerNameIsNotNull()
    {
        // Arrange
        var methodInfo = typeof(NotificationMessageRepository)
            .GetMethod("GetFilerName", BindingFlags.NonPublic | BindingFlags.Static);
        long? filerId = 123;
        // Act
        var result = methodInfo?.Invoke(null, ["Test Filer", filerId]) as string;

        // Assert
        Assert.That(result, Is.EqualTo("Test Filer(123)"), "Should return formatted FilerName with ID.");
    }

    [Test]
    public void GetFilerName_ShouldReturnFilerId_WhenFilerNameIsNull()
    {
        // Arrange
        var methodInfo = typeof(NotificationMessageRepository)
            .GetMethod("GetFilerName", BindingFlags.NonPublic | BindingFlags.Static);
        long? filerId = 456;
        // Act
        var result = methodInfo?.Invoke(null, [null, filerId]) as string;

        // Assert
        Assert.That(result, Is.EqualTo("456"), "Should return only Filer ID when Filer Name is null.");
    }

    [Test]
    public void GetFilerName_ShouldTruncateLongNames()
    {
        // Arrange
        string longFilerName = new('A', 60);
        var methodInfo = typeof(NotificationMessageRepository)
            .GetMethod("GetFilerName", BindingFlags.NonPublic | BindingFlags.Static);
        long? filerId = 789;
        // Act
        var result = methodInfo?.Invoke(null, [longFilerName, filerId]) as string;

        // Assert
        Assert.That(result, Has.Length.LessThanOrEqualTo(58), "Should truncate long Filer names properly.");
        Assert.That(result, Does.Contain("..."), "Truncated names should include ellipsis.");
    }

    [Test]
    public void GetFilerName_ShouldReturnEmptyStringIfFilerIdIsNull()
    {
        // Arrange
        string longFilerName = new('A', 60);
        var methodInfo = typeof(NotificationMessageRepository)
            .GetMethod("GetFilerName", BindingFlags.NonPublic | BindingFlags.Static);
        long? filerId = null;
        // Act
        var result = methodInfo?.Invoke(null, [longFilerName, filerId]) as string;

        // Assert
        Assert.That(result, Is.EqualTo(string.Empty), "Should return null if Filer Id is null.");
    }

    [Test]
    public async Task FindById_ShouldReturnNotification()
    {
        // Act
        var result = await _notificationMessageRepository.FindById(2);

        // Assert
        Assert.That(result is not null, "One notification should be returned.");
    }

    [Test]
    public async Task FindMessageById_ShouldReturnDtoWithTemplateTranslation()
    {
        // Act
        var result = await _notificationMessageRepository.FindMessageById(3);

        // Assert
        Assert.That(result, Is.Not.Null, "One notification should be returned.");
        Assert.That(result, Is.InstanceOf(typeof(NotificationMessageDto)), "Return type should be NotificationMessageDto.");
        Assert.That(result?.Subject is not null, "Subject should be populated from existing translation.");
    }

    [Test]
    public async Task FindAllActiveNotificationsByTemplateIdAndFilerId_ShouldReturnActiveNotifications()
    {
        // Act
        var result = await _notificationMessageRepository.FindAllActiveNotificationsByTemplateIdAndFilerId(4, 6);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1), "Only 1 active notification should be returned.");
    }

    [Test]
    public async Task FindAllActiveNotificationsByTemplateIdAndFilerId_ShouldReturnEmptyList_WhenNoActiveNotificationsExist()
    {
        // Act
        var result = await _notificationMessageRepository.FindAllActiveNotificationsByTemplateIdAndFilerId(4, 999); // Non-existent FilerId

        // Assert
        Assert.That(result, Is.Empty, "Should return an empty list when no active notifications exist.");
    }

    [Test]
    public async Task CountNotificationsByUser_ShouldReturnNoticationsCount()
    {
        //Act

        var result = await _notificationMessageRepository.CountNotificationsByUser("user1");

        //Assert
        Assert.That(result, Is.EqualTo(2));
    }

    [Test]
    public async Task CountActiveNotificationsByUser_ShouldReturnNoticationsCount()
    {
        //Act

        var result = await _notificationMessageRepository.CountActiveUserNotificationsByUser("user1");

        //Assert
        Assert.That(result, Is.EqualTo(2));
    }

    [Test]
    public async Task CountNotificationsByUserAndFilerTypeId_WithNoFilerType_ReturnsCount()
    {

        // Act
        var result = await _notificationMessageRepository.CountNotificationsByUserAndFilerTypeId(username: "user1", filerTypeId: 0);

        // Assert
        Assert.That(result, Is.EqualTo(2));
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByCreatedAtAscending()
    {
        // Arrange
        var request = new PagedUserDataRequest("user1", 0, 3, "CreatedAt", SortDirection.Ascending, null, null);

        // Act
        var result = await _notificationMessageRepository.FindAllNotificationsByUser(request);

        // Assert
        Assert.That(result, Has.Count.EqualTo(2), "There should be 2 notifications.");
        Assert.Multiple(() =>
        {
            Assert.That(result[0].CreatedAt, Is.LessThan(result[1].CreatedAt), "First element should be older than second.");
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByCreatedAtDescending()
    {
        // Arrange
        var request = new PagedUserDataRequest("user1", 0, 3, "CreatedAt", SortDirection.Descending, null, null);

        // Act
        var result = await _notificationMessageRepository.FindAllNotificationsByUser(request);

        // Assert
        Assert.That(result, Has.Count.EqualTo(2), "There should be 2 notifications.");
        Assert.Multiple(() =>
        {
            Assert.That(result[0].CreatedAt, Is.GreaterThan(result[1].CreatedAt), "First element should be newer than second.");
        });
    }

    [Test]
    public void AddSorting_ShouldThrowException_ForInvalidColumn()
    {
        // Arrange
        var request = new PagedUserDataRequest("user1", 0, 3, "InvalidColumn", SortDirection.Ascending, null, null);

        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() =>
        {
            _ = _notificationMessageRepository.FindAllNotificationsByUser(request).GetAwaiter().GetResult();
        });

        Assert.That(exception.Message, Does.Contain("Property not defined for sorting"), "Exception message should indicate an invalid property.");
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByIdAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.Id), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].Id, Is.EqualTo(1));
            Assert.That(result[1].Id, Is.EqualTo(2));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByIdDescending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.Id), SortDirection.Descending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].Id, Is.EqualTo(2));
            Assert.That(result[1].Id, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByDueDateAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.DueDate), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].DueDate, Is.LessThan(result[1].DueDate));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByDueDateDescending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.DueDate), SortDirection.Descending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].DueDate, Is.GreaterThan(result[1].DueDate));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByFilerIdAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.FilerId), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FilerId, Is.EqualTo(6));
            Assert.That(result[1].FilerId, Is.EqualTo(7));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByFilerIdDescending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.FilerId), SortDirection.Descending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FilerId, Is.EqualTo(7));
            Assert.That(result[1].FilerId, Is.EqualTo(6));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByViewedAtAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.ViewedAt), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].ViewedAt, Is.EqualTo(new DateTime(2025, 3, 1, 0, 0, 0, 0)));
            Assert.That(result[1].ViewedAt, Is.EqualTo(new DateTime(2025, 3, 2, 0, 0, 0, 0)));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByViewedAtDescending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.ViewedAt), SortDirection.Descending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].ViewedAt, Is.EqualTo(new DateTime(2025, 3, 2, 0, 0, 0, 0)));
            Assert.That(result[1].ViewedAt, Is.EqualTo(new DateTime(2025, 3, 1, 0, 0, 0, 0)));
        });
    }


    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByResolvedAtAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.ResolvedAt), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].ResolvedAt, Is.EqualTo(new DateTime(2025, 3, 1, 0, 0, 0, 0)));
            Assert.That(result[1].ResolvedAt, Is.EqualTo(new DateTime(2025, 3, 2, 0, 0, 0, 0)));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByResolvedAtDescending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.ResolvedAt), SortDirection.Descending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].ResolvedAt, Is.EqualTo(new DateTime(2025, 3, 2, 0, 0, 0, 0)));
            Assert.That(result[1].ResolvedAt, Is.EqualTo(new DateTime(2025, 3, 1, 0, 0, 0, 0)));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByIsPriorityMessageAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.NotificationTemplate.IsPriorityMessage), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].IsPriorityMessage, Is.False);
            Assert.That(result[1].IsPriorityMessage, Is.False);
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByIsActionRequiredeAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessage.NotificationTemplate.IsActionRequired), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].IsPriorityMessage, Is.False);
            Assert.That(result[1].IsPriorityMessage, Is.False);
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortBySubjectAscending()
    {
        var result = await GetSortedResults(nameof(NotificationTemplateTranslation.Subject), SortDirection.Ascending);
        Assert.That(result[0].Subject, Is.EqualTo("Test Subject"));
    }


    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByFilerNameAscending()
    {
        var result = await GetSortedResults(nameof(NotificationMessageDto.FilerName), SortDirection.Ascending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FilerName, Is.EqualTo("Filer Corp(7)"));
            Assert.That(result[1].FilerName, Is.EqualTo("Filer Corp(6)"));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUser_ShouldSortByFilerNameDescending()
    {
        var result = await GetSortedResults(nameof(NotificationMessageDto.FilerName), SortDirection.Descending);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FilerName, Is.EqualTo("Filer Corp(7)"));
            Assert.That(result[1].FilerName, Is.EqualTo("Filer Corp(6)"));
        });
    }

    [Test]
    public async Task UpdateRange_ShouldUpdateEntities_AndReturnThem()
    {
        // Arrange
        var entities = new List<NotificationMessage>
        {
            new() { Id = 9, NotificationTemplateId = 1 },
            new() { Id = 10, NotificationTemplateId = 1 }
        };

        // Add initial data to DB
        await _dbContext.NotificationMessages.AddRangeAsync(entities);
        _ = await _dbContext.SaveChangesAsync();

        // Modify entities
        entities[0].NotificationTemplateId = 2;
        entities[1].NotificationTemplateId = 3;

        // Act
        var result = await _notificationMessageRepository.UpdateRange(entities);

        // Assert
        Assert.That(result, Is.EqualTo(entities), "Returned entities should match input.");
        var updated1 = await _dbContext.NotificationMessages.Where(x => x.NotificationTemplateId == 2).ToListAsync();
        var updated2 = await _dbContext.NotificationMessages.Where(x => x.NotificationTemplateId == 3).ToListAsync();
        Assert.Multiple(() =>
        {
            Assert.That(updated1[0].NotificationTemplateId, Is.EqualTo(2));
            Assert.That(updated2[0].NotificationTemplateId, Is.EqualTo(3));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUserAndFilerType_FilerTypeZero_ReturnsPagedAndSortedResults()
    {
        // Arrange
        var request = new PagedUserDataRequest(
            Username: "user1",
            Skip: 0,
            Take: 10,
            SortColumn: nameof(NotificationMessage.CreatedAt),
            SortDirection: SortDirection.Descending,
            SearchFields: null,
            SearchValue: ""
        );

        // Act
        var result = await _notificationMessageRepository.FindAllNotificationsByUserAndFilerType(request, filerTypeId: 0);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(2));

        var dto = result[0];
        Assert.Multiple(() =>
        {
            Assert.That(dto.FilerName, Is.EqualTo("Filer Corp(7)"));
            Assert.That(dto.Subject, Is.EqualTo("Test Subject"));
        });
    }

    [Test]
    public async Task FindAllNotificationsByUserAndFilerType_ValidFilerType_ReturnsPagedAndSortedResults()
    {
        // Arrange
        var request = new PagedUserDataRequest(
            Username: "user1",
            Skip: 0,
            Take: 10,
            SortColumn: nameof(NotificationMessage.CreatedAt),
            SortDirection: SortDirection.Descending,
            SearchFields: null,
            SearchValue: ""
        );

        // Act
        var result = await _notificationMessageRepository.FindAllNotificationsByUserAndFilerType(request, filerTypeId: 101);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(2));

        var dto = result[0];
        Assert.Multiple(() =>
        {
            Assert.That(dto.FilerName, Is.EqualTo("Filer Corp(7)"));
            Assert.That(dto.Subject, Is.EqualTo("Test Subject"));
        });
    }

    [Test]
    public async Task DeleteNotification_UpdatesDeletedAt()
    {
        // Arrange
        var newMessage = await _notificationMessageRepository.Create(new()
        {
            Id = 86,
            CreatedAt = new DateTime(2025, 3, 1, 0, 0, 0, 0),
            DueDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(5),
            FilerId = 6,
            NotificationTemplateId = 1,
            ViewedAt = new DateTime(2025, 3, 1, 0, 0, 0, 0),
            ResolvedAt = new DateTime(2025, 3, 1, 0, 0, 0, 0),
            DeletedAt = null,
            UserId = 1,
        });

        // Act
        var recordsAffected = _notificationMessageRepository.Delete(newMessage);

        // Assert
        Assert.That(newMessage.DeletedAt, Is.Not.Null, "Should have DeletedAt value.");
    }

    [Test]
    public async Task CountNotificationsByUserAndFilerTypeId_ValidFilerType_ReturnsCount()
    {
        // Act
        var result = await _notificationMessageRepository.CountNotificationsByUserAndFilerTypeId(username: "user1", filerTypeId: 101);

        // Assert
        Assert.That(result, Is.EqualTo(2));
    }

    [Test]
    public void ParseSearchTerms_Reflection_MixedQuotedAndUnquoted_ReturnsExpected()
    {
        // Arrange
        var input = "\"Search\" Test \"Demo Test\"";

        // Use reflection to get the private static method
        var method = typeof(NotificationMessageRepository)
            .GetMethod("ParseSearchTerms", BindingFlags.NonPublic | BindingFlags.Static);
        Assert.That(method, Is.Not.Null, "ParseSearchTerms method not found");

        // Act
        var result = method.Invoke(null, [input]) as List<string>;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EquivalentTo(new List<string> { "Search", "Test", "Demo Test" }));
    }

    [Test]
    public void ParseSearchTerms_Reflection_EmptyInput_ReturnsEmptyList()
    {
        var input = "";

        var method = typeof(NotificationMessageRepository)
            .GetMethod("ParseSearchTerms", BindingFlags.NonPublic | BindingFlags.Static);
        var result = method!.Invoke(null, [input]) as List<string>;

        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task CountUnresolvedNotificationsForUserTest()
    {
        var username = "user1";
        var result = await _notificationMessageRepository.CountUnresolvedNotificationsByUser(username);

        Assert.That(result, Is.EqualTo(1));
    }
}
