using System.Globalization;
using SendGrid.Helpers.Errors.Model;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Contacts;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.OtherInfluencePayments;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;

/// <summary>
/// Implementation for the Transaction Service Interface.
/// </summary>
#pragma warning disable S107 // Methods should not have too many parameters
public sealed class TransactionSvc(
    ITransactionRepository transactionRepository,
    IFilerContactRepository filerContactRepository,
    IFilingContactSummaryRepository filingContactSummaryRepository,
    IFilingRepository filingRepository,
    IFilingPeriodRepository filingPeriodRepository,
    IDecisionsSvc decisionsSvc,
    ILobbyistEmployerRegistrationSvc lobbyistEmployerRegistrationSvc,
    ILobbyingFirmRegistrationSvc lobbyingFirmRegistrationSvc,
    IFilerContactSvc filerContactSvc,
    IFilingSvc filingSvc,
    IActionsLobbiedSvc actionsLobbiedSvc,
    IFilingSummaryRepository filingSummaryRepository,
    ITransactionHelperSvc transactionHelperSvc) : ITransactionSvc
#pragma warning restore S107 // Methods should not have too many parameters
{
    private readonly ITransactionRepository _transactionRepository = transactionRepository;
    private readonly ILobbyistEmployerRegistrationSvc _lobbyistEmployerRegistrationSvc = lobbyistEmployerRegistrationSvc;
    private readonly ILobbyingFirmRegistrationSvc _lobbyingFirmRegistrationSvc = lobbyingFirmRegistrationSvc;
    private readonly IFilerContactSvc _filerContactSvc = filerContactSvc;
    private readonly IFilingSvc _filingSvc = filingSvc;

    /// <inheritdoc />
    public Task AddAttachmentsToTransaction(IEnumerable<Attachment> attachments, long transactionId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task AddTransactionToFiling(long transactionId, long filingId)
    {
        await _transactionRepository.AddTransactionToFiling(transactionId, filingId);
    }

    /// <inheritdoc />
    public async Task<long> CreateTransaction(Transaction transaction)
    {
        Transaction createdTransaction = await _transactionRepository.Create(transaction);
        return createdTransaction.Id;
    }

    /// <inheritdoc />
    public Task<IEnumerable<ContributionType>> GetAllContributionTypes()
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<MonetaryType>> GetAllMonetaryTypes()
    {
        return await _transactionRepository.GetAllMonetaryTypes();
    }

    /// <inheritdoc />
    public Task<IEnumerable<Transaction>> GetAllTransactions()
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<Transaction>> GetAllTransactionsForFiler(long filerId)
    {
        return await _transactionRepository.GetAllByFilerId(filerId);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<Transaction>> GetAllTransactionsForFiling(long filingId)
    {
        return await _transactionRepository.GetAllByFilingId(filingId);
    }

    /// <inheritdoc />
    public Task<IEnumerable<TransactionType>> GetAllTransactionTypes()
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<Transaction?> GetTransaction(long transactionId)
    {
        return await _transactionRepository.FindById(transactionId);
    }

    /// <inheritdoc />
    public Task RemoveAttachmentsFromTransaction(IEnumerable<long> attachmentIds, long transactionId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task UpdateTransaction(Transaction transaction)
    {
        Transaction updatedTransaction = await _transactionRepository.Update(transaction);
    }

    /// <inheritdoc />
    public async Task<Transaction> CreateLobbyingCampaignContribution(long filerId, long filingId, string? recipientName, decimal? amount, DateTime contributionDate, bool? isCommittee, long? recipientCommitteeFilerId, bool? isContributer, string? nonFilerContributorName, long? contributorFilerId, string? separateAccountName)
    {
        LobbyingCampaignContribution contribution = new()
        {
            Amount = (Currency)amount!,
            TransactionDate = contributionDate,
            FilerId = filerId,
            SeparateAccountName = separateAccountName
        };

        if ((bool)isCommittee)
        {
            contribution.RecipientFilerId = recipientCommitteeFilerId;
        }
        else
        {
            contribution.NonCommitteeRecipientName = recipientName;
        }

        if ((bool)isContributer)
        {
            contribution.ContributorFilerId = contributorFilerId;
        }
        else
        {
            contribution.NonFilerContributorName = nonFilerContributorName;
        }

        Transaction createdTransaction = await CreateTransactionWrapper(contribution, filingId);
        return createdTransaction;
    }

    /// <inheritdoc />
    public async Task<Transaction> CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(long filerId, long filingId, string? recipientName, decimal? amount, DateTime contributionDate, bool? isCommittee, long? recipientCommitteeFilerId)
    {
        LobbyingCampaignContribution contribution = new()
        {
            Amount = (Currency)amount!,
            TransactionDate = contributionDate,
            FilerId = filerId
        };

        if ((bool)isCommittee)
        {
            contribution.RecipientFilerId = recipientCommitteeFilerId;
        }
        else
        {
            contribution.NonCommitteeRecipientName = recipientName;
        }

        Transaction createdTransaction = await CreateTransactionWrapper(contribution, filingId);

        return createdTransaction;
    }

    /// <inheritdoc />
    public async Task<Transaction> CreateOtherPaymentToInfluence(OtherInfluencePaymentDto otherInfluencePaymentDto)
    {
        List<ActionsLobbiedDto?> actionsLobbied = CombineActionsLobbied(otherInfluencePaymentDto) ?? [];
        OtherPaymentsToInfluence otherInfluencePayment = new()
        {
            FilerId = otherInfluencePaymentDto.FilerId,
            ContactId = otherInfluencePaymentDto.ContactId,
            PaymentCodeId = otherInfluencePaymentDto.PaymentCodeId,
            PaymentCodeDescription = otherInfluencePaymentDto.PaymentCodeDescription,
            Amount = (Currency)otherInfluencePaymentDto.Amount!,
            TransactionDate = otherInfluencePaymentDto.TransactionDate,
            OtherActionsLobbied = otherInfluencePaymentDto.OtherActionsLobbied
        };

        if (actionsLobbied.Count > 0)
        {
            otherInfluencePayment.ActionsLobbied = actionsLobbied.Select(al => new ActionsLobbied
            {
                BillId = al?.BillId,
                AgencyId = al?.AgencyId,
                AgencyDescription = al?.AgencyDescription,
                AdministrativeAction = al?.AdministrativeAction,
            }).ToList()!;
        }

        Transaction createdTransaction = await CreateTransactionWrapper(otherInfluencePayment, otherInfluencePaymentDto.FilingId);

        return createdTransaction;
    }

    /// <inheritdoc />
    public async Task<Transaction> EditOtherPaymentToInfluence(long transactionId, OtherInfluencePaymentDto request)
    {
        Transaction? existingTransaction = await _transactionRepository.FindById(transactionId) ?? throw new KeyNotFoundException($"Transaction with ID {transactionId} not found.");

        if (existingTransaction is not OtherPaymentsToInfluence otherInfluencePayment)
        {
            throw new InvalidOperationException("Transaction is not of type OtherPaymentsToInfluence");
        }

        List<ActionsLobbiedDto?> actionsLobbied = CombineActionsLobbied(request) ?? [];

        otherInfluencePayment.FilerId = request.FilerId;
        otherInfluencePayment.ContactId = request.ContactId;
        otherInfluencePayment.PaymentCodeId = request.PaymentCodeId;
        otherInfluencePayment.PaymentCodeDescription = request.PaymentCodeDescription;
        otherInfluencePayment.Amount = (Currency)request.Amount!;
        otherInfluencePayment.TransactionDate = request.TransactionDate;
        otherInfluencePayment.OtherActionsLobbied = request.OtherActionsLobbied;
        otherInfluencePayment.ActionsLobbied?.Clear();

        if (actionsLobbied.Count > 0)
        {
            otherInfluencePayment.ActionsLobbied = actionsLobbied.Select(al => new ActionsLobbied
            {
                BillId = al?.BillId,
                AgencyId = al?.AgencyId,
                AgencyDescription = al?.AgencyDescription,
                AdministrativeAction = al?.AdministrativeAction,
            }).ToList()!;
        }

        Transaction updatedTransaction = await _transactionRepository.Update(otherInfluencePayment);

        return updatedTransaction;
    }

    /// <inheritdoc />
    public async Task<OtherInfluencePaymentDto> GetOtherPaymentsToInfluenceTransactionById(long transactionId)
    {
        var transaction = await _transactionRepository.GetOtherPaymentsToInfluenceTransactionById(transactionId);
        var adminActions = transaction.ActionsLobbied.Where(a => a != null && a.AdministrativeAction != null).Select(a => new ActionsLobbiedDto()
        {
            AdministrativeAction = a!.AdministrativeAction,
            AgencyDescription = a!.AgencyDescription,
            AgencyId = a!.AgencyId,
            BillId = a!.BillId
        }).ToList();
        return new OtherInfluencePaymentDto
        {
            Id = transaction!.Id,
            Amount = transaction!.Amount,
            ContactId = transaction!.ContactId.GetValueOrDefault(),
            PaymentCodeId = transaction!.PaymentCodeId,
            OtherActionsLobbied = transaction!.OtherActionsLobbied,
            PaymentCodeDescription = transaction!.PaymentCodeDescription,
            TransactionDate = transaction.TransactionDate,
            AdministrativeActions = adminActions!,
            AdvertAdminActions = adminActions!.Count > 0,
            AssemblyBills = [],
            SenateBills = [],
        };
    }


    private static List<ActionsLobbiedDto?> CombineActionsLobbied(OtherInfluencePaymentDto dto)
    {
        return (dto.AssemblyBills ?? Enumerable.Empty<ActionsLobbiedDto?>())
            .Concat(dto.SenateBills ?? Enumerable.Empty<ActionsLobbiedDto?>())
            .Concat(dto.AdministrativeActions ?? Enumerable.Empty<ActionsLobbiedDto?>())
            .ToList();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<LobbyingCampaignContribution>> GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(long filingId)
    {
        return await _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ActivityExpense>> GetAllActivityExpenseTransactionsForFiling(long filingId)
    {
        return await _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PaymentMadeToLobbyingCoalitionResponse>> GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(long filingId, DateTime legislativeStartDate)
    {
        return await _transactionRepository.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, legislativeStartDate);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<OtherPaymentsToInfluenceResponse>> GetAllOtherPaymentsToInfluenceTransactionsForFiling(long filingId, DateTime legislativeStartDate)
    {
        return await _transactionRepository.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, legislativeStartDate);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<EndOfSessionLobbyingDto>> GetAllEndOfSessionLobbyingTransactionsForFiling(long filingId)
    {
        return await _transactionRepository.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId);
    }

    /// <inheritdoc />
    public async Task<CumulativeAmountResponse> GetOtherPaymentsCumulativeAmountForFilingAndContact(long filingId, long contactId)
    {
        DateTime legislativeStartDate = await _filingSvc.GetLegislativeStartDateForFiling(filingId);
        return await _transactionRepository.GetOtherPaymentsCumulativeAmountForFilingAndContact(filingId, contactId, legislativeStartDate);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PaymentMadeToLobbyingFirmsResponse>> GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(long filingId, DateTime legislativeStartDate)
    {
        return await transactionHelperSvc.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filingId, legislativeStartDate);
    }

    /// <inheritdoc />
    public async Task<TransactionSummaryResponse> GetTransactionSummary(long filingId)
    {
        Filing filing = await filingSvc.GetFiling(filingId) ?? throw new KeyNotFoundException($"Filing with ID {filingId} not found.");
        DateTime legislativeStartDate = filingSvc.GetLegislativeStartDateForDate(filing.StartDate);
        DateTime legislativeEndDate = filing.EndDate.Year % 2 == 0 ? filingSvc.GetLegislativeEndDateForDate(filing.EndDate) : filing.EndDate;
        IEnumerable<TransactionSummaryDto> transactions = await _transactionRepository.GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate);

        return new TransactionSummaryResponse
        {
            ActivityExpenseTransactions = [.. transactions.Where(t => t.TransactionTypeId == TransactionType.ActivityExpense.Id)],
            CampaignContributionTransactions = [.. transactions.Where(t => t.TransactionTypeId == TransactionType.LobbyingCampaignContribution.Id)],
            OtherPaymentsToInfluenceTransactions = [.. transactions.Where(t => t.TransactionTypeId == TransactionType.OtherPaymentsToInfluence.Id)],
            PaymentsToLobbyingFirmsTransactions = [.. transactions.Where(t => t.TransactionTypeId == TransactionType.PaymentMadeToLobbyingFirms.Id)],
            PaymentsToLobbyingCoalitionTransactions = [.. transactions.Where(t => t.TransactionTypeId == TransactionType.PaymentMadeToLobbyingCoalition.Id)],
            PaymentsReceivedByLobbyingCoalitionTransactions = [.. transactions.Where(t => t.TransactionTypeId == TransactionType.PaymentReceiveLobbyingCoalition.Id)],
            PaymentsPucActivityTransactions = [.. transactions.Where(t => t.FilingSummaryTypeId == FilingSummaryType.PucActivitySummary.Id)],
            PaymentsInHouseLobbyistTransactions = [.. transactions.Where(t => t.FilingSummaryTypeId == FilingSummaryType.PaymentsToInHouseLobbyists.Id)]
        };
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> CreatePaymentMadeToLobbyingCoalition(
        long filingId,
        decimal? amount,
        long filerId,
        long? registrationFilingId = null,
        long? filerContactId = null)
    {
        // Validate the input first with Decisions
        TransactionResponseDto result = await ValidatePaymentMadeToLobbyingCoalition(amount ?? default);

        if (result.ValidationErrors.Count == 0)
        {
            PaymentMadeToLobbyingCoalition transaction = new()
            {
                Amount = (Currency)amount,
                FilerId = filerId
            };

            // Case 1: When registrationFilingId is provided - the coalition is a registered filer
            if (registrationFilingId.HasValue)
            {
                FilerRegistration.Registrations.Models.LobbyistEmployerResponseDto? selectedCoalition = await _lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(registrationFilingId.Value);

                if (selectedCoalition == null || !selectedCoalition.FilerId.HasValue)
                {
                    throw new InvalidOperationException("Selected coalition is not found or does not have a FilerId");
                }

                // Try to find existing contact relationship between this filer and the coalition filer
                FilerContact? existingFilerContact = await _filerContactSvc.GetFilerContactByFilerIdAndContactFilerId(
                    filerId,
                    selectedCoalition.FilerId.Value);

                if (existingFilerContact != null)
                {
                    // Use existing contact relationship
                    transaction.ContactId = existingFilerContact.Id;
                }
                else
                {
                    // Create new contact relationship
                    long newFilerContactId = await _filerContactSvc.CreateFilerContact(new OrganizationContact
                    {
                        FilerId = filerId,
                        ContactFilerId = selectedCoalition.FilerId,
                        OrganizationName = selectedCoalition.Name,
                        AddressListId = selectedCoalition.AddressListId,
                        PhoneNumberListId = selectedCoalition.PhoneNumberListId
                    });

                    transaction.ContactId = newFilerContactId;
                }
            }
            // Case 2: When filerContactId is provided - using existing contact record
            else if (filerContactId.HasValue)
            {
                transaction.ContactId = filerContactId.Value;
            }

            Transaction createdTransaction = await CreateTransactionWrapper(transaction, filingId);

            result.Valid = true;
            result.Id = createdTransaction.Id;
        }

        return result;
    }

    public async Task<TransactionResponseDto> EditPaymentMadeToLobbyingCoalitionAmount(long transactionId, decimal? amount)
    {
        // Validate the input with Decisions
        TransactionResponseDto result = await ValidatePaymentMadeToLobbyingCoalition(amount ?? default);

        if (result.ValidationErrors.Count == 0)
        {
            result.Valid = true;
            result.Id = transactionId;
            Transaction transaction = await GetTransaction(transactionId) ?? throw new NotFoundException($"Transaction {transactionId} not found.");
            transaction!.Amount = (Currency)amount!;

            _ = await _transactionRepository.Update(transaction);
        }

        return result;
    }

    public async Task<TransactionResponseDto> CreatePaymentMadeToLobbyingFirmsTransaction(PaymentMadeToLobbyingFirmsRequestDto request)
    {
        (bool isValid, List<WorkFlowError> validationErrors) = await ValidatePaymentMadeToLobbyingFirms(request);
        if (!isValid)
        {
            return new TransactionResponseDto
            {
                Id = null,
                Valid = false,
                ValidationErrors = validationErrors!
            };
        }

        decimal totalAmount = (request.FeesAndRetainersAmount ?? 0)
                        + (request.ReimbursementOfExpensesAmount ?? 0)
                        + (request.AdvancesOrOtherPaymentsAmount ?? 0);

        PaymentMadeToLobbyingFirms transaction = new()
        {
            FeesAndRetainersAmount = request.FeesAndRetainersAmount.HasValue ? (Currency)request.FeesAndRetainersAmount.Value : default,
            ReimbursementOfExpensesAmount = request.ReimbursementOfExpensesAmount.HasValue ? (Currency)request.ReimbursementOfExpensesAmount.Value : default,
            AdvancesOrOtherPaymentsAmount = request.AdvancesOrOtherPaymentsAmount.HasValue ? (Currency)request.AdvancesOrOtherPaymentsAmount.Value : default,
            AdvancesOrOtherPaymentsExplanation = request.AdvancesOrOtherPaymentsExplanation,
            Amount = (Currency)totalAmount,
            FilerId = request.FilerId
        };

        await HandleLobbyingFirmContact(
            transaction,
            request.FilerId,
            request.RegistrationFilingId,
            request.FilerContactId);

        Transaction createdTransaction = await CreateTransactionWrapper(transaction, request.FilingId);

        return new TransactionResponseDto
        {
            Id = createdTransaction.Id,
            Valid = true,
            ValidationErrors = []
        };
    }

    /// <inheritdoc />
    public async Task<IEnumerable<PaymentReceiveLobbyingCoalitionResponse>> GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(long filingId)
    {
        DateTime legislativeStartDate = await _filingSvc.GetLegislativeStartDateForFiling(filingId);
        return await transactionHelperSvc.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId, legislativeStartDate);
    }

    public async Task<PaymentReceiveLobbyingCoalitionResponse> GetPaymentReceivedLobbyingCoalitionTransactionById(long transactionId)
    {
        var transaction = await _transactionRepository.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId)
                                    ?? throw new KeyNotFoundException($"Payment received lobbying Coalition transaction {transactionId}");
        return new PaymentReceiveLobbyingCoalitionResponse()
        {
            Id = transaction.Id,
            AmountThisPeriod = transaction.Amount,
            CoalitionName = string.Empty,
            Contact = new Contacts.Models.ContactResponseDto()
            {
                Id = transaction!.Contact!.Id,
            },
            CumulativeAmount = transaction.Amount
        };
    }

    /// <inheritdoc />
    public async Task<ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse> ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(long filingId)
    {
        bool isValid = true;
        List<WorkFlowError> validationErrors = [];

        IEnumerable<PaymentReceiveLobbyingCoalitionResponse> transactions = await GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId);
        PaymentReceiveLobbyingCoalitionDs decisionsData = new() { MemberCount = transactions.Count() };
        WorkFlowError decisionResponse = await decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionDs, WorkFlowError>(DecisionsWorkflow.PaymentReceiveLobbyingCoalitionRuleset, decisionsData, true);

        if (decisionResponse != null)
        {
            isValid = false;
            validationErrors.Add(decisionResponse);
        }

        return new ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse(filingId, isValid, validationErrors);
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> CreatePaymentReceivedByLobbyingCoalition(PaymentReceiveLobbyingCoalitionRequestDto request)
    {
        if (request.DisclosureFilingId is null)
        {
            throw new BadRequestException("DisclosureFilingId is required for creating the transaction.");
        }

        PaymentReceiveLobbyingCoalitionPaymentAmountDs decisionsData = new() { Amount = request.Amount };
        List<WorkFlowError> validationErrors = await decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionPaymentAmountDs, List<WorkFlowError>>(DecisionsWorkflow.PaymentReceiveLobbyingCoalitionPaymentAmountRuleset, decisionsData, true);

        if (validationErrors.Count > 0)
        {
            return new TransactionResponseDto
            {
                Id = null,
                Valid = false,
                ValidationErrors = validationErrors
            };
        }

        PaymentReceiveLobbyingCoalition transaction = new()
        {
            Amount = (Currency)request.Amount,
            FilerId = request.FilerId,
            ContactId = request.ContactId
        };

        Transaction createdTransaction = await CreateTransactionWrapper(transaction, (long)request.DisclosureFilingId);

        return new TransactionResponseDto
        {
            Id = createdTransaction.Id,
            Valid = true,
            ValidationErrors = []
        };
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> EditPaymentReceivedByLobbyingCoalition(long transactionId, PaymentReceiveLobbyingCoalitionRequestDto request)
    {
        Transaction transaction = await GetTransaction(transactionId) ?? throw new NotFoundException($"Transaction {transactionId} not found.");

        // Validate the input with Decisions
        PaymentReceiveLobbyingCoalitionPaymentAmountDs decisionsData = new() { Amount = request.Amount };
        List<WorkFlowError> validationErrors = await decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionPaymentAmountDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentReceiveLobbyingCoalitionPaymentAmountRuleset,
            decisionsData,
            true);

        if (validationErrors.Count > 0)
        {
            return new TransactionResponseDto
            {
                Id = transactionId,
                Valid = false,
                ValidationErrors = validationErrors
            };
        }

        transaction.ContactId = request.ContactId;
        transaction.Amount = (Currency)request.Amount!;

        _ = await _transactionRepository.Update(transaction);

        return new TransactionResponseDto
        {
            Id = transactionId,
            Valid = true,
            ValidationErrors = []
        };
    }

    /// <inheritdoc />
    public async Task<PaymentMadeToLobbyingCoalitionResponse?> GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(long transactionId)
    {
        long registrationId = default;
        PaymentMadeToLobbyingCoalition? transaction = await _transactionRepository.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(transactionId);
        if (transaction?.Contact is OrganizationContact organizationContact)
        {
            try
            {
                FilerRegistration.Registrations.Models.LobbyistEmployerResponseDto? registration = await _lobbyistEmployerRegistrationSvc.GetLobbyistEmployerByName(organizationContact.OrganizationName);
                registrationId = registration?.Id ?? 0L;
            }
            catch (Exception)
            {
                // Not throw to the FE
            }
        }
        PaymentMadeToLobbyingCoalitionResponse result = new()
        {
            AmountThisPeriod = transaction?.Amount ?? default,
            Id = transaction?.Id,
            CoalitionName = transaction?.Contact is OrganizationContact ? ((OrganizationContact)transaction.Contact!).OrganizationName : string.Empty,
            FilerId = transaction?.FilerId,
            FilingId = transaction?.FilingTransactions.FirstOrDefault()?.FilingId,
            CumulativeAmount = transaction?.Amount ?? default,
            ContactId = transaction?.ContactId,
            RegistrationId = registrationId
        };
        return result;
    }

    /// <inheritdoc />
    public async Task<LobbyingCampaignContribution?> GetLobbyistCampaignContributionTransactionById(long transactionId)
    {
        return await _transactionRepository.GetLobbyistCampaignContributionTransactionById(transactionId);
    }

    /// <inheritdoc />
    public async Task<Transaction> EditLobbyistCampaignContribution(LobbyistCampaignContributionRequestDto lobbyistCampaignContributionRequestDto)
    {
        if (lobbyistCampaignContributionRequestDto.Id is null or 0)
        {
            throw new BadRequestException("ID is required for updates");
        }

        LobbyingCampaignContribution? transaction = await GetLobbyistCampaignContributionTransactionById(lobbyistCampaignContributionRequestDto.Id.Value);
        LobbyingCampaignContribution contribution = GetLobbyingCampaignContributionObject(transaction, lobbyistCampaignContributionRequestDto);

        contribution.Id = lobbyistCampaignContributionRequestDto.Id.Value;
        Transaction x = await _transactionRepository.Update(contribution) ?? throw new InvalidOperationException("Transaction creation failed.");
        return x;
    }

    private static LobbyingCampaignContribution GetLobbyingCampaignContributionObject(LobbyingCampaignContribution? contribution,
        LobbyistCampaignContributionRequestDto lobbyistCampaignContributionRequestDto)
    {
        contribution!.Amount = (Currency)lobbyistCampaignContributionRequestDto.Amount!;
        contribution.TransactionDate = lobbyistCampaignContributionRequestDto.TransactionDate;
        contribution.FilerId = lobbyistCampaignContributionRequestDto.FilerId;
        contribution.SeparateAccountName = lobbyistCampaignContributionRequestDto.SeparateAccountName;

        if (lobbyistCampaignContributionRequestDto!.IsRecipientCommittee ?? false)
        {
            contribution.RecipientFilerId = lobbyistCampaignContributionRequestDto.RecipientCommitteeFilerId;
        }
        else
        {
            contribution.RecipientFilerId = null;
            contribution.RecipientFiler = null;
            contribution.NonCommitteeRecipientName = lobbyistCampaignContributionRequestDto.NonCommitteeRecipientName;
        }

        if (lobbyistCampaignContributionRequestDto!.IsContributorFiler ?? false)
        {
            contribution.ContributorFilerId = lobbyistCampaignContributionRequestDto.ContributorFilerId;
        }
        else
        {
            contribution.ContributorFiler = null;
            contribution.ContributorFilerId = null;
            contribution.NonFilerContributorName = lobbyistCampaignContributionRequestDto.NonFilerContributorName;
        }
        return contribution;
    }

    /// <inheritdoc />
    public async Task<LobbyingAdvertisement?> GetLobbyingAdvertisementTransactionByFilingId(long filingId)
    {
        return await _transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filingId);
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> CreateLobbyingAdvertisementTransaction(long filingId, LobbyingAdvertisementRequestDto request)
    {
        var filingSummaries = await filingSummaryRepository.GetAllByFilingId(filingId);

        var filingActionsLobbied =
            filingSummaries.FirstOrDefault(f => f.FilingSummaryTypeId == FilingSummaryType.LobbyingAdvertisementSummary.Id) ??
            throw new KeyNotFoundException($"Lobbying Advertisement Filing Summary not found.");

        // handle decision validation
        TransactionResponseDto result = await ValidateLobbyingAdvertisement(request);

        if (result.ValidationErrors.Count == 0)
        {
            filingActionsLobbied.FilingSummaryStatusId = FilingSummaryStatus.InProgress.Id;
            _ = await filingSummaryRepository.Update(filingActionsLobbied);

            var transaction = new LobbyingAdvertisement
            {
                Amount = (Currency)request.Amount!,
                PublicationDate = request.PublicationDate,
                DistributionMethodId = request.DistributionMethodId,
                DistributionMethodDescription = request.DistributionMethodDescription,
                AdditionalInformation = request.AdditionalInformation,
                FilerId = request.FilerId,
            };

            Transaction createdTransaction = await CreateTransactionWrapper(transaction, filingId);

            result.Id = createdTransaction.Id;
            result.Valid = true;
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto?> EditLobbyingAdvertisementTransaction(long filingId, LobbyingAdvertisementRequestDto request)
    {
        if (request is null)
        {
            throw new BadRequestException("ID is required for updates");
        }

        // handle decision validation
        TransactionResponseDto result = await ValidateLobbyingAdvertisement(request);

        if (result.ValidationErrors.Count == 0)
        {
            Transaction? transaction = await _transactionRepository.FindById(request.TransactionId ?? default);
            if (transaction is LobbyingAdvertisement lobbyingAdvertisement)
            {
                lobbyingAdvertisement.Amount = (Currency)request.Amount!;
                lobbyingAdvertisement.PublicationDate = request.PublicationDate;
                lobbyingAdvertisement.DistributionMethodId = request.DistributionMethodId;
                lobbyingAdvertisement.DistributionMethodDescription = request.DistributionMethodDescription;
                lobbyingAdvertisement.AdditionalInformation = request.AdditionalInformation;
                _ = await _transactionRepository.Update(lobbyingAdvertisement);

                result.Id = lobbyingAdvertisement.Id;
                result.Valid = true;
            }
            else
            {
                return null;
            }
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> CreateEndOfSessionLobbying(EndOfSessionLobbyingRequestDto request)
    {
        // Validate the input first with Decisions
        TransactionResponseDto result = await ValidateEndOfSessionLobbying(request);

        if (result.ValidationErrors.Count == 0)
        {
            EndOfSessionLobbying transaction = new()
            {
                Amount = (Currency)(request.Amount ?? 0),
                DateLobbyingFirmHired = request.DateLobbyingFirmHired,
                FilerId = request.FilerId
            };

            await HandleLobbyingFirmContact(
                transaction,
                request.FilerId,
                request.RegistrationFilingId,
                request.ContactId);

            Transaction createdTransaction = await CreateTransactionWrapper(transaction, request.FilingId);

            List<ActionsLobbiedRequestDto> actionsLobbied = request.AssemblyBills.Concat(request.SenateBills).ToList();
            _ = await actionsLobbiedSvc.UpsertActionsLobbiedForTransaction(createdTransaction.Id, actionsLobbied);
            result.Valid = true;
            result.Id = createdTransaction.Id;
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<TransactionResponseDto> EditEndOfSessionLobbying(long transactionId, EndOfSessionLobbyingRequestDto request)
    {
        // Validate the input first with Decisions
        TransactionResponseDto result = await ValidateEndOfSessionLobbying(request);

        if (result.ValidationErrors.Count == 0)
        {
            Transaction transaction = await GetTransaction(transactionId) ?? throw new NotFoundException($"Transaction {transactionId} not found.");

            if (transaction is not EndOfSessionLobbying endOfSessionLobbying)
            {
                throw new InvalidOperationException("Transaction is not of type EndOfSessionLobbying");

            }
            endOfSessionLobbying.Amount = (Currency)(request.Amount ?? 0);
            endOfSessionLobbying.DateLobbyingFirmHired = request.DateLobbyingFirmHired;

            Transaction updatedTransaction = await _transactionRepository.Update(endOfSessionLobbying);

            List<ActionsLobbiedRequestDto> actionsLobbied = request.AssemblyBills.Concat(request.SenateBills).ToList();
            _ = await actionsLobbiedSvc.UpsertActionsLobbiedForTransaction(updatedTransaction.Id, actionsLobbied);
            result.Valid = true;
            result.Id = updatedTransaction.Id;
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<EndOfSessionLobbyingDto> GetEndOfSessionLobbyingTransactionById(long transactionId)
    {
        var response = await _transactionRepository.GetAllEndOfSessionLobbyingTransactionById(transactionId)
            ?? throw new NotFoundException($"Transaction for Id={transactionId} not found.");
        return response;
    }

    /// <inheritdoc/>
    public async Task<List<TTransactionResponseDto>> GetAllTransactionsByFilingAsync<TTransaction, TTransactionResponseDto>(
        long filingId, Func<TTransaction,
            TTransactionResponseDto> responseFactory)
        where TTransaction : Transaction
        where TTransactionResponseDto : TransactionDetailResponseDto
    {
        bool isPaymentMadeByAgent = false;
        if (typeof(TTransactionResponseDto) == typeof(PaymentMadeByAgentOrIndependentContractorResponseDto))
        {
            isPaymentMadeByAgent = true;
        }

        List<TTransaction> result = await transactionRepository.FindAllTransactionsByFiling<TTransaction>(filingId, isPaymentMadeByAgent);

        return [.. result.Select(responseFactory)];
    }

    /// <inheritdoc/>
    public async Task DeleteTransactionAsync(long filingId, long transactionId)
    {
        Transaction transaction = await transactionRepository.FindById(transactionId) ?? throw new KeyNotFoundException($"Transaction not found. Id={transactionId}");

        // If transaction is already soft-deleted, do nothing.
        if (!transaction.Active)
        {
            return;
        }

        transaction.Active = false;

        if (transaction.TypeId == TransactionType.PaymentReceived.Id)
        {
            PaymentReceivedRequest deletionRequest = new() { ContactId = 0, Jurisdiction = string.Empty, Position = string.Empty, Amount = 0 };
            var filing = await filingRepository.FindById(filingId) ?? throw new KeyNotFoundException($"Filing not found. Id={transactionId}");
            (PaymentReceived? _, FilingContactSummary? previousContactSummary, FilingContactSummary _) = await GetSmoCampaignStatementFilingContactSummaryChanges(filing, deletionRequest, transactionId);
            if (previousContactSummary != null)
            {
                _ = filingContactSummaryRepository.Update(previousContactSummary);
            }
        }

        await transactionRepository.Update(transaction);

        // Update Filing Summary amount
        await _filingSvc.OnTransactionDeletedAsync(transaction, filingId);
    }

    /// <inheritdoc/>
    public async Task<Transaction> CreateTransactionWrapper<TTransaction>(
        TTransaction transaction,
        long filingId)
        where TTransaction : Transaction
    {
        Transaction createdTransaction = await _transactionRepository.Create(transaction) ?? throw new InvalidOperationException("Transaction creation failed.");
        await _transactionRepository.AddTransactionToFiling(createdTransaction.Id, filingId);
        await _filingSvc.OnTransactionCreated(transaction, filingId);

        return createdTransaction;
    }

    /// <inheritdoc/>
    public async Task UpdateTransactionWrapperAsync<TTransaction>(
        TTransaction transaction,
        decimal varianceAmount,
        long filingId,
        long contactId)
        where TTransaction : Transaction
    {
        var originalContactId = transaction.Contact?.Id;
        await UpdateFilerContactAsync(transaction, contactId);
        await _transactionRepository.Update(transaction);
        await _filingSvc.OnTransactionUpdatedAsync(transaction, varianceAmount, filingId, originalContactId);
    }

    /// <summary>
    /// Returns changes to the FilingContactSummary for this transaction request
    /// </summary>
    /// <param name="filing"></param>
    /// <param name="newTransactionRequest"></param>
    /// <param name="transactionId">If this is an update, the id of the DisclosureTransaction</param>
    /// <returns>existingTransaction transaction that is being updated, previousContactSummary if the summary category has changed, newContactSummary for the category associated with the transaction requested</returns>
    public async Task<(PaymentReceived? existingTransaction, FilingContactSummary? previousContactSummary, FilingContactSummary newContactSummary)>
            GetSmoCampaignStatementFilingContactSummaryChanges(Filing filing, PaymentReceivedRequest newTransactionRequest, long? transactionId)
    {
        FilingContactSummary? newFilingContactSummary = null;
        FilingContactSummary? previousFilingContactSummary = null;

        long filerId = filing.FilerId;
        FilingPeriod? filingPeriod = await filingPeriodRepository.FindById(filing.FilingPeriodId ?? 0);
        DateTime compareDate = filingPeriod!.EndDate;

        // Get List of valid statementIds
        List<SmoCampaignStatement> previousStatements = await filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(filerId, compareDate);
        List<long> validStatementIds = previousStatements.Select(i => i.Id).ToList();

        // Get Original PaymentReceived Record for offset
        PaymentReceived? existingPaymentReceived = (transactionId != null) ?
            await transactionRepository.GetSmoCampaignStatementPaymentRecievedByIdAsync((long)transactionId) : null;

        // Check if different.
        bool isDifferent = IsDifferentCategory(newTransactionRequest, existingPaymentReceived);

        // If isDifferent Get previousPaymentReceived
        if (isDifferent)
        {
            // Get list of payments that is filtered
            List<PaymentReceived> previousPaymentsReceived = await transactionRepository.GetMatchingTransactionByFilingId(
                validStatementIds,
                existingPaymentReceived?.ContactId ?? 0L,
                existingPaymentReceived?.DisclosureStanceOnCandidate?.Position ?? existingPaymentReceived?.DisclosureStanceOnBallotMeasure?.Position ?? string.Empty,
                existingPaymentReceived?.DisclosureStanceOnCandidate?.SubjectId,
                existingPaymentReceived?.DisclosureStanceOnBallotMeasure?.SubjectId) ?? new List<PaymentReceived>();

            // Sum amounts for all transactions except for this one
            decimal previousCategoryTotal = previousPaymentsReceived.Sum(x => (existingPaymentReceived?.Id == x.Id) ? 0 : x.Amount);

            // Get the previous FilingContactSummary from the first item in the list
            previousFilingContactSummary = previousPaymentsReceived.FirstOrDefault(x => x.DisclosureStanceOnCandidate?.FilingContactSummary != null)?.DisclosureStanceOnCandidate!.FilingContactSummary
                ?? previousPaymentsReceived.FirstOrDefault(x => x.DisclosureStanceOnBallotMeasure?.FilingContactSummary != null)?.DisclosureStanceOnBallotMeasure!.FilingContactSummary
                ?? new FilingContactSummary()
                {
                    DisclosureFilingId = filing.Id,
                    FilerContactId = 0L,
                    FilingContactSummaryTypeId = FilingContactSummaryType.PaymentReceived.Id,
                    Amount = 0,
                    PreviouslyUnitemizedAmount = 0,
                    CreatedBy = 0L,
                    ModifiedBy = 0L,
                };

            previousFilingContactSummary.Amount = previousCategoryTotal;
        }

        // Get list of payments that is filtered
        List<PaymentReceived> newPaymentsReceived = await transactionRepository.GetMatchingTransactionByFilingId(
            validStatementIds,
            newTransactionRequest.ContactId,
            newTransactionRequest.Position,
            newTransactionRequest.StanceOnCandidate?.CandidateId,
            newTransactionRequest.StanceOnBallotMeasure?.BallotMeasureId) ?? new List<PaymentReceived>();

        // Sum amounts for all transactions except for this one
        decimal newCategoryTotal = newPaymentsReceived.Sum(x => (transactionId == x.Id) ? 0 : x.Amount);
        newCategoryTotal += newTransactionRequest.Amount;

        // Add Unitemized
        newCategoryTotal += newTransactionRequest.UnitemizedAmount ?? 0;

        // Get the new FilingContactSummary from the first item in the list
        newFilingContactSummary = newPaymentsReceived.FirstOrDefault(x => x.DisclosureStanceOnCandidate?.FilingContactSummary != null)?.DisclosureStanceOnCandidate!.FilingContactSummary
            ?? newPaymentsReceived.FirstOrDefault(x => x.DisclosureStanceOnBallotMeasure?.FilingContactSummary != null)?.DisclosureStanceOnBallotMeasure!.FilingContactSummary
            ?? new FilingContactSummary()
            {
                DisclosureFilingId = filing.Id,
                FilerContactId = newTransactionRequest.ContactId,
                FilingContactSummaryTypeId = FilingContactSummaryType.PaymentReceived.Id,  // Placeholder until data is updated
                Amount = 0,
                PreviouslyUnitemizedAmount = 0,
                CreatedBy = 0L,
                ModifiedBy = 0L,
            };

        newFilingContactSummary.Amount = newCategoryTotal;
        if (newTransactionRequest.UnitemizedAmount != null)
        {
            newFilingContactSummary.PreviouslyUnitemizedAmount = newTransactionRequest.UnitemizedAmount;
        }

        return (existingPaymentReceived, previousFilingContactSummary, newFilingContactSummary);
    }

    private static bool IsDifferentCategory(PaymentReceivedRequest newTransaction, PaymentReceived? previous)
    {
        if (previous == null)
        {
            return false;
        }

        string prevPosition = previous.DisclosureStanceOnBallotMeasure?.Position ?? previous.DisclosureStanceOnCandidate?.Position ?? "";
        long prevCandidateId = previous.DisclosureStanceOnCandidate?.SubjectId ?? 0;
        long prevBallotId = previous.DisclosureStanceOnBallotMeasure?.SubjectId ?? 0;
        long prevContactId = previous.ContactId ?? 0;
        if (
            newTransaction.Position != prevPosition ||
            newTransaction.ContactId != prevContactId ||
            (newTransaction.StanceOnCandidate?.CandidateId ?? 0) != prevCandidateId ||
            (newTransaction.StanceOnBallotMeasure?.BallotMeasureId ?? 0) != prevBallotId
            )
        {
            return true;
        }
        return false;
    }

    #region Private
    /// <summary>
    /// Validates a payment made to lobbying firms request against business rules.
    /// </summary>
    /// <param name="request">The payment request to validate.</param>
    /// <returns>Validation result with errors if any were found.</returns>
    private async Task<(bool IsValid, List<WorkFlowError>? ValidationErrors)> ValidatePaymentMadeToLobbyingFirms(PaymentMadeToLobbyingFirmsRequestDto request)
    {
        PaymentMadeToLobbyingFirmsDs decisionsInput = new()
        {
            FeesRetainers = request.FeesAndRetainersAmount,
            ExpensesReimbursement = request.ReimbursementOfExpensesAmount,
            AdvancePayment = request.AdvancesOrOtherPaymentsAmount,
            AdvancePaymentExplanation = request.AdvancesOrOtherPaymentsExplanation
        };

        List<WorkFlowError>? validationErrors = await decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingFirmsDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentMadeToLobbyingFirmsRuleset,
            decisionsInput,
            true);

        return (validationErrors == null || validationErrors.Count == 0, validationErrors);
    }

    private async Task<TransactionResponseDto> ValidatePaymentMadeToLobbyingCoalition(decimal amount)
    {
        PaymentMadeToLobbyingCoalitionDs decisionsInput = new() { PeriodAmount = amount };
        TransactionResponseDto result = new()
        {
            Id = null,
            Valid = false,
            ValidationErrors = []
        };

        WorkFlowError validationError = await decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingCoalitionDs, WorkFlowError>(
            DecisionsWorkflow.PaymentMadeToLobbyingCoalitionRuleset,
            decisionsInput,
            true);

        if (validationError != null)
        {
            result.ValidationErrors.Add(validationError);
        }

        return result;
    }

    private async Task UpdateFilerContactAsync(Transaction transaction, long newContactId)
    {
        FilerContact newContact = await filerContactRepository.FindById(newContactId) ?? throw new KeyNotFoundException($"Filer Contact not found. Id={newContactId}");
        transaction.Contact = newContact;
    }

    private async Task<TransactionResponseDto> ValidateLobbyingAdvertisement(LobbyingAdvertisementRequestDto request)
    {
        LobbyingAdvertisementDs decisionsInput = new()
        {
            DistributionMethod = AdvertisementDistributionMethod.DistributionMethodDictionary.GetValueOrDefault(request.DistributionMethodId ?? default),
            Description = request.DistributionMethodDescription,
            PublicationDate = request.PublicationDate,
            Amount = request.Amount
        };

        List<WorkFlowError> validationErrors = await decisionsSvc.InitiateWorkflow<LobbyingAdvertisementDs, List<WorkFlowError>>(DecisionsWorkflow.FDLOBFiling72HourReportLobbyingAdvertisement, decisionsInput, true);

        return new TransactionResponseDto { ValidationErrors = validationErrors };
    }

    public async Task<TransactionResponseDto> ValidateEndOfSessionLobbying(EndOfSessionLobbyingRequestDto request)
    {
        IEnumerable<string> assemblyNumbers = request.AssemblyBills.Where(b => b.BillId.HasValue).Select(b => b.BillId!.Value.ToString(CultureInfo.InvariantCulture));
        IEnumerable<string> senateNumbers = request.SenateBills.Where(b => b.BillId.HasValue).Select(b => b.BillId!.Value.ToString(CultureInfo.InvariantCulture));
        EndOfSessionLobbyingDs decisionsInput = new()
        {
            Amount = request.Amount,
            LegislativeNumbers = assemblyNumbers.Concat(senateNumbers).ToList(),
            FirmHiringDate = request.DateLobbyingFirmHired?.ToString("yyyy-MM-ddTHH:mm:ss", CultureInfo.InvariantCulture)
        };

        List<WorkFlowError> validationErrors = await decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingDs, List<WorkFlowError>>(DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransactionAdditionalData, decisionsInput, true);

        return new TransactionResponseDto { ValidationErrors = validationErrors };
    }

    /// <summary>
    /// Handles the contact relationship for a transaction with a lobbying firm.
    /// </summary>
    /// <param name="transaction">The transaction to update with the contact relationship.</param>
    /// <param name="filerId">The ID of the filer creating the transaction.</param>
    /// <param name="registrationFilingId">Optional ID of a registered lobbying firm.</param>
    /// <param name="existingContactId">Optional ID of an existing contact relationship.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task HandleLobbyingFirmContact(
        Transaction transaction,
        long filerId,
        long? registrationFilingId = null,
        long? existingContactId = null)
    {
        // Case 1: When registrationFilingId is provided - the firm is a registered filer
        if (registrationFilingId.HasValue)
        {
            FilerRegistration.Registrations.Models.LobbyingFirmResponseDto? selectedFirm = await _lobbyingFirmRegistrationSvc.GetLobbyingFirm(registrationFilingId.Value);

            if (selectedFirm == null || !selectedFirm.FilerId.HasValue)
            {
                throw new InvalidOperationException("Selected firm is not found or does not have a FilerId");
            }

            // Try to find existing contact relationship between this filer and the lobbying firm filer
            FilerContact? existingFilerContact = await _filerContactSvc.GetFilerContactByFilerIdAndContactFilerId(
                filerId,
                selectedFirm.FilerId.Value);

            if (existingFilerContact != null)
            {
                // Use existing contact relationship
                transaction.ContactId = existingFilerContact.Id;
            }
            else
            {
                if (selectedFirm.Name is null)
                {
                    throw new InvalidOperationException("Selected firm does not have a valid name.");
                }

                // Create new contact relationship
                long newFilerContactId = await _filerContactSvc.CreateFilerContact(new OrganizationContact
                {
                    FilerId = filerId,
                    ContactFilerId = selectedFirm.FilerId,
                    OrganizationName = selectedFirm.Name,
                    AddressListId = selectedFirm.AddressListId,
                    PhoneNumberListId = selectedFirm.PhoneNumberListId
                });

                transaction.ContactId = newFilerContactId;
            }
        }
        // Case 2: When existingContactId is provided - using existing contact record
        else if (existingContactId.HasValue)
        {
            transaction.ContactId = existingContactId.Value;
        }
    }
    #endregion
}
