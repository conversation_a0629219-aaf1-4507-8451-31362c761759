@using System.Text.Json
@using SOS.CalAccess.UI.Common.Localization
@using SOS.CalAccess.UI.Common.Services;
@using SOS.CalAccess.FilerPortal.Models.Contacts
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.Models.FilerDisclosure.Contacts
@model SOS.CalAccess.FilerPortal.Models.Transactions.FirmPaymentTransactionViewModel
@inject IHtmlLocalizer<SharedResources> Localizer

@{
    ViewData["Title"] = SharedLocalizer[ResourceConstants.FirmPaymentTransactionTitle].Value;

    var subtitle = Model.ReportType == "Report48h" ? ResourceConstants.Report48HTransactionSubTitle : ResourceConstants.FirmPaymentTransactionSubTitle;
    var controller = Model.ReportType == "Report48h" ? "Contact" : "FirmPaymentTransaction";
    var action = Model.ReportType == "Report48h" ? "CreateEditLobbyingFirm" : "EnterContact";

    var htmlAttributes = new Dictionary<string, object>
    {
        { "class", "form-control" }
    };

    bool isReadOnly = Model.RegistrationFilingId != null && !string.IsNullOrEmpty(Model.RegistrationFilingId.ToString());
    if (isReadOnly)
    {
        htmlAttributes.Add("disabled", "disabled");
    }
}

<h1>@SharedLocalizer[ResourceConstants.FirmPaymentTransactionTitle]</h1>
<h2>@SharedLocalizer[subtitle]</h2>
<div class="p-5 mt-4 d-flex flex-column border border-gray">
    <div class="d-flex flex-column">
        <h2>@SharedLocalizer[ResourceConstants.FirmPaymentTransaction02Title]</h2>
        <p>@SharedLocalizer[ResourceConstants.FirmPaymentTransaction02SubTitle]</p>
    </div>

    @using (Html.BeginForm(action, controller, FormMethod.Post))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.ReportType)
        @Html.HiddenFor(m => m.FilerId)
        @Html.HiddenFor(m => m.FilingId)
        @Html.HiddenFor(m => m.ContactId)
        @Html.HiddenFor(m => m.RegistrationFilingId)
        @Html.HiddenFor(m => m.Id)

        <div class="mb-3 col-lg-4">
            @Html.LabelFor(m => m.Contact.OrganizationName, Localizer[ResourceConstants.FirmPaymentTransaction02NameLabel].Value, new { @class = "form-label" })
            @Html.TextBoxFor(m => m.Contact.OrganizationName, htmlAttributes)
            @Html.ValidationMessageFor(m => m.Contact.OrganizationName, null, new { @class = "text-danger" })
        </div>

        <div class="mb-3 col-lg-4">
            @Html.LabelFor(m => m.Contact!.PhoneNumber, SharedLocalizer[CommonResourceConstants.TelephoneNumber].Value, new { @class = "form-label" })
            <partial name="_ContactNumberInput" model='new ContactNumberInputModel { Name = @SharedLocalizer[CommonResourceConstants.TelephoneNumber].Value, CountryCodeName = "Contact.PhoneNumberCountryCode", CountryCodeValue = Model.Contact?.PhoneNumberCountryCode ?? string.Empty, ContactNumberName = "Contact.PhoneNumber", ContactNumber = Model.Contact?.PhoneNumber ?? string.Empty, HasValidationError = Model.Messages.Validations.ContainsKey("PhoneNumber"), IsDisabled = isReadOnly }' />
            @Html.ValidationMessageFor(m => m.Contact!.PhoneNumber, null, new { @class = "text-danger" })
        </div>

        <hr />

        <h6>@Localizer[ResourceConstants.FirmPaymentTransaction02AddressTitle]</h6>

        <div class="mb-3 col-lg-4">
            @Html.LabelFor(m => m.Contact.Country, Localizer["Common.Country"].Value, new { @class = "form-label" })
            <partial name="~/Views/Shared/_CountryDropdown.cshtml" model='(Name: "Contact.Country", Value: Model.Contact?.Country, ShowError: false, IsDisabled: isReadOnly)' />
            @Html.ValidationMessageFor(m => m.Contact.Country, null, new { @class = "text-danger" })
        </div>

        <div class="mb-3 col-lg-4" id="streetContainer">
            @Html.LabelFor(m => m.Contact.Street, Localizer["Common.Street"].Value, new { @class = "form-label" })
            @Html.TextBoxFor(m => m.Contact.Street, htmlAttributes)
            @Html.ValidationMessageFor(m => m.Contact.Street, null, new { @class = "text-danger" })
        </div>

        <div class="mb-3 col-lg-4" id="street2Container">
            @Html.LabelFor(m => m.Contact.Street2, Localizer["FilerPortal.Contact.CommonFields.Street2"].Value, new { @class = "form-label" })
            @Html.TextBoxFor(m => m.Contact.Street2, htmlAttributes)
            @Html.ValidationMessageFor(m => m.Contact.Street2, null, new { @class = "text-danger" })
        </div>

        <div class="mb-3 col-lg-4" id="cityContainer">
            @Html.LabelFor(m => m.Contact.City, Localizer["Common.City"].Value, new { @class = "form-label" })
            @Html.TextBoxFor(m => m.Contact.City, htmlAttributes)
            @Html.ValidationMessageFor(m => m.Contact.City, null, new { @class = "text-danger" })
        </div>

        <div class="mb-3 col-lg-4">
            @Html.LabelFor(m => m.Contact.State, Localizer["Common.State"].Value, new { @class = "form-label" })
            <partial name="_StatesDropdown" model='(Name: "Contact.State", Value: Model.Contact?.State, ShowError: false, IsDisabled: isReadOnly)' />
            @Html.ValidationMessageFor(m => m.Contact.State, null, new { @class = "text-danger" })

        </div>

        <div class="mb-3 col-lg-4" id="zipCodeContainer">
            @Html.LabelFor(m => m.Contact.ZipCode, Localizer["Common.ZipCode"].Value, new { @class = "form-label" })
            @Html.TextBoxFor(m => m.Contact.ZipCode, htmlAttributes)
            @Html.ValidationMessageFor(m => m.Contact.ZipCode, null, new { @class = "text-danger" })
        </div>

        <hr />

        <div class="mt-4">
            <partial name="_TransactionWorkflowNavigation" model="@("Continue")" />
        </div>
    }
</div>

