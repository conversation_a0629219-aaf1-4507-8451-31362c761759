using System.Collections.ObjectModel;
using System.Linq.Expressions;
using NSubstitute;
using SOS.CalAccess.Data.SmsMessaging;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.SmsMessaging;
using SOS.CalAccess.Services.Common.Queuing;
using SOS.CalAccess.Services.Common.SmsMessaging;
using SOS.CalAccess.Services.Common.SmsMessaging.Model;

namespace SOS.CalAccess.Services.Business.Tests.SmsMessaging;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(SmsMessagingSvc))]
public class SmsMessagingSvcTests
{
    private SmsMessagingSvc _smsMessagingSvc;
    private ISmsMessageRepository _messageRepository;
    private IMessageQueueSvc _messageQueueSvc;
    private SmsMessagingOptions _smsMessagingOptions;
    private const long FilerId = 123456;
    private const string Message = "Test SMS message";
    private IDateTimeSvc _dateTimeSvc;
    private DateTime _dateNow;


    /// <summary>
    /// Setting up objects
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _messageQueueSvc = Substitute.For<IMessageQueueSvc>();
        _messageRepository = Substitute.For<ISmsMessageRepository>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _smsMessagingOptions = new SmsMessagingOptions("1234567890");
        _smsMessagingSvc = new SmsMessagingSvc(_messageQueueSvc, _messageRepository, _dateTimeSvc, _smsMessagingOptions);
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

    }

    /// <summary>
    /// Create, Send and Update SMS message. Throw falilure on exception
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_SmsMessage_ThrowFailureOnException()
    {
        //Arrange
        var smsRecipient = new Collection<CalAccessSmsRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      PhoneNumber = "1234567890"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      PhoneNumber = "1234567891"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      PhoneNumber = "1234567892"
                }
            };

        var request = new SmsMessageRequest(smsRecipient, FilerId, Message);

        _messageRepository.Create(Arg.Any<SmsMessage>()).Returns(args => Task.FromResult((SmsMessage)args[0]));

        //Act
        await _smsMessagingSvc.SendSmsMessage(request);

        // Assert
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.Received().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Create, send and update sms message throws an exception when message passed as null
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_SmsMessage_ThrowMessagePassNull()
    {
        //Arrange
        var smsRecipient = new Collection<CalAccessSmsRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      PhoneNumber = "1234567890"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      PhoneNumber = "1234567891"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      PhoneNumber = "1234567892"
                }
            };
        var request = new SmsMessageRequest(smsRecipient, FilerId, "");

        _messageRepository.Create(Arg.Any<SmsMessage>()).Returns(args => Task.FromResult((SmsMessage)args[0]));

        //Act
        try
        {
            await _smsMessagingSvc.SendSmsMessage(request);
            Assert.Fail("Expected an exception to be thrown.");
        }
        catch (Exception)
        {
        }

        // Assert
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 678902));

    }

    /// <summary>
    /// Create, send and update sms message throws an exception when any phone bumber passed as empty
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task Create_Send_Update_SmsMessage_ThrowPhoneNumberPassNull()
    {
        //Arrange
        var smsRecipient = new Collection<CalAccessSmsRecipient> {
                new() { UserId = 12345,
                      NotificationId = 67890,
                      PhoneNumber = "1234567890"
                },
                new() { UserId = 123456,
                      NotificationId = 678901,
                      PhoneNumber = "1234567891"
                },
                new() { UserId = 123457,
                      NotificationId = 678902,
                      PhoneNumber = ""
                }
            };
        var request = new SmsMessageRequest(smsRecipient, FilerId, Message);

        _messageRepository.Create(Arg.Any<SmsMessage>()).Returns(args => Task.FromResult((SmsMessage)args[0]));

        //Act
        try
        {
            await _smsMessagingSvc.SendSmsMessage(request);
            Assert.Fail("Expected an exception to be thrown.");
        }
        catch (Exception)
        {
        }

        // Assert
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 67890));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 678901));
        await _messageQueueSvc.DidNotReceive().SendJsonMessage(QueueName.SmsRequest, Arg.Is<SmsMessage>(message => message.NotificationMessageId == 678902));

    }

    [Test]
    public async Task UpdateSmsMessageStatus_Should_UpdateMessage_When_MessageExists()
    {
        // Arrange
        var updateRequest = new UpdateSmsRequest
        {
            Id = 1,
            MessagingServiceSId = "service123",
            AccountSid = "account123",
            SId = "sms123",
            Status = "delivered",
            ErrorCode = null,
            ErrorMessage = null,
            DateSent = _dateNow,
            DateUpdated = _dateNow
        };
        var existingMessage = new SmsMessage { Id = 1 };

        _messageRepository.FindById(updateRequest.Id).Returns(existingMessage);

        // Act
        await _smsMessagingSvc.UpdateSmsMessageStatus(updateRequest);

        // Assert
        await _messageRepository.Received(1).Update(Arg.Is<SmsMessage>(
            s => s.MessagingServiceSId == updateRequest.MessagingServiceSId &&
                 s.AccountSId == updateRequest.AccountSid &&
                 s.SId == updateRequest.SId &&
                 s.Status == updateRequest.Status &&
                 s.ErrorCode == updateRequest.ErrorCode &&
                 s.ErrorMessage == updateRequest.ErrorMessage &&
                 s.DateSent == updateRequest.DateSent &&
                 s.DateUpdated == updateRequest.DateUpdated
        ));
    }

    [Test]
    public void UpdateSmsMessageStatus_Should_ThrowKeyNotFoundException_When_MessageNotFound()
    {
        // Arrange
        var updateRequest = new UpdateSmsRequest { Id = 1 };

        _messageRepository.FindById(updateRequest.Id).Returns((SmsMessage)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() =>
            _smsMessagingSvc.UpdateSmsMessageStatus(updateRequest));

        Assert.That(ex.Message, Is.EqualTo($"No Sms message found for ID: {updateRequest.Id}"));
    }

    [Test]
    public void UpdateSmsMessageStatus_Should_ThrowInvalidOperationException_When_RepositoryThrowsException()
    {
        // Arrange
        var updateRequest = new UpdateSmsRequest { Id = 1 };

        _messageRepository.FindById(updateRequest.Id).Returns(Task.FromException<SmsMessage>(new Exception("Database error")));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() =>
            _smsMessagingSvc.UpdateSmsMessageStatus(updateRequest));

        Assert.That(ex.Message, Is.EqualTo("Could not update the Sms Message Status."));
    }

    [Test]
    public async Task UpdateSmsMessageStatus_Should_UpdateErrorMessage_When_DateUpdatedOrSIdIsNull()
    {
        // Arrange
        var updateRequest = new UpdateSmsRequest
        {
            Id = 1,
            DateUpdated = null,
            SId = null,
            ErrorMessage = "Failed delivery"
        };

        var existingMessage = new SmsMessage { Id = 1 };

        _messageRepository.FindById(updateRequest.Id).Returns(existingMessage);

        // Act
        await _smsMessagingSvc.UpdateSmsMessageStatus(updateRequest);

        // Assert
        await _messageRepository.Received(1)
            .UpdateProperty(existingMessage, Arg.Any<Expression<Func<SmsMessage, string>>>(), updateRequest.ErrorMessage);
    }
}
