SET IDENTITY_INSERT [dbo].[NotificationTemplate] ON;
GO

MERGE INTO NotificationTemplate
USING (VALUES 
    (3, 'Registration Accepted', 'Notification to user that Candidate Registration has been accepted', 0, 0, 6, 13, NEWID()),
    (4, 'Attest Registration', 'Notification to user that Candidate Registration Attestation is needed', 0, 1, 6, 13, NEWID()),
    (5, 'Candidate Disclosure Form', 'Notification to user that Candidate disclosure form supplement is submitted', 0, 0, 20, 1, NEWID()),
    (6, 'Candidate Disclosure Submitted', 'Notification to user that Candidate disclosure form has been submitted', 0, 0, 20, 1, NEWID()),
    (7, 'Non Candidate Disclosure Form Attestation', 'Notification to user that Non Candidate disclosure form attestation is needed', 0, 1, 20, 1, NEWID()),
	(8, 'Campaign Committee Registration Requirement', 'Notification to user that Campaign Committee must be established', 0, 0, 22, 1, NEWID()),
	(9, 'Amendment to registration ready for attestation', 'Notification to user that Amendment to Candidate Registration Attestation is needed', 0, 1, 22, 13, NEWID()),
	(10, 'Amendment to registration submitted successfully', 'Notification to user that Amendment to Candidate Registration has been accepted', 0, 0, 22, 13, NEWID()),
    (16, 'Treasurer Acknowledgement', 'Notification to user that SMO Treasurer Acknowledgement is needed', 0, 1, 22, 3, NEWID()),
    (17, 'SMO Registration Accepted', 'Notification to user that SMO Registration has been accepted', 0, 0, 22, 13, NEWID()),
    (18, 'SMO Attest Registration', 'Notification to user that SMO Registration Attestation is needed', 0, 1, 22, 13, NEWID()),
    (19, 'SMO Registration Missing Qualification Date', 'Notification to user that the SMO Registration is missing the qualification date', 0, 0, 22, 17, NEWID()),
    (20, 'Lobbyist Report For Attestation', 'Notification to user that the Lobbyist Report is ready for attestation', 0, 1, 16, 6, NEWID()),
    (21, 'Lobbyist Report For Approval', 'Notification to user that the Lobbyist report is ready for approval', 0, 1, 16, 6, NEWID()),
    (22, 'Lobbyist 72H Report For Approval', 'Notification to user that the Lobbyist 72h report is ready for approval', 0, 1, 17, 6, NEWID()),
    (23, 'Lobbyist 72H Report For Attestation', 'Notification to user that the Lobbyist 72h report is ready for attestation', 0, 1, 17, 6, NEWID()),
    (24, 'SMO Registration Termination', 'Notification to user that SMO Registration has been terminated', 0, 1, 23, 3, NEWID()),
    (25, 'SMO Registration Termination Held For PRD Review', 'Notification to user that SMO Registration is being held for PRD Review', 0, 1, 23, 3, NEWID()),
    (26, 'Lobbyist Registration For Approval', 'Notification to user that Lobbyist Registration is ready for approval', 0, 1, 7, 6, NEWID()),
    (27, 'Lobbyist Registration For Attestation', 'Notification to user that Lobbyist Registration is ready for attestation', 0, 1, 7, 6, NEWID()),
    (28, 'Lobbyist 48H Report For Approval', 'Notification to user that Lobbyist 48h report is ready for approval', 0, 1, 18, 6, NEWID()),
    (29, 'Lobbyist 48H Report For Attestation', 'Notification to user that Lobbyist 48h report is ready for attestation', 0, 1, 18, 6, NEWID()),
    (30, 'Lobbyist Employer Report For Approval', 'Notification to user that Lobbyist Employer report is ready for approval', 0, 1, 19, 5, NEWID()),
    (31, 'SMO Campaign Statement Submitted', 'Notification to user that SMO Campaign Statement report submitted successfully', 0, 0, 21, 3, NEWID()),
    (32, 'SMO Campaign Statement Attestation', 'Notification to user that SMO Campaign Statement Attestation is needed', 0, 1, 21, 3, NEWID()),
    (33, 'SMO Campaign Statement Incomplete', 'Notification to user that SMO Campaign Statement is incomplete', 0, 0, 21, 3, NEWID()),
    (34, 'Lobbyist 72H Amendment Report for Approval', 'Notification to user that Lobbyist 72h amendment report is ready for approval', 0, 1, 17, 6, NEWID()),
    (35, 'Lobbyist 72H Amendment Report for Attestation', 'Notification to user that Lobbyist 72h amendment report is ready for attestation', 0, 1, 17, 6, NEWID()),
    (36, 'SMO Campaign Statement Amendment Attesation', 'Notification to user that SMO Campaign Statement amendment report is ready for attestation', 0, 1, 21, 3, NEWID()),
    (37, 'SMO Campaign Statement Amendment Submitted', 'Notification to user that SMO Campaign Statement amendment report submitted successfully', 0, 0, 21, 3, NEWID()),
    (38, 'SMO Complete Attestation', 'Notification to user that SMO Registration Attestation is needed for termination', 0, 1, 23, 3, NEWID()),
    (39, 'SMO Registration Terminated', 'Notification to user that SMO Registration has been terminated', 0, 0, 23, 3, NEWID()),
    (40, 'Lobbyist Registration Amendment For Approval', 'Notification to user that Lobbyist Registration Amendment is ready for approval', 0, 1, 7, 6, NEWID()),
    (41, 'Lobbyist Registration Amendment For Attestation', 'Notification to user that Lobbyist Registration Amendment is ready for attestation', 0, 1, 7, 6, NEWID()),
    (42, 'Lobbyist Employer Report For Attestation', 'Notification to user that Lobbyist Employer Report is ready for attestation', 0, 1, 19, 5, NEWID()),
    (43, 'Lobbyist Terminate Registration For Approval', 'Notification to Filer users that Lobbyist Terminate Registration report is ready for approval', 0, 0, 25, 6, NEWID()),
    (44, 'Lobbyist Terminate Registration For Approval', 'Notification to Linked Firm/ Employer users that Lobbyist Terminate Registration report is ready for approval', 0, 1, 25, 6, NEWID()),
    (45, 'Lobbyist Terminate Registration For Attestation', 'Notification to users that Lobbyist Terminate Registration report is ready for attestation', 0, 1, 25, 6, NEWID()),
    (50, 'Account Linkage Request for Filer', 'Notification to filer that a user has sent a linkage request.', 0, 1, 3, 13, NEWID()),
    (51, 'Account Linkage Request for Officer', 'Notification to officer that a filer has sent a linkage request.', 0, 1, 3, 13, NEWID()),
    (52, 'Account Linkage Request for Email', 'Notification to an email address that a filer has sent a linkage request.', 0, 1, 3, 13, NEWID()),
    (53, 'Account Linkage Accepted for Linked User', 'Notification to linked user that linkage request has been accepted.', 0, 0, 3, 13, NEWID()),
    (54, 'Account Linkage Accepted for Filer Users', 'Notification to filer users that linkage request has been accepted.', 0, 0, 3, 13, NEWID()),
    (55, 'Account Linkage Rejected for Linked User', 'Notification to linked user that linkage request has been rejected.', 0, 0, 3, 13, NEWID()),
    (56, 'Account Linkage Rejected for Filer Users', 'Notification to filer users that linkage request has been rejected.', 0, 0, 3, 13, NEWID()),
    (57, 'Account Linkage Terminated for Linked User', 'Notification to linked user that linkage has been Terminated.', 0, 0, 3, 13, NEWID()),
    (58, 'Account Linkage Terminated for Filer Users', 'Notification to filer users that linkage has been Terminated.', 0, 0, 3, 13, NEWID()),
    (59, 'Account Linkage Terminated for officers', 'Notification to removed officers to inform them that they have been de-linked from the filer.', 0, 0, 24, 13, NEWID()),
    (60, 'Account Linkage Terminated for Filer Users', 'Notification to all the filer users to notify them that the officers have been de-linked from the filer.', 0, 0, 24, 13, NEWID()),
    (61, 'Access the Pending Registration/Amendment to complete attestation', 'Notification to the filer user that the registration/amendment has been changed to draft.', 0, 0, 24, 2, NEWID()),
    (81, 'Lobbyist Registration Withdrawal Submitted for Filer User','Notification to inform that withdrawal submitted successfully',0,0,27,4,NEWID()),
	(82, 'Lobbyist Registration Withdrawal Pending','Notification to inform withdrawal Pending',0,1,27,6,NEWID()),
	(83, 'Lobbyist Registration Withdrawal Submitted for Linked Lobbyist Employer','Notification to inform that withdrawal registration is ready for attestation',0,1,27,4,NEWID()),
    (84, 'Lobbyist Renew Registration for Filer User ','Notification to inform Registration renewed successfully',0,0,26,6,NEWID()),
    (85, 'Lobbyist Renew Registration for Linked Filer Entities ',' Notification to inform Registration renewed successfully. Please take proper actions.',0,1,26,6,NEWID()),
    (86, 'Lobbyist Renew Registration for Filer User ','Notification to inform Registration renewed is ready for Attestation',0,1,26,6,NEWID()), 
    (87, 'Lobbyist Registration Withdrawal Submitted','Notification to inform that withdrawal submitted successfully',0,1,27,6,NEWID()),
	(88, 'Registration Termination for Filer User','Notification to inform that termination is ready for attestation',0,1,25,20,NEWID()),
    (89, 'Lobbyist 48H Amendment Report For Attestation','Notification to user that Lobbyist 48h amendment report is ready for attestation',0,1,18,7,NEWID()),
    (90, 'Lobbyist 48H Amendement Report For Approval','Notification to user that the Lobbyist 48h amendment report is ready for approval',0,0,18,7,NEWID()),
    (91, 'Disclosure/ Amendment for Filer User','Notification to inform that Activity report ready for attestation',0,1,28,13,NEWID()),
    (92, 'Disclosure/ Amendment for Filer User','Notification to inform that Amendment report submitted successfully',0,0,28,13,NEWID()),
	(93, 'Disclosure/ Amendment for Filer User','Notification to inform that Amendment report submitted successfully',0,0,28,13,NEWID()),
	(94, 'Disclosure/ Amendment for Filer User','Notification to inform that A campaign committee must be established',0,0,28,13,NEWID())
) AS source (Id, Name, Description, IsPriorityMessage, IsActionRequired, NotificationTypeId, FilerTypeId, AuditableResourceTag)
ON NotificationTemplate.Id = source.Id
WHEN MATCHED THEN 
    UPDATE SET
        Name = source.Name,
        Description = source.Description,
        IsPriorityMessage = source.IsPriorityMessage,
        IsActionRequired = source.IsActionRequired,
        NotificationTypeId = source.NotificationTypeId,
        FilerTypeId = source.FilerTypeId
WHEN NOT MATCHED THEN 
    INSERT (Id, Name, Description, IsPriorityMessage, IsActionRequired, NotificationTypeId, FilerTypeId, AuditableResourceTag)
    VALUES (source.Id, source.Name, source.Description, source.IsPriorityMessage, source.IsActionRequired, source.NotificationTypeId,
            source.FilerTypeId, NEWID());
GO

SET IDENTITY_INSERT [dbo].[NotificationTemplate] OFF;
GO
