using System.Linq.Expressions;
using Moq;
using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Filings;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Filings;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class Form470SvcTests
{
    private IDecisionsSvc _decisionsSvcMock;
    private IRegistrationRepository _registrationRepositoryMock;
    private IAttestationRepository _attestationRepositoryMock;
    private IFilingRelatedFilerRepository _filingRelatedFilerRepositoryMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private IFilingRepository _filingRepository;
    private IDateTimeSvc _dateTimeSvcMock;
    private Form470SvcDependencies _dependenices;
    private Form470Svc _service;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _attestationRepositoryMock = Substitute.For<IAttestationRepository>();
        _filingRelatedFilerRepositoryMock = Substitute.For<IFilingRelatedFilerRepository>();
        _filingRepository = Substitute.For<IFilingRepository>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _dependenices = new Form470SvcDependencies
        (
            _decisionsSvcMock,
            _authorizationSvcMock,
            _filerSvcMock,
            _notificationSvcMock,
            _userMaintenanceSvcMock,
            _dateTimeSvcMock,
            _registrationRepositoryMock,
            _filingRepository
        );

        _service = new Form470Svc(_dependenices, _attestationRepositoryMock, _filingRelatedFilerRepositoryMock);
    }

    #region GetCandidateIntentionStatementWithElectionByFilerId

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsMappedDto_WhenDataExists()
    {
        // Arrange
        var filerId = 1L;
        var candidateIntention = new CandidateIntentionStatement
        {
            StatusId = 1,
            Id = 100,
            FilerId = filerId,
            Name = "Sri S",
            Email = "<EMAIL>",
            ElectionOfficeSought = "Governor",
            ElectionJurisdiction = "CA",
            ElectionDistrictNumber = "12",
            ElectionRace = new()
            {
                Election = new()
                {
                    ElectionDate = new DateTime(2024, 11, 5),
                    Name = "Sri election"
                }
            },
            AddressList = new()
            {
                Addresses = new()
                {
                    new()
                    {
                        Purpose = "Candidate",
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Type = "Home"
                    }
                }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                {
                    new() { Type = "Home", Number = "555-1234" },
                    new() { Type = "Fax", Number = "555-5678" }
                }
            }
        };

        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
            .Returns(candidateIntention);

        // Act
        var result = await _service.GetCandidateIntentionStatementWithElectionByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.RegistrationId, Is.EqualTo(candidateIntention.Id));
            Assert.That(result.FilerId, Is.EqualTo(candidateIntention.FilerId));
            Assert.That(result.Name, Is.EqualTo(candidateIntention.Name));
            Assert.That(result.EmailAddress, Is.EqualTo(candidateIntention.Email));
            Assert.That(result.OfficeSought, Is.EqualTo(candidateIntention.ElectionOfficeSought));
            Assert.That(result.ElectionDate, Is.EqualTo(candidateIntention.ElectionRace.Election.ElectionDate));
            Assert.That(result.PhoneNumber, Is.EqualTo("555-1234"));
            Assert.That(result.FaxNumber, Is.EqualTo("555-5678"));
            Assert.That(result.AddressDto?.City, Is.EqualTo("Sacramento"));
        });
    }

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsNull_WhenDataIsMissing()
    {
        // Arrange
        var filerId = 2L;
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
            .Returns((CandidateIntentionStatement?)null);

        // Act
        var result = await _service.GetCandidateIntentionStatementWithElectionByFilerId(filerId);

        // Assert
        Assert.That(result.FilerId, Is.Null);
    }

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsMappedDto_WithNullAddress_WhenNoAddressAndPhoneNumbersList()
    {
        // Arrange
        var filerId = 1L;
        var candidateIntention = new CandidateIntentionStatement
        {
            StatusId = 1,
            Id = 100,
            FilerId = filerId,
            Name = "Sri S",
            Email = "<EMAIL>",
            ElectionOfficeSought = "Governor",
            ElectionJurisdiction = "CA",
            ElectionDistrictNumber = "12",
            ElectionRace = new()
            {
                Election = new()
                {
                    ElectionDate = new DateTime(2024, 11, 5),
                    Name = "Sri election"
                }
            },
            AddressList = null,
            PhoneNumberList = null
        };

        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
            .Returns(candidateIntention);

        // Act
        var result = await _service.GetCandidateIntentionStatementWithElectionByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.RegistrationId, Is.EqualTo(candidateIntention.Id));
            Assert.That(result.FilerId, Is.EqualTo(candidateIntention.FilerId));
            Assert.That(result.Name, Is.EqualTo(candidateIntention.Name));
            Assert.That(result.EmailAddress, Is.EqualTo(candidateIntention.Email));
            Assert.That(result.OfficeSought, Is.EqualTo(candidateIntention.ElectionOfficeSought));
            Assert.That(result.ElectionDate, Is.EqualTo(candidateIntention.ElectionRace.Election.ElectionDate));
            Assert.That(result.PhoneNumber, Is.Null);
            Assert.That(result.FaxNumber, Is.Null);
            Assert.That(result.AddressDto, Is.Null);
        });
    }

    #endregion

    #region GetForm470ById

    [Test]
    public async Task GetForm470ById_ReturnsFiling_WhenFound()
    {
        // Arrange
        long id = 5;
        var expectedFiling = new OfficeHolderCandidateShortForm
        {
            Id = id,
            FilerId = 123,
            FilingPeriodId = 456,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
            StatusId = FilingStatus.Pending.Id
        };

        _filingRepository.GetForm470ById(id).Returns(expectedFiling);

        // Act
        var result = await _service.GetForm470ById(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.Id, Is.EqualTo(id));
            Assert.That(result?.FilerId, Is.EqualTo(expectedFiling.FilerId));
            Assert.That(result?.FilingPeriodId, Is.EqualTo(expectedFiling.FilingPeriodId));
            Assert.That(result?.FilingTypeId, Is.EqualTo(expectedFiling.FilingTypeId));
            Assert.That(result?.StatusId, Is.EqualTo(expectedFiling.StatusId));
        });
    }

    [Test]
    public async Task GetForm470ById_ReturnsNull_WhenNotFound()
    {
        // Arrange
        long id = 999;
        _filingRepository.GetForm470ById(id).Returns((OfficeHolderCandidateShortForm?)null);

        // Act
        var result = await _service.GetForm470ById(id);

        // Assert
        Assert.That(result, Is.Null);
    }

    #endregion

    #region CreateOfficeHolderCandidateShortFormFiling

    [Test]
    public async Task CreateOfficeHolderCandidateShortFormFiling_ReturnsFiling_WhenValidInput()
    {
        // Arrange
        long filerId = 10;
        long filingPeriodId = 20;

        var expectedFiling = new Filing
        {
            Id = 1,
            FilerId = filerId,
            FilingPeriodId = filingPeriodId,
            Version = 0,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id
        };

        _filingRepository.Create(Arg.Is<Filing>(f =>
            f.FilerId == filerId &&
            f.FilingPeriodId == filingPeriodId &&
            f.Version == 0 &&
            f.StatusId == FilingStatus.Draft.Id &&
            f.FilingTypeId == FilingType.OfficeHolderCandidateShortForm.Id
        )).Returns(expectedFiling);

        // Act
        var result = await _service.CreateOfficeHolderCandidateShortFormFiling(filerId, filingPeriodId);

        // Assert
        Assert.That(result, Is.EqualTo(1));
    }

    #endregion

    #region GetForm470Overview

    [Test]
    public async Task GetForm470Overview_ReturnsResponse_WhenFilingAndRegistrationExist()
    {
        // Arrange
        long filingId = 1;
        long filerId = 10;

        var filing = new OfficeHolderCandidateShortForm
        {
            Id = filingId,
            FilerId = filerId,
            FilingPeriodId = 100,
            StatusId = FilingStatus.Pending.Id,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
            StartDate = _dateNow,
            EndDate = _dateNow.AddDays(1)
        };

        var registration = new CandidateIntentionStatement
        {
            Id = 2,
            FilerId = filerId,
            Name = "Sri S",
            Email = "<EMAIL>",
            ElectionOfficeSought = "Mayor",
            ElectionJurisdiction = "City",
            ElectionDistrictNumber = "1",
            StatusId = 1,
        };

        _filingRepository.GetForm470ById(filingId).Returns(filing);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId).Returns(registration);

        // Act
        var result = await _service.GetForm470Overview(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.CandidateIntentionStatement470, Is.Not.Null);
            Assert.That(result.Form470Filing, Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.Form470Filing.Id, Is.EqualTo(filingId));
            Assert.That(result.CandidateIntentionStatement470?.FilerId, Is.EqualTo(filerId));
        });
    }

    [Test]
    public async Task GetForm470Overview_ReturnsNull_WhenFilingNotFound()
    {
        // Arrange
        _filingRepository.GetForm470ById(Arg.Any<long>()).Returns((OfficeHolderCandidateShortForm?)null);

        // Act
        var result = await _service.GetForm470Overview(1);

        // Assert
        Assert.That(result.Form470Filing, Is.Null);
    }

    [Test]
    public async Task GetForm470Overview_ReturnsNull_WhenCandidateIntentionStatementNotFound()
    {
        // Arrange
        var filing = new OfficeHolderCandidateShortForm { Id = 1, FilerId = 10, StatusId = 1 };
        _filingRepository.GetForm470ById(1).Returns(filing);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(10)
            .Returns((CandidateIntentionStatement?)null);

        // Act
        var result = await _service.GetForm470Overview(1);

        // Assert
        Assert.That(result.Form470Filing, Is.Null);
    }

    #endregion GetForm470Overview

    #region FindPrimarilyFormedCommitteeByIdOrName

    [Test]
    public async Task FindPrimarilyFormedCommitteeByIdOrName_ReturnsMappedDtos()
    {
        // Arrange
        var query = "Test";
        var entities = new[]
        {
            new PrimarilyFormedCommittee{
                Id = 1,
                Name = "null case",
                StatusId = 2,
                CommitteeTypeId = CommitteeType.PrimarilyFormed.Id,
                JurisdictionCounty = "County",
                JurisdictionActive = "Jurisdiction",
                FinancialInstitutionName = "Bank",
                FinancialInstitutionPhone = "************",
                FinancialInstitutionAccountNumber = "*********",
                Email = "test",
                AddressList = null,
                Filer = null,
            },
            new PrimarilyFormedCommittee{
                Id = 2,
                Name = "perfect case",
                StatusId = 2,
                CommitteeTypeId = CommitteeType.PrimarilyFormed.Id,
                JurisdictionCounty = "County",
                JurisdictionActive = "Jurisdiction",
                FinancialInstitutionName = "Bank",
                FinancialInstitutionPhone = "************",
                FinancialInstitutionAccountNumber = "*********",
                Email = "test",
                AddressList = new AddressList
                {
                    Addresses = new List<SOS.CalAccess.Models.Common.Address>
                    {
                        new()
                        {
                            Street = "123 Main St",
                            City = "Sacramento",
                            State = "CA",
                            Zip = "95814",
                            Country = "USA",
                            Purpose = "Business",
                            Type = "Office"
                        }
                    }
                },
                Filer = new Filer
                {
                    FilerStatusId = FilerStatus.Active.Id,
                    FilerTypeId = FilerType.Candidate.Id,
                    Users = new List<FilerUser>
                    {
                        new()
                        {
                            FilerRole = FilerRole.RecipientCommittee_Treasurer,
                            User = new User
                            {
                                FirstName = "Test",
                                LastName = "Treasurer",
                                EmailAddress = "<EMAIL>",
                                EntraOid = "TestOid"
                            }
                        }
                    }
                }
            },
             new PrimarilyFormedCommittee{
                Id = 3,
                Name = "Treasurer bad case",
                StatusId = 2,
                CommitteeTypeId = CommitteeType.PrimarilyFormed.Id,
                JurisdictionCounty = "County",
                JurisdictionActive = "Jurisdiction",
                FinancialInstitutionName = "Bank",
                FinancialInstitutionPhone = "************",
                FinancialInstitutionAccountNumber = "*********",
                Email = "test",
                AddressList = new AddressList
                {
                    Addresses = new List<SOS.CalAccess.Models.Common.Address>
                    {
                        new()
                        {
                            Street = "123 Main St",
                            City = "Sacramento",
                            State = "CA",
                            Zip = "95814",
                            Country = "USA",
                            Purpose = "Business",
                            Type = "Office"
                        }
                    }
                },
                Filer = new Filer
                {
                    FilerStatusId = FilerStatus.Active.Id,
                    FilerTypeId = FilerType.Candidate.Id,
                    Users = new List<FilerUser>
                    {
                        new()
                        {
                            FilerRole = FilerRole.RecipientCommittee_Treasurer,
                            User = new User
                            {
                                FirstName = "",
                                LastName = "",
                                EmailAddress = "",
                                EntraOid = ""
                            }
                        }
                    }
                }
            },
        };
        _dependenices.RegistrationRepository.FindPrimarilyFormedCommitteeByIdOrName(query).Returns(entities);

        // Act
        var result = (await _service.FindPrimarilyFormedCommitteeByIdOrName(query)).ToList();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Has.Count.EqualTo(3));

            var first = result[0];
            Assert.That(first.Id, Is.EqualTo(1));
            Assert.That(first.CommitteeName, Is.EqualTo("null case"));
            Assert.That(first.Address, Is.Empty);
            Assert.That(first.Treasurer, Is.Null.Or.Empty);

            var second = result[1];
            var address2 = second.Address.Single();
            Assert.That(address2.Street, Is.EqualTo("123 Main St"));
            Assert.That(address2.City, Is.EqualTo("Sacramento"));
            Assert.That(address2.State, Is.EqualTo("CA"));
            Assert.That(address2.Zip, Is.EqualTo("95814"));
            Assert.That(address2.Country, Is.EqualTo("USA"));
            Assert.That(address2.Purpose, Is.EqualTo("Business"));
            Assert.That(address2.Type, Is.EqualTo("Office"));
            var filerUser2 = second.Treasurer;
            Assert.That(filerUser2.FirstName, Is.EqualTo("Test"));
            Assert.That(filerUser2.LastName, Is.EqualTo("Treasurer"));
            Assert.That(filerUser2.EmailAddress, Is.EqualTo("<EMAIL>"));

            var third = result[2];
            var filerUser3 = third.Treasurer;
            Assert.That(filerUser3.FirstName, Is.Null.Or.Empty);
            Assert.That(filerUser3.LastName, Is.Null.Or.Empty);
            Assert.That(filerUser3.EmailAddress, Is.Null.Or.Empty);
        });
    }

    #endregion

    #region CreateFilingRelatedFiler

    [Test]
    public async Task CreateFilingRelatedFiler_ReturnsCreatedEntity_WhenValidInput()
    {
        // Arrange
        long filingId = 100;
        long filerId = 200;

        var expected = new FilingRelatedFiler
        {
            FilingId = filingId,
            FilerId = filerId,
            Active = true
        };

        _filingRelatedFilerRepositoryMock.Create(Arg.Is<FilingRelatedFiler>(f =>
            f.FilingId == filingId &&
            f.FilerId == filerId &&
            f.Active == true
        )).Returns(expected);

        // Act
        var result = await _service.CreateFilingRelatedFiler(filingId, filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilingId, Is.EqualTo(filingId));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.Active, Is.True);
        });
    }

    #endregion

    #region CancelFilingRelatedFiler

    [Test]
    public async Task CancelFilingRelatedFiler_UpdatesActiveToFalse_WhenEntityIsActive()
    {
        // Arrange
        long id = 1;
        var filingRelatedFiler = new FilingRelatedFiler
        {
            Id = id,
            FilingId = 100,
            FilerId = 200,
            Active = true
        };

        _filingRelatedFilerRepositoryMock.FindById(id).Returns(filingRelatedFiler);

        // Act
        await _service.CancelFilingRelatedFiler(id);

        // Assert
        await _filingRelatedFilerRepositoryMock.Received(1)
            .UpdateProperty(filingRelatedFiler, Arg.Any<Expression<Func<FilingRelatedFiler, bool>>>(), false);
    }

    [Test]
    public void CancelFilingRelatedFiler_ThrowsKeyNotFoundException_WhenEntityNotFound()
    {
        // Arrange
        long id = 999;
        _filingRelatedFilerRepositoryMock.FindById(id).Returns((FilingRelatedFiler?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.CancelFilingRelatedFiler(id));
        Assert.That(ex!.Message, Does.Contain("Filing Related filer with ID 999 not found."));
    }

    [Test]
    public void CancelFilingRelatedFiler_ThrowsInvalidOperationException_WhenAlreadyInactive()
    {
        // Arrange
        long id = 2;
        var filingRelatedFiler = new FilingRelatedFiler
        {
            Id = id,
            FilingId = 100,
            FilerId = 200,
            Active = false
        };

        _filingRelatedFilerRepositoryMock.FindById(id).Returns(filingRelatedFiler);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.CancelFilingRelatedFiler(id));
        Assert.That(ex!.Message, Does.Contain("Cannot cancel a filer related filer that is not in 'Active' status."));
    }

    #endregion CancelFilingRelatedFiler

    #region GetFilingRelatedFiler

    [Test]
    public async Task GetFilingRelatedFiler_ReturnsCommittees_WhenActiveRelatedFilersExist()
    {
        // Arrange
        long filingId = 1;

        var activeFilerIds = new[] { 101L, 102L };

        var filings = new List<Filing>
        {
            new() {
                Id = filingId,
                StatusId = FilingStatus.Pending.Id,
                FilingRelatedFilers = new List<FilingRelatedFiler>
                {
                    new() { FilerId = 101, Active = true, Filer = new Filer{ FilerStatusId = FilerStatus.Active.Id, FilerTypeId = FilerType.Candidate.Id } },
                    new() { FilerId = 102, Active = true, Filer = new Filer{ FilerStatusId = FilerStatus.Active.Id, FilerTypeId = FilerType.Candidate.Id } },
                    new() { FilerId = 103, Active = false }, // should be ignored
                }
            }
        };

        var committee1 = new PrimarilyFormedCommittee
        {
            StatusId = RegistrationStatus.Submitted.Id,
            Id = 10,
            FilerId = 101,
            Name = "Committee A",
            Email = "test.com",
            JurisdictionActive = "true",
            JurisdictionCounty = "country",
            FinancialInstitutionAccountNumber = "1",
            FinancialInstitutionName = "New name",
            FinancialInstitutionPhone = "11",
            Filer = new Filer
            {
                Users = new List<FilerUser>
            {
                new()
                {
                    FilerRole = FilerRole.RecipientCommittee_Treasurer,
                    User = new User
                    {
                        FirstName = "Test",
                        LastName = "Treasurer",
                        EmailAddress = "<EMAIL>",
                        EntraOid = "TestOid"
                    }
                }
            }
            },
            AddressList = new AddressList
            {
                Addresses = new List<SOS.CalAccess.Models.Common.Address>
            {
                new()
                {
                    Street = "123 Main",
                    City = "City",
                    State = "ST",
                    Zip = "12345",
                    Country = "USA",
                    Type = "",
                    Purpose = "Candidate"
                }
            }
            }
        };

        var committee2 = new PrimarilyFormedCommittee
        {
            StatusId = RegistrationStatus.Submitted.Id,
            Id = 11,
            FilerId = 102,
            Name = "Committee B",
            Filer = new Filer(),
            Email = "test.com",
            JurisdictionActive = "true",
            JurisdictionCounty = "country",
            FinancialInstitutionAccountNumber = "1",
            FinancialInstitutionName = "New name",
            FinancialInstitutionPhone = "11",
        };

        _filingRepository.GetCandidateCommitteesByFilingId(filingId).Returns(filings);
        _dependenices.RegistrationRepository.GetCommitteeByFilerId(101).Returns(committee1);
        _dependenices.RegistrationRepository.GetCommitteeByFilerId(102).Returns(committee2);

        // Act
        var result = await _service.GetFilingRelatedFiler(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(2));

        Assert.Multiple(() =>
        {
            Assert.That(result[0].Id, Is.EqualTo(committee1.Id));
            Assert.That(result[1].Id, Is.EqualTo(committee2.Id));
        });
    }

    [Test]
    public async Task GetFilingRelatedFiler_ReturnsEmptyList_WhenNoActiveFilingRelatedFilers()
    {
        // Arrange
        long filingId = 1;
        var filing = CreateFiling(filingId, activeFiler: false); // No active related filers

        _filingRepository.GetCandidateCommitteesByFilingId(filingId)
            .Returns(new List<Filing> { filing });

        // Act
        var result = await _service.GetFilingRelatedFiler(filingId);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetFilingRelatedFiler_SkipsNullCandidateIntentionStatement()
    {
        // Arrange
        long filingId = 1;
        var filing = CreateFiling(filingId, activeFiler: true);

        _filingRepository.GetCandidateCommitteesByFilingId(filingId)
            .Returns(new List<Filing> { filing });

        // Simulate null CIS
        _dependenices.RegistrationRepository.GetCisByFilerId(Arg.Any<long>())
            .Returns((CandidateIntentionStatement?)null);

        // Act
        var result = await _service.GetFilingRelatedFiler(filingId);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetFilingRelatedFiler_ReturnsMultipleResults_WhenMultipleActiveFilers()
    {
        // Arrange
        long filingId = 1;
        var filing = CreateFiling(filingId, activeFiler: true, numberOfFilers: 2);

        _filingRepository.GetCandidateCommitteesByFilingId(filingId)
            .Returns(new List<Filing> { filing });

        _dependenices.RegistrationRepository.GetCommitteeByFilerId(Arg.Any<long>())
            .Returns(x => CreateCommittee((long)x[0]));

        // Act
        var result = await _service.GetFilingRelatedFiler(filingId);

        // Assert
        Assert.That(result, Has.Count.EqualTo(2));
    }

    [Test]
    public async Task GetFilingRelatedFiler_TreasurerAndAddressAreMappedCorrectly()
    {
        // Arrange
        long filingId = 1;
        long filerId = 100;

        var filing = new Filing
        {
            StatusId = FilingStatus.Pending.Id,
            FilingRelatedFilers = new List<FilingRelatedFiler>
        {
            new() {
                Active = true,
                FilerId = filerId,
                Filer = new Filer
                {
                    FilerStatusId = FilerStatus.Active.Id,
                    FilerTypeId = FilerType.Candidate.Id
                }
            }
        }
        };

        _filingRepository.GetCandidateCommitteesByFilingId(filingId)
            .Returns(new List<Filing> { filing });

        var committees = new PrimarilyFormedCommittee
        {
            StatusId = FilingStatus.Pending.Id,
            Email = "test.com",
            JurisdictionActive = "true",
            JurisdictionCounty = "country",
            FinancialInstitutionAccountNumber = "1",
            FinancialInstitutionName = "New name",
            FinancialInstitutionPhone = "11",
            Id = 123,
            Name = "Test Committee",
            Filer = new Filer
            {
                Users = new List<FilerUser>
                {
                    new() {
                        User = new User
                        {
                            FirstName = "Sri",
                            LastName = "s",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            },
            AddressList = new AddressList
            {
                Addresses = new List<SOS.CalAccess.Models.Common.Address>
                {
                    new() {
                        Purpose = "Candidate",
                        Street = "123 Main St",
                        City = "Testville",
                        State = "CA",
                        Zip = "90001",
                        Country = "USA",
                        Type = "Home"
                    }
                }
            }
        };

        _dependenices.RegistrationRepository.GetCommitteeByFilerId(filerId).Returns(committees);

        // Act
        var result = await _service.GetFilingRelatedFiler(filingId);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1), "There should be exactly one Form470Committee returned.");

        var committee = result.First();

        // Treasurer assertions
        Assert.That(committee.Treasurer, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(committee.Treasurer.FirstName, Is.EqualTo("Sri"));
            Assert.That(committee.Treasurer.LastName, Is.EqualTo("s"), "Treasurer's last name mismatch.");
        });
        Assert.Multiple(() =>
        {
            Assert.That(committee.Treasurer.EmailAddress, Is.EqualTo("<EMAIL>"));

            // CommitteeAddress assertions
            Assert.That(committee.CommitteeAddress, Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(committee.CommitteeAddress.Street, Is.EqualTo("123 Main St"));
            Assert.That(committee.CommitteeAddress.City, Is.EqualTo("Testville"));
            Assert.That(committee.CommitteeAddress.State, Is.EqualTo("CA"));
            Assert.That(committee.CommitteeAddress.Zip, Is.EqualTo("90001"));
        });
        Assert.Multiple(() =>
        {
            Assert.That(committee.CommitteeAddress.Country, Is.EqualTo("USA"));
            Assert.That(committee.CommitteeAddress.Type, Is.EqualTo("Home"));
            Assert.That(committee.CommitteeAddress.Purpose, Is.EqualTo("Candidate"));
        });
    }

    private static Filing CreateFiling(long filingId, bool activeFiler, int numberOfFilers = 1)
    {
        var relatedFilers = new List<FilingRelatedFiler>();

        for (int i = 0; i < numberOfFilers; i++)
        {
            relatedFilers.Add(new FilingRelatedFiler
            {
                FilerId = 100 + i,
                Active = activeFiler,
                Filer = new Filer
                {
                    Id = 100 + i,
                    FilerStatusId = FilerStatus.Active.Id,
                    FilerTypeId = FilerType.Candidate.Id
                }
            });
        }

        return new Filing
        {
            StatusId = FilingStatus.Pending.Id,
            Id = filingId,
            FilingRelatedFilers = relatedFilers
        };
    }

    private static Committee CreateCommittee(long filerId)
    {
        return new PrimarilyFormedCommittee
        {
            StatusId = RegistrationStatus.Pending.Id,
            Id = filerId + 1,
            Email = "test.com",
            JurisdictionActive = "true",
            JurisdictionCounty = "country",
            FinancialInstitutionAccountNumber = "1",
            FinancialInstitutionName = "New name",
            FinancialInstitutionPhone = "11",
            FilerId = filerId,
            Name = $"Candidate {filerId}",
            AddressList = new AddressList
            {
                Addresses = new List<SOS.CalAccess.Models.Common.Address>
            {
                new() {
                    Street = "123 Test St",
                    City = "Testville",
                    State = "CA",
                    Zip = "90000",
                    Purpose = "Candidate",
                    Type = "Business",
                    Country ="USA"
                }
            }
            },
            Filer = new Filer
            {
                Users = new List<FilerUser>
            {
                new()
                {
                    FilerRole = FilerRole.RecipientCommittee_Treasurer,
                    User = new User
                    {
                        FirstName = "Test",
                        LastName = "Treasurer",
                        EmailAddress = "<EMAIL>",
                        EntraOid = "TestOid"
                    }
                }
            }
            }
        };
    }

    #endregion

    [Test]
    public async Task UpdateOfficeHolderCandidateShortFormFiling_ReturnsUpdatedFiling_WhenValidInput()
    {
        // Arrange
        var request = new Form470FilingRequest
        {
            FilingId = 10,
            FilingPeriodId = 30
        };

        var existingFiling = new OfficeHolderCandidateShortForm
        {
            Id = request.FilingId,
            FilerId = 15,
            FilingPeriodId = 25,
            Version = 1,
            StatusId = FilingStatus.Draft.Id,
            OriginalId = 100,
            ParentId = 200,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id
        };

        var updatedFiling = new OfficeHolderCandidateShortForm
        {
            Id = request.FilingId,
            FilingPeriodId = request.FilingPeriodId,
            Version = existingFiling.Version,
            StatusId = existingFiling.StatusId,
            OriginalId = existingFiling.OriginalId,
            ParentId = existingFiling.ParentId,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id
        };

        // Mock repository behavior
        _filingRepository.GetForm470ById(Arg.Is<long>(id => id == request.FilingId))
            .Returns(existingFiling);

        _filingRepository.Update(Arg.Is<Filing>(f =>
            f.Id == request.FilingId &&
            f.FilingPeriodId == request.FilingPeriodId &&
            f.Version == existingFiling.Version &&
            f.StatusId == existingFiling.StatusId &&
            f.FilingTypeId == FilingType.OfficeHolderCandidateShortForm.Id
        )).Returns(updatedFiling);

        // Act
        try
        {
            await _service.UpdateOfficeHolderCandidateShortFormFiling(request);
        }
        catch (Exception e)
        {
            Assert.Fail(e.Message);
        }

        // Assert
        Assert.Pass();
    }

    [Test]
    public async Task UpdateOfficeHolderCandidateShortFormFiling_Throws_WhenNoExistingFiling()
    {
        // Arrange
        var request = new Form470FilingRequest
        {
            FilingId = 10,
            FilingPeriodId = 30
        };
        // Act
        try
        {
            await _service.UpdateOfficeHolderCandidateShortFormFiling(request);
        }
        catch (Exception ex)
        {
            // Assert
            Assert.That(ex, Is.InstanceOf<Exception>());
            Assert.Pass();
        }
        Assert.Fail();
    }
    [Test]
    public async Task GetFilerIdOfLatestAccepted501_FilerFound_ShouldReturnId()
    {
        // Arrange
        var user = new BasicUserDto(1, "<EMAIL>", "Test", "Test");
        var filer = new Filer
        {
            Id = 100,
        };
        _dependenices.UserMaintenanceSvc.GetCurrentUser().Returns(user);
        _dependenices.RegistrationRepository.GetFilerOfLatestAcceptedCisRegistration(Arg.Any<long>()).Returns(filer);
        // Act
        var result = await _service.GetFilerIdOfLatestAccepted501();
        // Assert
        Assert.That(result, Is.EqualTo(100));
    }
    [Test]
    public async Task GetFilerIdOfLatestAccepted501_NoFiler_ShouldReturn0()
    {
        // Arrange
        var user = new BasicUserDto(1, "<EMAIL>", "Test", "Test");
        _dependenices.UserMaintenanceSvc.GetCurrentUser().Returns(user);
        // Act
        var result = await _service.GetFilerIdOfLatestAccepted501();
        // Assert
        Assert.That(result, Is.EqualTo(0));
    }

    #region Attestation

    [Test]
    public async Task AttestOfficeholderAndCandidateCampaignStatement_AlreadySubmitted_Return()
    {

        // Arrange
        var registrationId = 1982;
        var filingId = 1;
        var filer = new FilerUserDto
        {
            Id = 1,
            FilerId = 10,
            UserId = 1,
            FilerRole = FilerRole.CandidateRegistration_Candidate,
        };
        var candidacy = new CandidateIntentionStatement()
        {
            Id = registrationId,
            Name = "Name",
            StatusId = RegistrationStatus.Submitted.Id,
            SelfRegister = true,
            FilerId = filer.Id,
            FirstName = "Nick",
            LastName = "Fury",
            Version = 1,
        };
        var filing = new OfficeHolderCandidateShortForm
        {
            Id = filingId,
            FilerId = 10,
            StatusId = FilingStatus.Accepted.Id,
        };

        _filingRepository.GetForm470ById(filingId).Returns(filing);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(1);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filer.FilerId, filer.UserId).Returns(filer);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filer.FilerId).Returns(candidacy);
        _filingRelatedFilerRepositoryMock.IsFilingRelatedFilerExisted(registrationId, filer.FilerId).Returns(true);
        _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true)
            .Returns(new DecisionsForm470AttestationResponse(new List<WorkFlowError>()) { Notifications = new List<NotificationTrigger> { new(true, 1, _dateNow) } });

        var request = new Form470AttestRequest
        {
            IsAgreedTerm = true,
            CheckRequiredFieldsFlag = true
        };

        // Act
        var result = await _service.AttestOfficeholderAndCandidateCampaignStatement(filingId, request);
        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });

    }


    [Test]
    public async Task AttestOfficeholderAndCandidateCampaignStatement_ShouldReturnValue()
    {
        //Arrange
        var registrationId = 1982;
        var filingId = 1;
        var filer = new FilerUserDto
        {
            Id = 1,
            FilerId = 10,
            UserId = 1,
            FilerRole = FilerRole.CandidateRegistration_Candidate,
        };
        var candidacy = new CandidateIntentionStatement()
        {
            Id = registrationId,
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filer.Id,
            FirstName = "Nick",
            LastName = "Fury",
            Version = 1,
        };
        var filing = new OfficeHolderCandidateShortForm { Id = filingId, FilerId = 10, StatusId = 1 };

        _filingRepository.GetForm470ById(filingId).Returns(filing);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(1);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filer.FilerId, filer.UserId).Returns(filer);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filer.FilerId).Returns(candidacy);
        _filingRelatedFilerRepositoryMock.IsFilingRelatedFilerExisted(registrationId, filer.FilerId).Returns(true);
        _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true)
            .Returns(new DecisionsForm470AttestationResponse(new List<WorkFlowError>()) { Notifications = new List<NotificationTrigger> { new(true, 1, _dateNow) } });

        var request = new Form470AttestRequest
        {
            IsAgreedTerm = true,
            CheckRequiredFieldsFlag = true
        };

        //Act
        var result = await _service.AttestOfficeholderAndCandidateCampaignStatement(filingId, request);

        //Assert
        _ = await _attestationRepositoryMock.Received().Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>());
        _ = await _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true);
        _ = await _dependenices.FilingRepository.Received(1).Update(Arg.Is<OfficeHolderCandidateShortForm>(f => f.Id == filingId && f.StatusId == FilingStatus.Accepted.Id));


        await _dependenices.NotificationSvc.Received(1).SendFilerNotification(
            Arg.Is<SendFilerNotificationRequest>(n =>
                n.NotificationTemplateId == 1 &&
                n.FilerId == filer.FilerId &&
                n.DueDate.HasValue &&
                n.NotificationData == null
            ));

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });

    }

    [Test]
    public async Task AttestOfficeholderAndCandidateCampaignStatement_ShouldReturnValue_WithoutNotifications()
    {
        //Arrange
        var registrationId = 1982;
        var filingId = 1;
        var filer = new FilerUserDto
        {
            Id = 1,
            FilerId = 10,
            UserId = 1,
            FilerRole = FilerRole.CandidateRegistration_Candidate,
        };
        var candidacy = new CandidateIntentionStatement()
        {
            Id = registrationId,
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filer.Id,
            FirstName = "Nick",
            LastName = "Fury",
            Version = 1,
        };
        var filing = new OfficeHolderCandidateShortForm { Id = filingId, FilerId = 10, StatusId = 1 };

        _filingRepository.GetForm470ById(filingId).Returns(filing);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(1);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filer.FilerId, filer.UserId).Returns(filer);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filer.FilerId).Returns(candidacy);
        _filingRelatedFilerRepositoryMock.IsFilingRelatedFilerExisted(registrationId, filer.FilerId).Returns(true);
        _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true)
            .Returns(new DecisionsForm470AttestationResponse(new List<WorkFlowError>()) { Notifications = new List<NotificationTrigger> { new(false, null, _dateNow) } });

        var request = new Form470AttestRequest
        {
            IsAgreedTerm = true,
            CheckRequiredFieldsFlag = true
        };

        //Act
        var result = await _service.AttestOfficeholderAndCandidateCampaignStatement(filingId, request);

        //Assert
        _ = await _attestationRepositoryMock.Received().Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>());
        _ = await _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true);
        _ = await _dependenices.FilingRepository.Received(1).Update(Arg.Is<OfficeHolderCandidateShortForm>(f => f.Id == filingId && f.StatusId == FilingStatus.Accepted.Id));

        await _dependenices.NotificationSvc.DidNotReceive().SendUserNotification(
            Arg.Is<SendUserNotificationRequest>(n =>
                n.NotificationTemplateId == 1 &&
                n.DueDate.HasValue &&
                n.FilerId == filer.FilerId &&
                n.UserId == filer.UserId
            ));

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });

    }

    [Test]
    public async Task AttestOfficeholderAndCandidateCampaignStatement_IsAgreedTerm_NotCheck_ShouldReturnError()
    {
        //Arrange
        var request = new Form470AttestRequest
        {
            IsAgreedTerm = false
        };

        //Act
        var result = await _service.AttestOfficeholderAndCandidateCampaignStatement(It.IsAny<long>(), request);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Member(new WorkFlowError(nameof(request.IsAgreedTerm), RegistrationConstants.ValidationError.ErrorTypeValidation, RegistrationConstants.ValidationError.ErrorTypeValidation, $"Penalty of Perjury statement should be checked.")));
        });
    }

    [Test]
    public async Task AttestOfficeholderAndCandidateCampaignStatement_Decision_ShouldReturnError()
    {
        //Arrange
        var registrationId = 1982;
        var filingId = 1;
        var filer = new FilerUserDto
        {
            Id = 1,
            FilerId = 10,
            UserId = 1,
            FilerRole = FilerRole.CandidateRegistration_Candidate,
        };
        var candidacy = new CandidateIntentionStatement()
        {
            Id = registrationId,
            Name = "Name",
            StatusId = RegistrationStatus.Submitted.Id,
            SelfRegister = true,
            FilerId = filer.Id,
            FirstName = "Nick",
            LastName = "Fury",
            Version = 1,
        };
        var filing = new OfficeHolderCandidateShortForm
        {
            Id = filingId,
            FilerId = 10,
            StatusId = FilingStatus.Draft.Id,
        };

        _filingRepository.GetForm470ById(Arg.Any<long>()).Returns(filing);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(1);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filer.FilerId, filer.UserId).Returns(filer);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filer.FilerId).Returns(candidacy);
        _filingRelatedFilerRepositoryMock.IsFilingRelatedFilerExisted(registrationId, filer.FilerId).Returns(true);

        _ = _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(
            Arg.Is<DecisionsWorkflow>(d => d == DecisionsWorkflow.Form470AttestationRuleSet),
            Arg.Any<DecisionsForm470Attestation>(),
            Arg.Is<bool>(b => b)
            ).Returns(new DecisionsForm470AttestationResponse([new WorkFlowError("", "", "", "")]));

        var request = new Form470AttestRequest
        {
            IsAgreedTerm = true,
            CheckRequiredFieldsFlag = true
        };
        //Act
        var result = await _service.AttestOfficeholderAndCandidateCampaignStatement(It.IsAny<long>(), request);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.AtLeast(1));
        });
    }

    [Test]
    public async Task AttestOfficeholderAndNonCandidateCampaignStatement_ReturnsValidatedResponse_WhenDataIsValid()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 100L;
        var userId = 200L;

        var existingFiling = new OfficeHolderCandidateShortForm
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id
        };

        var filerUser = new FilerUserDto
        {
            Id = 1,
            FilerId = filerId,
            UserId = userId,
            FilerRole = FilerRole.CandidateRegistration_Candidate
        };

        var existingRegistration = new CandidateIntentionStatement
        {
            Id = 10,
            FilerId = filerId,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test",
        };

        var request = new Form470AttestRequest
        {
            CheckRequiredFieldsFlag = true
        };

        _dependenices.FilingRepository.GetForm470ById(filingId).Returns(existingFiling);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(userId);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filerId, userId).Returns(filerUser);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId).Returns(existingRegistration);

        var decisionsResult = new DecisionsForm470SendForAttestationResponse([])
        {
            Notifications = new()
            {
                new(true, 7, null)
            }
        };
        _ = _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470SendForAttestation, DecisionsForm470SendForAttestationResponse>(
            Arg.Is<DecisionsWorkflow>(d => d == DecisionsWorkflow.Form470SendForAttestationRuleSet),
            Arg.Any<DecisionsForm470SendForAttestation>(),
            Arg.Is<bool>(b => b)
            ).Returns(decisionsResult);

        // Act
        var result = await _service.AttestOfficeholderAndNonCandidateCampaignStatement(filingId, request);

        // Assert
        await _dependenices.FilingRepository.Received(1).Update(Arg.Is<OfficeHolderCandidateShortForm>(f => f.Id == filingId && f.StatusId == FilingStatus.Pending.Id));
        await _dependenices.NotificationSvc.Received(1).SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
        });
    }

    [Test]
    public async Task AttestOfficeholderAndNonCandidateCampaignStatement_ReturnsErrorResponse_WhenDataHasErrors()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 100L;
        var userId = 200L;

        var existingFiling = new OfficeHolderCandidateShortForm
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id
        };

        var filerUser = new FilerUserDto
        {
            Id = 1,
            FilerId = filerId,
            UserId = userId,
            FilerRole = FilerRole.CandidateRegistration_Candidate
        };

        var existingRegistration = new CandidateIntentionStatement
        {
            Id = 10,
            FilerId = filerId,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test",
        };

        var request = new Form470AttestRequest
        {
            CheckRequiredFieldsFlag = true
        };

        _dependenices.FilingRepository.GetForm470ById(filingId).Returns(existingFiling);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(userId);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filerId, userId).Returns(filerUser);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerId).Returns(existingRegistration);

        var decisionsResult = new DecisionsForm470SendForAttestationResponse([new WorkFlowError("", "", "", "")])
        {
            Notifications = new()
            {
                new(true, 7, null)
            }
        };
        _ = _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470SendForAttestation, DecisionsForm470SendForAttestationResponse>(
            Arg.Is<DecisionsWorkflow>(d => d == DecisionsWorkflow.Form470SendForAttestationRuleSet),
            Arg.Any<DecisionsForm470SendForAttestation>(),
            Arg.Is<bool>(b => b)
            ).Returns(decisionsResult);

        // Act
        var result = await _service.AttestOfficeholderAndNonCandidateCampaignStatement(filingId, request);

        // Assert
        await _dependenices.FilingRepository.DidNotReceive().Update(Arg.Is<OfficeHolderCandidateShortForm>(f => f.Id == filingId && f.StatusId == FilingStatus.Pending.Id));
        await _dependenices.NotificationSvc.DidNotReceive().SendUserNotification(Arg.Any<SendUserNotificationRequest>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
        });
    }

    [Test]
    public void AttestOfficeholderAndNonCandidateCampaignStatement_ShouldThrow_KeyNotFoundException_WhenFilingNotFound()
    {
        // Arrange
        long filingId = 123;

        _filingRepository.GetForm470ById(filingId).Returns((OfficeHolderCandidateShortForm?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
        {
            await _service.AttestOfficeholderAndNonCandidateCampaignStatement(filingId, new Form470AttestRequest());
        });

        Assert.That(ex, Is.Not.Null);
        Assert.That(ex.Message, Does.Contain($"OfficeHolder Candidate Short Form not found. Id={filingId}"));
    }

    [Test]
    public void AttestOfficeholderAndNonCandidateCampaignStatement_ShouldThrow_InvalidOperationException_WhenFilerUserNotFound()
    {
        // Arrange
        long filingId = 123;
        var filing = new OfficeHolderCandidateShortForm { Id = filingId, FilerId = 456, StatusId = FilingStatus.Pending.Id };

        _filingRepository.GetForm470ById(filingId).Returns(filing);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(1);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filing.FilerId, 1).Returns((FilerUserDto?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
        {
            await _service.AttestOfficeholderAndNonCandidateCampaignStatement(filingId, new Form470AttestRequest());
        });

        Assert.That(ex, Is.Not.Null);
        Assert.That(ex.Message, Does.Contain($"No filer user found with registration ID {filingId}."));
    }

    [Test]
    public void AttestOfficeholderAndNonCandidateCampaignStatement_ShouldThrow_KeyNotFoundException_WhenRegistrationNotFound()
    {
        // Arrange
        long filingId = 123;
        var filing = new OfficeHolderCandidateShortForm { Id = filingId, FilerId = 456, StatusId = FilingStatus.Pending.Id };
        var filerUser = new FilerUserDto { Id = 1, FilerId = 456, UserId = 1 };

        _filingRepository.GetForm470ById(filingId).Returns(filing);
        _dependenices.AuthorizationSvc.GetInitiatingUserId().Returns(1);
        _dependenices.FilerSvc.GetFilerUserByUserIdAsync(filing.FilerId, 1).Returns(filerUser);
        _dependenices.RegistrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(filerUser.FilerId).Returns((CandidateIntentionStatement?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
        {
            await _service.AttestOfficeholderAndNonCandidateCampaignStatement(filingId, new Form470AttestRequest());
        });

        Assert.That(ex, Is.Not.Null);
        Assert.That(ex.Message, Does.Contain($"Registration not found. Id={filingId}"));
    }

    #endregion

    #region SubmitCandidateStatementShortForEfile

    [Test]
    public async Task SubmitCandidateStatementShortForEfile_ValidSubmission_CreatesFilingAndAttestationAndReturnsSuccess()
    {
        // Arrange
        var filing = new OfficeHolderCandidateShortForm
        {
            Id = 10,
            FilerId = 20,
            StatusId = FilingStatus.Draft.Id
        };
        var attestation = new CandidateCampaignStatementShortAttestation
        {
            FirstName = "John",
            LastName = "Doe",
            Role = "Candidate"
        };
        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = filing,
            Attestation = attestation
        };
        var decisionsResponse = new DecisionsForm470AttestationResponse(new List<WorkFlowError>());
        _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(
                DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true).Returns(decisionsResponse);

        _filingRepository.Create(filing).Returns(filing);
        _attestationRepositoryMock.Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>())
            .Returns(new Models.FilerRegistration.Registrations.Attestation { Id = 1 });
        _filingRepository.Update(filing).Returns(filing);

        // Act
        var result = await _service.SubmitCandidateStatementShortForEfile(submission);

        // Assert
        await _filingRepository.Received(1).Create(filing);
        await _attestationRepositoryMock.Received(1).Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>());
        await _filingRepository.Received(1).Update(filing);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task SubmitCandidateStatementShortForEfile_WithValidationErrors_DoesNotCreateFilingOrAttestation()
    {
        // Arrange
        var filing = new OfficeHolderCandidateShortForm
        {
            Id = 10,
            FilerId = 20,
            StatusId = FilingStatus.Draft.Id
        };
        var attestation = new CandidateCampaignStatementShortAttestation
        {
            FirstName = "John",
            LastName = "Doe",
            Role = "Candidate"
        };
        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = filing,
            Attestation = attestation
        };
        var errors = new List<WorkFlowError> { new("field", "type", "code", "message") };
        var decisionsResponse = new DecisionsForm470AttestationResponse(errors);
        _dependenices.DecisionsSvc.InitiateWorkflow<DecisionsForm470Attestation, DecisionsForm470AttestationResponse>(
                DecisionsWorkflow.Form470AttestationRuleSet, Arg.Any<DecisionsForm470Attestation>(), true).Returns(decisionsResponse);

        // Act
        var result = await _service.SubmitCandidateStatementShortForEfile(submission);

        // Assert
        await _filingRepository.DidNotReceive().Create(Arg.Any<Filing>());
        await _attestationRepositoryMock.DidNotReceive().Create(Arg.Any<Models.FilerRegistration.Registrations.Attestation>());
        await _filingRepository.DidNotReceive().Update(Arg.Any<Filing>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusId, Is.EqualTo(filing.StatusId));
            Assert.That(result.Valid, Is.True); // The method always returns true for Valid
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
    }

    [Test]
    public void SubmitCandidateStatementShortForEfile_ThrowsIfAttestationIsNull()
    {
        // Arrange
        var filing = new OfficeHolderCandidateShortForm
        {
            Id = 10,
            FilerId = 20,
            StatusId = FilingStatus.Draft.Id
        };

        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = filing,
            Attestation = null
        };

        // Act & Assert
        Assert.ThrowsAsync<NullReferenceException>(async () =>
        {
            await _service.SubmitCandidateStatementShortForEfile(submission);
        });
    }

    [Test]
    public void SubmitCandidateStatementShortForEfile_ThrowsIfCandidateStatementShortIsNull()
    {
        // Arrange
        var attestation = new CandidateCampaignStatementShortAttestation
        {
            FirstName = "John",
            LastName = "Doe",
            Role = "Candidate"
        };
        var submission = new CandidateStatementShortSubmissionDto
        {
            CandidateStatementShort = null,
            Attestation = attestation
        };

        // Act & Assert
        Assert.ThrowsAsync<NullReferenceException>(async () =>
        {
            await _service.SubmitCandidateStatementShortForEfile(submission);
        });
    }

    #endregion
}
