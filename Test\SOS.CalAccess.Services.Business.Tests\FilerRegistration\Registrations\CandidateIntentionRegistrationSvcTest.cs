using System.Linq.Expressions;
using NSubstitute;
using SendGrid.Helpers.Errors.Model;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Elections;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.UserAccountMaintenance.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Tests.Common;
using Attestation = SOS.CalAccess.Models.FilerRegistration.Registrations.Attestation;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;

/// <summary>
/// Unit tests for the <see cref="CandidateIntentionRegistrationSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(CandidateIntentionRegistrationSvc))]
public sealed class CandidateIntentionRegistrationSvcTest
{
    #region Properties

    private IDecisionsSvc _decisionsSvc;
    private IAuthorizationSvc _authorizationSvc;
    private IAttestationRepository _attestationRepository;
    private IElectionRaceRepository _electionRaceRepository;
    private IPoliticalPartyRepository _politicalPartyRepository;
    private IRegistrationRepository _registrationRepository;
    private ICandidateRepository _candidateRepository;
    private IFilerLinkRepository _filerLinkRepository;
    private INotificationSvc _notificationSvc;
    private IUserMaintenanceSvc _userMaintenanceSvc;
    private ILinkageSvc _linkageSvc;
    private IRegistrationModelMapper _modelMapper;
    private IFilerUserRepository _filerUserRepository;
    private IDateTimeSvc _dateTimeSvcMock;
    private DateTime _dateNow;

    private CandidateIntentionRegistrationSvc _service;

    /// <summary>
    /// Maximum allowed execution time (in milliseconds).
    /// </summary>
    private const int MaxTimeAllowed = 60000;

    /// <summary>
    /// A minimal ElectionRace that only contains FKs to other entities
    /// </summary>
    private static readonly ElectionRace _minimalElectionRace = new()
    {
        ElectionId = 1,
        OfficeId = 1,
        DistrictId = 1
    };

    /// <summary>
    /// ElectionRace object that contains actual entities that it references,
    /// not just FKs.
    /// </summary>
    private static readonly ElectionRace _defaultElectionRace = new()
    {
        Election = new()
        {
            Id = 1,
            ElectionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            Name = "2025"
        },
        Office = new()
        {
            Id = 1,
            AgencyName = "Some Agency",
            Name = "Governor"
        },
        District = new()
        {
            Id = 1,
            DistrictNumber = 1
        }
    };

    private static readonly CandidateIntentionStatement _mappedCandidateIntentionStatement = new()
    {
        Id = 41381, //would normally only be set after repository create method called
        Name = "Test",
        StatusId = RegistrationStatus.Draft.Id,
        FirstName = "First",
        MiddleName = "Middle",
        LastName = "Last",
        Email = "Email",
        Filer = new()
        {
            FilerStatusId = FilerStatus.Draft.Id,
            FilerTypeId = FilerType.Candidate.Id,
            Users = new() {
                    new(){UserId=10004, FilerRoleId = 3,},
            }
        },
        PhoneNumberList = new()
        {
            PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
        },
        AddressList = new()
        {
            Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
        },
    };

    /// <summary>
    /// List of all political parties when we call GetAll() from the PoliticalPartyRepository.
    /// </summary>
    private static readonly List<PoliticalParty> _allPoliticalParties = new()
    {
        new()
        {
            Id = 1,
            Name = "American Independent Party"
        }
    };


    private static readonly CandidateIntentionStatement _UpdatedmappedCandidateIntentionStatement = new()
    {
        Id = 41381, //would normally only be set after repository create method called
        Name = "Test",
        StatusId = RegistrationStatus.Draft.Id,
        FirstName = "First",
        MiddleName = "Middle",
        LastName = "Last",
        Email = "Email",
        Filer = new()
        {
            FilerStatusId = FilerStatus.Draft.Id,
            FilerTypeId = FilerType.Candidate.Id,
            Users = new() {
                    new(){UserId=10004, FilerRoleId = 3,},
            }
        },
        PhoneNumberList = _mappedCandidateIntentionStatement.PhoneNumberList, // reuse
        AddressList = _mappedCandidateIntentionStatement.AddressList
    };

    #endregion

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _registrationRepository = Substitute.For<IRegistrationRepository>();
        _candidateRepository = Substitute.For<ICandidateRepository>();
        _attestationRepository = Substitute.For<IAttestationRepository>();
        _modelMapper = Substitute.For<IRegistrationModelMapper>();
        _electionRaceRepository = Substitute.For<IElectionRaceRepository>();
        _politicalPartyRepository = Substitute.For<IPoliticalPartyRepository>();
        _filerLinkRepository = Substitute.For<IFilerLinkRepository>();
        _notificationSvc = Substitute.For<INotificationSvc>();
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _linkageSvc = Substitute.For<ILinkageSvc>();
        _filerUserRepository = Substitute.For<IFilerUserRepository>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        var dependencies = new CandidateIntentionDependencies(
            _authorizationSvc,
            _decisionsSvc,
            _notificationSvc,
            _registrationRepository,
            _candidateRepository,
            _filerLinkRepository,
            _userMaintenanceSvc,
            _linkageSvc,
            _dateTimeSvcMock);

        _service = new CandidateIntentionRegistrationSvc(
            dependencies,
            _attestationRepository,
            _electionRaceRepository,
            _politicalPartyRepository,
            _modelMapper,
            _filerUserRepository);
    }

    /// <summary>
    /// Create a Candidate Intention Statement in draft status.
    /// </summary>
    [Test]
    public async Task CreateCandidateIntentionStatementWithoutDecisionsAndAddressValidation()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 41381, //would normally only be set after repository create method called
            CandidateId = 1,
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },

        };

        _modelMapper.MapCandidateIntentionStatementRequestToModel(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(mappedCandidateIntentionStatement);

        _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsCandidateIntentionStatement>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _authorizationSvc.GetInitiatingUserId()
            .Returns(22482);

        _registrationRepository.Create(Arg.Any<CandidateIntentionStatement>())
            .Returns(mappedCandidateIntentionStatement);

        _registrationRepository.Update(Arg.Any<CandidateIntentionStatement>())
            .Returns(mappedCandidateIntentionStatement);

        var request = new CandidateIntentionStatementRequest();

        // Act
        var result = await _service.CreateCandidateIntentionStatement(request);

#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed

        // Assert

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(41381));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
        });

        //Validate that request properly mapped to Decisions input
        _decisionsSvc.Received(1).InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(
            DecisionsWorkflow.CandidateInformationRuleset,
            Arg.Is<DecisionsCandidateIntentionStatement>(x =>
                x.FirstName == "First"
                && x.MiddleName == "Middle"
                && x.LastName == "Last"
                && x.Email == "Email"
                && x.PhoneNumber == "**********"
                && x.FaxNumber == "**********"
                && x.Address1!.City == "Plainsville"
                && x.Address2!.City == "Anytown"
            ),
            false);

        //Validate that user association to registration created
        Assert.That(mappedCandidateIntentionStatement.Filer, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(mappedCandidateIntentionStatement.Filer.FilerStatusId, Is.EqualTo(FilerStatus.Draft.Id));
            Assert.That(mappedCandidateIntentionStatement.Filer.CurrentRegistration?.Id, Is.EqualTo(mappedCandidateIntentionStatement.Id));
            Assert.That(mappedCandidateIntentionStatement.Filer.Users, Is.Not.Null);
            Assert.That(mappedCandidateIntentionStatement.Filer.Users, Has.Count.EqualTo(1));
            Assert.That(mappedCandidateIntentionStatement.Filer.Users?[0].UserId, Is.EqualTo(22482));
            Assert.That(mappedCandidateIntentionStatement.Filer.Users?[0].FilerRoleId, Is.EqualTo(FilerRole.CandidateRegistration_AccountManager.Id));
        });

        //Validate that DB record inserted
        _registrationRepository.Received(1).Create(mappedCandidateIntentionStatement);
        _registrationRepository.Received(1).Update(mappedCandidateIntentionStatement);


#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
    }

    [Test]
    public async Task CreateCandidateIntentionStatement_Invalid()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = null,
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },

        };

        _modelMapper.MapCandidateIntentionStatementRequestToModel(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(mappedCandidateIntentionStatement);

        var errors = new List<WorkFlowError>() {
            new ("FirstName","GlobalErr1000", "fatal", "Field Required")
        };
        _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsCandidateIntentionStatement>(), Arg.Any<bool>())
            .Returns(errors);


        var request = new CandidateIntentionStatementRequest();

        // Act
        var result = await _service.CreateCandidateIntentionStatement(request);


        // Assert

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(0));
            Assert.That(result.Valid, Is.False);
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(result.ValidationErrors, Is.EquivalentTo(errors));
        });

        //Validate that DB record NOT inserted
        await _registrationRepository.DidNotReceive().Create(Arg.Any<CandidateIntentionStatement>());
    }

    /// <summary>
    /// Test to ensure AddRegistrationAddress adds a valid address.
    /// </summary>
    [Test]
    public async Task CreateCandidateIntentionStatement_SelfRegisterCreatesCandidateFilerRole()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 41381, //would normally only be set after repository create method called
            CandidateId = null,
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },

        };

        var candidate = new Candidate() { };

        _modelMapper.MapCandidateIntentionStatementRequestToModel(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(mappedCandidateIntentionStatement);

        _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsCandidateIntentionStatement>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _authorizationSvc.GetInitiatingUserId()
            .Returns(22482);

        _registrationRepository.Create(Arg.Any<CandidateIntentionStatement>())
            .Returns(mappedCandidateIntentionStatement);

        _candidateRepository.Create(Arg.Any<Candidate>())
            .Returns(candidate);
        var request = new CandidateIntentionStatementRequest() { SelfRegister = true };

        // Act
        var result = await _service.CreateCandidateIntentionStatement(request);

        // Assert

        //Validate that user association to registration created
        Assert.Multiple(() =>
        {
            Assert.That(mappedCandidateIntentionStatement.Filer?.Users, Is.Not.Null);
            Assert.That(mappedCandidateIntentionStatement.Filer!.Users, Has.Count.EqualTo(1));
            Assert.That(mappedCandidateIntentionStatement.Filer.Users![0].UserId, Is.EqualTo(22482));
            Assert.That(mappedCandidateIntentionStatement.Filer.Users[0].FilerRoleId, Is.EqualTo(FilerRole.CandidateRegistration_Candidate.Id));
        });
    }

    [Test]
    public async Task CreateCandidateIntentionStatement_NonCandidate_CreatesCandidate()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 41381, //would normally only be set after repository create method called
            SelfRegister = false,
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },

        };

        Candidate candidate = new()
        {
            Id = 1
        };

        _modelMapper.MapCandidateIntentionStatementRequestToModel(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(mappedCandidateIntentionStatement);

        _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsCandidateIntentionStatement>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _authorizationSvc.GetInitiatingUserId()
            .Returns(22482);

        _registrationRepository.Create(Arg.Any<CandidateIntentionStatement>())
            .Returns(mappedCandidateIntentionStatement);

        _candidateRepository.Create(Arg.Any<Candidate>())
            .Returns(candidate);

        var request = new CandidateIntentionStatementRequest() { SelfRegister = true };

        // Act
        var result = await _service.CreateCandidateIntentionStatement(request);

        // Assert

        //Validate that user association to registration created
        Assert.Multiple(() =>
        {
            Assert.That(mappedCandidateIntentionStatement.Filer?.Users, Is.Not.Null);
            Assert.That(mappedCandidateIntentionStatement.Filer!.Users, Has.Count.EqualTo(1));
            Assert.That(mappedCandidateIntentionStatement.Filer.Users![0].UserId, Is.EqualTo(22482));
            Assert.That(mappedCandidateIntentionStatement.Filer.Users[0].FilerRoleId, Is.EqualTo(FilerRole.CandidateRegistration_Candidate.Id));
        });
    }

    [Test]
    public async Task CreateCandidateIntentionStatement_PreviousCandidate_CreatesCandidate()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 41381, //would normally only be set after repository create method called
            SelfRegister = false,
            PreviousCandidate = true,
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },

        };

        User user = new()
        {
            Id = 123,
            FirstName = mappedCandidateIntentionStatement.FirstName,
            LastName = mappedCandidateIntentionStatement.LastName,
            EmailAddress = "<EMAIL>",
            EntraOid = "0"
        };

        Candidate candidate = new()
        {
            Id = 1,
            UserId = user.Id
        };

        _modelMapper.MapCandidateIntentionStatementRequestToModel(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(mappedCandidateIntentionStatement);

        _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsCandidateIntentionStatement>(), Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _authorizationSvc.GetInitiatingUserId()
            .Returns(22482);

        _registrationRepository.Create(Arg.Any<CandidateIntentionStatement>())
            .Returns(mappedCandidateIntentionStatement);

        _candidateRepository.Create(Arg.Any<Candidate>())
            .Returns(candidate);

        _userMaintenanceSvc.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(new List<User> { user });

        var request = new CandidateIntentionStatementRequest() { SelfRegister = true };

        // Act
        var result = await _service.CreateCandidateIntentionStatement(request);

        // Assert
        await _candidateRepository.Received(1).Create(Arg.Is<Candidate>(c => c.UserId == user.Id));
    }

    [Test]
    public async Task GetCandidateIntentionStatement_Found()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 41381, //would normally only be set after repository create method called
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },
            IsSameAsCandidateAddress = true,
            PreviousCandidate = true,
            SelfRegister = true,
            ExpenditureCeilingAmount = 1981,
            ElectionCounty = "county",
            ElectionRaceId = 1982,
            ElectionJurisdiction = "Juris",
            ElectionOfficeSought = "Office",
            ElectionDistrictNumber = "43",
            PoliticalPartyId = 1983,
            CandidateId = 2556,
        };

        _registrationRepository.FindCandidateIntentionStatementById(41381).Returns(mappedCandidateIntentionStatement);

        //Act
        var result = await _service.GetCandidateIntentionStatement(41381);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(mappedCandidateIntentionStatement.Id));
            Assert.That(result.Email, Is.EqualTo(mappedCandidateIntentionStatement.Email));
            Assert.That(result.FirstName, Is.EqualTo(mappedCandidateIntentionStatement.FirstName));
            Assert.That(result.LastName, Is.EqualTo(mappedCandidateIntentionStatement.LastName));
            Assert.That(result.MiddleName, Is.EqualTo(mappedCandidateIntentionStatement.MiddleName));
            Assert.That(result.IsSameAsCandidateAddress, Is.EqualTo(mappedCandidateIntentionStatement.IsSameAsCandidateAddress));
            Assert.That(result.PreviousCandidate, Is.EqualTo(mappedCandidateIntentionStatement.PreviousCandidate));
            Assert.That(result.SelfRegister, Is.EqualTo(mappedCandidateIntentionStatement.SelfRegister));
            Assert.That(result.ExpenditureCeilingAmount, Is.EqualTo(mappedCandidateIntentionStatement.ExpenditureCeilingAmount));

            Assert.That(result.ElectionCounty, Is.EqualTo(mappedCandidateIntentionStatement.ElectionCounty));
            Assert.That(result.ElectionRaceId, Is.EqualTo(mappedCandidateIntentionStatement.ElectionRaceId));
            Assert.That(result.ElectionJurisdiction, Is.EqualTo(mappedCandidateIntentionStatement.ElectionJurisdiction));
            Assert.That(result.ElectionOfficeSought, Is.EqualTo(mappedCandidateIntentionStatement.ElectionOfficeSought));
            Assert.That(result.ElectionDistrictNumber, Is.EqualTo(mappedCandidateIntentionStatement.ElectionDistrictNumber));
            Assert.That(result.PoliticalPartyId, Is.EqualTo(mappedCandidateIntentionStatement.PoliticalPartyId));
            Assert.That(result.MailingAddress?.City, Is.EqualTo("Anytown"));

            Assert.That(result.CandidateAddress?.City, Is.EqualTo("Plainsville"));
            Assert.That(result.CandidateId, Is.EqualTo(mappedCandidateIntentionStatement.CandidateId));
            Assert.That(result.PhoneNumber?.Number, Is.EqualTo("**********"));
            Assert.That(result.FaxNumber?.Number, Is.EqualTo("**********"));
        });
    }

    [Test]
    public async Task GetCandidateIntentionStatement_NotFound()
    {
        //Act
        try
        {
            var result = await _service.GetCandidateIntentionStatement(99999);
            Assert.Fail("Expected KeyNotFoundException not thrown");
        }
        catch (KeyNotFoundException)
        {
            //do nothing
        }
    }

    [Test]
    public async Task GetCandidateIntentionStatementSummary_ReturnsStatement_WhenFound()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 1,
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },
            IsSameAsCandidateAddress = true,
            PreviousCandidate = true,
            SelfRegister = true,
            ExpenditureCeilingAmount = 1981,
            ElectionCounty = "county",
            ElectionRaceId = 1982,
            ElectionJurisdiction = "Juris",
            ElectionOfficeSought = "Office",
            ElectionDistrictNumber = "43",
            PoliticalPartyId = 1983,
            CandidateId = 2556,
        };

        _registrationRepository.FindCandidateIntentionStatementWithElectionById(1).Returns(mappedCandidateIntentionStatement);

        //Act
        var result = await _service.GetCandidateIntentionStatementSummary(1);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(mappedCandidateIntentionStatement.Id));
            Assert.That(result.Email, Is.EqualTo(mappedCandidateIntentionStatement.Email));
            Assert.That(result.FirstName, Is.EqualTo(mappedCandidateIntentionStatement.FirstName));
            Assert.That(result.LastName, Is.EqualTo(mappedCandidateIntentionStatement.LastName));
            Assert.That(result.MiddleName, Is.EqualTo(mappedCandidateIntentionStatement.MiddleName));
            Assert.That(result.IsSameAsCandidateAddress, Is.EqualTo(mappedCandidateIntentionStatement.IsSameAsCandidateAddress));
            Assert.That(result.PreviousCandidate, Is.EqualTo(mappedCandidateIntentionStatement.PreviousCandidate));
            Assert.That(result.SelfRegister, Is.EqualTo(mappedCandidateIntentionStatement.SelfRegister));
            Assert.That(result.ExpenditureCeilingAmount, Is.EqualTo(mappedCandidateIntentionStatement.ExpenditureCeilingAmount));

            Assert.That(result.ElectionCounty, Is.EqualTo(mappedCandidateIntentionStatement.ElectionCounty));
            Assert.That(result.ElectionRaceId, Is.EqualTo(mappedCandidateIntentionStatement.ElectionRaceId));
            Assert.That(result.ElectionJurisdiction, Is.EqualTo(mappedCandidateIntentionStatement.ElectionJurisdiction));
            Assert.That(result.ElectionOfficeSought, Is.EqualTo(mappedCandidateIntentionStatement.ElectionOfficeSought));
            Assert.That(result.ElectionDistrictNumber, Is.EqualTo(mappedCandidateIntentionStatement.ElectionDistrictNumber));
            Assert.That(result.PoliticalPartyId, Is.EqualTo(mappedCandidateIntentionStatement.PoliticalPartyId));

        });
    }

    [Test]
    public async Task GetCandidateIntentionStatementSummary_ReturnsNull_WhenNotFound()
    {
        // Arrange
        long id = 99999;

        _registrationRepository
            .FindCandidateIntentionStatementWithElectionById(id)
            .Returns((CandidateIntentionStatement?)null);

        // Act
        var result = await _service.GetCandidateIntentionStatementSummary(id);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task LinkElectionToCandidateIntentionStatement_ShouldReturnErrors_WhenDecisionScreenValidationFails()
    {
        // Arrange
        var candidateIntentId = 1L;
        var electionId = 1982L;

        var electionreq = new UpdateRegistrationElectionRequest
        {
            CheckRequiredFieldsFlag = true,
            CountyId = 1,
            DistrictId = 2,
            ElectionId = electionId,
            Jurisdiction = "state",
            OfficeId = 4,
            PartyId = 5,
            ElectionYear = "2025",
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("Office", "ERR001", "Validation", "Office is required")
        };

        _decisionsSvc
            .InitiateWorkflow<DecisionsElectionValidation, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationElectionScreenValidationRuleset,
                Arg.Any<DecisionsElectionValidation>(),
                true)
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _service.LinkElectionToCandidateIntentionStatement(candidateIntentId, electionreq);

        // Assert
        Assert.That(result.Id, Is.EqualTo(0));
        await _registrationRepository.DidNotReceiveWithAnyArgs()
            .LinkElectionToCandidateIntentRegistration(default, default, default, default, default, default, default);
    }

    [Test]
    public async Task LinkElectionToCandidateIntentRegistration_ShouldCallLinkElectionToCandidateIntentRegistration_Success()
    {
        // Arrange
        var candidateIntentId = 1L;
        var electionId = 1982L;

        var electionreq = new UpdateRegistrationElectionRequest
        {
            CheckRequiredFieldsFlag = true,
            CountyId = 1,
            DistrictId = 2,
            ElectionId = electionId,
            Jurisdiction = "state",
            OfficeId = 4,
            PartyId = 5,
            ElectionYear = "2025",
        };

        var race = new ElectionRace
        {
            DistrictId = 2,
            District = new() { DistrictNumber = 2, Name = "District 2" },
            ElectionId = electionId,
            Election = new()
            {
                Name = "2026 General Election",
                ElectionDate = _dateNow,
                ElectionType = new ElectionType() { Name = "General" },
            },
            OfficeId = 4,
            Office = new()
            {
                Name = "Governor",
                AgencyName = "Not Applicable",
                IsNonPartisanOffice = false,
            }
        };

        var party = new PoliticalParty { Name = "Centrist" };
        var decisionResponse = new List<WorkFlowError>();

        _electionRaceRepository.GetElectionRaceAsync(electionId, 4, 2).Returns(race);
        _politicalPartyRepository.FindById(5).Returns(party);

        _decisionsSvc
            .InitiateWorkflow<DecisionsElectionValidation, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationElectionScreenValidationRuleset,
                Arg.Any<DecisionsElectionValidation>(),
                true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        _decisionsSvc
            .InitiateWorkflow<DecisionsElectionInformation, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationElectionRuleset,
                Arg.Any<DecisionsElectionInformation>(),
                true)
            .Returns(Task.FromResult(decisionResponse));

        _registrationRepository
            .LinkElectionToCandidateIntentRegistration(
                candidateIntentId, "state", electionId, 4, 2, 1, 5)
            .Returns(electionId);

        // Act
        var result = await _service.LinkElectionToCandidateIntentionStatement(candidateIntentId, electionreq);

        // Assert
        Assert.That(result.Id, Is.EqualTo(electionId));
        await _registrationRepository.Received(1)
            .LinkElectionToCandidateIntentRegistration(candidateIntentId, "state", electionId, 4, 2, 1, 5);
    }


    [Test]
    public async Task LinkElectionToCandidateIntentionStatement_Should()
    {
        // Arrange
        long id = 1;
        var electionInfo = new UpdateRegistrationElectionRequest
        {
            Jurisdiction = "state"
        };
        var electionRace = new ElectionRace
        {
            District = new District { Name = "TestDistrict", DistrictNumber = 1 },
            Office = new Office { AgencyName = "TestAgencyName", Name = "TestOffice" },
            Election = new Election
            {
                Name = "TestElection",
                ElectionDate = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                ElectionType = new ElectionType { Name = "TestElectionType" },
            }
        };
        var politicalParty = new PoliticalParty
        {
            Name = "TestPoliticalParty"
        };
        var decisionResponse = new List<WorkFlowError>() { };
        _electionRaceRepository.GetElectionRaceAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>()).Returns(electionRace);
        _politicalPartyRepository.FindById(Arg.Any<long>()).Returns(politicalParty);
        _decisionsSvc.InitiateWorkflow<DecisionsElectionValidation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsElectionValidation>(), false).Returns(decisionResponse);
        _decisionsSvc.InitiateWorkflow<DecisionsElectionInformation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsElectionInformation>(), false).Returns(decisionResponse);
        _registrationRepository.LinkElectionToCandidateIntentRegistration(Arg.Any<long>(), Arg.Any<string>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>()).Returns(1);

        // Act
        var output = await _service.LinkElectionToCandidateIntentionStatement(id, electionInfo);

        // Assert
        Assert.That(output, Is.Not.Null);
    }

    [Test, Category("Unit")]
    public async Task SendForAttestation_Valid()
    {
        //Arrange
        var id = 1982;
        var candidacy = new CandidateIntentionStatement() { Id = id, Version = 0, FilerId = 1, Name = "Name", Email = "Email", StatusId = RegistrationStatus.Draft.Id, SelfRegister = false };

        _registrationRepository.FindCandidateIntentionStatementWithElectionById(id).Returns(candidacy);
        _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(DecisionsWorkflow.CandidateInformationNonCandidateSubmissionRuleset, Arg.Any<DecisionsCandidateIntentionStatementSubmission>(), true)
            .Returns(new DecisionsCandidateIntentionStatementSubmissionResponse() { Status = RegistrationStatus.Pending.ToString()!, Error = new List<WorkFlowError>() });
        await _linkageSvc.SendLinkageRequestToPerson(Arg.Any<SendLinkageRequestToPersonDto>());
        _filerUserRepository.FindFilerUsersByFilerId(Arg.Any<long>()).Returns(new List<FilerUser>());


        //Act
        var result = await _service.SendForAttestation(id);

        //Assert
        await _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(DecisionsWorkflow.CandidateInformationNonCandidateSubmissionRuleset, Arg.Any<DecisionsCandidateIntentionStatementSubmission>(), true);
        await _registrationRepository.Received(1).Update(
           Arg.Is<CandidateIntentionStatement>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Pending.Id)
        );

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }
    [Test]
    public void SendForAttestation_NotFound_Throw()
    {
        //Arrange
        var id = 1982;
        //Act
        var result = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.SendForAttestation(id));
    }

    [Test]
    public async Task SubmitCandidateIntentionStatementWithAttestation_Valid()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var candidacy = new CandidateIntentionStatement()
        {
            Id = id,
            Version = 0,
            Name = "FirstLast",
            FirstName = "First",
            LastName = "Last",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Draft.Id
            }
        };

        var response = new DecisionsCandidateIntentionStatementSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Error = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger> { new(true, 3, _dateNow) }
        };

        _registrationRepository.FindCandidateIntentionStatementWithElectionById(id).Returns(candidacy);
        _registrationRepository.GetRegistrationByFilerId(filerId).Returns(candidacy);
        _filerUserRepository.FindFilerUsersByFilerId(Arg.Any<long>()).Returns(new List<FilerUser>());

        var expectedUser = new User() { Id = id, EmailAddress = "email", FirstName = "First", LastName = "Last", UserName = "FirstLast", EntraOid = "TestOid" };

        _userMaintenanceSvc
            .GetListUsersByUserNameAsync(Arg.Any<List<string>>())
            .Returns(new List<User> { expectedUser });

        var userId = expectedUser?.Id;

        _decisionsSvc
            .InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationCandidateSubmissionRuleset,
                Arg.Any<DecisionsCandidateIntentionStatementSubmission>(),
                true)
            .Returns(response);

        // Act
        var result = await _service.SubmitCandidateIntentionStatement(id);

        // Assert
        await _attestationRepository.Received(1).Create(Arg.Any<Attestation>());
        await _decisionsSvc.Received(1)
            .InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationCandidateSubmissionRuleset,
                Arg.Any<DecisionsCandidateIntentionStatementSubmission>(),
                true);
        await _registrationRepository.Received(1).Update(
            Arg.Is<CandidateIntentionStatement>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Accepted.Id && x.Filer!.FilerStatusId == FilerStatus.Active.Id));

        await _notificationSvc.Received(1).SendFilerNotification(
            Arg.Is<SendFilerNotificationRequest>(n =>
                n.NotificationTemplateId == 3 &&
                n.FilerId == filerId &&
                n.DueDate.HasValue
            ));

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public void SubmitCandidateIntentionStatementWithAttestation_InValid_NonCandidate_CandidateUserAccountDoesNotExist()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var candidacy = new CandidateIntentionStatement()
        {
            Id = id,
            Name = "FirstLast",
            FirstName = "First",
            LastName = "Last",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId
        };

        var response = new DecisionsCandidateIntentionStatementSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Error = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger> { new(true, 3, _dateNow) }
        };

        _registrationRepository.FindCandidateIntentionStatementWithElectionById(id).Returns(candidacy);
        _registrationRepository.GetRegistrationByFilerId(filerId).Returns(candidacy);
        _filerUserRepository.FindFilerUsersByFilerId(Arg.Any<long>()).Returns(new List<FilerUser>());

        var expectedUser = new User() { EmailAddress = "email", FirstName = "First", LastName = "Last", UserName = "FirstLast", EntraOid = "TestOid" };

        _userMaintenanceSvc
            .GetListUsersByUserNameAsync(Arg.Any<List<string>>())
            .Returns(new List<User> { });

        var userId = expectedUser?.Id;

        _decisionsSvc
            .InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationCandidateSubmissionRuleset,
                Arg.Any<DecisionsCandidateIntentionStatementSubmission>(),
                true)
            .Returns(response);

        // Act
        var result = _service.SubmitCandidateIntentionStatement(id);

        // Assert
        Assert.That(result.Exception, Is.Null);
    }

    [Test]
    public void SubmitCandidateIntentionStatementWithAttestation_InValid_NullNotificationTemplateId()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var candidacy = new CandidateIntentionStatement()
        {
            Id = id,
            Name = "FirstLast",
            FirstName = "First",
            LastName = "Last",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId
        };

        var response = new DecisionsCandidateIntentionStatementSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Error = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger> { new(true, null, _dateNow) }
        };

        _registrationRepository.FindCandidateIntentionStatementWithElectionById(id).Returns(candidacy);
        _registrationRepository.GetRegistrationByFilerId(filerId).Returns(candidacy);
        _filerUserRepository.FindFilerUsersByFilerId(Arg.Any<long>()).Returns(new List<FilerUser>());

        var expectedUser = new User() { Id = id, EmailAddress = "email", FirstName = "First", LastName = "Last", UserName = "FirstLast", EntraOid = "TestOid" };

        _userMaintenanceSvc
            .GetListUsersByUserNameAsync(Arg.Any<List<string>>())
            .Returns(new List<User> { expectedUser });

        _decisionsSvc
            .InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationCandidateSubmissionRuleset,
                Arg.Any<DecisionsCandidateIntentionStatementSubmission>(),
                true)
            .Returns(response);

        // Act
        var result = _service.SubmitCandidateIntentionStatement(id);

        // Assert
        Assert.That(result.Exception, Is.Null);
    }

    [Test]
    public async Task SubmitCandidateIntentionStatement_NoNotifications_ShouldNotCallNotificationService()
    {
        // Arrange
        var id = 1982;
        var filerId = 12345;
        var candidacy = new CandidateIntentionStatement()
        {
            Id = id,
            Version = 0,
            Name = "Name",
            StatusId = RegistrationStatus.Draft.Id,
            SelfRegister = true,
            FilerId = filerId
        };

        var response = new DecisionsCandidateIntentionStatementSubmissionResponse()
        {
            Status = RegistrationStatus.Accepted.ToString()!,
            Error = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>
            {
                // All notifications have SendNotification = false
                new(false, null, null)
            }
        };

        _registrationRepository.FindCandidateIntentionStatementWithElectionById(id).Returns(candidacy);
        _decisionsSvc
            .InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationCandidateSubmissionRuleset,
                Arg.Any<DecisionsCandidateIntentionStatementSubmission>(),
                true)
            .Returns(response);
        _filerUserRepository.FindFilerUsersByFilerId(Arg.Any<long>()).Returns(new List<FilerUser>());

        // Act
        var result = await _service.SubmitCandidateIntentionStatement(id);

        // Assert
        await _attestationRepository.Received(1).Create(Arg.Any<Attestation>());
        await _decisionsSvc.Received(1)
            .InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(
                DecisionsWorkflow.CandidateInformationCandidateSubmissionRuleset,
                Arg.Any<DecisionsCandidateIntentionStatementSubmission>(),
                true);
        await _registrationRepository.Received(1).Update(
            Arg.Is<CandidateIntentionStatement>(x => x.Version == 0 && x.StatusId == RegistrationStatus.Accepted.Id));

        // Ensure notification service was NOT called
        await _notificationSvc.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public async Task PatchCandidateIntentionStatementStatusById_setStatusId_Ok()
    {
        // Arrange
        var mappedCandidateIntentionStatement = new CandidateIntentionStatement()
        {
            Id = 41381, //would normally only be set after repository create method called
            Name = "Test",
            StatusId = RegistrationStatus.Draft.Id,
            FirstName = "First",
            MiddleName = "Middle",
            LastName = "Last",
            Email = "Email",
            PhoneNumberList = new()
            {
                PhoneNumbers = new() {
                    new() {Number= "**********", Type = "Home"},
                    new() {Number= "**********", Type = "Fax"}
                }
            },
            AddressList = new()
            {
                Addresses = new() {
                    new() { Street = "1234 Main St", City="Anytown", Country="United States", Purpose="Mailing", State="CA", Type="Home", Zip=(ZipCode)"12345"},
                    new() { Street = "9876 Main St", City="Plainsville", Country="United States", Purpose="Candidate", State="CA", Type="Business", Zip=(ZipCode)"12345"}
                }
            },

        };

        _modelMapper.MapCandidateIntentionStatementRequestToModel(Arg.Any<CandidateIntentionStatementRequest>())
            .Returns(mappedCandidateIntentionStatement);

        _registrationRepository.FindCandidateIntentionStatementById(Arg.Any<long>()).Returns(mappedCandidateIntentionStatement);

        _registrationRepository.Update(Arg.Any<Registration>())
            .Returns(mappedCandidateIntentionStatement);

        var request = new CandidateIntentionPatchDto.StatusRequest()
        {
            StatusId = 3
        };

        // Act
        var result = await _service.PatchCandidateIntentionStatementStatusById(41381, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.CompletedOk, Is.EqualTo(true));
    }

    #region LinkControlledCommitteeToCandidateIntentionStatement

    [Test]
    public async Task LinkControlledCommitteeToCandidateIntentionStatement_OnSuccess()
    {
        // Arrange
        var id = 1;
        var request = new ControlledCommitteeRequestDto()
        {
            IsRegistered = true,
            ControlledCommitteeFilerId = 1
        };
        var candidateIntentionResponse = new CandidateIntentionStatement()
        {
            Name = "Test",
            StatusId = 1,
            FilerId = 1
        };
        var user = new BasicUserDto(1, "Email", "First", "Last");
        var candidateIntention = _registrationRepository.FindCandidateIntentionStatementById(id).Returns(candidateIntentionResponse);
        _userMaintenanceSvc.GetCurrentUser().Returns(user);

        // Act
        await _service.LinkControlledCommitteeToCandidateIntentionStatement(id, request);

        // Assert
        await _filerLinkRepository.Received(1).LinkControlledCommitteeToCandidateIntentionStatement(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task LinkControlledCommitteeToCandidateIntentionStatement_Invalid_ReturnWorkFlowError()
    {
        // Arrange
        var id = 1;
        var request = new ControlledCommitteeRequestDto()
        {
            IsRegistered = null,
            ControlledCommitteeFilerId = null
        };

        var result = await _service.LinkControlledCommitteeToCandidateIntentionStatement(id, request);

        Assert.That(result, Has.Count.EqualTo(1));
    }

    [Test]
    public async Task LinkControlledCommitteeToCandidateIntentionStatement_Invalid_OnNullCandidateIntentionStatement()
    {
        // Arrange
        var id = 1;
        var request = new ControlledCommitteeRequestDto()
        {
            ControlledCommitteeFilerId = 1
        };

        _registrationRepository
            .FindCandidateIntentionStatementById(id)
            .Returns((CandidateIntentionStatement?)null);

        var result = await _service.LinkControlledCommitteeToCandidateIntentionStatement(id, request);

        Assert.That(result, Has.Count.EqualTo(1));
        Assert.That(result.Find(x => x.FieldName == string.Empty), Is.Not.Null);
    }
    [Test]
    public async Task LinkControlledCommitteeToCandidateIntentionStatement_Invalid_OnMissingControlledCommitteeFilerId()
    {
        // Arrange
        var id = 1;
        var request = new ControlledCommitteeRequestDto()
        {
            IsRegistered = true,
            ControlledCommitteeFilerId = null
        };
        var candidateIntentionResponse = new CandidateIntentionStatement()
        {
            Name = "Test",
            StatusId = 1,
            FilerId = 1
        };
        var candidateIntention = _registrationRepository.FindCandidateIntentionStatementById(id).Returns(candidateIntentionResponse);
        // Act
        var result = await _service.LinkControlledCommitteeToCandidateIntentionStatement(id, request);
        // Assert
        await _filerLinkRepository.Received(0).LinkControlledCommitteeToCandidateIntentionStatement(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>());
        Assert.That(result, Has.Count.EqualTo(1));
        Assert.That(result.Find(x => x.FieldName == "CommitteeFilerId"), Is.Not.Null);
    }

    #endregion

    #region SearchControlledCommitteeByIdOrName

    #endregion

    #region GetLinkedControlledCommittee

    [Test]
    public async Task GetLinkedControlledCommittee_OnSuccess()
    {
        // Arrange
        var id = 1;

        var candidateIntentionResponse = new CandidateIntentionStatement()
        {
            Id = id,
            Name = "Test",
            Email = "Email",
            StatusId = 1,
            FilerId = 1
        };
        var filerLinkResponse = new FilerLink()
        {
            FilerId = 1,
            LinkedEntityId = 1,
            FilerLinkTypeId = FilerLinkType.ControlledCommittee.Id,
            EffectiveDate = _dateNow,
            CreatedBy = 1,
            ModifiedBy = 1
        };
        var controlledCommitteeRepsonse = new CandidateControlledCommittee()
        {
            Name = "test",
            Email = "",
            StatusId = 1,
            JurisdictionCounty = "",
            JurisdictionActive = "",
            FinancialInstitutionAccountNumber = "",
            FinancialInstitutionName = "",
            FinancialInstitutionPhone = "",
            ElectionOfficeSought = "",
            ElectionDistrictNumber = "",
            ElectionId = 0,
        };
        _registrationRepository.FindCandidateIntentionStatementById(id).Returns(candidateIntentionResponse);
        _filerLinkRepository.FindByFilerIdAndLinkType(1, (int)FilerLinkType.ControlledCommittee.Id).Returns(filerLinkResponse);
        _registrationRepository.GetControlledCommitteeByFilerId(1).Returns(controlledCommitteeRepsonse);

        // Act
        var result = await _service.GetLinkedControlledCommittee(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        await _registrationRepository.Received(1).FindCandidateIntentionStatementById(id);
    }

    [Test]
    public async Task GetLinkedControlledCommittee_OnSuccess_EmptyObject()
    {
        // Arrange
        var id = 1;

        var candidateIntentionResponse = new CandidateIntentionStatement()
        {
            Id = id,
            Name = "Test",
            Email = "Email",
            StatusId = 1,
            FilerId = 1
        };

        var controlledCommitteeRepsonse = new CandidateIntentionStatement()
        {
            Name = "test",
            StatusId = 1,
        };
        _registrationRepository.FindCandidateIntentionStatementById(id).Returns(candidateIntentionResponse);
        _filerLinkRepository.FindByFilerIdAndLinkType(1, (int)FilerLinkType.ControlledCommittee.Id).Returns((FilerLink?)null);
        _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(1).Returns(controlledCommitteeRepsonse);

        // Act
        var result = await _service.GetLinkedControlledCommittee(id);

        // Assert
        Assert.That(result?.Id, Is.Null);
        await _registrationRepository.Received(1).FindCandidateIntentionStatementById(id);

    }

    [Test]
    public void GetLinkedControlledCommittee_Invalid_OnNullCandidateIntentionStatement()
    {
        // Arrange
        var id = 1;

        _registrationRepository
            .FindCandidateIntentionStatementById(id)
            .Returns((CandidateIntentionStatement?)null);

        Assert.ThrowsAsync<BadRequestException>(() =>
             _service.GetLinkedControlledCommittee(id));
    }

    #endregion

    [Test]
    public void SubmitCandidateIntentionStatement_RegistrationNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        const long id = 99999;

        _registrationRepository
            .FindCandidateIntentionStatementWithElectionById(id)
            .Returns(Task.FromResult((CandidateIntentionStatement?)null));

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(
            async () => await _service.SubmitCandidateIntentionStatement(id)
        );

        Assert.That(ex.Message, Is.EqualTo($"Registration not Found Id={id}"));
    }

    /// <summary>
    /// Should throw if no expenditure record is found.
    /// </summary>
    [Test]
    public void GetCandidateIntentionStatementExpenditureExpenseAmount_ThrowsWhenNotFound()
    {
        // Arrange
        const long id = 123;
        _registrationRepository
            .FindExpenditureExpenseAmount(id)
            .Returns((ExpenditureExpenseAmount?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.GetCandidateIntentionStatementExpenditureExpenseAmount(id)
        );
        Assert.That(ex.Message, Is.EqualTo($"Id {id} not found."));
    }

    /// <summary>
    /// Should return PrimarySpecialAmount when election type is Primary or Special (case‑insensitive).
    /// </summary>
    [TestCase("Primary")]
    [TestCase("SPECIAL")]
    public async Task GetCandidateIntentionStatementExpenditureExpenseAmount_ReturnsPrimarySpecial_ForPrimaryOrSpecial(string electionName)
    {
        // Arrange
        const long id = 456;
        var amounts = new ExpenditureExpenseAmount
        {
            PrimarySpecialAmount = (Models.FilerDisclosure.Transactions.Currency)150.5m,
            GeneralRunoffAmount = (Models.FilerDisclosure.Transactions.Currency)999.9m
        };
        _registrationRepository.FindExpenditureExpenseAmount(id).Returns(amounts);
        _registrationRepository.FindElectionType(id).Returns(new ElectionType { Name = electionName });

        // Act
        var result = await _service.GetCandidateIntentionStatementExpenditureExpenseAmount(id);

        // Assert
        Assert.That(result, Is.EqualTo(150.5m));
    }

    /// <summary>
    /// Should return GeneralRunoffAmount when election type is General or Runoff.
    /// </summary>
    [TestCase("General")]
    [TestCase("runoff")]
    public async Task GetCandidateIntentionStatementExpenditureExpenseAmount_ReturnsGeneralRunoff_ForGeneralOrRunoff(string electionName)
    {
        // Arrange
        const long id = 789;
        var amounts = new ExpenditureExpenseAmount
        {
            PrimarySpecialAmount = (Models.FilerDisclosure.Transactions.Currency)1.1m,
            GeneralRunoffAmount = (Models.FilerDisclosure.Transactions.Currency)2.2m
        };
        _registrationRepository.FindExpenditureExpenseAmount(id).Returns(amounts);
        _registrationRepository.FindElectionType(id).Returns(new ElectionType { Name = electionName });

        // Act
        var result = await _service.GetCandidateIntentionStatementExpenditureExpenseAmount(id);

        // Assert
        Assert.That(result, Is.EqualTo(2.2m));
    }

    /// <summary>
    /// Should throw if the election type is neither primary/special nor general/runoff.
    /// </summary>
    [Test]
    public void GetCandidateIntentionStatementExpenditureExpenseAmount_ThrowsWhenElectionTypeInvalid()
    {
        // Arrange
        const long id = 321;
        var amounts = new ExpenditureExpenseAmount
        {
            PrimarySpecialAmount = (Models.FilerDisclosure.Transactions.Currency)10m,
            GeneralRunoffAmount = (Models.FilerDisclosure.Transactions.Currency)20m
        };
        _registrationRepository.FindExpenditureExpenseAmount(id).Returns(amounts);
        _registrationRepository.FindElectionType(id).Returns(new ElectionType { Name = "UnknownType" });

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.GetCandidateIntentionStatementExpenditureExpenseAmount(id)
        );
    }

    /// <summary>
    /// GetCandidateIntentionStatementElection should return whatever the repository returns (including null).
    /// </summary>
    [Test]
    public async Task GetCandidateIntentionStatementElection_ReturnsElectionOrNull()
    {
        const long id = 101;

        // case 1: repository returns an Election
        var expectedElection = new Election { Id = 5, Name = "Test Election", ElectionDate = new DateTime(2024, 12, 20) };
        _registrationRepository.FindElectionByRegistrationId(id).Returns(expectedElection);

        var actual = await _service.GetCandidateIntentionStatementElection(id);
        Assert.That(actual, Is.SameAs(expectedElection));

        // case 2: repository returns null
        _registrationRepository.FindElectionByRegistrationId(id).Returns((Election?)null);

        var actualNull = await _service.GetCandidateIntentionStatementElection(id);
        Assert.That(actualNull, Is.Null);
    }

    #region Efile Submission

    /// <summary>
    /// 
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitCandidateIntentionStatementForEfile_Amendment_PopulatesElectionFields()
    {
        // Arrange
        var parentId = 123L;
        var raceId = 888L;
        var expectedOffice = "State Treasurer";
        var expectedAgency = "Dept. of Finance";
        var expectedDistrictNumber = 7;
        List<PoliticalParty> politicalParties = new();

        // minimal original so amendment branch runs
        CandidateIntentionStatement? original = new()
        {
            Id = parentId,
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "test"
        };

        _registrationRepository.FindCandidateIntentionStatementById(parentId)
            .Returns(Task.FromResult<CandidateIntentionStatement?>(original));

        var submission = CreateValidEfileSubmission();
        submission.Amendment = new EfileAmendment
        {
            IsAmendment = true,
            SupercededFilingId = parentId,
            DescriptionOfAmendment = "Fix typo"
        };

        var dto = new CandidateIntentionStatementDto
        {
            Race = new ElectionRace { Id = raceId },
            Registration = new CandidateIntentionStatement
            {
                ParentId = parentId,
                StatusId = 1,
                FirstName = submission.CandidateInformation.FirstName!,
                LastName = submission.CandidateInformation.LastName!,
                ElectionJurisdiction = submission.CandidateInformation.ElectionJurisdiction,
                Email = submission.CandidateInformation.Email,
                ExpenditureLimitAccepted = submission.StateCandidateExpenditureLimit.ExpenditureLimitAccepted,
                ExpenditureExceeded = submission.StateCandidateExpenditureLimit.ExpenditureExceeded,
                SelfRegister = true,
                Name = submission.Attestation.Signature,
                SubmittedAt = _dateNow,
                PhoneNumberList = new PhoneNumberList { PhoneNumbers = new List<PhoneNumber> { new() { Number = "+1" + submission.CandidateInformation.Phone, Type = "Home" } } },
                AddressList = new AddressList
                {
                    Addresses = new List<Models.Common.Address> {
                        new(){ Street = submission.CandidateInformation.CandidateAddress.Street, City = submission.CandidateInformation.CandidateAddress.City, Country = submission.CandidateInformation.CandidateAddress.Country, Purpose="Candidate", State = submission.CandidateInformation.CandidateAddress.State, Type = submission.CandidateInformation.CandidateAddress.Type, Zip = (ZipCode)submission.CandidateInformation.CandidateAddress.ZipCode },
                        new(){ Street = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).Street, City = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).City, Country = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).Country, Purpose="Mailing", State = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).State, Type = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).Type, Zip = (ZipCode)(submission.CandidateInformation.MailingAddress?.ZipCode ?? submission.CandidateInformation.CandidateAddress.ZipCode) }
                    }
                },
                VerificationSignature = submission.Attestation.Signature,
                VerificationExecutedAt = submission.Attestation.ExecutedAt
            },
            UserId = 10004
        };

        // stub our amendment‐ElectionRace
        var race = new ElectionRace
        {
            Office = new Office { Name = expectedOffice, AgencyName = expectedAgency },
            Election = new Election { ElectionDate = _dateNow, Name = "primary" },
            District = new District { DistrictNumber = expectedDistrictNumber }
        };

        _electionRaceRepository
            .GetElectionRaceByIdAsync(raceId)
            .Returns(race);

        CandidateIntentionStatement? created = null;
        _registrationRepository
            .Create(Arg.Do<CandidateIntentionStatement>(c => created = c))
            .Returns(_mappedCandidateIntentionStatement);

        _politicalPartyRepository.GetAll().Returns(_allPoliticalParties);

        // Act
        RegistrationResponseDto? resp = await _service.SubmitCandidateIntentionStatementForEfile(dto);

        // Assert
        Assert.That(resp, Is.Not.Null);
        Assert.That(resp.ValidationErrors, Is.Null.Or.Empty);
    }

    /// <summary>
    /// Test a complete and successful submission of the Candidate Intention Statement (form 501)
    /// through the Submit...ForEfile() method.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitCandidateIntentionStatementForEfile_Valid()
    {
        // Arrange
        EfileCandidateIntentionStatement stmt = CreateValidEfileSubmission();

        // Under normal circumstances this timestamp would come from the exact date/time of the HTTP
        // request to the Efile Validation function.
        DateTime receivedAt = new(2026, 3, 6, 0, 0, 0, 0);

        Business.Efile.Model.EfileAddress candidateAddress = stmt.CandidateInformation.CandidateAddress;
        Business.Efile.Model.EfileAddress? mailingAddress = stmt.CandidateInformation.MailingAddress
            ?? stmt.CandidateInformation.CandidateAddress;

        // Load the DTO object to send to the service from the object above
        CandidateIntentionStatementDto submission = new()
        {
            Race = new ElectionRace
            {
                Id = stmt.CandidateInformation.ElectionRaceId
            },
            Registration = new CandidateIntentionStatement
            {
                ContributedPersonalExcessFundsOn = stmt.StateCandidateExpenditureLimit.ContributedPersonalExcessFundsOn,
                FirstName = stmt.CandidateInformation.FirstName ?? "",
                LastName = stmt.CandidateInformation.LastName ?? "",
                ElectionJurisdiction = stmt.CandidateInformation.ElectionJurisdiction,
                Email = stmt.CandidateInformation.Email,
                ExpenditureLimitAccepted = stmt.StateCandidateExpenditureLimit.ExpenditureLimitAccepted,
                ExpenditureExceeded = stmt.StateCandidateExpenditureLimit.ExpenditureExceeded,
                ParentId = stmt.Amendment.SupercededFilingId,
                PoliticalParty = new PoliticalParty
                {
                    Name = stmt.CandidateInformation.PartyAffiliation ?? ""
                },
                Name = stmt.Attestation.Signature,

                // SelfRegister is always true for Efile submission, indicating that it must be attested at the time of submission.
                SelfRegister = true,

                StatusId = RegistrationStatus.Draft.Id,
                SubmittedAt = _dateNow,
                PhoneNumberList = new PhoneNumberList
                {
                    PhoneNumbers = new List<PhoneNumber> {
                        new() {
                            Number = string.IsNullOrWhiteSpace(stmt.CandidateInformation.Phone)
                                ? "" : "+" + stmt.CandidateInformation.Phone,
                            Type = "Home"
                        }
                    }
                },
                AddressList = new AddressList
                {
                    Addresses = new List<Models.Common.Address> {
                        new() {
                            Street = candidateAddress.Street,
                            City = candidateAddress.City,
                            Country = candidateAddress.Country,
                            Purpose = "Candidate",
                            State = candidateAddress.State,
                            Type = candidateAddress.Type,
                            Zip = (ZipCode)candidateAddress.ZipCode
                        },
                        new() {
                            Street = mailingAddress.Street,
                            City = mailingAddress.City,
                            Country = mailingAddress.Country,
                            Purpose = "Mailing",
                            State = mailingAddress.State,
                            Type = mailingAddress.Type,
                            Zip = (ZipCode)mailingAddress.ZipCode
                        },
                    }
                },
                VerificationSignature = stmt.Attestation.Signature,
                VerificationExecutedAt = stmt.Attestation.ExecutedAt,
            },
            UserId = 10004
        };

        DecisionsCandidateIntentionStatementSubmissionResponse? decisionResponse = new()
        {
            Error = new()
        };

        _registrationRepository.Create(default)
            .ReturnsForAnyArgs(_mappedCandidateIntentionStatement);

        _registrationRepository.Update(Arg.Any<CandidateIntentionStatement>())
            .Returns(_UpdatedmappedCandidateIntentionStatement);

        _electionRaceRepository.FindById(default)
            .ReturnsForAnyArgs(_minimalElectionRace);

        _electionRaceRepository.GetElectionRaceByIdAsync(default)
            .ReturnsForAnyArgs(_defaultElectionRace);

        _politicalPartyRepository.GetAll().Returns(_allPoliticalParties);

        _decisionsSvc.InitiateWorkflow<DecisionsCandidateIntentionStatementSubmission, DecisionsCandidateIntentionStatementSubmissionResponse>(default, default, default)
            .ReturnsForAnyArgs(Task.FromResult(decisionResponse));

        // Act
        RegistrationResponseDto? result = await _service.SubmitCandidateIntentionStatementForEfile(submission);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ValidationErrors, Is.Null.Or.Empty);
    }

    /// <summary>
    /// Test a complete and successful submission of the Candidate Intention Statement (form 501)
    /// through the Submit...ForEfile() method.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitCandidateIntentionStatementForEfile_ValidAmendment()
    {
        // Arrange
        long parentId = 1;
        EfileCandidateIntentionStatement stmt = CreateValidEfileSubmission();
        CandidateIntentionStatement original = CreateOriginalCandidateIntentionStatement();
        stmt.Amendment = new EfileAmendment
        {
            IsAmendment = true,
            SupercededFilingId = parentId,
            DescriptionOfAmendment = "Description of Amendment"
        };

        // Under normal circumstances this timestamp would come from the exact date/time of the HTTP
        // request to the Efile Validation function.
        DateTime receivedAt = new(2026, 3, 6, 0, 0, 0, 0);

        Business.Efile.Model.EfileAddress candidateAddress = stmt.CandidateInformation.CandidateAddress;
        Business.Efile.Model.EfileAddress? mailingAddress = stmt.CandidateInformation.MailingAddress
            ?? stmt.CandidateInformation.CandidateAddress;

        // Load the DTO object to send to the service from the object above
        CandidateIntentionStatementDto submission = new()
        {
            Race = new ElectionRace
            {
                Id = stmt.CandidateInformation.ElectionRaceId
            },
            Registration = new CandidateIntentionStatement
            {
                ContributedPersonalExcessFundsOn = stmt.StateCandidateExpenditureLimit.ContributedPersonalExcessFundsOn,
                FirstName = stmt.CandidateInformation.FirstName ?? "",
                LastName = stmt.CandidateInformation.LastName ?? "",
                ElectionJurisdiction = stmt.CandidateInformation.ElectionJurisdiction,
                Email = stmt.CandidateInformation.Email,
                ExpenditureLimitAccepted = stmt.StateCandidateExpenditureLimit.ExpenditureLimitAccepted,
                ExpenditureExceeded = stmt.StateCandidateExpenditureLimit.ExpenditureExceeded,
                ParentId = stmt.Amendment.SupercededFilingId,
                PoliticalParty = new PoliticalParty
                {
                    Name = stmt.CandidateInformation.PartyAffiliation ?? ""
                },
                Name = stmt.Attestation.Signature,

                // SelfRegister is always true for Efile submission, indicating that it must be attested at the time of submission.
                SelfRegister = true,

                StatusId = RegistrationStatus.Draft.Id,
                SubmittedAt = _dateNow,
                PhoneNumberList = new PhoneNumberList
                {
                    PhoneNumbers = new List<PhoneNumber> {
                        new() {
                            Number = string.IsNullOrWhiteSpace(stmt.CandidateInformation.Phone)
                                ? "" : "+" + stmt.CandidateInformation.Phone,
                            Type = "Home"
                        }
                    }
                },
                AddressList = new AddressList
                {
                    Addresses = new List<Models.Common.Address> {
                        new() {
                            Street = candidateAddress.Street,
                            City = candidateAddress.City,
                            Country = candidateAddress.Country,
                            Purpose = "Candidate",
                            State = candidateAddress.State,
                            Type = candidateAddress.Type,
                            Zip = (ZipCode)candidateAddress.ZipCode
                        },
                        new() {
                            Street = mailingAddress.Street,
                            City = mailingAddress.City,
                            Country = mailingAddress.Country,
                            Purpose = "Mailing",
                            State = mailingAddress.State,
                            Type = mailingAddress.Type,
                            Zip = (ZipCode)mailingAddress.ZipCode
                        },
                    }
                },
                VerificationSignature = stmt.Attestation.Signature,
                VerificationExecutedAt = stmt.Attestation.ExecutedAt,
            },
            UserId = 10004
        };

        _registrationRepository.Create(default)
            .ReturnsForAnyArgs(_mappedCandidateIntentionStatement);

        _registrationRepository.Update(Arg.Any<CandidateIntentionStatement>())
            .Returns(_UpdatedmappedCandidateIntentionStatement);

        _electionRaceRepository.FindById(default)
            .ReturnsForAnyArgs(_minimalElectionRace);

        _electionRaceRepository.GetElectionRaceByIdAsync(default)
            .ReturnsForAnyArgs(_defaultElectionRace);

        _politicalPartyRepository.GetAll().Returns(_allPoliticalParties);

        _registrationRepository.FindCandidateIntentionStatementById(parentId)
            .Returns(original);

        // Act
        RegistrationResponseDto? result = await _service.SubmitCandidateIntentionStatementForEfile(submission);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ValidationErrors, Is.Null.Or.Empty);
    }

    /// <summary>
    /// Test a the non-candidate flow of the private ValidateCandidateIntentionStatementSubmission
    /// method.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task SubmitCandidateIntentionStatementForEfileNonCandidateFlow()
    {
        // Arrange
        EfileCandidateIntentionStatement stmt = CreateValidEfileSubmission();

        // Under normal circumstances this timestamp would come from the exact date/time of the HTTP
        // request to the Efile Validation function.
        DateTime receivedAt = new(2026, 3, 6, 0, 0, 0, 0);

        Business.Efile.Model.EfileAddress candidateAddress = stmt.CandidateInformation.CandidateAddress;
        Business.Efile.Model.EfileAddress? mailingAddress = stmt.CandidateInformation.MailingAddress
            ?? stmt.CandidateInformation.CandidateAddress;

        // Load the DTO object to send to the service from the object above
        CandidateIntentionStatementDto submission = new()
        {
            Race = new ElectionRace
            {
                Id = stmt.CandidateInformation.ElectionRaceId
            },
            Registration = new CandidateIntentionStatement
            {
                ContributedPersonalExcessFundsOn = stmt.StateCandidateExpenditureLimit.ContributedPersonalExcessFundsOn,
                FirstName = stmt.CandidateInformation.FirstName ?? "",
                LastName = stmt.CandidateInformation.LastName ?? "",
                ElectionJurisdiction = stmt.CandidateInformation.ElectionJurisdiction,
                Email = stmt.CandidateInformation.Email,
                ExpenditureLimitAccepted = stmt.StateCandidateExpenditureLimit.ExpenditureLimitAccepted,
                ExpenditureExceeded = stmt.StateCandidateExpenditureLimit.ExpenditureExceeded,
                ParentId = stmt.Amendment.SupercededFilingId,
                PoliticalParty = new PoliticalParty
                {
                    Name = stmt.CandidateInformation.PartyAffiliation ?? ""
                },
                Name = stmt.Attestation.Signature,

                // SelfRegister FALSE will trigger the non-candidate flow
                SelfRegister = false,

                StatusId = RegistrationStatus.Draft.Id,
                SubmittedAt = _dateNow,
                PhoneNumberList = new PhoneNumberList
                {
                    PhoneNumbers = new List<PhoneNumber> {
                        new() {
                            Number = string.IsNullOrWhiteSpace(stmt.CandidateInformation.Phone)
                                ? "" : "+" + stmt.CandidateInformation.Phone,
                            Type = "Home"
                        }
                    }
                },
                AddressList = new AddressList
                {
                    Addresses = new List<Models.Common.Address> {
                        new() {
                            Street = candidateAddress.Street,
                            City = candidateAddress.City,
                            Country = candidateAddress.Country,
                            Purpose = "Candidate",
                            State = candidateAddress.State,
                            Type = candidateAddress.Type,
                            Zip = (ZipCode)candidateAddress.ZipCode
                        },
                        new() {
                            Street = mailingAddress.Street,
                            City = mailingAddress.City,
                            Country = mailingAddress.Country,
                            Purpose = "Mailing",
                            State = mailingAddress.State,
                            Type = mailingAddress.Type,
                            Zip = (ZipCode)mailingAddress.ZipCode
                        },
                    }
                },
                VerificationSignature = stmt.Attestation.Signature,
                VerificationExecutedAt = stmt.Attestation.ExecutedAt,
            },
            UserId = 10004
        };

        _registrationRepository.Create(default)
            .ReturnsForAnyArgs(_mappedCandidateIntentionStatement);

        _registrationRepository.Update(Arg.Any<CandidateIntentionStatement>())
            .Returns(_UpdatedmappedCandidateIntentionStatement);

        _electionRaceRepository.FindById(default)
            .ReturnsForAnyArgs(_minimalElectionRace);

        _electionRaceRepository.GetElectionRaceByIdAsync(default)
            .ReturnsForAnyArgs(_defaultElectionRace);

        _politicalPartyRepository.GetAll().Returns(_allPoliticalParties);

        // Act
        RegistrationResponseDto? result = await _service.SubmitCandidateIntentionStatementForEfile(submission);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ValidationErrors, Is.Null.Or.Empty);
    }

    /// <summary>
    /// Verify ArgumentNullException if required object is null.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateIntentionStatementForEfile_ThrowIfRaceIsNull()
    {
        // Arrange
        CandidateIntentionStatementDto submission = new()
        {
            Race = null
        };

        // Act & Assert
        Assert.That(
            () => _service.SubmitCandidateIntentionStatementForEfile(submission),
            Throws.TypeOf<ArgumentNullException>()
        );
    }

    /// <summary>
    /// Verify ArgumentNullException if required object is null.
    /// </summary>
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void SubmitCandidateIntentionStatementForEfile_ThrowIfRegistrationIsNull()
    {
        // Arrange
        CandidateIntentionStatementDto submission = new()
        {
            Registration = null
        };

        // Act & Assert
        Assert.That(
            () => _service.SubmitCandidateIntentionStatementForEfile(submission),
            Throws.TypeOf<ArgumentNullException>()
        );
    }

    #endregion

    /// <summary>
    /// UpdateCandidateIntentionStatement should throw if no registration exists with that id.
    /// </summary>
    [Test]
    public void UpdateCandidateIntentionStatement_ThrowsWhenRegistrationNotFound()
    {
        const long id = 202;
        _registrationRepository.FindCandidateIntentionStatementById(id).Returns((CandidateIntentionStatement?)null);
        var request = new CandidateIntentionStatementRequest();

        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.UpdateCandidateIntentionStatement(id, request)
        );
        Assert.That(ex.Message, Is.EqualTo($"Registration not Found Id={id}"));
    }

    /// <summary>
    /// UpdateCandidateIntentionStatement should return Valid=false and NOT call Update(...) when decisions return errors.
    /// </summary>
    [Test]
    public async Task UpdateCandidateIntentionStatement_ReturnsInvalidWhenDecisionsReturnErrors()
    {
        // Arrange
        const long id = 303;
        var existing = new CandidateIntentionStatement { Id = id, StatusId = 99, Name = "Test" };
        _registrationRepository.FindCandidateIntentionStatementById(id).Returns(existing);

        var request = new CandidateIntentionStatementRequest { CheckRequiredFieldsFlag = true };

        _modelMapper.UpdateCandidateIntentionStatement(existing, request).Returns(existing);

        var errors = new List<WorkFlowError>
        {
            new("F","C","T","m")
        };


        _decisionsSvc
            .InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationRuleset,
                Arg.Any<DecisionsCandidateIntentionStatement>(),
                true
            ).Returns(Task.FromResult(errors));

        // Act
        var result = await _service.UpdateCandidateIntentionStatement(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EquivalentTo(errors));
            Assert.That(result.StatusId, Is.EqualTo(existing.StatusId));
        });

        await _registrationRepository.DidNotReceive()
            .Update(Arg.Any<CandidateIntentionStatement>());
    }

    /// <summary>
    /// UpdateCandidateIntentionStatement should return Valid=true and call Update(...) when decisions return no errors.
    /// </summary>
    [Test]
    public async Task UpdateCandidateIntentionStatement_ReturnsValidAndUpdatesWhenNoDecisionErrors()
    {
        const long id = 404;
        var existing = new CandidateIntentionStatement { Id = id, StatusId = 7, Name = "Test" };
        var request = new CandidateIntentionStatementRequest { CheckRequiredFieldsFlag = false };
        _modelMapper.UpdateCandidateIntentionStatement(existing, request).Returns(existing);

        _registrationRepository
            .FindCandidateIntentionStatementById(id)
            .Returns(existing);

        _decisionsSvc
            .InitiateWorkflow<DecisionsCandidateIntentionStatement, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationRuleset,
                Arg.Any<DecisionsCandidateIntentionStatement>(),
                false
            )
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        Registration asBase = existing;
        Task<Registration> updateResult = Task.FromResult(asBase);

        _registrationRepository
            .Update(Arg.Any<CandidateIntentionStatement>())
            .Returns(updateResult);

        // Act
        var result = await _service.UpdateCandidateIntentionStatement(
            id,
            request
        );

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(existing.StatusId));
        });

        // And verify Update was called
        await _registrationRepository.Received(1)
            .Update(Arg.Is<CandidateIntentionStatement>(r => r.Id == id));
    }

    /// <summary>
    /// Cancel should throw if no registration is found.
    /// </summary>
    [Test]
    public void CancelCandidateIntentionStatement_ThrowsWhenNotFound()
    {
        const long id = 11;

        // Arrange
        _registrationRepository
            .FindById(id)
            .Returns((CandidateIntentionStatement?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.CancelCandidateIntentionStatement(id)
        );
        Assert.That(ex.Message, Is.EqualTo($"Registration with ID {id} not found."));
    }

    /// <summary>
    /// Cancel should throw if the registration is not in Draft status.
    /// </summary>
    [Test]
    public void CancelCandidateIntentionStatement_ThrowsWhenNotDraft()
    {
        // Arrange
        const long id = 22;
        var reg = new CandidateIntentionStatement
        {
            Id = id,
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Test"
        };

        _registrationRepository
            .FindById(id)
            .Returns(reg);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _service.CancelCandidateIntentionStatement(id)
        );

        Assert.That(
            ex.Message,
            Is.EqualTo($"Cannot cancel a registration that is not in 'Draft' status. Id={id} Status={reg.StatusId}")
        );
    }

    /// <summary>
    /// Cancel should update the StatusId to Canceled when in Draft status.
    /// </summary>
    [Test]
    public async Task CancelCandidateIntentionStatement_UpdatesStatusWhenDraft()
    {
        const long id = 33;
        var reg = new CandidateIntentionStatement
        {
            Id = id,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test"
        };

        // Arrange
        _registrationRepository
            .FindById(id)
            .Returns(reg);

        Task<Registration> updateReturn = Task.FromResult<Registration>(reg);
        _registrationRepository
            .UpdateProperty<long>(
                Arg.Any<CandidateIntentionStatement>(),
                Arg.Any<Expression<Func<Registration, long>>>(),
                RegistrationStatus.Canceled.Id
            )
            .Returns(updateReturn);

        // Act
        await _service.CancelCandidateIntentionStatement(id);

        //Assert 
        await _registrationRepository.Received(1)
            .UpdateProperty<long>(
                Arg.Is<CandidateIntentionStatement>(r => r.Id == id),
                Arg.Any<Expression<Func<Registration, long>>>(),
                RegistrationStatus.Canceled.Id
            );
    }

    [Test]
    public void UpdateCandidateIntentionStatementExpenditureLimit_RequestNull_ThrowsBadRequestException()
    {
        // Arrange
        const long id = 123;
        _registrationRepository
            .FindCandidateIntentionStatementById(id)
            .Returns(new CandidateIntentionStatement { Id = id, StatusId = 1, Name = "Test" });

        // Act / Assert
        Assert.ThrowsAsync<BadRequestException>(
            async () => await _service.UpdateCandidateIntentionStatementExpenditureLimit(id, null!));
    }

    [Test]
    public void UpdateCandidateIntentionStatementExpenditureLimit_RegistrationNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        const long id = 42;
        _registrationRepository
            .FindCandidateIntentionStatementById(id)
            .Returns((CandidateIntentionStatement?)null);

        var req = new CandidateIntentionStatementExpenditureLimitRequest();

        // Act / Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.UpdateCandidateIntentionStatementExpenditureLimit(id, req));

        Assert.That(ex.Message, Is.EqualTo($"Registration not Found Id={id}"));
    }

    [Test]
    public void UpdateCandidateIntentionStatementExpenditureLimit_NoExpenditureData_ThrowsKeyNotFoundException()
    {
        // Arrange
        const long id = 99;
        var reg = new CandidateIntentionStatement { Id = id, StatusId = 1, Name = "Test" };
        _registrationRepository
            .FindCandidateIntentionStatementById(id)
            .Returns(reg);

        _registrationRepository
            .FindExpenditureExpenseAmount(id)
            .Returns((ExpenditureExpenseAmount?)null);

        var req = new CandidateIntentionStatementExpenditureLimitRequest();

        // Act / Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.UpdateCandidateIntentionStatementExpenditureLimit(id, req));

        Assert.That(ex.Message, Is.EqualTo($"Id {id} not found."));
    }

    [Test]
    public async Task UpdateCandidateIntentionStatementExpenditureLimit_DecisionsReturnErrors_DoesNotUpdateAndReturnsInvalid()
    {
        // Arrange
        const long id = 7;
        var reg = new CandidateIntentionStatement
        {
            Id = id,
            StatusId = 1,
            Name = "Test",
            ElectionRace = new ElectionRace { Office = new Office { Name = "Treasurer" } }
        };
        _registrationRepository.FindCandidateIntentionStatementById(id).Returns(reg);

        var amounts = new ExpenditureExpenseAmount
        {
            PrimarySpecialAmount = (Currency)50m,
            GeneralRunoffAmount = (Currency)75m
        };
        _registrationRepository.FindExpenditureExpenseAmount(id).Returns(amounts);
        _registrationRepository.FindElectionType(id).Returns(new ElectionType { Name = "Primary" });


        var errors = new List<WorkFlowError> { new("X", "C", "T", "failed") };
        _decisionsSvc
            .InitiateWorkflow<DecisionsExpenditureLimit, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationElectionLimitRuleset,
                Arg.Any<DecisionsExpenditureLimit>(),
                Arg.Any<bool>())
            .Returns(Task.FromResult(errors));

        var req = new CandidateIntentionStatementExpenditureLimitRequest
        {
            ExpenditureLimitAccepted = true,
            CheckRequiredFieldsFlag = true
        };

        // Act
        var result = await _service.UpdateCandidateIntentionStatementExpenditureLimit(id, req);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Valid, Is.False, "Expected Valid == false when there are workflow errors");
            Assert.That(result.ValidationErrors, Is.EquivalentTo(errors));
        });
        await _registrationRepository.DidNotReceive().Update(Arg.Any<CandidateIntentionStatement>());
    }

    [Test]
    public async Task UpdateCandidateIntentionStatementExpenditureLimit_NoErrors_UpdatesAndReturnsValid()
    {
        // Arrange
        const long id = 88;
        var reg = new CandidateIntentionStatement
        {
            Id = id,
            StatusId = 1,
            Name = "Test",
            ElectionRace = new ElectionRace { Office = new Office { Name = "Auditor" } }
        };
        _registrationRepository.FindCandidateIntentionStatementById(id).Returns(reg);

        var amounts = new ExpenditureExpenseAmount
        {
            PrimarySpecialAmount = (Currency)123.45m,
            GeneralRunoffAmount = (Currency)200m
        };
        _registrationRepository.FindExpenditureExpenseAmount(id).Returns(amounts);
        _registrationRepository.FindElectionType(id).Returns(new ElectionType { Name = "Primary" });

        _decisionsSvc
            .InitiateWorkflow<DecisionsExpenditureLimit, List<WorkFlowError>>(
                DecisionsWorkflow.CandidateInformationElectionLimitRuleset,
                Arg.Any<DecisionsExpenditureLimit>(),
                Arg.Any<bool>())
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        _registrationRepository
            .Update(Arg.Any<CandidateIntentionStatement>())
            .Returns(call => call.Arg<CandidateIntentionStatement>());

        var request = new CandidateIntentionStatementExpenditureLimitRequest
        {
            ExpenditureLimitAccepted = false,
            ContributedPersonalExcessFundsOn = new DateTime(2025, 5, 1),
            ExpenditureExceeded = true,
            CheckRequiredFieldsFlag = false
        };

        // Act
        var result = await _service.UpdateCandidateIntentionStatementExpenditureLimit(id, request);

        Assert.Multiple(() =>
        {
            // Assert DTO
            Assert.That(result.Valid, "Expected Valid == true when there are no workflow errors");
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.Id, Is.EqualTo(id));
        });

        await _registrationRepository.Received(1)
            .Update(Arg.Is<CandidateIntentionStatement>(r =>
                r.ExpenditureCeilingAmount == 123.45m &&
                r.ExpenditureLimitAccepted == request.ExpenditureLimitAccepted &&
                r.ContributedPersonalExcessFundsOn == request.ContributedPersonalExcessFundsOn &&
                r.ExpenditureExceeded == request.ExpenditureExceeded
            ));
    }

    /// <summary>
    /// Verify that a specific exception is thrown if the registration is not in Draft status.
    /// </summary>
    [Test, Category("Unit")]
    public void ValidateCandidateIntentionStatementSubmissionFailsIfNotDraft()
    {
        // Arrange
        CandidateIntentionStatement registration = CreateOriginalCandidateIntentionStatement();
        registration.StatusId = RegistrationStatus.Terminated.Id;

        // Act
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() =>
            PrivateMemberAccessor.InvokePrivateAsync<RegistrationResponseDto>(
                _service,
                "ValidateCandidateIntentionStatementSubmission",
                [registration, false]));

        // Assert
        Assert.That(ex, Is.Not.Null);
        Assert.That(ex.Message, Does.Contain("Cannot submit a registration that is not in 'Draft' status."));
    }

    [Test, Category("Unit")]
    public async Task SubmitCandidateIntentionStatementForEfile_InitialFiling_PopulatesElectionFields()
    {
        // Arrange
        var raceId = 999L;
        var expectedOffice = "Lieutenant Governor";
        var expectedAgency = "State Executive";
        var expectedDistrictNumber = 42;
        var submission = CreateValidEfileSubmission();
        var dto = new CandidateIntentionStatementDto
        {
            Race = new ElectionRace { Id = raceId },
            Registration = new CandidateIntentionStatement
            {

                FirstName = submission.CandidateInformation.FirstName!,
                StatusId = 1,
                LastName = submission.CandidateInformation.LastName!,
                ElectionJurisdiction = submission.CandidateInformation.ElectionJurisdiction,
                Email = submission.CandidateInformation.Email,
                ExpenditureLimitAccepted = submission.StateCandidateExpenditureLimit.ExpenditureLimitAccepted,
                ExpenditureExceeded = submission.StateCandidateExpenditureLimit.ExpenditureExceeded,
                SelfRegister = true,
                Name = submission.Attestation.Signature,
                SubmittedAt = _dateNow,
                PhoneNumberList = new PhoneNumberList { PhoneNumbers = new List<PhoneNumber> { new() { Number = "+1" + submission.CandidateInformation.Phone, Type = "Home" } } },
                AddressList = new AddressList
                {
                    Addresses = new List<Models.Common.Address>{
                new(){ Street = submission.CandidateInformation.CandidateAddress.Street, City = submission.CandidateInformation.CandidateAddress.City, Country = submission.CandidateInformation.CandidateAddress.Country, Purpose="Candidate", State = submission.CandidateInformation.CandidateAddress.State, Type = submission.CandidateInformation.CandidateAddress.Type, Zip = (ZipCode)submission.CandidateInformation.CandidateAddress.ZipCode },
                new(){ Street = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).Street, City = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).City, Country = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).Country, Purpose="Mailing", State = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).State, Type = (submission.CandidateInformation.MailingAddress ?? submission.CandidateInformation.CandidateAddress).Type, Zip = (ZipCode)(submission.CandidateInformation.MailingAddress?.ZipCode ?? submission.CandidateInformation.CandidateAddress.ZipCode) }
            }
                },
                VerificationSignature = submission.Attestation.Signature,
                VerificationExecutedAt = submission.Attestation.ExecutedAt
            },
            UserId = 10004
        };


        var race = new ElectionRace
        {
            Office = new Office { Name = expectedOffice, AgencyName = expectedAgency },
            Election = new Election { ElectionDate = _dateNow, Name = "primary" },
            District = new District { DistrictNumber = expectedDistrictNumber }
        };
        _electionRaceRepository
            .GetElectionRaceByIdAsync(raceId)
            .Returns(race);

        CandidateIntentionStatement? created = null;
        _registrationRepository
            .Create(Arg.Do<CandidateIntentionStatement>(c => created = c))
            .Returns(_mappedCandidateIntentionStatement);

        // Act
        var resp = await _service.SubmitCandidateIntentionStatementForEfile(dto);

        // Assert 
        Assert.That(resp, Is.Not.Null);
        Assert.That(resp.ValidationErrors, Is.Null.Or.Empty);
    }

    /// <summary>
    /// 
    /// </summary>
    [Test]
    public void SearchControlledCommitteeByIdOrName_MapsStatementsToResponseDtos()
    {
        // Arrange
        var query = "Test";

        // Create one fake CandidateControlledCommittee with a single address
        var candidate = new CandidateControlledCommittee
        {
            Id = 1,
            Name = "Jane Doe",
            StatusId = 1,
            Email = "<EMAIL>",
            AddressList = new AddressList
            {
                Addresses = new List<Models.Common.Address>
                    {
                        new Models.Common.Address
                        {
                            Street = "123 Main St",
                            City   = "Townsville",
                            State="CA",
                            Country="USA",
                            Zip="19345",
                            Purpose="test",
                            Type="Business"
                        }
                    }
            },
            JurisdictionCounty = "",
            JurisdictionActive = "",
            FinancialInstitutionAccountNumber = "",
            FinancialInstitutionName = "",
            FinancialInstitutionPhone = "",
            ElectionOfficeSought = "",
            ElectionDistrictNumber = "",
            ElectionId = 0,
        };

        var fakeList = new List<CandidateControlledCommittee> { candidate };

        _registrationRepository
            .FindControlledCommitteesByNameOrId(query)
            .Returns(Task.FromResult<IEnumerable<CandidateControlledCommittee>>(fakeList));

        // Act & Assert
        Assert.DoesNotThrowAsync(() => _service.SearchControlledCommitteeByIdOrName(query));
    }

    #region Private Methods

    /// <summary>
    /// A complete/valid F501 submission
    /// </summary>
    private static EfileCandidateIntentionStatement CreateValidEfileSubmission()
    {
        return new()
        {
            Attestation = new Business.Efile.Model.EfileAttestation()
            {
                ExecutedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Signature = "signature"
            },
            CandidateInformation = new CandidateInformation()
            {
                FirstName = "First",
                MiddleName = "Middle",
                LastName = "Last",
                CandidateAddress = new Business.Efile.Model.EfileAddress()
                {
                    Street = "456 Campaign Blvd",
                    Street2 = "",
                    City = "Sacramento",
                    State = "CA",
                    Type = "Business",
                    ZipCode = "95814",
                    County = "Sacramento",
                    Country = "United States"
                },
                MailingAddress = new Business.Efile.Model.EfileAddress()
                {
                    Street = "123 Front St",
                    Street2 = "Appt 8",
                    City = "Lafayette",
                    State = "LA",
                    ZipCode = "70506",
                    County = "Lafayette",
                    Type = "Residential",
                    Country = "United States"
                },
                ElectionRaceId = 1,
                ElectionJurisdiction = "State",
                IsNonPartisanOffice = true,
                PartyAffiliation = "American Independent Party",
                Phone = "3375551234",
                Email = "<EMAIL>",
            },
            StateCandidateExpenditureLimit = new StateCandidateExpenditureLimit()
            {
                ExpenditureLimitAccepted = true,
                ExpenditureExceeded = true,
                ContributedPersonalExcessFundsOn = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            }
        };
    }

    /// <summary>
    /// An original filing to use when testing amendments.
    /// </summary>
    private static CandidateIntentionStatement CreateOriginalCandidateIntentionStatement()
    {
        return new CandidateIntentionStatement
        {
            Id = 1,
            Name = "An original filing",
            StatusId = RegistrationStatus.Accepted.Id
        };
    }

    #endregion

}
