using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.FilerPortal.ControllerServices.CisRegistrationWithdrawalCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(CisRegistrationWithdrawalCtlSvc))]
public class CisRegistrationWithdrawalCtlSvcTests
{
    private CisRegistrationWithdrawalCtlSvc _service;
    private ICisRegistrationWithdrawalSvc _cisRegistrationWithdrawalSvc;
    private IDateTimeSvc _mockDateTimeSvc;


    [SetUp]
    public void Setup()
    {
        _mockDateTimeSvc = Substitute.For<IDateTimeSvc>();
        _cisRegistrationWithdrawalSvc = Substitute.For<ICisRegistrationWithdrawalSvc>();
        _service = new CisRegistrationWithdrawalCtlSvc(_cisRegistrationWithdrawalSvc, _mockDateTimeSvc);
    }

    #region CancelCisWithdrawal
    [Test]
    public async Task Cancel_ReturnsExpectedViewModel()
    {
        var id = 123;

        // Act
        var result = await _service.CancelCisWithdrawal(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<MethodResult>());
        });
    }

    [Test]
    public async Task Cancel_InvalidResult_ReturnsException()
    {
        var id = 123;

        _cisRegistrationWithdrawalSvc
            .CancelCisWithdrawal(Arg.Any<long>())
            .Throws(new InvalidOperationException("Something went wrong")); ;

        // Act
        var result = await _service.CancelCisWithdrawal(id);

        // Assert
        Assert.That(result.Error, Is.Not.Null);
    }
    #endregion

    #region InitializeCisWithdrawal
    [Test]
    public async Task InitializeCisWithdrawal_ReturnsId()
    {
        var id = 123;
        var cisWithdrawal = GenerateCisWithdrawal();

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .Returns(cisWithdrawal);

        // Act
        var result = await _service.InitializeCisWithdrawal(id);

        // Assert
        Assert.That(result, Is.InstanceOf<MethodResult<long>>());
    }

    [Test]
    public async Task InitializeCisWithdrawal_NullCisWithdrawal_ReturnsId()
    {
        var id = 123;

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .Returns(new CisRegistrationWithdrawalDto());

        _cisRegistrationWithdrawalSvc
            .CreateCisWithdrawal(Arg.Any<long>())
            .Returns(id + 1);

        // Act
        var result = await _service.InitializeCisWithdrawal(id);

        // Assert
        Assert.That(result, Is.InstanceOf<MethodResult<long>>());
    }

    [Test]
    public async Task InitializeCisWithdrawal_ReturnsException()
    {
        var id = 123;
        var cisWithdrawal = GenerateCisWithdrawal();

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .ThrowsAsync(new InvalidCastException("Error"));

        // Act
        var result = await _service.InitializeCisWithdrawal(id);

        // Assert
        Assert.That(result, Is.InstanceOf<MethodResult<long>>());
    }
    #endregion

    #region Page01
    [Test]
    public async Task Page01GetViewModel_ReturnsExpectedViewModel()
    {
        var id = 123;
        var cisWithdrawal = GenerateCisWithdrawal();

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .Returns(cisWithdrawal);


        // Act
        var result = await _service.Page01GetViewModel(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<MethodResult<CisRegistrationWithdrawalPage01ViewModel>>());
        });
    }

    [Test]
    public async Task Page01GetViewModel_InvalidResult_ReturnsExpectedViewModel()
    {
        var id = 123;
        var cisWithdrawal = GenerateCisWithdrawal();

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .Throws(new InvalidOperationException("Something went wrong")); ;

        // Act
        var result = await _service.Page01GetViewModel(id);

        // Assert
        Assert.That(result.Error, Is.Not.Null);
    }
    #endregion

    #region Page02
    [Test]
    public async Task Page02GetViewModel_ReturnsExpectedViewModel()
    {
        // Act
        var id = 123;
        var result = await _service.Page02GetViewModel(id);

        // Assert
        Assert.That(result.Data, Is.Not.Null);

    }
    [Test]
    public async Task Page02GetViewModel_InvalidResult_ReturnsExpectedViewModel()
    {
        var id = 123;

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .Throws(new InvalidOperationException("Something went wrong")); ;

        // Act
        var result = await _service.Page02GetViewModel(id);

        // Assert
        Assert.That(result.Error, Is.Not.Null);
    }

    [Test]
    public async Task Page02Submit_ValidResult_ReturnsExpectedViewModel()
    {
        var model = new CisRegistrationWithdrawalPage02ViewModel
        {
            Id = 1L,
            IsAttest = true,
        };

        _cisRegistrationWithdrawalSvc
            .CancelCisWithdrawal(Arg.Any<long>()).Returns(Task.CompletedTask);

        // Act
        var result = await _service.Page02Submit(model);

        // Assert
        Assert.That(result.Error, Is.Null);
    }

    [Test]
    public async Task Page02Submit_InvalidResult_ReturnsExpectedViewModel()
    {
        var model = new CisRegistrationWithdrawalPage02ViewModel
        {
            IsAttest = true,
        };

        _cisRegistrationWithdrawalSvc
            .CancelCisWithdrawal(Arg.Any<long>())
            .Throws(new InvalidOperationException("Something went wrong")); ;

        // Act
        var result = await _service.Page02Submit(model);

        // Assert
        Assert.That(result.Error, Is.Not.Null);
    }

    [Test]
    public async Task Page02Submit_WhenNotAttest_ShouldCallSendForAttestationCisWithdrawal()
    {
        // Arrange
        var model = new CisRegistrationWithdrawalPage02ViewModel
        {
            Id = 123,
            IsAttest = false
        };

        _cisRegistrationWithdrawalSvc
            .SendForAttestationCisWithdrawal(Arg.Any<long>())
            .Returns(Task.FromResult(new RegistrationResponseDto()));

        // Act
        var result = await _service.Page02Submit(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            _cisRegistrationWithdrawalSvc
                .Received(1)
                .SendForAttestationCisWithdrawal(123);
        });
    }
    #endregion

    #region Page03
    [Test]
    public async Task Page03GetViewModel_ReturnsExpectedViewModel()
    {
        // Act
        var id = 123;
        var cisWithdrawal = GenerateCisWithdrawal();

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .Returns(cisWithdrawal);

        var result = await _service.Page03GetViewModel(id);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page03GetViewModel_InvalidResult_ReturnsExpectedViewModel()
    {
        var id = 123;

        _cisRegistrationWithdrawalSvc
            .GetCisWithdrawal(Arg.Any<long>())
            .Throws(new InvalidOperationException("Something went wrong")); ;

        // Act
        var result = await _service.Page03GetViewModel(id);

        // Assert
        Assert.That(result.Error, Is.Not.Null);
    }
    #endregion

    #region Private
    private static CisRegistrationWithdrawalDto GenerateCisWithdrawal()
    {
        return new()
        {
            Candidate = new()
            {
                FirstName = "First",
                LastName = "Last",
            },
            Addresses = new(),
            ElectionOfficeSought = "Office",
            ElectionRace = new()
        };
    }
    #endregion
}
