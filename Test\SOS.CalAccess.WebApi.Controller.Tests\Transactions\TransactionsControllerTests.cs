using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.OtherInfluencePayments;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Transactions;

namespace SOS.CalAccess.WebApi.Tests.Transactions;

[TestFixture]
public class TransactionsControllerTests
{
    private ITransactionSvc _transactionSvc;
    private IFilingSvc _filingSvc;
    private IAuthorizationService _authorizationSvc;
    private IAuditService _auditSvc;
    private IDecisionsSvc _decisionsSvc;
    private TransactionsController _controller;
    private LobbyistEmployerCoalitionController _lobbyistEmployerCoalitionController;
    private IDateTimeSvc _dateTimeSvc;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _filingSvc = Substitute.For<IFilingSvc>();
        _transactionSvc = Substitute.For<ITransactionSvc>();
        _authorizationSvc = Substitute.For<IAuthorizationService>();
        _auditSvc = Substitute.For<IAuditService>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _controller = new TransactionsController(
            _authorizationSvc,
            _auditSvc,
            _transactionSvc,
            _dateTimeSvc
        );

        _lobbyistEmployerCoalitionController = new LobbyistEmployerCoalitionController(_transactionSvc, _filingSvc, _authorizationSvc);
    }

    [Test]
    public async Task CreateLobbyingCampaignContribution_ConvertsEmptyStringsToNull_AndCreatesTransaction()
    {
        // Arrange
        long filerId = 123L;
        LobbyistCampaignContributionRequestDto request = new()
        {
            NonCommitteeRecipientName = string.Empty,
            NonFilerContributorName = string.Empty,
            SeparateAccountName = string.Empty,
            FilingId = 456L,
            Amount = 1000.50m,
            TransactionDate = _dateNow,
            IsRecipientCommittee = true,
            RecipientCommitteeFilerId = 789L,
            IsContributorFiler = false,
            ContributorFilerId = null
        };

        _decisionsSvc.InitiateWorkflow<LobbyistCampaignContributionDs, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistCampaignContributionRuleset,
            Arg.Any<LobbyistCampaignContributionDs>(),
            true)
            .Returns([]);

        // Mock transaction service to return a transaction
        LobbyingCampaignContribution expectedTransaction = new() { Id = 1, Amount = (Currency)1000.50m };
        _transactionSvc.CreateLobbyingCampaignContribution(
            filerId,
            request.FilingId,
            null,
            request.Amount,
            request.TransactionDate.Value,
            request.IsRecipientCommittee,
            request.RecipientCommitteeFilerId,
            request.IsContributorFiler,
            null,
            request.ContributorFilerId,
            null)
            .Returns(expectedTransaction);

        // Act
        ActionResult<TransactionItemResponse> result = await _controller.CreateLobbyingCampaignContribution(
            filerId,
            request,
            _decisionsSvc,
            _transactionSvc);

        // Assert
        // Verify empty strings were converted to null
        Assert.Multiple(() =>
        {
            Assert.That(request.NonCommitteeRecipientName, Is.Null);
            Assert.That(request.NonFilerContributorName, Is.Null);
            Assert.That(request.SeparateAccountName, Is.Null);
        });

        // Verify the transaction was created
        ActionResult<TransactionItemResponse> okResult = result;

        // Verify service calls
        await _decisionsSvc.Received(1).InitiateWorkflow<LobbyistCampaignContributionDs, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistCampaignContributionRuleset,
            Arg.Any<LobbyistCampaignContributionDs>(),
            true);

        await _transactionSvc.Received(1).CreateLobbyingCampaignContribution(
            filerId,
            request.FilingId,
            null,
            request.Amount,
            request.TransactionDate.Value,
            request.IsRecipientCommittee,
            request.RecipientCommitteeFilerId,
            request.IsContributorFiler,
            null,
            request.ContributorFilerId,
            null);
    }

    [Test]
    public async Task CreateLobbyistEmployerCoalitionLobbyingCampaignContribution_ConvertsEmptyStringToNull_AndCreatesTransaction()
    {
        // Arrange
        long filerId = 123L;
        LobbyingCampaignContributionRequestDto request = new()
        {
            NonCommitteeRecipientName = string.Empty, // This should be converted to null
            FilingId = 456L,
            Amount = 1000.50m,
            TransactionDate = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            IsRecipientCommittee = true,
            RecipientCommitteeFilerId = 789L
        };

        // Mock decisions service to return no errors
        _decisionsSvc.InitiateWorkflow<LobbyistEmployerCoalitionCampaignContributionDs, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistEmployerCoalitionCampaignContributonRuleset,
            Arg.Any<LobbyistEmployerCoalitionCampaignContributionDs>(),
            true)
            .Returns([]);

        // Mock transaction service to return a transaction
        LobbyingCampaignContribution expectedTransaction = new() { Id = 1, Amount = (Currency)1000.50m };
        _transactionSvc.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
            filerId,
            request.FilingId,
            null, // Expecting null because empty string was converted
            request.Amount,
            request.TransactionDate.Value,
            request.IsRecipientCommittee,
            request.RecipientCommitteeFilerId)
            .Returns(expectedTransaction);

        // Act
        ActionResult<TransactionItemResponse> result = await _controller.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
            filerId,
            request,
            _decisionsSvc,
            _transactionSvc);

        // Assert
        // Verify empty string was converted to null
        Assert.That(request.NonCommitteeRecipientName, Is.Null);

        // Verify the transaction was created
        ActionResult<TransactionItemResponse> okResult = result;

        // Verify service calls
        await _decisionsSvc.Received(1).InitiateWorkflow<LobbyistEmployerCoalitionCampaignContributionDs, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistEmployerCoalitionCampaignContributonRuleset,
            Arg.Any<LobbyistEmployerCoalitionCampaignContributionDs>(),
            true);

        await _transactionSvc.Received(1).CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
            filerId,
            request.FilingId,
            null,
            request.Amount,
            request.TransactionDate.Value,
            request.IsRecipientCommittee,
            request.RecipientCommitteeFilerId);
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_ReturnsOk_WhenResponseIsValid()
    {
        // Arrange
        long filingId = 12345L;
        ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse response = new(filingId, true, []);
        _ = _transactionSvc.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId)
            .Returns(Task.FromResult(response));

        // Act
        ActionResult<ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse> result = await _lobbyistEmployerCoalitionController.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId, _transactionSvc);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_ShouldCallService_AndReturnResponse()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            AdvancesOrOtherPaymentsAmount = 100.50m,
            AdvancesOrOtherPaymentsExplanation = "Test explanation",
            FeesAndRetainersAmount = 500.75m,
            FilerContactId = 789L,
            FilerId = 123L,
            FilingId = 456L,
            RegistrationFilingId = null,
            ReimbursementOfExpensesAmount = 200.25m
        };

        TransactionResponseDto expectedResponse = new()
        {
            Id = 999L,
            Valid = true,
            ValidationErrors = []
        };

        _ = _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        TransactionResponseDto result = await _controller.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.EqualTo(expectedResponse.Valid));
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _transactionSvc.Received(1).CreatePaymentMadeToLobbyingFirmsTransaction(Arg.Is<PaymentMadeToLobbyingFirmsRequestDto>(r =>
            r.AdvancesOrOtherPaymentsAmount == request.AdvancesOrOtherPaymentsAmount &&
            r.AdvancesOrOtherPaymentsExplanation == request.AdvancesOrOtherPaymentsExplanation &&
            r.FeesAndRetainersAmount == request.FeesAndRetainersAmount &&
            r.FilerContactId == request.FilerContactId &&
            r.FilerId == request.FilerId &&
            r.FilingId == request.FilingId &&
            r.RegistrationFilingId == request.RegistrationFilingId &&
            r.ReimbursementOfExpensesAmount == request.ReimbursementOfExpensesAmount
        ));
    }

    [Test]
    public async Task GetAllEndOfSessionLobbyingTransactionsForFiling_ReturnsExpectedTransactions()
    {
        // Arrange
        long filingId = 123;
        List<EndOfSessionLobbyingDto> expectedTransactions = new()
        {
            new()
            {
                FilerId = 1001,
                FirmName = "Lobbying Firm A",
                DateLobbyingFirmHired = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Local),
                Amount = (Currency)500
            },
            new()
            {
                FilerId = 1002,
                FirmName = "Lobbying Firm B",
                DateLobbyingFirmHired = new DateTime(2023, 2, 15, 0, 0, 0, DateTimeKind.Local),
                Amount = (Currency)300
            }
        };

        _ = _transactionSvc
            .GetAllEndOfSessionLobbyingTransactionsForFiling(filingId)
            .Returns(Task.FromResult<IEnumerable<EndOfSessionLobbyingDto>>(expectedTransactions));

        // Act
        ActionResult<IReadOnlyList<EndOfSessionLobbyingDto>> result = await _controller.GetAllEndOfSessionLobbyingTransactionsForFiling(
            filingId, _transactionSvc);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.Not.Null);

        List<EndOfSessionLobbyingDto> transactions = result.Value.ToList();
        Assert.That(transactions, Has.Count.EqualTo(2));

        Assert.Multiple(() =>
        {
            Assert.That(transactions[0].FilerId, Is.EqualTo(1001));
            Assert.That(transactions[0].FirmName, Is.EqualTo("Lobbying Firm A"));
            Assert.That(transactions[0].DateLobbyingFirmHired, Is.EqualTo(new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(transactions[0].Amount, Is.EqualTo(500));

            Assert.That(transactions[1].FilerId, Is.EqualTo(1002));
            Assert.That(transactions[1].FirmName, Is.EqualTo("Lobbying Firm B"));
            Assert.That(transactions[1].DateLobbyingFirmHired, Is.EqualTo(new DateTime(2023, 2, 15, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(transactions[1].Amount, Is.EqualTo(300));
        });

        _ = await _transactionSvc.Received(1).GetAllEndOfSessionLobbyingTransactionsForFiling(filingId);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_WithRegistrationFilingId_ShouldCallService_AndReturnResponse()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            AdvancesOrOtherPaymentsAmount = null,
            AdvancesOrOtherPaymentsExplanation = null,
            FeesAndRetainersAmount = 750.25m,
            FilerContactId = null,
            FilerId = 123L,
            FilingId = 456L,
            RegistrationFilingId = 789L,
            ReimbursementOfExpensesAmount = 250.75m
        };

        TransactionResponseDto expectedResponse = new()
        {
            Id = 999L,
            Valid = true,
            ValidationErrors = []
        };

        _ = _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        TransactionResponseDto result = await _controller.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedResponse.Id));
            Assert.That(result.Valid, Is.EqualTo(expectedResponse.Valid));
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _transactionSvc.Received(1).CreatePaymentMadeToLobbyingFirmsTransaction(Arg.Is<PaymentMadeToLobbyingFirmsRequestDto>(r =>
            r.FeesAndRetainersAmount == request.FeesAndRetainersAmount &&
            r.FilerId == request.FilerId &&
            r.FilingId == request.FilingId &&
            r.RegistrationFilingId == request.RegistrationFilingId &&
            r.ReimbursementOfExpensesAmount == request.ReimbursementOfExpensesAmount
        ));
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_WithInvalidRequest_ShouldReturnInvalidResponse()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            AdvancesOrOtherPaymentsAmount = null,
            AdvancesOrOtherPaymentsExplanation = null,
            FeesAndRetainersAmount = null,
            FilerContactId = null,
            FilerId = 123L,
            FilingId = 456L,
            RegistrationFilingId = null,
            ReimbursementOfExpensesAmount = null
        };

        List<WorkFlowError> validationErrors = new()
        {
            new("Amount", "Required", "Validation", "An amount is required")
        };

        TransactionResponseDto expectedResponse = new()
        {
            Id = null,
            Valid = false,
            ValidationErrors = validationErrors
        };

        _ = _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        TransactionResponseDto result = await _controller.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.Null);
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
        Assert.That(result.ValidationErrors.First().Message, Is.EqualTo("An amount is required"));

        _ = await _transactionSvc.Received(1).CreatePaymentMadeToLobbyingFirmsTransaction(Arg.Any<PaymentMadeToLobbyingFirmsRequestDto>());
    }


    [Test]
    public async Task CreateOtherInfluencePayment_ShouldReturnCreatedResult_WithCorrectTransactionData()
    {
        // Arrange
        long filerId = 1L;
        long filingId = 2L;
        long expectedTransactionId = 123L;

        OtherInfluencePaymentDto dto = new()
        {
            FilerId = filerId,
            FilingId = filingId,
            ContactId = 10L,
            PaymentCodeId = 20L,
            PaymentCodeDescription = "Test",
            Amount = 123.45m,
            TransactionDate = new DateTime(2023, 1, 1, 0, 0, 0, 0),
            AdministrativeActions = [new() { AgencyId = 1, AgencyDescription = "Yes", AdministrativeAction = "Todo" }],
            AssemblyBills = [new() { BillId = 1 }],
            SenateBills = [new() { BillId = 2 }],
            OtherActionsLobbied = "OtherTest"
        };

        OtherPaymentsToInfluence expectedTransaction = new()
        {
            Id = expectedTransactionId,
            FilerId = dto.FilerId,
            ContactId = dto.ContactId,
            PaymentCodeId = dto.PaymentCodeId,
            Notes = dto.PaymentCodeDescription,
            Amount = (Currency)dto.Amount,
            TransactionDate = dto.TransactionDate,
            OtherActionsLobbied = dto.OtherActionsLobbied
        };

        _transactionSvc.CreateOtherPaymentToInfluence(dto).Returns(expectedTransaction);

        TransactionsController controller = new(
            _authorizationSvc,
            _auditSvc,
            _transactionSvc,
            _dateTimeSvc
        );

        // Act
        ActionResult<TransactionItemResponse> result = await controller.CreateOtherInfluencePayment(
            filerId, filingId, _transactionSvc, _decisionsSvc, dto);

        // Assert
        CreatedAtRouteResult? createdAtRouteResult = result.Result as CreatedAtRouteResult;
        Assert.That(createdAtRouteResult, Is.Not.Null);
        Assert.That(createdAtRouteResult!.RouteName, Is.EqualTo("GetTransaction"));

        TransactionItemResponse? response = createdAtRouteResult.Value as TransactionItemResponse;
        Assert.That(response, Is.Not.Null);
        Assert.That(response!.Id, Is.EqualTo(expectedTransactionId));
    }

    [Test]
    [TestCase(false, false, false, false, false, false, TestName = "All collections null")]
    [TestCase(true, false, true, false, true, false, TestName = "All collections empty")]
    [TestCase(true, true, true, true, true, true, TestName = "All collections populated")]
    [TestCase(true, true, false, false, false, false, TestName = "Only AssemblyBills populated")]
    [TestCase(false, false, true, true, false, false, TestName = "Only SenateBills populated")]
    [TestCase(false, false, false, false, true, true, TestName = "Only AdministrativeActions populated")]
    public async Task CreateOtherInfluencePayment_ShouldReturnFailure_IfDecisionsErrorsExist(
        [Values] bool includeAssemblyBill,
        [Values] bool populateAssemblyBill,
        [Values] bool includeSenateBill,
        [Values] bool populateSenateBill,
        [Values] bool includeAdminActions,
        [Values] bool populateAdminActions)
    {
        // Arrange
        long filerId = 1L;
        long filingId = 2L;

        OtherInfluencePaymentDto dto = new()
        {
            FilerId = filerId,
            FilingId = filingId,
            ContactId = 10L,
            PaymentCodeId = 20L,
            PaymentCodeDescription = "Test",
            Amount = 123.45m,
            TransactionDate = new DateTime(2023, 1, 1, 0, 0, 0, 0),
            OtherActionsLobbied = "OtherTest",
            AdvertAdminActions = true,
            AdvertLegislation = true,
            AdvertOther = true
        };

        if (includeAssemblyBill)
        {
            dto.AssemblyBills = populateAssemblyBill
                ? new() {
                    new() { BillId = 1 },
                    new() { BillId = null }
                }
                : new();
        }
        else
        {
            dto.AssemblyBills = null!;
        }

        if (includeSenateBill)
        {
            dto.SenateBills = populateSenateBill
                ? new() {
                    new() { BillId = 1 },
                    new() { BillId = null }
                }
                : new();
        }
        else
        {
            dto.SenateBills = null!;
        }

        if (includeAdminActions)
        {
            dto.AdministrativeActions = populateAdminActions
                ? new() {
                    new() { AgencyId = 1, AdministrativeAction = "Test Action", AgencyDescription = "Test Agency" },
                    new() { AgencyId = null, AdministrativeAction = null, AgencyDescription = null }
                }
                : new();
        }
        else
        {
            dto.AdministrativeActions = null!;
        }

        TransactionsController controller = new(
            _authorizationSvc,
            _auditSvc,
            _transactionSvc,
            _dateTimeSvc
        );

        List<WorkFlowError> validationErrors = new()
        {
            new("Some error", "ErrGlobal0001", "Validation", "{{Field Name}} is required")
        };

        _decisionsSvc.InitiateWorkflow<OtherPaymentsToInfluenceDs, List<WorkFlowError>>(
            DecisionsWorkflow.OtherPaymentsToInfluenceTransactionRuleset, Arg.Any<OtherPaymentsToInfluenceDs>(), true)
            .Returns(Task.FromResult(validationErrors));

        // Act
        ActionResult<TransactionItemResponse> result = await controller.CreateOtherInfluencePayment(
            filerId, filingId, _transactionSvc, _decisionsSvc, dto);

        //Assert
        Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
        ObjectResult? objectResult = (ObjectResult)result.Result;

        Assert.Multiple(() =>
        {
            Assert.That(objectResult.StatusCode, Is.EqualTo(422));
            Assert.That(objectResult.Value, Is.EqualTo(validationErrors));
        });
    }

    [Test]
    public async Task EditOtherInfluencePayment_ShouldReturnCreatedResult_WithCorrectTransactionData()
    {
        // Arrange
        long transactionId = 123L;
        long filerId = 1L;
        long expectedTransactionId = 456L;

        OtherInfluencePaymentDto dto = new()
        {
            FilerId = filerId,
            ContactId = 10L,
            PaymentCodeId = 20L,
            PaymentCodeDescription = "Updated Test Description",
            Amount = 500.75m,
            TransactionDate = new DateTime(2023, 2, 15, 0, 0, 0, DateTimeKind.Utc),
            AdministrativeActions = [new() { AgencyId = 2, AgencyDescription = "Updated Agency", AdministrativeAction = "Updated Action" }],
            AssemblyBills = [new() { BillId = 3 }],
            SenateBills = [new() { BillId = 4 }],
            OtherActionsLobbied = "Updated Other Actions"
        };

        OtherPaymentsToInfluence expectedTransaction = new()
        {
            Id = expectedTransactionId,
            FilerId = dto.FilerId,
            ContactId = dto.ContactId,
            PaymentCodeId = dto.PaymentCodeId,
            PaymentCodeDescription = dto.PaymentCodeDescription,
            Amount = (Currency)dto.Amount,
            TransactionDate = dto.TransactionDate,
            OtherActionsLobbied = dto.OtherActionsLobbied
        };

        _transactionSvc.EditOtherPaymentToInfluence(transactionId, dto).Returns(expectedTransaction);

        TransactionsController controller = new(
            _authorizationSvc,
            _auditSvc,
            _transactionSvc,
            _dateTimeSvc
        );

        // Act
        ActionResult<TransactionItemResponse> result = await controller.EditOtherInfluencePayment(
            transactionId, dto, _transactionSvc, _decisionsSvc);

        // Assert
        CreatedAtRouteResult? createdAtRouteResult = result.Result as CreatedAtRouteResult;
        Assert.That(createdAtRouteResult, Is.Not.Null);
        Assert.That(createdAtRouteResult!.RouteName, Is.EqualTo("GetTransaction"));

        TransactionItemResponse? response = createdAtRouteResult.Value as TransactionItemResponse;
        Assert.That(response, Is.Not.Null);
        Assert.That(response!.Id, Is.EqualTo(expectedTransactionId));

        // Verify service was called with correct parameters
        await _transactionSvc.Received(1).EditOtherPaymentToInfluence(transactionId, dto);
        await _decisionsSvc.Received(1).InitiateWorkflow<OtherPaymentsToInfluenceDs, List<WorkFlowError>>(
            DecisionsWorkflow.OtherPaymentsToInfluenceTransactionRuleset,
            Arg.Any<OtherPaymentsToInfluenceDs>(),
            true);
    }

    [Test]
    public async Task EditOtherInfluencePayment_ShouldReturnUnprocessableEntity_WhenValidationFails()
    {
        // Arrange
        long transactionId = 123L;

        OtherInfluencePaymentDto dto = new()
        {
            // Missing required fields
            FilerId = 1L,
            Amount = -100m // Invalid amount
        };

        List<WorkFlowError> validationErrors = new()
    {
        new("Amount", "InvalidAmount", "Validation", "Amount must be positive")
    };

        _decisionsSvc.InitiateWorkflow<OtherPaymentsToInfluenceDs, List<WorkFlowError>>(
            DecisionsWorkflow.OtherPaymentsToInfluenceTransactionRuleset,
            Arg.Any<OtherPaymentsToInfluenceDs>(),
            true)
            .Returns(validationErrors);

        TransactionsController controller = new(
            _authorizationSvc,
            _auditSvc,
            _transactionSvc,
            _dateTimeSvc
        );

        // Act
        ActionResult<TransactionItemResponse> result = await controller.EditOtherInfluencePayment(
            transactionId, dto, _transactionSvc, _decisionsSvc);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<UnprocessableEntityObjectResult>());
        UnprocessableEntityObjectResult? unprocessableEntityResult = result.Result as UnprocessableEntityObjectResult;
        Assert.That(unprocessableEntityResult, Is.Not.Null);

        var returnedErrors = unprocessableEntityResult!.Value as List<WorkFlowError>;
        Assert.That(returnedErrors, Is.Not.Null);
        Assert.That(returnedErrors, Has.Count.EqualTo(1));
        Assert.That(returnedErrors![0].Message, Is.EqualTo("Amount must be positive"));

        // Verify transaction service was not called
        await _transactionSvc.DidNotReceive().EditOtherPaymentToInfluence(Arg.Any<long>(), Arg.Any<OtherInfluencePaymentDto>());
    }

    [Test]
    public async Task EditOtherInfluencePayment_ShouldNormalizeEmptyStrings()
    {
        // Arrange
        long transactionId = 123L;
        long filerId = 1L;

        OtherInfluencePaymentDto dto = new()
        {
            FilerId = filerId,
            ContactId = 10L,
            PaymentCodeId = 20L,
            PaymentCodeDescription = "",
            Amount = 200.50m,
            TransactionDate = new DateTime(2023, 3, 15, 0, 0, 0, DateTimeKind.Utc),
            OtherActionsLobbied = ""
        };

        OtherPaymentsToInfluence expectedTransaction = new()
        {
            Id = 456L,
            FilerId = dto.FilerId,
            ContactId = dto.ContactId,
            PaymentCodeId = dto.PaymentCodeId,
            Amount = (Currency)dto.Amount,
            TransactionDate = dto.TransactionDate
        };

        _transactionSvc.EditOtherPaymentToInfluence(transactionId, dto).Returns(expectedTransaction);

        TransactionsController controller = new(
            _authorizationSvc,
            _auditSvc,
            _transactionSvc,
            _dateTimeSvc
        );

        // Act
        await controller.EditOtherInfluencePayment(
            transactionId, dto, _transactionSvc, _decisionsSvc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(dto.PaymentCodeDescription, Is.Null, "PaymentCodeDescription should be normalized to null");
            Assert.That(dto.OtherActionsLobbied, Is.Null, "OtherActionsLobbied should be normalized to null");
        });

        await _transactionSvc.Received(1).EditOtherPaymentToInfluence(transactionId, Arg.Is<OtherInfluencePaymentDto>(d =>
            d.PaymentCodeDescription == null &&
            d.OtherActionsLobbied == null));
    }

    [Test]
    public async Task GetOtherPaymentsCumulativeAmountForFilingAndContact_ReturnsOk_WhenResponseIsValid()
    {
        // Arrange
        long filingId = 12345L;
        long contactId = 54321L;
        CumulativeAmountResponse response = new() { CumulativeAmount = (Currency)3000 };

        _ = _transactionSvc.GetOtherPaymentsCumulativeAmountForFilingAndContact(filingId, contactId)
            .Returns(Task.FromResult(response));

        // Act
        ActionResult<CumulativeAmountResponse> result = await _controller.GetOtherPaymentsCumulativeAmountForFilingAndContact(filingId, contactId, _transactionSvc, _filingSvc);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetLobbyistCampaignContributionTransactionById_ReturnsOk_WithValidData()
    {
        // Arrange
        long transactionId = 1;

        LobbyingCampaignContribution contribution = new()
        {
            Id = transactionId,
            Amount = new Currency(300),
            TransactionDate = _dateNow,
            CreatedBy = 1,
            ModifiedBy = 1,
            NonCommitteeRecipientName = "Some Name",
        };

        // Act
        ActionResult<LobbyistCampaignContributionItemResponse> result = await _controller.GetLobbyistCampaignContributionTransactionById(
            transactionId, _transactionSvc);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task EditLobbyistCampaignContribution_ConvertsEmptyStringsToNull_AndEditsTransaction()
    {
        // Arrange
        LobbyistCampaignContributionRequestDto request = new()
        {
            NonCommitteeRecipientName = string.Empty,
            NonFilerContributorName = string.Empty,
            SeparateAccountName = string.Empty,
            FilingId = 456L,
            Amount = 2000.75m,
            TransactionDate = _dateNow,
            IsRecipientCommittee = false,
            RecipientCommitteeFilerId = null,
            IsContributorFiler = true,
            ContributorFilerId = 321L
        };

        _decisionsSvc.InitiateWorkflow<LobbyistCampaignContributionDs, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistCampaignContributionRuleset,
            Arg.Any<LobbyistCampaignContributionDs>(),
            true)
            .Returns([]);

        LobbyingCampaignContribution editedTransaction = new() { Id = 2, Amount = (Currency)2000.75m };

        _transactionSvc.EditLobbyistCampaignContribution(request)
            .Returns(editedTransaction);

        // Act
        ActionResult<TransactionItemResponse> result = await _controller.EditLobbyistCampaignContribution(
            request,
            _decisionsSvc,
            _transactionSvc);

        // Verify response is OkObjectResult with the expected transaction
        OkObjectResult? okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        TransactionItemResponse? response = okResult.Value as TransactionItemResponse;
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Id, Is.EqualTo(2));
            Assert.That(response.Amount, Is.EqualTo((Currency)2000.75m));
        });

        // Verify service calls
        await _decisionsSvc.Received(1).InitiateWorkflow<LobbyistCampaignContributionDs, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistCampaignContributionRuleset,
            Arg.Any<LobbyistCampaignContributionDs>(),
            true);

        await _transactionSvc.Received(1).EditLobbyistCampaignContribution(request);
    }

    [Test]
    public async Task EditLobbyistCampaignContribution_AmountIsNull_ReturnsUnprocessableEntity()
    {
        // Arrange
        LobbyistCampaignContributionRequestDto request = new()
        {
            Amount = null, // Simulate missing amount
            FilingId = 456L,
            TransactionDate = _dateNow,
            IsRecipientCommittee = false,
            IsContributorFiler = true,
            ContributorFilerId = 321L
        };

        List<WorkFlowError> validationErrors = new()
        {
            new("Some error", "ErrGlobal0001", "Validation", "{{Field Name}} is required")
        };

        _decisionsSvc.InitiateWorkflow<LobbyistCampaignContributionDs, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistCampaignContributionRuleset,
            Arg.Any<LobbyistCampaignContributionDs>(),
            true)
            .Returns(validationErrors);

        // Act
        ActionResult<TransactionItemResponse> result = await _controller.EditLobbyistCampaignContribution(
            request,
            _decisionsSvc,
            _transactionSvc);

        //Assert
        Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
        ObjectResult? objectResult = (ObjectResult)result.Result;

        Assert.Multiple(() =>
        {
            Assert.That(objectResult.StatusCode, Is.EqualTo(422));
        });

        // Ensure transaction service was NOT called
        await _transactionSvc.DidNotReceive().EditLobbyistCampaignContribution(Arg.Any<LobbyistCampaignContributionRequestDto>());
    }

    [Test]
    public void DeleteTransactionAsync_ValidRequest_ShouldNotThrowError()
    {
        // Arrange
        int id = 1;
        int filingSummaryId = 1;

        _ = _transactionSvc.DeleteTransactionAsync(Arg.Any<long>(), Arg.Any<long>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.DeleteTransactionAsync(id, filingSummaryId));
        _transactionSvc.Received().DeleteTransactionAsync(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task CreateEndOfSessionLobbying_ValidRequest_ReturnsTransactionResponse()
    {
        // Arrange
        EndOfSessionLobbyingRequestDto request = new()
        {
            FilingId = 123,
        };

        TransactionResponseDto expectedResponse = new()
        {
            Id = 1,
            Valid = true,
            ValidationErrors = []
        };

        _transactionSvc
            .CreateEndOfSessionLobbying(request)
            .Returns(expectedResponse);

        // Act
        TransactionResponseDto result = await _controller.CreateEndOfSessionLobbying(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(1));
            Assert.That(result.Valid, Is.True);
        });

        await _transactionSvc.Received(1).CreateEndOfSessionLobbying(request);
    }

    [Test]
    public async Task GetOtherPaymentsToInfluenceTransactionByIdPath_ShouldReturnValue()
    {
        // Arrange
        long transactionId = 123L;
        long filerId = 1L;
        var contactId = 10;

        OtherInfluencePaymentDto dto = new()
        {
            Id = transactionId,
            FilerId = filerId,
            ContactId = contactId,
            PaymentCodeId = 20L,
            PaymentCodeDescription = "",
            Amount = 200.50m,
            TransactionDate = new DateTime(2023, 3, 15, 0, 0, 0, DateTimeKind.Utc),
            OtherActionsLobbied = ""
        };

        _transactionSvc.GetOtherPaymentsToInfluenceTransactionById(transactionId).Returns(dto);

        // Act
         var result = await _controller.GetOtherPaymentsToInfluenceTransactionById(transactionId, _transactionSvc);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(dto.Id, Is.EqualTo(transactionId));
            Assert.That(dto.ContactId, Is.EqualTo(contactId));
        });
    }

    [Test]
    public async Task GetEndOfSessionLobbyingTransactionById_ShouldReturnValue()
    {
        // Arrange
        long transactionId = 1001L;

        EndOfSessionLobbyingDto dto = new()
        {
            Id = transactionId,
            FilerId = 2002L,
            Amount = 7500m,
            Contact = new OrganizationContact { Id = 3003, OrganizationName = "Firm A" },
            FirmName = "Firm A",
            DateLobbyingFirmHired = new DateTime(2025, 5, 1),
            FilingId = 4004,
            RegistrationId = 5005,
            ActionsLobbied = []
        };

        _transactionSvc.GetEndOfSessionLobbyingTransactionById(transactionId).Returns(dto);

        // Act
        var result = await _controller.GetEndOfSessionLobbyingTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.FirmName, Is.EqualTo("Firm A"));
            Assert.That(result.Amount, Is.EqualTo(7500m));
        });
    }

    [Test]
    public async Task EditEndOfSessionLobbying_ShouldReturnSuccessResult_WhenValid()
    {
        // Arrange
        long transactionId = 1001L;

        EndOfSessionLobbyingRequestDto request = new()
        {
            FilingId = 4004,
            FilerId = 2002,
            ContactId = 3003,
            Amount = 8000m,
            DateLobbyingFirmHired = new DateTime(2025, 6, 1),
            AssemblyBills = [new ActionsLobbiedRequestDto { BillId = 111 }],
            SenateBills = [new ActionsLobbiedRequestDto { BillId = 222 }]
        };

        TransactionResponseDto response = new()
        {
            Id = transactionId,
            Valid = true,
            ValidationErrors = []
        };

        _transactionSvc.EditEndOfSessionLobbying(transactionId, request).Returns(response);

        // Act
        var result = await _controller.EditEndOfSessionLobbying(transactionId, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }


}
