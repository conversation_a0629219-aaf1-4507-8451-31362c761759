using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using Moq;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class TransactionCtlSvcTests
{
    private Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
    private Mock<IContactsApi> _mockContactsApi;
    private Mock<ILobbyistEmployerCoalitionApi> _mockLobbyistEmployerCoalitionApi;
    private Mock<ILobbyistEmployerApi> _mockLobbyistEmployerApi;
    private Mock<ILobbyingAdvertisementApi> _mockLobbyingAdvertisementApi;
    private TransactionCtlSvc _transactionCtlSvc;

    [SetUp]
    public void Setup()
    {
        _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
        _mockLobbyistEmployerApi = new Mock<ILobbyistEmployerApi>();
        _mockLobbyistEmployerCoalitionApi = new Mock<ILobbyistEmployerCoalitionApi>();
        _mockContactsApi = new Mock<IContactsApi>();
        _mockLobbyingAdvertisementApi = new Mock<ILobbyingAdvertisementApi>();
        var localizedString = new LocalizedString("key", "text");
        _mockLocalizer
            .Setup(x => x[It.IsAny<string>()]).Returns(localizedString);

        _transactionCtlSvc = new TransactionCtlSvc(_mockContactsApi.Object, _mockLobbyistEmployerApi.Object, _mockLobbyistEmployerCoalitionApi.Object, _mockLobbyingAdvertisementApi.Object, _mockLocalizer.Object);
    }

#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

    [Test]
    public async Task GetContactDetailForViewModel_ShouldReturnModel_WhenContactIdValidAsync()
    {
        // Arrange
        var contactId = 1;
        var registrationFilingId = 0;
        var cancellationToken = CancellationToken.None;
        var mockContactResponse = new ContactItemResponse(1
            , "Test City"
            , "United States"
            , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
            , 1
            , contactId
            , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax"), }
            , "HI"
            , "123 Sample St."
            , "APT A"
            , 2
            , true
            , new List<WorkFlowError>()
            , "https://example.com"
            , "12345");
        var mockModel = new PaymentMadeToLobbyingCoalitionViewModel
        {
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
        };

        _mockContactsApi.Setup(x => x.GetContact(contactId, It.IsAny<CancellationToken>())).ReturnsAsync(mockContactResponse);

        // Act
        var result = await _transactionCtlSvc.GetContactDetailForViewModel(mockModel, cancellationToken);

        // Assert
        var faxNumber = mockContactResponse.PhoneNumbers.First(x => x.Type == "Fax");
        var phoneNumber = mockContactResponse.PhoneNumbers.First(x => x.Type == "Phone");
        Assert.Multiple(() =>
        {
            Assert.That(result!.Contact!.City, Is.EqualTo(mockContactResponse.City));
            Assert.That(result!.Contact!.Country, Is.EqualTo(mockContactResponse.Country));
            Assert.That(result!.Contact!.EmailAddress, Is.EqualTo(mockContactResponse.EmailAddresses[0].Email));
            Assert.That(result!.Contact!.FaxNumber, Is.EqualTo(faxNumber.Number));
            Assert.That(result!.Contact!.FaxNumberCountryCode, Is.EqualTo(faxNumber.CountryCode));
            Assert.That(result!.Contact!.PhoneNumber, Is.EqualTo(phoneNumber.Number));
            Assert.That(result!.Contact!.PhoneNumberCountryCode, Is.EqualTo(phoneNumber.CountryCode));
            Assert.That(result!.Contact!.State, Is.EqualTo(mockContactResponse.State));
            Assert.That(result!.Contact!.Street, Is.EqualTo(mockContactResponse.Street));
            Assert.That(result!.Contact!.Street2, Is.EqualTo(mockContactResponse.Street2));
            Assert.That(result!.Contact!.ZipCode, Is.EqualTo(mockContactResponse.ZipCode));
        });
    }

    [Test]
    public async Task GetContactDetailForViewModel_ShouldReturnModel_WhenRegistrationFilingIdValidAsync()
    {
        // Arrange
        var contactId = 0;
        var registrationFilingId = 1;
        var cancellationToken = CancellationToken.None;
        var mockContactResponse = new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
                , 1
                , 1
                , new List<PhoneNumber>() { new(null, "+1", 1, 1, "987", 1, false, false, 1, "1011231234", 1, "Phone"), new(null, "+1", 1, 1, "678", 1, false, false, 1, "2029871234", 1, "Fax"), }
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , "12345");
        var addressList = new List<AddressDtoModel>
        {
            new("Test City", "United States", "home","HI","123 Sample St.", "APT A", "work", "12345")
        };
        var phoneNumberList = new List<PhoneNumberDto>
        {
            new("+1", null, "987", 1, false, "1011231234", 1, false, "Phone"),
            new("+1", null, "678", 1, false, "2029871234", 1, false,"Fax"),
        };
        var mockLobbyistEmployerResponse = new LobbyistEmployerResponseDto(addressList, 1, new List<RegistrationAgencyDto>(), "test", "test dsc", new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local), "<EMAIL>", "dummy employee", "developer", 1, registrationFilingId, "test", "class", "portion", "type", false, 0, "lobbying interest", new List<LobbyingEmployerGroupMemberDto>(), "dummy name", "interest", 1, 1, phoneNumberList, true, 1, 1);

        var mockModel = new PaymentMadeToLobbyingCoalitionViewModel
        {
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
        };

        _mockLobbyistEmployerApi.Setup(x => x.GetLobbyistEmployer(registrationFilingId, It.IsAny<CancellationToken>())).ReturnsAsync(mockLobbyistEmployerResponse);

        // Act
        var result = await _transactionCtlSvc.GetContactDetailForViewModel(mockModel, cancellationToken);

        // Assert

        var faxNumber = mockContactResponse.PhoneNumbers.First(x => x.Type == "Fax");
        var phoneNumber = mockContactResponse.PhoneNumbers.First(x => x.Type == "Phone");
        Assert.Multiple(() =>
        {
            Assert.That(result!.Contact!.City, Is.EqualTo(mockContactResponse.City));
            Assert.That(result!.Contact!.Country, Is.EqualTo(mockContactResponse.Country));
            Assert.That(result!.Contact!.EmailAddress, Is.EqualTo(mockContactResponse.EmailAddresses[0].Email));
            Assert.That(result!.Contact!.FaxNumber, Is.EqualTo(faxNumber.Number));
            Assert.That(result!.Contact!.FaxNumberCountryCode, Is.EqualTo(faxNumber.CountryCode));
            Assert.That(result!.Contact!.PhoneNumber, Is.EqualTo(phoneNumber.Number));
            Assert.That(result!.Contact!.PhoneNumberCountryCode, Is.EqualTo(phoneNumber.CountryCode));
            Assert.That(result!.Contact!.State, Is.EqualTo(mockContactResponse.State));
            Assert.That(result!.Contact!.Street, Is.EqualTo(mockContactResponse.Street));
            Assert.That(result!.Contact!.Street2, Is.EqualTo(mockContactResponse.Street2));
            Assert.That(result!.Contact!.ZipCode, Is.EqualTo(mockContactResponse.ZipCode));
        });
    }

    [Test]
    public async Task HandlePaymentToLobbyingCoalitionPage02Submit_ShouldCreateNewContact_WhenContactIdIsNull()
    {
        // Arrange
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            ContactId = null, // Initially null to simulate new contact creation
            FilerId = 12345,
            LobbyingCoalitionName = "test name",
            Contact = new GenericContactViewModel
            {
                City = "New York",
                EmailAddress = "<EMAIL>",
                PhoneNumber = "************",
                State = "NY",
                Street = "123 Main St",
                Street2 = "Apt 4B",
                Website = "https://example.com",
                ZipCode = "10001"
            }
        };

        // Mocking the CreateContact API
        var newContact = new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
                , 1
                , 1
                , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax"), }
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , "12345");
        _mockContactsApi.Setup(api => api.CreateFilerContact(It.IsAny<long>(), It.IsAny<UpsertOrganizationFilerContactRequest>(), It.IsAny<CancellationToken>()))
                       .ReturnsAsync(newContact);

        // Act
        var result = await _transactionCtlSvc.HandlePaymentToLobbyingCoalitionPage02Submit(model, CancellationToken.None);

        // Assert
        Assert.That(model.ContactId, Is.EqualTo(1)); // ContactId should be set to the newly created contact's Id (1)
        _mockContactsApi.Verify(api => api.CreateFilerContact(It.IsAny<long>(), It.IsAny<UpsertOrganizationFilerContactRequest>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task HandlePaymentToLobbyingCoalitionPage02Submit_ShouldUpdateExistingContact_WhenContactIdIsValid()
    {
        // Arrange
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            ContactId = 1,
            FilerId = 12345,
            LobbyingCoalitionName = "test name",
            Contact = new GenericContactViewModel
            {
                City = "New York",
                EmailAddress = "<EMAIL>",
                PhoneNumber = "************",
                State = "NY",
                Street = "123 Main St",
                Street2 = "Apt 4B",
                Website = "http://example.com",
                ZipCode = "10001"
            }
        };

        var fakeResponse = new ContactItemResponse(
            addressId: 1,
            city: "New York",
            country: "US",
            emailAddresses: [],
            filerId: 12345,
            id: 1,
            phoneNumbers: [],
            state: "NY",
            street: "123 Main St",
            street2: "Apt 4B",
            typeId: 5,
            valid: true,
            validationErrors: [],
            website: "http://example.com",
            zipCode: "10001"
        );

        _mockContactsApi.Setup(api => api.UpdateFilerContact(It.IsAny<long>(), It.IsAny<UpsertOrganizationFilerContactRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fakeResponse);

        // Act
        var result = await _transactionCtlSvc.HandlePaymentToLobbyingCoalitionPage02Submit(model, CancellationToken.None);

        // Assert
        Assert.That(result, Is.EqualTo(1)); // ContactId should be unchanged
        _mockContactsApi.Verify(api => api.UpdateFilerContact(It.IsAny<long>(), It.IsAny<UpsertOrganizationFilerContactRequest>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task HandlePaymentToLobbyingCoalitionPage03Submit_ShouldAddModelError_WhenResponseIsInvalid()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var requestDto = new PaymentMadeToLobbyingCoalitionRequestDto(-1, 1, 1, 1, null, null); // Invalid value
        var errorResponse = new TransactionResponseDto(1, false, new List<WorkFlowError>
        {
            new("101", "error", "PeriodAmount", "Amount is invalid")
        });

        _mockLobbyistEmployerCoalitionApi.Setup(api => api.CreatePaymentMadeToLobbyingCoalition(It.IsAny<PaymentMadeToLobbyingCoalitionRequestDto>(), It.IsAny<CancellationToken>()))
                                        .ReturnsAsync(errorResponse);

        // Act
        var result = await _transactionCtlSvc.HandlePaymentToLobbyingCoalitionPage03Submit(requestDto, modelState, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.IsValid, Is.False);
            Assert.That(modelState.ContainsKey("Amount"), Is.True);
            Assert.That(modelState["Amount"]!.Errors[0].ErrorMessage, Is.EqualTo("Amount is invalid"));
        });
        _mockLobbyistEmployerCoalitionApi.Verify(api => api.CreatePaymentMadeToLobbyingCoalition(It.IsAny<PaymentMadeToLobbyingCoalitionRequestDto>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task HandlePaymentToLobbyingCoalitionPage03Submit_ShouldReturnValidResponse_WhenResponseIsValid()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var requestDto = new PaymentMadeToLobbyingCoalitionRequestDto(1000, 1, 1, 1, null, null); // Valid value
        var validResponse = new TransactionResponseDto(1, true, new List<WorkFlowError>());

        _mockLobbyistEmployerCoalitionApi.Setup(api => api.CreatePaymentMadeToLobbyingCoalition(It.IsAny<PaymentMadeToLobbyingCoalitionRequestDto>(), It.IsAny<CancellationToken>()))
                                        .ReturnsAsync(validResponse);

        // Act
        var result = await _transactionCtlSvc.HandlePaymentToLobbyingCoalitionPage03Submit(requestDto, modelState, CancellationToken.None);

        // Assert
        Assert.That(modelState.IsValid, Is.True);
        _mockLobbyistEmployerCoalitionApi.Verify(api => api.CreatePaymentMadeToLobbyingCoalition(It.IsAny<PaymentMadeToLobbyingCoalitionRequestDto>(), It.IsAny<CancellationToken>()), Times.Once);
    }
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.

}
