using System.Globalization;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

/// <summary>
/// Service for managing Lobbyist registrations.
/// </summary> 
public sealed class LobbyistRegistrationSvc(
    LobbyistRegistrationSvcDependencies dependencies,
    IRegistrationModelMapper modelMapper,
    IDateTimeSvc dateTimeSvc
    ) : ILobbyistRegistrationSvc
{
    public async Task<RegistrationResponseDto> SubmitLobbyistRegistrationForEfile(LobbyistRegistrationSubmissionDto request)
    {
        var lobbyistReg = request.Lobbyist;

        var lobbyistInfo = PopulateDecisionServiceRequest(lobbyistReg);
        lobbyistInfo.LobbyingEmployerOrFirmId = request.LobbyistEmployerOrLobbyingFirmId;

        var validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(DecisionsWorkflow.FRLOBFilingLobbyistStep1GeneralInfo, lobbyistInfo, true);

        if (validationErrors.Count > 0)
        {
            return new RegistrationResponseDto(null, false, validationErrors, null);
        }

        // Create the lobbyist registration
        var registration = await dependencies.RegistrationRepository.CreateLobbyist(lobbyistReg);

        if (registration is not null)
        {
            var filerRequest = new Filer
            {
                CurrentRegistrationId = registration.Id,
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.Lobbyist.Id,
                CreatedBy = request.UserId,
                Users = new List<FilerUser>
                {
                    new()
                    {
                        FilerRoleId = FilerRole.Lobbyist_ThirdPartyVendor.Id,
                        UserId = request.UserId,
                    }
                }
            };

            // Create the Filer
            var filerId = await dependencies.FilerSvc.AddFilerAsync(filerRequest);

            registration.OriginalId = registration.Id;
            registration.FilerId = filerId;
            registration.CreatedBy = request.UserId;

            // Link the lobbyist to the employer or firm
            await LinkLobbyistToEmployerOrFirmAsync(
                filerId: filerId,
                lobbyistEmployerOrLobbyingFirmId: request.LobbyistEmployerOrLobbyingFirmId,
                filerService: dependencies.FilerSvc,
                filerLinkRepository: dependencies.FilerLinkRepository,
                request.UserId);

            // Update the registration with the Filer ID
            await dependencies.RegistrationRepository.Update(registration);
        }
        return new RegistrationResponseDto(registration?.Id, true, validationErrors, registration?.StatusId, filerId: registration?.FilerId);
    }

    public async Task<RegistrationResponseDto> CreateLobbyistRegistrationPage03(LobbyistRegistrationRequestDto request)
    {
        bool isValid = false;

        var lobbyistRegistrationRequest = modelMapper.MapLobbyistRegistrationRequestToModel(request);

        var validationErrors = new List<WorkFlowError>();
        if (!request.IgnoreDecisionRule)  //TD will implement Decision rule for inhouse lobbyist 
        {
            var decisionWorkFlow = request.IsNewCertification.HasValue && request.IsNewCertification.Value
                ? DecisionsWorkflow.FRLOBFilingLobbyistStep1GeneralInfo
                : DecisionsWorkflow.FRLOBFilingLobbyistRenewalStep1GeneralInfo;

            DecisionsLobbyistRegistrationGeneralInfo lobbyistRegistrationGeneralInfoDsInput = PopulateDecisionServiceRequest(lobbyistRegistrationRequest, request);
            validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(decisionWorkFlow, lobbyistRegistrationGeneralInfoDsInput, request.CheckRequiredFieldsFlag);

            if (validationErrors.Count > 0)
            {
                return new RegistrationResponseDto(null, false, validationErrors, null);
            }
            else
            {
                isValid = true;
            }
        }
        var registration = await dependencies.RegistrationRepository.CreateLobbyist(lobbyistRegistrationRequest);

        if (registration is not null)
        {
            var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();

            var filerRequest = new Filer
            {
                CurrentRegistrationId = registration.Id,
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.Lobbyist.Id,
                CreatedBy = userId,
                Users = new List<FilerUser>
                {
                    new()
                    {
                        FilerRoleId = (request.SelfRegister == true) switch
                        {
                            true => FilerRole.Lobbyist_AccountManager.Id,
                            false => FilerRole.Lobbyist_Lobbyist.Id
                        },
                        UserId = userId.Value,
                    }
                }
            };

            var filerId = await dependencies.FilerSvc.AddFilerAsync(filerRequest);

            registration.OriginalId = registration.Id;
            registration.FilerId = filerId;
            registration.CreatedBy = userId.GetValueOrDefault();

            await LinkLobbyistToEmployerOrFirmAsync(
                filerId: filerId,
                lobbyistEmployerOrLobbyingFirmId: request.LobbyistEmployerOrLobbyingFirmId,
                filerService: dependencies.FilerSvc,
                filerLinkRepository: dependencies.FilerLinkRepository,
                userId);

            if (request.Photo is not null)
            {
                var uploadedFile = await dependencies.UploadFileSvc.FindUploadByFileNameGuid(request.Photo) ?? throw new KeyNotFoundException($"Uploaded file with GUID {request.Photo} not found.");
                uploadedFile.RelationshipId = registration.Id;
                await dependencies.UploadFileSvc.UpdateUploadedFile(uploadedFile);
            }

            await dependencies.RegistrationRepository.Update(registration);
        }
        return new RegistrationResponseDto(registration?.Id, isValid, validationErrors, registration?.StatusId, filerId: registration?.FilerId);
    }

    /// <inheritdoc />
    public async Task<LobbyistResponseDto?> GetLobbyistByFilerId(long id)
    {
        var lobbyist = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Lobbyist>(id)
            ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {id} not found.");

        return new LobbyistResponseDto(lobbyist);
    }

    /// <inheritdoc />
    public async Task<LobbyistResponseDto?> GetLobbyistRegistration(long id)
    {
        var lobbyist = await GetRegistrationById(id);

        return lobbyist;
    }

    /// <summary>
    /// Search for  lobbyist employer or lobbying firm by Id and Name
    /// </summary>
    /// <param name="q">query term to partially search for</param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    public async Task<IEnumerable<LobbyistEmployerOrLobbyistFirmSearchResultDto>> SearchLobbyistEmployerOrLobbyingFirmByIdOrName(string q)
    {
        var filers = await dependencies.RegistrationRepository.FindLobbyistEmployerOrLobbyingFirmByIdOrName(q);

        List<LobbyistEmployerOrLobbyistFirmSearchResultDto> list = new();
        foreach (Filer filer in filers)
        {
            LobbyistEmployerOrLobbyistFirmSearchResultDto item = new()
            {
                Id = filer.Id,
                Name = filer.CurrentRegistration!.Name,
            };
            list.Add(item);
        }
        return list;
    }

    /// <summary>
    /// Cancels a Draft Lobbyist Registration
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task CancelLobbyistRegistration(long id)
    {
        var registration = await dependencies.RegistrationRepository.FindById(id);
        if (registration == null)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            throw new InvalidOperationException($"Cannot cancel a registration that is not in 'Draft' status. Id={id} Status={registration.StatusId}");
        }

        registration.StatusId = RegistrationStatus.Canceled.Id;

        await dependencies.RegistrationRepository.Update(registration);
    }

    public async Task<RegistrationResponseDto> UpdateLobbyistRegistration(long id, LobbyistRegistrationRequestDto request)
    {
        bool isValid = false;

        if (await dependencies.RegistrationRepository.FindLobbyistById(id) is not Lobbyist existingRegistration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        var registration = modelMapper.UpdateLobbyist(request, existingRegistration);
        registration.Id = id;
        var validationErrors = new List<WorkFlowError>();

        if (!request.IgnoreDecisionRule) //TD will implement Decision rule for inhouse lobbyist 
        {
            DecisionsLobbyistRegistrationGeneralInfo lobbyistRegistrationGeneralInfoDsInput = PopulateDecisionServiceRequest(registration, request);

            if (registration.Version > 0)
            {
                var amendLobbyistRegistrationGeneralInfoDsInput = new DecisionsAmendLobbyistRegistrationGeneralInfo { EffectiveDateOfChanges = request.EffectiveDateOfChanges, GeneralInfo = lobbyistRegistrationGeneralInfoDsInput };
                validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsAmendLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(DecisionsWorkflow.FRLOBFilingAmendRegistrationStep1GeneralInfo, amendLobbyistRegistrationGeneralInfoDsInput, request.CheckRequiredFieldsFlag);
            }
            else
            {
                validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(DecisionsWorkflow.FRLOBFilingLobbyistStep1GeneralInfo, lobbyistRegistrationGeneralInfoDsInput, request.CheckRequiredFieldsFlag);
            }
        }

        if (validationErrors.Count == 0)
        {
            isValid = true;
            await dependencies.RegistrationRepository.Update(registration);

            var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();

            await LinkLobbyistToEmployerOrFirmAsync(
                filerId: request.FilerId,
                lobbyistEmployerOrLobbyingFirmId: request.LobbyistEmployerOrLobbyingFirmId,
                filerService: dependencies.FilerSvc,
                filerLinkRepository: dependencies.FilerLinkRepository,
                userId: userId);
        }

        return new RegistrationResponseDto(registration.Id, isValid, validationErrors, registration.StatusId);
    }

    private static async Task LinkLobbyistToEmployerOrFirmAsync(
    long? filerId,
    long? lobbyistEmployerOrLobbyingFirmId,
    IFilerSvc filerService,
    IFilerLinkRepository filerLinkRepository,
    long? userId)
    {
        if (filerId == null || lobbyistEmployerOrLobbyingFirmId == null || userId == null)
        {
            return;
        }

        var linkedEntity = await filerService.GetFiler(lobbyistEmployerOrLobbyingFirmId.Value);
        if (linkedEntity == null)
        {
            return;
        }

        var linkType = linkedEntity.FilerTypeId switch
        {
            var _ when linkedEntity.FilerTypeId == FilerType.LobbyistEmployer.Id => FilerLinkType.LobbyistEmployer,
            var _ when linkedEntity.FilerTypeId == FilerType.LobbyingFirm.Id => FilerLinkType.LobbyingFirm,
            _ => null
        };

        if (linkType != null)
        {
            await filerLinkRepository.LinkEntityTypeToFiler(
                filerId: filerId.Value,
                linkedEntityId: lobbyistEmployerOrLobbyingFirmId.Value,
                linkType: linkType,
                userId: userId,
                entityInLinkType: [FilerLinkType.LobbyistEmployer, FilerLinkType.LobbyingFirm]);
        }
    }

    private static DecisionsLobbyistRegistrationGeneralInfo PopulateDecisionServiceRequest(Lobbyist lobbyistRegistration, LobbyistRegistrationRequestDto? lobbyistRegistrationRequest = null)
    {
        var lobbyistRegistrationGeneralInfoDsInput = new DecisionsLobbyistRegistrationGeneralInfo
        {
            FilerID = lobbyistRegistration.FilerId,
            IsMyselfOrLobbyist = lobbyistRegistration.SelfRegister switch
            {
                true => "Myself",
                false => "Lobbyist",
                _ => null
            },
            FirstName = lobbyistRegistration.FirstName,
            LastName = lobbyistRegistration.LastName,
            Email = lobbyistRegistration.Email,
            PhoneNumber = PopulatePhoneAndFaxNumbers(lobbyistRegistration, RegistrationConstants.PhoneNumber.TypeHome),
            FaxNumber = PopulatePhoneAndFaxNumbers(lobbyistRegistration, RegistrationConstants.PhoneNumber.TypeFax),
            OrganizationAddress = MapAddress(lobbyistRegistration.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == "Business")),
            MailingAddress = MapAddress(lobbyistRegistration.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == "Mailing")),
            LegislativeSession = lobbyistRegistration.LegislativeSessionId?.ToString(CultureInfo.InvariantCulture),
            DateOfQualification = lobbyistRegistration.DateQualified,
            LobbyingEmployerOrFirmId = lobbyistRegistrationRequest != null ? lobbyistRegistrationRequest.LobbyistEmployerOrLobbyingFirmId : lobbyistRegistration.Filer?.FilerLinks?.FirstOrDefault(x => x!.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id || x!.FilerLinkTypeId == FilerLinkType.LobbyingFirm.Id)?.LinkedEntityId,
            IsPlacementAgent = lobbyistRegistration.PlacementAgent,
            CompletedEthicsCourse = lobbyistRegistration.EthicsCourseCompleted,
            CompletedCourseDate = lobbyistRegistration.EthicsCourseCompletionDate,
            EthicsCertNewOrRenewal = lobbyistRegistration.IsNewCertification switch
            {
                true => "New",
                false => "Renewal",
                _ => null
            },
            AgenciesLobbied = lobbyistRegistration.LobbyOnlySpecifiedAgencies switch
            {
                true => "I will only lobby the agencies identified below",
                false => "I will lobby the agencies identified on the Lobbyist Employer or Lobbying Firm Registration Statement and subsequent amendments",
                _ => null
            },
            Agencies = [.. lobbyistRegistration.RegistrationAgencies.Select(static a => a.AgencyId.ToString(CultureInfo.InvariantCulture))],
            IsLobbyingStateLegislature = lobbyistRegistration.StateLegislatureLobbying,
            Photo = lobbyistRegistrationRequest != null ? lobbyistRegistrationRequest.Photo : "1"
            //TD: Logic for getting photo relationship for update workflow
        };

        return lobbyistRegistrationGeneralInfoDsInput;
    }

    private static DecisionsAddress? MapAddress(Address? address)
    {
        if (address == null)
        {
            return null;
        }

        return new DecisionsAddress
        {
            Street = address.Street,
            Street2 = address.Street2,
            City = address.City,
            Country = address.Country,
            Purpose = address.Purpose,
            State = address.State,
            Type = address.Type,
            Zip = address.Zip
        };
    }

    private static string? PopulatePhoneAndFaxNumbers(Lobbyist candidateIntentionStatement, string type)
    {
        //TODO: Need to refactor using Country Code from Country table with CountryID instead of CountryCode column in PhoneNumber table
        var phoneNumber = candidateIntentionStatement?.PhoneNumberList?.PhoneNumbers?.FirstOrDefault(x => x.Type == type);
        if (phoneNumber is null || phoneNumber.Number is null)
        {
            return string.Empty;
        }
        return phoneNumber.CountryId.HasValue ? phoneNumber.Number : string.Join("", phoneNumber.CountryCode, phoneNumber.Number);
    }

    private static WorkFlowError CreateUniqueLobbyistNameValidationError()
    {
        return new WorkFlowError("Name", RegistrationConstants.ValidationError.ErrorCodeGlobal0002, RegistrationConstants.ValidationError.ErrorTypeValidation, $"Lobbyist names must be unique. There is an active lobbyist with this name.");
    }

    /// <summary>
    /// Self register dictates business flow
    /// True - Attest
    /// False - Send for attestation
    /// </summary>
    public async Task<RegistrationResponseDto> SubmitLobbyistRegistration(long id)
    {
        if (await dependencies.RegistrationRepository.FindLobbyistById(id) is not Lobbyist registration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        if (registration.SelfRegister == true)
        {
            await dependencies.AttestationRepository.Create(
                new Attestation
                {
                    CreatedBy = 0,
                    ExecutedAt = dateTimeSvc.GetCurrentDateTime(),
                    ModifiedBy = 0,
                    Name = registration.Name,
                    RegistrationId = id,

                });
        }

        var response = await ValidateLobbyistRegistrationSubmission(registration, registration.ParentId != null);

        if (response.Valid && response.StatusId.HasValue)
        {
            // Get Lobbyist Registration Status Id based on the related Lobbyist Employer
            var registrationStatusId = await GetLobbyistRegistrationStatusId(registration, response.StatusId.Value);
            registration.StatusId = registrationStatusId;
            registration.Version = registration.Version ?? 0;
            await dependencies.RegistrationRepository.Update(registration);
        }

        if (response.Notifications != null &&
            response.Notifications.Any(n => n.SendNotification) &&
            registration.FilerId.HasValue)
        {
            var validNotifications = response.Notifications
                .Where(n => n.SendNotification && n.NotificationTemplateId != null)
                .ToList();

            if (validNotifications.Count != 0)
            {
                try
                {
                    await SendNotificationSubmitLobbyistRegistration(registration, validNotifications);
                }
                catch (Exception)
                {
                    // Background notifications may fail silently
                }
            }
        }

        if (registration.SelfRegister == false)
        {
            try
            {
                await dependencies.LinkageSvc.SendLinkageRequestToPerson(
                    new SendLinkageRequestToPersonDto
                    {
                        FilerId = registration.FilerId!.Value,
                        FilerRoleId = FilerRole.Lobbyist_Lobbyist.Id,
                        RecipientEmail = registration.Email!,
                        RecipientName = registration.Name!,
                    });
            }
            catch (Exception)
            {
                // Will currently throw for UserNotificationPreferences. Feature is WIP
            }
        }
        return response;
    }

    private async Task SendNotificationSubmitLobbyistRegistration(Lobbyist registration, List<NotificationTrigger> validNotifications)
    {
        foreach (var notification in validNotifications)
        {
            var templateId = notification.NotificationTemplateId!.Value;

            long filerId = registration!.FilerId!.Value;
            var notificationData = new Dictionary<string, string> { { "DocumentId", registration.Id.ToString(CultureInfo.InvariantCulture) } };

            if (notification.Recipient == nameof(NotificationRecipient.LinkedFilerUser))
            {
                var employerId = registration.Filer?.FilerLinks?
                    .FirstOrDefault(link =>
                        link.FilerLinkTypeId == FilerLinkType.LobbyingFirm.Id ||
                        link.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id)?.LinkedEntityId;

                var employer = employerId.HasValue
                    ? await dependencies.RegistrationRepository.FindLobbyistEmployerById(employerId.Value)
                    : null;

                if (employer?.FilerId is long employerFilerId)
                {
                    notificationData["FilerName"] = employer.EmployerName ?? string.Empty;
                    filerId = employerFilerId;
                }
            }

            await dependencies.NotificationSvc.SendFilerNotification(new SendFilerNotificationRequest(
                NotificationTemplateId: templateId,
                FilerId: filerId,
                DueDate: null,
                NotificationData: notificationData));
        }
    }

    /// <summary>
    /// Get Lobbyist Registration Status ID based on the related Lobbyist Employer
    /// </summary>
    /// <param name="registration">Lobbyist Entity</param>
    /// <param name="responseStatusId">The status returned from Decision response</param>
    /// <returns>Registration status ID</returns>
    private async Task<long> GetLobbyistRegistrationStatusId(Registration registration, long responseStatusId)
    {
        var linkedEmployerEntity = registration.Filer?.FilerLinks?.FirstOrDefault(link =>
            link.FilerLinkTypeId == FilerLinkType.LobbyingFirm.Id ||
            link.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id);

        var validStatuses = new List<long>()
        {
            RegistrationStatus.PendingFilerAction.Id,
            RegistrationStatus.Submitted.Id,
            RegistrationStatus.Pending.Id,
            RegistrationStatus.Accepted.Id
        };

        if (linkedEmployerEntity == null)
        {
            return responseStatusId;
        }

        var employerRegistration = await dependencies.RegistrationRepository
            .FindLobbyistEmployerById(linkedEmployerEntity.LinkedEntityId);

        // Return response status if registration not found or it's the original or it's the amendment with invalid status
        bool isOriginal = employerRegistration?.OriginalId == employerRegistration?.Id;
        bool isInvalidStatusAmendment = employerRegistration != null &&
                               employerRegistration.OriginalId != employerRegistration.Id &&
                               !validStatuses.Contains(employerRegistration.StatusId);

        if (employerRegistration == null || isOriginal || isInvalidStatusAmendment)
        {
            return responseStatusId;
        }

        // Determine status based on termination date
        return linkedEmployerEntity.TerminationDate.HasValue
            ? RegistrationStatus.PendingFilerAction.Id
            : RegistrationStatus.Accepted.Id;
    }

    private async Task<RegistrationResponseDto> ValidateLobbyistRegistrationSubmission(Lobbyist registration, bool isAmend)
    {
        var id = registration.Id;
        var isSelfRegister = registration.SelfRegister.GetValueOrDefault();
        var isRenewal = registration.IsNewCertification.HasValue && !registration.IsNewCertification.Value && registration.Id != registration.OriginalId;

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            throw new InvalidOperationException($"Cannot submit a registration that is not in 'Draft' status. Id={id} Status={registration.StatusId}");
        }

        var lobbyistRequestDs = PopulateDecisionServiceRequest(registration);

        var workflowType = GetDecisionWorkflowLobbyistRegistration(isSelfRegister, isAmend, isRenewal);

        StandardDecisionsSubmissionResponse decisionsResponse;

        if (isAmend)
        {
            var amendLobbyistRegistrationGeneralInfoDsInput = new DecisionsAmendLobbyistRegistrationGeneralInfo { EffectiveDateOfChanges = registration.EffectiveDateOfChanges, GeneralInfo = lobbyistRequestDs };
            decisionsResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsAmendLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                workflowType,
                amendLobbyistRegistrationGeneralInfoDsInput,
                true);
        }
        else
        {
            decisionsResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, StandardDecisionsSubmissionResponse>(
                workflowType,
                lobbyistRequestDs,
                true);
        }

        bool isValid = decisionsResponse?.Errors?.Count == 0;
        long statusId = decisionsResponse switch
        {
            { Errors.Count: 0 } when isSelfRegister == true => RegistrationStatus.Accepted.Id,
            { Errors.Count: 0 } => RegistrationStatus.Pending.Id,
            _ => registration.StatusId
        };

        return new RegistrationResponseDto(
            registration.Id,
            isValid,
            decisionsResponse?.Errors,
            statusId,
            decisionsResponse?.Notifications);
    }

    public async Task<LobbyistResponseDto> CreateLobbyistAmendmentRegistrationAsync(long id)
    {
        // ID could be the Parent or Amendment registration
        var registration = await dependencies.RegistrationRepository.FindLobbyistById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // Only Accepted or Rejected status can create an amendment
        if (registration.StatusId != RegistrationStatus.Accepted.Id && registration.StatusId != RegistrationStatus.Rejected.Id)
        {
            return new LobbyistResponseDto(registration);
        }

        // Get UserID
        var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();

        // Clone lobbyist registration
        var amendmentRegistration = CloneLobbyistRegistration<Lobbyist>(registration);

        // To create a new amendment, we will clone everything from the parent, increase the version, change the parent, and set the status to Draft
        // Reset some related entities and set amendment-specific properties 
        ResetLobbyistRegistration(amendmentRegistration, registration, userId.GetValueOrDefault());

        // Re-create some related entities IDs.
        // Clone Address
        CloneRegistrationAddressList(amendmentRegistration.AddressList);

        // Clone Phone Number
        CloneRegistrationPhoneNumberList(amendmentRegistration.PhoneNumberList);

        // Create amendment lobbyist registration
        await dependencies.RegistrationRepository.Create(amendmentRegistration);

        return new LobbyistResponseDto(amendmentRegistration);
    }

    private static T CloneLobbyistRegistration<T>(Lobbyist source) where T : Registration
    {
        var settings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            PreserveReferencesHandling = PreserveReferencesHandling.None
        };

        // Serialize/De-serialize object to remove the tracking.
        var json = JsonConvert.SerializeObject(source, settings);
        var registration = JsonConvert.DeserializeObject<T>(json)!;

        return registration;
    }

    private static void ResetLobbyistRegistration(Lobbyist amendmentRegistration, Lobbyist originalRegistration, long userId)
    {
        amendmentRegistration.Id = 0;
        amendmentRegistration.Version = (originalRegistration.Version ?? 0) + 1;
        amendmentRegistration.ParentId = originalRegistration.Id;
        amendmentRegistration.StatusId = RegistrationStatus.Draft.Id;
        amendmentRegistration.SubmittedAt = null;
        amendmentRegistration.ApprovedAt = null;
        amendmentRegistration.TerminatedAt = null;
        amendmentRegistration.CreatedBy = userId;
        amendmentRegistration.LegislativeSession = null;
        amendmentRegistration.LegislativeSessionId = originalRegistration.LegislativeSessionId;
        amendmentRegistration.RegistrationAgencies = [];

        // Keep the Filer remain
        amendmentRegistration.FilerId = originalRegistration.FilerId;
        amendmentRegistration.Filer = null;
        amendmentRegistration.Attestations = [];
        amendmentRegistration.RegistrationRegistrationContacts = [];
    }

    private static void CloneRegistrationAddressList(AddressList? addressList)
    {
        if (addressList is null)
        {
            return;
        }

        addressList.Id = 0;
        foreach (var addr in addressList.Addresses ?? [])
        {
            addr.Id = 0;
        }
    }

    private static void CloneRegistrationPhoneNumberList(PhoneNumberList? phoneNumberList)
    {
        if (phoneNumberList is null)
        {
            return;
        }

        phoneNumberList.Id = 0;
        foreach (var phone in phoneNumberList.PhoneNumbers ?? [])
        {
            phone.Id = 0;
        }
    }

    private static void ResetRegistration(Registration registration, Lobbyist originalRegistration, long userId)
    {
        registration.Id = 0;
        registration.Version = (originalRegistration.Version ?? 0) + 1;
        registration.ParentId = originalRegistration.Id;
        registration.StatusId = RegistrationStatus.Draft.Id;
        registration.CreatedBy = userId;

        // Keep the Filer remain
        registration.FilerId = originalRegistration.FilerId;
        registration.Filer = null;
        registration.Attestations = [];
        registration.RegistrationRegistrationContacts = [];
    }

    /// <summary>
    /// Process withdraw lobbyist registration
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<RegistrationResponseDto> WithdrawLobbyistRegistration(long id, WithdrawLobbyistRegistrationRequestDto request)
    {
        var registrationType = await dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");
        // Check Decision Error
        var decisionsResponse = await dependencies.DecisionsSvc
            .InitiateWorkflow<DecisionsLobbyistRegistrationWithdrawal, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.LobbyistRegistrationSubmitWithdrawal,
                new DecisionsLobbyistRegistrationWithdrawal()
                {
                    EffectiveDateOfWithdrawal = request.WithdrawnAt,
                    LegislativeSessionId = request.LegislativeSessionId
                },
                true);

        if (decisionsResponse?.Errors.Count > 0)
        {
            return new RegistrationResponseDto(id, false, decisionsResponse.Errors, request.StatusId);
        }

        if (registrationType == nameof(Lobbyist))
        {
            var registration = await dependencies.RegistrationRepository.FindLobbyistById(id);
            // Clone registration
            var withdrawRegistration = CloneLobbyistRegistration<LobbyistWithdrawal>(registration!);

            // Get UserID
            var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();

            ResetRegistration(withdrawRegistration, registration!, userId.GetValueOrDefault());
            withdrawRegistration.Version = null;

            // Re-create some related entities IDs.
            // Clone Address
            CloneRegistrationAddressList(withdrawRegistration.AddressList);

            // Clone Phone Number
            CloneRegistrationPhoneNumberList(withdrawRegistration.PhoneNumberList);

            if (request.StatusId != null)
            {
                withdrawRegistration.StatusId = request.StatusId.Value;
            }

            withdrawRegistration.WithdrawnAt = request.WithdrawnAt;
            var newWithdrawal = await dependencies.RegistrationRepository.Create(withdrawRegistration);
            return new RegistrationResponseDto(id: newWithdrawal.Id, true, decisionsResponse?.Errors, newWithdrawal.StatusId);
        }

        // If the registration is not a Lobbyist, it must be a LobbyistWithdrawal
        var lobbyistWithdrawal = await dependencies.RegistrationRepository.FindLobbyistWithdrawalById(id)
                                 ?? throw new KeyNotFoundException($"Lobbyist withdrawal with ID {id} not found.");
        // Update the withdrawal registration
        lobbyistWithdrawal.WithdrawnAt = request.WithdrawnAt;
        if (request.StatusId.HasValue)
        {
            lobbyistWithdrawal.StatusId = request.StatusId.GetValueOrDefault();
        }

        await dependencies.RegistrationRepository.Update(lobbyistWithdrawal);
        return new RegistrationResponseDto(id, true, decisionsResponse?.Errors, lobbyistWithdrawal.StatusId);
    }

    public async Task<RegistrationResponseDto> SendLobbyistRegistrationWithdrawal(long id, WithdrawLobbyistRegistrationRequestDto request)
    {
        // Decisions flow
        var decisionsWorkflow = request.StatusId == RegistrationStatus.Accepted.Id
            ? DecisionsWorkflow.LobbyistRegistrationSubmitWithdrawal
            : DecisionsWorkflow.LobbyistRegistrationSendForAttestationWithdrawal;
        var decisionsResponse = await dependencies.DecisionsSvc
            .InitiateWorkflow<DecisionsLobbyistRegistrationWithdrawal, StandardDecisionsSubmissionResponse>(
                decisionsWorkflow,
                new DecisionsLobbyistRegistrationWithdrawal()
                {
                    EffectiveDateOfWithdrawal = request.WithdrawnAt,
                    LegislativeSessionId = request.LegislativeSessionId
                },
                true);

        if (decisionsResponse?.Errors.Count > 0)
        {
            return new RegistrationResponseDto(id, false, decisionsResponse.Errors, request.StatusId);
        }

        // Update registration
        var lobbyistWithdrawal = await dependencies.RegistrationRepository.FindLobbyistWithdrawalById(id)
            ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {id} not found.");

        var newRegistrationStatusId = request.StatusId.GetValueOrDefault();

        // If the Request Withdraw is Submitted, need to update the status to PendingFilerAction or Accepted
        if (request.StatusId == RegistrationStatus.Accepted.Id)
        {
            var defaultStatusUpdated = RegistrationStatus.PendingFilerAction.Id;
            newRegistrationStatusId = await GetLobbyistRegistrationStatusId(lobbyistWithdrawal!, defaultStatusUpdated);
        }
        lobbyistWithdrawal.StatusId = newRegistrationStatusId;
        lobbyistWithdrawal.WithdrawnAt = request.WithdrawnAt;
        lobbyistWithdrawal.SubmittedAt = request.ExecutedOn;

        await dependencies.RegistrationRepository.Update(lobbyistWithdrawal);

        // Send notification
        if (decisionsResponse?.Notifications != null &&
            decisionsResponse.Notifications.Any(n => n.SendNotification) &&
            lobbyistWithdrawal.FilerId.HasValue)
        {
            var validNotifications = decisionsResponse.Notifications
                .Where(n => n.SendNotification && n.NotificationTemplateId != null)
                .ToList();

            if (validNotifications.Count != 0)
            {
                var notificationTasks = validNotifications
                    .Select(n => dependencies.NotificationSvc.SendFilerNotification(
                        new SendFilerNotificationRequest(
                            DueDate: DateTime.UtcNow,
                            NotificationTemplateId: n.NotificationTemplateId!.Value,
                            NotificationData: null,
                            FilerId: lobbyistWithdrawal.FilerId.Value
                            )));

                try { await Task.WhenAll(notificationTasks); }
                catch (Exception) { /* Background notifications may fail silently */ }
            }
        }

        return new RegistrationResponseDto(id, true, decisionsResponse?.Errors);
    }

    public async Task<LobbyistResponseDto?> GetRegistrationById(long id)
    {
        // Get Discriminator of registration
        var type = await dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(id);
        switch (type)
        {
            // Is Lobbyist
            case nameof(Lobbyist):
                var lobbyist = await dependencies.RegistrationRepository.FindLobbyistById(id)
                                      ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {id} not found.");
                var lobbyistLinkEntityId = lobbyist.Filer?.FilerLinks?
                                           .FirstOrDefault(x => x.FilerLinkTypeId == FilerLinkType.LobbyingFirm.Id || x.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id)?
                                           .LinkedEntityId;
                var lobbyistLinkItem = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(lobbyistLinkEntityId.GetValueOrDefault());
                var lobbyistResponse = new LobbyistResponseDto(lobbyist);
                lobbyistResponse!.LobbyistEmployerOrLobbyingFirmName = lobbyistLinkItem?.Name;
                lobbyistResponse.Type = type;
                return lobbyistResponse;
            // Is LobbyistWithdrawal
            case nameof(LobbyistWithdrawal):
                var lobbyistWithdrawal = await dependencies.RegistrationRepository.FindLobbyistWithdrawalById(id)
                                      ?? throw new KeyNotFoundException($"Lobbyist withdrawal with ID {id} not found.");

                var withdrawalLinkEntityId = lobbyistWithdrawal.Filer?.FilerLinks?
                                   .FirstOrDefault(x => x.FilerLinkTypeId == FilerLinkType.LobbyingFirm.Id || x.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id)?
                                   .LinkedEntityId;

                var withdrawalLinkItem = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(withdrawalLinkEntityId.GetValueOrDefault());

                return new LobbyistResponseDto()
                {
                    Id = lobbyistWithdrawal.Id,
                    Name = lobbyistWithdrawal.Name,
                    Addresses = lobbyistWithdrawal.AddressList == null ? [] : [.. lobbyistWithdrawal.AddressList.Addresses.Select(a => new AddressDtoModel(a))],
                    LegislativeSessionId = lobbyistWithdrawal.LegislativeSessionId,
                    WithdrawnAt = lobbyistWithdrawal.WithdrawnAt,
                    LobbyistEmployerOrLobbyingFirmName = withdrawalLinkItem?.Name,
                    Type = type,
                };
            // Is LobbyistTermination
            case nameof(LobbyistTermination):
                var lobbyistTermination = await dependencies.RegistrationRepository.FindLobbyistTerminationById(id)
                                                    ?? throw new KeyNotFoundException($"Lobbyist termination with ID {id} not found.");
                var terminationlinkEntityId = lobbyistTermination.Filer?.FilerLinks?
                                               .FirstOrDefault(x => x.FilerLinkTypeId == FilerLinkType.LobbyingFirm.Id || x.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id)?
                                               .LinkedEntityId;
                var terminationlinkItem = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(terminationlinkEntityId.GetValueOrDefault());
                return new LobbyistResponseDto()
                {
                    Id = lobbyistTermination.Id,
                    Name = lobbyistTermination.Name,
                    Addresses = [.. lobbyistTermination!.AddressList!.Addresses.Select(a => new AddressDtoModel(a))],
                    PhoneNumbers = [.. lobbyistTermination!.PhoneNumberList!.PhoneNumbers.Select(p => new PhoneNumberDto(p))],
                    TerminatedAt = lobbyistTermination!.TerminatedAt,
                    LobbyistEmployerOrLobbyingFirmName = terminationlinkItem?.Name,
                    FilerId = lobbyistTermination.FilerId,
                    LegislativeSessionId = lobbyistTermination!.LegislativeSessionId,
                    Email = lobbyistTermination!.Email,
                    FirstName = lobbyistTermination!.FirstName,
                    LastName = lobbyistTermination!.LastName,
                    MiddleName = lobbyistTermination!.MiddleName,
                    Type = type
                };
            default:
                break;
        }

        return null;
    }

    public async Task<RegistrationResponseDto> SendLobbyistRegistrationTermination(long id, LobbyistRegistrationTerminationRequestDto request)
    {
        // Decisions flow
        var decisionFlow = request.StatusId == RegistrationStatus.Accepted.Id
                            ? DecisionsWorkflow.FRLOBFilingSubmitLobbyistRegistrationTermination : DecisionsWorkflow.FRLOBFilingTerminalRegistrationSendForAttestation;
        var decisionsResponse = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistRegistrationTermination, StandardDecisionsSubmissionResponse>(
                         decisionFlow,
                          new DecisionsLobbyistRegistrationTermination()
                          {
                              EffectiveDateOfTermination = request.TerminatedAt,
                              LegislativeSessionId = request.LegislativeSessionId
                          },
                          true);

        if (decisionsResponse?.Errors.Count > 0)
        {
            return new RegistrationResponseDto(id, false, decisionsResponse.Errors, request.StatusId);
        }

        // Update registration
        var lobbyist = await dependencies.RegistrationRepository.FindLobbyistTerminationById(id)
            ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {id} not found.");

        lobbyist.StatusId = request.StatusId.GetValueOrDefault();
        lobbyist.TerminatedAt = request.TerminatedAt;
        lobbyist.LegislativeSessionId = request.LegislativeSessionId;
        lobbyist.SubmittedAt = request.ExcuteOn;

        await dependencies.RegistrationRepository.Update(lobbyist);

        // Send notification
        if (decisionsResponse?.Notifications != null &&
            decisionsResponse.Notifications.Any(n => n.SendNotification) &&
            lobbyist.FilerId.HasValue)
        {
            // Filter notification
            var validNotifications = decisionsResponse.Notifications
                .Where(n => n.SendNotification && n.NotificationTemplateId != null)
                .ToList();

            if (validNotifications.Count != 0)
            {
                var notificationTasks = validNotifications
                    .Select(n => dependencies.NotificationSvc.SendFilerNotification(
                        new SendFilerNotificationRequest(
                            NotificationTemplateId: n.NotificationTemplateId!.Value,
                            FilerId: lobbyist.FilerId.Value,
                            DueDate: DateTime.UtcNow,
                            NotificationData: null)));

                try { await Task.WhenAll(notificationTasks); }
                catch (Exception) { /* Background notifications may fail silently */ }
            }
        }

        return new RegistrationResponseDto(id, true, decisionsResponse?.Errors);
    }

    public async Task<RegistrationResponseDto> SaveLobbyistRegistrationTermination(long id, [Body] LobbyistRegistrationTerminationRequestDto request)
    {
        // Get Discriminator of registration
        var type = await dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(id);

        if (type == nameof(Lobbyist))
        {
            // The first time save item -> create new item with LobbyistTermination type
            var registration = await dependencies.RegistrationRepository.FindLobbyistById(id)
                                        ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {id} not found.");

            // Clone registration
            var terminateRegistration = CloneLobbyistRegistration<LobbyistTermination>(registration);

            // Get UserID
            var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();

            ResetRegistration(terminateRegistration, registration, userId.GetValueOrDefault());

            // Clone Address
            CloneRegistrationAddressList(terminateRegistration.AddressList);

            // Clone Phone Number
            CloneRegistrationPhoneNumberList(terminateRegistration.PhoneNumberList);

            if (request.StatusId != null)
            {
                terminateRegistration.StatusId = request.StatusId.Value;
            }

            terminateRegistration.Version = null;
            terminateRegistration.TerminatedAt = request.TerminatedAt;
            var newWithdrawal = await dependencies.RegistrationRepository.Create(terminateRegistration);
            return new RegistrationResponseDto()
            {
                Id = newWithdrawal.Id,
            };
        }
        else
        {
            // Update information
            var terminateRegistration = await dependencies.RegistrationRepository.FindLobbyistTerminationById(id)
                                            ?? throw new KeyNotFoundException($"Lobbyist termination with ID {id} not found.");
            terminateRegistration.TerminatedAt = request?.TerminatedAt;
            if (request?.StatusId != null)
            {
                terminateRegistration.StatusId = request.StatusId.Value;
            }
            await dependencies.RegistrationRepository.Update(terminateRegistration);
            return new RegistrationResponseDto()
            {
                Id = terminateRegistration.Id,
            };
        }
    }

    private static DecisionsWorkflow GetDecisionWorkflowLobbyistRegistration(bool isSelfRegister, bool isAmend, bool isRenewal)
    {
        if (isSelfRegister)
        {
            if (isRenewal)
            {
                return DecisionsWorkflow.FRLOBFilingLobbyistRenewalFinalSubmission;
            }

            if (isAmend)
            {
                return DecisionsWorkflow.FRLOBFilingAmendLobbyistFinalSubmission;
            }

            return DecisionsWorkflow.FRLOBFilingLobbyistFinalSubmission;
        }
        else
        {
            if (isRenewal)
            {
                return DecisionsWorkflow.FRLOBFilingLobbyistRenewalSendAttestation;
            }

            if (isAmend)
            {
                return DecisionsWorkflow.FRLOBFilingAmendLobbyistSendAttestation;
            }

            return DecisionsWorkflow.FRLOBFilingLobbyistSendAttestation;
        }
    }
}
