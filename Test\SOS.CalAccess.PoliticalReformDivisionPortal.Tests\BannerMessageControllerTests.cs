using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.PoliticalReformDivisionPortal.Controllers;
using SOS.CalAccess.PoliticalReformDivisionPortal.Models;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Technical.SystemAdministration;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.PoliticalReformDivisionPortal.Tests;

[TestFixture]
public class BannerMessageControllerTests
{
    private IBannerMessageMaintenanceSvc _bannerSvc;
    private IReferenceDataSvc _referenceDataSvc;
    private BannerMessageController _controller;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _bannerSvc = Substitute.For<IBannerMessageMaintenanceSvc>();
        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _controller = new BannerMessageController(_bannerSvc, _referenceDataSvc);
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    [Test]
    public async Task IndexReturnsViewWithSmallDataGridModel()
    {
        // Arrange
        var bannerList = new List<BannerMessagesGridRow>
            {
                new ( 1,"Test","Committee", _dateNow,_dateNow.AddDays(1))
            };
        _ = _bannerSvc.ListBannerMessages().Returns(bannerList);

        // Act
        var result = await _controller.Index();

        // Assert
        Assert.That(result, Is.TypeOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.That(viewResult?.Model, Is.Not.Null);
        Assert.That(viewResult?.Model, Is.AssignableTo<SmallDataGridModel>());

        var model = viewResult?.Model as SmallDataGridModel;
        Assert.Multiple(() =>
        {
            Assert.That(model?.DataSource, Is.EqualTo(bannerList));
            Assert.That(model?.GridId, Is.EqualTo("BannerMessagesGrid"));
        });
    }

    [Test]
    public async Task CreateReturnsViewWithFilteredFilerTypes()
    {
        var filerTypes = new List<FilerType>
            {
                new() { Id = 1, Name = "Candidate" },
                new() { Id = 2, Name = "All Filers" },
                new() { Id = 3, Name = "Committee" }
            };
        _ = _referenceDataSvc.GetAllFilerTypes().Returns(filerTypes);

        var result = await _controller.Create();

        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Edit"));
        var model = viewResult.Model as BannerViewModel;
        Assert.That(model, Is.Not.Null);
    }

    [Test]
    public async Task EditGetReturnsViewWithBannerViewModel()
    {
        long id = 1;
        var filerTypes = new List<FilerType>
            {
                new() { Id = 1, Name = "Candidate" },
                new() { Id = 2, Name = "Committee" }
            };
        _ = _referenceDataSvc.GetAllFilerTypes().Returns(filerTypes);

        var dto = new BannerMessageDto(
            1, DateTime.Today, DateTime.Today, DateTime.Today.AddDays(1),
            1, (int)DurationType.Before, "Msg", new List<long> { 1 });
        _ = _bannerSvc.ViewBannerMessage(id).Returns(dto);

        var result = await _controller.Edit(id);

        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        var model = viewResult.Model as BannerViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model.Banner.Message, Is.EqualTo(dto.Message));
    }

    [Test]
    public async Task EditPostInvalidModelStateReturnsSameView()
    {
        var model = new BannerViewModel(new List<FilerType>());
        _controller.ModelState.AddModelError("key", "error");

        var result = await _controller.Edit(model);

        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task EditPostValidModelStateRedirectsToIndex()
    {
        var model = new BannerViewModel(new List<FilerType>())
        {
            Banner = new BannerMessage
            {
                Id = 1,
                EventDate = DateTime.Today,
                BeginDate = DateTime.Today,
                EndDate = DateTime.Today.AddDays(1),
                DurationInDays = 1,
                DurationType = DurationType.After,
                Message = "Test",
                FilerTypes = new List<BannerMessageFilerType>
                    {
                        new() { FilerTypeId = 1 }
                    }
            }
        };

        var result = await _controller.Edit(model);

        await _bannerSvc.Received(1).UpdateBannerMessage(Arg.Any<BannerMessageDto>());

        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect.ActionName, Is.EqualTo(nameof(_controller.Index)));
    }

    [Test]
    public async Task AddModelStateInvalidReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");
        var model = new BannerViewModel(new List<FilerType>());

        // Act
        var result = await _controller.Add(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("Add"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task DeleteWhenModelStateIsInvalidReturnsBadRequest()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "Invalid");

        // Act
        var result = await _controller.Delete(42L);

        // Assert
        Assert.That(result, Is.TypeOf<BadRequestResult>());
        await _bannerSvc.DidNotReceive().RemoveBannerMessage(Arg.Any<long>());
    }

    [Test]
    public async Task DeleteWhenModelStateIsValidCallsServiceAndReturnsOk()
    {
        // Arrange
        long bannerId = 123L;

        // Act
        var result = await _controller.Delete(bannerId);

        // Assert
        Assert.That(result, Is.TypeOf<OkResult>());
        await _bannerSvc.Received(1).RemoveBannerMessage(bannerId);
    }

}
