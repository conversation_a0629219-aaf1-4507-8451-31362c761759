@model SOS.CalAccess.FilerPortal.Models.Disclosure.DisclosureSummaryViewModel
@using Microsoft.AspNetCore.Html
@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.Models.FilerDisclosure.Filings
@inject IHtmlLocalizer<SharedResources> SharedLocalizer

@{
    var controller = ViewContext.RouteData.Values["controller"]?.ToString();
    var amendmentExplanationEntry = Model.TransactionEntries.FirstOrDefault(e => e.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id);
    string statusColor = HtmlHelpers.GetFilingSummaryStatusColorByStatusName(amendmentExplanationEntry?.Status);
}

<h3 class="fw-bold">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.AmendmentExplanation"]</h3>
<ul class="list-group-flush w-100">
    <li class="list-group-item">
        <div class="d-flex flex-row justify-content-between py-3">
            <div class="d-flex flex-row align-items-start mb-3">
                <span style="display: inline-block; width: 4px; height: 65px; border-radius: 25px; background-color: @statusColor;"></span>
                <div class="d-flex flex-column align-items-start mb-3 ms-3">
                    @if (!string.IsNullOrEmpty(amendmentExplanationEntry?.Status))
                    {
                        <p class="mb-0">
                            <span style="color: @statusColor">@amendmentExplanationEntry.Status</span>
                        </p>
                    }
                    <p class="fw-bold mb-0">@SharedLocalizer["FilerPortal.Disclosure.AmendmentExplanation.Title"]</p>
                </div>
            </div>
            <div class="d-flex flex-column">
                <div class="d-flex flex-column align-items-end">
                    @Html.LinkButton(
                        localizer: SharedLocalizer,
                        textKey: HtmlHelpers.GetFilingSummaryButtonTextByStatusName(amendmentExplanationEntry?.Status, SharedLocalizer),
                        controller: controller,
                        action: "Index",
                        routeValues: new
                        {
                            viewName = "AmendmentExplanation",
                            filerId = Model.FilerId,
                            filingId = Model.Id,
                            reportType = ViewBag.ReportType,
                        },
                        cssClass: "btn btn-outline-primary btn-sm ms-auto"
                    )
                </div>
                <div class="d-flex">
                    @if (amendmentExplanationEntry?.Status == FilingSummaryStatus.NotStarted.Name)
                    {
                        @Html.LinkButton(
                            localizer: SharedLocalizer,
                            textKey: "FilerPortal.Disclosure.Dashboard.DontHaveAnything",
                            controller: controller,
                            action: "HandleUpdateStatusFilingSummary",
                            routeValues: new
                            {
                                filerId = Model.FilerId,
                                filingId = Model.Id,
                                reportType = ViewBag.ReportType,
                                     filingSummaryId = amendmentExplanationEntry.Id,
                                filingSummaryStatusName = FilingSummaryStatus.NothingToReport.Name
                            },
                            cssClass: "link-primary"
                        )
                    }
                </div>
            </div>
        </div>
    </li>
</ul>
