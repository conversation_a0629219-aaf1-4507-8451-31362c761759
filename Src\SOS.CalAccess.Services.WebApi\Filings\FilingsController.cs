// <copyright file="FilingsController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.WebApi.Authorization;
using SOS.CalAccess.Services.WebApi.Authorization.ThirdParty.Decisions;
using SOS.CalAccess.Services.WebApi.Shared;

namespace SOS.CalAccess.Services.WebApi.Filings;

/// <summary>
/// API controller responsible for routing requests related to filings.
/// </summary>
[Route("api")]
[ApiController]
[ApiConventionType(typeof(DefaultApiConventions))]
public sealed class FilingsController(IAuthorizationService authorization, IDateTimeSvc dateTimeSvc)
    : AuthorizationAwareControllerBase(authorization)
{
    private const string Unexpected = "Unexpected command result";

    /// <summary>
    /// Creates a new filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to create the filing for.</param>
    /// <param name="request">The request object containing the details of the filing to create.</param>
    /// <param name="handler">The command handler to execute the creation of the filing.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    /// <exception cref="InvalidOperationException">When an unexpected command result is returned.</exception>
    [HttpPost("Filers/{id:long}/[controller]", Name = nameof(CreateFiling))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<ActionResult<FilingItemResponse>> CreateFiling(
        [FromRoute] long id,
        [FromBody] UpsertFilingRequest request,
        [FromServices] ICreateFiling handler,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.ToFiler(id),
            RulesAllowAction.Create<Filing>(new { FilerId = id })]);

        var result = await handler.Execute(request.IntoCreateCommand(id, dateTimeSvc), cancellationToken);

        return result switch
        {
            Success<Filing> s => CreatedAtAction(
                nameof(GetFiling),
                new { id = s.Value.Id },
                new FilingItemResponse(s.Value)),
            Failure<Filing>.NotFound n => NotFound(n),
            Failure<Filing>.InvalidState i => BadRequest(i),
            _ => throw new InvalidOperationException(Unexpected),
        };
    }

    /// <summary>
    /// Retrieves a single filing by id.
    /// </summary>
    /// <param name="id">The id of the filing to retrieve.</param>
    /// <param name="query">The query handler to execute the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/{id:long}", Name = nameof(GetFiling))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<FilingItemResponse>> GetFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var result = await filingSvc.GetFilingById(id);

        return result is not null ? new FilingItemResponse(result) : NotFound();
    }

    /// <summary>
    /// Retrieves a single Session by id.
    /// </summary>
    /// <param name="id">The id of the filing to retrieve.</param>
    /// <param name="query">The query handler to execute the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/LegislativeSessions", Name = nameof(GetLegistlativeSessions))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<LegislativeSessionResponseList>> GetLegistlativeSessions(
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        var sessions = await filingSvc.GetAllLegislativeSessions();

        if (sessions == null || !sessions.Any())
        {
            return NotFound();
        }

        var sessionList = sessions.Select(session =>
        {
            return new LegislativeSessionResponse
            {
                Id = session.Id,
                Name = session.Name,
                StartDate = session.StartDate,
                EndDate = session.EndDate
            };
        }).ToList();

        var response = new LegislativeSessionResponseList(sessionList);
        return Ok(response);
    }



    /// <summary>
    /// Retrieves all filings for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to retrieve filings for.</param>
    /// <param name="query">The query handler to execute the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    /// <exception cref="InvalidOperationException">When an unexpected command result is returned.</exception>
    [HttpGet("Filers/{id:long}/[controller]", Name = nameof(GetFilings))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<IReadOnlyList<FilingItemResponse>>> GetFilings(
        [FromRoute] long id,
        [FromServices] IGetAllFilings query,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([HasAccess.ToFiler(id)]);

        var result = await query.Execute(new IGetAllFilings.ByFiler(id), cancellationToken);

        if (result.Unwrap(out var filings, out var failure))
        {
            var n = failure as Failure<IReadOnlyList<Filing>>.NotFound
                    ?? throw new InvalidOperationException(Unexpected);

            return NotFound(n);
        }

        return Ok(filings.Select(x => new FilingItemResponse(x)).ToList());
    }

    /// <summary>
    /// Updates an existing filing.
    /// </summary>
    /// <param name="id">The id of the filing to update.</param>
    /// <param name="request">The request object containing the details of the filing to update.</param>
    /// <param name="handler">The command handler to execute the update of the filing.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    /// <exception cref="InvalidOperationException">When an unexpected command result is returned.</exception>
    [HttpPut("[controller]/{id:long}", Name = nameof(UpdateFiling))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Edit))]
    public async Task<ActionResult> UpdateFiling(
        [FromRoute] long id,
        [FromBody] UpsertFilingRequest request,
        [FromServices] IUpdateFiling handler,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.To<Filing>(id),
            RulesAllowAction.Update<Filing>(new { FilerId = id })]);

        var result = await handler.Execute(request.IntoUpdateCommand(id), cancellationToken);

        return result switch
        {
            Success<Filing> => NoContent(),
            Failure<Filing>.NotFound n => NotFound(n),
            Failure<Filing>.InvalidState i => BadRequest(i),
            _ => throw new InvalidOperationException(Unexpected),
        };
    }

    [HttpPatch("[controller]/{id:long}/UpdateCustomPeriod", Name = nameof(UpdateFilingCustomPeriod))]
    [AllowAnonymous]
    public async Task<ActionResult<FilingResponseDto>> UpdateFilingCustomPeriod(
        [FromRoute] long id,
        [FromBody] UpdateFilingCustomPeriodRequest request,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await filingSvc.UpdateFilingCustomPeriod(id, request);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            // Return 400 BadRequest with the error message from FilingSvc
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Submits a filing based on its id.
    /// </summary>
    /// <param name="id">The id of the filing to submit.</param>
    /// <param name="submit">Command handler.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("[controller]/{id:long}", Name = nameof(SubmitFiling))]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FilingItemResponse))]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<FilingItemResponse>> SubmitFiling(
        [FromRoute] long id,
        [FromServices] ISubmitFiling submit,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.To<Filing>(id),
            RulesAllowAction.Update<Filing>(new { Id = id, DesiredStatus = FilingStatus.Submitted })]);

        return await submit.Execute(id, cancellationToken) switch
        {
            Success<Filing> s => Ok(new FilingItemResponse(s.Value)),
            INotFound n => NotFound(n),
            IFailure f => BadRequest(f),
            _ => throw new InvalidOperationException(Unexpected),
        };
    }

    /// <summary>
    /// Triggers an amendment to an approved filing based on its id.
    /// </summary>
    /// <param name="id">The id of the filing to submit.</param>
    /// <param name="submit">Command handler.</param>
    /// <param name="audit">High level business logic audit facilities.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [HttpPost("[controller]/{id:long}/amend", Name = nameof(AmendFiling))]
    public async Task<ActionResult<FilingItemResponse>> AmendFiling(
        [FromRoute] long id,
        [FromServices] IAmendFiling submit,
        [FromServices] IAuditService audit,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.To<Filing>(id),
            RulesAllowAction.Create<Filing>(new { ParentId = id })]);

        await audit.LogAction(BusinessAction.ChangeStatus<Filing>(id.ToString(), dateTimeSvc, "Amendment"), cancellationToken);

        return await submit.Execute(id, cancellationToken) switch
        {
            Success<Filing> s => Ok(new FilingItemResponse(s.Value)),
            INotFound n => NotFound(n),
            IFailure f => BadRequest(f),
            _ => throw new InvalidOperationException(Unexpected),
        };
    }

    /// <summary>
    /// Executes an amendment to an approved filing based on its id.
    /// </summary>
    /// <param name="id">The id of the filing to amend.</param>
    /// <param name="filingSvc">The filing service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("[controller]/{id:long}/amendment", Name = nameof(CreateFilingAmendment))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<FilingResponseDto>> CreateFilingAmendment(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.CreateFilingAmendmentAsync(id);
    }

    /// <summary>
    /// Creates a draft lobbyist employer report filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="model">The model containing the details for the lobbyist employer report.</param>
    /// <param name="filingSvc">The filing service to handle the creation.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{id:long}/[controller]/LobbyistEmployerReport/New")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<FilingItemResponse>> CreateLobbyistEmployerReportFiling(
        [FromRoute] long id,
        [FromBody] CreateLobbyistEmployerReportDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var filing = await filingSvc.CreateLobbyistEmployerReport(model.FilerId, model.FilingPeriodId);
        return filing is not null ? new FilingItemResponse(filing) : NotFound();
    }

    /// <summary>
    /// Retrieves all filing periods.
    /// </summary>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="IEnumerable{FilingPeriod}"/> with all filing periods.</returns>
    [HttpGet("FilingPeriods", Name = nameof(GetAllFilingPeriods))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<IEnumerable<FilingPeriod>> GetAllFilingPeriods(
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default
        )
    {
        return await filingSvc.GetAllFilingPeriods();
    }

    /// <summary>
    /// Retrieves all filing periods for a filer.
    /// </summary>
    /// <param name="filerId">Filer Id</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="IEnumerable{FilingPeriodDto}"/> with all filing periods.</returns>
    [HttpGet("Filers/{filerId:long}/FilingPeriods", Name = nameof(GetAllFilingPeriodsForFiler))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<IEnumerable<FilingPeriodDto>> GetAllFilingPeriodsForFiler(
        [FromRoute] long filerId,
        [FromQuery] long? filingTypeId,
        [FromQuery] long? filingId,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default
        )
    {
        return await filingSvc.GetAllFilingPeriodsForFiler(filerId, filingTypeId, filingId);
    }

    [HttpGet("Filing/Filer/{filerId:long}/LobbyingFilingPeriods", Name = nameof(GetAllFilingPeriodsForLobbying))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<IEnumerable<FilingPeriodDto>> GetAllFilingPeriodsForLobbying(
        [FromRoute] long filerId,
        [FromQuery] long? filingTypeId,
        [FromQuery] long? filingId,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default
    )
    {
        return await filingSvc.GetAllFilingPeriodsForLobbying(filerId, filingTypeId, filingId);
    }

    /// <summary>
    /// Retrieves a lobbyist employer report filing by id.
    /// </summary>
    /// <param name="id">The id of the lobbyist employer report to retrieve.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/LobbyistEmployerReport/{id:long}")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<LobbyistEmployerReportResponse>> GetLobbyistEmployerReportFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.GetLobbyistEmployerReport(id);
        return report is not null ? new LobbyistEmployerReportResponse(report) : NotFound();
    }

    /// <summary>
    /// Retrieves the actions lobbied in a lobbyist employer report by its ID.
    /// </summary>
    /// <param name="id">The ID of the lobbyist employer report.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> containing the actions lobbied in the report.</returns>
    [HttpGet("[controller]/{id:long}/ActionsLobbied")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<ActionsLobbiedSummaryResponse>> GetActionsLobbiedSummaryForFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var result = await filingSvc.GetActionsLobbiedSummaryForFiling(id);
        return Ok(result);
    }

    /// <summary>
    /// Updates the actions lobbied in a lobbyist employer report by its ID.
    /// </summary>
    /// <param name="id">The ID of the lobbyist employer report.</param>
    /// <param name="model">The request model containing the updated actions lobbied data.</param>
    /// <param name="filingSvc">The filing service to handle the update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> containing the updated actions lobbied response.</returns>
    [HttpPut("[controller]/LobbyistEmployerReport/{id:long}/ActionsLobbied")]
    [AllowAnonymous]
    public async Task<ActionResult<ActionsLobbiedSummaryResponse>> UpdateLobbyistEmployerReportActionsLobbied(
        [FromRoute] long id,
        [FromBody] LobbyistEmployerActionsLobbiedRequest model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var result = await filingSvc.UpdateLobbyistEmployerReportActionsLobbied(id, model);
        return Ok(result);
    }

    /// <summary>
    /// Validates a list of agencies for actions lobbied in a lobbyist employer report.
    /// </summary>
    /// <param name="request">The list of actions lobbied to validate.</param>
    /// <param name="filingSvc">The filing service to handle the validation.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> containing a list of workflow errors, if any.</returns>
    [HttpPost("[controller]/LobbyistEmployerReport/ValidateAgencies")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<ActionResult<List<WorkFlowError>>> ValidateLobbyistEmployerReportActionsLobbiedAgencies(
        [FromBody] List<ActionsLobbiedRequestDto> request,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.ValidateLobbyistEmployerActionsLobbiedAgencies(request);
    }

    /// <summary>
    /// Retrieves a lobbyist employer report filing by id.
    /// </summary>
    /// <param name="id">The id of the filing summary to retrieve.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/FilingSummary/Filing/{id:long}")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<IReadOnlyList<FilingSummary>>> GetFilingSummaries(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var filingSummaries = await filingSvc.GetAllFilingSummariesByFilingId(id);
        return filingSummaries is not null ? Ok(filingSummaries.ToList()) : NotFound();
    }

    /// <summary>
    /// Updates a lobbyist employer report by id.
    /// </summary>
    /// <param name="id">The id of the lobbyist employer report to update.</param>
    /// <param name="model">The model containing the updated details for the report.</param>
    /// <param name="filingSvc">The filing service to handle the update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("[controller]/LobbyistEmployerReport/{id:long}", Name = nameof(UpdateLobbyistEmployerReport))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<UpdateLobbyistEmployerReportResponseDto>> UpdateLobbyistEmployerReport(
    [FromRoute] long id,
    [FromBody] UpdateLobbyistEmployerReportDto model,
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.UpdateLobbyistEmployerReport(id, model.TotalPaymentsToInHouseLobbyists);
        return report is not null ? report : NotFound();
    }

    /// <summary>
    /// Updates lump sum values for a lobbyist employer report.
    /// </summary>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("[controller]/LobbyistEmployerReport/LumpSums/{id:long}", Name = nameof(UpdateLobbyistEmployerLumpSums))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<UpdateLobbyistEmployerLumpSumResponseDto>> UpdateLobbyistEmployerLumpSums(
        [FromRoute] long id,
        [FromBody] UpdateLobbyistEmployerReportDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.UpdateLobbyistEmployerLumpSums(id, model.TotalOverheadExpense, model.TotalUnderThresholdPayments);
        return report is not null ? Ok(report) : NotFound();
    }

    /// <summary>
    /// Updates a lobbyist employer report by id.
    /// </summary>
    /// <param name="id">The id of the lobbyist employer report to update.</param>
    /// <param name="model">The model containing the updated details for the report.</param>
    /// <param name="filingSvc">The filing service to handle the update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("[controller]/LobbyistEmployerPaymentsMadeToLobbyingCoalition/{id:long}", Name = nameof(UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<LobbyistEmployerReportResponse>> UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(
    [FromRoute] long id,
    [FromBody] UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto model,
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(id, model.IsMemberOfLobbyingCoalition);
        return report is not null ? new LobbyistEmployerReportResponse(report) : NotFound();
    }

    /// <summary>
    /// Creates a draft lobbyist employer report filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="model">The model containing the details for the lobbyist employer report.</param>
    /// <param name="filingSvc">The filing service to handle the creation.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{id:long}/[controller]/LobbyistReport/New")]
    [AllowAnonymous]
    public async Task<ActionResult<FilingItemResponse>> CreateLobbyistReportFiling(
        [FromRoute] long id,
        [FromBody] CreateLobbyistReportDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var filing = await filingSvc.CreateLobbyistReport(model.FilerId, model.FilingPeriodId);
        return filing is not null ? new FilingItemResponse(filing) : NotFound();
    }

    /// <summary>
    /// Retrieves a lobbyist report filing by id.
    /// </summary>
    /// <param name="id">The id of the lobbyist report to retrieve.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/LobbyistReport/{id:long}")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<LobbyistReportResponse>> GetLobbyistReportFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.GetLobbyistReport(id);
        return report is not null ? new LobbyistReportResponse(report) : NotFound();
    }

    /// <summary>
    /// Retrieves the cumulative period start date for a filing by its id.
    /// </summary>
    /// <param name="id">The id of the filing to retrieve the cumulative period start date for.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/{id:long}/CumulativePeriodStart")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<DateTime>> GetCumulativePeriodStartByFilingId(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.GetCumulativePeriodStartByFilingId(id);
    }

    /// <summary>
    /// Cancels a draft lobbyist employer report filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPut(IFilingSvc.CancelLobbyistEmployerReportPath)]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<LobbyistEmployerReportResponse>> CancelLobbyistEmployerReportFiling(
        [FromRoute] long filingId,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.CancelLobbyistEmployerReport(filingId);
        return report is not null ? new LobbyistEmployerReportResponse(report) : NotFound();
    }

    /// <summary>
    /// Cancels a draft lobbyist report filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPut("Filers/{id:long}/[controller]/LobbyistReport/Cancel")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<LobbyistReportResponse>> CancelLobbyistReportFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.CancelLobbyistReport(id);
        return report is not null ? new LobbyistReportResponse(report) : NotFound();
    }

    /// <summary>
    /// Cancels a draft filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>    
    [HttpPut("[controller]/{id:long}/Cancel", Name = nameof(CancelFiling))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<FilingItemResponse>> CancelFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var filing = await filingSvc.CancelFiling(id);
        if (filing is null)
        {
            return NotFound();
        }

        return Ok(new FilingItemResponse(filing));
    }


    /// <summary>
    /// Cancels a draft lobbyist employer report filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("Filers/{id:long}/[controller]/LobbyistEmployerReport/Submit")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<ValidatedLobbyistEmployerReport>> SubmitLobbyistEmployerReport(
        [FromRoute] long id,
        [FromBody] SubmitLobbyistEmployerReportDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.SubmitLobbyistEmployerReport(id, model.DiligenceStatementVerified);
        return report is not null ? report : NotFound();
    }

    /// <summary>
    /// Cancels a draft lobbyist report filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("Filers/{id:long}/[controller]/LobbyistReport/Submit")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<SubmitLobbyingReportResponse>> SubmitLobbyistReport(
        [FromRoute] long id,
        [FromBody] SubmitLobbyistReportDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.SubmitLobbyistReport(id, model.DiligenceStatementVerified);
        return report is not null ? report : NotFound();
    }

    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpGet("[controller]/LobbyistReport/{id:long}/SendForAttestation")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<SubmitLobbyingReportResponse>> SendForAttestationLobbyistReport(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.SendForAttestationLobbyistReport(id);
        return report is not null ? report : NotFound();
    }

    /// <summary>
    /// Updates a lobbyist employer report by id.
    /// </summary>
    /// <param name="id">The id of the lobbyist employer report to update.</param>
    /// <param name="model">The model containing the updated details for the report.</param>
    /// <param name="filingSvc">The filing service to handle the update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("[controller]/LobbyistEmployerCampaignContributions/{id:long}", Name = nameof(UpdateLobbyistEmployerCampaignContributions))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<LobbyistEmployerReportResponse>> UpdateLobbyistEmployerCampaignContributions(
    [FromRoute] long id,
    [FromBody] UpdateLobbyistEmployerCampaignContributionsDto model,
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.UpdateLobbyistEmployerCampaignContributions(id, model.ContributionsInExistingStatements, model.RelatedFilerId);
        return report is not null ? new LobbyistEmployerReportResponse(report) : NotFound();
    }

    /// <summary>
    /// Updates a lobbyist employer report with multiple related filers for contributions in existing statements.
    /// </summary>
    /// <param name="filingId">The id of the filing to update.</param>
    /// <param name="model">The model containing the updated details for the report.</param>
    /// <param name="filingSvc">The filing service to handle the update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("[controller]/ContributionsInExistingStatements/{filingId:long}", Name = nameof(UpdateContributionsInExistingStatements))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<UpdateContributionsInExistingStatementsResponseDto>> UpdateContributionsInExistingStatements(
        [FromRoute] long filingId,
        [FromBody] UpdateContributionsInExistingStatementsDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var response = await filingSvc.UpdateContributionsInExistingStatements(filingId, model.ContributionsInExistingStatements, model.RelatedFilerIds);
        return response is not null ? Ok(response) : NotFound();
    }

    /// <summary>
    /// Get Disclosure Filing Reports.
    /// </summary>
    /// <returns>Return set of disclosure reports.</returns>
    [HttpGet("[controller]/Disclosure/Reports", Name = nameof(GetDisclosureFilingReports))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<IEnumerable<FilingReportGridDto>> GetDisclosureFilingReports(
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        return await filingSvc.GetFilingReports();
    }

    /// <summary>
    /// Runs task to create a 72h report filing.
    /// </summary>
    /// <param name="id">The ID of the filer.</param>
    /// <returns>The filing with the specified Id.</returns>
    [HttpPost("Filers/{id:long}/Filings/Report72H", Name = nameof(CreateReport72H))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<FilingResponseDto> CreateReport72H(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.CreateReport72H(id);
    }

    /// <summary>
    /// Retrieves a 72h report filing by id.
    /// </summary>
    /// <param name="id">The id of the 72h report to retrieve.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/Report72H/{id:long}")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<Report72HResponseDto> GetReport72HFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.GetReport72H(id);
    }

    /// <summary>
    /// Updates the actions lobbied for a 72h report.
    /// </summary>
    /// <param name="id">The id of the 72h report to retrieve.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("[controller]/Report72H/{id:long}/ActionsLobbied")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Put))]
    [AllowAnonymous]
    public async Task<ActionsLobbiedSummaryResponse> UpdateReport72HActionsLobbied(
        [FromRoute] long id,
        [FromBody] Report72HActionsLobbiedRequestDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        WebApiHelper.NormalizeEmptyStringsToNull(model);
        return await filingSvc.UpdateReport72HActionsLobbied(id, model);
    }

    /// <summary>
    /// Validates a list of actions lobbied for bills.
    /// </summary>
    /// <param name="request">The list of actions lobbied to validate.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("[controller]/Report72H/ValidateBills")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<ValidateReport72HActionsLobbiedResponseDto> ValidateReport72HActionsLobbiedBills(
        [FromBody] ValidateReport72HActionsLobbiedRequestDto request,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        WebApiHelper.NormalizeEmptyStringsToNull(request);
        return await filingSvc.ValidateReport72HActionsLobbiedBills(request);
    }

    /// <summary>
    /// Validates a list of actions lobbied for agencies.
    /// </summary>
    /// <param name="request">The list of actions lobbied to validate.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("[controller]/Report72H/ValidateAgencies")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<ValidateReport72HActionsLobbiedResponseDto> ValidateReport72HActionsLobbiedAgencies(
        [FromBody] ValidateReport72HActionsLobbiedRequestDto request,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        WebApiHelper.NormalizeEmptyStringsToNull(request);
        return await filingSvc.ValidateReport72HActionsLobbiedAgencies(request);
    }

    /// <summary>
    /// Runs task to create a 48h report filing.
    /// </summary>
    /// <param name="id">The ID of the filer.</param>
    /// <returns>The filing with the specified Id.</returns>
    [HttpPost("Filers/{id:long}/Filings/Report48H", Name = nameof(CreateReport48H))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<FilingResponseDto> CreateReport48H(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.CreateReport48H(id);
    }

    /// <summary>
    /// Retrieves a 48h report filing by id.
    /// </summary>
    /// <param name="id">The id of the 48h report to retrieve.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/Report48H/{id:long}")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<Report48HResponseDto> GetReport48HFiling(
        [FromRoute] long id,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.GetReport48H(id);
    }

    /// <summary>
    /// Cancels a draft 48H report filing for a filer.
    /// </summary>
    /// <param name="id">The id of the filer to get the filing for.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A Task of <see cref="ActionResult"/> with the result of the request.</returns>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("Filers/{id:long}/[controller]/Report48H/Submit")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    [AllowAnonymous]
    public async Task<ActionResult<SubmitLobbyingReportResponse>> SubmitReport48H(
        [FromRoute] long id,
        [FromBody] SubmitLobbyistEmployerReportDto model,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var report = await filingSvc.SubmitReport48H(id, model.DiligenceStatementVerified);
        return report is not null ? report : NotFound();
    }

    /// <summary>
    /// Sends a 48-hour report filing for attestation.
    /// </summary>
    /// <param name="id">The unique identifier of the 48-hour report filing.</param>
    /// <param name="request">The request containing user IDs for attestation.</param>
    /// <param name="filingSvc">The filing service used to process the attestation.</param>
    /// <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> representing the asynchronous operation, with a <see cref="SubmitLobbyingReportResponse"/> result.
    /// </returns>
    [HttpPost("Filings/Report48H/{id:long}/SendForAttestation", Name = nameof(SendForAttestationReport48H))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<SubmitLobbyingReportResponse> SendForAttestationReport48H(
        [FromRoute] long id,
        [FromBody] Report48HSendForAttestationRequest request,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.SendForAttestationReport48H(id, request);
    }

    /// <summary>
    /// Adds an associated filer user to a filing.
    /// </summary>
    /// <param name="id">The unique identifier of the filing.</param>
    /// <param name="byFilingRequest">The byFilingRequest containing the filer role ID and user ID to associate.</param>
    /// <param name="filingSvc">The filing service used to add the associated filer user.</param>
    /// <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> representing the asynchronous operation, with a <see cref="FilerUserDto"/> result.
    /// </returns>
    [HttpPost("Filings/{id:long}/FilerUser")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<FilerUserDto> AddAssociatedFilerUserByFiling(
        [FromRoute] long id,
        [FromBody] AddAssociatedFilerUserByFilingRequest byFilingRequest,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.AddAssociatedFilerUserByFiling(id, byFilingRequest);
    }

    /// <summary>
    /// Update the total payments amount of PUC Activity.
    /// </summary>
    /// <param name="id">The Filing ID.</param>
    /// <param name="request">The total payment amount.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns></returns>
    [HttpPost("[controller]/{id:long}/UpdatePucActivityPayment", Name = nameof(UpdatePucActivityPayment))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<UpdatePucActivityPaymentResponseDto> UpdatePucActivityPayment(
        [FromRoute] long id,
        [FromBody] UpdatePucActivityPaymentRequestDto request,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        WebApiHelper.NormalizeEmptyStringsToNull(request);
        return await filingSvc.UpdatePucActivityPayment(id, request);
    }

    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPost(IFilingSvc.SendForAttestationReport72HPath)]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<ValidatedReport72H>> SendForAttestationReport72H(
    [FromRoute] long filingId,
    [FromBody] List<long> selectedResponsibleOfficerIds,
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        var result = await filingSvc.SendForAttestationReport72H(filingId, selectedResponsibleOfficerIds);
        return result is not null ? result : NotFound();
    }

    /// <summary>
    /// Get a collection of contact that is responsbile for Attestation
    /// </summary>
    /// <param name="filingId">The Filing ID.</param>
    /// <param name="filingSvc">The filing service to handle the retrieval.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A collection of contact</returns>
    [HttpGet(IFilingSvc.GetResponsibleOfficersPath, Name = nameof(GetResponsibleOfficers))]
    [AllowAnonymous]
    public async Task<IEnumerable<ReponsibleOfficerDto>> GetResponsibleOfficers([FromRoute] long filingId,
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        return await filingSvc.GetResponsibleOfficers(filingId);
    }

    [HttpGet(IFilingSvc.GetFilersAssociatedUserPath, Name = nameof(GetFilersAssociatedUser))]
    [AllowAnonymous]
    public async Task<IEnumerable<FilerUserDto>> GetFilersAssociatedUser([FromRoute] long userId,
        [FromRoute] long filingTypeId,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        return await filingSvc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId);
    }

    /// <summary>
    /// Runs task to attest a 72h report filing.
    /// </summary>
    /// <param name="id">The ID of the filer.</param>
    /// <returns>The filing with the specified Id.</returns>
    [HttpPost("Filings/Report72H/{id:long}/Attest", Name = nameof(AttestReport72H))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Post))]
    [AllowAnonymous]
    public async Task<ValidateReport72HSubmissionResponseDto> AttestReport72H(
        [FromRoute] long id,
        [FromBody] SubmitFilingReportDto model,
        [FromServices] IReport72HSvc report72HSvc,
        CancellationToken cancellationToken = default)
    {
        return await report72HSvc.AttestReport72H(id, model.DiligenceStatementVerified);
    }

    /// <summary>
    /// Searches for filers by name or ID, filtered by filer type, and returns data suitable for AutoComplete components.
    /// </summary>
    /// <param name="query">The search query (name or ID)</param>
    /// <param name="filerTypeId">The filer type ID to filter by</param>
    /// <param name="filingSvc">The filing service for data access</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A collection of FilerSearchDto objects for AutoComplete components</returns>
    [HttpGet("[controller]/SearchFilers", Name = nameof(SearchFilers))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<IEnumerable<FilerSearchDto>>> SearchFilers(
        [FromQuery] string query,
        [FromQuery] long filerTypeId,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var result = await filingSvc.SearchFilers(query, filerTypeId);
        return Ok(result);
    }

    /// <summary>
    /// Retrieves related filers associated with a specific filing for contributions in existing statements.
    /// </summary>
    /// <param name="filingId">The filing ID to retrieve related filers for</param>
    /// <param name="filingSvc">The filing service for data access</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A collection of FilerSearchDto objects representing related filers</returns>
    [HttpGet("[controller]/{filingId:long}/RelatedFilers", Name = nameof(GetRelatedFilersByFilingId))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<IEnumerable<FilerSearchDto>>> GetRelatedFilersByFilingId(
        [FromRoute] long filingId,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var relatedFilers = await filingSvc.GetRelatedFilersByFilingId(filingId);
        return Ok(relatedFilers);
    }

    [HttpPost(IFilingSvc.SendForAttestationReportLobbyistEmployerPath)]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<ValidatedLobbyistEmployerReport>> SendForAttestationReportLobbyistEmployer(
    [FromRoute] long filingId,
    [FromBody] List<long> selectedResponsibleOfficerIds,
    [FromServices] IFilingSvc filingSvc,
    CancellationToken cancellationToken = default)
    {
        var result = await filingSvc.SendForAttestationReportLobbyistEmployer(filingId, selectedResponsibleOfficerIds);
        return result is not null ? result : NotFound();
    }

    [HttpPatch(IFilingSvc.UpdateAmendmentExplanationPath, Name = nameof(UpdateAmendmentExplanation))]
    [AllowAnonymous]
    public async Task<ActionResult<UpdateAmendmentExplanationResponse>> UpdateAmendmentExplanation(
       [FromRoute] long id,
       [FromBody] UpdateAmendmentExplanationRequest request,
       [FromServices] IFilingSvc filingSvc,
       CancellationToken cancellationToken = default)
    {
        var result = await filingSvc.UpdateAmendmentExplanation(id, request);
        return result is not null ? Ok(result) : NotFound();
    }

}
