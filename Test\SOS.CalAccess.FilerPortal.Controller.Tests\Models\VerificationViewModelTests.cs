using SOS.CalAccess.FilerPortal.Alerts;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
[TestOf(nameof(VerificationViewModel))]
internal sealed class VerificationViewModelTests : IDisposable
{
    private VerificationViewModel _viewModel;

    [SetUp]
    public void SetUp()
    {
        _viewModel = new VerificationViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Continue,
            IsSubmission = true,
            IsAgreementAccepted = true,
            CandidateName = "John Doe",
            ExecutedOn = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            Messages = new PortalAlerts(),
            PhoneNumber = "1231234",
            FaxNumber = "1231234",
            MailingAddress = new AddressViewModel
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            },
            CandidateAddress = new AddressViewModel
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            },
            SelectedJurisdiction = "State",
            SelectedElectionYear = "2025",
            SelectedElection = "Presidential Election",
            SelectedOffice = "Governor",
            SelectedDistrict = "District 5",
            SelectedAgency = "Federal Election Commission",
            SelectedPartyAffiliation = "Independent"

        };

    }
    [TearDown]
    public void TearDown()
    {
        _viewModel = null;
    }

    [Test]
    public void VerificationViewModel_Should_SetAndGet_RegistrationId()
    {
        long expectedRegistrationId = 987;
        _viewModel.RegistrationId = expectedRegistrationId;
        Assert.That(_viewModel.RegistrationId, Is.EqualTo(expectedRegistrationId));
    }
    [Test]
    public void VerificationViewModel_Should_SetAndGet_Action()
    {
        FormAction expectedAction = FormAction.Continue;
        _viewModel.Action = expectedAction;
        Assert.That(_viewModel.Action, Is.EqualTo(expectedAction));
    }
    [Test]
    public void VerificationViewModel_Should_SetAndGet_IsSubmission()
    {
        bool expectedIsSubmission = true;
        _viewModel.IsSubmission = expectedIsSubmission;
        Assert.That(_viewModel.IsSubmission, Is.EqualTo(expectedIsSubmission));
    }
    [Test]
    public void VerificationViewModel_Should_SetAndGet_IsAgreementAccepted()
    {
        bool expectedAgreementAccepted = false;
        _viewModel.IsAgreementAccepted = expectedAgreementAccepted;
        Assert.That(_viewModel.IsAgreementAccepted, Is.EqualTo(expectedAgreementAccepted));
    }

    [Test]
    public void VerificationViewModel_Should_SetAndGet_CandidateName()
    {
        string expectedCandidateName = "Jane Doe";
        _viewModel.CandidateName = expectedCandidateName;
        Assert.That(_viewModel.CandidateName, Is.EqualTo(expectedCandidateName));
    }

    [Test]
    public void VerificationViewModel_Should_Handle_Null_CandidateName()
    {
        _viewModel.CandidateName = null;
        Assert.That(_viewModel.CandidateName, Is.Null);
    }

    [Test]
    public void VerificationViewModel_Should_SetAndGet_ExecutedOn()
    {
        var dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        DateTime expectedExecutedOn = dateNow.AddDays(-1);
        _viewModel.ExecutedOn = expectedExecutedOn;
        Assert.That(_viewModel.ExecutedOn, Is.EqualTo(expectedExecutedOn));
    }

    [Test]
    public void VerificationViewModel_Should_SetAndGet_Messages()
    {
        var expectedMessages = new PortalAlerts();
        var alert = new GlobalAlert { Message = "Test Alert" };
        expectedMessages.GlobalAlerts.Add(alert);
        _viewModel.Messages = expectedMessages;
        Assert.That(_viewModel.Messages, Is.EqualTo(expectedMessages));
        Assert.That(_viewModel.Messages.GlobalAlerts, Has.Count.EqualTo(1));
    }
    [Test]
    public void VerificationViewModel_Should_SetAndGet_PhoneNumber()
    {
        string expectedPhoneNumber = "1231234";
        _viewModel.CandidateName = expectedPhoneNumber;
        Assert.That(_viewModel.PhoneNumber, Is.EqualTo(expectedPhoneNumber));
    }
    [Test]
    public void VerificationViewModel_Should_SetAndGet_FaxNumber()
    {
        string expectedFaxNumber = "1231234";
        _viewModel.CandidateName = expectedFaxNumber;
        Assert.That(_viewModel.FaxNumber, Is.EqualTo(expectedFaxNumber));
    }
    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_CandidateAddress()
    {
        var expectedAddress = new AddressViewModel
        {
            Street = "123 Main St",
            City = "Test City",
            State = "CA"
        };

        _viewModel.CandidateAddress = expectedAddress;

        Assert.That(_viewModel.CandidateAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(_viewModel.CandidateAddress.Street, Is.EqualTo(expectedAddress.Street));
            Assert.That(_viewModel.CandidateAddress.City, Is.EqualTo(expectedAddress.City));
            Assert.That(_viewModel.CandidateAddress.State, Is.EqualTo(expectedAddress.State));
        });
    }

    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_MailingAddress()
    {
        var expectedAddress = new AddressViewModel
        {
            Street = "123 Main St",
            City = "Test City",
            State = "CA"
        };

        _viewModel.MailingAddress = expectedAddress;

        Assert.That(_viewModel.MailingAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(_viewModel.MailingAddress.Street, Is.EqualTo(expectedAddress.Street));
            Assert.That(_viewModel.MailingAddress.City, Is.EqualTo(expectedAddress.City));
            Assert.That(_viewModel.MailingAddress.State, Is.EqualTo(expectedAddress.State));
        });
    }

    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_SelectedJurisdiction()
    {
        string expectedValue = "State";
        _viewModel.SelectedJurisdiction = expectedValue;
        Assert.That(_viewModel.SelectedJurisdiction, Is.EqualTo(expectedValue));
    }

    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_SelectedElectionYear()
    {
        string expectedValue = "2025";
        _viewModel.SelectedElectionYear = expectedValue;
        Assert.That(_viewModel.SelectedElectionYear, Is.EqualTo(expectedValue));
    }

    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_SelectedElection()
    {
        string expectedValue = "Presidential Election";
        _viewModel.SelectedElection = expectedValue;
        Assert.That(_viewModel.SelectedElection, Is.EqualTo(expectedValue));
    }

    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_SelectedOffice()
    {
        string expectedValue = "Governor";
        _viewModel.SelectedOffice = expectedValue;
        Assert.That(_viewModel.SelectedOffice, Is.EqualTo(expectedValue));
    }
    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_SelectedDistrict()
    {
        string expectedValue = "District 5";
        _viewModel.SelectedDistrict = expectedValue;
        Assert.That(_viewModel.SelectedDistrict, Is.EqualTo(expectedValue));
    }
    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_SelectedAgency()
    {
        string expectedValue = "Federal Election Commission";
        _viewModel.SelectedAgency = expectedValue;
        Assert.That(_viewModel.SelectedAgency, Is.EqualTo(expectedValue));
    }
    [Test]
    public void CandidateIntentionStatementViewModel_Should_SetAndGet_SelectedPartyAffiliation()
    {
        string expectedValue = "Independent";
        _viewModel.SelectedPartyAffiliation = expectedValue;
        Assert.That(_viewModel.SelectedPartyAffiliation, Is.EqualTo(expectedValue));
    }
    [Test]
    public void VerificationViewModel_Should_Initialize_Messages_Default()
    {
        Assert.That(_viewModel.Messages, Is.Not.Null);
    }
    // Dispose method (if required)
    public void Dispose()
    {
        // Clean up any resources if necessary
    }

}
