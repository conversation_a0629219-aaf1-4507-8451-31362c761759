// <copyright file="TransactionLike.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>


using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Transactions;

/// <summary>
/// A default implementation for testing purposes of the <see cref="ITransactionLike"/> interface
/// that represents stock transaction data.
/// </summary>
/// <param name="ContactId">Target contact id.</param>
/// <param name="Amount">The amount for the transaction.</param>
/// <param name="TransactionDate">The paid or received date for the transaction.</param>
/// <param name="FilerId">The id of the filer to which the transaction belongs.</param>
/// <param name="Notes">Optional notes to attach to the transaction.</param>
internal sealed record TransactionLike(
    long? ContactId, Currency Amount, DateTime? TransactionDate, long? FilerId = default, string? Notes = default) : ITransactionLike
{
    /// <summary>
    /// Template data for testing when the contents of the transaction are not meaningful.
    /// </summary>
    public static readonly TransactionLike Dummy =
        new(default, (Currency)100.00m, new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local), Notes: "Testing");
}
