﻿CREATE TABLE [dbo].[FilerLinkHistory] (
    [Id]                   BIGINT         NOT NULL,
    [FilerId]              BIGINT         NOT NULL,
    [LinkedEntityId]       BIGINT         NOT NULL,
    [EffectiveDate]        DATETIME2 (7)  NOT NULL,
    [FilerLinkTypeId]      BIGINT         NOT NULL,
    [CreatedBy]            BIGINT         NOT NULL,
    [ModifiedBy]           BIGINT         NOT NULL,
    [AuditableResourceTag] NVARCHAR (450) NULL,
    [PeriodEnd]            DATETIME2 (7)  NOT NULL,
    [PeriodStart]          DATETIME2 (7)  NOT NULL,
    [TerminationDate]      DATETIME2 (7)  NULL,
    [Active]               BIT            NOT NULL
);






GO
CREATE CLUSTERED INDEX [ix_FilerLinkHistory]
    ON [dbo].[FilerLinkHistory]([PeriodEnd] ASC, [PeriodStart] ASC) WITH (DATA_COMPRESSION = PAGE);

