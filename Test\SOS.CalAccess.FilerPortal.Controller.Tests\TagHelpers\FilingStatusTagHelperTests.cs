using Microsoft.AspNetCore.Razor.TagHelpers;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.TagHelpers;
using FilingType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;

namespace SOS.CalAccess.FilerPortal.Tests.TagHelpers;

[TestFixture]
[Parallelizable(ParallelScope.All)]
public class FilingStatusTagHelperTests
{
    [TestCase(InteropConstants.FilingStatus.Draft, "Draft")]
    [TestCase(InteropConstants.FilingStatus.Submitted, "Submitted")]
    [TestCase(InteropConstants.FilingStatus.Approved, "Approved")]
    [TestCase(InteropConstants.FilingStatus.Cancelled, "Cancelled")]
    [TestCase(InteropConstants.FilingStatus.Incomplete, "Incomplete")]
    [TestCase(InteropConstants.FilingStatus.Pending, "Pending")]
    public void Process_ShouldOutputCorrectStatusName(long statusId, string expectedOutput)
    {
        var tagHelper = new FilingStatusTagHelper
        {
            Filing = new FilingItemResponse
            (
                id: 1,
                endDate: new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                filerName: "Test Filer",
                filingType: "Test Type",
                filingStatus: expectedOutput,
                status: statusId,
                parentId: null,
                startDate: new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                filerId: 1,
                submittedDate: null,
                version: null,
                amendmentExplanation: string.Empty,
                legislativeSessionId: 1,
                totalPaymentsPucActivity: 1,
                filingTypeId: FilingType.LobbyistEmployerReport.Id,
                filingPeriodId: 1
            )
        };

        var context = new TagHelperContext(
            new TagHelperAttributeList(),
            new Dictionary<object, object>(),
            Guid.NewGuid().ToString("N"));

        var output = new TagHelperOutput("filing-status",
            new TagHelperAttributeList(),
            (result, encoder) =>
            {
                var tagHelperContent = new DefaultTagHelperContent();
                return Task.FromResult<TagHelperContent>(tagHelperContent);
            });

        // Act
        tagHelper.Process(context, output);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(output.Content.GetContent(), Is.EqualTo(expectedOutput));
            Assert.That(output.IsContentModified, Is.True);
        });
    }

    [Test]
    public void Process_WithUnknownStatus_ShouldThrowException()
    {
        // Arrange
        var tagHelper = new FilingStatusTagHelper
        {
            Filing = new FilingItemResponse
            (
                id: 1,
                endDate: new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                filerName: "Test Filer",
                filingType: "Test Type",
                filingStatus: "Unknown",
                status: 999, // Unknown status
                parentId: null,
                startDate: new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                filerId: 1,
                submittedDate: null,
                version: null,
                amendmentExplanation: "AmendmentExplanation",
                legislativeSessionId: 1,
                totalPaymentsPucActivity: null,
                filingTypeId: FilingType.LobbyistEmployerReport.Id,
                filingPeriodId: 1
            )
        };

        var context = new TagHelperContext(
            new TagHelperAttributeList(),
            new Dictionary<object, object>(),
            Guid.NewGuid().ToString("N"));

        var output = new TagHelperOutput("filing-status",
            new TagHelperAttributeList(),
            (result, encoder) =>
            {
                var tagHelperContent = new DefaultTagHelperContent();
                return Task.FromResult<TagHelperContent>(tagHelperContent);
            });

        // Act & Assert
        _ = Assert.Throws<NotSupportedException>(() => tagHelper.Process(context, output));
    }
}
