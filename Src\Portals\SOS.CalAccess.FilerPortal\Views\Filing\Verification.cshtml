@using Microsoft.AspNetCore.Mvc.Localization
@using SOS.CalAccess.FilerPortal.Models.Filings
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.Foundation.Utils
@using SOS.CalAccess.Models.FilerDisclosure.Filings
@using SOS.CalAccess.UI.Common.Services;
@using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
@model LobbyingReportVerificationViewModel
@inject IHtmlLocalizer<SharedResources> Localizer
@inject IDateTimeSvc DateTimeSvc
@using Microsoft.AspNetCore.Html

@{
    ViewData[LayoutConstants.Title] = Localizer["FilerPortal.Disclosure.Index.Title"].Value;
    ViewData["Title"] = Localizer["FilerPortal.Transaction.NewActivityExpenseRP.Title"].Value;
    var currentDate = DateTimeSvc.GetCurrentDateTime().ToString("MM/dd/yyyy");
    var buttonBarModel = new ButtonBarModel
    {
       LeftButtons =
       [
            new ButtonConfig
            {
                Type = ButtonType.Link,
                CssClass = "btn btn-flat-primary btn-sm ms-auto",
                InnerTextKey = Localizer[ResourceConstants.Previous].Value,
                Url = Url.Action("Index", "Disclosure", new
                {
                    filerId = Model.FilerId,
                    filingId = Model.Id,
                    reportType = ViewBag.ReportType
                })
            },
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                CssClass = "btn btn-secondary btn-sm ms-2",
                HtmlContent = new HtmlString(
                    "<button type='submit' id='attestButton' class='btn btn-primary btn-sm ms-auto'>" +
                    Localizer["FilerPortal.Filing.Verification.Attest"].Value +
                    "</button>"
                )
            }
       ],
       RightButtons =
       [
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                CssClass = "btn btn-flat-primary btn-sm ms-auto",
                HtmlContent = new HtmlString(
                   "<a asp-controller='DisclosureTemporaryDashboard' asp-action='Index' asp-route-viewName='' class='btn btn-flat-primary btn-sm ms-auto' " +
                   "data-bs-toggle='modal' " +
                   "data-bs-target='#cancelConfirmModal'>" +
                   Localizer["Common.Cancel"].Value +
                   "</a>"
                )
            },
        ]
    };

    var formAction = ViewBag.ReportType switch
    {
        var r when r == FilingType.LobbyistReport.Name => "SubmitLobbyistReport",
        var r when r == FilingType.LobbyistEmployerReport.Name => "SubmitLobbyistEmployerReport",
        var r when r == FilingType.Report48h.Name => "SubmitReport48H",
        var r when r == FilingType.Report72h.Name => "Submit72hReport",
        _ => null
    };

    string reportTypeName = ViewBag.ReportType switch
    {
        var r when r == FilingTypeModel.LobbyistReport.Name => SharedLocalizer["FilerPortal.Filing.Lobbyist.Title"].Value,
        var r when r == FilingTypeModel.LobbyistEmployerReport.Name => SharedLocalizer["FilerPortal.Filing.LobbyistEmployer.Title"].Value,
        var r when r == FilingTypeModel.Report72h.Name => SharedLocalizer["FilerPortal.Filing.Report72H.Title"].Value,
        var r when r == FilingTypeModel.Report48h.Name => SharedLocalizer["FilerPortal.Filing.Report48H.Title"].Value,
        _ => "reportTypeName not set in view"
    };
}

<h1 class="mb-3">@Localizer["FilerPortal.Disclosure.Index.Title"]</h1>
<h2 class="mb-5">@reportTypeName</h2>
<div class="p-5 mt-4 d-flex align-items-left justify-content-center border border-gray d-flex flex-column ps-7 pe-7">
    <h2>@Localizer["FilerPortal.Filing.Verification.Title"]</h2>

    @* Verification and Submitted screens currently only exist for the Lobbyist report. *@
    @* Different POST actions will be implemented later for different report types. *@
    @using (Html.BeginForm(formAction, "Filing", FormMethod.Post))
    {
        @Html.HiddenFor(m => m.Id)
        @Html.HiddenFor(m => m.FilerId)
        <input type="hidden" name="reportType" value="@ViewBag.ReportType" />

        <div class="mb-3 form-check">
            @Html.CheckBox("DiligenceStatementVerification", Model.DiligenceStatementVerification, new { @class = "form-check-input" })
            <label class="form-check-label" for="DiligenceStatementVerification">
                @Localizer["FilerPortal.Filing.Verification.DiligenceStatement"]
            </label>
        </div>

        <table class="mb-3 table border-bottom border-top border-gray">
            <thead class="border-gray">
                <tr>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th scope="row">@Localizer["FilerPortal.Filing.Verification.Name"]</th>
                    <td class="text-end">@Model.FilerName</td>
                </tr>
                <tr>
                    <th scope="row">@Localizer["FilerPortal.Filing.Verification.ExecutedOn"]</th>
                    <td class="text-end">@currentDate</td>
                </tr>
            </tbody>
        </table>

        <a asp-action="UnderConstruction"
           asp-controller="Home"
           class="btn btn-flat-primary btn-sm">
            <i class="fas fa-file-pdf mr-2"></i> @Localizer["FilerPortal.Filing.Verification.PreviewPDF"]
        </a>

        @if (Model.Messages != null && (ViewBag.ReportType == FilingTypeModel.LobbyistReport.Name || ViewBag.ReportType == FilingTypeModel.LobbyistEmployerReport.Name))
        {
            <partial name="_LobbyistSubmitValidationMessages" />
        }

        @if (Model.Messages != null && ViewBag.ReportType == FilingTypeModel.Report48h.Name)
        {
            <partial name="_48HSubmitValidationMessages" />
        }
        @if (Model.Messages != null && ViewBag.ReportType == FilingTypeModel.Report72h.Name)
        {
            <partial name="_72HSubmitValidationMessages" />
        }
    <div class="my-4"></div>
    <partial name="_ButtonBar" model="buttonBarModel" />

    }
</div>

@{
    var cancelModal = new CancelConfirmModal(
        Title: "Confirm Cancellation",
        Body: "Are you sure you want to cancel? Any unsaved changes will be lost.",
        CloseButtonText: "No, Stay Here",
        SubmitButtonText: "Yes, Cancel",
        ActionUrl: Url.Action("Index", "DisclosureTemporaryDashboard")!
    );
}

<partial name="_CancelConfirmModal" model="cancelModal" />

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const attestButton = document.getElementById("attestButton");
        const diligenceStatement = document.getElementById("DiligenceStatementVerification");

        function toggleFields() {
            const isDisabled = !diligenceStatement.checked;
            attestButton.disabled = isDisabled;
            attestButton.setAttribute("aria-disabled", isDisabled);
        }

        toggleFields();
        diligenceStatement.addEventListener("change", toggleFields);
    });
</script>
