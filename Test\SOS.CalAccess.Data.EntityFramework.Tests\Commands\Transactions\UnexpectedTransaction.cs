// <copyright file="UnexpectedTransaction.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Transactions;

/// <summary>
/// Transaction subclass that doesn't belong in the application model.
/// It is meant as a testing tool, to verify that items that rely on transaction subclasses
/// fail when encountering a mismatched target.
/// </summary>
public sealed class UnexpectedTransaction : Transaction
{
    /// <summary>
    /// Initializes a new instance of the <see cref="UnexpectedTransaction"/> class.
    /// </summary>
    [SetsRequiredMembers]
    public UnexpectedTransaction()
        : base(TransactionType.Contribution)
    {
        TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        Amount = (Currency)20.00m;
    }
}
