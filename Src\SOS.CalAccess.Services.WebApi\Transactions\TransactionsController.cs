// <copyright file="TransactionsController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Globalization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.OtherInfluencePayments;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Authorization;
using SOS.CalAccess.Services.WebApi.Authorization.ThirdParty.Decisions;
using SOS.CalAccess.Services.WebApi.Exceptions;
using SOS.CalAccess.Services.WebApi.Shared;
using LobbyistCampaignContributionDs = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels.LobbyistCampaignContributionDs;
using LobbyistEmployerCoalitionCampaignContributionDs = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels.LobbyistEmployerCoalitionCampaignContributionDs;


namespace SOS.CalAccess.Services.WebApi.Transactions;

/// <summary>
/// API controller responsible for routing requests related to registrations.
/// </summary>
[Route("api")]
[ApiController]
[ApiConventionType(typeof(DefaultApiConventions))]
public sealed class TransactionsController(
    IAuthorizationService authorization, IAuditService auditService, ITransactionSvc transactionSvc, IDateTimeSvc dateTimeSvc) : AuthorizationAwareControllerBase(authorization)
{
    /// <summary>
    /// Creates a new transaction.
    /// </summary>
    /// <param name="filerId">The filer id to which the transaction will be assigned.</param>
    /// <param name="createTransaction">Command handler.</param>
    /// <param name="request">Request data from body.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{filerId:long}/[controller]", Name = nameof(CreateTransaction))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<ActionResult<TransactionItemResponse>> CreateTransaction(
        [FromRoute] long filerId,
        [FromServices] ICreateTransaction createTransaction,
        [FromBody] UpsertTransactionRequest request,
        CancellationToken cancellationToken = default)
    {
        request.FilerId = filerId;

        await ThrowIfRequirementNotMet([
            HasAccess.ToFiler(filerId),
            RulesAllowAction.Create<Transaction>()]);

        return await createTransaction.Execute(request.IntoCreateCommand(), cancellationToken) switch
        {
            Success<Transaction> s =>
                CreatedAtAction(nameof(GetTransaction), new { s.Value.Id }, new TransactionItemResponse(s.Value)),

            Failure<Transaction>.NotFound => NotFound(),
            IFailure f => BadRequest(f),

            _ => throw new InvalidResultException(),
        };
    }

    /// <summary>
    /// Updates an existing transaction.
    /// </summary>
    /// <param name="id">The target transaction id.</param>
    /// <param name="updateTransaction">Command handler.</param>
    /// <param name="request">Request data from body.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPut("[controller]/{id:long}", Name = nameof(UpdateTransaction))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Update))]
    public async Task<ActionResult<TransactionItemResponse>> UpdateTransaction(
        [FromRoute] long id,
        [FromServices] IUpdateTransaction updateTransaction,
        [FromBody] UpsertTransactionRequest request,
        CancellationToken cancellationToken = default)
    {
        request.Id = id;

        await ThrowIfRequirementNotMet([
            HasAccess.To<Transaction>(id),
            RulesAllowAction.Update<Transaction>(new { request.Id })]);

        return await updateTransaction.Execute(request.IntoUpdateCommand(), cancellationToken) switch
        {
            Success<Transaction> => NoContent(),

            Failure<Transaction>.NotFound => NotFound(),
            IFailure f => BadRequest(f),

            _ => throw new InvalidResultException(),
        };
    }

    /// <summary>
    /// Deletes an existing transaction.
    /// </summary>
    /// <param name="id">The target transaction id.</param>
    /// <param name="deleteTransaction">Command handler.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpDelete("[controller]/{id:long}", Name = nameof(DeleteTransaction))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Delete))]
    public async Task<ActionResult<TransactionItemResponse>> DeleteTransaction(
        [FromRoute] long id,
        [FromServices] IDeleteTransaction deleteTransaction,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.To<Transaction>(id),
            RulesAllowAction.Update<Transaction>(new { id })]);

        var result = await deleteTransaction.Execute(id, cancellationToken);
        return result switch
        {
            Success<Transaction> t => Ok(new TransactionItemResponse(t.Value)),
            Failure<Transaction>.NotFound => NotFound(),
            IFailure f => BadRequest(f),

            _ => throw new InvalidResultException(),
        };
    }

    /// <summary>
    /// Retrieves a single transaction by id.
    /// </summary>
    /// <param name="id">The id of the user to search for.</param>
    /// <param name="getTransaction">Query handler.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    /// <exception cref="InvalidOperationException">On invalid result state received.</exception>
    [HttpGet("[controller]/{id:long}", Name = nameof(GetTransaction))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<TransactionResponse>> GetTransaction(
        [FromRoute] long id,
        [FromServices] IGetTransaction getTransaction,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.To<Transaction>(id),
            RulesAllowAction.Read<Transaction>(new { Id = id })]);

        await auditService.LogAction(BusinessAction.Read<Transaction>(id.ToString(), dateTimeSvc), cancellationToken);

        return await getTransaction.Execute(new(id), cancellationToken) switch
        {
            Success<Transaction> s => s.Value.IntoResponse(),

            Failure<Transaction>.NotFound => NotFound(),
            IFailure f => BadRequest(f),

            _ => throw new InvalidResultException(),
        };
    }

    /// <summary>
    /// Retrieves a list of transactions, scoped by filer.
    /// </summary>
    /// <param name="filerId">The targeted filer id.</param>
    /// <param name="getAllTransactions">Query handler.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    /// <exception cref="InvalidOperationException">On invalid result state received.</exception>
    [HttpGet("Filers/{filerId:long}/[controller]", Name = nameof(GetAllTransactionsByFiler))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<IReadOnlyList<TransactionItemResponse>>> GetAllTransactionsByFiler(
        [FromRoute] long filerId,
        [FromServices] IGetAllTransactions getAllTransactions,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([
            HasAccess.ToFiler(filerId),
            RulesAllowAction.ReadAll<Transaction>(new { ScopedToFiler = filerId })]);

        return await getAllTransactions.Execute(new IGetAllTransactions.ByFiler(filerId), cancellationToken) switch
        {
            Success<IReadOnlyList<Transaction>> s =>
                Ok(s.Value.Select(t => new TransactionItemResponse(t)).ToList()),

            IFailure f => BadRequest(f),

            _ => throw new InvalidResultException(),
        };
    }

    /// <summary>
    /// Retrieves a list of reportable persons, scoped by filer.
    /// </summary>
    /// <param name="filerId">The targeted filer id.</param>
    /// <param name="transactionReportablePersonSvc">Query handler.</param>
    /// <param name="query">The query to search for.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("Filers/{filerId:long}/[controller]/ReportablePersons", Name = nameof(GetAllReportablePersonsByFiler))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<IReadOnlyList<TransactionReportablePersonDto>>> GetAllReportablePersonsByFiler(
        [FromRoute] long filerId,
        [FromServices] ITransactionReportablePersonSvc transactionReportablePersonSvc,
        [FromQuery] string query,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await ThrowIfRequirementNotMet([
                HasAccess.ToFiler(filerId),
            RulesAllowAction.ReadAll<Transaction>(new { ScopedToFiler = filerId })]);

            var potentialMatches = await transactionReportablePersonSvc.GetPotentialMatches(filerId, query);

            return Ok(potentialMatches);
        }
        catch (Exception ex)
        {
            return Problem(detail: ex.Message);
        }
    }

    /// <summary>
    /// Retrieves a page of transactions from those belonging to a given filing, referenced
    /// by its id.
    /// </summary>
    /// <param name="filingId">The id of the target filing.</param>
    /// <param name="getAllTransactions">Query handler.</param>
    /// <param name="filter">Pagination options.</param>
    /// <param name="type">The type of transaction to get. See <see cref="TransactionType"/>.</param>
    /// <param name="kind">The kind of contribution to get. See <see cref="ContributionType"/>.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("Filings/{filingId:long}/[controller]", Name = nameof(GetAllTransactionsByFiling))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<PagedTransactionListResponse>> GetAllTransactionsByFiling(
        [FromRoute] long filingId,
        [FromServices] IGetAllTransactions getAllTransactions,
        [FromQuery] StandardFilter filter,
        [FromQuery] TransactionType? type = null,
        [FromQuery] ContributionType? kind = null,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([HasAccess.To<Filing>(filingId)]);

        var command = new IGetAllTransactions.ByFiling(filingId, type, kind, filter.Page, filter.PageSize);
        var result = await getAllTransactions.Execute(command, cancellationToken);

        if (result.Unwrap(out var success, out var failure))
        {
            return failure switch
            {
                INotFound n => NotFound(n),
                IDependencyFailed d => Problem(detail: d.Message),
                _ => Problem(),
            };
        }

        string? LinkBuilder(int page)
        {
            var state = new { Page = page, filter.PageSize, Type = type?.Name, Kind = kind?.Name };
            return Url.Link(nameof(GetAllTransactionsByFiling), state);
        }

        PageLinks links = PageLinks.For(LinkBuilder, filter, success.TotalItems);

        return Ok(new PagedTransactionListResponse(success, links, success.Metadata.Total));
    }

    /// <summary>
    /// Creates a new lobbying campaign contribution.
    /// </summary>
    /// <param name="filerId">The filer id to which the transaction will be assigned.</param>
    /// <param name="request">Request data from body.</param>
    /// <param name="decisionsSvc">The decision service.</param>
    /// <param name="transactionSvc">The transaction service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{filerId:long}/[controller]/LobbyingCampaignContribution/Create")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<TransactionItemResponse>> CreateLobbyingCampaignContribution(
        [FromRoute] long filerId,
        [FromBody] LobbyistCampaignContributionRequestDto request,
        [FromServices] IDecisionsSvc decisionsSvc,
        [FromServices] ITransactionSvc transactionSvc,
        CancellationToken cancellationToken = default)
    {
        if (request?.NonCommitteeRecipientName == string.Empty)
        {
            request.NonCommitteeRecipientName = null;
        }
        if (request?.NonFilerContributorName == string.Empty)
        {
            request.NonFilerContributorName = null;
        }
        if (request?.SeparateAccountName == string.Empty)
        {
            request.SeparateAccountName = null;
        }
        LobbyistCampaignContributionDs decisionsData = PopulateLobbyistCampaignContributionDs(request);
        var decisionResponse = await decisionsSvc.InitiateWorkflow<LobbyistCampaignContributionDs, List<WorkFlowError>>(DecisionsWorkflow.LobbyistCampaignContributionRuleset, decisionsData, true);

        WebApiHelper.ValidateRequiredBooleanFields(request, decisionResponse);

        if (decisionResponse != null && decisionResponse.Count > 0)
        {
            // Return 422 with validation errors
            return UnprocessableEntity(decisionResponse);
        }
        var createdTransaction = await transactionSvc.CreateLobbyingCampaignContribution(filerId, request.FilingId, request.NonCommitteeRecipientName, request.Amount, request.TransactionDate ?? dateTimeSvc.GetCurrentDateTime(), request.IsRecipientCommittee, request.RecipientCommitteeFilerId, request.IsContributorFiler, request.NonFilerContributorName, request.ContributorFilerId, request.SeparateAccountName);
        var response = createdTransaction is not null ? new TransactionItemResponse(createdTransaction) : null;
        return response is not null ? response : NotFound();
    }

    private static LobbyistCampaignContributionDs PopulateLobbyistCampaignContributionDs(LobbyistCampaignContributionRequestDto contribution)
    {
        var lobbyistCampaignContributionDs = new LobbyistCampaignContributionDs
        {
            TransactionDate = contribution.TransactionDate,
            Amount = contribution.Amount,
            CommitteeId = contribution.RecipientCommitteeFilerId,
            NonCommitteeRecipentName = contribution.NonCommitteeRecipientName,
            FilerId = contribution.FilerId,
            ContributorFilerId = contribution.ContributorFilerId,
            NonFilerContributorName = contribution.NonFilerContributorName,
            SeparateAccountName = contribution.SeparateAccountName
        };

        if (contribution.IsRecipientCommittee.HasValue)
        {
            lobbyistCampaignContributionDs.IsRecipientCommittee = contribution.IsRecipientCommittee;
        }

        if (contribution.IsContributorFiler.HasValue)
        {
            lobbyistCampaignContributionDs.IsContributorFiler = contribution.IsContributorFiler;
        }

        return lobbyistCampaignContributionDs;
    }

    /// <summary>
    /// Creates a new lobbying campaign contribution for lobbyist employer or coalition.
    /// </summary>
    /// <param name="filerId">The filer id to which the transaction will be assigned.</param>
    /// <param name="request">Request data from body.</param>
    /// <param name="decisionsSvc">The decision service.</param>
    /// <param name="transactionSvc">The transaction service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{filerId:long}/[controller]/LobbyistEmployerCoalitionLobbyingCampaignContribution/Create")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<TransactionItemResponse>> CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
        [FromRoute] long filerId,
        [FromBody] LobbyingCampaignContributionRequestDto request,
        [FromServices] IDecisionsSvc decisionsSvc,
        [FromServices] ITransactionSvc transactionSvc,
        CancellationToken cancellationToken = default)
    {
        if (request?.NonCommitteeRecipientName == string.Empty)
        {
            request.NonCommitteeRecipientName = null;
        }
        LobbyistEmployerCoalitionCampaignContributionDs decisionsData = PopulateLobbyistEmployerCoalitionCampaignContributionDs(request);
        var decisionResponse = await decisionsSvc.InitiateWorkflow<LobbyistEmployerCoalitionCampaignContributionDs, List<WorkFlowError>>(DecisionsWorkflow.LobbyistEmployerCoalitionCampaignContributonRuleset, decisionsData, true);

        WebApiHelper.ValidateRequiredBooleanFields(request, decisionResponse);

        if (decisionResponse != null && decisionResponse.Count > 0)
        {
            // Return 422 with validation errors
            return UnprocessableEntity(decisionResponse);
        }
        var createdTransaction = await transactionSvc.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(filerId, request.FilingId, request.NonCommitteeRecipientName, request.Amount, request.TransactionDate ?? dateTimeSvc.GetCurrentDateTime(), request.IsRecipientCommittee, request.RecipientCommitteeFilerId);
        var response = createdTransaction is not null ? new TransactionItemResponse(createdTransaction) : null;
        return response is not null ? response : NotFound();
    }

    private static LobbyistEmployerCoalitionCampaignContributionDs PopulateLobbyistEmployerCoalitionCampaignContributionDs(LobbyingCampaignContributionRequestDto contribution)
    {
        var lobbyingCampaignContributionDs = new LobbyistEmployerCoalitionCampaignContributionDs
        {
            TransactionDate = contribution.TransactionDate,
            Amount = contribution.Amount,
            CommitteeId = contribution.RecipientCommitteeFilerId,
            FilerId = contribution.FilerId,
            NonCommitteeRecipentName = contribution.NonCommitteeRecipientName
        };

        if (contribution.IsRecipientCommittee.HasValue)
        {
            lobbyingCampaignContributionDs.IsRecipientCommittee = contribution.IsRecipientCommittee;
        }

        return lobbyingCampaignContributionDs;
    }

    /// <summary>
    /// Gets transaction summary scoped by filingId
    /// </summary>
    /// <param name="filerId">The filer id to which the transaction will be assigned.</param>
    /// <param name="transactionSvc">The transaction service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(ITransactionSvc.GetTransactionSummaryPath, Name = nameof(GetTransactionSummary))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<TransactionSummaryResponse>> GetTransactionSummary(
        [FromRoute] long filingId,
        [FromServices] ITransactionSvc transactionSvc,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var response = await transactionSvc.GetTransactionSummary(filingId);
        return response is not null ? response : NotFound();
    }

    /// <summary>
    /// Retrieves a list of other payments to influence variants, scoped by filing ID.
    /// </summary>
    /// <param name="filingId">The filing id for which to get all lobbyist employer coalition payment transactions.</param>
    /// <param name="transactionSvc">The transaction service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(ITransactionSvc.GetAllEndOfSessionLobbyingTransactionsForFilingPath, Name = nameof(GetAllEndOfSessionLobbyingTransactionsForFiling))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<IReadOnlyList<EndOfSessionLobbyingDto>>> GetAllEndOfSessionLobbyingTransactionsForFiling(
        [FromRoute] long filingId,
        [FromServices] ITransactionSvc transactionSvc,
        CancellationToken cancellationToken = default)
    {
        var transactions = await transactionSvc.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId);
        return transactions.ToList().AsReadOnly();
    }

    /// <summary>
    /// Creates a new other payment to influence.
    /// </summary>
    /// <param name="filerId">The filer id to which the other payment to influence will be assigned.</param>
    /// <param name="filingId">The filing id to which the other payment to influence will be assigned.</param>
    /// <param name="otherInfluencePaymentDto">Request data from body.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("Filers/{filerId:long}/filings/{filingId:long}/OtherPayments", Name = nameof(CreateOtherInfluencePayment))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    [AllowAnonymous]
    public async Task<ActionResult<TransactionItemResponse>> CreateOtherInfluencePayment(
        [FromRoute] long filerId,
        [FromRoute] long filingId,
        [FromServices] ITransactionSvc transactionSvc,
        [FromServices] IDecisionsSvc decisionsSvc,
        [FromBody] OtherInfluencePaymentDto otherInfluencePaymentDto,
        CancellationToken cancellationToken = default)
    {
        WebApiHelper.NormalizeEmptyStringsToNull(otherInfluencePaymentDto);
        OtherPaymentsToInfluenceDs decisionsData = PopulateOtherPaymentsToInfluenceDs(otherInfluencePaymentDto);
        var decisionsResponse = await decisionsSvc.InitiateWorkflow<OtherPaymentsToInfluenceDs, List<WorkFlowError>>(DecisionsWorkflow.OtherPaymentsToInfluenceTransactionRuleset, decisionsData, true);
        if (decisionsResponse != null && decisionsResponse.Count > 0)
        {
            // Return 422 with validation errors
            return UnprocessableEntity(decisionsResponse);
        }

        var newOtherInfluencePayment = await transactionSvc.CreateOtherPaymentToInfluence(otherInfluencePaymentDto);
        return CreatedAtRoute(
            nameof(GetTransaction),
            new { id = newOtherInfluencePayment.Id },
            new TransactionItemResponse(newOtherInfluencePayment));
    }

    /// <summary>
    /// Creates a new other payment to influence.
    /// </summary>
    /// <param name="transactionId"> The transaction id to edit.</param>
    /// <param name="otherInfluencePaymentDto">Request data from body.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPut(ITransactionSvc.EditOtherPaymentToInfluencePath, Name = nameof(EditOtherInfluencePayment))]
    public async Task<ActionResult<TransactionItemResponse>> EditOtherInfluencePayment(
        [FromRoute] long transactionId,
        [FromBody] OtherInfluencePaymentDto otherInfluencePaymentDto,
        [FromServices] ITransactionSvc transactionSvc,
        [FromServices] IDecisionsSvc decisionsSvc,
        CancellationToken cancellationToken = default)
    {
        WebApiHelper.NormalizeEmptyStringsToNull(otherInfluencePaymentDto);
        OtherPaymentsToInfluenceDs decisionsData = PopulateOtherPaymentsToInfluenceDs(otherInfluencePaymentDto);
        var decisionsResponse = await decisionsSvc.InitiateWorkflow<OtherPaymentsToInfluenceDs, List<WorkFlowError>>(DecisionsWorkflow.OtherPaymentsToInfluenceTransactionRuleset, decisionsData, true);
        if (decisionsResponse != null && decisionsResponse.Count > 0)
        {
            // Return 422 with validation errors
            return UnprocessableEntity(decisionsResponse);
        }

        var updatedOtherInfluencePayment = await transactionSvc.EditOtherPaymentToInfluence(transactionId, otherInfluencePaymentDto);
        return CreatedAtRoute(
            nameof(GetTransaction),
            new { id = updatedOtherInfluencePayment.Id },
            new TransactionItemResponse(updatedOtherInfluencePayment));
    }

    /// <summary>
    /// Get other payment to fluence transaction by id.
    /// </summary>
    /// <param name="transactionId"> The transaction ID.</param>
    /// <param name="request">The payment details.</param>
    /// <param name="transactionSvc">The transaction service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(ITransactionSvc.GetOtherPaymentsToInfluenceTransactionByIdPath)]
    public async Task<ActionResult<OtherInfluencePaymentDto>> GetOtherPaymentsToInfluenceTransactionById(
        [FromRoute] long transactionId,
        [FromServices] ITransactionSvc transactionSvc,
        CancellationToken cancellationToken = default)
    {
        return await transactionSvc.GetOtherPaymentsToInfluenceTransactionById(transactionId);
    }


    private static OtherPaymentsToInfluenceDs PopulateOtherPaymentsToInfluenceDs(OtherInfluencePaymentDto dto)
    {
        var lobbyingSubjects = new List<string>();
        if (dto.AdvertLegislation == true)
        {
            lobbyingSubjects.Add("Legislation");
        }
        if (dto.AdvertAdminActions == true)
        {
            lobbyingSubjects.Add("AdministrativeActions");
        }
        if (dto.AdvertOther == true)
        {
            lobbyingSubjects.Add("Other");
        }

        var otherPaymentsToInfluenceDs = new OtherPaymentsToInfluenceDs
        {
            PaymentCodeId = dto.PaymentCodeId?.ToString(CultureInfo.InvariantCulture),
            AdvertisingInfo = new AdvertisingInfoDs
            {
                LobbyingAdvertisementSubjects = lobbyingSubjects,
                AssemblyBills = dto.AssemblyBills?
                    .Where(b => b != null)
                    .Select(b => new BillInfoDs
                    {
                        BillNumber = b?.BillId?.ToString(CultureInfo.InvariantCulture)
                    }).ToList() ?? new List<BillInfoDs>(),
                SenateBills = dto.SenateBills?
                    .Where(b => b != null)
                    .Select(b => new BillInfoDs
                    {
                        BillNumber = b?.BillId?.ToString(CultureInfo.InvariantCulture)
                    }).ToList() ?? new List<BillInfoDs>(),
                AdministrativeActions = dto.AdministrativeActions?
                    .Where(a => a != null)
                    .Select(a => new AdministrativeActionDs
                    {
                        AgencyOrOffice = a?.AgencyId?.ToString(CultureInfo.InvariantCulture),
                        ActionDescription = a?.AgencyDescription
                    }).ToList() ?? new List<AdministrativeActionDs>(),
                OtherActionDescription = dto.OtherActionsLobbied
            },
            OtherPaymentDescription = dto.PaymentCodeDescription,
        };

        if (dto.Amount != 0)
        {
            otherPaymentsToInfluenceDs.QuarterlyAmount = dto.Amount;
        }

        return otherPaymentsToInfluenceDs;
    }

    /// <summary>
    /// Creates a new payment made to lobbying firms transaction.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost(ITransactionSvc.CreatePaymentMadeToLobbyingFirmsTransactionPath, Name = nameof(CreatePaymentMadeToLobbyingFirmsTransaction))]
    public async Task<TransactionResponseDto> CreatePaymentMadeToLobbyingFirmsTransaction([FromBody] PaymentMadeToLobbyingFirmsRequestDto request)
    {
        return await transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request);
    }

    /// <summary>
    /// Get cumulative amount for other payments to influence variants for a specific filing id and contact id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <param name="contactId"></param>
    /// <param name="legislativeStartDate"></param>
    /// <returns>A response with CumulativeAmount.</returns>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("[controller]/filings/{filingId:long}/contact/{contactId:long}/GetCumulativeAmount")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<CumulativeAmountResponse>> GetOtherPaymentsCumulativeAmountForFilingAndContact(
        [FromRoute] long filingId,
        [FromRoute] long contactId,
        [FromServices] ITransactionSvc transactionSvc,
        [FromServices] IFilingSvc filingSvc,
        CancellationToken cancellationToken = default)
    {
        var cumulativeAmountResponse = await transactionSvc.GetOtherPaymentsCumulativeAmountForFilingAndContact(filingId, contactId);
        return cumulativeAmountResponse is not null ? Ok(cumulativeAmountResponse) : NotFound();
    }

    /// <summary>
    /// Retrieves lobbyist campaign contribution transaction, scoped by transaction ID.
    /// </summary>
    /// <param name="transactionId">The transaction id for which to get lobbyist campaign contribution transaction.</param>
    /// <param name="transactionSvc">The transaction service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(ITransactionSvc.GetLobbyistCampaignContributionTransactionByIdPath, Name = nameof(GetLobbyistCampaignContributionTransactionById))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<LobbyistCampaignContributionItemResponse>> GetLobbyistCampaignContributionTransactionById(
        [FromRoute] long transactionId,
        [FromServices] ITransactionSvc transactionSvc,
        CancellationToken cancellationToken = default)
    {
        var transaction = await transactionSvc.GetLobbyistCampaignContributionTransactionById(transactionId);
        return transaction is not null ? Ok(new LobbyistCampaignContributionItemResponse(transaction)) : NotFound();
    }

    /// <summary>
    /// Edit a lobbyist campaign contribution.
    /// </summary>
    /// <param name="request">Request data from body.</param>
    /// <param name="decisionsSvc">The decision service.</param>
    /// <param name="transactionSvc">The transaction service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost(ITransactionSvc.EditLobbyistCampaignContributionPath, Name = nameof(EditLobbyistCampaignContribution))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Create))]
    public async Task<ActionResult<TransactionItemResponse>> EditLobbyistCampaignContribution(
        [FromBody] LobbyistCampaignContributionRequestDto request,
        [FromServices] IDecisionsSvc decisionsSvc,
        [FromServices] ITransactionSvc transactionSvc,
        CancellationToken cancellationToken = default)
    {
        LobbyistCampaignContributionDs decisionsData = PopulateLobbyistCampaignContributionDs(request);
        var decisionResponse = await decisionsSvc.InitiateWorkflow<LobbyistCampaignContributionDs, List<WorkFlowError>>(DecisionsWorkflow.LobbyistCampaignContributionRuleset, decisionsData, true);

        WebApiHelper.ValidateRequiredBooleanFields(request, decisionResponse);

        if (decisionResponse != null && decisionResponse.Count > 0)
        {
            // Return 422 with validation errors
            return UnprocessableEntity(decisionResponse);
        }

        var editedTransaction = await transactionSvc.EditLobbyistCampaignContribution(request);
        return editedTransaction is not null ? Ok(new TransactionItemResponse(editedTransaction)) : NotFound();
    }

    /// <inheritdoc/>
    [HttpPost(ITransactionSvc.CreateEndOfSessionLobbyingPath, Name = nameof(CreateEndOfSessionLobbying))]
    public async Task<TransactionResponseDto> CreateEndOfSessionLobbying([FromBody] EndOfSessionLobbyingRequestDto body)
    {
        return await transactionSvc.CreateEndOfSessionLobbying(body);
    }

    /// <inheritdoc/>
    [HttpGet(ITransactionSvc.GetEndOfSessionLobbyingTransactionByIdPath, Name = nameof(GetEndOfSessionLobbyingTransactionById))]
    public async Task<EndOfSessionLobbyingDto> GetEndOfSessionLobbyingTransactionById(
        [FromRoute] long transactionId,
        CancellationToken cancellationToken = default)
    {
        return await transactionSvc.GetEndOfSessionLobbyingTransactionById(transactionId);
    }

    /// <inheritdoc/>
    [HttpPatch(ITransactionSvc.EditEndOfSessionLobbyingPath, Name = nameof(EditEndOfSessionLobbying))]
    public async Task<TransactionResponseDto> EditEndOfSessionLobbying(
        [FromRoute] long transactionId,
        [FromBody] EndOfSessionLobbyingRequestDto body,
        CancellationToken cancellationToken = default)
    {
        return await transactionSvc.EditEndOfSessionLobbying(transactionId, body);
    }

    /// <inheritdoc/>
    [HttpPatch(ITransactionSvc.DeleteTransactionAsyncPath, Name = nameof(DeleteTransactionAsync))]
    public async Task DeleteTransactionAsync([FromRoute] long filingId, [FromRoute] long transactionId)
    {
        await transactionSvc.DeleteTransactionAsync(filingId, transactionId);
    }
}
