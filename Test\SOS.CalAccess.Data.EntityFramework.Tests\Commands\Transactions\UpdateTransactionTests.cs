// <copyright file="UpdateTransactionTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Transactions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Transactions;

/// <summary>
/// Tests for the <see cref="UpdateTransaction"/> command processor implementation.
/// </summary>
[TestFixture]
[TestOf(typeof(UpdateTransaction))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
public sealed class UpdateTransactionTests
{
    private readonly IDecisionsService _decisions = Substitute.For<IDecisionsService>();
    private readonly IAuditService _auditService = Substitute.For<IAuditService>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    /// <summary>
    /// Tests that <see cref="UpdateTransaction"/> returns an instance of <see cref="Failure{TResult}.NotFound"/>
    /// if the targeted transaction cannot be located.
    /// </summary>
    /// <returns>A <see cref="Task"/> instance representing the execution of the async unit test.</returns>
    [Test]
    public async Task UpdateTransaction_ReturnsNotFoundFailure_WhenTargetTransactionDoesNotExist()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var commandProcessor = new UpdateTransaction(context, _decisions, _auditService, _dateTimeSvc);

        var command = new UpdateExpenditureCommand(default, TransactionLike.Dummy, "Testing");

        var result = await commandProcessor.Execute(command);

        Assert.That(result, Is.AssignableTo<Failure<Transaction>.NotFound>());
    }

    /// <summary>
    /// Tests that <see cref="UpdateTransaction"/> returns an instance of <see cref="Failure{TResult}.DependencyFailed"/>
    /// if the requested target contact cannot be located.
    /// </summary>
    /// <returns>A <see cref="Task"/> instance representing the execution of the async unit test.</returns>
    [Test]
    public async Task UpdateTransaction_ReturnsDependencyFailed_WhenTargetContactDoesNotExist()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var data = (await context.PrepareTransactionsData())[0];

        var commandProcessor = new UpdateTransaction(context, _decisions, _auditService, _dateTimeSvc);

        var command = new UpdateExpenditureCommand(data.TransactionId, TransactionLike.Dummy, "Testing");

        var result = await commandProcessor.Execute(command);

        Assert.That(result, Is.AssignableTo<Failure<Transaction>.DependencyFailed>());
    }

    /// <summary>
    /// Tests that <see cref="UpdateTransaction"/> forwards the failure from the underlying command's
    /// <see cref="UpdateTransactionCommand.Apply"/> implementation if it fails.
    /// </summary>
    /// <remarks>This should also serve as a test that the command's apply is always called.</remarks>
    /// <returns>A <see cref="Task"/> instance representing the execution of the async unit test.</returns>
    [Test]
    public async Task UpdateTransaction_ForwardsFailure_WhenTheUnderlyingCommandFails()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var data = (await context.PrepareTransactionsData())[0];

        var commandProcessor = new UpdateTransaction(context, _decisions, _auditService, _dateTimeSvc);

        var command = new FailingUpdateCommand(
            data.TransactionId, TransactionLike.Dummy with { ContactId = data.ContactId });

        var result = await commandProcessor.Execute(command);

        Assert.That(result, Is.AssignableTo<Failure<Transaction>.DependencyFailed>()
            .And.Property(nameof(IFailure.Message)).EqualTo("Always fails"));
    }

    /// <summary>
    /// Tests that <see cref="UpdateTransaction"/> returns a success instance when all required operations
    /// complete successfully and that the database context is requested to save changes *once and only once*
    /// in that case.
    /// </summary>
    /// <returns>A <see cref="Task"/> instance representing the execution of the async unit test.</returns>
    [Test]
    public async Task UpdateTransaction_SavesChangesAndReturnsSuccess_OnComplete()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var data = (await context.PrepareTransactionsData())[0];

        int called = 0;
        context.SavedChanges += (r, a) => called += 1;

        var commandProcessor = new UpdateTransaction(context, _decisions, _auditService, _dateTimeSvc);

        var command = new UpdateExpenditureCommand(
            data.TransactionId, TransactionLike.Dummy with { ContactId = data.ContactId }, "Testing Update");

        var result = await commandProcessor.Execute(command);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.AssignableTo<Success<Transaction>>());
            Assert.That(called, Is.EqualTo(1));
        });
    }
}
