using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;

/// <summary>
/// Unit tests for the <see cref="RegistrationModelExtensions"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(RegistrationModelMapper))]
public sealed class RegistrationModelExtensionTests
{
    private IReferenceDataSvc _referenceDataSvc;
    private RegistrationModelMapper _registrationModelMapper;
    private CandidateIntentionStatementRequest _cisRequest;
    private SmoContactRequest _smoContactRequest;
    private LobbyistRegistrationRequestDto _lobbyistRegRequest;
    private DateTime _dateNow;


    [SetUp]
    public void SetUp()
    {
        _cisRequest = new CandidateIntentionStatementRequest
        {
            FirstName = "FirstName",
            MiddleName = "MiddleName",
            LastName = "LastName",
            OriginalId = 1,
            Email = "<EMAIL>",
            Telephone = new PhoneNumberDto() { CountryCode = "+1", Number = "1234567890" },
            FaxNumber = new PhoneNumberDto() { CountryCode = "+1", Number = "1234567890" },
            SelfRegister = true,
            PreviousCandidate = true,
            IsSameAsCandidateAddress = false,
            CandidateAddress = new()
            {
                Street = "CandidateStreet",
                Street2 = "CandidateStreet2",
                City = "CandidateCity",
                State = "CandidateState",
                Zip = "CandidateZip",
                Type = "CandidateType",
                Country = "CandidateCountry",
                Purpose = "CandidatePurpose",
            },
            CandidateMailingAddress = new()
            {
                Street = "MailingStreet",
                Street2 = "MailingStreet2",
                City = "MailingCity",
                State = "MailingState",
                Zip = "MailingZip",
                Type = "MailingType",
                Country = "MailingCountry",
                Purpose = "MailingPurpose",
            },
            CheckRequiredFieldsFlag = false,
        };

        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();

        _registrationModelMapper = new RegistrationModelMapper(_referenceDataSvc);

        _smoContactRequest = new SmoContactRequest
        {
            Name = "Sample contact name",
            Email = "<EMAIL>",
            County = "Sacramento",
            IsSameAsOrganizationAddress = true,
            OrganizationAddress = new AddressDto
            {
                Street = "NewOrgStreet",
                Street2 = "NewOrgStreet2",
                City = "NewOrgCity",
                State = "NewOrgState",
                Zip = "NewOrgZip",
                Type = "NewOrgType",
                Country = "NewOrgCountry",
                Purpose = "Organization",
            },
            MailingAddress = new AddressDto
            {
                Street = "NewMailingStreet",
                Street2 = "NewMailingStreet2",
                City = "NewMailingCity",
                State = "NewMailingState",
                Zip = "NewMailingZip",
                Type = "NewMailingType",
                Country = "NewMailingCountry",
                Purpose = "Mailing",
            },
            PhoneNumber = new PhoneNumberDto(),
            FaxNumber = new PhoneNumberDto(),
        };

        _lobbyistRegRequest = new LobbyistRegistrationRequestDto
        {
            SelfRegister = true,
            FirstName = "FirstName",
            MiddleName = "MiddleName",
            LastName = "LastName",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto() { Number = "1234567890", CountryCode = "+1" },
            FaxNumber = new PhoneNumberDto() { Number = "0987654321", CountryCode = "+1" },
            FilerId = 1,
            LobbyistEmployerOrLobbyingFirmId = 1,
            BusinessAddress = new()
            {
                Street = "LobbyistStreet",
                Street2 = "LobbyistStreet2",
                City = "LobbyistCity",
                State = "LobbyistState",
                Zip = "LobbyistZip",
                Type = "LobbyistType",
                Country = "LobbyistCountry",
                Purpose = "Organization",
            },
            MailingAddress = new()
            {
                Street = "MailingStreet",
                Street2 = "MailingStreet2",
                City = "MailingCity",
                State = "MailingState",
                Zip = "MailingZip",
                Type = "MailingType",
                Country = "MailingCountry",
                Purpose = "Mailing",
            },
            LegislativeSessionId = 1,
            DateOfQualification = _dateNow,
            IsPlacementAgent = true,
            CompletedEthicsCourse = true,
            IsNewCertification = null,
            CompletedCourseDate = _dateNow,
            Agencies = new()
            {
                new RegistrationAgencyDto{ AgencyId = 1, AgencyName = "dummy agency", RegistrationId = 1 },
                new RegistrationAgencyDto{ AgencyId = 2, AgencyName = "dummy agency 2", RegistrationId = 1 }
            },
            IsLobbyingStateLegislature = true,
            Photo = "Testing photo",
            DiligenceVerificationStatement = null,
        };
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    #region AsRegistration
    [Test]
    public void AsRegistration_ShouldMapToCandidateIntentionStatement()
    {
        // Arrange

        // Act
        var model = _registrationModelMapper.MapCandidateIntentionStatementRequestToModel(_cisRequest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Name, Is.EqualTo("FirstName MiddleName LastName"));
            Assert.That(model.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(model.OriginalId, Is.EqualTo(1));
        });
    }

    [Test]
    public void AsRegistration_ShouldMapToExistingCandidateIntentionStatement()
    {
        // Arrange
        var modelIn = new CandidateIntentionStatement
        {
            Name = "OriginalName",
            StatusId = 1,
            AddressList = new()
            {
                Addresses = new List<Models.Common.Address>
                {
                    new () {
                        Street = "OldCandidateStreet",
                        Street2 = "OldCandidateStreet2",
                        City = "OldCandidateCity",
                        State = "OldCandidateState",
                        Zip = "OldCandidateZip",
                        Type = "OldCandidateType",
                        Country = "OldCandidateCountry",
                        Purpose = "Candidate",
                    },
                    new () {
                        Street = "OldMailingStreet",
                        Street2 = "OldMailingStreet2",
                        City = "OldMailingCity",
                        State = "OldMailingState",
                        Zip = "OldMailingZip",
                        Type = "OldMailingType",
                        Country = "OldMailingCountry",
                        Purpose = "Mailing",
                    }
                }
            },
            PhoneNumberList = new() { },
        };

        // Act
        var modelOut = _registrationModelMapper.UpdateCandidateIntentionStatement(modelIn, _cisRequest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelOut.Name, Is.EqualTo("FirstName MiddleName LastName"));
            Assert.That(modelOut.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(modelOut.AddressList?.Addresses[0].Street, Is.EqualTo("CandidateStreet"));
            Assert.That(modelOut.AddressList?.Addresses[1].Street, Is.EqualTo("MailingStreet"));
        });
    }
    [Test]
    public void As_Registration_UpdateHomePhoneNumber_WhenCandidateHomePhoneNumberIsNotNull()
    {
        // Arrange
        var request = new CandidateIntentionStatementRequest
        {
            Telephone = new PhoneNumberDto() { CountryCode = "replace_with_ref", SelectedCountry = 24, Number = "1234567890", Extension = "222" }
        };

        var existingCandidateIntentionStatement = new CandidateIntentionStatement
        {
            Name = "test",
            StatusId = 1,
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
            {
                new() {
                    Type = "Home",
                    Number = "************"
                }
            }
            }
        };

        _referenceDataSvc.GetCountryById(24).Returns(new Country() { Id = 24, CountryName = "Test Country", PhoneCountryCode = "+42" });

        // Act
        var modelOut = _registrationModelMapper.UpdateCandidateIntentionStatement(existingCandidateIntentionStatement, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers, Has.Count.EqualTo(1));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].Number, Is.EqualTo("1234567890"));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].Extension, Is.EqualTo("222"));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].CountryId, Is.EqualTo(24));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].CountryCode, Is.EqualTo("+42"));
        });
    }
    [Test]
    public void As_Registration_UpdateFaxPhoneNumber_WhenCandidateFaxPhoneNumberIsNotNull()
    {
        // Arrange
        var request = new CandidateIntentionStatementRequest
        {
            FaxNumber = new PhoneNumberDto() { CountryCode = "replace_with_ref", SelectedCountry = 24, Number = "1234567890", Extension = "222" }
        };

        var existingCandidateIntentionStatement = new CandidateIntentionStatement
        {
            Name = "test",
            StatusId = 1,
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
            {
                new() {
                    Type = "Fax",
                    Number = "************"
                }
            }
            }
        };

        _referenceDataSvc.GetCountryById(24).Returns(new Country() { Id = 24, CountryName = "Test Country", PhoneCountryCode = "+42" });

        // Act
        var modelOut = _registrationModelMapper.UpdateCandidateIntentionStatement(existingCandidateIntentionStatement, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers, Has.Count.EqualTo(1));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].Number, Is.EqualTo("1234567890"));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].Extension, Is.EqualTo("222"));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].CountryId, Is.EqualTo(24));
            Assert.That(existingCandidateIntentionStatement.PhoneNumberList.PhoneNumbers[0].CountryCode, Is.EqualTo("+42"));
        });
    }

    [Test]
    public void AsRegistration_ShouldMapToSmoContactRequest()
    {
        // Arrange


        // Act
        var model = _registrationModelMapper.MapSmoContactRequestToModel(_smoContactRequest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Name, Is.EqualTo("Sample contact name"));
            Assert.That(model.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(model.AddressList, Is.Not.Null);
            Assert.That(model.PhoneNumberList, Is.Not.Null);
            Assert.That(model.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(model.IsSameAsCandidateAddress, Is.EqualTo(true));
            Assert.That(model.County, Is.EqualTo("Sacramento"));
        });
    }

    #endregion

    #region SmoContactRequest
    [Test]
    public void SmoContactRequest_AsRegistration_ShouldMapData()
    {
        // Arrange
        var input = new SmoContactRequest
        {
            Name = "NewTest",
            Email = "<EMAIL>",
            County = "Sacramento",
            IsSameAsOrganizationAddress = true,
            OrganizationAddress = null,
            MailingAddress = null,
            PhoneNumber = null,
            FaxNumber = null,
        };
        var existing = new SlateMailerOrganization
        {
            Name = "OldName",
            Email = "<EMAIL>",
            County = "Alameda",
            IsSameAsCandidateAddress = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateSlateMailerOrganization(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("NewTest"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.County, Is.EqualTo("Sacramento"));
            Assert.That(output.IsSameAsCandidateAddress, Is.EqualTo(true));
        });
    }

    [Test]
    public void SmoContactRequest_AsRegistration_ShouldMapDataWithBothAddressExisting()
    {
        // Arrange
        var input = new SmoContactRequest
        {
            Name = "NewTest",
            Email = "<EMAIL>",
            County = "Sacramento",
            IsSameAsOrganizationAddress = true,
            OrganizationAddress = new AddressDto
            {
                Street = "NewOrgStreet",
                Street2 = "NewOrgStreet2",
                City = "NewOrgCity",
                State = "NewOrgState",
                Zip = "NewOrgZip",
                Type = "NewOrgType",
                Country = "NewOrgCountry",
                Purpose = "Organization",
            },
            MailingAddress = new AddressDto
            {
                Street = "NewMailingStreet",
                Street2 = "NewMailingStreet2",
                City = "NewMailingCity",
                State = "NewMailingState",
                Zip = "NewMailingZip",
                Type = "NewMailingType",
                Country = "NewMailingCountry",
                Purpose = "Mailing",
            },
            PhoneNumber = null,
            FaxNumber = null,
        };
        var existing = new SlateMailerOrganization
        {
            Name = "OldName",
            Email = "<EMAIL>",
            County = "Alameda",
            IsSameAsCandidateAddress = false,
            StatusId = RegistrationStatus.Draft.Id,
            AddressList = new AddressList()
            {
                Addresses = new List<Address>
                {
                    new() {
                        Street = "OldOrgStreet",
                        Street2 = "OldOrgStreet2",
                        City = "OldOrgCity",
                        State = "OldOrgState",
                        Zip = "OldOrgZip",
                        Type = "OldOrgType",
                        Country = "OldOrgCountry",
                        Purpose = "Organization",
                    },
                    new() {
                        Street = "OldMailingStreet",
                        Street2 = "OldMailingStreet2",
                        City = "OldMailingCity",
                        State = "OldMailingState",
                        Zip = "OldMailingZip",
                        Type = "OldMailingType",
                        Country = "OldMailingCountry",
                        Purpose = "Mailing",
                    },
                }
            }
        };

        // Act
        var output = _registrationModelMapper.UpdateSlateMailerOrganization(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("NewTest"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.County, Is.EqualTo("Sacramento"));
            Assert.That(output.IsSameAsCandidateAddress, Is.EqualTo(true));
        });
        Assert.That(output.AddressList, Is.Not.Null);
        var orgAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Organization");
        Assert.That(orgAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(orgAddress.Street, Is.EqualTo("NewOrgStreet"));
            Assert.That(orgAddress.Street2, Is.EqualTo("NewOrgStreet2"));
            Assert.That(orgAddress.City, Is.EqualTo("NewOrgCity"));
            Assert.That(orgAddress.State, Is.EqualTo("NewOrgState"));
            Assert.That(orgAddress.Zip, Is.EqualTo("NewOrgZip"));
            Assert.That(orgAddress.Type, Is.EqualTo("NewOrgType"));
            Assert.That(orgAddress.Country, Is.EqualTo("NewOrgCountry"));
        });
        var mailAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Mailing");
        Assert.That(mailAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(mailAddress.Street, Is.EqualTo("NewMailingStreet"));
            Assert.That(mailAddress.Street2, Is.EqualTo("NewMailingStreet2"));
            Assert.That(mailAddress.City, Is.EqualTo("NewMailingCity"));
            Assert.That(mailAddress.State, Is.EqualTo("NewMailingState"));
            Assert.That(mailAddress.Zip, Is.EqualTo("NewMailingZip"));
            Assert.That(mailAddress.Type, Is.EqualTo("NewMailingType"));
            Assert.That(mailAddress.Country, Is.EqualTo("NewMailingCountry"));
        });
    }

    [Test]
    public void SmoContactRequest_AsRegistration_ShouldMapDataWithBothAddressNotExisting()
    {
        // Arrange
        var input = new SmoContactRequest
        {
            Name = "NewTest",
            Email = "<EMAIL>",
            County = "Sacramento",
            IsSameAsOrganizationAddress = true,
            OrganizationAddress = new AddressDto
            {
                Street = "NewOrgStreet",
                Street2 = "NewOrgStreet2",
                City = "NewOrgCity",
                State = "NewOrgState",
                Zip = "NewOrgZip",
                Type = "NewOrgType",
                Country = "NewOrgCountry",
                Purpose = "Organization",
            },
            MailingAddress = new AddressDto
            {
                Street = "NewMailingStreet",
                Street2 = "NewMailingStreet2",
                City = "NewMailingCity",
                State = "NewMailingState",
                Zip = "NewMailingZip",
                Type = "NewMailingType",
                Country = "NewMailingCountry",
                Purpose = "Mailing",
            },
            PhoneNumber = null,
            FaxNumber = null,
        };
        var existing = new SlateMailerOrganization
        {
            Name = "OldName",
            Email = "<EMAIL>",
            County = "Alameda",
            IsSameAsCandidateAddress = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateSlateMailerOrganization(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("NewTest"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.County, Is.EqualTo("Sacramento"));
            Assert.That(output.IsSameAsCandidateAddress, Is.EqualTo(true));
        });
        Assert.That(output.AddressList, Is.Not.Null);
        var orgAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Organization");
        Assert.That(orgAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(orgAddress.Street, Is.EqualTo("NewOrgStreet"));
            Assert.That(orgAddress.Street2, Is.EqualTo("NewOrgStreet2"));
            Assert.That(orgAddress.City, Is.EqualTo("NewOrgCity"));
            Assert.That(orgAddress.State, Is.EqualTo("NewOrgState"));
            Assert.That(orgAddress.Zip, Is.EqualTo("NewOrgZip"));
            Assert.That(orgAddress.Type, Is.EqualTo("NewOrgType"));
            Assert.That(orgAddress.Country, Is.EqualTo("NewOrgCountry"));
        });
        var mailAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Mailing");
        Assert.That(mailAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(mailAddress.Street, Is.EqualTo("NewMailingStreet"));
            Assert.That(mailAddress.Street2, Is.EqualTo("NewMailingStreet2"));
            Assert.That(mailAddress.City, Is.EqualTo("NewMailingCity"));
            Assert.That(mailAddress.State, Is.EqualTo("NewMailingState"));
            Assert.That(mailAddress.Zip, Is.EqualTo("NewMailingZip"));
            Assert.That(mailAddress.Type, Is.EqualTo("NewMailingType"));
            Assert.That(mailAddress.Country, Is.EqualTo("NewMailingCountry"));
        });
    }

    [Test]
    public void SmoContactRequest_AsRegistration_ShouldMapDataWithOrgAddress()
    {
        // Arrange
        var input = new SmoContactRequest
        {
            Name = "NewTest",
            Email = "<EMAIL>",
            County = "Sacramento",
            IsSameAsOrganizationAddress = true,
            OrganizationAddress = new AddressDto
            {
                Street = "NewOrgStreet",
                Street2 = "NewOrgStreet2",
                City = "NewOrgCity",
                State = "NewOrgState",
                Zip = "NewOrgZip",
                Type = "NewOrgType",
                Country = "NewOrgCountry",
                Purpose = "Organization",
            },
            MailingAddress = null,
            PhoneNumber = null,
            FaxNumber = null,
        };
        var existing = new SlateMailerOrganization
        {
            Name = "OldName",
            Email = "<EMAIL>",
            County = "Alameda",
            IsSameAsCandidateAddress = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateSlateMailerOrganization(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("NewTest"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.County, Is.EqualTo("Sacramento"));
            Assert.That(output.IsSameAsCandidateAddress, Is.EqualTo(true));
        });
        Assert.That(output.AddressList, Is.Not.Null);
        var orgAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Organization");
        Assert.That(orgAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(orgAddress.Street, Is.EqualTo("NewOrgStreet"));
            Assert.That(orgAddress.Street2, Is.EqualTo("NewOrgStreet2"));
            Assert.That(orgAddress.City, Is.EqualTo("NewOrgCity"));
            Assert.That(orgAddress.State, Is.EqualTo("NewOrgState"));
            Assert.That(orgAddress.Zip, Is.EqualTo("NewOrgZip"));
            Assert.That(orgAddress.Type, Is.EqualTo("NewOrgType"));
            Assert.That(orgAddress.Country, Is.EqualTo("NewOrgCountry"));
        });
        var mailAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Mailing");
        Assert.That(mailAddress, Is.Null);
    }

    [Test]
    public void SmoContactRequest_AsRegistration_ShouldMapDataWithMailAddress()
    {
        // Arrange
        var input = new SmoContactRequest
        {
            Name = "NewTest",
            Email = "<EMAIL>",
            County = "Sacramento",
            IsSameAsOrganizationAddress = true,
            OrganizationAddress = null,
            MailingAddress = new AddressDto
            {
                Street = "NewMailingStreet",
                Street2 = "NewMailingStreet2",
                City = "NewMailingCity",
                State = "NewMailingState",
                Zip = "NewMailingZip",
                Type = "NewMailingType",
                Country = "NewMailingCountry",
                Purpose = "Mailing",
            },
            PhoneNumber = null,
            FaxNumber = null,
        };
        var existing = new SlateMailerOrganization
        {
            Name = "OldName",
            Email = "<EMAIL>",
            County = "Alameda",
            IsSameAsCandidateAddress = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateSlateMailerOrganization(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("NewTest"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.County, Is.EqualTo("Sacramento"));
            Assert.That(output.IsSameAsCandidateAddress, Is.EqualTo(true));
        });
        Assert.That(output.AddressList, Is.Not.Null);
        var orgAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Organization");
        Assert.That(orgAddress, Is.Null);
        var mailAddress = output.AddressList.Addresses.FirstOrDefault(x => x.Purpose == "Mailing");
        Assert.That(mailAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(mailAddress.Street, Is.EqualTo("NewMailingStreet"));
            Assert.That(mailAddress.Street2, Is.EqualTo("NewMailingStreet2"));
            Assert.That(mailAddress.City, Is.EqualTo("NewMailingCity"));
            Assert.That(mailAddress.State, Is.EqualTo("NewMailingState"));
            Assert.That(mailAddress.Zip, Is.EqualTo("NewMailingZip"));
            Assert.That(mailAddress.Type, Is.EqualTo("NewMailingType"));
            Assert.That(mailAddress.Country, Is.EqualTo("NewMailingCountry"));
        });
    }


    [Test]
    public void SmoContactRequest_AsRegistration_ShouldMapDataWithBothPhoneNotExisting()
    {
        // Arrange
        var input = new SmoContactRequest
        {
            PhoneNumber = new() { CountryCode = "replace_with_ref", SelectedCountry = 24, Number = "1112223333", Extension = "222" },
            FaxNumber = new() { CountryCode = "", SelectedCountry = null, Number = "4445556666", Extension = "222" },
        };
        var existing = new SlateMailerOrganization
        {
            Name = "OldName",
            Email = "<EMAIL>",
            County = "Alameda",
            IsSameAsCandidateAddress = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        _referenceDataSvc.GetCountryById(24).Returns(new Country() { Id = 24, CountryName = "Test Country", PhoneCountryCode = "+42" });

        // Act
        var output = _registrationModelMapper.UpdateSlateMailerOrganization(existing, input);

        // Assert
        Assert.That(output.PhoneNumberList, Is.Not.Null);
        var homeNumber = output.PhoneNumberList.PhoneNumbers.FirstOrDefault(x => x.Type == "Home");
        Assert.That(homeNumber, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(homeNumber.Number, Is.EqualTo("1112223333"));
            Assert.That(homeNumber.Extension, Is.EqualTo("222"));
            Assert.That(homeNumber.CountryId, Is.EqualTo(24));
            Assert.That(homeNumber.CountryCode, Is.EqualTo("+42"));

        });
        var faxAddress = output.PhoneNumberList.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax");
        Assert.That(faxAddress, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(faxAddress.Number, Is.EqualTo("4445556666"));
            Assert.That(faxAddress.Extension, Is.EqualTo("222"));
            Assert.That(faxAddress.CountryId, Is.EqualTo(null));
            Assert.That(faxAddress.CountryCode, Is.EqualTo(null));
        });
    }

    [Test]
    public void SmoContactRequest_AsRegistration_ShouldMapDataWithBothPhoneExisting()
    {
        // Arrange
        var input = new SmoContactRequest
        {
            PhoneNumber = new() { CountryCode = "replace_with_ref", SelectedCountry = 24, Number = "1112223333", Extension = "222" },
            FaxNumber = new() { CountryCode = null, SelectedCountry = -1, Number = "4445556666", Extension = "222" },
        };
        var existing = new SlateMailerOrganization
        {
            Name = "OldName",
            Email = "<EMAIL>",
            County = "Alameda",
            IsSameAsCandidateAddress = false,
            StatusId = RegistrationStatus.Draft.Id,
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
                {
                    new()
                    {
                        Type = "Home",
                        Number = "0000000000",
                        Extension = "111",
                        InternationalNumber = false,
                        CountryCode = "+1",
                    },
                    new()
                    {
                        Type = "Fax",
                        Number = "0000000000",
                        Extension = "111",
                        InternationalNumber = false,
                        CountryCode = "+1",
                    }
                }
            }
        };

        _referenceDataSvc.GetCountryById(24).Returns(new Country() { Id = 24, CountryName = "Test Country", PhoneCountryCode = "+42" });

        // Act
        var output = _registrationModelMapper.UpdateSlateMailerOrganization(existing, input);

        // Assert
        Assert.That(output.PhoneNumberList, Is.Not.Null);
        var homeNumber = output.PhoneNumberList.PhoneNumbers.FirstOrDefault(x => x.Type == "Home");
        Assert.That(homeNumber, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(homeNumber.Number, Is.EqualTo("1112223333"));
            Assert.That(homeNumber.Extension, Is.EqualTo("222"));
            Assert.That(homeNumber.CountryId, Is.EqualTo(24));
            Assert.That(homeNumber.CountryCode, Is.EqualTo("+42"));
        });
        var faxNumber = output.PhoneNumberList.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax");
        Assert.That(faxNumber, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(faxNumber.Number, Is.EqualTo("4445556666"));
            Assert.That(faxNumber.Extension, Is.EqualTo("222"));
            Assert.That(faxNumber.CountryId, Is.EqualTo(null));
            Assert.That(faxNumber.CountryCode, Is.EqualTo("+1"));
        });
    }
    #endregion

    #region Lobbyist Registration
    [Test]
    public void AsRegistration_ShouldMapToLobbyistRegistration()
    {
        // Arrange
        var expectedPhoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "1234567890", Type = RegistrationConstants.PhoneNumber.TypeHome },
                new() { Number = "0987654321", Type = RegistrationConstants.PhoneNumber.TypeFax }
            },
        };

        var expectedAddressList = new AddressList
        {
            Addresses = new List<Address>
            {
                new()
                {
                    Street = "LobbyistStreet",
                    Street2 = "LobbyistStreet2",
                    City = "LobbyistCity",
                    State = "LobbyistState",
                    Zip = "LobbyistZip",
                    Type = "LobbyistType",
                    Country = "LobbyistCountry",
                    Purpose = "Business",
                },
                new()
                {
                    Street = "MailingStreet",
                    Street2 = "MailingStreet2",
                    City = "MailingCity",
                    State = "MailingState",
                    Zip = "MailingZip",
                    Type = "MailingType",
                    Country = "MailingCountry",
                    Purpose = "Mailing",
                },
            },
        };

        // Act
        var model = _registrationModelMapper.MapLobbyistRegistrationRequestToModel(_lobbyistRegRequest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(model.EmployerName, Is.EqualTo(""));

            for (int i = 0; i < expectedPhoneNumberList.PhoneNumbers.Count; i++)
            {
                Assert.That(model.PhoneNumberList!.PhoneNumbers[i].Number, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Number));
                Assert.That(model.PhoneNumberList.PhoneNumbers[i].Type, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Type));
            }

            for (int i = 0; i < expectedAddressList.Addresses.Count; i++)
            {
                var actual = model.AddressList!.Addresses[i];
                var expected = expectedAddressList.Addresses[i];

                Assert.That(actual.Street, Is.EqualTo(expected.Street));
                Assert.That(actual.Street2, Is.EqualTo(expected.Street2));
                Assert.That(actual.City, Is.EqualTo(expected.City));
                Assert.That(actual.State, Is.EqualTo(expected.State));
                Assert.That(actual.Zip, Is.EqualTo(expected.Zip));
                Assert.That(actual.Type, Is.EqualTo(expected.Type));
                Assert.That(actual.Country, Is.EqualTo(expected.Country));
                Assert.That(actual.Purpose, Is.EqualTo(expected.Purpose));
            }
        });
    }

    [Test]
    public void AsRegistrationWithExisting_ShouldMapToLobbyistRegistration()
    {
        // Arrange
        var expectedPhoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "1234567890", Type = RegistrationConstants.PhoneNumber.TypeHome },
                new() { Number = "0987654321", Type = RegistrationConstants.PhoneNumber.TypeFax }
            },
        };

        var expectedAddressList = new AddressList
        {
            Addresses = new List<Address>
            {
                new()
                {
                    Street = "LobbyistStreet",
                    Street2 = "LobbyistStreet2",
                    City = "LobbyistCity",
                    State = "LobbyistState",
                    Zip = "LobbyistZip",
                    Type = "LobbyistType",
                    Country = "LobbyistCountry",
                    Purpose = "Organization",
                },
                new()
                {
                    Street = "MailingStreet",
                    Street2 = "MailingStreet2",
                    City = "MailingCity",
                    State = "MailingState",
                    Zip = "MailingZip",
                    Type = "MailingType",
                    Country = "MailingCountry",
                    Purpose = "Mailing",
                },
            },
        };

        var existingLobbyist = new Lobbyist
        {
            Id = 1,
            Name = "Test Lobbyist",
            Email = null,
            StatusId = 2,
            FilerId = 3,
            EmployerName = "Test Employer",
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            AddressListId = null,
            PhoneNumberListId = null,
            AddressList = expectedAddressList,
            PhoneNumberList = null
        };

        // Act
        var model = _registrationModelMapper.UpdateLobbyist(_lobbyistRegRequest, existingLobbyist);

        Assert.That(model, Is.InstanceOf<Lobbyist>());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));

            for (int i = 0; i < expectedPhoneNumberList.PhoneNumbers.Count; i++)
            {
                Assert.That(model.PhoneNumberList!.PhoneNumbers[i].Number, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Number));
                Assert.That(model.PhoneNumberList.PhoneNumbers[i].Type, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Type));
            }

            for (int i = 0; i < expectedAddressList.Addresses.Count; i++)
            {
                var actual = model.AddressList!.Addresses[i];
                var expected = expectedAddressList.Addresses[i];

                Assert.That(actual.Street, Is.EqualTo(expected.Street));
                Assert.That(actual.Street2, Is.EqualTo(expected.Street2));
                Assert.That(actual.City, Is.EqualTo(expected.City));
                Assert.That(actual.State, Is.EqualTo(expected.State));
                Assert.That(actual.Zip, Is.EqualTo(expected.Zip));
                Assert.That(actual.Type, Is.EqualTo(expected.Type));
                Assert.That(actual.Country, Is.EqualTo(expected.Country));
                Assert.That(actual.Purpose, Is.EqualTo(expected.Purpose));
            }
        });
    }

    [Test]
    public void AsRegistrationWithExisting_ShouldMapToLobbyistRegistration_NoAddress()
    {
        // Arrange
        var expectedPhoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "1234567890", Type = RegistrationConstants.PhoneNumber.TypeHome },
                new() { Number = "0987654321", Type = RegistrationConstants.PhoneNumber.TypeFax }
            },
        };

        var existingLobbyist = new Lobbyist
        {
            Id = 1,
            Name = "Test Lobbyist",
            Email = null,
            StatusId = 2,
            FilerId = 3,
            EmployerName = "Test Employer",
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            AddressListId = null,
            PhoneNumberListId = null,
            AddressList = new AddressList(),
            PhoneNumberList = null
        };

        // Act
        var lobbyistRegRequest = _lobbyistRegRequest;
        lobbyistRegRequest.BusinessAddress = null;
        lobbyistRegRequest.MailingAddress = null;
        var model = _registrationModelMapper.UpdateLobbyist(_lobbyistRegRequest, existingLobbyist);

        Assert.That(model, Is.InstanceOf<Lobbyist>());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));

            for (int i = 0; i < expectedPhoneNumberList.PhoneNumbers.Count; i++)
            {
                Assert.That(model.PhoneNumberList!.PhoneNumbers[i].Number, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Number));
                Assert.That(model.PhoneNumberList.PhoneNumbers[i].Type, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Type));
            }

            Assert.That(model.AddressList?.Addresses, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public void AsRegistrationWithExisting_ShouldMapToLobbyistRegistration_NullAddressFields()
    {
        // Arrange
        var expectedPhoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "1234567890", Type = RegistrationConstants.PhoneNumber.TypeHome },
                new() { Number = "0987654321", Type = RegistrationConstants.PhoneNumber.TypeFax }
            },
        };

        var existingAddressList = new AddressList
        {
            Addresses = new List<Address>
            {
                new()
                {
                    Street = "LobbyistStreet",
                    Street2 = "LobbyistStreet2",
                    City = "LobbyistCity",
                    State = "LobbyistState",
                    Zip = "LobbyistZip",
                    Type = "LobbyistType",
                    Country = "LobbyistCountry",
                    Purpose = "Organization",
                },
                new()
                {
                    Street = "MailingStreet",
                    Street2 = "MailingStreet2",
                    City = "MailingCity",
                    State = "MailingState",
                    Zip = "MailingZip",
                    Type = "MailingType",
                    Country = "MailingCountry",
                    Purpose = "Mailing",
                },
            },
        };

        var existingLobbyist = new Lobbyist
        {
            Id = 1,
            Name = "Test Lobbyist",
            Email = null,
            StatusId = 2,
            FilerId = 3,
            EmployerName = "Test Employer",
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            AddressListId = null,
            PhoneNumberListId = null,
            AddressList = existingAddressList,
            PhoneNumberList = null
        };

        // Act
        var lobbyistRegRequest = _lobbyistRegRequest;
        lobbyistRegRequest.BusinessAddress = new AddressDto();
        lobbyistRegRequest.MailingAddress = new AddressDto();
        var model = _registrationModelMapper.UpdateLobbyist(_lobbyistRegRequest, existingLobbyist);

        Assert.That(model, Is.InstanceOf<Lobbyist>());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));

            for (int i = 0; i < expectedPhoneNumberList.PhoneNumbers.Count; i++)
            {
                Assert.That(model.PhoneNumberList!.PhoneNumbers[i].Number, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Number));
                Assert.That(model.PhoneNumberList.PhoneNumbers[i].Type, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Type));
            }

            Assert.That(model.AddressList?.Addresses, Has.Count.EqualTo(2));
        });
    }

    [Test]
    public void AsRegistrationWithExisting_ShouldMapToLobbyistRegistration_AddressesNotOrgOrMailing()
    {
        // Arrange
        var expectedPhoneNumberList = new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "1234567890", Type = RegistrationConstants.PhoneNumber.TypeHome },
                new() { Number = "0987654321", Type = RegistrationConstants.PhoneNumber.TypeFax }
            },
        };

        var existingAddressList = new AddressList
        {
            Addresses = new List<Address>
            {
                new()
                {
                    Street = "LobbyistStreet",
                    Street2 = "LobbyistStreet2",
                    City = "LobbyistCity",
                    State = "LobbyistState",
                    Zip = "LobbyistZip",
                    Type = "LobbyistType",
                    Country = "LobbyistCountry",
                    Purpose = "Business",
                },
                new()
                {
                    Street = "MailingStreet",
                    Street2 = "MailingStreet2",
                    City = "MailingCity",
                    State = "MailingState",
                    Zip = "MailingZip",
                    Type = "MailingType",
                    Country = "MailingCountry",
                    Purpose = "Donations",
                },
            },
        };

        var existingLobbyist = new Lobbyist
        {

            Id = 1,
            Name = "Test Lobbyist",
            Email = null,
            StatusId = 2,
            FilerId = 3,
            EmployerName = "Test Employer",
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            AddressListId = null,
            PhoneNumberListId = null,
            AddressList = existingAddressList,
            PhoneNumberList = null
        };

        // Act
        var lobbyistRegRequest = _lobbyistRegRequest;
        lobbyistRegRequest.BusinessAddress = new AddressDto();
        lobbyistRegRequest.MailingAddress = new AddressDto();
        var model = _registrationModelMapper.UpdateLobbyist(_lobbyistRegRequest, existingLobbyist);

        Assert.That(model, Is.InstanceOf<Lobbyist>());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));

            for (int i = 0; i < expectedPhoneNumberList.PhoneNumbers.Count; i++)
            {
                Assert.That(model.PhoneNumberList!.PhoneNumbers[i].Number, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Number));
                Assert.That(model.PhoneNumberList.PhoneNumbers[i].Type, Is.EqualTo(expectedPhoneNumberList.PhoneNumbers[i].Type));
            }

            Assert.That(model.AddressList?.Addresses, Has.Count.EqualTo(2));
        });
    }
    #endregion

    #region Lobbyist Employer
    [Test]
    public void LobbyistEmployerGeneralInfoRequest_AsRegistration_ShouldMapData()
    {
        // Arrange
        var input = new LobbyistEmployerGeneralInfoRequest
        {
            EmployerName = "NewTest",
            Email = "<EMAIL>",
            BusinessAddress = null,
            MailingAddress = null,
            PhoneNumber = null,
            FaxNumber = null,
            LegislativeSessionId = null,
            QualificationDate = null,
            IsLobbyingCoalition = true,
        };
        var existing = new LobbyistEmployer
        {
            Name = "OldName",
            Email = "<EMAIL>",
            IsLobbyingCoalition = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateLobbyistEmployerGeneralInfo(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("NewTest"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.IsLobbyingCoalition, Is.EqualTo(true));
        });
    }

    [Test]
    public void LobbyistEmployerGeneralInfoRequest_AsRegistration_WithObjects_ShouldMapData()
    {
        // Arrange
        var input = new LobbyistEmployerGeneralInfoRequest
        {
            EmployerName = "NewTest",
            Email = "<EMAIL>",
            BusinessAddress = new AddressDto
            {
                Street = "NewBusinessStreet",
                Street2 = "NewBusinessStreet2",
                City = "NewBusinessCity",
                State = "NewBusinessState",
                Zip = "NewBusinessZip",
                Type = "NewBusinessType",
                Country = "NewBusinessCountry",
                Purpose = "Business",
            },
            MailingAddress = new AddressDto
            {
                Street = "NewMailingStreet",
                Street2 = "NewMailingStreet2",
                City = "NewMailingCity",
                State = "NewMailingState",
                Zip = "NewMailingZip",
                Type = "NewMailingType",
                Country = "NewMailingCountry",
                Purpose = "Mailing",
            },
            PhoneNumber = new PhoneNumberDto() { Number = "1234567890", CountryCode = "+1" },
            FaxNumber = new PhoneNumberDto() { Number = "0987654321", CountryCode = "+1" },
            LegislativeSessionId = null,
            QualificationDate = null,
            IsLobbyingCoalition = true,
        };
        var existing = new LobbyistEmployer
        {
            Name = "OldName",
            Email = "<EMAIL>",
            IsLobbyingCoalition = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateLobbyistEmployerGeneralInfo(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("NewTest"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.AddressList, Is.Not.Null);
            Assert.That(output.PhoneNumberList, Is.Not.Null);
            Assert.That(output.IsLobbyingCoalition, Is.EqualTo(true));
        });
    }

    [Test]
    public void AsRegistration_ShouldMapToLobbyistEmployerGeneralInfoRequest()
    {
        // Arrange
        var input = new LobbyistEmployerGeneralInfoRequest
        {
            EmployerName = "NewTest",
            Email = "<EMAIL>",
            BusinessAddress = new AddressDto
            {
                Street = "NewBusinessStreet",
                Street2 = "NewBusinessStreet2",
                City = "NewBusinessCity",
                State = "NewBusinessState",
                Zip = "NewBusinessZip",
                Type = "NewBusinessType",
                Country = "NewBusinessCountry",
                Purpose = "Business",
            },
            MailingAddress = new AddressDto
            {
                Street = "NewMailingStreet",
                Street2 = "NewMailingStreet2",
                City = "NewMailingCity",
                State = "NewMailingState",
                Zip = "NewMailingZip",
                Type = "NewMailingType",
                Country = "NewMailingCountry",
                Purpose = "Mailing",
            },
            PhoneNumber = null,
            FaxNumber = null,
            LegislativeSessionId = null,
            QualificationDate = null,
            IsLobbyingCoalition = true,
        };

        // Act
        var model = _registrationModelMapper.MapLobbyistEmployerGeneralInfoToModel(input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Name, Is.EqualTo("NewTest"));
            Assert.That(model.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(model.AddressList, Is.Not.Null);
            Assert.That(model.PhoneNumberList, Is.Not.Null);
            Assert.That(model.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(model.IsLobbyingCoalition, Is.EqualTo(true));
        });
    }

    [Test]
    public void LobbyistEmployerStateAgenciesRequestt_AsRegistration_ShouldMapData()
    {
        // Arrange
        var input = new LobbyistEmployerStateAgenciesRequest
        {
            Agencies = new()
            {
                new RegistrationAgencyDto{ AgencyId = 1, AgencyName = "dummy agency", RegistrationId = 1 },
                new RegistrationAgencyDto{ AgencyId = 2, AgencyName = "dummy agency 2", RegistrationId = 1 }
            },
            IsLobbyingStateLegislature = true,
        };
        var existing = new LobbyistEmployer
        {
            Name = "OldName",
            Email = "<EMAIL>",
            RegistrationAgencies = [],
            StateLegislatureLobbying = false,
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateLobbyistEmployerStateAgencies(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("OldName"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.RegistrationAgencies, Is.Not.Null);
            Assert.That(output.StateLegislatureLobbying, Is.EqualTo(true));
        });
    }

    [Test]
    public void LobbyistEmployerLobbyingInterestsRequest_AsRegistration_ShouldMapData()
    {
        // Arrange
        var input = new LobbyistEmployerLobbyingInterestsRequest
        {
            FilingInterestsDescription = "Individual"
        };
        var existing = new LobbyistEmployer
        {
            Name = "OldName",
            Email = "<EMAIL>",
            LobbyingInterest = new()
            { Name = "Industry" },
            StatusId = RegistrationStatus.Draft.Id,
        };

        // Act
        var output = _registrationModelMapper.UpdateLobbyistEmployerLobbyingInterests(existing, input);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(output.Name, Is.EqualTo("OldName"));
            Assert.That(output.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(output.LobbyingInterest?.Name, Is.EqualTo("Individual"));
        });
    }
    #endregion
}
