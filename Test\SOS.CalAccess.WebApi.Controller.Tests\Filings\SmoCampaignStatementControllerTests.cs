using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.WebApi.Filings;

namespace SOS.CalAccess.WebApi.Tests.Filings;
[TestFixture]
public class SmoCampaignStatementControllerTests
{
    private ISmoCampaignStatementSvc _smoCampaignStatementSvcMock;
    private SmoCampaignStatementController _controller;
    private IAuthorizationService _authorizationServiceMock;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _smoCampaignStatementSvcMock = Substitute.For<ISmoCampaignStatementSvc>();
        _authorizationServiceMock = Substitute.For<IAuthorizationService>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _controller = new SmoCampaignStatementController(_authorizationServiceMock, _smoCampaignStatementSvcMock);
    }

    [Test]
    public async Task GetSmoGeneralInformationById_ValidIds_NoError()
    {
        // Arrange
        long filerId = 1;
        long registrationId = 1;
        var dto = new SmoGeneralInformationResponseDto(
            new SmoRegistrationResponseDto()
            {
                Id = registrationId,
                FilerId = filerId
            },
            null,
            new SmoRegistrationContactDto());

        _ = _smoCampaignStatementSvcMock.GetSmoGeneralInformationById(Arg.Any<long>()).Returns(dto);

        // Act
        SmoGeneralInformationResponseDto result = await _controller.GetSmoGeneralInformationById(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RegistrationDetail, Is.Not.Null);
        Assert.That(result.RegistrationDetail.Id, Is.EqualTo(registrationId));
    }

    [Test]
    public async Task CreateSmoCampaignStatement_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        long filerId = 1;
        long filingPeriodId = 2;
        long filingId = 1;
        var request = new SmoCampaignStatementRequest
        {
            FilerId = filerId,
            FilingPeriodId = filingPeriodId,
        };

        _ = _smoCampaignStatementSvcMock.CreateSmoCampaignStatementAsync(Arg.Any<SmoCampaignStatementRequest>()).Returns(filingId);

        // Act
        var result = await _controller.CreateSmoCampaignStatementAsync(request);

        // Assert
        Assert.That(result, Is.EqualTo(filingId));
    }

    [Test]
    public void UpdateSmoCampaignStatementAsync_ValidRequest_ShouldNotThrowError()
    {
        // Arrange
        var id = 1;
        var request = new SmoCampaignStatementRequest
        {
            FilingPeriodId = 1,
        };

        _ = _smoCampaignStatementSvcMock.UpdateSmoCampaignStatementAsync(Arg.Any<long>(), Arg.Any<SmoCampaignStatementRequest>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.UpdateSmoCampaignStatementAsync(id, request));
        _smoCampaignStatementSvcMock.Received().UpdateSmoCampaignStatementAsync(Arg.Any<long>(), Arg.Any<SmoCampaignStatementRequest>());
    }

    [Test]
    public async Task GetSmoFilingSummaryById_ValidId_NoError()
    {
        // Arrange
        long filerId = 1;
        long filingId = 1;
        List<FilingSummaryResponseDto> list =
        [
            GenerateFilingSummaryResponseDtoDto(filingId, 5, 100),
            GenerateFilingSummaryResponseDtoDto(filingId, 6, 200),
        ];

        _ = _smoCampaignStatementSvcMock.GetSmoFilingSummaryByFilingId(filerId).Returns(list);

        // Act
        var result = await _controller.GetSmoFilingSummaryByFilingId(filerId);

        // Assert
        Assert.That(result, Is.Not.Empty);
        Assert.That(result, Has.Count.EqualTo(list.Count));
    }

    [Test]
    public async Task GetUnreportedFilingPeriodsAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var response = new List<FilingPeriodResponseDto>
        {
            new()
            {
                Id = 1,
            }
        };

        _ = _smoCampaignStatementSvcMock.GetUnreportedFilingPeriodsByFilerAsync(id).Returns(Task.FromResult(response));

        // Act
        var result = await _controller.GetUnreportedFilingPeriodsByFilerAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<FilingPeriodResponseDto>>());
        });
    }

    [Test]
    public async Task GetSmoCampaignStatementOverviewAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var response = new FilingOverviewResponseDto
        {
            Id = id,
        };

        _ = _smoCampaignStatementSvcMock.GetSmoCampaignStatementOverviewAsync(Arg.Any<long>()).Returns(Task.FromResult(response));

        // Act
        var result = await _controller.GetSmoCampaignStatementOverviewAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<FilingOverviewResponseDto>());
        });
    }

    [Test]
    public void MarkFilingSummaryAsNothingToReportAsync_ValidRequest_ShouldNotThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;

        _ = _smoCampaignStatementSvcMock.MarkFilingSummaryAsNothingToReportAsync(Arg.Any<long>(), Arg.Any<long>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.MarkFilingSummaryAsNothingToReportAsync(id, filingSummaryId));
        _smoCampaignStatementSvcMock.Received().MarkFilingSummaryAsNothingToReportAsync(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task GetTransactionSummaryAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;
        var response = new TransactionSummaryResponseDto
        {
            FilerId = 1,
            TransactionResponseDtos = new List<TransactionDetailResponseDto>
            {
                new()
                {
                    Id = 1,
                    Amount = 1,
                    TransactionDate = _dateNow,
                }
            },
            SummaryResponseDto = new FilingSummaryResponseDto
            {
                Id = 1,
            }
        };

        _ = _smoCampaignStatementSvcMock.GetTransactionSummaryAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(response));

        // Act
        var result = await _controller.GetTransactionSummaryAsync(id, filingSummaryId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionSummaryResponseDto>());
        });
    }

    [Test]
    public void UpdateFilingSummaryAsync_ValidRequest_ShouldNotThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;
        var request = new SmoCampaignStatementFilingSummaryRequest
        {
            UnitemizedAmount = 100
        };

        _ = _smoCampaignStatementSvcMock.UpdateFilingSummaryAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<SmoCampaignStatementFilingSummaryRequest>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.UpdateFilingSummaryAsync(id, filingSummaryId, request));
        _smoCampaignStatementSvcMock.Received().UpdateFilingSummaryAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<SmoCampaignStatementFilingSummaryRequest>());
    }

    #region GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync
    [Test]
    public async Task GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var response = new List<CandidateOrMeasureNotListedPaymentReceivedResponseDto>
        {
            new ()
            {
                Id = 1,
                CandidateOrMeasure = "Candidate",
                Jurisdiction = "Jurisdiction",
                OfficeOrMeasure = "Office",
                Position = "Support",
            },
            new ()
            {
                Id = 2,
                CandidateOrMeasure = "Measure",
                Jurisdiction = "Jurisdiction",
                OfficeOrMeasure = "Measure",
                Position = "Oppose",
            }
        };

        _ = _smoCampaignStatementSvcMock.GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync(Arg.Any<long>()).Returns(Task.FromResult(response));

        // Act
        var result = await _controller.GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<CandidateOrMeasureNotListedPaymentReceivedResponseDto>>());
        });
    }
    #endregion

    #region AttestAsync 
    [Test]
    public async Task AttestAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var response = new ValidatedSmoCampaignStatementResponseDto();

        _ = _smoCampaignStatementSvcMock.AttestStatementAsync(1).Returns(response);

        // Act
        var result = await _controller.AttestStatementAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ValidatedSmoCampaignStatementResponseDto>());
        });
    }

    #endregion

    #region SendForAttestationAsync
    [Test]
    public async Task SendForAttestationAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long> { }
        };

        var response = new ValidatedSmoCampaignStatementResponseDto();

        _ = _smoCampaignStatementSvcMock.SendForAttestationAsync(Arg.Any<long>(), Arg.Any<SmoRegistrationSendForAttestationRequest>()).Returns(Task.FromResult(response));

        // Act
        var result = await _controller.SendForAttestationAsync(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ValidatedSmoCampaignStatementResponseDto>());
        });
    }
    #endregion

    #region GetSmoCampaignStatementResponsibleOfficerContactsAsync
    [Test]
    public async Task GetSmoCampaignStatementResponsibleOfficerContactsAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;


        var response = new List<SmoRegistrationContactDto>() { };

        _ = _smoCampaignStatementSvcMock.GetSmoCampaignStatementResponsibleOfficerContactsAsync(Arg.Any<long>()).Returns(response);

        // Act
        var result = await _controller.GetSmoCampaignStatementResponsibleOfficerContactsAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<SmoRegistrationContactDto>>());
        });
    }
    #endregion

    #region GetSmoCampaignStatementPendingItemsAsync
    [Test]
    public async Task GetSmoCampaignStatementPendingItemsAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;


        var response = new List<PendingItemDto>() { };

        _ = _smoCampaignStatementSvcMock.GetSmoCampaignStatementPendingItemsAsync(Arg.Any<long>()).Returns(response);

        // Act
        var result = await _controller.GetSmoCampaignStatementPendingItemsAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<PendingItemDto>>());
        });
    }
    #endregion

    #region GetSmoCampaignStatementAttestationAsync
    [Test]
    public async Task GetSmoCampaignStatementAttestationAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;


        var response = new SmoCampaignStatementAttestationResponseDto() { };

        _ = _smoCampaignStatementSvcMock.GetSmoCampaignStatementAttestationAsync(Arg.Any<long>()).Returns(response);

        // Act
        var result = await _controller.GetSmoCampaignStatementAttestationAsync(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementAttestationResponseDto>());
        });
    }
    #endregion

    #region CreateSmoCampaignStatementTransactionAsync
    [Test]
    public async Task CreateSmoCampaignStatementTransactionAsync_ValidRequest_ShouldExecuteSuccessfullyAndReturnResponse()
    {
        // Arrange
        var id = 1;
        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 1m,
        };
        var response = new TransactionResponseDto
        {
            Id = id,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _ = _smoCampaignStatementSvcMock
            .CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<TransactionDetailRequest>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.CreateSmoCampaignStatementTransactionAsync(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(response));
        });
    }
    #endregion

    #region UpdateSmoCampaignStatementTransactionAsync
    [Test]
    public async Task UpdateSmoCampaignStatementTransactionAsync_ValidRequest_ShouldExecuteSuccessfullyAndReturnResponse()
    {
        // Arrange
        var filingId = 1;
        var transactionId = 1;
        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 1m,
        };
        var response = new TransactionResponseDto
        {
            Id = transactionId,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _ = _smoCampaignStatementSvcMock
            .UpdateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<TransactionDetailRequest>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(response));
        });
    }
    #endregion

    #region GetSmoCampaignStatementTransactionAsync
    [Test]
    public async Task GetSmoCampaignStatementTransactionAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var filingId = 1;
        var transactionId = 1;
        var transactionType = TransactionType.PaymentMade.Name;
        var response = new TransactionDetailResponseDto
        {
            Id = transactionId,
        };

        _ = _smoCampaignStatementSvcMock
            .GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<string>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.GetSmoCampaignStatementTransactionAsync(filingId, transactionId, transactionType);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionDetailResponseDto>());
            Assert.That(result.Id, Is.EqualTo(transactionId));
        });
    }
    #endregion

    [Test]
    public async Task GetSmoCampaignStatementPaymentReceivedTransactionById_Valid_ReturnsTransaction()
    {
        // Arrange
        DisclosureWithoutPaymentReceivedDto disclosure = new()
        {
            Id = 1L,
            Position = "Support",
        };

        _ = _smoCampaignStatementSvcMock.GetDisclosureWithoutPaymentReceivedByIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(disclosure);

        // Act
        var result = await _controller.GetDisclosureWithoutPaymentReceivedByIdAsync(1L, 1L);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(1L));
    }

    [Test]
    public async Task CreateDisclosureWithoutPaymentReceivedAsync_Valid_ReturnsRecord()
    {
        // Arrange
        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
        };
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _ = _smoCampaignStatementSvcMock
            .CreateDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<DisclosureWithoutPaymentReceivedDto>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.CreateDisclosureWithoutPaymentReceivedAsync(1L, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(response));
        });
    }

    [Test]
    public async Task UpdateDisclosureWithoutPaymentReceivedAsync_Valid_ReturnsTransaction()
    {
        // Arrange
        // Arrange
        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Id = 1L,
            Position = "Support",
        };
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _ = _smoCampaignStatementSvcMock
            .UpdateDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<DisclosureWithoutPaymentReceivedDto>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.UpdateDisclosureWithoutPaymentReceivedAsync(1L, 1L, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(response));
        });
    }

    #region DeleteDisclosureWithoutPaymentReceivedAsync
    [Test]
    public void DeleteTransactionAsync_ValidRequest_ShouldNotThrowError()
    {
        // Arrange
        var id = 1;
        var recordId = 1;

        _ = _smoCampaignStatementSvcMock.DeleteDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<long>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.DeleteDisclosureWithoutPaymentReceivedAsync(id, recordId));
        _smoCampaignStatementSvcMock.Received().DeleteDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<long>());
    }
    #endregion

    #region UpdateAmendmentExplanationAsync
    [Test]
    public void UpdateAmendmentExplanationAsync_ValidRequest_ShouldNotThrowError()
    {
        // Arrange
        var id = 1;
        var request = new FilingSummaryAmendmentExplanationRequest
        {
            AmendmentExplanation = "string",
        };

        _ = _smoCampaignStatementSvcMock.UpdateAmendmentExplanationAsync(Arg.Any<long>(), Arg.Any<FilingSummaryAmendmentExplanationRequest>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.UpdateAmendmentExplanationAsync(id, request));
        _smoCampaignStatementSvcMock.Received().UpdateAmendmentExplanationAsync(Arg.Any<long>(), Arg.Any<FilingSummaryAmendmentExplanationRequest>());
    }
    #endregion

    #region InitializeSmoCampaignStatementAmendmentAsync
    [Test]
    public async Task InitializeSmoCampaignStatementAmendmentAsync_ValidRequest_ShouldReturnNewCreatedId()
    {
        // Arrange
        var id = 1L;
        var expected = 2L;

        _ = _smoCampaignStatementSvcMock.InitializeSmoCampaignStatementAmendmentAsync(Arg.Any<long>()).Returns(Task.FromResult(expected));

        // Act 
        var result = await _controller.InitializeSmoCampaignStatementAmendmentAsync(id);

        // Assert
        Assert.That(result, Is.EqualTo(expected));
    }
    #endregion

    #region ValidateDisclosurePaymentReceivedAsync
    [Test]
    public async Task ValidateDisclosurePaymentReceivedAsync_ValidRequest_ShouldNotThrowError()
    {
        // Arrange
        PaymentReceivedValidationRequestDto request = new()
        {
            Position = "Support",
        };
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _ = _smoCampaignStatementSvcMock
            .ValidatePaymentReceivedAsync(Arg.Any<long>(), Arg.Any<PaymentReceivedValidationRequestDto>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.ValidatePaymentReceivedAsync(1L, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(1L));
        });
    }
    #endregion

    #region IsSmoCampaignStatementAmendmentAsync
    [Test]
    public async Task IsSmoCampaignStatementAmendmentAsync_ValidRequest_ShouldReturnFlag()
    {
        // Arrange
        var id = 1L;
        var expected = true;

        _ = _smoCampaignStatementSvcMock.IsSmoCampaignStatementAmendmentAsync(Arg.Any<long>()).Returns(Task.FromResult(expected));

        // Act 
        var result = await _controller.IsSmoCampaignStatementAmendmentAsync(id);

        // Assert
        Assert.That(result, Is.EqualTo(expected));
    }
    #endregion

    #region ValidatePersonReceivingOfficerAsync
    [Test]
    public async Task ValidatePersonReceivingOfficerAsync_ValidRequest_ShouldReturnValidationResult()
    {
        // Arrange
        var id = 1L;
        var request = new PersonReceiving1000ValidationRequest
        {
            OfficerId = 1L,
        };
        var expected = new TransactionResponseDto
        {
            ValidationErrors = new List<WorkFlowError>(),
            Valid = true,
        };

        _ = _smoCampaignStatementSvcMock
            .ValidatePersonReceivingOfficerAsync(Arg.Any<long>(), Arg.Any<PersonReceiving1000ValidationRequest>())
            .Returns(Task.FromResult(expected));

        // Act 
        var result = await _controller.ValidatePersonReceivingOfficerAsync(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(expected.Valid));
        });
    }
    #endregion

    #region ValidateCandidateOrMeasureNotListedP1Async
    [Test]
    public async Task ValidateCandidateOrMeasureNotListedP1Async_ValidRequest_ShouldReturnValidationResult()
    {
        // Arrange
        var id = 1L;
        var request = new CandidateOrMeasureNotListedValidationRequest
        {
            CandidateOrMeasure = "Candidate",
            Jurisdiction = "Local",
            Position = "Support"
        };
        var expected = new TransactionResponseDto
        {
            ValidationErrors = new List<WorkFlowError>(),
            Valid = true,
        };

        _ = _smoCampaignStatementSvcMock
            .ValidateCandidateOrMeasureNotListedP1Async(Arg.Any<long>(), Arg.Any<CandidateOrMeasureNotListedValidationRequest>())
            .Returns(Task.FromResult(expected));

        // Act 
        var result = await _controller.ValidateCandidateOrMeasureNotListedP1Async(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(expected.Valid));
        });
    }
    #endregion

    #region GetPaymentReceivedCumulativeAmountAsync
    [Test]
    public async Task GetPaymentReceivedCumulativeAmountAsync_ValidRequest_ReturnResult()
    {
        // Arrange
        var filingId = 1;
        var request = new PaymentReceivedRequest
        {
            Jurisdiction = "State",
            Position = "Support",
            Amount = 100,
            StanceOnCandidate = new()
            {
                CandidateId = 3,
            },
        };
        var response = new PaymentsReceivedCumulativeAmountDto(1L, (decimal)100.00, (decimal)200.00D);

        _ = _smoCampaignStatementSvcMock
            .GetPaymentReceivedCumulativeAmountAsync(Arg.Any<long>(), Arg.Any<PaymentReceivedRequest>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _controller.GetPaymentReceivedCumulativeAmountAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(response));
        });

    }

    #endregion

    #region Private
    private static FilingSummaryResponseDto GenerateFilingSummaryResponseDtoDto(long filingId, long type, decimal amount)
    {
        return new(new()
        {
            FilingId = filingId,
            FilingSummaryTypeId = type,
            PeriodAmount = amount,
            ToDateAmount = amount,
        });
    }

    #endregion
}
