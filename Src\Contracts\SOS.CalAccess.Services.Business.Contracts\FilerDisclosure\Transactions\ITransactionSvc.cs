using Refit;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.OtherInfluencePayments;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// This interface defines the contract for the Transaction Service. The service is responsible for managing transactions, including creating, updating, and associating transactions with filings.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing and manipulating transaction data.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The business function of this service is to handle the lifecycle of transactions, including creation, modification, and association with filings.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FD-01: Enter an Activity Report</li>
/// <li>FD-02: Modify an Activity Report</li>
/// <li>FD-03: Upload an Activity Report</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | ITransactionRepository         | Create                         | Creates a new transaction    |
/// | ITransactionRepository         | Update                         | Updates an existing transaction |
/// | IFilingRepository              | AddTransaction                 | Adds a transaction to a filing |
/// | ITransactionRepository         | FindById                       | Retrieves a transaction by ID |
/// | ITransactionRepository         | GetAll                         | Retrieves all transactions   |
/// | ITransactionRepository         | GetAllByFilingId               | Retrieves all transactions for a specific filing |
/// | IContributionTypeRepository    | GetAll                         | Retrieves all contribution types |
/// | IMonetaryTypeRepository        | GetAll                         | Retrieves all monetary types |
/// | ITransactionTypeRepository     | GetAll                         | Retrieves all transaction types |
#endregion

/// <summary>
///  Interface for the Transaction Service.
/// </summary>
public interface ITransactionSvc
{
    /// <summary>
    /// Creates a new transaction.
    /// </summary>
    /// <param name="transaction">The transaction to create.</param>
    /// <returns>The ID of the created transaction.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreateTransaction()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return long"];
    /// ITransactionSvc >> Actor [label="return long"];
    /// \endmsc
    Task<long> CreateTransaction(Transaction transaction);

    /// <summary>
    /// Updates an existing transaction.
    /// </summary>
    /// <param name="transaction">The transaction to update.</param>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="UpdateTransaction()"];
    /// ITransactionSvc => ITransactionRepository [label="Update()"];
    /// ITransactionRepository >> ITransactionSvc [label="return"];
    /// ITransactionSvc >> Actor [label="return"];
    /// \endmsc
    Task UpdateTransaction(Transaction transaction);

    /// <summary>
    /// Adds a transaction to a filing.
    /// </summary>
    /// <param name="transactionId">The ID of the transaction to add.</param>
    /// <param name="filingId">The ID of the filing.</param>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => ITransactionSvc [label="AddTransactionToFiling()"];
    /// ITransactionSvc => IFilingRepository [label="AddTransaction()"];
    /// IFilingRepository >> ITransactionSvc [label="return"];
    /// ITransactionSvc >> Actor [label="return long"];
    /// \endmsc
    Task AddTransactionToFiling(long transactionId, long filingId);

    /// <summary>
    /// Adds attachments to a transaction.
    /// </summary>
    /// <param name="attachments">The attachments to add.</param>
    /// <param name="transactionId">The ID of the transaction.</param>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="AddAttachmentsToTransaction()"];
    /// ITransactionSvc => ITransactionRepository [label="AddAttachments()"];
    /// ITransactionRepository >> ITransactionSvc [label="return"];
    /// ITransactionSvc >> Actor [label="return"];
    /// \endmsc
    Task AddAttachmentsToTransaction(IEnumerable<Attachment> attachments, long transactionId);

    /// <summary>
    /// Removes attachments from a transaction.
    /// </summary>
    /// <param name="attachmentIds">The IDs of the attachments to remove.</param>
    /// <param name="transactionId">The ID of the transaction.</param>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="RemoveAttachmentsFromTransaction()"];
    /// ITransactionSvc => ITransactionRepository [label="RemoveAttachments()"];
    /// ITransactionRepository >> ITransactionSvc [label="return"];
    /// ITransactionSvc >> Actor [label="return"];
    /// \endmsc
    Task RemoveAttachmentsFromTransaction(IEnumerable<long> attachmentIds, long transactionId);

    /// <summary>
    /// Gets a transaction by ID.
    /// </summary>
    /// <param name="transactionId">The ID of the transaction to retrieve.</param>
    /// <returns>The transaction with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetTransaction()"];
    /// ITransactionSvc => ITransactionRepository [label="FindById()"];
    /// ITransactionRepository >> ITransactionSvc [label="return Transaction"];
    /// ITransactionSvc >> Actor [label="return Transaction"];
    /// \endmsc
    Task<Transaction?> GetTransaction(long transactionId);

    /// <summary>
    /// Gets all transactions.
    /// </summary>
    /// <returns>A collection of all transactions.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllTransactions()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAll()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<Transaction>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<Transaction>"];
    /// \endmsc
    Task<IEnumerable<Transaction>> GetAllTransactions();

    /// <summary>
    /// Gets all transactions for a specific filer.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>A collection of all transactions for the specified filer.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllTransactionsForFiler()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllByFilerId()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<Transaction>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<Transaction>"];
    /// \endmsc
    Task<IEnumerable<Transaction>> GetAllTransactionsForFiler(long filerId);

    /// <summary>
    /// Gets all transactions for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of all transactions for the specified filing.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllByFilingId()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<Transaction>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<Transaction>"];
    /// \endmsc
    Task<IEnumerable<Transaction>> GetAllTransactionsForFiling(long filingId);

    /// <summary>
    /// Gets all contribution types.
    /// </summary>
    /// <returns>A collection of all contribution types.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], IContributionTypeRepository [label="IContribution \n Type \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllContributionTypes()"];
    /// ITransactionSvc => IContributionTypeRepository [label="GetAll()"];
    /// IContributionTypeRepository >> ITransactionSvc [label="return IEnumerable<ContributionType>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<ContributionType>"];
    /// \endmsc
    Task<IEnumerable<ContributionType>> GetAllContributionTypes();

    /// <summary>
    /// Gets all monetary types.
    /// </summary>
    /// <returns>A collection of all monetary types.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], IMonetaryTypeRepository [label="IMonetary \n Type \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllMonetaryTypes()"];
    /// ITransactionSvc => IMonetaryTypeRepository [label="GetAll()"];
    /// IMonetaryTypeRepository >> ITransactionSvc [label="return IEnumerable<MonetaryType>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<MonetaryType>"];
    /// \endmsc
    Task<IEnumerable<MonetaryType>> GetAllMonetaryTypes();

    /// <summary>
    /// Gets all transaction types.
    /// </summary>
    /// <returns>A collection of all transaction types.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionTypeRepository [label="ITransaction \n Type \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllTransactionTypes()"];
    /// ITransactionSvc => ITransactionTypeRepository [label="GetAll()"];
    /// ITransactionTypeRepository >> ITransactionSvc [label="return IEnumerable<TransactionType>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<TransactionType>"];
    /// \endmsc
    Task<IEnumerable<TransactionType>> GetAllTransactionTypes();

    /// <summary>
    /// Creates a new lobbying campaign contribution transaction for lobbyist employer / coalition.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <param name="recipientName">The recipient name for non committee recipient.</param>
    /// <param name="amount">The contribution amount.</param>
    /// <param name="contributionDate">The contribution date.</param>
    /// <returns>The created transaction with the specified ID..</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreateLobbyingCampaignContribution()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return Transaction"];
    /// ITransactionSvc >> Actor [label="return Transaction"];
    /// \endmsc
    Task<Transaction> CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(long filerId, long filingId, string? recipientName, decimal? amount, DateTime contributionDate, bool? isCommittee, long? recipientCommitteeFilerId);

    /// <summary>
    /// Creates a new lobbying campaign contribution transaction.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <param name="recipientName">The recipient name for non committee recipient.</param>
    /// <param name="amount">The contribution amount.</param>
    /// <param name="contributionDate">The contribution date.</param>
    /// <returns>The created transaction with the specified ID..</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreateLobbyingCampaignContribution()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return Transaction"];
    /// ITransactionSvc >> Actor [label="return Transaction"];
    /// \endmsc
    Task<Transaction> CreateLobbyingCampaignContribution(long filerId, long filingId, string? recipientName, decimal? amount, DateTime contributionDate, bool? isCommittee, long? recipientCommitteeFilerId, bool? isContributer, string? nonFilerContributorName, long? contributorFilerId, string? separateAccountName);

    /// <summary>
    /// Gets all lobbyist employer campaign contribution transactions for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of all lobbyist employer campaign contribution transactions for the specified filing.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllLobbyistEmployerCampaignContributionTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllLobbyistEmployerCampaignContributionTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// \endmsc
    Task<IEnumerable<LobbyingCampaignContribution>> GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(long filingId);

    /// <summary>
    /// Gets all activity expense transactions for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of activity expense transactions for the specified filing.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllActivityExpenseTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllAcitivtyExpenseTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<ActivityExpense>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<ActivityExpense>"];
    /// \endmsc
    Task<IEnumerable<ActivityExpense>> GetAllActivityExpenseTransactionsForFiling(long filingId);

    /// <summary>
    /// Gets all lobbyist employer coalition payment transactions for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of all lobbyist employer coalition payment transactions for the specified filing.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllLobbyistEmployerCampaignContributionTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllLobbyistEmployerCampaignContributionTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// \endmsc
    Task<IEnumerable<PaymentMadeToLobbyingCoalitionResponse>> GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(long filingId, DateTime legislativeStartDate);

    /// <summary>
    /// Get all other payments to influence variants for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of influence payment transactions.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllOtherPaymentsToInfluenceTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllOtherPaymentsToInfluenceTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<OtherPaymentsToInfluenceResponse>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<OtherPaymentsToInfluenceResponse>"];
    /// \endmsc
    Task<IEnumerable<OtherPaymentsToInfluenceResponse>> GetAllOtherPaymentsToInfluenceTransactionsForFiling(long filingId, DateTime legislativeStartDate);

    /// <summary>
    /// Get cumulative amount for other payments to influence variants for a specific filing id and contact id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <param name="contactId"></param>
    /// <param name="legislativeStartDate"></param>
    /// <returns>A response with CumulativeAmount.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetOtherPaymentsCumulativeAmountForFilingAndContact()"];
    /// ITransactionSvc => ITransactionRepository [label="GetOtherPaymentsCumulativeAmountForFilingAndContact()"];
    /// \endmsc
    Task<CumulativeAmountResponse> GetOtherPaymentsCumulativeAmountForFilingAndContact(long filingId, long contactId);

    /// <summary>
    /// Get all end-of-session lobbying transactions for a specific filing id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>A collection of end of session lobbying transactions.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllEndOfSessionLobbyingTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllEndOfSessionLobbyingTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<EndOfSessionLobbyingDto>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<EndOfSessionLobbyingResponse>"];
    /// \endmsc
    [Get("/api/" + GetAllEndOfSessionLobbyingTransactionsForFilingPath)]
    Task<IEnumerable<EndOfSessionLobbyingDto>> GetAllEndOfSessionLobbyingTransactionsForFiling(long filingId);
    const string GetAllEndOfSessionLobbyingTransactionsForFilingPath = "Filings/{filingId}/EndOfSessionLobbyingTransactions";

    /// <summary>
    /// Gets all lobbyist employer firm payment transactions for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of all lobbyist employer firm payment transactions for the specified filing.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<PaymentMadeToLobbyingFirmsResponse>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<PaymentMadeToLobbyingFirmsResponse>"];
    /// \endmsc
    [Get(GetAllPaymentsMadeToLobbyingFirmsTransactionsForFilingPath)]
    Task<IEnumerable<PaymentMadeToLobbyingFirmsResponse>> GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(long filingId, DateTime legislativeStartDate);
    const string GetAllPaymentsMadeToLobbyingFirmsTransactionsForFilingPath = "/api/LobbyistEmployerCoalition/PaymentMadeToLobbyingFirmsTransactions/GetAll/{filingId}";

    /// <summary>
    /// Creates a new payment made to lobbying firms transaction.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreatePaymentMadeToLobbyingFirmsTransaction()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return TransactionResponseDto"];
    /// ITransactionSvc >> Actor [label="return TransactionResponseDto"];
    /// \endmsc
    [Post("/api/" + CreatePaymentMadeToLobbyingFirmsTransactionPath)]
    Task<TransactionResponseDto> CreatePaymentMadeToLobbyingFirmsTransaction(PaymentMadeToLobbyingFirmsRequestDto request);
    const string CreatePaymentMadeToLobbyingFirmsTransactionPath = "Transactions/PaymentMadeToLobbyingFirms";

    /// <summary>
    /// Gets transaction summary for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection that represents a payment summary.</returns>
    [Get("/api/" + GetTransactionSummaryPath)]
    Task<TransactionSummaryResponse> GetTransactionSummary(long filingId);
    const string GetTransactionSummaryPath = "Filers/{filingId}/Transactions/Summary";

    /// <summary>
    /// Creates a new payment made to lobbying coalition transaction.
    /// </summary>
    /// <param name="filingId">The ID of the disclosure filing.</param>
    /// <param name="amount">The payment amount.</param>
    /// <param name="filerId">The ID of the filer to which the transaction will be attached.</param>
    /// <param name="registrationFilingId">The ID of the registration filing (optional).</param>
    /// <param name="filerContactId">The ID of the filer contact (optional).</param>
    /// <returns>The ID of the created transaction.</returns>
    /// 
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreatePaymentMadeToLobbyingCoalition()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return long"];
    /// ITransactionSvc >> Actor [label="return long"];
    /// \endmsc
    Task<TransactionResponseDto> CreatePaymentMadeToLobbyingCoalition(
        long filingId,
        decimal? amount,
        long filerId,
        long? registrationFilingId = null,
        long? filerContactId = null);

    /// <summary>
    /// Gets all lobbyist employer payments received by a coalition transactions for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of all lobbyist employer payments received by a coalition transactionss for the specified filing.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// \endmsc
    Task<IEnumerable<PaymentReceiveLobbyingCoalitionResponse>> GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(long filingId);

    /// <summary>
    /// Validates all lobbyist employer payments received by a coalition transactions for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>Validation errors for lobbyist employer payments received by a coalition transactionss for the specified filing.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling()"];
    /// ITransactionSvc => ITransactionRepository [label="GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<LobbyingCampaignContribution>"];
    /// \endmsc
    Task<ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse> ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(long filingId);


    /// <summary>
    /// Creates a new payment received by lobbying coalition transaction.
    /// </summary>
    /// <param name="request">The request body</param>
    /// <returns>The ID of the created transaction.</returns>
    /// 
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreatePaymentReceivedByLobbyingCoalition()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return long"];
    /// ITransactionSvc >> Actor [label="return long"];
    /// \endmsc
    [Post(CreatePaymentReceivedByLobbyingCoalitionPath)]
    Task<TransactionResponseDto> CreatePaymentReceivedByLobbyingCoalition(PaymentReceiveLobbyingCoalitionRequestDto request);
    const string CreatePaymentReceivedByLobbyingCoalitionPath = "/api/LobbyistEmployerCoalition/PaymentReceivedByLobbyingCoalitionTransactions";

    /// <summary>
    /// Edits an existing payment received by lobbying coalition transaction.
    /// </summary>
    /// <param name="request">The request body</param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="EditPaymentReceivedByLobbyingCoalition()"];
    /// ITransactionSvc => ITransactionRepository [label="Update()"];
    /// ITransactionRepository >> ITransactionSvc [label="return TransactionResponseDto"];
    /// ITransactionSvc >> Actor [label="return TransactionResponseDto"];
    /// \endmsc
    [Put(EditPaymentReceivedByLobbyingCoalitionPath)]
    Task<TransactionResponseDto> EditPaymentReceivedByLobbyingCoalition(long transactionId, PaymentReceiveLobbyingCoalitionRequestDto request);
    const string EditPaymentReceivedByLobbyingCoalitionPath = "/api/LobbyistEmployerCoalition/PaymentReceivedByLobbyingCoalitionTransactions/{transactionId}";

    /// <summary>
    /// Get lobbyist employer payments received by a coalition transaction by id 
    /// </summary>
    /// <param name="transactionId"></param>
    /// <returns></returns>
    [Get(GetPaymentReceivedLobbyingCoalitionTransactionByIdPath)]
    Task<PaymentReceiveLobbyingCoalitionResponse> GetPaymentReceivedLobbyingCoalitionTransactionById(long transactionId);
    const string GetPaymentReceivedLobbyingCoalitionTransactionByIdPath = "/api/LobbyistEmployerCoalition/PaymentReceivedByLobbyingCoalitionTransactions/{transactionId}";


    /// <summary>
    /// Creates a new other influence payment.
    /// </summary>
    /// <param name="otherInfluencePaymentDto">The other influence payment to create.</param>
    /// <returns>The ID of the created other influence payment.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreatePaymentReceivedByLobbyingCoalition()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return long"];
    /// ITransactionSvc >> Actor [label="return long"];
    /// \endmsc
    Task<Transaction> CreateOtherPaymentToInfluence(OtherInfluencePaymentDto otherInfluencePaymentDto);

    /// <summary>
    /// Edits an existing other influence payment.
    /// </summary>
    /// <param name="transactionId"> The ID of the transaction to edit.</param>
    /// <param name="request">The other influence payment to edit.</param>
    /// <returns></returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="EditOtherPaymentToInfluence()"];
    /// ITransactionSvc => ITransactionRepository [label="Update()"];
    /// ITransactionRepository >> ITransactionSvc [label="return Transaction"];
    /// ITransactionSvc >> Actor [label="return Transaction"];
    /// \endmsc
    [Put(EditOtherPaymentToInfluencePath)]
    Task<Transaction> EditOtherPaymentToInfluence(long transactionId, OtherInfluencePaymentDto request);
    const string EditOtherPaymentToInfluencePath = "/api/Transactions/OtherPayments/{transactionId}";

    /// <summary>
    /// Get other payments to influence transaction by id.
    /// </summary>
    /// <param name="transactionId"></param>
    /// <returns>other payment to influence transaction.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetOtherPaymentsToInfluenceTransactionById()"];
    /// ITransactionSvc => ITransactionRepository [label="GetOtherPaymentsToInfluenceTransactionById()"];
    /// ITransactionRepository >> ITransactionSvc [label="return OtherPaymentsToInfluenceResponse"];
    /// ITransactionSvc >> Actor [label="return OtherPaymentsToInfluenceResponse"];
    /// \endmsc
    /// 
    [Get(GetOtherPaymentsToInfluenceTransactionByIdPath)]
    Task<OtherInfluencePaymentDto> GetOtherPaymentsToInfluenceTransactionById(long transactionId);
    const string GetOtherPaymentsToInfluenceTransactionByIdPath = "/api/Transactions/OtherPayments/{transactionId}";

    /// <summary>
    /// Gets all lobbyist campaign contribution transactions for a specific filing.
    /// </summary>
    /// <param name="transactionId">The ID of the transaction.</param>
    /// <returns>A lobbyist campaign contribution transactions for the specified transaction id.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetLobbyistCampaignContributionTransactionById()"];
    /// ITransactionSvc => ITransactionRepository [label="GetLobbyistCampaignContributionTransactionById()"];
    /// ITransactionRepository >> ITransactionSvc [label="return LobbyingCampaignContribution"];
    /// ITransactionSvc >> Actor [label="return LobbyingCampaignContribution"];
    /// \endmsc
    [Get(GetLobbyistCampaignContributionTransactionByIdPath)]
    Task<LobbyingCampaignContribution?> GetLobbyistCampaignContributionTransactionById(long transactionId);
    const string GetLobbyistCampaignContributionTransactionByIdPath = "/api/Transactions/LobbyistCampaignContributionTransaction/Get/{transactionId}";

    /// <summary>
    /// Edit a lobbyist campaign contribution transaction.
    /// </summary>
    /// <param name="transactionId">The ID of the transaction.</param>
    /// <param name="lobbyistCampaignContributionRequestDto">The lobbyist campaign contribution transaction to edit.</param>
    /// <returns>The edited transaction with the specified ID..</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="EditLobbyistCampaignContribution()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return Transaction"];
    /// ITransactionSvc >> Actor [label="return Transaction"];
    /// \endmsc
    [Post(EditLobbyistCampaignContributionPath)]
    Task<Transaction> EditLobbyistCampaignContribution(LobbyistCampaignContributionRequestDto lobbyistCampaignContributionRequestDto);
    const string EditLobbyistCampaignContributionPath = "/Filers/Transactions/LobbyistCampaignContribution/Edit";

    /// <summary>
    /// Creates a lobbying advertisement transaction transaction for 72h report.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <param name="filingId">The ID of the filing.</param>
    /// <param name="request">The request object.</param>
    /// <returns>The created record ID..</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="CreateLobbyingAdvertisementTransaction()"];
    /// ITransactionSvc => ITransactionRepository [label="Create()"];
    /// ITransactionRepository >> ITransactionSvc [label="return Transaction"];
    /// ITransactionSvc >> Actor [label="return Transaction"];
    /// \endmsc
    [Post(CreateLobbyingAdvertisementTransactionPath)]
    Task<TransactionResponseDto> CreateLobbyingAdvertisementTransaction(long filingId, LobbyingAdvertisementRequestDto request);
    const string CreateLobbyingAdvertisementTransactionPath = "/api/Filings/{filingId}/Transactions/LobbyingAdvertisement/Create";

    /// <summary>
    /// Edits a lobbying advertisement transaction for 72h report.
    /// </summary>
    /// <param name="request">The request object.</param>
    /// <returns>The updated record ID..</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="EditLobbyingAdvertisementTransactionByFilingId()"];
    /// ITransactionSvc => ITransactionRepository [label="Update()"];
    /// ITransactionRepository >> ITransactionSvc [label="return Transaction"];
    /// ITransactionSvc >> Actor [label="return Transaction"];
    /// \endmsc
    [Put(EditLobbyingAdvertisementTransactionPath)]
    Task<TransactionResponseDto?> EditLobbyingAdvertisementTransaction(long filingId, LobbyingAdvertisementRequestDto request);
    const string EditLobbyingAdvertisementTransactionPath = "/api/Filings/{filingId}/Transactions/LobbyingAdvertisement/Edit";

    /// <summary>
    /// Gets the lobbying advertisement transaction for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A lobbying advertisement transactions for the specified filing id.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetLobbyingAdvertisementTransactionByFilingId()"];
    /// ITransactionSvc => ITransactionRepository [label="GetLobbyingAdvertisementTransactionByFilingId()"];
    /// ITransactionRepository >> ITransactionSvc [label="return LobbyingAdvertisement"];
    /// ITransactionSvc >> Actor [label="return LobbyingAdvertisement"];
    /// \endmsc
    [Get(GetLobbyingAdvertisementTransactionByFilingIdPath)]
    Task<LobbyingAdvertisement?> GetLobbyingAdvertisementTransactionByFilingId(long filingId);
    const string GetLobbyingAdvertisementTransactionByFilingIdPath = "/api/Transactions/LobbyingAdvertisementTransaction/Get/{filingId}";

    /// <summary>
    /// Created an end-of-sesion lobbying transaction.
    /// </summary>
    /// <param name="request">The end-of-session lobbying request.</param>
    /// <returns>Id of created transaction.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetLobbyingAdvertisementTransactionByFilingId()"];
    /// ITransactionSvc => ITransactionRepository [label="GetLobbyingAdvertisementTransactionByFilingId()"];
    /// ITransactionRepository >> ITransactionSvc [label="return LobbyingAdvertisement"];
    /// ITransactionSvc >> Actor [label="return LobbyingAdvertisement"];
    /// \endmsc
    [Post(CreateEndOfSessionLobbyingPath)]
    Task<TransactionResponseDto> CreateEndOfSessionLobbying(EndOfSessionLobbyingRequestDto request);
    const string CreateEndOfSessionLobbyingPath = "/api/Transactions/EndOfSessionLobbying";

    /// <summary>
    /// Get an end-of-session lobbying transactions by id.
    /// </summary>
    /// <param name="filingId"></param>
    /// <returns>An end of session lobbying transaction.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetEndOfSessionLobbyingTransactionById()"];
    /// ITransactionSvc => ITransactionRepository [label="GetEndOfSessionLobbyingTransactionById()"];
    /// ITransactionRepository >> ITransactionSvc [label="return IEnumerable<EndOfSessionLobbyingDto>"];
    /// ITransactionSvc >> Actor [label="return IEnumerable<EndOfSessionLobbyingResponse>"];
    /// \endmsc
    [Get(GetEndOfSessionLobbyingTransactionByIdPath)]
    Task<EndOfSessionLobbyingDto> GetEndOfSessionLobbyingTransactionById(long transactionId);
    const string GetEndOfSessionLobbyingTransactionByIdPath = "/api/Transactions/EndOfSessionTransactionLobbying/{transactionId}";

    /// <summary>
    /// Edit an end-of-sesion lobbying transaction.
    /// </summary>
    /// <param name="request">The end-of-session lobbying request.</param>
    /// <returns>Id of edited transaction.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="EditEndOfSessionLobbying()"];
    /// ITransactionSvc => ITransactionRepository [label="EditEndOfSessionLobbying()"];
    /// ITransactionRepository >> ITransactionSvc [label="return TransactionResponseDto"];
    /// ITransactionSvc >> Actor [label="return TransactionResponseDto"];
    /// \endmsc
    [Patch(EditEndOfSessionLobbyingPath)]
    Task<TransactionResponseDto> EditEndOfSessionLobbying(long transactionId, EndOfSessionLobbyingRequestDto request);
    const string EditEndOfSessionLobbyingPath = "/api/Transactions/EndOfSessionLobbying/{transactionId}";

    /// <summary>
    /// Get all active transactions by a Filing
    /// </summary>
    /// <typeparam name="TTransaction">Transaction type that needs to get</typeparam>
    /// <typeparam name="TTransactionResponseDto">The response model corresponding to the transaction type</typeparam>
    /// <param name="filingId">ID of Filing to which the transaction belongs</param>
    /// <param name="responseFactory">
    /// A delegate function used to transform a transaction entity of type <typeparamref name="TTransaction"/>
    /// into its corresponding response DTO of type <typeparamref name="TTransactionResponseDto"/>.
    /// </param>
    /// <returns>A collection of transaction response object</returns>
    [Get(GetAllTransactionsByFilingAsyncPath)]
    Task<List<TTransactionResponseDto>> GetAllTransactionsByFilingAsync<TTransaction, TTransactionResponseDto>(long filingId, Func<TTransaction, TTransactionResponseDto> responseFactory)
        where TTransaction : Transaction
        where TTransactionResponseDto : TransactionDetailResponseDto;
    const string GetAllTransactionsByFilingAsyncPath = "/api/Filings/{filingId}/Transactions";

    /// <summary>
    /// Soft-delete a transaction
    /// </summary>
    /// <param name="filingId">ID of Filing to which the transaction belongs</param>
    /// <param name="transactionId">ID of Transaction that is soft-deleted</param>
    /// <returns></returns>
    [Patch(DeleteTransactionAsyncPath)]
    Task DeleteTransactionAsync(long filingId, long transactionId);
    const string DeleteTransactionAsyncPath = "/api/Filings/{filingId}/Transactions/{transactionId}/Delete";

    /// <summary>
    /// Gets the lobbyist employer coalition payment transaction by transaction id.
    /// </summary>
    /// <param name="transactionId">The ID of the transaction.</param>
    /// <returns>A lobbyist employer coalition payment transaction for the specified transaction id.</returns>
    ///
    /// \msc
    /// Actor, ITransactionSvc [label="ITransaction \n Svc"], ITransactionRepository [label="ITransaction \n Repository"];
    /// Actor => ITransactionSvc [label="GetPaymentMadeToLobbyingCoalitionTransactionsByFilingId()"];
    /// ITransactionSvc => ITransactionRepository [label="GetPaymentMadeToLobbyingCoalitionTransactionsByFilingId()"];
    /// ITransactionRepository >> ITransactionSvc [label="return PaymentMadeToLobbyingCoalitionResponse"];
    /// ITransactionSvc >> Actor [label="return PaymentMadeToLobbyingCoalitionResponse"];
    /// \endmsc
    [Get(GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionIdPath)]
    Task<PaymentMadeToLobbyingCoalitionResponse?> GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(long transactionId);
    const string GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionIdPath = "/api/LobbyistEmployerCoalition/PaymentMadeToLobbyingCoalitionTransaction/Get/{transactionId}";

    /// <summary>
    /// Edit a transaction
    /// </summary>
    /// <param name="transactionId">ID of transaction</param>
    /// <param name="amount">Period amount of transaction</param>
    /// <returns></returns>
    [Get(EditPaymentMadeToLobbyingCoalitionAmountPath)]
    Task<TransactionResponseDto> EditPaymentMadeToLobbyingCoalitionAmount(long transactionId, decimal? amount);
    const string EditPaymentMadeToLobbyingCoalitionAmountPath = "/api/LobbyistEmployerCoalition/PaymentMadeToLobbyingCoalitionTransaction/Edit/{transactionId}";

    /// <summary>
    /// Creates a transaction and links it to a filing (reusable for all TPH types).
    /// </summary>
    /// <typeparam name="TTransaction">Transaction type to create</typeparam>
    /// <param name="transaction">Transaction to create</param>
    /// <param name="filingId">ID of filing that transaction belongs to</param>
    /// <returns>A created Transaction</returns>
    [Post("/ForServicesOnly")]
    Task<Transaction> CreateTransactionWrapper<TTransaction>(TTransaction transaction, long filingId) where TTransaction : Transaction;

    /// <summary>
    /// Update a transaction and its related entity
    /// </summary>
    /// <typeparam name="TTransaction">Transaction type to update</typeparam>
    /// <param name="transaction">Transaction to update</param>
    /// <param name="varianceAmount">Variance amount of transaction during the change</param>
    /// <param name="filingId">ID of filing that transaction belongs to</param>
    /// <param name="contactId">ID of contact that transaction associates to</param>
    /// <returns></returns>
    [Put("/ForServicesOnly")]
    Task UpdateTransactionWrapperAsync<TTransaction>(TTransaction transaction, decimal varianceAmount, long filingId, long contactId)
        where TTransaction : Transaction;

    /// <summary>
    /// Returns changes to the FilingContactSummary for this transaction request
    /// </summary>
    /// <param name="filing"></param>
    /// <param name="newTransactionRequest"></param>
    /// <param name="transactionId">If this is an update, the id of the DisclosureTransaction</param>
    /// <returns>existingTransaction transaction that is being updated, previousContactSummary if the summary category has changed, newContactSummary for the category associated with the transaction requested</returns>
    Task<(PaymentReceived? existingTransaction, FilingContactSummary? previousContactSummary, FilingContactSummary newContactSummary)>
        GetSmoCampaignStatementFilingContactSummaryChanges(Filing filing, PaymentReceivedRequest newTransactionRequest, long? transactionId)
    { throw new NotImplementedException(); }
}
