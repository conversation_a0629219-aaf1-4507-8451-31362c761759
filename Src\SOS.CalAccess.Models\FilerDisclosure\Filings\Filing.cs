// <copyright file="Filing.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations.Schema;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Models.FilerDisclosure.Filings;

/// <summary>
/// <para>Class that represents a filing in the system, generally a statement or other
/// form of document that discloses information relevant to the activities of a filer.</para>
/// <para>Examples of filings could include:</para>
/// <list type="bullet">
///  <item>statements of economic interest</item>
///  <item>lobbying reports</item>
///  <item>campaign finance reports</item>
///  <item>declaration of intent to participate in specific programs</item>
///  </list>
/// </summary>
[Table("DisclosureFiling")]
[Documentation("", Context = "This table holds system filings.")]
public class Filing : IIdentifiable<long>
{
    /// <summary>
    /// Gets or sets the primary identifier for this filing.
    /// </summary>
    [Documentation("Filing identifier.")]
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the version number for this filing.
    /// </summary>
    [Documentation("Filing version used to indicate the version of the filing record as amendments are made.", Context = "Incremental version number.")]
    public int? Version { get; set; }

    /// <summary>
    /// Gets or sets the identifier for the filer on whose behalf this filing
    /// was submitted.
    /// </summary>
    [Documentation("Filer to which this filing is linked.", Context = "Reference to a filer.")]
    public long FilerId { get; set; }

    /// <summary>
    /// Gets or sets the filer on whose behalf this filing
    /// was submitted.
    /// </summary>
    public Filer? Filer { get; set; }

    /// <summary>
    /// Gets or sets the identifier for the filing period to which this filing is linked.
    /// </summary>
    [Documentation("Filing period to which this filing is linked.", Context = "Reference to a filing period.")]
    public long? FilingPeriodId { get; set; }

    /// <summary>
    /// Gets or sets the filing period to which this filing is linked.
    /// </summary>
    public FilingPeriod? FilingPeriod { get; set; }

    /// <summary>
    /// Gets or sets the identifier for the type of filing this is.
    /// </summary>
    [Documentation("Filing type.", Context = "Reference to a filing type.")]
    public long FilingTypeId { get; set; }

    /// <summary>
    /// Gets or sets the type of filing this is.
    /// </summary>
    public FilingType? FilingType { get; set; }

    /// <summary>
    /// Gets or sets the current status of this filing.
    /// </summary>
    public FilingStatus? Status { get; set; }

    /// <summary>
    /// Gets or sets the current status of this filing.
    /// </summary>
    [Documentation("Filing status identifier.", Context = "Reference to a filing status.")]
    public required long StatusId { get; set; }

    /// <summary>
    /// Gets or sets the date at which this filing was submitted.
    /// </summary>
    [Documentation("Filing submission date.")]
    public DateTime? SubmittedDate { get; set; }

    /// <summary>
    /// Gets or sets a reference to the original filing that was used as the
    /// initial filing in the chain of amendments. This differs from the
    /// <see cref="ParentId"/> in that it will always point to the original
    /// filing.
    /// </summary>
    /// <remarks>
    /// For original versions, this value should equal the value of <see cref="Id"/>.
    /// </remarks>
    [Documentation("Original filing to which this filing is linked. If this filing is an amendment, the OriginalId is populated with a reference to the first filing ID that initialized the chain of amendments. This differs from ParentId in that it will always point to the original filing.", Context = "Reference to a filing.")]
    public long? OriginalId { get; set; }

    /// <summary>
    /// Gets or sets the filing that was used as the initial filing in the chain
    /// of amendments.
    /// </summary>
    public Filing? Original { get; set; }

    /// <summary>
    /// Gets or sets a reference to the filing that was used as base to produce
    /// this instance, or that was amended by this submission. This differs from
    /// the <see cref="OriginalId"/> in that it will always point to the
    /// parent (previous) filing.
    /// </summary>
    /// <remarks>
    /// For original versions, this value should equal the value of <see cref="Id"/>.
    /// </remarks>
    [Documentation("Parent filing to which this filing is linked. If this filing is an amendment, the ParentId is populated with a reference to the filing ID that was amended by this amendment. This differs from the OriginalId in that it will always point to the previous filing.", Context = "Reference to a filing.")]
    public long? ParentId { get; set; }

    /// <summary>
    /// Gets or sets the filing that was used
    /// as base to produce this instance, or that was amended by this submission.
    /// </summary>
    public Filing? Parent { get; set; }

    /// <summary>
    /// Gets the filing that has a <see cref="ParentId"/> that matches this
    /// filing.
    /// </summary>
    public Filing? Amendment { get; set; }

    /// <summary>
    /// Gets or sets the start date of the period covered by this filing.
    /// </summary>
    [Documentation("Filing start date.")]
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date of the period covered by this filing.
    /// </summary>
    [Documentation("Filing end date.")]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Gets the full set of amendments to this filing.
    /// </summary>
    /// <remarks>
    /// This will usually only be valid for the "original" instance of
    /// a filing, the first version to be approved.
    /// </remarks>
    public List<Filing> Amendments { get; init; } = [];

    /// <summary>
    /// Gets the list of transactions included in this filing.
    /// </summary>
    public List<FilingTransaction> FilingTransactions { get; set; } = [];

    /// <summary>
    /// Gets the list of related filers for the filing.
    /// </summary>
    public List<FilingRelatedFiler> FilingRelatedFilers { get; set; } = [];

    /// <summary>
    /// Gets or sets the filing summaries to which this filing is linked.
    /// </summary>
    public List<FilingSummary> FilingSummaries { get; set; } = [];

    /// <summary>
    /// Gets or sets the date and time this filing was approved.
    /// </summary>
    public DateTime? ApprovedAt { get; set; }

    /// <summary>
    /// Gets or sets the created by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who created this record.")]
    public long CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the modified by user identifier.
    /// </summary>
    [Documentation("The identifier of the user who last modified this record.")]
    public long ModifiedBy { get; set; }

    /// <summary>
    /// Gets or sets if user has verified reasonable diligence statement on submit.
    /// </summary>
    [Documentation("If user has verified reasonable diligence statement on submit.")]
    public bool? DiligenceStatementVerified { get; set; }

    /// <summary>
    /// Gets or sets if the report was submitted late.
    /// </summary>
    [Documentation("Was the report submitted late.")]
    public bool? SubmittedLate { get; set; }

    /// <summary>
    /// Gets or sets the identifier for the legislative session id to which this id is linked.
    /// </summary>
    [Documentation("Legislative session to which this id is linked.", Context = "Reference to a legislative session.")]
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// Gets or sets the legislative session to which this  id is linked.
    /// </summary>
    public LegislativeSession? LegislativeSession { get; set; }
    /// <summary>
    /// Gets or sets based on if the contributions go over a certain amount
    /// or expenditure was reached.
    /// </summary>
    [Documentation("Contributions or expenditure was exceeded on.")]
    public DateTime? ContributionsOrExpenditureOverOn { get; set; }

    [Documentation("Amendment Explanation.")]
    public string? AmendmentExplanation { get; set; }

    /// <summary>
    /// Gets the list of actions lobbied
    /// </summary>
    public List<ActionsLobbied?> ActionsLobbied { get; set; } = [];

    /// <summary>
    /// Gets or sets the ID of the registration associated with the Disclosure Filing at the time it was submitted.
    /// </summary>
    [Documentation("The ID of the registration linked to the Disclosure Filing at the time of submission.")]
    public long? RegistrationId { get; set; }

    /// <summary>
    /// Gets or sets the registration record associated with the Disclosure Filing at the time it was submitted.
    /// </summary>
    public Registration? RegistrationAtSubmission { get; set; }

    [Documentation("Total payments in connection with PUC administrative testimony.")]
    public decimal? TotalPaymentsPucActivity { get; set; }

    /// <summary>
    /// Gets or sets the linkages of candidates or measures not listed on the payment received
    /// </summary>
    public List<DisclosureWithoutPaymentReceived> DisclosureWithoutPaymentReceiveds { get; set; } = new();

    /// <summary>
    /// Returns true if this filing is an amendment (i.e. its Id differs from its OriginalId).
    /// </summary>
    public bool IsAmendment => OriginalId.HasValue && Id != OriginalId.Value;
}
