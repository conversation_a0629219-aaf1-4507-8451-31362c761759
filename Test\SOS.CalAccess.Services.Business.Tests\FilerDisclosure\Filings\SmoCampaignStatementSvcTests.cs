using System.Linq.Expressions;
using System.Reflection;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.FileSystem;
using SOS.CalAccess.Services.Common.FileSystem.Models;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Filings;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class SmoCampaignStatementSvcTests
{
    private IRegistrationRepository _registrationRepositoryMock;
    private IFilingPeriodRepository _filingPeriodRepositoryMock;
    private IFilingRepository _filingRepositoryMock;
    private IFilingSummaryRepository _filingSummaryRepositoryMock;
    private ISmoRegistrationSvc _smoRegistrationSvcMock;
    private IFilerSvc _filerSvcMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private ITransactionSvc _transactionSvcMock;
    private IDecisionsSvc _decisionsSvcMock;
    private IAttestationRepository _attestationRepositoryMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IRegistrationRegistrationContactRepository _registrationRegistrationContactRepositoryMock;
    private ITransactionRepository _transactionRepositoryMock;
    private IDisclosureWithoutPaymentReceivedRepository _disclosureWithoutPaymentReceivedRepositoryMock;
    private IReferenceDataSvc _referenceDataSvc;
    private ILinkageSvc _linkageSvcMock;
    private IFilerUserRepository _filerUserRepositoryMock;
    private ILinkageRequestRepository _linkageRequestRepositoryMock;
    private IFilingContactSummaryRepository _filingContactSummaryRepositoryMock;
    private IDateTimeSvc _dateTimeSvcMock;
    private IUploadFileSvc _uploadFileSvcMock;
    private DateTime _dateNow;

    private SmoCampaignStatementSvcDependencies _dependencies;
    private FilingSharedServicesDependencies _servicesDependencies;
    private FilingSharedRepositoriesDependencies _repositoriesDependencies;
    private SmoCampaignStatementSvc _service;

    [SetUp]
    public void SetUp()
    {
        _filingRepositoryMock = Substitute.For<IFilingRepository>();
        _filingSummaryRepositoryMock = Substitute.For<IFilingSummaryRepository>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _filingPeriodRepositoryMock = Substitute.For<IFilingPeriodRepository>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _smoRegistrationSvcMock = Substitute.For<ISmoRegistrationSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _transactionSvcMock = Substitute.For<ITransactionSvc>();
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _disclosureWithoutPaymentReceivedRepositoryMock = Substitute.For<IDisclosureWithoutPaymentReceivedRepository>();
        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();
        _filingContactSummaryRepositoryMock = Substitute.For<IFilingContactSummaryRepository>();
        _attestationRepositoryMock = Substitute.For<IAttestationRepository>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _registrationRegistrationContactRepositoryMock = Substitute.For<IRegistrationRegistrationContactRepository>();
        _transactionRepositoryMock = Substitute.For<ITransactionRepository>();
        _linkageSvcMock = Substitute.For<ILinkageSvc>();
        _filerUserRepositoryMock = Substitute.For<IFilerUserRepository>();
        _linkageRequestRepositoryMock = Substitute.For<ILinkageRequestRepository>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _uploadFileSvcMock = Substitute.For<IUploadFileSvc>();

        _dependencies = new SmoCampaignStatementSvcDependencies(
             _smoRegistrationSvcMock,
             _transactionSvcMock,
             _uploadFileSvcMock,
             _disclosureWithoutPaymentReceivedRepositoryMock
             );

        _servicesDependencies = new FilingSharedServicesDependencies(
            _filerSvcMock,
            _decisionsSvcMock,
            _authorizationSvcMock,
            _userMaintenanceSvcMock,
            _notificationSvcMock,
            _referenceDataSvc,
            _linkageSvcMock,
            _dateTimeSvcMock
        );

        _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
            _filingRepositoryMock,
            _filingSummaryRepositoryMock,
            _filingPeriodRepositoryMock,
            _registrationRepositoryMock,
            _attestationRepositoryMock,
            _registrationRegistrationContactRepositoryMock,
            _transactionRepositoryMock,
            _filerUserRepositoryMock,
            _linkageRequestRepositoryMock,
            _filingContactSummaryRepositoryMock
        );

        _service = new SmoCampaignStatementSvc(_dateTimeSvcMock, _dependencies, _servicesDependencies, _repositoriesDependencies);
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    [Test]
    public async Task GetSmoGeneralInformationById_NoRegistrationId_ShouldReturnResult()
    {
        // Arrange
        var filingId = 1L;
        var registrationId = 1L;
        var filing = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        var smoRegistration = GenerateSampleSmo(registrationId, 1, "Test1", RegistrationStatus.Accepted.Id);
        var dto = new SmoGeneralInformationResponseDto(new SmoRegistrationResponseDto(), null, new SmoRegistrationContactDto());
        _ = _filingRepositoryMock.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(filing);
        _ = _registrationRepositoryMock.FindSmoRegistrationLatestAcceptedByFilerId(Arg.Any<long>()).Returns(smoRegistration);
        _ = _smoRegistrationSvcMock.GetSmoGeneralInformationAsync(Arg.Any<long>()).Returns(dto);

        // Act
        var result = await _service.GetSmoGeneralInformationById(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.RegistrationDetail, Is.Not.Null);
            Assert.That(result.Treasurer, Is.Not.Null);
        });
    }

    [Test]
    public async Task GetSmoGeneralInformationById_WithRegistrationId_ShouldReturnResult()
    {
        // Arrange
        var filingId = 1L;
        var registrationId = 1L;
        var filing = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Incomplete.Id);
        filing.RegistrationId = registrationId;
        var smoRegistration = GenerateSampleSmo(registrationId, 1, "Test1", RegistrationStatus.Accepted.Id);
        var dto = new SmoGeneralInformationResponseDto(new SmoRegistrationResponseDto(), null, new SmoRegistrationContactDto());
        _ = _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(filing);
        _ = _registrationRepositoryMock.FindSmoRegistrationLatestAcceptedByFilerId(Arg.Any<long>()).Returns(smoRegistration);
        _ = _smoRegistrationSvcMock.GetSmoGeneralInformationAsync(Arg.Any<long>()).Returns(dto);

        // Act
        var result = await _service.GetSmoGeneralInformationById(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.RegistrationDetail, Is.Not.Null);
            Assert.That(result.Treasurer, Is.Not.Null);
        });
    }

    [Test]
    public void GetSmoGeneralInformationById_InValidFiling_KeyNotFoundException()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var filingPeriodId = 1L;
        var filing = GenerateSampleSmoCampaignStatement(filingId, filerId, filingPeriodId, FilingStatus.Draft.Id);
        _ = _filingRepositoryMock.GetFilingById(filingId).Returns(filing);

        // Act
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetSmoGeneralInformationById(999));
        Assert.That(ex.Message, Is.EqualTo($"Filing not Found Id=999"));
    }

    [Test]
    public void GetSmoGeneralInformationById_InvalidRegistration_KeyNotFoundException()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var filingPeriodId = 1L;
        var filing = GenerateSampleSmoCampaignStatement(filingId, filerId, filingPeriodId, FilingStatus.Draft.Id);
        var registrationId = 1L;
        _ = _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(filingId).Returns(filing);

        // Act
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetSmoGeneralInformationById(filingId));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found with Filer Id={registrationId}"));
    }

    [Test]
    public void CreateSmoCampaignStatementAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var request = new SmoCampaignStatementRequest
        {
            RegistrationId = 1,
        };

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.CreateSmoCampaignStatementAsync(request));
        Assert.That(error.Message, Is.EqualTo($"Registration not found. Id={request.RegistrationId}"));
    }

    [Test]
    public void CreateSmoCampaignStatementAsync_HasNoPermission_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;
        var request = new SmoCampaignStatementRequest
        {
            RegistrationId = registrationId,
        };
        var userId = 999L;
        var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();
        var smoRegistration = GenerateSampleSmo(registrationId, 1, "");

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _authorizationSvcMock.GetInitiatingUserId().Returns(Task.FromResult<long?>(userId));
        _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

        // Act & Assert
        var error = Assert.ThrowsAsync<UnauthorizedAccessException>(async () => await _service.CreateSmoCampaignStatementAsync(request));
        Assert.That(error.Message, Is.EqualTo($"You do not have permission to create campaign statement for registration Id={request.RegistrationId}"));
    }

    [Test]
    public void CreateSmoCampaignStatementAsync_RegistrationIsInvalid_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;
        var userId = 1;
        var request = new SmoCampaignStatementRequest
        {
            RegistrationId = registrationId,
        };
        var smoRegistration = GenerateSampleSmo(registrationId, 1, "", RegistrationStatus.Draft.Id);
        var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _authorizationSvcMock.GetInitiatingUserId().Returns(Task.FromResult<long?>(userId));
        _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

        // Act & Assert
        var error = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.CreateSmoCampaignStatementAsync(request));
        Assert.That(error.Message, Is.EqualTo($"Cannot create campaign statement from unapproved registration Id={request.RegistrationId}"));
    }

    [Test]
    public async Task CreateSmoCampaignStatementAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var registrationId = 1;
        var userId = 1;
        var request = new SmoCampaignStatementRequest
        {
            RegistrationId = registrationId,
        };
        var smoRegistration = GenerateSampleSmo(registrationId, 1, "");
        var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _authorizationSvcMock.GetInitiatingUserId().Returns(Task.FromResult<long?>(userId));
        _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

        // Act
        await _service.CreateSmoCampaignStatementAsync(request);

        // Assert
        Assert.DoesNotThrowAsync(async () => await _service.CreateSmoCampaignStatementAsync(request));
        _ = _filingRepositoryMock.Received().Create(Arg.Any<SmoCampaignStatement>());
        _ = _filingRepositoryMock.Received().Update(Arg.Any<SmoCampaignStatement>());
    }

    [Test]
    public void UpdateSmoCampaignStatementAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var request = new SmoCampaignStatementRequest
        {
            FilingPeriodId = 1,
        };

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.UpdateSmoCampaignStatementAsync(id, request));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public void UpdateSmoCampaignStatementAsync_ApprovedFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var request = new SmoCampaignStatementRequest
        {
            FilingPeriodId = 1,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Accepted.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act & Assert
        var error = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.UpdateSmoCampaignStatementAsync(id, request));
        Assert.That(error.Message, Is.EqualTo($"Cannot modify a Filing with {FilingStatus.Accepted.Name} status. Id={id}"));
    }

    [Test]
    public void UpdateSmoCampaignStatementAsync_ExistStatement_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var request = new SmoCampaignStatementRequest
        {
            FilingPeriodId = 2,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _filingRepositoryMock.IsExistingActiveSmoCampaignStatement(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(true));

        // Act & Assert
        var error = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.UpdateSmoCampaignStatementAsync(id, request));
        Assert.That(error.Message, Is.EqualTo($"The statement in this Filing Period already existed. FilingPeriodId={request.FilingPeriodId}"));
    }

    [Test]
    public void UpdateSmoCampaignStatementAsync_NotFoundFilingPeriod_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var request = new SmoCampaignStatementRequest
        {
            FilingPeriodId = 2,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);

        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _filingRepositoryMock.IsExistingActiveSmoCampaignStatement(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(false));
        _filingPeriodRepositoryMock.FindById(Arg.Any<long>()).ReturnsNull();

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.UpdateSmoCampaignStatementAsync(id, request));
        Assert.That(error.Message, Is.EqualTo($"Filing period not found. Id={request.FilingPeriodId}"));
    }

    [Test]
    public async Task UpdateSmoCampaignStatementAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var request = new SmoCampaignStatementRequest
        {
            FilingPeriodId = 2,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        var previousStatements = GenerateSampleSmoCampaignStatements();
        var filingPeriod = GenerateFilingPeriods().FirstOrDefault();

        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _filingRepositoryMock.IsExistingActiveSmoCampaignStatement(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(false));
        _filingPeriodRepositoryMock.FindById(Arg.Any<long>()).Returns(Task.FromResult(filingPeriod));
        _filingRepositoryMock.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>()).Returns(Task.FromResult(previousStatements));

        // Act
        await _service.UpdateSmoCampaignStatementAsync(id, request);

        //  Assert
        Assert.DoesNotThrowAsync(async () => await _service.UpdateSmoCampaignStatementAsync(id, request));
        _ = _filingRepositoryMock.Received().Update(Arg.Any<SmoCampaignStatement>());
    }

    [Test]
    public async Task GetSmoFilingSummaryByFilingId_ValidId_NoError()
    {
        // Arrange
        long filerId = 1;
        long filingId = 1;
        List<FilingSummary> list = [GenerateSmoFilingSummaryDto(filingId, 5, 100), GenerateSmoFilingSummaryDto(filingId, 6, 200)];

        _ = _filingSummaryRepositoryMock.FindFilingSummariesSmoCampaignStatementByFilingId(filingId).Returns(list);

        // Act
        var result = await _service.GetSmoFilingSummaryByFilingId(filerId);

        // Assert
        Assert.That(result, Is.Not.Empty);
        Assert.That(result?.Count, Is.EqualTo(list.Count));
    }

    [Test]
    public void FindAllUnreportedFilingPeriodsSmoCampaignStatement_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetUnreportedFilingPeriodsByFilerAsync(id));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public async Task FindAllUnreportedFilingPeriodsSmoCampaignStatement_NotFoundFilingPeriod_ShouldReturnEmpty()
    {
        // Arrange
        var id = 1;
        var filingPeriods = new List<FilingPeriod>();
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);

        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _filingPeriodRepositoryMock.FindAllUnreportedFilingPeriodsSmoCampaignStatementByFiler(id).Returns(Task.FromResult(filingPeriods));

        // Act
        var result = await _service.GetUnreportedFilingPeriodsByFilerAsync(id);

        //  Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Empty);
            Assert.That(result, Is.InstanceOf<List<FilingPeriodResponseDto>>());
        });
    }

    [Test]
    public async Task FindAllUnreportedFilingPeriodsSmoCampaignStatement_Found_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        var filingPeriods = GenerateFilingPeriods();

        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _filingPeriodRepositoryMock.FindAllUnreportedFilingPeriodsSmoCampaignStatementByFiler(id).Returns(Task.FromResult(filingPeriods));

        // Act
        var result = await _service.GetUnreportedFilingPeriodsByFilerAsync(id);

        //  Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<FilingPeriodResponseDto>>());
        });
    }

    [Test]
    public void GetSmoCampaignStatementOverviewAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetSmoCampaignStatementOverviewAsync(id));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public async Task GetSmoCampaignStatementOverviewAsync_FoundFiling_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act
        var result = await _service.GetSmoCampaignStatementOverviewAsync(id);

        //  Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<FilingOverviewResponseDto>());
            Assert.That(result.Id, Is.EqualTo(id));
        });
    }

    [Test]
    public void MarkFilingSummaryAsNothingToReportAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.MarkFilingSummaryAsNothingToReportAsync(id, filingSummaryId));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public void MarkFilingSummaryAsNothingToReportAsync_NotFoundFilingSummary_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 999;
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.MarkFilingSummaryAsNothingToReportAsync(id, filingSummaryId));
        Assert.That(error.Message, Is.EqualTo($"Filing summary not found. Id={filingSummaryId}"));
    }

    [Test]
    public async Task MarkFilingSummaryAsNothingToReportAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act
        await _service.MarkFilingSummaryAsNothingToReportAsync(id, filingSummaryId);

        //  Assert
        Assert.DoesNotThrowAsync(async () => await _service.MarkFilingSummaryAsNothingToReportAsync(id, filingSummaryId));
        _ = _filingSummaryRepositoryMock.Received().Update(Arg.Any<FilingSummary>());
    }

    [Test]
    public void GetTransactionSummaryAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetTransactionSummaryAsync(id, filingSummaryId));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public void GetTransactionSummaryAsync_NotFoundFilingSummary_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryTypeId = 999;
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetTransactionSummaryAsync(id, filingSummaryTypeId));
        Assert.That(error.Message, Is.EqualTo($"Filing summary not found with requested type. Id={filingSummaryTypeId}"));
    }

    [TestCaseSource(nameof(GetTransactionSummaryAsyncTestCases))]
    public async Task GetTransactionSummaryAsync_ValidRequest_ShouldReturnResult(long filingSummaryTypeId, object transactions)
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;

        var statement = new SmoCampaignStatement
        {
            Id = id,
            StatusId = FilingStatus.Draft.Id,
            FilingSummaries = new List<FilingSummary>
            {
                new()
                {
                    Id = filingSummaryId,
                    FilingSummaryTypeId = filingSummaryTypeId,
                    PeriodAmount = 100m,
                    ToDateAmount = 100m,
                }
            }
        };

        _repositoriesDependencies.FilingRepository
            .FindSmoCampaignStatementById(Arg.Any<long>())
            .Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        if (filingSummaryTypeId == FilingSummaryType.PaymentReceivedSummary.Id)
        {
            var paymentReceivedTransactions = (List<PaymentReceived>)transactions;
            _transactionSvcMock.GetAllTransactionsByFilingAsync(
                Arg.Any<long>(), Arg.Any<Func<PaymentReceived, PaymentReceivedResponseDto>>())
                .Returns([.. paymentReceivedTransactions.Select(t => new PaymentReceivedResponseDto(id, t))]);
        }

        if (filingSummaryTypeId == FilingSummaryType.PaymentMadeSummary.Id)
        {
            var paymentMadeTransactions = (List<PaymentMade>)transactions;
            _transactionSvcMock.GetAllTransactionsByFilingAsync(
                Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeResponseDto>>())
                .Returns([.. paymentMadeTransactions.Select(t => new PaymentMadeResponseDto(id, t))]);
        }

        if (filingSummaryTypeId == FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id)
        {
            var paymentMadeTransactions = (List<PaymentMade>)transactions;
            _transactionSvcMock.GetAllTransactionsByFilingAsync(
                Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeByAgentOrIndependentContractorResponseDto>>())
                .Returns([.. paymentMadeTransactions.Select(t => new PaymentMadeByAgentOrIndependentContractorResponseDto(id, t))]);
        }

        if (filingSummaryTypeId == FilingSummaryType.PersonReceiving1000OrMoreSummary.Id)
        {
            var personTransactions = (List<PersonReceiving1000OrMore>)transactions;
            _transactionSvcMock.GetAllTransactionsByFilingAsync(
                Arg.Any<long>(), Arg.Any<Func<PersonReceiving1000OrMore, PersonReceiving1000OrMoreResponseDto>>())
                .Returns([.. personTransactions.Select(t => new PersonReceiving1000OrMoreResponseDto(id, t))]);
        }

        // Act
        var result = await _service.GetTransactionSummaryAsync(id, filingSummaryTypeId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionSummaryResponseDto>());
            Assert.That(result.SummaryResponseDto.Id, Is.EqualTo(filingSummaryId));
        });

        if (filingSummaryTypeId == 999L)
        {
            Assert.That(result.TransactionResponseDtos, Is.Empty);
        }
        else
        {
            Assert.That(result.TransactionResponseDtos, Has.Count.EqualTo(1));
        }
    }

    [Test]
    public void UpdateFilingSummaryAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;
        var request = new SmoCampaignStatementFilingSummaryRequest
        {
            UnitemizedAmount = 100m,
        };

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.UpdateFilingSummaryAsync(id, filingSummaryId, request));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public void UpdateFilingSummaryAsync_NotFoundFilingSummary_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 999;
        var request = new SmoCampaignStatementFilingSummaryRequest
        {
            UnitemizedAmount = 100m,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.UpdateFilingSummaryAsync(id, filingSummaryId, request));
        Assert.That(error.Message, Is.EqualTo($"Filing summary not found. Id={filingSummaryId}"));
    }

    [Test]
    public async Task UpdateFilingSummaryAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 1;
        var request = new SmoCampaignStatementFilingSummaryRequest
        {
            UnitemizedAmount = 100m,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementUnitemizedPaymentsReceivedDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementUnitemizedPaymentsReceivedDs>(),
                Arg.Any<bool>())
            .Returns(new List<WorkFlowError>()); // No validation errors

        // Act
        await _service.UpdateFilingSummaryAsync(id, filingSummaryId, request);

        //  Assert
        Assert.DoesNotThrowAsync(async () => await _service.UpdateFilingSummaryAsync(id, filingSummaryId, request));
        _ = _filingSummaryRepositoryMock.Received().Update(Arg.Any<FilingSummary>());
    }

    [Test]
    public async Task UpdateFilingSummaryAsync_ValidRequest_ShouldNotUpdateWithDecisionsErrors()
    {
        // Arrange
        var id = 1;
        var filingSummaryId = 2;
        var request = new SmoCampaignStatementFilingSummaryRequest
        {
            UnitemizedAmount = 100m,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        List<WorkFlowError> workFlowError = new()
        {
            new("UnitemizedPaymentsReceivedTotal", "ErrGlobal0008", "Validation", "Amount must be equal or greater than $0.00."),
        };

        _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementUnitemizedPaymentsMadeDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementUnitemizedPaymentsMadeDs>(),
                Arg.Any<bool>())
            .Returns(workFlowError);

        // Act
        var response = await _service.UpdateFilingSummaryAsync(id, filingSummaryId, request);

        //  Assert
        Assert.Multiple(() =>
        {
            Assert.That(response.Valid, Is.False);
            Assert.That(response.ValidationErrors, Is.Not.Empty);
            Assert.That(response.ValidationErrors, Has.Count.EqualTo(1));
        });
    }

    #region AttestAsync
    [Test]
    public async Task AttestAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var filingId = 1;
        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var smoContacts = new List<RegistrationRegistrationContact>
        {
            GenerateSampleRegistrationContact(1, 1001, "Treasurer", "Mr.", true, 1, "Test", "Test"),
            GenerateSampleRegistrationContact(1, 1002, "Secretary", "Ms.", false, 2, "Jane", "Smith"),
            GenerateSampleRegistrationContact(2, 1003, "Manager", "Dr.", true, 3, "Alice", "Johnson")
        };
        var decisionsAttestationResponse = new DecisionsSmoCampaignStatementSubmissionResponse() { };

        var decisionsResponse = new DecisionsSmoCampaignStatementSubmissionResponse()
        {
            Status = "Accepted",
            Notifications = new List<NotificationTrigger>() { new(true, 1, _dateNow) }
        };

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        statement.OriginalId = 2;
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(1).Returns(smoContacts);
        _filerSvcMock.GetFilerRegistrationAsOfAsync(Arg.Any<long>(), Arg.Any<DateTime>()).Returns(Task.FromResult(1L));

        var paymentsReceived = new List<PaymentReceivedResponseDto>
        {
            new()
            {
                Name = "Name",
                CandidateOrMeasure = "CandidateOrMeasure",
                Position = "Position",
                TransactionDate = _dateNow,
                StanceOnCandidateDto = null,
                Amount = 100,
                Notes = ""
            }
        };

        var paymentsMade = new List<PaymentMadeResponseDto>
        {
            new()
            {
                Name = "Name",
                Description = "Description"
            }
        };

        var paymentsMadeByAgent = new List<PaymentMadeByAgentOrIndependentContractorResponseDto>
        {
            new()
            {
                Name = "Name",
                Description = "Description",
                AgentOrIndependentContractorName = "Agent",
            }
        };

        var paymentsReceiving1000OrMore = new List<PersonReceiving1000OrMoreResponseDto>
        {
            new()
            {
                Name = "Name",
                Amount = 1,
                Notes = "Notes",
            }
        };

        var candidatesOrMeasures = new List<DisclosureWithoutPaymentReceived>
        {
            new ()
            {
                Id = 1,
                FilingId = 1,
                DisclosureStanceOnBallotMeasure = new DisclosureStanceOnBallotMeasure
                {
                    Id = 1,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    Position = "Support"
                }
            },
            new ()
            {
                Id = 2,
                FilingId = 1,
                DisclosureStanceOnCandidate = new DisclosureStanceOnCandidate
                {
                    Id = 1,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    Position = "Support"
                }
            }
        };

        var transactionsResponse = GenerateTransactions();

        _transactionSvcMock
            .GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentReceived, PaymentReceivedResponseDto>>())
            .Returns(paymentsReceived);

        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _disclosureWithoutPaymentReceivedRepositoryMock.FindAllDisclosureWithoutPaymentReceiveds(Arg.Any<long>()).Returns(Task.FromResult(candidatesOrMeasures));

        _transactionSvcMock.GetAllTransactionsForFiling(1).Returns(transactionsResponse);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(
            Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeResponseDto>>())
            .Returns(paymentsMade);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeByAgentOrIndependentContractorResponseDto>>())
            .Returns(paymentsMadeByAgent);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(
            Arg.Any<long>(), Arg.Any<Func<PersonReceiving1000OrMore, PersonReceiving1000OrMoreResponseDto>>())
            .Returns(paymentsReceiving1000OrMore);

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoCampaignStatementSubmission, DecisionsSmoCampaignStatementSubmissionResponse>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoCampaignStatementSubmission>(),
            checkRequiredFields: true).Returns(decisionsResponse);
        _servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoAttestation, DecisionsSmoCampaignStatementSubmissionResponse>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoAttestation>(),
            false).Returns(decisionsAttestationResponse);

        _servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoCampaignStatementSubmission, DecisionsSmoCampaignStatementSubmissionResponse>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoCampaignStatementSubmission>(),
            false).Returns(decisionsResponse);

        // Act
        var result = await _service.AttestStatementAsync(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Valid, Is.True);
    }

    [Test]
    public void AttestAsync_InValidRequest_NoFiling_ShouldThrowException()
    {
        // Arrange
        var filingId = 1;

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.AttestStatementAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo($"SMO CampaignStatement not found. Id={filingId}"));
    }

    [Test]
    public void AttestAsync_InvalidRequest_NoFilerUser_ShouldThrowException()
    {
        // Arrange
        var filingId = 1;
        var smo = GenerateSampleSmo(1, 1, "Test", FilingStatus.Draft.Id);

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.AttestStatementAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo($"No filer user found with filing. Id={filingId}"));
    }

    [Test]
    public void AttestAsync_InvalidRequest_NoRegistration_ShouldThrowException()
    {
        // Arrange
        var filingId = 1;
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.AttestStatementAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found with filer. Id={statement.FilerId}"));
    }

    [Test]
    public async Task AttestAsync_ValidRequest_AlreadyAccepted_ShouldExecuteSuccessfully()
    {
        // Arrange
        var filingId = 1;
        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Accepted.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Accepted.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);

        // Act
        var result = await _service.AttestStatementAsync(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Valid, Is.True);
    }

    [Test]
    public void AttestAsync_ValidRequest_HasNotStartedFiling_ShouldThrowError()
    {
        // Arrange
        var filingId = 1;
        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Accepted.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        statement.FilingSummaries = GenerateFilingSummaries();

        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.AttestStatementAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo($"Cannot submit this statement due to a {FilingSummaryStatus.NotStarted.Name} transaction type. Filing Id={filingId}"));
    }

    [Test]
    public void AttestAsync_ValidRequest_NotFoundRegistrationLinkToFiler_ShouldThrowError()
    {
        // Arrange
        var filingId = 1;
        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var smoContacts = new List<RegistrationRegistrationContact>
        {
            GenerateSampleRegistrationContact(1, 1001, "Treasurer", "Mr.", true, 1, "Test", "Test"),
            GenerateSampleRegistrationContact(1, 1002, "Secretary", "Ms.", false, 2, "Jane", "Smith"),
            GenerateSampleRegistrationContact(2, 1003, "Manager", "Dr.", true, 3, "Alice", "Johnson")
        };
        var paymentsReceived = new List<PaymentReceivedResponseDto>
        {
            new()
            {
                Name = "Name",
                CandidateOrMeasure = "CandidateOrMeasure",
                Position = "Position",
                TransactionDate = _dateNow,
                StanceOnCandidateDto = new StanceOnCandidateDto()
                {
                    CandidateId = 1L,
                },
                Amount = 100,
                Notes = ""
            }
        };
        var paymentsMade = new List<PaymentMadeResponseDto>
        {
            new()
            {
                Name = "Name",
                Description = "Description"
            }
        };
        var paymentsMadeByAgent = new List<PaymentMadeByAgentOrIndependentContractorResponseDto>
        {
            new()
            {
                Name = "Name",
                Description = "Description",
                AgentOrIndependentContractorName = "Agent",
            }
        };

        var paymentsReceiving1000OrMore = new List<PersonReceiving1000OrMoreResponseDto>
        {
            new()
            {
                Name = "Name",
                Amount = 1,
                Notes = "Notes",
            }
        };
        var transactionsResponse = new List<Transaction> { };
        var decisionsResponse = new DecisionsSmoCampaignStatementSubmissionResponse() { };

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(1).Returns(smoContacts);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentReceived, PaymentReceivedResponseDto>>())
        .Returns(paymentsReceived);

        _transactionSvcMock.GetAllTransactionsForFiling(1).Returns(transactionsResponse);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeResponseDto>>())
            .Returns(paymentsMade);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeByAgentOrIndependentContractorResponseDto>>())
            .Returns(paymentsMadeByAgent);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PersonReceiving1000OrMore, PersonReceiving1000OrMoreResponseDto>>())
            .Returns(paymentsReceiving1000OrMore);


        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoCampaignStatementSubmission, DecisionsSmoCampaignStatementSubmissionResponse>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoCampaignStatementSubmission>(),
            checkRequiredFields: true).Returns(decisionsResponse);

        _disclosureWithoutPaymentReceivedRepositoryMock.FindAllDisclosureWithoutPaymentReceiveds(1).Returns(new List<DisclosureWithoutPaymentReceived> { });

        _transactionSvcMock.GetAllTransactionsForFiling(1).Returns(transactionsResponse);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeResponseDto>>())
            .Returns(paymentsMade);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PersonReceiving1000OrMore, PersonReceiving1000OrMoreResponseDto>>())
            .Returns(paymentsReceiving1000OrMore);


        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoCampaignStatementSubmission, DecisionsSmoCampaignStatementSubmissionResponse>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoCampaignStatementSubmission>(),
            checkRequiredFields: true).Returns(decisionsResponse);

        _disclosureWithoutPaymentReceivedRepositoryMock.FindAllDisclosureWithoutPaymentReceiveds(1).Returns(new List<DisclosureWithoutPaymentReceived> { });

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.AttestStatementAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo($"Cannot found latest registration of this filer. FilerId={statement.FilerId}"));
    }

    [Test]
    public void AttestAsync_InvalidRequest_NoMatchedContact_ShouldThrowException()
    {
        // Arrange
        var filingId = 1;
        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        var smoContacts = new List<RegistrationRegistrationContact>
        {
            GenerateSampleRegistrationContact(1, 1002, "Secretary", "Ms.", false, 2, "Jane", "Smith"),
            GenerateSampleRegistrationContact(2, 1003, "Manager", "Dr.", true, 3, "Alice", "Johnson")
        };

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(1).Returns(smoContacts);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.AttestStatementAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo($"No contact matched with current user. Id={filerUser!.UserId}"));
    }
    #endregion

    #region SendForAttestationAsync
    [Test]
    public async Task SendForAttestationAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var filingId = 2;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long>()
        };

        var smoRegistration = GenerateSampleSmo(2, 1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var paymentsReceived = new List<PaymentReceivedResponseDto>
        {
            new()
            {
                Name = "Name",
                CandidateOrMeasure = "CandidateOrMeasure",
                Position = "Position",
                TransactionDate = _dateNow,
                StanceOnCandidateDto = null,
                Amount = 100,
                Notes = "",
                StanceOnBallotMeasureDto = new StanceOnBallotMeasureDto()
                {
                    BallotMeasureId = 1L,
                }
            }
        };

        var paymentsMade = new List<PaymentMadeResponseDto>
        {
            new()
            {
                Name = "Name",
                Description = "Description"
            }
        };

        var paymentsMadeByAgent = new List<PaymentMadeByAgentOrIndependentContractorResponseDto>
        {
            new()
            {
                Name = "Name",
                Description = "Description",
                AgentOrIndependentContractorName = "Agent",
            }
        };

        var paymentsReceiving1000OrMore = new List<PersonReceiving1000OrMoreResponseDto>
        {
            new()
            {
                Name = "Name",
                Amount = 1,
                Notes = "Notes",
            }
        };

        var transactionsResponse = new List<Transaction> { };
        var decisionsResponse = new DecisionsSmoCampaignStatementSubmissionResponse() { };

        var statement = GenerateSampleSmoCampaignStatement(2, 1, 1, FilingStatus.Draft.Id);
        statement.OriginalId = 1;
        statement.FilingSummaries[5].FilingId = 2;
        statement.FilingSummaries[5].FilingSummaryStatusId = FilingSummaryStatus.NothingToReport.Id;
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);
        _filerSvcMock.GetFilerRegistrationAsOfAsync(Arg.Any<long>(), Arg.Any<DateTime>()).Returns(Task.FromResult(1L));

        _servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoCampaignStatementSubmission, DecisionsSmoCampaignStatementSubmissionResponse>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoCampaignStatementSubmission>(),
            false).Returns(decisionsResponse);

        _transactionSvcMock
            .GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentReceived, PaymentReceivedResponseDto>>())
            .Returns(paymentsReceived);

        _transactionSvcMock.GetAllTransactionsForFiling(2).Returns(transactionsResponse);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(
            Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeResponseDto>>())
            .Returns(paymentsMade);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(Arg.Any<long>(), Arg.Any<Func<PaymentMade, PaymentMadeByAgentOrIndependentContractorResponseDto>>())
            .Returns(paymentsMadeByAgent);
        _transactionSvcMock.GetAllTransactionsByFilingAsync(
            Arg.Any<long>(), Arg.Any<Func<PersonReceiving1000OrMore, PersonReceiving1000OrMoreResponseDto>>())
            .Returns(paymentsReceiving1000OrMore);

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoCampaignStatementSubmission, DecisionsSmoCampaignStatementSubmissionResponse>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoCampaignStatementSubmission>(),
            checkRequiredFields: true).Returns(decisionsResponse);
        _disclosureWithoutPaymentReceivedRepositoryMock.FindAllDisclosureWithoutPaymentReceiveds(2).Returns(new List<DisclosureWithoutPaymentReceived> { });

        // Act
        var result = await _service.SendForAttestationAsync(filingId, request);

        //  Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Valid, Is.True);
    }

    [Test]
    public async Task SendForAttestationAsync_ValidRequest_AlreadyAccepted_ShouldExecuteSuccessfully()
    {
        // Arrange
        var filingId = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long>()
        };
        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Accepted.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Accepted.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);

        // Act
        var result = await _service.SendForAttestationAsync(filingId, request);

        //  Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Valid, Is.True);
    }

    [Test]
    public void SendForAttestationAsync_ValidRequest_HasNotStartedFiling_ShouldThrowError()
    {
        // Arrange
        var filingId = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long>()
        };
        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Accepted.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        statement.FilingSummaries = GenerateFilingSummaries();

        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.SendForAttestationAsync(filingId, request));
        Assert.That(ex.Message, Is.EqualTo($"Cannot submit this statement due to a {FilingSummaryStatus.NotStarted.Name} transaction type. Filing Id={filingId}"));
    }
    #endregion

    #region GetSmoCampaignStatementResponsibleOfficerContactsAsync
    [Test]
    public async Task GetSmoCampaignStatementResponsibleOfficerContactsAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var filingId = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long>()
        };

        var smoRegistration = GenerateSampleSmo(1, 1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();



        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _authorizationSvcMock.GetInitiatingUserId().Returns(1);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(1, 1).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(1).Returns(smoRegistration);
        _smoRegistrationSvcMock
            .GetResponsibleOfficerContactsAsync(1)
            .Returns(new List<SmoRegistrationContactDto>());
        // Act
        var result = await _service.GetSmoCampaignStatementResponsibleOfficerContactsAsync(filingId);

        //  Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<List<SmoRegistrationContactDto>>());
    }
    #endregion

    #region GetSmoCampaignStatementPendingItemsAsync
    [Test]
    public async Task TaskGetFilingPendingItemsAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var attestationMock = new Attestation();

        _attestationRepositoryMock
            .FindByFilingId(id)
            .Returns(attestationMock);

        // Act
        var result = await _service.GetSmoCampaignStatementPendingItemsAsync(id);

        //  Assert
        Assert.DoesNotThrowAsync(async () => await _service.GetSmoCampaignStatementPendingItemsAsync(id));
        Assert.That(result, Is.InstanceOf<List<PendingItemDto>>());

    }
    #endregion

    #region GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync
    [Test]
    public void GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync(id));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public async Task GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync_FoundFiling_ShouldResult()
    {
        // Arrange
        var id = 1;
        var candidatesOrMeasures = new List<DisclosureWithoutPaymentReceived>
        {
            new ()
            {
                Id = 1,
                FilingId = 1,
                DisclosureStanceOnBallotMeasure = new DisclosureStanceOnBallotMeasure
                {
                    Id = 1,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    Position = "Support"
                }
            },
            new ()
            {
                Id = 2,
                FilingId = 1,
                DisclosureStanceOnCandidate = new DisclosureStanceOnCandidate
                {
                    Id = 1,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    Position = "Support"
                }
            }
        };

        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _disclosureWithoutPaymentReceivedRepositoryMock.FindAllDisclosureWithoutPaymentReceiveds(Arg.Any<long>()).Returns(Task.FromResult(candidatesOrMeasures));

        // Act
        var result = await _service.GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync(id);

        //  Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<CandidateOrMeasureNotListedPaymentReceivedResponseDto>>());
        });
    }
    #endregion

    #region CreateSmoCampaignStatementTransactionAsync
    [Test]
    public void CreateSmoCampaignStatementTransactionAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 1m,
        };

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.CreateSmoCampaignStatementTransactionAsync(id, request));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [TestCaseSource(nameof(CreateSmoCampaignStatementTransactionAsyncTestCases))]
    public async Task CreateSmoCampaignStatementTransactionAsync_VariousRequests_ReturnsExpectedResult(
        TransactionDetailRequest request,
        Transaction transaction)
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, filerId, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));

        // Mock the internal methods based on request type
        if (request is PaymentReceivedRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentReceivedInformationDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentReceivedInformationDs>(),
                    Arg.Any<bool>())
                .Returns(new List<WorkFlowError>()); // No validation errors

            FilingContactSummary filingContactSummary = new()
            {
                Amount = 100,
                PreviouslyUnitemizedAmount = 100,
            };
            _ = _transactionSvcMock.GetSmoCampaignStatementFilingContactSummaryChanges(Arg.Any<Filing>(), Arg.Any<PaymentReceivedRequest>(), Arg.Any<long?>())
                .Returns(((PaymentReceived?)transaction, filingContactSummary, filingContactSummary));

            _ = _filingContactSummaryRepositoryMock.Create(Arg.Any<FilingContactSummary>())
                .Returns(new FilingContactSummary() { Id = 100, Amount = 100 });
            _ = _filingContactSummaryRepositoryMock.Update(Arg.Any<FilingContactSummary>())
                .Returns(new FilingContactSummary() { Id = 100, Amount = 100 });
            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PaymentReceived>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
        }
        else if (request is PaymentMadeByAgentOrIndependentContractorRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                    Arg.Any<bool>())
                .Returns(new List<WorkFlowError>()); // No validation errors

            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PaymentMade>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
            _ = _uploadFileSvcMock.UpdateUploadedFileRelationshipsAsync(Arg.Any<UpdateUploadedFileRelationshipsRequest>());
        }
        else if (request is PaymentMadeRequest)
        {
            _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                Arg.Any<bool>())
            .Returns(new List<WorkFlowError>()); // No validation errors

            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PaymentMade>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
        }
        else if (request is PersonReceiving1000OrMoreRequest)
        {
            _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PersonReceiving1000OrMore>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
        }

        // Act
        var result = await _service.CreateSmoCampaignStatementTransactionAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
        });
    }

    [TestCaseSource(nameof(CreateSmoCampaignStatementTransactionAsyncTestCases))]
    public async Task CreateSmoCampaignStatementTransactionAsync_VariousRequests_ReturnsDecisionsErrors(
    TransactionDetailRequest request,
    Transaction transaction)
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, filerId, 1, FilingStatus.Draft.Id);
        List<WorkFlowError> workFlowError = new()
        {
            new("UnitemizedPaymentsReceivedTotal", "ErrGlobal0008", "Validation", "Amount must be equal or greater than $0.00."),
        };
        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));

        // Mock the internal methods based on request type
        if (request is PaymentReceivedRequest)
        {
            FilingContactSummary filingContactSummary = new()
            {
                Amount = 100,
                PreviouslyUnitemizedAmount = 100,
            };
            _ = _transactionSvcMock.GetSmoCampaignStatementFilingContactSummaryChanges(Arg.Any<Filing>(), Arg.Any<PaymentReceivedRequest>(), Arg.Any<long?>())
                .Returns(((PaymentReceived?)transaction, filingContactSummary, filingContactSummary));
            _ = _filingContactSummaryRepositoryMock.Create(Arg.Any<FilingContactSummary>())
                .Returns(new FilingContactSummary() { Id = 100, Amount = 100 });
            _ = _filingContactSummaryRepositoryMock.Update(Arg.Any<FilingContactSummary>())
                .Returns(new FilingContactSummary() { Id = 100, Amount = 100 });

            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PaymentReceived>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
        }
        else if (request is PaymentMadeByAgentOrIndependentContractorRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                    Arg.Any<bool>())
                .Returns(workFlowError);

            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PaymentMade>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
        }
        else if (request is PaymentMadeRequest)
        {
            _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                Arg.Any<bool>())
            .Returns(workFlowError);

            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PaymentMade>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
        }
        else if (request is PersonReceiving1000OrMoreRequest)
        {
            _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                Arg.Any<bool>())
            .Returns(workFlowError);
            _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PersonReceiving1000OrMore>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction));
        }

        // Act
        var result = await _service.CreateSmoCampaignStatementTransactionAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            // Assert that Id is null once rest of Decisions have been integrated
        });
    }

    [Test]
    public async Task CreatePersonReceiving1000OrMoreTransactionAsync_WithValidationErrors_ReturnsErrorsInResponse()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;

        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 1000m,
            Notes = "Test",
        };

        var transaction = new PersonReceiving1000OrMore { Id = 99, FilerId = filerId, Amount = (Currency)1m };
        var expectedErrors = new List<WorkFlowError>
        {
            new("AmountPaid", "E001", "Validation", "Amount is invalid")
        };

        var statement = GenerateSampleSmoCampaignStatement(filingId, filerId, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindById(filingId).Returns(statement);

        _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PersonReceiving1000OrMore>(), filingId)
            .Returns(transaction);

        _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                DecisionsWorkflow.PersonsReceivingTransactionRuleSet,
                Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                true)
            .Returns(expectedErrors);

        // Act
        var result = await _service.CreateSmoCampaignStatementTransactionAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.False);
            Assert.That(result.Id, Is.Null);
            Assert.That(result.ValidationErrors, Is.EquivalentTo(expectedErrors));
        });
    }

    [Test]
    public async Task CreatePersonReceiving1000OrMoreTransactionAsync_WithNoValidationErrors_ReturnsEmptyList()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;

        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 1000m,
            Notes = "None"
        };

        var transaction = new PersonReceiving1000OrMore { Id = 123, FilerId = filerId, Amount = (Currency)1m };

        var statement = GenerateSampleSmoCampaignStatement(filingId, filerId, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindById(filingId).Returns(statement);

        _transactionSvcMock.CreateTransactionWrapper(Arg.Any<PersonReceiving1000OrMore>(), filingId)
            .Returns(transaction);

        _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                DecisionsWorkflow.PersonsReceivingTransactionRuleSet,
                Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                true)
            .Returns(new List<WorkFlowError>()); // empty list

        // Act
        var result = await _service.CreateSmoCampaignStatementTransactionAsync(filingId, request);

        // Assert
        Assert.That(result.ValidationErrors, Is.Empty);
        Assert.That(result.Valid, Is.True);
    }
    #endregion

    #region UpdateSmoCampaignStatementTransactionAsync
    [Test]
    public void UpdateSmoCampaignStatementTransactionAsync_NotFoundTransaction_ShouldThrowError()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 1L;
        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 1m,
        };

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request));
        Assert.That(error.Message, Is.EqualTo($"Transaction not found. Id={transactionId}"));
    }

    [TestCaseSource(nameof(UpdateSmoCampaignStatementTransactionAsyncTestCases))]
    public async Task UpdateSmoCampaignStatementTransactionAsync_VariousRequests_ReturnsExpectedResult(
        long transactionId,
        TransactionDetailRequest request,
        Transaction transaction)
    {
        // Arrange
        var filingId = 1L;

        // Mock the internal methods based on request type
        if (request is PaymentReceivedRequest)
        {
            List<long> validFilingIds = new() { 1 };

            PaymentReceivedRequest myRequest = new()
            {
                ContactId = 1L,
                Amount = (Currency)100,
                Position = "Support",
                Jurisdiction = "State",
                StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
                {
                    BallotMeasureId = 2L,
                }
            };
            request = myRequest;

            PaymentReceived oldPayment = new()
            {
                Id = 99L,
                ContactId = 99L,
                Amount = (Currency)200,
                DisclosureStanceOnBallotMeasure = new()
                {
                    Id = 99L,
                    CreatedBy = 0L,
                    ModifiedBy = 0L,
                    Position = "Oppose",
                    SubjectId = 99L,
                    FilingContactSummaryId = 99L,
                    FilingContactSummary = new()
                    {
                        Id = 99L,
                        Amount = 999,
                        PreviouslyUnitemizedAmount = 99,
                    }
                }

            };
            _ = _transactionSvcMock.GetSmoCampaignStatementFilingContactSummaryChanges(Arg.Any<Filing>(), Arg.Any<PaymentReceivedRequest>(), Arg.Any<long?>())
                .Returns((oldPayment, oldPayment.DisclosureStanceOnBallotMeasure.FilingContactSummary, oldPayment.DisclosureStanceOnBallotMeasure.FilingContactSummary));

            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentReceivedInformationDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentReceivedInformationDs>(),
                    Arg.Any<bool>())
                .Returns(new List<WorkFlowError>()); // No validation errors

            _ = _filingContactSummaryRepositoryMock.Update(Arg.Any<FilingContactSummary>()).Returns(oldPayment.DisclosureStanceOnBallotMeasure.FilingContactSummary);
            _ = _filingContactSummaryRepositoryMock.Create(Arg.Any<FilingContactSummary>()).Returns(oldPayment.DisclosureStanceOnBallotMeasure.FilingContactSummary);
            _ = _transactionRepositoryMock.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(transaction as PaymentReceived));
            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentReceived>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
            var filing = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
            _ = _filingRepositoryMock.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(filing);
        }
        else if (request is PaymentMadeByAgentOrIndependentContractorRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                    Arg.Any<bool>())
                .Returns(new List<WorkFlowError>()); // No validation errors

            _transactionRepositoryMock.FindByIdAndFilingId<PaymentMade>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PaymentMade));

            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentMade>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        }
        else if (request is PaymentMadeRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                    Arg.Any<bool>())
                .Returns(new List<WorkFlowError>()); // No validation errors

            _transactionRepositoryMock.FindByIdAndFilingId<PaymentMade>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PaymentMade));

            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentMade>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        }
        else if (request is PersonReceiving1000OrMoreRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                    Arg.Any<bool>())
                .Returns(new List<WorkFlowError>()); // No validation errors

            _transactionRepositoryMock.FindByIdAndFilingId<PersonReceiving1000OrMore>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PersonReceiving1000OrMore));
            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PersonReceiving1000OrMore>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        }

        // Act
        var result = await _service.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Id, Is.EqualTo(transactionId));
        });
    }

    [TestCaseSource(nameof(UpdateSmoCampaignStatementTransactionAsyncTestCases))]
    public async Task UpdateSmoCampaignStatementTransactionAsync_VariousRequests_ReturnsDecisionsErrors(
    long transactionId,
    TransactionDetailRequest request,
    Transaction transaction)
    {
        // Arrange
        var filingId = 1L;
        List<WorkFlowError> workFlowError = new()
        {
            new("UnitemizedPaymentsReceivedTotal", "ErrGlobal0008", "Validation", "Amount must be equal or greater than $0.00."),
        };

        // Mock the internal methods based on request type
        if (request is PaymentReceivedRequest)
        {
            _ = _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentReceivedInformationDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentReceivedInformationDs>(),
                    Arg.Any<bool>())
                .Returns(workFlowError);
            _ = _transactionRepositoryMock.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(transaction as PaymentReceived));
            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentReceived>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
            var filing = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
            _ = _filingRepositoryMock.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(filing);
        }
        else if (request is PaymentMadeByAgentOrIndependentContractorRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                    Arg.Any<bool>())
                .Returns(workFlowError);

            _transactionRepositoryMock.FindByIdAndFilingId<PaymentMade>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PaymentMade));

            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentMade>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        }
        else if (request is PaymentMadeRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPaymentsMadeBySmoAgentContractorDs>(),
                    Arg.Any<bool>())
                .Returns(workFlowError);

            _transactionRepositoryMock.FindByIdAndFilingId<PaymentMade>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PaymentMade));

            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentMade>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        }
        else if (request is PersonReceiving1000OrMoreRequest)
        {
            _decisionsSvcMock
                .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                    Arg.Any<DecisionsWorkflow>(),
                    Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                    Arg.Any<bool>())
                .Returns(workFlowError);

            _transactionRepositoryMock.FindByIdAndFilingId<PersonReceiving1000OrMore>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PersonReceiving1000OrMore));
            _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PersonReceiving1000OrMore>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        }

        // Act
        var result = await _service.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            // Assert that Id is null once rest of Decisions have been integrated
        });
    }

    [Test]
    public async Task UpdateSmoCampaignStatementTransactionAsync_PersonReceiving_WithValidationErrors_ReturnsErrorResponse()
    {
        // Arrange
        long filingId = 1;
        long transactionId = 999;

        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 500m,
            Notes = "Invalid update",
            ContactId = 9
        };

        var existingTransaction = new PersonReceiving1000OrMore
        {
            Id = transactionId,
            Amount = (Currency)1000m,
            FilerId = 1
        };

        var expectedErrors = new List<WorkFlowError>
    {
        new("AmountPaid", "ERR001", "Validation", "Amount is not sufficient")
    };

        _transactionRepositoryMock
            .FindByIdAndFilingId<PersonReceiving1000OrMore>(transactionId, filingId)
            .Returns(existingTransaction);

        _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                DecisionsWorkflow.PersonsReceivingTransactionRuleSet,
                Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                true)
            .Returns(expectedErrors);

        // Act
        var result = await _service.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.False);
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.ValidationErrors, Is.EqualTo(expectedErrors));
        });

        await _transactionSvcMock.DidNotReceiveWithAnyArgs()
            .UpdateTransactionWrapperAsync(Arg.Any<PersonReceiving1000OrMore>(), default, default, default);
    }

    [Test]
    public async Task UpdateSmoCampaignStatementTransactionAsync_PersonReceiving_NoValidationErrors_UpdatesTransaction()
    {
        // Arrange
        long filingId = 1;
        long transactionId = 123;

        var request = new PersonReceiving1000OrMoreRequest
        {
            Amount = 1200m,
            Notes = "Valid update",
            ContactId = 5
        };

        var existingTransaction = new PersonReceiving1000OrMore
        {
            Id = transactionId,
            Amount = (Currency)800m,
            FilerId = 1
        };

        _transactionRepositoryMock
            .FindByIdAndFilingId<PersonReceiving1000OrMore>(transactionId, filingId)
            .Returns(existingTransaction);

        _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPersonReceiving1000OrMoreDs, List<WorkFlowError>>(
                DecisionsWorkflow.PersonsReceivingTransactionRuleSet,
                Arg.Any<SmoCampaignStatementPersonReceiving1000OrMoreDs>(),
                true)
            .Returns(new List<WorkFlowError>());

        _transactionSvcMock
            .UpdateTransactionWrapperAsync(existingTransaction, 400m, filingId, request.ContactId)
            .Returns(Task.CompletedTask);

        // Act
        var result = await _service.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        await _transactionSvcMock.Received(1)
            .UpdateTransactionWrapperAsync(existingTransaction, 400m, filingId, request.ContactId);
    }

    #endregion

    #region GetSmoCampaignStatementTransactionAsync
    [Test]
    public void GetSmoCampaignStatementTransactionAsync_NotFoundTransactionType_ShouldThrowError()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 1L;
        var transactionType = "Test";

        // Act & Assert
        var error = Assert.ThrowsAsync<ArgumentException>(async () =>
            await _service.GetSmoCampaignStatementTransactionAsync(filingId, transactionId, transactionType));
        Assert.That(error.Message, Is.EqualTo($"Invalid transaction type: {transactionType}."));
    }

    [Test]
    public void GetSmoCampaignStatementTransactionAsync_NotFoundTransaction_ShouldThrowError()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 1L;
        var transactionType = TransactionType.PaymentReceived.Name;

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _service.GetSmoCampaignStatementTransactionAsync(filingId, transactionId, transactionType));
        Assert.That(error.Message, Is.EqualTo($"Transaction not found. Id={transactionId}"));
    }

    [TestCaseSource(nameof(GetSmoCampaignStatementTransactionTestCases))]
    public async Task GetSmoCampaignStatementTransactionAsync_VariousRequests_ReturnsExpectedResult(
        long transactionId,
        string transactionType,
        Transaction transaction)
    {
        // Arrange
        var filingId = 1L;

        // Mock the internal methods based on request type
        if (transactionType == TransactionType.PaymentReceived.Name)
        {
            _transactionRepositoryMock.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PaymentReceived));
        }
        else if (transactionType == TransactionType.PaymentMade.Name)
        {
            _transactionRepositoryMock.FindByIdAndFilingId<PaymentMade>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PaymentMade));
        }
        else if (transactionType == TransactionType.PersonReceiving1000OrMore.Name)
        {
            _transactionRepositoryMock.FindByIdAndFilingId<PersonReceiving1000OrMore>(Arg.Any<long>(), Arg.Any<long>())
                .Returns(Task.FromResult(transaction as PersonReceiving1000OrMore));
        }

        // Act
        var result = await _service.GetSmoCampaignStatementTransactionAsync(filingId, transactionId, transactionType);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionDetailResponseDto>());
            Assert.That(result.Id, Is.EqualTo(transactionId));
        });
    }
    #endregion

    #region CreateDisclosureWithoutPaymentReceivedAsync
    [Test]
    public async Task CreateDisclosureWithoutPaymentReceivedAsync_ValidStateCandidate_ShouldCreateNewRecord()
    {
        // Arrange
        var filingId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindSmoCampaignStatementById(filingId).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnCandidate = new StanceOnCandidateDto()
            {
                CandidateId = 1L,
            }
        };
        DisclosureWithoutPaymentReceived response = new()
        {
            Id = 1L,
        };

        _disclosureWithoutPaymentReceivedRepositoryMock.Create(Arg.Any<DisclosureWithoutPaymentReceived>()).Returns(response);

        // Act
        var result = await _service.CreateDisclosureWithoutPaymentReceivedAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Id, Is.EqualTo(response.Id));
        });
    }

    [Test]
    public void CreateDisclosureWithoutPaymentReceivedAsync_ValidStateBallotMeasure_ShouldCreateNewRecord()
    {
        // Arrange
        var filingId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindSmoCampaignStatementById(filingId).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                BallotMeasureId = 1L,
            }
        };
        DisclosureWithoutPaymentReceived response = new()
        {
            Id = 1L,
        };

        _disclosureWithoutPaymentReceivedRepositoryMock.Create(Arg.Any<DisclosureWithoutPaymentReceived>()).Returns(response);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _service.CreateDisclosureWithoutPaymentReceivedAsync(filingId, request));
        _ = _disclosureWithoutPaymentReceivedRepositoryMock.Received(1).Create(Arg.Any<DisclosureWithoutPaymentReceived>());
    }

    [Test]
    public async Task CreateDisclosureWithoutPaymentReceivedAsync_ValidLocalCandidate_ShouldCreateNewRecord()
    {
        // Arrange
        var filingId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindSmoCampaignStatementById(filingId).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnCandidate = new StanceOnCandidateDto()
            {
                FirstName = "Test",
                MiddleName = "Test",
                LastName = "Test",
                Jurisdiction = "Test",
                District = "Test",
                OfficeSought = "Test",
            }
        };
        DisclosureWithoutPaymentReceived response = new()
        {
            Id = 1L,
        };

        _disclosureWithoutPaymentReceivedRepositoryMock.Create(Arg.Any<DisclosureWithoutPaymentReceived>()).Returns(response);

        // Act
        var result = await _service.CreateDisclosureWithoutPaymentReceivedAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Id, Is.EqualTo(response.Id));
        });
    }

    [Test]
    public async Task CreateDisclosureWithoutPaymentReceivedAsync_ValidLocalBallotMeasure_ShouldCreateNewRecord()
    {
        // Arrange
        var filingId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindSmoCampaignStatementById(filingId).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                Jurisdiction = "Test",
                BallotLetter = "Test",
                Title = "Test",
            }
        };
        DisclosureWithoutPaymentReceived response = new()
        {
            Id = 1L,
        };

        _disclosureWithoutPaymentReceivedRepositoryMock.Create(Arg.Any<DisclosureWithoutPaymentReceived>()).Returns(response);

        // Act
        var result = await _service.CreateDisclosureWithoutPaymentReceivedAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Id, Is.EqualTo(response.Id));
        });
    }

    [Test]
    public void CreateDisclosureWithoutPaymentReceivedAsync_InvalidFiling_KeyException()
    {
        // Arrange
        var filingId = 1L;
        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                Jurisdiction = "Test",
                BallotLetter = "Test",
                Title = "Test",
            }
        };
        DisclosureWithoutPaymentReceived response = new()
        {
            Id = 1L,
        };

        _disclosureWithoutPaymentReceivedRepositoryMock.Create(Arg.Any<DisclosureWithoutPaymentReceived>()).Returns(response);

        // Act and Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.CreateDisclosureWithoutPaymentReceivedAsync(filingId, request));
        Assert.That(ex.Message, Is.EqualTo($"Filing not found. Id={filingId}"));
    }
    #endregion

    #region UpdateDisclosureWithoutPaymentReceivedAsync
    [Test]
    public void UpdateDisclosureWithoutPaymentReceivedAsync_InvalidFiling_KeyException()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 1L;
        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                Jurisdiction = "Test",
                BallotLetter = "Test",
                Title = "Test",
            }
        };

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.UpdateDisclosureWithoutPaymentReceivedAsync(filingId, transactionId, request));
        Assert.That(ex.Message, Is.EqualTo($"Filing not found. Id={filingId}"));
    }

    [Test]
    public void UpdateDisclosureWithoutPaymentReceivedAsync_NotFoundRecord_ShouldThrowError()
    {
        // Arrange
        var disclosureId = 999L;
        var filingId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);

        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                Jurisdiction = "Test",
                BallotLetter = "Test",
                Title = "Test",
            }
        };
        DisclosureWithoutPaymentReceived disclosure = new()
        {
            Id = disclosureId,
        };

        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));
        _disclosureWithoutPaymentReceivedRepositoryMock.GetDisclosureWithoutPaymentReceivedWithStanceById(Arg.Any<long>()).ReturnsNull();


        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.UpdateDisclosureWithoutPaymentReceivedAsync(filingId, disclosureId, request));
        Assert.That(ex.Message, Is.EqualTo($"Record not found. Id={disclosureId}"));
    }

    [Test]
    public async Task UpdateDisclosureWithoutPaymentReceivedAsync_ValidLocalBallotMeasure_ShouldUpdateExistingRecord()
    {
        // Arrange
        var disclosureId = 999L;
        var filingId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);

        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                Jurisdiction = "Test",
                BallotLetter = "Test",
                Title = "Test",
            }
        };
        DisclosureWithoutPaymentReceived disclosure = new()
        {
            Id = disclosureId,
        };

        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));
        _disclosureWithoutPaymentReceivedRepositoryMock
            .GetDisclosureWithoutPaymentReceivedWithStanceById(Arg.Any<long>()).Returns(Task.FromResult<DisclosureWithoutPaymentReceived?>(disclosure));

        _ = _disclosureWithoutPaymentReceivedRepositoryMock.Update(Arg.Any<DisclosureWithoutPaymentReceived>()).Returns(disclosure);

        // Act
        var result = await _service.UpdateDisclosureWithoutPaymentReceivedAsync(filingId, disclosureId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Id, Is.EqualTo(disclosure.Id));
        });
    }
    #endregion

    #region GetDisclosureWithoutPaymentReceivedAsync
    [Test]
    public void GetDisclosureWithoutPaymentReceivedByIdAsync_InvalidFiling_KeyException()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 1L;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetDisclosureWithoutPaymentReceivedByIdAsync(filingId, transactionId));
        Assert.That(ex.Message, Is.EqualTo($"Filing not found. Id={filingId}"));
    }

    [Test]
    public async Task GetDisclosureWithoutPaymentReceivedAsync_ValidLocalCandidate_ShouldReturnRecord()
    {
        // Arrange
        var disclosureId = 999L;
        var filingId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));

        DisclosureWithoutPaymentReceivedDto request = new()
        {
            Position = "Support",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                Jurisdiction = "Test",
                BallotLetter = "Test",
                Title = "Test",
            }
        };
        DisclosureWithoutPaymentReceived disclosure = new()
        {
            Id = disclosureId,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 1L,
                UnregisteredBallotMeasureSubject = new()
                {
                    Id = 1L,
                    Jurisdiction = "Test",
                    BallotNumberOrLetter = "Test",
                    FullTitleOrDescription = "Test",
                },
                Position = "Test",
                CreatedBy = 0L,
                ModifiedBy = 0L,
            },
            DisclosureStanceOnCandidate = new()
            {
                Id = 1L,
                UnregisteredCandidateSubject = new()
                {
                    Id = 1L,
                    Jurisdiction = "Test",
                    District = "Test",
                    FirstName = "Test",
                    MiddleName = "Test",
                    LastName = "Test",
                },
                Position = "Test",
                CreatedBy = 0L,
                ModifiedBy = 0L,
            }
        };

        _ = _disclosureWithoutPaymentReceivedRepositoryMock.GetDisclosureWithoutPaymentReceivedWithStanceById(Arg.Any<long>()).Returns(disclosure);

        // Act
        var result = await _service.GetDisclosureWithoutPaymentReceivedByIdAsync(1L, 1L);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(disclosureId));
    }

    [Test]
    public void GetDisclosureWithoutPaymentReceivedAsync_NotFoundRecord_ShouldThrowError()
    {
        // Arrange
        var filingId = 1L;
        var recordId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));

        _ = _disclosureWithoutPaymentReceivedRepositoryMock.GetDisclosureWithoutPaymentReceivedWithStanceById(Arg.Any<long>()).ReturnsNull();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.GetDisclosureWithoutPaymentReceivedByIdAsync(filingId, recordId));
        Assert.That(ex.Message, Is.EqualTo($"Record not found. Id={recordId}"));
    }
    #endregion

    #region ValidateDisclosurePaymentReceivedAsync
    [Test]
    public async Task ValidateDisclosurePaymentReceivedAsync_VariousRequests_ReturnsDecisionsErrors()
    {
        // Arrange
        var filingId = 1L;
        PaymentReceivedValidationRequestDto request = new()
        {
            Position = "Support",
        };
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        SmoCampaignStatementPaymentReceivedInformationDs decisionsInput = new();
        List<WorkFlowError> workFlowError = new()
        {
            new("UnitemizedPaymentsReceivedTotal", "ErrGlobal0008", "Validation", "Amount must be equal or greater than $0.00."),
        };

        // Mock the internal methods based on request type
        _ = _decisionsSvcMock.InitiateWorkflow<SmoCampaignStatementPaymentReceivedInformationDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementPaymentReceivedInformationDs>(),
                Arg.Any<bool>()).Returns(workFlowError);

        // Act
        var result = await _service.ValidatePaymentReceivedAsync(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(false));
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
        });
    }
    #endregion

    #region DeleteDisclosureWithoutPaymentReceivedAsync
    [Test]
    public void GetDisclosureWithoutPaymentReceivedByIdAsync_NotFoundFiling_ShouldThrowException()
    {
        // Arrange
        var filingId = 1L;
        var recordId = 1L;

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.DeleteDisclosureWithoutPaymentReceivedAsync(filingId, recordId));
        Assert.That(ex.Message, Is.EqualTo($"Filing not found. Id={filingId}"));
    }

    [Test]
    public void DeleteDisclosureWithoutPaymentReceivedAsync_NotFoundRecord_ShouldThrowError()
    {
        // Arrange
        var filingId = 1L;
        var recordId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));

        _ = _disclosureWithoutPaymentReceivedRepositoryMock.GetDisclosureWithoutPaymentReceivedWithStanceById(Arg.Any<long>()).ReturnsNull();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.DeleteDisclosureWithoutPaymentReceivedAsync(filingId, recordId));
        Assert.That(ex.Message, Is.EqualTo($"Record not found. Id={recordId}"));
    }

    [Test]
    public void DeleteDisclosureWithoutPaymentReceivedAsync_AlreadyDelete_ShouldDoNothingAndReturn()
    {
        // Arrange
        var filingId = 1L;
        var recordId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        DisclosureWithoutPaymentReceived disclosure = new()
        {
            Id = recordId,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 1L,
                UnregisteredBallotMeasureSubject = new()
                {
                    Id = 1L,
                    Jurisdiction = "Test",
                    BallotNumberOrLetter = "Test",
                    FullTitleOrDescription = "Test",
                },
                Position = "Test",
                CreatedBy = 0L,
                ModifiedBy = 0L,
            },
            DisclosureStanceOnCandidate = null,
            Active = false,
        };
        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));

        _ = _disclosureWithoutPaymentReceivedRepositoryMock
            .GetDisclosureWithoutPaymentReceivedWithStanceById(Arg.Any<long>())
            .Returns(Task.FromResult<DisclosureWithoutPaymentReceived?>(disclosure));

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _service.DeleteDisclosureWithoutPaymentReceivedAsync(filingId, recordId));
        _ = _disclosureWithoutPaymentReceivedRepositoryMock
            .Received(0)
            .UpdateProperty(Arg.Any<DisclosureWithoutPaymentReceived>(), Arg.Any<Expression<Func<DisclosureWithoutPaymentReceived, bool>>>(), Arg.Any<bool>());
    }

    [Test]
    public void DeleteDisclosureWithoutPaymentReceivedAsync_ValidRequest_ShouldSoftDeleteRecord()
    {
        // Arrange
        var filingId = 1L;
        var recordId = 1L;
        var statement = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        DisclosureWithoutPaymentReceived disclosure = new()
        {
            Id = recordId,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 1L,
                UnregisteredBallotMeasureSubject = new()
                {
                    Id = 1L,
                    Jurisdiction = "Test",
                    BallotNumberOrLetter = "Test",
                    FullTitleOrDescription = "Test",
                },
                Position = "Test",
                CreatedBy = 0L,
                ModifiedBy = 0L,
            },
            DisclosureStanceOnCandidate = null,
            Active = true,
        };
        _filingRepositoryMock.FindById(filingId).Returns(Task.FromResult<Filing?>(statement));

        _ = _disclosureWithoutPaymentReceivedRepositoryMock
            .GetDisclosureWithoutPaymentReceivedWithStanceById(Arg.Any<long>())
            .Returns(Task.FromResult<DisclosureWithoutPaymentReceived?>(disclosure));

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _service.DeleteDisclosureWithoutPaymentReceivedAsync(filingId, recordId));
        _ = _disclosureWithoutPaymentReceivedRepositoryMock
            .Received(1)
            .UpdateProperty(Arg.Any<DisclosureWithoutPaymentReceived>(), Arg.Any<Expression<Func<DisclosureWithoutPaymentReceived, bool>>>(), Arg.Any<bool>());
    }
    #endregion

    #region UpdateAmendmentExplanationAsync
    [Test]
    public void UpdateAmendmentExplanationAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var request = new FilingSummaryAmendmentExplanationRequest
        {
            AmendmentExplanation = "string",
        };

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.UpdateAmendmentExplanationAsync(id, request));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public void UpdateAmendmentExplanationAsync_NotFoundFilingSummary_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var request = new FilingSummaryAmendmentExplanationRequest
        {
            AmendmentExplanation = "string",
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        statement.FilingSummaries = statement.FilingSummaries.Where(x => x.FilingSummaryTypeId != FilingSummaryType.AmendmentExplanation.Id).ToList();
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.UpdateAmendmentExplanationAsync(id, request));
        Assert.That(error.Message, Is.EqualTo($"Filing summary not found. Type={FilingSummaryType.AmendmentExplanation.Name}"));
    }

    [Test]
    public void UpdateAmendmentExplanationAsync_ValidRequest_ShouldThrowDecisionsErrors()
    {
        // Arrange
        var id = 1L;
        var request = new FilingSummaryAmendmentExplanationRequest
        {
            AmendmentExplanation = "string",
        };
        var amendmentExplanationSummary = new FilingSummary
        {
            Id = 1,
            FilingId = id,
            PeriodAmount = 0m,
            ToDateAmount = 0m,
            FilingSummaryTypeId = FilingSummaryType.AmendmentExplanation.Id,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        statement.FilingSummaries.Add(amendmentExplanationSummary);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        List<WorkFlowError> workFlowError = new()
        {
            new("AmendmentExplanation", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionsAmendmentExplanation, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsAmendmentExplanation>(),
                Arg.Any<bool>())
            .Returns(workFlowError);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _service.UpdateAmendmentExplanationAsync(id, request));
    }

    [Test]
    public void UpdateAmendmentExplanationAsync_ValidRequest_ShouldUpdateSuccessfully()
    {
        // Arrange
        var id = 1L;
        var request = new FilingSummaryAmendmentExplanationRequest
        {
            AmendmentExplanation = "string",
        };
        var amendmentExplanationSummary = new FilingSummary
        {
            Id = 1,
            FilingId = id,
            PeriodAmount = 0m,
            ToDateAmount = 0m,
            FilingSummaryTypeId = FilingSummaryType.AmendmentExplanation.Id,
        };
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        statement.FilingSummaries.Add(amendmentExplanationSummary);
        _repositoriesDependencies.FilingRepository.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        _decisionsSvcMock
        .InitiateWorkflow<DecisionsAmendmentExplanation, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsAmendmentExplanation>(),
            Arg.Any<bool>())
        .Returns(new List<WorkFlowError>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _service.UpdateAmendmentExplanationAsync(id, request));
        _filingRepositoryMock.Received(1).Update(Arg.Any<SmoCampaignStatement>());
    }
    #endregion

    #region InitializeSmoCampaignStatementAmendmentAsync
    [Test]
    public void InitializeSmoCampaignStatementAmendmentAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.InitializeSmoCampaignStatementAmendmentAsync(id));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public void InitializeSmoCampaignStatementAmendmentAsync_InvalidStatus_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindSmoCampaignStatementByIdAsNoTracking(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));

        // Act & Assert
        var error = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.InitializeSmoCampaignStatementAmendmentAsync(id));
        Assert.That(error.Message, Is.EqualTo($"Filing status is not valid for starting a new amendment."));
    }

    [Test]
    public void InitializeSmoCampaignStatementAmendmentAsync_NotValidStatus_ShouldThrowError()
    {
        // Arrange
        var id = 2;
        var statement = GenerateSampleSmoCampaignStatement(id, 1, 1, FilingStatus.Accepted.Id);
        var activeAmendmentStatement = GenerateSampleSmoCampaignStatement(3, 1, 1, FilingStatus.Draft.Id);
        _filingRepositoryMock.FindSmoCampaignStatementByIdAsNoTracking(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _filingRepositoryMock.FindActiveSmoCampaignStatementAmendment(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(activeAmendmentStatement));

        // Act & Assert
        var error = Assert.ThrowsAsync<InvalidOperationException>(async () => await _service.InitializeSmoCampaignStatementAmendmentAsync(id));
        Assert.That(error.Message, Is.EqualTo($"There is an existing active amendment for this statement. Id={id}"));
    }

    [Test]
    public async Task InitializeSmoCampaignStatementAmendmentAsync_ValidRequest_ShouldReturnNewCreatedId()
    {
        // Arrange
        var id = 1;
        var statement = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Accepted.Id);
        var expected = GenerateSampleSmoCampaignStatement(2, 1, 1, FilingStatus.Draft.Id);
        var filingTransactions = GenerateFilingTransactions();
        var previousStatements = GenerateSampleSmoCampaignStatements();
        var contactSummaries = GenerateContactSummaries();

        _filingRepositoryMock.FindSmoCampaignStatementByIdAsNoTracking(Arg.Any<long>()).Returns(Task.FromResult<SmoCampaignStatement?>(statement));
        _transactionRepositoryMock.FindAllFilingTransactionsByFilingId(Arg.Any<long>()).Returns(Task.FromResult(filingTransactions));
        _filingRepositoryMock.FindActiveSmoCampaignStatementAmendment(Arg.Any<long>()).ReturnsNull();
        _filingRepositoryMock.Create(Arg.Any<SmoCampaignStatement>()).Returns(Task.FromResult<Filing>(expected));
        _filingRepositoryMock.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>()).Returns(Task.FromResult(previousStatements));
        _filingContactSummaryRepositoryMock.FindAllByFilingId(Arg.Any<long>()).Returns(Task.FromResult(contactSummaries));

        // Act
        var result = await _service.InitializeSmoCampaignStatementAmendmentAsync(id);

        // Assert
        Assert.That(result, Is.GreaterThanOrEqualTo(0));
        _ = _filingRepositoryMock.Received(1).Create(Arg.Any<SmoCampaignStatement>());
        _ = _filingContactSummaryRepositoryMock.Received(1).AddFilingContactSummaries(Arg.Any<List<FilingContactSummary>>());
        _ = _filingRepositoryMock.Received(1).Update(Arg.Any<SmoCampaignStatement>());
    }
    #endregion

    #region IsSmoCampaignStatementAmendmentAsync
    [Test]
    public void IsSmoCampaignStatementAmendmentAsync_NotFoundFiling_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        _filingRepositoryMock.FindById(Arg.Any<long>()).ReturnsNull();

        // Act & Assert
        var error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _service.IsSmoCampaignStatementAmendmentAsync(id));
        Assert.That(error.Message, Is.EqualTo($"Filing not found. Id={id}"));
    }

    [Test]
    public async Task IsSmoCampaignStatementAmendmentAsync_ValidRequest_ShouldReturnFlag()
    {
        // Arrange
        var id = 1;
        var statement = GenerateSampleSmoCampaignStatement(id, 1L, 1L, 1L);

        _filingRepositoryMock.FindById(Arg.Any<long>()).Returns(Task.FromResult<Filing?>(statement));

        // Act
        var result = await _service.IsSmoCampaignStatementAmendmentAsync(id);

        // Assert
        Assert.That(result, Is.True);
    }
    #endregion

    #region ValidatePersonReceivingOfficerAsync
    [Test]
    public async Task ValidatePersonReceivingOfficerAsync_InvalidRequest_ShouldReturnResponseErrors()
    {
        // Arrange
        var id = 1;
        var request = new PersonReceiving1000ValidationRequest
        {
            OfficerId = null,
        };
        var validationErrors = new List<WorkFlowError>
        {
            new("Test", "errorCode", "Validation", "message")
        };
        _decisionsSvcMock.InitiateWorkflow<SmoCampaignStatementOfficerDs, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<SmoCampaignStatementOfficerDs>(), Arg.Any<bool>())
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _service.ValidatePersonReceivingOfficerAsync(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
    }

    [Test]
    public async Task ValidatePersonReceivingOfficerAsync_ValidRequest_ShouldReturnResponseWithoutErrors()
    {
        // Arrange
        var id = 1;
        var request = new PersonReceiving1000ValidationRequest
        {
            OfficerId = 1L,
        };
        _decisionsSvcMock.InitiateWorkflow<SmoCampaignStatementOfficerDs, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<SmoCampaignStatementOfficerDs>(), Arg.Any<bool>())
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _service.ValidatePersonReceivingOfficerAsync(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }
    #endregion

    #region ResolveDecisionsWorkflowPostSubmission
    [TestCaseSource(nameof(ResolveDecisionsWorkflowPostSubmissionTestCases))]
    public void ResolveDecisionsWorkflowPostSubmission_ValidRequest_ShouldReturnCorrectDecisionWorkflow(bool isAmending, bool isAttesting, DecisionsWorkflow expected)
    {
        // Act
        var methodInfo = typeof(SmoCampaignStatementSvc).GetMethod("ResolveDecisionsWorkflowPostSubmission", BindingFlags.NonPublic | BindingFlags.Static)!;

        // Assert
        var result = methodInfo.Invoke(_service, [isAmending, isAttesting]);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<DecisionsWorkflow>());
            Assert.That(result, Is.EqualTo(expected));
        });
    }
    #endregion

    #region ValidateCandidateOrMeasureNotListedP1Async
    [Test]
    public async Task ValidateCandidateOrMeasureNotListedP1Async_InvalidRequest_ShouldReturnResponseErrors()
    {
        // Arrange
        var id = 1;
        var request = new CandidateOrMeasureNotListedValidationRequest
        {
            CandidateOrMeasure = "",
            Jurisdiction = "",
            Position = ""
        };
        var validationErrors = new List<WorkFlowError>
        {
            new("Test", "errorCode", "Validation", "message")
        };
        _decisionsSvcMock.InitiateWorkflow<SmoCampaignStatementCandidateAndMeasureNotListedEntryDs, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<SmoCampaignStatementCandidateAndMeasureNotListedEntryDs>(), Arg.Any<bool>())
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _service.ValidateCandidateOrMeasureNotListedP1Async(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
    }

    [Test]
    public async Task ValidateCandidateOrMeasureNotListedP1Async_ValidRequest_ShouldReturnResponseWithoutErrors()
    {
        // Arrange
        var id = 1;
        var request = new CandidateOrMeasureNotListedValidationRequest
        {
            CandidateOrMeasure = "Candidate",
            Jurisdiction = "Local",
            Position = "Support"
        };
        _decisionsSvcMock.InitiateWorkflow<SmoCampaignStatementCandidateAndMeasureNotListedEntryDs, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<SmoCampaignStatementCandidateAndMeasureNotListedEntryDs>(), Arg.Any<bool>())
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _service.ValidateCandidateOrMeasureNotListedP1Async(id, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }
    #endregion

    #region ValidatePaymentReceivedCumulativeAmountAsync
    [Test]
    public async Task GetFilingContactSummaryChanges_ValidRequest_ShouldReturnResponseWithoutErrors()
    {
        // Arrange
        _decisionsSvcMock.InitiateWorkflow<SmoCampaignStatementOfficerDs, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<SmoCampaignStatementOfficerDs>(), Arg.Any<bool>())
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        PaymentReceivedRequest request = new()
        {
            ContactId = 1L,
            Amount = (Currency)100,
            Position = "Support",
            Jurisdiction = "State",
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                BallotMeasureId = 2L,
            }
        };
        SmoCampaignStatement filing = GenerateSampleSmoCampaignStatement(1, 1, 1, FilingStatus.Accepted.Id);
        _ = _filingRepositoryMock.FindById(Arg.Any<long>()).Returns(filing);

        PaymentReceived oldPayment = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        FilingContactSummary filingContactSummary = new()
        {
            Amount = 100,
            PreviouslyUnitemizedAmount = 100,
        };

        _ = _transactionSvcMock.GetSmoCampaignStatementFilingContactSummaryChanges(Arg.Any<Filing>(), Arg.Any<PaymentReceivedRequest>(), Arg.Any<long?>())
            .Returns((oldPayment, filingContactSummary, filingContactSummary));

        // Act
        var result = await _service.GetPaymentReceivedCumulativeAmountAsync(1L, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<PaymentsReceivedCumulativeAmountDto>());
        });
    }
    #endregion

    [Test]
    public async Task UpdateSmoCampaignStatementTransactionAsync_BallotUpdateSummary_ReturnsDecisionsErrors()
    {
        var transactionId = 1L;
        PaymentReceivedRequest request = new()
        {
            Amount = (Currency)1,
            Position = "Position",
            Jurisdiction = "Local",
            ContactId = 1L,
            UnitemizedAmount = 500,
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
            {
                Jurisdiction = "Name",
                BallotLetter = "Letter",
                Title = "Title",
            }
        };
        PaymentReceived existingTransaction = new()
        {
            Id = 1,
            Amount = (Currency)1,
            FilingTransactions = {
                new FilingTransaction()
                {
                    Id = 1,
                    Filing = new Filing()
                    {
                        Id = 1,
                        StatusId = 1,
                        FilingPeriodId = 1,
                    }
                }
            },
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 1L,
                Position = "Support",
                CreatedBy = 0L,
                ModifiedBy = 0L,
                FilingContactSummary = new()
                {
                    Id = 1L,
                    Amount = 100,
                }
            }
        };

        PaymentReceived? oldPayment = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Id = 99L,
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };

        _ = _transactionSvcMock.GetSmoCampaignStatementFilingContactSummaryChanges(Arg.Any<Filing>(), Arg.Any<PaymentReceivedRequest>(), Arg.Any<long?>())
            .Returns((oldPayment, oldPayment.DisclosureStanceOnBallotMeasure.FilingContactSummary, oldPayment.DisclosureStanceOnBallotMeasure.FilingContactSummary));

        // Arrange
        var filingId = 1L;
        var workFlowError = new List<WorkFlowError>();

        // Mock the internal methods based on request type
        _ = _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPaymentReceivedInformationDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementPaymentReceivedInformationDs>(),
                Arg.Any<bool>())
            .Returns(workFlowError);

        _ = _transactionRepositoryMock.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>()).Returns(existingTransaction);
        _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentReceived>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        _ = _filingContactSummaryRepositoryMock.Update(Arg.Any<FilingContactSummary>()).Returns(existingTransaction.DisclosureStanceOnBallotMeasure.FilingContactSummary);
        var filing = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _ = _filingRepositoryMock.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(filing);

        // Act
        var result = await _service.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            // Assert that Id is null once rest of Decisions have been integrated
        });
    }

    [Test]
    public async Task UpdateSmoCampaignStatementTransactionAsync_CandidateCreateSummary_ReturnsDecisionsErrors()
    {
        var transactionId = 1L;
        PaymentReceivedRequest request = new()
        {
            Amount = (Currency)1,
            Position = "Position",
            Jurisdiction = "Local",
            ContactId = 1L,
            UnitemizedAmount = 500,
            StanceOnCandidate = new StanceOnCandidateDto()
            {
                CandidateId = 3,
            }
        };
        PaymentReceived transaction = new()
        {
            Id = 1,
            Amount = (Currency)1,
            ContactId = 3L,
            FilingTransactions = {
                new FilingTransaction()
                {
                    Id = 1,
                    Filing = new Filing()
                    {
                        Id = 1,
                        StatusId = 1,
                        FilingPeriodId = 1,
                    }
                }
            },
            DisclosureStanceOnCandidate = new()
            {
                Id = 1L,
                Position = "Support",
                CreatedBy = 0L,
                ModifiedBy = 0L,
                FilingContactSummary = new()
                {
                    Id = 1L,
                    Amount = 100,
                }
            }
        };

        PaymentReceived oldPayment = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnCandidate = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };

        _ = _transactionSvcMock.GetSmoCampaignStatementFilingContactSummaryChanges(Arg.Any<Filing>(), Arg.Any<PaymentReceivedRequest>(), Arg.Any<long?>())
            .Returns((oldPayment, oldPayment.DisclosureStanceOnCandidate.FilingContactSummary, oldPayment.DisclosureStanceOnCandidate.FilingContactSummary));

        // Arrange
        var filingId = 1L;
        var workFlowError = new List<WorkFlowError>();

        // Mock the internal methods based on request type
        _ = _decisionsSvcMock
            .InitiateWorkflow<SmoCampaignStatementPaymentReceivedInformationDs, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<SmoCampaignStatementPaymentReceivedInformationDs>(),
                Arg.Any<bool>())
            .Returns(workFlowError);

        _ = _transactionRepositoryMock.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>()).Returns(transaction);
        _ = _transactionSvcMock.UpdateTransactionWrapperAsync(Arg.Any<PaymentReceived>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long>());
        _ = _filingContactSummaryRepositoryMock.Create(Arg.Any<FilingContactSummary>()).Returns(transaction.DisclosureStanceOnCandidate.FilingContactSummary);
        var filing = GenerateSampleSmoCampaignStatement(filingId, 1, 1, FilingStatus.Draft.Id);
        _ = _filingRepositoryMock.FindSmoCampaignStatementById(Arg.Any<long>()).Returns(filing);

        // Act
        var result = await _service.UpdateSmoCampaignStatementTransactionAsync(filingId, transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TransactionResponseDto>());
            // Assert that Id is null once rest of Decisions have been integrated
        });
    }

    #region CloneFilingContactSummaries
    [Test]
    public void CloneFilingContactSummaries_EmptyContactSummaries_ShouldDoNothingAndReturn()
    {
        // Act
        var filingId = 1L;
        var contactSummaries = new List<FilingContactSummary>();
        var filingContactSummaryMap = new Dictionary<long, FilingContactSummary>();

        // Arrange
        PrivateMemberAccessor.InvokePrivateStatic(_service, "CloneFilingContactSummaries", [filingId, contactSummaries, filingContactSummaryMap]);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(contactSummaries, Is.Empty);
            Assert.That(filingContactSummaryMap, Is.Empty);
        });
    }
    #endregion

    #region CloneDisclosureStanceOnBallotMeasure
    [Test]
    public void CloneDisclosureStanceOnBallotMeasure_ExistOriginalContactSummary_ShouldMapContactSummaryWhenClone()
    {
        // Act
        var measure = new DisclosureStanceOnBallotMeasure
        {
            Id = 1,
            Position = "Support",
            CreatedBy = 0,
            ModifiedBy = 0,
            FilingContactSummaryId = 1,
        };
        var contactSummary = new FilingContactSummary
        {
            Id = 1,
            Amount = 40m,
        };
        var filingContactSummaryMap = new Dictionary<long, FilingContactSummary>
        {
            {1 , contactSummary}
        };

        // Arrange
        PrivateMemberAccessor.InvokePrivateStatic(_service, "CloneDisclosureStanceOnBallotMeasure", [measure, filingContactSummaryMap]);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(measure.FilingContactSummary, Is.Not.Null);
            Assert.That(measure.FilingContactSummary, Is.EqualTo(contactSummary));
        });
    }
    #endregion

    #region CloneDisclosureStanceOnCandidate
    [Test]
    public void CloneDisclosureStanceOnCandidate_ExistOriginalContactSummary_ShouldMapContactSummaryWhenClone()
    {
        // Act
        var candidate = new DisclosureStanceOnCandidate
        {
            Id = 1,
            Position = "Support",
            CreatedBy = 0,
            ModifiedBy = 0,
            FilingContactSummaryId = 1,
        };
        var contactSummary = new FilingContactSummary
        {
            Id = 1,
            Amount = 40m,
        };
        var filingContactSummaryMap = new Dictionary<long, FilingContactSummary>
        {
            {1 , contactSummary}
        };

        // Arrange
        PrivateMemberAccessor.InvokePrivateStatic(_service, "CloneDisclosureStanceOnCandidate", [candidate, filingContactSummaryMap]);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(candidate.FilingContactSummary, Is.Not.Null);
            Assert.That(candidate.FilingContactSummary, Is.EqualTo(contactSummary));
        });
    }
    #endregion

    #region SendUserNotificationsAsync
    [Test]
    public async Task SendUserNotificationsAsync_NullNotification_ShouldDoNothingAndReturn()
    {
        // Act
        var filingId = 1L;
        var filerId = 1L;
        var userIds = new List<long?>();

        // Arrange
        await PrivateMemberAccessor.InvokePrivateAsync(_service, "SendUserNotificationsAsync", [filingId, filerId, null, userIds]);

        // Assert
        _ = _notificationSvcMock.Received(0).SendUserNotification(Arg.Any<SendUserNotificationRequest>());
    }

    [Test]
    public async Task SendUserNotificationsAsync_HasNotification_ShouldTriggerNotificationSvc()
    {
        // Act
        var filingId = 1L;
        var filerId = 1L;
        var notifications = new List<NotificationTrigger>
        {
            new()
            {
                SendNotification = true,
                NotificationTemplateId = 1L,
            }
        };
        var userIds = new List<long?> { null, 1L };

        // Arrange
        await PrivateMemberAccessor.InvokePrivateAsync(_service, "SendUserNotificationsAsync", [filingId, filerId, notifications, userIds]);

        // Assert
        _ = _notificationSvcMock.Received().SendUserNotification(Arg.Any<SendUserNotificationRequest>());
    }
    #endregion

    #region Private

    private static IEnumerable<object[]> GetTransactionSummaryAsyncTestCases()
    {
        yield return new object[]
        {   FilingSummaryType.PaymentReceivedSummary.Id,
            new List<PaymentReceived>
            {
                (PaymentReceived)GenerateTransactions().FirstOrDefault(x => x.Id == 1)!
            },
        };
        yield return new object[]
        {   FilingSummaryType.PaymentMadeSummary.Id,
            new List<PaymentMade>
            {
                (PaymentMade)GenerateTransactions().FirstOrDefault(x => x.Id == 2)!
            }
        };
        yield return new object[]
        {   FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id,
            new List<PaymentMade>
            {
                (PaymentMade)GenerateTransactions().FirstOrDefault(x => x.Id == 3)!
            }
        };
        yield return new object[]
        {   FilingSummaryType.PersonReceiving1000OrMoreSummary.Id,
            new List<PersonReceiving1000OrMore>
            {
                (PersonReceiving1000OrMore) GenerateTransactions().FirstOrDefault(x => x.Id == 4)!
            }
        };
        yield return new object[]
        {   999,
            Array.Empty<object>(),
        };
    }

    private static IEnumerable<object[]> CreateSmoCampaignStatementTransactionAsyncTestCases()
    {
        yield return new object[]
        {
            new PaymentReceivedRequest
            {
                Amount = (Currency)1,
                Position = "Position",
                Jurisdiction = "State",
                StanceOnCandidate = new StanceOnCandidateDto()
                {
                    FirstName = "First",
                    LastName = "Last",
                    MiddleName = "Middle",
                    Jurisdiction = "LocalJurisdiction",
                    District = "LocalDistrict",
                    OfficeSought = "LocalOffice",
                }
            },
            new PaymentReceived { Id = 1, Amount = (Currency)1 }
        };
        yield return new object[]
        {
            new PaymentMadeByAgentOrIndependentContractorRequest
            {
                Amount = (Currency)1,
                AgentOrIndependentContractorName = "Agent",
                Description = "", CodeId = 1,
                IsPaidByAgentOrIndependentContractor = true,
                AttachedFileGuidsJson = JsonConvert.SerializeObject(new List<string> { Guid.NewGuid().ToString() }),
            },
            new PaymentMade { Id = 2, Amount = (Currency)1, ExpenditureCodeDescription = "", ExpenditureCodeId = 1, AgentOrIndependentContractorName = "Agent" }
        };
        yield return new object[]
        {
            new PaymentMadeRequest { Amount = (Currency)1, Description = "", CodeId = 1 },
            new PaymentMade { Id = 3, Amount = (Currency)1, ExpenditureCodeDescription = "", ExpenditureCodeId = 1 }
        };
        yield return new object[]
        {
            new PersonReceiving1000OrMoreRequest { Amount = (Currency)1 },
            new PersonReceiving1000OrMore { Id = 4, Amount = (Currency)1 }
        };
    }

    private static IEnumerable<object[]> GetSmoCampaignStatementTransactionTestCases()
    {
        yield return new object[]
        {
            1L,
            TransactionType.PaymentReceived.Name,
            new PaymentReceived { Id = 1, Amount = (Currency)1 }
        };
        yield return new object[]
        {
            2L,
            TransactionType.PaymentMade.Name,
            new PaymentMade { Id = 2, Amount = (Currency)1, ExpenditureCodeDescription = "", ExpenditureCodeId = 1, AgentOrIndependentContractorName = "Agent" }
        };
        yield return new object[]
        {
            3L,
            TransactionType.PaymentMade.Name,
            new PaymentMade { Id = 3, Amount = (Currency)1, ExpenditureCodeDescription = "", ExpenditureCodeId = 1 }
        };
        yield return new object[]
        {
            4L,
            TransactionType.PersonReceiving1000OrMore.Name,
            new PersonReceiving1000OrMore { Id = 4, Amount = (Currency)1 }
        };
    }

    private static IEnumerable<object[]> UpdateSmoCampaignStatementTransactionAsyncTestCases()
    {
        yield return new object[]
        {
            1L,
            new PaymentReceivedRequest {
                Amount = (Currency)1,
                Position = "Position",
                Jurisdiction = "Local",
                StanceOnBallotMeasure = new StanceOnBallotMeasureDto()
                {
                    Jurisdiction = "Name",
                    BallotLetter = "Letter",
                    Title = "Title",
                }
            },
            new PaymentReceived {
                Id = 1,
                Amount = (Currency)1,
                FilingTransactions = {
                    new FilingTransaction()
                    {
                        Id = 1,
                        Filing = new Filing()
                        {
                            Id = 1,
                            StatusId = 1,
                            FilingPeriodId = 1,
                        }
                    }
                }
            }
        };
        yield return new object[]
        {
            2L,
            new PaymentMadeByAgentOrIndependentContractorRequest { Amount = (Currency)1, AgentOrIndependentContractorName = "Agent", Description = "", CodeId = 1, IsPaidByAgentOrIndependentContractor = true },
            new PaymentMade { Id = 2, Amount = (Currency)1, ExpenditureCodeDescription = "", ExpenditureCodeId = 1, AgentOrIndependentContractorName = "Agent" }
        };
        yield return new object[]
        {
            3L,
            new PaymentMadeRequest { Amount = (Currency)1, Description = "", CodeId = 1 },
            new PaymentMade { Id = 3, Amount = (Currency)1, ExpenditureCodeDescription = "", ExpenditureCodeId = 1 }
        };
        yield return new object[]
        {
            4L,
            new PersonReceiving1000OrMoreRequest { Amount = (Currency)1 },
            new PersonReceiving1000OrMore { Id = 4, Amount = (Currency)1 }
        };
    }

    private static IEnumerable<object[]> ResolveDecisionsWorkflowPostSubmissionTestCases()
    {
        yield return new object[]
        {
            true, true, DecisionsWorkflow.SmoCampaignStatementPostSubmissionAmendmentRuleSet,
        };
        yield return new object[]
        {
            true, false, DecisionsWorkflow.SmoCampaignStatementSendForAttestationAmendmentRuleSet,
        };
        yield return new object[]
        {
            false, true, DecisionsWorkflow.SmoCampaignStatementPostSubmissionRuleSet,
        };
        yield return new object[]
        {
            false, false, DecisionsWorkflow.SmoCampaignStatementSendForAttestationRuleSet,
        };
    }

    private static SmoCampaignStatement GenerateSampleSmoCampaignStatement(long filingId, long filerId, long filingPeriodId, long statusId, long parentId = 2)
    {
        return new SmoCampaignStatement
        {
            Id = filingId,
            FilerId = filerId,
            FilingPeriodId = filingPeriodId,
            Version = 0,
            StatusId = statusId,
            ParentId = parentId,
            FilingSummaries = new List<FilingSummary>
            {
                new()
                {
                    Id = 1,
                    FilingSummaryTypeId = FilingSummaryType.PaymentReceivedSummary.Id,
                    PeriodAmount = 100m,
                    ToDateAmount = 100m,
                    UnitemizedAmount = 100m,
                },
                new()
                {
                    Id = 2,
                    FilingSummaryTypeId = FilingSummaryType.PaymentMadeSummary.Id,
                    PeriodAmount = 100m,
                    ToDateAmount = 100m,
                    UnitemizedAmount = 100m,
                },
                new()
                {
                    Id = 3,
                    FilingSummaryTypeId = FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id,
                    PeriodAmount = 100m,
                    ToDateAmount = 100m,
                },
                new()
                {
                    Id = 4,
                    FilingSummaryTypeId = FilingSummaryType.PersonReceiving1000OrMoreSummary.Id,
                    PeriodAmount = 100m,
                    ToDateAmount = 100m,
                },
                new()
                {
                    Id = 5,
                    FilingSummaryTypeId = FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Id,
                    PeriodAmount = 0m,
                    ToDateAmount = 0m,
                },
                new()
                {
                    Id = 6,
                    FilingSummaryTypeId = FilingSummaryType.AmendmentExplanation.Id,
                    PeriodAmount = 0m,
                    ToDateAmount = 0m,
                }
            },
            DisclosureWithoutPaymentReceiveds = new List<DisclosureWithoutPaymentReceived>
            {
                new ()
                {
                    Id = 1,
                    DisclosureStanceOnCandidate = new ()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        Position = "Position",
                        UnregisteredCandidateSubject = new ()
                        {
                            FirstName = "First Name",
                            Jurisdiction = "Jurisdiction",
                            LastName = "Last Name"
                        }
                    },
                },
                new ()
                {
                    Id = 2,
                    DisclosureStanceOnBallotMeasure = new ()
                    {
                        CreatedBy = 0,
                        ModifiedBy = 0,
                        Position = "Position",
                        UnregisteredBallotMeasureSubject = new ()
                        {
                            Jurisdiction = "Jurisdiction",
                        }
                    }
                }
            },
        };
    }

    private static List<SmoCampaignStatement> GenerateSampleSmoCampaignStatements()
    {
        return new List<SmoCampaignStatement>
        {
            new()
            {
                Id = 10,
                FilerId = 1,
                FilingPeriodId = 1,
                Version = 0,
                StatusId = FilingStatus.Accepted.Id,
                FilingTypeId = FilingType.SlateMailerOrganization.Id,
                FilingSummaries = new List<FilingSummary>
                {
                    GenerateSmoFilingSummaryDto(1, FilingSummaryType.PaymentMadeSummary.Id, 100),
                    GenerateSmoFilingSummaryDto(1, FilingSummaryType.PaymentReceivedSummary.Id, 100),
                }
            },
            new()
            {
                Id = 11,
                FilerId = 1,
                FilingPeriodId = 2,
                Version = 0,
                StatusId = FilingStatus.Accepted.Id,
                FilingTypeId = FilingType.SlateMailerOrganization.Id,
                FilingSummaries = new List<FilingSummary>
                {
                    GenerateSmoFilingSummaryDto(1, FilingSummaryType.PaymentMadeSummary.Id, 100),
                    GenerateSmoFilingSummaryDto(1, FilingSummaryType.PaymentReceivedSummary.Id, 100),
                }
            },
        };
    }

    private static SlateMailerOrganization GenerateSampleSmo(long id, long filerId, string sampleName, long statusId = 3)
    {
        return new()
        {
            Id = id,
            FilerId = filerId,
            StatusId = statusId,
            Name = "Base",
            Email = "<EMAIL>",
            ParentId = 2,
            CreatedBy = 1,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.SlateMailerOrg.Id,
                CurrentRegistration = new GeneralPurposeCommittee()
                {
                    Id = 2,
                    StatusId = statusId,
                    CommitteeType = CommitteeType.GeneralPurpose,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    JurisdictionCounty = string.Empty,
                    JurisdictionActive = string.Empty,
                    FinancialInstitutionName = string.Empty,
                    FinancialInstitutionPhone = string.Empty,
                    FinancialInstitutionAccountNumber = string.Empty,
                },
                Users = new()
                {
                    new()
                    {
                        FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                        User = new()
                        {
                            FirstName = "FirstName",
                            LastName = "LastName",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            }
        };
    }

    /// <summary>
    /// Create a sample registration contact
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
#pragma warning disable S107 // Methods should not have too many parameters
    private static RegistrationRegistrationContact GenerateSampleRegistrationContact(
        long registrationId, long contactId, string role, string title, bool canAuthorize, long? userId, string firstName, string lastName
        )
#pragma warning restore S107 // Methods should not have too many parameters
    {
        return new()
        {
            Id = contactId,
            CanAuthorizeSlateMailerContents = canAuthorize,
            CreatedBy = userId ?? 0,
            ModifiedBy = userId ?? 0,
            RegistrationId = registrationId,
            Role = role,
            Title = title,
            RegistrationContact = new()
            {
                FirstName = firstName,
                LastName = lastName,
                MiddleName = string.Empty,
                Email = "<EMAIL>",
                AddressList = new()
                {
                    Addresses = new()
                    {
                        new()
                        {
                            Country = "United States",
                            Street = "Street1",
                            Street2 = "Street2",
                            City = "Honolulu",
                            State = "HI",
                            Zip = "96812",
                            Type = "Residential",
                            Purpose = "PrincipalOfficer",
                        },
                    }
                },
                PhoneNumberList = new()
                {
                    PhoneNumbers = new()
                    {
                        new()
                        {
                            InternationalNumber = false,
                            CountryCode = "1",
                            Extension = "808",
                            Number = "1231234",
                            Type = "Phone",
                        }
                    }
                }
            },

            // Default
            CapitalContributionOver10K = false,
            PercentOfOwnership = 100,
            CumulativeCapitalContributions = 0,
            TenPercentOrGreater = false,
        };
    }

    private static FilingSummary GenerateSmoFilingSummaryDto(long filingId, long type, decimal amount)
    {
        return new()
        {
            FilingId = filingId,
            FilingSummaryTypeId = type,
            PeriodAmount = amount,
            ToDateAmount = amount,
        };
    }

    private static List<FilingSummary> GenerateFilingSummaries()
    {
        return new()
        {
            new()
            {
                Id = 1,
                FilingId = 1,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
                FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
                FilingSummaryTypeId = FilingSummaryType.PaymentMadeSummary.Id,
            },
            new()
            {
                Id = 2,
                FilingId = 1,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
                FilingSummaryStatusId = FilingSummaryStatus.InProgress.Id,
                FilingSummaryTypeId = FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id,
            },
            new()
            {
                Id = 3,
                FilingId = 1,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
                FilingSummaryStatusId = FilingSummaryStatus.NothingToReport.Id,
                FilingSummaryTypeId = FilingSummaryType.PersonReceiving1000OrMoreSummary.Id,
            },
            new()
            {
                Id = 4,
                FilingId = 1,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
                FilingSummaryStatusId = FilingSummaryStatus.Completed.Id,
                FilingSummaryTypeId = FilingSummaryType.PaymentReceivedSummary.Id,
            },
        };
    }

    private static List<FilingPeriod> GenerateFilingPeriods()
    {
        return new List<FilingPeriod>
        {
            new ()
            {
                Id = 1,
                StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Filings = new List<Filing>
                {
                    new ()
                    {
                        Id = 1,
                        StatusId = FilingStatus.Draft.Id,
                        FilingTypeId = FilingType.SlateMailerOrganization.Id,
                    }
                }
            },
            new ()
            {
                Id = 2,
                StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Filings = new List<Filing>
                {
                    new ()
                    {
                        Id = 2,
                        StatusId = FilingStatus.Draft.Id,
                        FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
                    }
                }
            }
        };
    }

    private static List<User> GenerateUsers()
    {
        return new List<User>
        {
            new()
            {
                Id = 1,
                FirstName = "Test",
                LastName = "Test",
                EmailAddress = "<EMAIL>",
                UserName = "TestTest",
                EntraOid = "TestOid"
            },
            new()
            {
                Id = 2,
                FirstName = "Test2",
                LastName = "Test2",
                EmailAddress = "<EMAIL>",
                UserName = "Test2Test2",
                EntraOid = "TestOid"
            },
            new()
            {
                Id = 3,
                FirstName = "Test1",
                LastName = "Test1",
                EmailAddress = "<EMAIL>",
                UserName = "Test1Test1",
                EntraOid = "TestOid"
            },
            new()
            {
                Id = 999,
                FirstName = "Test2",
                LastName = "Test2",
                EmailAddress = "<EMAIL>",
                UserName = "Test2Test2",
                EntraOid = "TestOid"
            }
        };
    }

    private static List<FilerUser> GenerateFilerUsers()
    {
        return new List<FilerUser>
        {
             new()
             {
                Id = 1,
                FilerId = 1,
                FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                UserId = 1,
                FilerRole = FilerRole.SlateMailerOrg_Treasurer,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 1)
             },
             new()
             {
                Id = 2,
                FilerId = 2,
                FilerRoleId = FilerRole.SlateMailerOrg_Officer.Id,
                UserId = 2,
                FilerRole = FilerRole.SlateMailerOrg_Officer,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 1)
             },
             new()
             {
                Id = 2,
                FilerId = 2,
                FilerRoleId = FilerRole.SlateMailerOrg_AccountManager.Id,
                UserId = 2,
                FilerRole = FilerRole.SlateMailerOrg_AccountManager,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 1)
             },
             new()
             {
                Id = 3,
                FilerId = 3,
                FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                UserId = 1,
                FilerRole = FilerRole.SlateMailerOrg_Treasurer,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 3)
             },
        };
    }

    private static List<Transaction> GenerateTransactions()
    {
        return new List<Transaction>
        {
            new PaymentReceived
            {
                Id = 1,
                Amount = (Currency)1m,
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Contact = GenerateContacts(FilerContactType.Individual)
            },
            new PaymentMade
            {
                Id = 2,
                Amount = (Currency)1m,
                ExpenditureCodeId = 1,
                ExpenditureCodeDescription = "Description",
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Contact = GenerateContacts(FilerContactType.Organization)
            },
            new PaymentMade
            {
                Id = 3,
                Amount = (Currency)1m,
                ExpenditureCodeId = 1,
                ExpenditureCodeDescription = "Description",
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                AgentOrIndependentContractorName = "Agent",
                Contact = GenerateContacts(FilerContactType.Committee)

            },
            new PersonReceiving1000OrMore
            {
                Id = 4,
                Amount = (Currency)1m,
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Contact = GenerateContacts(FilerContactType.Candidate)
            }
        };
    }

    private static FilerContact? GenerateContacts(FilerContactType filerContactType)
    {
        var individualContact = new IndividualContact
        {
            Id = 1,
            FirstName = "FirstName",
            LastName = "LastName",
            Employer = "Employer",
            AddressList = new AddressList
            {
                Id = 1,
                Addresses = new()
                {
                    new ()
                    {
                        Country = "United States",
                        Street = "Street1",
                        Street2 = "Street2",
                        City = "Honolulu",
                        State = "HI",
                        Zip = "96812",
                        Type = "Residential",
                        Purpose = "PrincipalOfficer",
                    }
                }
            },
            FilerContactType = FilerContactType.Individual
        };

        var organizationContact = new OrganizationContact
        {
            Id = 2,
            FirstName = "FirstName",
            LastName = "LastName",
            OrganizationName = "OrganizationName",
            AddressList = new AddressList
            {
                Id = 1,
                Addresses = new()
                {
                    new ()
                    {
                        Country = "United States",
                        Street = "Street1",
                        Street2 = "Street2",
                        City = "Honolulu",
                        State = "HI",
                        Zip = "96812",
                        Type = "Residential",
                        Purpose = "PrincipalOfficer",
                    }
                }
            },
            FilerContactType = FilerContactType.Organization
        };

        var candidateContact = new CandidateContact
        {
            Id = 3,
            FirstName = "FirstName",
            LastName = "LastName",
            OfficeSought = "OfficerSought",
            Jurisdiction = "Jurisdiction",
            District = "District",
            AddressList = new AddressList
            {
                Id = 1,
                Addresses = new()
                {
                    new ()
                    {
                        Country = "United States",
                        Street = "Street1",
                        Street2 = "Street2",
                        City = "Honolulu",
                        State = "HI",
                        Zip = "96812",
                        Type = "Residential",
                        Purpose = "PrincipalOfficer",
                    }
                }
            },
            FilerContactType = FilerContactType.Candidate
        };

        var commiteeContact = new CommitteeContact
        {
            Id = 4,
            FirstName = "FirstName",
            LastName = "LastName",
            CommitteeType = "CommiteeType",
            AddressList = new AddressList
            {
                Id = 1,
                Addresses = new()
                {
                    new ()
                    {
                        Country = "United States",
                        Street = "Street1",
                        Street2 = "Street2",
                        City = "Honolulu",
                        State = "HI",
                        Zip = "96812",
                        Type = "Residential",
                        Purpose = "PrincipalOfficer",
                    }
                }
            },
            FilerContactType = FilerContactType.Committee
        };

        var contacts = new List<FilerContact> { individualContact, organizationContact, candidateContact, commiteeContact };

        return contacts.FirstOrDefault(x => x.FilerContactType == filerContactType);
    }

    private static List<FilingTransaction> GenerateFilingTransactions()
    {
        return new List<FilingTransaction>
        {
            new ()
            {
                Id = 1,
                FilingId = 1,
                Transaction = new PaymentMade
                {
                    Id = 1,
                    Amount = (Currency)0m,
                    ExpenditureCodeDescription = "description",
                    ExpenditureCodeId = 1,
                }
            },
            new ()
            {
                Id = 2,
                FilingId = 1,
                Transaction = new PaymentReceived
                {
                    Id = 2,
                    Amount = (Currency)0m,
                }
            },
        };
    }

    private static List<FilingContactSummary> GenerateContactSummaries()
    {
        return new List<FilingContactSummary>
        {
            new ()
            {
                Id = 1,
                Amount = 1m,
                PreviouslyUnitemizedAmount = 1m,
                DisclosureFilingId = 1L,
                FilerContactId = 1L,
                FilingContactSummaryTypeId = FilingContactSummaryType.PaymentReceived.Id,
            },
            new ()
            {
                Id = 2,
                Amount = 1m,
                PreviouslyUnitemizedAmount = 0m,
                DisclosureFilingId = 1L,
                FilerContactId = 1L,
                FilingContactSummaryTypeId = FilingContactSummaryType.PersonReceiving1000OrMore.Id,
            },
        };
    }
    #endregion
}
