using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines database interactions for the Registration table.
/// </p>
/// <p>
/// Architectural Design: This repository represents a Data Service invoked by Business Services.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Every entity has an Id field containing a unique identifier that can be used to retrieve a single record.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this repository is to handle data persistence functions acting as an intermediary between business logic and data storage.
/// </p>
/// <h4>Feature</h4>
/// <p>
/// <ul>
/// <li>FR-01: Register a Candidate, Campaign, or Lobbying Filing Entity</li>
/// <li>FR-02: Amend a Registration</li>
/// <li>FR-03: Terminate a Registration</li>
/// <li>FR-04: Withdraw a Registration</li>
/// <li>FR-05: Establish Major Donor/Independent Expenditure and Payment To Influence Filer Entity Accounts</li>
/// <li>FR-06: Manage Penalty of Perjury Attestation</li>
/// <li>FR-09: Process a Lobbyist Photo</li>
/// <li>FR-12: Enter Lobbying Ethics Certification</li>
/// <li>FR-13: Void Lobbyist Registration</li>
/// <li>FR-14: Reinstate Lobbyist Registration</li>
/// <li>FR-15: Renew Lobbying Registration</li>
/// </ul>
/// </p>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this repository.
/// </p>
/// | Service                 | Operation                    | Description                         |
/// | ----------------------- | ---------------------------- | ----------------------------------- |
/// | N/A | N/A | N/A |
#endregion

public interface IRegistrationRepository : IRepository<Registration, long>
{
    /// <summary>
    /// Add a new address to a registration.
    /// </summary>
    /// <param name="address"></param>
    /// <param name="registrationId"></param>
    Task AddAddress(Address address, long registrationId);

    /// <summary>
    /// Update an existing address for a registration.
    /// </summary>
    /// <param name="address"></param>
    /// <param name="registrationId"></param>
    Task UpdateAddress(Address address, long registrationId);

    /// <summary>
    /// Find a CandidateIntentionStatementRegistration By ID.
    /// </summary>
    /// <param name="registrationId"></param>
    Task<CandidateIntentionStatement?> FindCandidateIntentionStatementById(long registrationId);

    /// <summary>
    /// Find expenditure expense amount by id.
    /// </summary>
    /// <param name="registrationId"></param>
    Task<ExpenditureExpenseAmount?> FindExpenditureExpenseAmount(long registrationId);

    /// <summary>
    /// Find expenditure type by id.
    /// </summary>
    /// <param name="registrationId"></param>
    Task<ElectionType?> FindElectionType(long registrationId);

    /// <summary>
    /// Find election by id.
    /// </summary>
    /// <param name="registrationId"></param>
    Task<Election?> FindElectionByRegistrationId(long registrationId);

    /// <summary>
    /// Remove an existing address from a registration.
    /// </summary>
    /// <param name="addressId"></param>
    /// <param name="registrationId"></param>
    Task RemoveAddress(long addressId, long registrationId);

    /// <summary>
    /// Add a new phone number to a registration.
    /// </summary>
    /// <param name="phoneNumber"></param>
    /// <param name="registrationId"></param>
    Task AddPhoneNumber(PhoneNumber phoneNumber, long registrationId);

    /// <summary>
    /// Update an existing phone number for a registration.
    /// </summary>
    /// <param name="phoneNumber"></param>
    /// <param name="registrationId"></param>
    Task UpdatePhoneNumber(PhoneNumber phoneNumber, long registrationId);

    /// <summary>
    /// Remove an existing phone number from a registration.
    /// </summary>
    /// <param name="phoneNumberId"></param>
    /// <param name="registrationId"></param>
    Task RemovePhoneNumber(long phoneNumberId, long registrationId);

    /// <summary>
    /// Assigns an election to a registration.
    /// </summary>
    /// <param name="registrationId">ID for Registration that is linking to Election</param>
    /// <returns></returns>
    Task<long> LinkElectionToCandidateIntentRegistration(long registrationId, string? jurisdiction, long? electionId, long? officeId, long? districtId, long? countyId, long? partyId);

    /// <summary>
    /// Create Candidate Registration.
    /// </summary>
    /// <param name="entity">Candidate Intention Statement entity</param>
    /// <returns></returns>
    Task<CandidateIntentionStatement> Create(CandidateIntentionStatement entity);

    /// <summary>
    /// Create Candidate Registration.
    /// </summary>
    /// <param name="entity">Candidate Intention Statement entity</param>
    /// <returns></returns>
    Task<Lobbyist> CreateLobbyist(Lobbyist entity);

    /// <summary>
    /// Get all registrations that match the query
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<IEnumerable<Registration>> GetAll(string query = "");

    /// <summary>
    /// Get registration.
    /// </summary>
    /// <param name="registrationId"></param>
    /// <returns>returns candidate registration with election data.</returns>
    Task<CandidateIntentionStatement?> FindCandidateIntentionStatementWithElectionById(long registrationId);

    /// <summary>
    /// Get registration.
    /// </summary>
    /// <param name="query"></param>
    /// <returns>returns candidate registration with election data.</returns>
    Task<IEnumerable<CandidateIntentionStatement>> FindCandidateRegistrationsWithElectionByIdOrName(string query);

    /// <summary>
    /// Find a SlateMailerOrganization Registration By ID.
    /// </summary>
    /// <param name="id">ID of the RegistrationFiler</param>
    Task<SlateMailerOrganization?> FindSlateMailerOrganizationById(long id);

    /// <summary>
    /// Finds a Slate Mailer Organization registration by ID with basic information only.
    /// </summary>
    /// <param name="id">ID of the registration</param>
    /// <returns>The Slate Mailer Organization registration with basic details.</returns>
    Task<SlateMailerOrganization?> FindSlateMailerOrganizationBasicById(long id);

    /// <summary>
    /// Gets the SMO RegistrationContacts
    /// </summary>
    /// <param name="id">Id of the RegistrationFiler</param>
    /// <returns></returns>
    Task<IEnumerable<RegistrationRegistrationContact>> FindSmoRegistrationContactsById(long id);

    /// <summary>
    /// Gets specific RegistrationContact
    /// </summary>
    /// <param name="registrationId">Id of the RegistrationFiler</param>
    /// <param name="contactId">Id of the RegistrationRegistrationContact</param>
    /// <returns></returns>
    Task<RegistrationRegistrationContact?> GetSmoRegistrationRegistrationContactById(long registrationId, long contactId);

    /// <summary>
    /// Retrieves a lobbyist employer registration by ID including related address and phone number data.
    /// </summary>
    /// <param name="id">The ID of the lobbyist employer registration to find.</param>
    /// <returns>The lobbyist employer registration if found; otherwise, null.</returns>
    Task<LobbyistEmployer?> FindLobbyistEmployerById(long id);

    /// <summary>
    /// Retrieves a lobbyist employer registration by Name.
    /// </summary>
    /// <param name="name">The name of the lobbyist employer registration to find.</param>
    /// <returns>The lobbyist employer registration if found; otherwise, null.</returns>
    Task<LobbyistEmployer?> FindLobbyistEmployerByName(string name);

    /// <summary>
    /// Retrieves a lobbying firm registration by ID including related address data.
    /// </summary>
    /// <param name="id">The ID of the lobbying firm registration to find.</param>
    /// <returns>The lobby firm registration if found; otherwise, null.</returns>
    Task<LobbyingFirm?> FindLobbyingFirmById(long id);

    /// <summary>
    /// Retrieves list lobbying firms.
    /// </summary>
    /// <returns></returns>
    Task<List<LobbyingFirm>> GetAllLobbyingFirms();

    /// <summary>
    /// Gets the AddressList by Id
    /// </summary>
    /// <param name="id">AddressList Id</param>
    /// <returns></returns>
    Task<AddressList?> GetAddressListById(long id);

    /// <summary>
    /// Gets the PhoneNumberList by Id
    /// </summary>
    /// <param name="id">PhoneNumber List</param>
    /// <returns></returns>
    Task<PhoneNumberList?> GetPhoneNumberListById(long id);

    /// <summary>
    /// Core name uniqueness check (can be made private/internal if not exposed)
    /// </summary>
    Task<bool> IsRegistrationNameUnique(string name, string discriminator, long filerTypeId, long? parentId = null);

    /// <summary>
    /// Check the Slate Mailer Organization (SMO) Name is unique.
    /// </summary>
    async Task<bool> IsUniqueSmoName(string name, long? parentId = null) =>
        await IsRegistrationNameUnique(name, nameof(SlateMailerOrganization), FilerType.SlateMailerOrg.Id, parentId);

    /// <summary>
    /// Check the lobbyist Name is unique.
    /// </summary>
    async Task<bool> IsLobbyistNameUnique(string name, long? parentId = null) =>
        await IsRegistrationNameUnique(name, nameof(Lobbyist), FilerType.Lobbyist.Id, parentId);

    /// <summary>
    /// Check the lobbyist employer Name is unique.
    /// </summary>
    async Task<bool> IsLobbyistEmployerNameUnique(string name, long? parentId = null) =>
        await IsRegistrationNameUnique(name, nameof(LobbyistEmployer), FilerType.LobbyistEmployer.Id, parentId);

    /// <summary>
    /// Searches for Committees by Id or Name
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<IEnumerable<Filer>> FindCommitteeByIdOrName(string query);

    /// <summary>
    /// Searches for  lobbyist employer or lobbying firm by Id or Name
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<IEnumerable<Filer>> FindLobbyistEmployerOrLobbyingFirmByIdOrName(string query);

    /// <summary>
    /// Searches for  lobbyist by Id or Name
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<IEnumerable<Filer>> FindLobbyistByIdOrName(string query);

    /// <summary>
    /// Finds a Primarily Formed Committee by Id (complete) or Name (partial)
    /// </summary>
    /// <param name="query">term to search for</param>
    /// <returns></returns>
    Task<IEnumerable<Committee>> FindPrimarilyFormedCommitteeByIdOrName(string query);

    /// <summary>
    /// Retrieves a registration by filer ID including related address and phone number data.
    /// </summary>
    /// <param name="id">The ID of the registration to find.</param>
    /// <returns>The registration if found; otherwise, null.</returns>
    Task<T?> FindLobbyingRegistrationByFilerId<T>(long filerId) where T : Registration;

    /// <summary>
    /// Finds a list of registrations of type <typeparamref name="T"/> by the specified filer ID.
    /// </summary>
    /// <typeparam name="T">The registration type, must inherit from <see cref="Registration"/>.</typeparam>
    /// <param name="filerId">The unique identifier of the filer.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of registrations of type <typeparamref name="T"/> associated with the filer ID.</returns>
    Task<List<T?>> FindListOfRegistrationsByFilerId<T>(long filerId) where T : Registration;

    /// <summary>
    /// Gets a Lobbyist Registration Record from the Database using the employer Registration Id
    /// </summary>
    /// <param name="employerRegistrationId">Employer ID of the registration</param>
    /// <returns>List of lobbyist registration </returns>
    Task<List<Lobbyist>?> FindLobbyistRegistrationByEmployer(long employerRegistrationId);

    /// <summary>
    /// Find a Lobbyist By Id.
    /// </summary>
    /// <param name="id">ID of the Filer Registration</param>
    /// <returns>The Lobbyist registration if found; otherwise null</returns>
    Task<Lobbyist?> FindLobbyistById(long id);

    /// <summary>
    /// Gets the Lobbyist Withdrawal Registration Record from the Database
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    Task<LobbyistWithdrawal?> FindLobbyistWithdrawalById(long id);

    /// <summary>
    /// Gets the Lobbyist termination Registration Record from the Database
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    Task<LobbyistTermination?> FindLobbyistTerminationById(long id);

    /// <summary>
    /// Gets the Registration Record type
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    Task<string?> GetRegistrationDiscriminatorById(long id);

    /// <summary>
    /// Get registration based on the filerId (no Election data).
    /// </summary>
    /// <param name="filerId">filerId</param>
    /// <returns>returns candidateIntentionStatement</returns>
    Task<CandidateIntentionStatement?> GetRegistrationByFilerId(long filerId);

    /// <summary>
    /// Get CIS withdrawal registration based on the filerId.
    /// </summary>
    /// <param name="registrationId">registrationId</param>
    /// <returns>returns CisWithdrawal</returns>
    Task<CisWithdrawal?> GetCisRegistrationWithdrawalById(long registrationId);

    /// <summary>
    /// Get registration based on the filerId.
    /// </summary>
    /// <param name="filerId">filerId</param>
    /// <returns>returns candidateIntentionStatement</returns>
    Task<CandidateIntentionStatement?> GetCandidateIntentionStatementWithElectionByFilerId(long filerId);

    /// <summary>
    /// Find an existing SMO Registration Amendment
    /// </summary>
    /// <param name="parentId">ID of parent SMO registration</param>
    /// <param name="statusId">ID of registration status</param>
    /// <returns>SMO registration object</returns>
    Task<SlateMailerOrganization?> FindExistingSmoAmendmentRegistration(long parentId, long statusId);

    /// <summary>
    /// Gets the SMO RegistrationContacts no tracking
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns>A collection of RegistrationRegistrationContact</returns>
    Task<IEnumerable<RegistrationRegistrationContact>> FindSmoRegistrationContactsByIdNoTracking(long id);

    /// <summary>
    /// Searches for Controlled Committees by Id or Name
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    Task<IEnumerable<CandidateControlledCommittee>> FindControlledCommitteesByNameOrId(string q);

    /// <summary>
    /// Gets Controlled Committee by filerId
    /// </summary>
    /// <param name="filerId"></param>
    /// <returns></returns>
    Task<CandidateControlledCommittee?> GetControlledCommitteeByFilerId(long filerId);

    /// <summary>
    /// Search SMO registration by ID or name and status
    /// </summary>
    /// <param name="query">Text to compare</param>
    /// <param name="statusId">Status of registration</param>
    /// <param name="userId">User ID that linked to the SMO registration</param>
    /// <returns>A collection of SMO registration</returns>
    Task<List<SlateMailerOrganization>> SearchSmoRegistrationByIdOrName(string query, long statusId, long? userId);

    /// <summary>
    /// Gets the latest Accepted SMO Registration from Filer Id
    /// </summary>
    /// <param name="filerId"></param>
    /// <returns></returns>
    Task<SlateMailerOrganization?> FindSmoRegistrationLatestAcceptedByFilerId(long filerId);

    /// <summary>
    /// Gets filer of latest accepted
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<Filer?> GetFilerOfLatestAcceptedCisRegistration(long userId);

    /// <summary>
    /// Gets latest accepted CIS by filer Id
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<CandidateIntentionStatement?> GetLatestAcceptedCisRegistrationByFilerId(long filerId);

    /// <summary>
    /// Retrieves the most recent <see cref="CandidateIntentionStatement"/> for a given filer ID,
    /// excluding any statements that have a status of <see cref="RegistrationStatus.Canceled"/>.
    /// </summary>
    /// <param name="filerId">The ID of the filer to retrieve the statement for.</param>
    /// <returns>returns candidateIntentionStatement </returns>
    Task<CandidateIntentionStatement?> GetCisByFilerId(long filerId);

    /// <summary>
    /// Retrieves the most recent <see cref="Committee"/> for a given filer ID,
    /// excluding any statements that have a status of Draft, Rejected, or Canceled./>.
    /// </summary>
    /// <param name="filerId">The ID of the filer to retrieve the statement for.</param>
    /// <returns>Committee</returns>
    Task<Committee?> GetCommitteeByFilerId(long filerId);

    /// <summary>
    /// Find the SMO registration along with its parent (original from)
    /// </summary>
    /// <param name="id">ID of SMO registration</param>
    /// <returns>SMO registration along with its parent registration</returns>
    Task<SlateMailerOrganization?> FindSlateMailerOrganizationWithParentById(long id);
}
