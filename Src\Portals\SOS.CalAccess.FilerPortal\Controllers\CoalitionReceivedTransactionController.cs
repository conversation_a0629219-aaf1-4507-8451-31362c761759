using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Extensions;
using SOS.CalAccess.UI.Common.Services;
using FilerContactType = SOS.CalAccess.Models.FilerDisclosure.Contacts.FilerContactType;
using FilerPortalValidationMessage = SOS.CalAccess.FilerPortal.Alerts.ValidationMessage;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using IFilerPortalAlertsContainer = SOS.CalAccess.FilerPortal.Alerts.IPortalAlertsContainer;
using ValidationMessage = SOS.CalAccess.UI.Common.Extensions.ValidationMessage;


namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Controller for payments received by lobbying coalitions transaction-related actions.
/// </summary>
public class CoalitionReceivedTransactionController : Controller
{
    private const string SelectContactViewName = "01_SelectContact";
    private const string EnterContactViewName = "02_EnterContact";
    private const string EnterAmountViewName = "03_EnterAmount";

    private const string ContinueAction = "Continue";
    private const string PreviousAction = "Previous";
    private const string SaveAction = "Save";

    private readonly ILogger<CoalitionReceivedTransactionController> _logger;
    private readonly IToastService _toastService;
    private readonly IStringLocalizer<SharedResources> _localizer;
    private readonly ILobbyistEmployerCoalitionApi _lobbyistEmployerCoalitionApi;
    private readonly IContactsApi _contactsApi;
    private readonly ICoalitionReceivedTransactionCtlSvc _coalitionReceivedTransactionCtlSvc;
    private readonly IFilingsApi _filingsApi;

    public CoalitionReceivedTransactionController(
        IToastService toastService,
        ILogger<CoalitionReceivedTransactionController> logger,
        IStringLocalizer<SharedResources> localizer,
        ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
        IContactsApi contactsApi,
        ICoalitionReceivedTransactionCtlSvc coalitionReceivedTransactionCtlSvc,
        IFilingsApi filingsApi)
    {
        _logger = logger;
        _toastService = toastService;
        _localizer = localizer;
        _lobbyistEmployerCoalitionApi = lobbyistEmployerCoalitionApi;
        _contactsApi = contactsApi;
        _coalitionReceivedTransactionCtlSvc = coalitionReceivedTransactionCtlSvc;
        _filingsApi = filingsApi;
    }

    #region Step 01 - Select Lobbying Coalition Member Contact

    /// <summary>
    /// Loader for 01_SelectContact view.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> SelectContact(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long? contactId,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        var model = await _coalitionReceivedTransactionCtlSvc.GetSelectContactViewModel(reportType, filingId, filerId, contactId, cancellationToken);

        return View(SelectContactViewName, model);
    }

    /// <summary>
    /// Handles redirects based on action selected in step 1.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> SelectContact(CoalitionReceivedTransactionViewModel model, string action, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        return action == ContinueAction
            ? RedirectToAction(nameof(EnterContact),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId
                })
            : await RedirectToTransactionSummary(model);
    }

    /// <summary>
    /// Search filer contact for select contact search component.
    /// </summary>
    /// <param name="contactsApi"></param>
    /// <param name="search"></param>
    /// <param name="filerId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<JsonResult> SearchFilerContactsByIdOrName(
        [FromServices] IContactsApi contactsApi,
        [FromQuery] string search,
        [FromQuery] long filerId,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var contactTypes = new List<string> { "Individual", "Organization" };
        var data = await contactsApi.SearchContactsByNameOrId(filerId, search, contactTypes, cancellationToken);
        return new JsonResult(data);
    }
    #endregion

    #region Step 02 - Enter Lobbying Coalition Member Contact

    /// <summary>
    /// Loader for 02_EnterContact view.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="contactId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> EnterContact(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long? contactId,
        [FromQuery] long? transactionId,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        var model = await _coalitionReceivedTransactionCtlSvc.GetEnterContactViewModel(reportType, filingId, filerId, contactId, cancellationToken);
        if (model!.Contact.TypeId is null or 0)
        {
            model!.Contact.TypeId = FilerContactType.Individual.Id;
        }
        model!.TransactionId = transactionId;
        return View(EnterContactViewName, model);
    }

    /// <summary>
    /// Handles redirects or save contact operation based on action selected in step 2.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> EnterContact(CoalitionReceivedTransactionViewModel model, string action, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        return action switch
        {
            PreviousAction => RedirectToAction(nameof(SelectContact),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId
                }),
            ContinueAction => await SaveContactAsync(model, cancellationToken),
            _ => await RedirectToTransactionSummary(model)
        };
    }

    /// <summary>
    /// Save contact async operation.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<IActionResult> SaveContactAsync(CoalitionReceivedTransactionViewModel model, CancellationToken cancellationToken)
    {
        var contactDetail = model.Contact;
        if (model.FilingId == null || model.FilerId == null || contactDetail == null || contactDetail.TypeId == null)
        {
            return HandleInvalidModelState();
        }

        try
        {
            var request = BuildUpsertFilerContactRequest(model, "PaymentReceiveLobbyingCoalition");
            if (model.ContactId != null && model.ContactId.Value != 0)
            {
                await _contactsApi.UpdateFilerContact(model.ContactId.Value, request, cancellationToken);
            }
            else
            {
                var newContact = await _contactsApi.CreateFilerContact(model.FilerId.Value, request, cancellationToken);
                if (newContact.ValidationErrors.Count == 0)
                {
                    model.ContactId = newContact.Id;
                }
                else
                {
                    SyncDecisionsValidationsWithViewModel([.. newContact.ValidationErrors], model.Contact);
                    return View(EnterContactViewName, model);
                }

            }

            return RedirectToAction(nameof(EnterAmount),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId,
                    transactionId = model.TransactionId
                });
        }
        catch (ApiException err) when (err.StatusCode == (HttpStatusCode)422)
        {
            if (err.Content != null)
            {
                // Deserialize workflow errors from API response
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content);

                SyncDecisionsValidationsWithViewModel(workflowErrors!, model.Contact);
            }
            return View(EnterContactViewName, model);
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer[ResourceConstants.UpdateFailMessage]);
            _logger.LogError(ex, "Update lobbyist employer payments received by lobbying coalition failed during contact save.");
            return View(EnterAmountViewName, model);
        }
    }

    /// <summary>
    /// Builds AddressDto list for save contact request.
    /// </summary>
    /// <param name="contactDetail"></param>
    /// <returns></returns>
    private static List<AddressDto> BuildAddressDtoList(dynamic contactDetail)
    {
        return new List<AddressDto>
        {
            new(
                contactDetail.City ?? string.Empty,
                contactDetail.Country ?? string.Empty,
                0,
                string.Empty,
                contactDetail.State ?? string.Empty,
                contactDetail.Street ?? string.Empty,
                contactDetail.Street2 ?? string.Empty,
                "Business",
                contactDetail.ZipCode ?? string.Empty
            )
        };
    }

    /// <summary>
    /// Builds EmailAddressDto list for save contact request.
    /// </summary>
    /// <param name="contactDetail"></param>
    /// <returns></returns>
    private static List<EmailAddressDto> BuildEmailAddressDtoList(dynamic contactDetail)
    {
        return string.IsNullOrEmpty(contactDetail.EmailAddress)
               ? new List<EmailAddressDto>()
               : new List<EmailAddressDto> { new(contactDetail.EmailAddress, 0, string.Empty, string.Empty) };
    }

    /// <summary>
    /// Builds UpsertFilerContactRequest for save contact request.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="form"></param>
    /// <returns></returns>
    private static UpsertFilerContactRequest BuildUpsertFilerContactRequest(CoalitionReceivedTransactionViewModel model, string form)
    {
        var contactDetail = model.Contact;
        var addressDtoList = BuildAddressDtoList(contactDetail);
        var emailAddressDtoList = BuildEmailAddressDtoList(contactDetail);
        var phoneNumberDtoList = new List<PhoneNumberDto>();
        return contactDetail.TypeId switch
        {
            var type when type == CalAccess.Models.FilerDisclosure.Contacts.FilerContactType.Individual.Id =>
                new UpsertIndividualFilerContactRequest(
                    addressDtoList,
                    emailAddressDtoList,
                    string.Empty,
                    form,
                    contactDetail.FirstName ?? string.Empty,
                    contactDetail.LastName ?? string.Empty,
                    contactDetail.MiddleName ?? string.Empty,
                    string.Empty,
                    phoneNumberDtoList),
            var type when type == CalAccess.Models.FilerDisclosure.Contacts.FilerContactType.Organization.Id =>
                new UpsertOrganizationFilerContactRequest(
                    addressDtoList,
                    emailAddressDtoList,
                    form,
                    contactDetail.OrganizationName ?? string.Empty,
                    phoneNumberDtoList),
            _ => new UpsertFilerContactRequest(
                    addressDtoList,
                    emailAddressDtoList,
                    form,
                    phoneNumberDtoList)
        };
    }
    #endregion

    #region Step 03 - Enter Payment Amount Received by Lobbying Coalition

    /// <summary>
    /// Loader for 03_EnterAmount view.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="contactId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> EnterAmount(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long contactId,
        [FromQuery] long? transactionId,
        [FromServices] ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        var model = await _coalitionReceivedTransactionCtlSvc.GetEnterAmountViewModel(reportType, filingId, filerId, contactId, cancellationToken);
        if (transactionId != null && model != null)
        {
            var transaction = await lobbyistEmployerCoalitionApi.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId.GetValueOrDefault(), cancellationToken);
            model.Amount = (decimal)(transaction?.AmountThisPeriod ?? 0);
            model.Id = transaction?.Id;
        }
        return View(EnterAmountViewName, model);
    }

    /// <summary>
    /// Handles redirects or save transaction operation based on action selected in step 3.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> EnterAmount(CoalitionReceivedTransactionViewModel model, string action, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            foreach (var state in ModelState)
            {
                foreach (var error in state.Value.Errors)
                {
                    model.Messages.Validations[state.Key] = new ValidationMessage { Message = error.ErrorMessage };
                }
            }

            return View(EnterAmountViewName, model);
        }

        return action switch
        {
            PreviousAction => RedirectToAction(nameof(EnterContact),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId
                }),
            SaveAction => await SavePaymentReceivedAsync(model, cancellationToken),
            _ => await RedirectToTransactionSummary(model)
        };
    }

    /// <summary>
    /// Handles save payment transaction async operation.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<IActionResult> SavePaymentReceivedAsync(CoalitionReceivedTransactionViewModel model, CancellationToken cancellationToken)
    {
        if (model.FilingId == null || model.ContactId == null || model.FilerId == null)
        {
            return HandleInvalidModelState();
        }

        try
        {
            var request = new PaymentReceiveLobbyingCoalitionRequestDto(
                amount: model.Amount.HasValue ? (double)model.Amount.Value : 0,
                disclosureFilingId: model.FilingId.Value,
                contactId: model.ContactId.Value,
                filerId: model.FilerId.Value);
            TransactionResponseDto response;
            if (model.Id == null)
            {
                response = await _lobbyistEmployerCoalitionApi.CreatePaymentReceivedByLobbyingCoalition(request, cancellationToken);
                ProcessTransactionDtoResponse(model, response, _localizer[ResourceConstants.CreateTransactionSuccessMessage]);
            }
            else
            {
                // Update transaciton
                response = await _lobbyistEmployerCoalitionApi.EditPaymentReceivedByLobbyingCoalition(model!.Id.Value, request, cancellationToken);
                ProcessTransactionDtoResponse(model, response, _localizer[ResourceConstants.UpdateTransactionSuccessMessag]);
            }

            return response.Valid ? await RedirectToTransactionSummary(model) : View(EnterAmountViewName, model);
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer[ResourceConstants.UpdateFailMessage]);
            _logger.LogError(ex, "Update lobbyist employer payments received by lobbying coalition failed during payment save.");
            return View(EnterAmountViewName, model);
        }
    }

    private void ProcessTransactionDtoResponse(CoalitionReceivedTransactionViewModel model, TransactionResponseDto response, string resourceKey)
    {
        if (response.Valid)
        {
            _toastService.Success(_localizer[resourceKey]);
        }
        else
        {
            foreach (var error in response.ValidationErrors)
            {
                model.Messages.Validations[error.FieldName] = new ValidationMessage { Message = error.Message };
            }
        }
    }
    #endregion

    #region Helper Methods

    /// <summary>
    /// Handles invalid model state by returning a NotFound result.
    /// </summary>
    /// <returns></returns>
    private NotFoundResult HandleInvalidModelState() => NotFound();

    /// <summary>
    /// Redirects to the transaction summary page.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<RedirectToActionResult> RedirectToTransactionSummary(CoalitionReceivedTransactionViewModel model)
    {
        var isAmendment = await GetIsAmendment((long)model.FilingId!);
        var controller = isAmendment ? "AmendDisclosure" : "Disclosure";

        return RedirectToAction("Index", controller, new
        {
            viewName = FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name,
            reportType = model.ReportType,
            filingId = model.FilingId,
            filerId = model.FilerId
        });
    }

    /// <summary>
    /// Redirects to the transaction summary page.
    /// </summary>
    /// <param name="decisions"></param>
    /// /// <param name="viewModel"></param>
    /// <returns></returns>
    private void SyncDecisionsValidationsWithViewModel(
        IList<WorkFlowError> decisions,
        IFilerPortalAlertsContainer viewModel
    )
    {
        if (decisions.Any())
        {
            for (int i = 0; i < decisions.Count; i++)
            {
                var dsmNew = decisions[i];

                this.AddDecisionValidationToViewModel(viewModel,
                    new FilerPortalValidationMessage
                    {
                        ErrorCode = dsmNew.ErrorCode,
                        FieldName = dsmNew.FieldName,
                        Message = dsmNew.Message,
                        Type = dsmNew.ErrorType
                    });
            }
        }
    }
    private async Task<bool> GetIsAmendment(long filingId)
    {
        var filing = await _filingsApi.GetFiling(filingId);
        return filing?.Version > 0;
    }
    #endregion
}
