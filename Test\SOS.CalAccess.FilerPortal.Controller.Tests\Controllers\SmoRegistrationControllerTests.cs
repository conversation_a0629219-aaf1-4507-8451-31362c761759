using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Moq;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class SmoRegistrationControllerTests
{
    private Mock<IStringLocalizer<SharedResources>> _localizerMock;
    private Mock<ISmoRegistrationCtlSvc> _smoRegistrationsCtlSvcMock;
    private Mock<ISmoRegistrationSvc> _smoRegistrationSvcMock;
    private Mock<IAccuMailValidatorService> _mockAccuMailValidatorSvc;
    private Mock<IToastService> _toastServiceMock;
    private Mock<IAuthorizationSvc> _authorizationSvcMock;
    private SmoRegistrationController _controller;
    private TempDataDictionary _mockTempData;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _localizerMock = new Mock<IStringLocalizer<SharedResources>>();
        _smoRegistrationsCtlSvcMock = new Mock<ISmoRegistrationCtlSvc>();
        _smoRegistrationSvcMock = new Mock<ISmoRegistrationSvc>();
        _mockAccuMailValidatorSvc = new Mock<IAccuMailValidatorService>();
        _toastServiceMock = new Mock<IToastService>();
        _authorizationSvcMock = new Mock<IAuthorizationSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        var localizedString = new LocalizedString("key", "text");
        _localizerMock
            .Setup(x => x[It.IsAny<string>()]).Returns(localizedString);

        _localizerMock
        .Setup(x => x[ResourceConstants.SmoRegistrationTitle])
        .Returns(new LocalizedString(ResourceConstants.SmoRegistrationTitle, "SmoRegistrationTitle"));

        _localizerMock
        .Setup(x => x[ResourceConstants.SmoRegistrationOrganization])
        .Returns(new LocalizedString(ResourceConstants.SmoRegistrationOrganization, "SmoRegistrationOrganization"));

        _localizerMock
        .Setup(x => x[ResourceConstants.SmoRegistrationOfficers])
        .Returns(new LocalizedString(ResourceConstants.SmoRegistrationOfficers, "SmoRegistrationOfficers"));

        _localizerMock
        .Setup(x => x[ResourceConstants.SmoRegistrationIndividualAuthorizers])
        .Returns(new LocalizedString(ResourceConstants.SmoRegistrationIndividualAuthorizers, "SmoRegistrationIndividualAuthorizers"));

        _localizerMock
        .Setup(x => x[ResourceConstants.SmoRegistrationSubmit])
        .Returns(new LocalizedString(ResourceConstants.SmoRegistrationSubmit, "SmoRegistrationSubmit"));

        // Initialize TempData
        _mockTempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            ["$global_alert_message"] = null,
            ["$global_alert_type"] = null
        };

        _controller = new SmoRegistrationController(
            _smoRegistrationsCtlSvcMock.Object,
            _smoRegistrationSvcMock.Object,
            _mockAccuMailValidatorSvc.Object,
            _localizerMock.Object,
            _toastServiceMock.Object,
            _authorizationSvcMock.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    [Test]
    public void OnActionExecuting_ShouldCallRedirectToDashboard()
    {
        // Assert
        Assert.That(() => _controller.OnActionExecuting(It.IsAny<ActionExecutingContext>()), Throws.Nothing);
    }

    #region ViewOrViewAndComplete
    [Test]
    public async Task ViewOrViewAndComplete_IsValid_RedirectsToSummary()
    {
        _authorizationSvcMock
            .Setup(x => x.IsAuthorized(It.IsAny<AuthorizationRequest>()))
            .ReturnsAsync(true);

        var result = await _controller.ViewOrViewAndComplete(1) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Summary"));
    }

    [Test]
    [TestCase(true, false)]
    [TestCase(false, false)]
    public async Task ViewOrViewAndComplete_NotValid_RedirectsToDashboard(bool modelStateError, bool isAuthorized)
    {
        _authorizationSvcMock
            .Setup(x => x.IsAuthorized(It.IsAny<AuthorizationRequest>()))
            .ReturnsAsync(isAuthorized);

        if (modelStateError)
        {
            _controller.ModelState.AddModelError("key", "error");
        }

        var result = await _controller.ViewOrViewAndComplete(1) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result.ActionName, Is.EqualTo("Index"));
        });
    }
    #endregion

    [Test]
    public void Index_ShouldRedirectToPage01()
    {
        // Act
        var result = _controller.Index() as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void Edit_ShouldRedirectToPage03WhenHasId()
    {
        // Arrange
        long id = 1;

        // Act
        var result = _controller.Edit(id) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Edit_ShouldReturnNotFoundWhenModelStateInvalid()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = _controller.Edit(id) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void Close_ShouldRedirectToDashboard()
    {
        // Act
        var result = _controller.Close() as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public void Close_ShouldToastWhenFormActionCancel()
    {
        // Act
        var result = _controller.Close(FormAction.Cancel) as ActionResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.InstanceOf<ActionResult>());
            Assert.That(_toastServiceMock.Invocations, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task Cancel_ShouldRedirectToRoot()
    {
        // Arrange
        long id = 1;
        _smoRegistrationSvcMock
            .Setup(x => x.CancelSmoRegistration(It.IsAny<long>()))
            .Returns(Task.FromResult(default(object)));

        // Act
        var result = await _controller.Cancel(id, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Cancel_WhenModelStateInvalidReturnNotFound()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.Cancel(id, default) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void CloseOfficer_ShouldRedirectToPage08()
    {
        // Act
        var result = _controller.CloseOfficer(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void CancelOfficer_ShouldRedirectToPage08()
    {
        // Act
        var result = _controller.CancelOfficer(1, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void CancelOfficer_WhenModelStateInvalidReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = _controller.CancelOfficer(1, default) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void CloseAuthorizer_ShouldRedirectToPage12()
    {
        // Arrange
        var id = 1;
        var expected = "Page12";

        // Act
        var result = _controller.CloseAuthorizer(id) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(result!.ActionName, Is.EqualTo(expected));
        });
    }

    [Test]
    public void CancelAuthorizer_ShouldRedirectToPage12()
    {
        // Arrange
        var id = 1;
        var expected = "Page12";

        // Act
        var result = _controller.CancelAuthorizer(id, default) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(result!.ActionName, Is.EqualTo(expected));
        });
    }

    [Test]
    public void CancelAuthorizer_WhenModelStateInvalidReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = _controller.CancelAuthorizer(1, default) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    #region Page01
    [Test]
    public void Page01_ShouldReturnView()
    {
        // Act
        var result = _controller.Page01() as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    #endregion

    #region Page02_Get
    [Test]
    public void Page02_ShouldReturnView()
    {
        // Act
        var result = _controller.Page02(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void Page02_Get_InvalidModelState_RedirectsToPage01()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = -1
        };

        // Make the ModelState invalid by adding an error
        _controller.ModelState.AddModelError("Id", "Invalid ID");

        // Act
        var result = _controller.Page02(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public void Page02_Get_InvalidModelStates_RedirectsToPage01()
    {
        // Arrange
        long? id = 5;
        _controller.ModelState.AddModelError("SomeKey", "Some error"); // Force invalid model state

        // Act
        var result = _controller.Page02(id) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }
    #endregion

    #region Page02_Post
    [Test]
    public void Page02_Post_ContinueWithNoId_RedirectsToPage03WithoutRouteValues()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = 0, // Triggers the else path
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Page02_Post_Previous_RedirectsToPage01()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = 123,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }

    [Test]
    public void Page02_Post_InvalidAction_AddsModelErrorAndReturnsView()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Action = (FormAction)999, // Invalid action (not Continue or Previous)
            Id = -1
        };
        _controller.ModelState.Clear(); // Ensure ModelState is clear before testing

        // Act
        var result = _controller.Page02(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    [Test]
    public void Page02_Continue_WithValidId_RedirectsToPage03Async()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Page02(model);

        // Assert
        var redirectToActionResult = result as RedirectToActionResult;
        Assert.That(redirectToActionResult, Is.Not.Null);
        Assert.That(redirectToActionResult.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Page02_Continue_WithoutId_RedirectsToPage03Async()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Action = FormAction.Continue,
            Id = 1
        };

        // Act
        var result = _controller.Page02(model);

        // Assert
        var redirectToActionResult = result as RedirectToActionResult;
        Assert.That(redirectToActionResult, Is.Not.Null);
        Assert.That(redirectToActionResult.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public void Page02_InvalidAction_AddsModelErrorAndReturnsViewAsync()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Action = (FormAction)999,
            Id = 1
        };

        // Act
        var result = _controller.Page02(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult, Is.Not.Null);
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }
    #endregion

    #region Page03_Get
    [Test]
    public async Task Page03_Get_ShouldReturnViewAsync()
    {
        // Arrange
        long id = 1;
        var model = new SmoRegistrationStep01ViewModel();
        _smoRegistrationsCtlSvcMock
            .Setup(s => s.GetPage03ViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.Page03(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page03_Get_WhenModelStateInvalid_ShouldReturnEmptyModel()
    {
        long id = 1;
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.Page03(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result.Model, Is.InstanceOf<SmoRegistrationStep01ViewModel?>());
    }

    [Test]
    public async Task Page03_Get_WhenRecordNotFound_ShouldReturnEmptyModel()
    {
        long id = 1;
        _smoRegistrationsCtlSvcMock
            .Setup(s => s.GetPage03ViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((SmoRegistrationStep01ViewModel?)null);

        // Act
        var result = await _controller.Page03(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result.Model, Is.InstanceOf<SmoRegistrationStep01ViewModel?>());
        SmoRegistrationStep01ViewModel model = (SmoRegistrationStep01ViewModel)result.Model;
        Assert.That(model.Id, Is.Null);
    }
    #endregion

    #region Page03_Post
    [Test]
    public async Task Page03_IsValid_ShouldValidateAccuMailOnNoSuggestions()
    {
        // Arrange
        var smoRegistrationCtlSvcMock = Substitute.For<ISmoRegistrationCtlSvc>();
        var accuMailValidatorSvcMock = Substitute.For<IAccuMailValidatorService>();
        var controller = new SmoRegistrationController(smoRegistrationCtlSvcMock, _smoRegistrationSvcMock.Object, accuMailValidatorSvcMock, _localizerMock.Object, _toastServiceMock.Object, _authorizationSvcMock.Object);
        var model = new SmoRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Suggestions = new List<AccuMailSuggestion>(),
            Addresses = new List<AddressViewModel>
            {
                new()
                {
                    Purpose = "Mailing"
                }
            }
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        controller.TempData = tempData;

        // Act
        var result = await controller.Page03(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page03_IsValid_ShouldReturnViewOnAccuMailHasSuggestions()
    {
        // Arrange
        var smoRegistrationCtlSvcMock = Substitute.For<ISmoRegistrationCtlSvc>();
        var accuMailValidatorSvcMock = Substitute.For<IAccuMailValidatorService>();
        var controller = new SmoRegistrationController(smoRegistrationCtlSvcMock, _smoRegistrationSvcMock.Object, accuMailValidatorSvcMock, _localizerMock.Object, _toastServiceMock.Object, _authorizationSvcMock.Object);
        var model = new SmoRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Suggestions = new List<AccuMailSuggestion>(),
            IsSameAsOrganizationAddress = true,
            Addresses = new List<AddressViewModel>
            {
                new()
                {
                    Purpose = "Organization"
                }
            }
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        controller.TempData = tempData;
        accuMailValidatorSvcMock
            .AccuMailValidationHandler(Arg.Any<SmoRegistrationStep01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(true);

        // Act
        var result = await controller.Page03(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<ViewResult>());
        await smoRegistrationCtlSvcMock.DidNotReceive().Page03Submit(Arg.Any<SmoRegistrationStep01ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>());
    }

    [Test]
    public async Task Page03_Post_IsNotValid_ShouldReturnViewAsync()
    {
        // Arrange
        var model = new SmoRegistrationStep01ViewModel();
        _controller.ModelState.AddModelError("", "Invalid model state");

        // Act
        var result = await _controller.Page03(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page03Submit_InvalidSubmission_ReturnsViewWithModelAsync()
    {
        // Arrange
        var model = new SmoRegistrationStep01ViewModel
        {
            Email = "invalid-email", // Invalid Email
            OrganizationName = "", // Invalid organization name
            Action = FormAction.Continue
        };
        _controller.TempData = _mockTempData;
        _controller.ModelState.AddModelError("Email", "Invalid email format.");
        _smoRegistrationsCtlSvcMock
            .Setup(s => s.Page03Submit(It.IsAny<SmoRegistrationStep01ViewModel>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync((long?)null); // Simulate no registration id due to validation errors

        // Act
        var result = _controller.Page03(model);

        // Assert
        var viewResult = await result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }
    #endregion

    #region Page04_Get
    [Test]
    public async Task Page04_Get_ShouldReturnView()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = 1,
            OrganizationLevelOfActivity = "",
            IsOrganizationQualified = null,
            IsOrganizationCampaignCommittee = null,
            DateQualifiedAsSMO = null,
            CommitteeId = 1,
            CommitteeName = "",
        };

        // Act
        var result = await _controller.Page04Async(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    #endregion

    #region Page04_Post
    [Test]
    public async Task Page04Previous_ValidSubmission_RedirectsToPage03Async()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        // Act
        var result = await _controller.Page04Previous(model);

        // Assert
        var redirectToActionResult = result as RedirectToActionResult;
        Assert.That(redirectToActionResult, Is.Not.Null);
        Assert.That(redirectToActionResult.ActionName, Is.EqualTo("Page03"));
    }

    [Test]
    public async Task Page04Previous_InvalidSubmission_ReturnsViewWithModelAsync()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };
        _controller.ModelState.AddModelError("Id", "Id is invalid.");

        // Act
        var result = await _controller.Page04Previous(model);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.ViewName, Is.EqualTo("Page04"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
        });
    }

    [Test]
    public async Task Page04_Post_IsValidShouldRedirectOnSave()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose, // Ensure this triggers the redirect logic
            OrganizationLevelOfActivity = "",
            IsOrganizationQualified = null,
            IsOrganizationCampaignCommittee = null,
            DateQualifiedAsSMO = null
        };

        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Set up the mock for the Page04ContinueSubmit method
        var mockRegistrationId = 123L; // Simulate a successful registration ID return
        _smoRegistrationsCtlSvcMock.Setup(svc => svc.Page04ContinueSubmit(It.IsAny<SmoRegistrationDetailsStep01ViewModel>(), It.IsAny<ModelStateDictionary>(), false))
            .ReturnsAsync(mockRegistrationId);

        // Act
        var result = await _controller.Page04Async(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);  // Ensure the result is not null
        Assert.That(result.ActionName, Is.EqualTo("Index"));
    }


    [Test]
    public async Task Page04_Post_IsValid_ShouldRedirectOnContinue()
    {
        // Arrange
        var model = new SmoRegistrationDetailsStep01ViewModel
        {
            Action = FormAction.Continue,
            Id = 100,
            OrganizationLevelOfActivity = "city",
            IsOrganizationQualified = false,
            IsOrganizationCampaignCommittee = false,
            DateQualifiedAsSMO = null,
            CommitteeId = 1,
            CommitteeName = "",
        };

        // Mock the service to return a valid result
        var mockSmoRegistrationCtlSvc = new Mock<ISmoRegistrationCtlSvc>();
        var registrationId = 123;
        _ = mockSmoRegistrationCtlSvc
            .Setup(s => s.Page04ContinueSubmit(It.IsAny<SmoRegistrationDetailsStep01ViewModel>(), It.IsAny<ModelStateDictionary>(), It.IsAny<bool>()))
            .ReturnsAsync(registrationId);

        // Initialize the controller with the mocked service
        var controller = new SmoRegistrationController(mockSmoRegistrationCtlSvc.Object, _smoRegistrationSvcMock.Object, _mockAccuMailValidatorSvc.Object, _localizerMock.Object, _toastServiceMock.Object, _authorizationSvcMock.Object);


        // Act
        var result = await controller.Page04Async(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    #endregion

    #region Page05_Get
    [Test]
    public void Page05_Get_ShouldReturnView()
    {
        // Act
        var result = _controller.Page05(123, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    #endregion

    #region Page05_Post
    [Test]
    public void Page05_Post_ShouldReturnView_OnModelStateInvalid()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = _controller.Page05(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page05_Post_ShouldRedirectOnContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Action = FormAction.Continue };

        // Act
        var result = _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page05_Post_ShouldRedirectOnPrevious()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Action = FormAction.Previous };

        // Act
        var result = _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page05_Post_ShouldReturnError_NoFormAction()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1
        };

        // Act
        _controller.Page05(model, default);

        // Assert
        Assert.That(_controller.ModelState.IsValid, Is.False);
    }
    #endregion

    #region Page06_Get
    [Test]
    public async Task Page06_Get_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("", "Invalid model state");

        // Act
        var result = await _controller.Page06(123, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task Page06_Get_ShouldReturnView()
    {
        // Assert
        var model = new SmoRegistrationStep02ViewModel { Id = 123 };
        _smoRegistrationsCtlSvcMock.Setup(svc => svc.GetPage06AndPage07ViewModel(123, default))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.Page06(123, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page06_Get_ShouldReturnViewIfNoModelReturned()
    {
        // Assert
        var model = new SmoRegistrationStep02ViewModel { Id = 123 };

        // Act
        var result = await _controller.Page06(123, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    #endregion

    #region Page06_Post
    [Test]
    public async Task Page06_Post_IsValid_ShouldRedirectOnInvalidFormAction()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel();

        // Act
        var result = await _controller.Page06(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page06_Post_IsValid_ShouldRedirectOnContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Action = FormAction.Continue, IsUserTreasurer = true };

        // Act
        var result = await _controller.Page06(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page06_Post_IsValid_ShouldRedirectOnContinueAndPrefill()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Action = FormAction.Continue, IsUserTreasurer = true };

        // Act
        var result = await _controller.Page06(model) as RedirectToActionResult;

        // Assert 
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page06_Post_IsValid_ShouldReturnViewOnNoIsUserTreasuer()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Action = FormAction.Continue };

        // Act
        var result = await _controller.Page06(model) as ViewResult;

        // Assert 
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page06_Post_IsValid_ShouldRedirectOnPrevious()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Action = FormAction.Previous };

        // Act
        var result = await _controller.Page06(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page06_Post_IsInvalid_ShouldReturnView()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel { Action = FormAction.SaveAndClose };

        // Act
        var result = await _controller.Page06(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page07_Get
    [Test]
    public async Task Page07_Get_IsValid_ShouldReturnView()
    {
        // Arrange
        long mockId = 1;

        // Act
        var result = await _controller.Page07(mockId, true) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page07_Get_IsValid_ShouldReturnPrefill()
    {
        // Arrange
        long mockId = 1;
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.GetPage06AndPage07ViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()))
    .ReturnsAsync(new SmoRegistrationStep02ViewModel());
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.PagePrefill(It.IsAny<long>(), null, true, It.IsAny<CancellationToken>()))
.ReturnsAsync(new SmoRegistrationStep02ViewModel());

        // Act
        var result = await _controller.Page07(mockId, true) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page07_Get_IsValid_ShouldReturnNoPrefill()
    {
        // Arrange
        long mockId = 1;
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.GetPage06AndPage07ViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()))
    .ReturnsAsync(new SmoRegistrationStep02ViewModel());
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.PagePrefill(It.IsAny<long>(), null, false, It.IsAny<CancellationToken>()))
.ReturnsAsync(new SmoRegistrationStep02ViewModel());

        // Act
        var result = await _controller.Page07(mockId, false) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page07_Get_IsValid_ShouldReturnNoPrefillFromIsUserTreasurerUpdate()
    {
        // Arrange
        long mockId = 1;
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.GetPage06AndPage07ViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()))
    .ReturnsAsync(new SmoRegistrationStep02ViewModel() { IsUserTreasurer = true });
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.PagePrefill(It.IsAny<long>(), null, false, It.IsAny<CancellationToken>()))
.ReturnsAsync(new SmoRegistrationStep02ViewModel());

        // Act
        var result = await _controller.Page07(mockId, false) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page07_Get_IsInvalid_ShouldReturnView()
    {
        // Arrange
        long mockId = 1;
        _controller.ModelState.AddModelError("", "Invalid model state");


        // Act
        var result = await _controller.Page07(mockId, null) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page07_Post
    [Test]
    public async Task Page07_Post_IsValid_ShouldSaveAndRedirectToDashboard()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page07(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page07_Post_IsValid_ShouldContinueAndNavigateToPage08()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = await _controller.Page07(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page07_Post_IsValid_ShouldNavigateToPreviousPage()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        // Act
        var result = await _controller.Page07(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page07_Post_ShouldReturnViewWhenNoActionMatch()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Action = null,
            Id = 1,
        };

        // Act
        var result = await _controller.Page07(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page08_Get
    [Test]
    public async Task Page08_ShouldReturnView()
    {
        // Act
        var result = await _controller.Page08(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page08_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page08(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
    }

    [Test]
    public async Task Page08_ShouldReturnView_WithValidModel()
    {
        // Arrange
        var dataSource = new List<SmoOfficerGridDto>
        {
            new() {
                OfficerName = "John Doe",
                Title = "CEO",
                StartDate = _dateNow,
                PhoneNumber = new PhoneNumberDto
                {
                    InternationalNumber = false,
                    CountryCode = "1",
                    Extension = "808",
                    Number = "1111111",
                    Type = "Phone"
                }
            }
        };

        var modelGrid = new SmallDataGridModel
        {
            GridId = "OfficerContactsGrid",
            GridType = nameof(SmoOfficerGridDto),
            DataSource = dataSource,
            AllowPaging = false,
            AllowTextWrap = false,
            AllowAdding = false,
            AllowDeleting = true,
            DeleteConfirmationMessage = "deleteMessage",
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new() { Field = nameof(SmoOfficerGridDto.OfficerName), HeaderText = ResourceConstants.SmoRegistration08OfficerName },
                new() { Field = nameof(SmoOfficerGridDto.Title), HeaderText = ResourceConstants.SmoRegistration08OfficerTitle },
                new() { Field = nameof(SmoOfficerGridDto.StartDate), HeaderText = ResourceConstants.SmoRegistration08OfficerStartDate, IsUtcDate = true },
                new() { Field = nameof(SmoOfficerGridDto.PhoneNumber), HeaderText = ResourceConstants.SmoRegistration08OfficerPhoneNumber }
            },
            ActionItems = new List<GridActionItem>
            {
                new() { Label = CommonResourceConstants.Edit, Action = "editRow", ControllerName= "SmoRegistration", ActionName = "Edit" },
                new() { Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = "SmoRegistration", ActionName = "Delete" }
            }
        };

        // Act
        var result = await _controller.Page08(1) as ViewResult;

        var model = result?.Model as SmoRegistrationStep02ViewModel;
        model!.OfficerContactsGridModel = modelGrid;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
            Assert.That(model.OfficerContactsGridModel, Is.Not.Null);
        });

        // Validate Columns
        var expectedColumns = new List<string> { "OfficerName", "Title", "StartDate", "PhoneNumber" };
        foreach (var column in model.OfficerContactsGridModel.Columns)
        {
            Assert.That(expectedColumns, Does.Contain(column.Field));
        }

        // Validate Action Items
        Assert.That(model.OfficerContactsGridModel.ActionItems, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(model.OfficerContactsGridModel.ActionItems[0].Action, Is.EqualTo("editRow"));
            Assert.That(model.OfficerContactsGridModel.ActionItems[1].Action, Is.EqualTo("deleteRow"));
        });
    }
    #endregion

    #region Page08_Post
    [Test]
    public void Page08_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = _controller.Page08(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page08_Post_ShouldReturnRedirect_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Page08(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page08_Post_ShouldReturnRedirect_OnFormActionPrevious()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Page08(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page08_Post_ShouldReturnError_NoFormAction()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1
        };

        // Act
        _controller.Page08(model, default);

        // Assert
        Assert.That(_controller.ModelState.IsValid, Is.False);
    }
    #endregion

    #region Page09_Get
    [Test]
    public void Page09_ShouldReturnView()
    {
        // Act
        var result = _controller.Page09(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void Page09_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = _controller.Page09(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoRegistrationStep02ViewModel>());
    }
    #endregion

    #region Page09_Post
    [Test]
    public void Page09_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = _controller.Page09(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public void Page09_Post_ShouldReturnRedirect_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
            IsOfficer = true
        };

        // Act
        var result = _controller.Page09(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page09_Post_ShouldReturnRedirect_OnFormActionPrevious()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Page09(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Page09_Post_ShouldReturnError_NoFormAction()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1
        };

        // Act
        _controller.Page09(model, default);

        // Assert
        Assert.That(_controller.ModelState.IsValid, Is.False);
    }
    #endregion

    #region Page10
    [Test]
    public async Task Page10_Get_ShouldReturnSmoRegistrationStep02ViewModel()
    {
        // Act
        var result = await _controller.Page10(1, 1, true) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page10_Get_ShouldReturnSmoRegistrationStep02ViewModelInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page10(1, 1, true) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page10_Post_ShouldRedirect_OnFormActionSave()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Action = FormAction.Submit,
            Id = 1,
        };

        // Act
        var result = await _controller.Page10(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo(nameof(_controller.Page08)));
        });
    }

    [Test]
    public async Task Page10_Post_ShouldNotRedirect_OnFormActionSaveInvalid()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page10(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page10_Post_ShouldReturnError_NoFormAction()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            IsOfficer = true
        };

        // Act
        _ = await _controller.Page10(model, default);

        // Assert
        Assert.That(_controller.ModelState.IsValid, Is.False);
    }

    [Test]
    public async Task Page10_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        var model = new SmoRegistrationStep02ViewModel
        {
            Id = 1,
            IsOfficer = true
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page10(model, default) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page11
    [Test]
    public void Page11_ShouldReturnView()
    {
        // Act
        var result = _controller.Page11(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void Page11_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = _controller.Page11(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoRegistrationStep03ViewModel>());
    }

    [Test]
    public void Page11_Post_WithValidModel_Continue_ShouldRedirectToPage12()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Page11(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo(nameof(_controller.Page12)));
        });
    }

    [Test]
    public void Page11_Post_WithValidModel_Previous_ShouldRedirectToPage08()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Page11(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo(nameof(_controller.Page08)));
    }

    [Test]
    public void Page11_Post_WithInvalidModel_ShouldReturnViewWithModel()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel();
        _controller.ModelState.AddModelError("Error", "Invalid model");

        // Act
        var result = _controller.Page11(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public void Page11_Get_ShouldReturnView_WithNewModel()
    {
        // Act
        var result = _controller.Page11(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<SmoRegistrationStep03ViewModel>());
    }

    [Test]
    public void Page11_Post_WithValidModel_Default_ShouldReturnViewWithModelError()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel
        {
            Id = 1,
            Action = null // Default path (not Continue or Previous)
        };

        // Act
        var result = _controller.Page11(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }
    #endregion

    #region Page12
    [Test]
    public void Page12_Post_WithValidModel_Continue_ShouldRedirectToPage14()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.Page12(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo(nameof(_controller.Page14)));
    }

    [Test]
    public void Page12_Post_WithValidModel_Previous_ShouldRedirectToPage11()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel
        {
            Id = 1,
            Action = FormAction.Previous
        };

        // Act
        var result = _controller.Page12(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo(nameof(_controller.Page11)));
    }

    [Test]
    public void Page12_Post_WithInvalidModel_ShouldReturnViewWithModel()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel();
        _controller.ModelState.AddModelError("Error", "Invalid model");

        // Act
        var result = _controller.Page12(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page12_Get_ShouldReturnSmoOfficerViewModel()
    {
        // Act
        var result = await _controller.Page12(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page12_ShouldReturnView_WithValidModel()
    {
        // Arrange
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.GetPage12ViewModel(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new SmoRegistrationStep03ViewModel());

        // Act
        var result = await _controller.Page12(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page12_Get_ShouldReturnView_WithNewModel()
    {
        // Arrange
        var smoRegistrationCtlSvcMock = Substitute.For<ISmoRegistrationCtlSvc>();
        var accuMailValidatorSvcMock = Substitute.For<IAccuMailValidatorService>();
        var controller = new SmoRegistrationController(smoRegistrationCtlSvcMock, _smoRegistrationSvcMock.Object, accuMailValidatorSvcMock, _localizerMock.Object, _toastServiceMock.Object, _authorizationSvcMock.Object);
        smoRegistrationCtlSvcMock.Page13GetEmptyViewModel(Arg.Any<long>()).Returns(new SmoRegistrationStep03ViewModel());

        // Act
        var result = await controller.Page12(1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<SmoRegistrationStep03ViewModel>());
    }

    [Test]
    public void Page12_Post_WithValidModel_Default_ShouldReturnViewWithModelError()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel
        {
            Id = 1,
            Action = null // Default path (not Continue or Previous)
        };

        // Act
        var result = _controller.Page12(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }
    #endregion

    #region Page13
    [Test]
    public async Task Page13_Get_ShouldReturnSmoOfficerViewModel()
    {
        // Act
        var result = await _controller.Page13(1, null) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Page13_Get_ShouldReturnView_WithNewModel()
    {
        // Arrange
        _ = _smoRegistrationsCtlSvcMock.Setup(svc => svc.GetPage13ViewModel(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new SmoRegistrationStep03ViewModel());

        // Act
        var result = await _controller.Page13(1, 1) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.TypeOf<SmoRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page13_Post_WithValidModel_SaveAndClose_ShouldRedirectToPage12()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.Page13(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page12"));
    }

    [Test]
    public async Task Page13_Post_WithInvalidModel_ShouldReturnViewWithModelErrors()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel();
        _controller.ModelState.AddModelError("Error", "Invalid data");

        // Act
        var result = await _controller.Page13(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page13Save_WithValidModel_ShouldRedirectToPage12()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel { Id = 1 };

        // Act
        var result = await _controller.Page13Save(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Page12"));
    }

    [Test]
    public async Task Page13Save_WithInvalidModel_ShouldReturnPage12View()
    {
        // Arrange
        var model = new SmoRegistrationStep03ViewModel();
        _controller.ModelState.AddModelError("Error", "Invalid data");

        // Act
        var result = await _controller.Page13Save(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("Page13"));
            Assert.That(result.Model, Is.EqualTo(model));
        });
    }
    #endregion

    #region Page14_Get
    [Test]
    public async Task Page14_Get_ShouldReturnViewModel()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            }
        };
        _smoRegistrationsCtlSvcMock
            .Setup(x => x.GetPage14ViewModel(It.IsAny<long>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.Page14(model.Id, It.IsAny<CancellationToken>()) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task Page14_Get_ShouldReturnNotFound_InvalidModelState()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page14(123, It.IsAny<CancellationToken>()) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }
    #endregion

    #region Page14_Post
    [Test]
    public async Task Page14_Post_ShouldReturnViewPage15_WhenSendForAcknowledgement_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Continue,
            IsRequiredOtherAcknowledgements = true,
        };
        _smoRegistrationsCtlSvcMock
            .Setup(x => x.Page14SendForAcknowledgement(It.IsAny<SmoRegistrationStep04ViewModel>()));

        // Act
        var result = await _controller.Page14(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page14_Post_ShouldReturnViewPage14_WhenHasUncompletedAcknowledgement_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Continue,
        };
        var expected = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            IsRequiredOtherAcknowledgements = true,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new()
                {
                    FirstName = "Test",
                    LastName = "Test",
                    Title = "Test",
                }
            },
            Action = FormAction.Continue,
        };
        _smoRegistrationsCtlSvcMock
            .Setup(x => x.Page14Submit(It.IsAny<SmoRegistrationStep04ViewModel>()))
            .ReturnsAsync(expected);

        // Act
        var result = await _controller.Page14(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page14_Post_ShouldRedirect_OnFormActionPrevious()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Previous,
        };

        // Act
        var result = await _controller.Page14(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page14_Post_ShouldRedirect_OnFormActionClose()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
            Action = FormAction.Close,
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page14(model) as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page14_Post_ShouldReturnError_NoFormAction()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
        };

        // Act
        await _controller.Page14(model);

        // Assert
        Assert.That(_controller.ModelState.IsValid, Is.False);
    }

    [Test]
    public async Task Page14_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            IsTreasurerOrAssistantTreasurer = true,
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page14(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page15_Get
    [Test]
    public async Task Page15_Get_ShouldReturnViewModel()
    {
        // Act
        var result = await _controller.Page15(123, It.IsAny<CancellationToken>()) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    [Test]
    public async Task Page15_Get_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page15(123, It.IsAny<CancellationToken>()) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }
    #endregion

    #region Page15_Post
    [Test]
    public async Task Page15_Post_ShouldReturnViewPage16_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = true,
            IsVerificationCertified = true,
        };

        _smoRegistrationsCtlSvcMock
            .Setup(x => x.Page15AttestRegistration(It.IsAny<SmoRegistrationStep04ViewModel>(), It.IsAny<ModelStateDictionary>()));

        // Act
        var result = await _controller.Page15(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(result?.ActionName, Is.EqualTo("Page16"));
        });
    }

    [Test]
    public async Task Page15_Post_ShouldRemainPage15_IfHaveError_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
            IsVerificationCertified = false,
        };

        _smoRegistrationsCtlSvcMock
            .Setup(x => x.Page15SendForAttestation(It.IsAny<SmoRegistrationStep04ViewModel>(), It.IsAny<ModelStateDictionary>()))
            .Callback<SmoRegistrationStep04ViewModel, ModelStateDictionary>((_, modelState) =>
            {
                modelState.AddModelError("Test", "Test");
            });

        // Act
        var result = await _controller.Page15(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
            Assert.That(_controller.ModelState["Test"]?.Errors.Count, Is.GreaterThan(0));
        });
    }

    [Test]
    public async Task Page15_Post_ShouldRedirect_OnFormActionPrevious()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Previous,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };

        // Act
        var result = await _controller.Page15(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Page15_Post_ShouldRedirect_OnFormActionClose()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = FormAction.Close,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page15(model) as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page15_Post_ShouldError_AddModelError()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = null,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };

        // Act
        var result = await _controller.Page15(model) as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Page15_Post_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        var model = new SmoRegistrationStep04ViewModel
        {
            Id = 1,
            Action = null,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page15(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page16_Get
    [Test]
    public async Task Page16_Get_ShouldReturnView()
    {
        // Arrange
        var registrationId = 1;
        var model = new ConfirmationViewModel
        {
            ExecutedOn = _dateNow,
            IsSubmission = true,
            PendingItems = new List<PendingItemSharedViewModel>
            {
                new() { Item = "Mock Treasurer Acknowledgement", Status = "In Progress"},
                new() { Item = "Mock Assistance Treasurer Acknowledgement", Status = "Complete"}
            }
        };

        _smoRegistrationsCtlSvcMock
            .Setup(x => x.Page16GetViewModel(registrationId))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.Page16(123, default) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    [Test]
    public async Task Page16_Get_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        var model = new ConfirmationViewModel
        {
            ExecutedOn = _dateNow,
            IsSubmission = true,
            PendingItems = new List<PendingItemSharedViewModel>
            {
                new() { Item = "Mock Treasurer Acknowledgement", Status = "In Progress"},
                new() { Item = "Mock Assistance Treasurer Acknowledgement", Status = "Complete"}
            }
        };

        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Page16(123, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    #endregion

    #region Summary
    [Test]
    public async Task Summary_Get_ReturnsViewWithModel()
    {
        // Arrange
        var id = 1L;
        var viewModel = new SmoRegistrationSummaryViewModel { IsAmendment = false };
        _smoRegistrationsCtlSvcMock.Setup(x => x.SummaryGetViewModel(id)).ReturnsAsync(viewModel);

        // Act
        var result = await _controller.Summary(id) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.EqualTo(viewModel));

    }

    [Test]
    public async Task Summary_Post_InvalidModel_ReturnsView()
    {
        // Arrange
        var model = new SmoRegistrationSummaryViewModel { IsAmendment = false };
        _controller.ModelState.AddModelError("error", "invalid");

        // Act
        var result = await _controller.Summary(1, model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    [TestCase(false)]
    [TestCase(true)]
    public async Task Summary_Post_ContinueAction_RedirectsToTreasurerAck(bool isAmendment)
    {
        // Arrange
        var submission = new SmoRegistrationSummaryViewModel
        {
            Action = FormAction.Continue,
            IsAmendment = false,
            ShowTreasurerAcknowledgement = false,
        };
        var viewModel = new SmoRegistrationSummaryViewModel
        {
            IsAmendment = isAmendment,
            ShowTreasurerAcknowledgement = false,
        };
        _smoRegistrationsCtlSvcMock.Setup(x => x.SummaryGetViewModel(1)).ReturnsAsync(viewModel);

        // Act
        var result = await _controller.Summary(1, submission) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        if (isAmendment)
        {
            Assert.Multiple(() =>
            {
                Assert.That(result.ControllerName, Is.EqualTo("AmendSmoRegistration"));
                Assert.That(result.ActionName, Is.EqualTo("Page11"));
            });
        }
        else
        {
            Assert.That(result.ActionName, Is.EqualTo("Page14"));
        }
    }

    [Test]
    [TestCase(false)]
    [TestCase(true)]
    public async Task Summary_Post_ContinueAction_RedirectsToAttest(bool isAmendment)
    {
        // Arrange
        var submission = new SmoRegistrationSummaryViewModel
        {
            Action = FormAction.Continue,
            IsAmendment = false,
            ShowTreasurerAcknowledgement = true
        };
        var viewModel = new SmoRegistrationSummaryViewModel
        {
            IsAmendment = isAmendment,
            ShowTreasurerAcknowledgement = true,
        };
        _smoRegistrationsCtlSvcMock.Setup(x => x.SummaryGetViewModel(1)).ReturnsAsync(viewModel);

        // Act
        var result = await _controller.Summary(1, submission) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        if (isAmendment)
        {
            Assert.Multiple(() =>
            {
                Assert.That(result.ControllerName, Is.EqualTo("AmendSmoRegistration"));
                Assert.That(result.ActionName, Is.EqualTo("Page12"));
            });
        }
        else
        {
            Assert.That(result.ActionName, Is.EqualTo("Page15"));
        }
    }

    [Test]
    public async Task Summary_Post_CloseAction_RedirectsToDashboard()
    {
        // Arrange
        var model = new SmoRegistrationSummaryViewModel
        {
            Action = FormAction.Close,
            IsAmendment = false,
        };

        // Act
        var result = await _controller.Summary(1, model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task Summary_Post_InvalidAction_AddsModelErrorAndReturnsView()
    {
        // Arrange
        var model = new SmoRegistrationSummaryViewModel
        {
            Action = (FormAction)999,
            IsAmendment = false,
        };

        // Act
        var result = await _controller.Summary(1, model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState[string.Empty].Errors, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task SummaryEdit_InvalidModelState_RedirectsToSummary()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "invalid");

        // Act
        var result = await _controller.SummaryEdit(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Summary"));
    }

    [Test]
    [TestCase(false)]
    [TestCase(true)]
    public async Task SummaryEdit_RedirectsToEdit(bool isAmendment)
    {
        // Arrange
        var model = new SmoRegistrationSummaryViewModel
        {
            Action = FormAction.Continue,
            IsAmendment = false,
        };
        var viewModel = new SmoRegistrationSummaryViewModel
        {
            IsAmendment = isAmendment,
            ShowTreasurerAcknowledgement = false,
        };
        _smoRegistrationsCtlSvcMock
            .Setup(x => x.SummaryGetViewModel(1))
            .ReturnsAsync(viewModel);
        _smoRegistrationsCtlSvcMock
            .Setup(s => s.HandleRegistrationEditAsync(It.IsAny<long>()))
            .ReturnsAsync(new Services.Business.MethodResult());
        // Act
        var result = await _controller.SummaryEdit(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        if (isAmendment)
        {
            Assert.Multiple(() =>
            {
                Assert.That(result.ControllerName, Is.EqualTo("AmendSmoRegistration"));
                Assert.That(result.ActionName, Is.EqualTo("Edit"));
            });
        }
        else
        {
            Assert.That(result.ActionName, Is.EqualTo("Edit"));
        }
    }

    [Test]
    public async Task SummaryEdit_RedirectsToSummary()
    {
        // Arrange
        var model = new SmoRegistrationSummaryViewModel
        {
            Action = FormAction.Continue,
            IsAmendment = false,
        };

        // Act
        _smoRegistrationsCtlSvcMock
            .Setup(s => s.HandleRegistrationEditAsync(It.IsAny<long>()))
            .ReturnsAsync(new Services.Business.MethodResult(new InvalidOperationException("Exception")));
        var result = await _controller.SummaryEdit(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Summary"));
    }
    #endregion

    #region Shared
    [Test]
    public void PageClose_ShouldRedirectToDashboard()
    {
        // Arrange
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = _controller.PageClose() as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_ShouldReturnResult_WhenModelStateIsValid()
    {
        // Arrange
        var search = "123";
        var expected = new List<CommitteeSearchResultDto>
        {
            new() {
                Id = 1,
                Name = "Test",
                Addresses = new List<AddressDto>()
            },
        };
        _smoRegistrationSvcMock
            .Setup(s => s.SearchCommitteeByIdOrName(It.IsAny<string>()))
            .ReturnsAsync(expected);

        // Act
        var result = await _controller.SearchRecipientCommitteeByIdOrName(search, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result.Value, Is.InstanceOf<List<CommitteeSearchResultDto>>());
        Assert.That(result.Value, Is.Not.Empty);
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_ShouldReturnEmptyResult_WhenModelStateIsInvalid()
    {
        // Arrange
        var search = "";
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.SearchRecipientCommitteeByIdOrName(search, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result.Value, Is.Empty);
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_ShouldReturnEmptyResult_WhenSearchQueryParamIsEmpty()
    {
        // Arrange
        var search = "";

        // Act
        var result = await _controller.SearchRecipientCommitteeByIdOrName(search, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result.Value, Is.Empty);
    }

    [Test]
    public async Task EditOfficer_ShouldRedirectToPage07()
    {
        // Arrange
        _smoRegistrationsCtlSvcMock
            .Setup(s => s.IsTreasurer(It.IsAny<long>(), It.IsAny<long>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.EditOfficer(1, 1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo(nameof(_controller.Page07)));
    }

    [Test]
    public async Task EditOfficer_ShouldRedirectToPage10()
    {
        // Act
        var result = await _controller.EditOfficer(1, 1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo(nameof(_controller.Page10)));
    }

    [Test]
    public async Task EditIndividualAuthorizer_ShouldRedirectToPage12()
    {
        // Act
        var result = await _controller.EditIndividualAuthorizer(1, 1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task DeleteOfficer_ShouldRemainOnPage08()
    {
        //Arrange
        _ = _smoRegistrationSvcMock
            .Setup(x => x.DeleteSmoRegistrationContactsPage06(It.IsAny<long>(), It.IsAny<long>()))
            .ReturnsAsync(new RegistrationResponseDto());

        // Act
        var result = await _controller.DeleteOfficer(1, 1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task DeleteOfficer_ShouldNotFound()
    {
        //Arrange
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.DeleteOfficer(1, 1) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ShouldReturnJsonResult_WithValidModel()
    {
        // Arrange
        long contactId = 1;
        long registrationId = 1;
        var mockGridModel = new SmallDataGridModel();
        var mockViewModel = new SmoRegistrationStep03ViewModel
        {
            IndividualAuthorizersGridModel = mockGridModel
        };

        _smoRegistrationsCtlSvcMock
            .Setup(svc => svc.Page12DeleteIndividualAuthorizer(registrationId, contactId))
            .ReturnsAsync(mockViewModel);

        // Act
        var result = await _controller.DeleteIndividualAuthorizer(registrationId, contactId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.EqualTo(mockGridModel));
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ShouldReturnEmptyJsonResult_WhenGridModelIsNull()
    {
        // Arrange
        long contactId = 1;
        long registrationId = 1;
        var mockViewModel = new SmoRegistrationStep03ViewModel
        {
            IndividualAuthorizersGridModel = null
        };

        _smoRegistrationsCtlSvcMock
            .Setup(svc => svc.Page12DeleteIndividualAuthorizer(registrationId, contactId))
            .ReturnsAsync(mockViewModel);

        // Act
        var result = await _controller.DeleteIndividualAuthorizer(registrationId, contactId);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ShouldReturnEmptyJsonResult_WhenViewModelIsNull()
    {
        // Arrange
        long contactId = 1;
        long registrationId = 1;

        _smoRegistrationsCtlSvcMock
            .Setup(svc => svc.Page12DeleteIndividualAuthorizer(registrationId, contactId))
            .ReturnsAsync((SmoRegistrationStep03ViewModel?)null);

        // Act
        var result = await _controller.DeleteIndividualAuthorizer(registrationId, contactId);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ShouldReturnNotFound_WhenModelStateIsInvalid()
    {
        // Arrange  
        _controller.ModelState.AddModelError("Test", "Error"); // Adding an error to make the model state invalid  
        long contactId = 1;
        long registrationId = 1;

        // Act  
        var result = await _controller.DeleteIndividualAuthorizer(registrationId, contactId);

        // Assert  
        Assert.That(result, Is.Not.Null);
        Assert.That(result.StatusCode, Is.EqualTo(404));
    }
    #endregion
}
