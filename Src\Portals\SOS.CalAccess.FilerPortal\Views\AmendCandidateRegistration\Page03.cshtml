@using SOS.CalAccess.FilerPortal.Models.Localization
@using Microsoft.AspNetCore.Mvc.ModelBinding;
@using SOS.CalAccess.FilerPortal.Models.Registrations.AmendCandidateRegistration
@using SOS.CalAccess.Foundation.Utils
@using SOS.CalAccess.UI.Common.Enums;
@inject IHtmlLocalizer<SharedResources> Localizer;
@inject IDateTimeSvc DateTimeSvc

@model AmendCandidateRegistrationStep03ViewModel
@{
    // FR-CAND-A-VEL
    var progressItems = new List<ProgressItem>
    {
        new ProgressItem(ViewData["ProgressItem1Name"]?.ToString() ?? "", true, false, true, $"/AmendCandidateRegistration/Page01/{Model.Id}", true),
        new ProgressItem(ViewData["ProgressItem2Name"]?.ToString() ?? "", false, true, false, $"/AmendCandidateRegistration/Page02/{Model.Id}", true),
        new ProgressItem(ViewData["ProgressItem3Name"]?.ToString() ?? "", false, false, true, $"/AmendCandidateRegistration/Page04/{Model.Id}", true),
    };
    var progressBar = new ProgressBar(progressItems);

    var buttonConfig = new ButtonBarModel
            {
                RightButtons = new()
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", Model.Id),
            },
            ButtonBarModel.DefaultSaveAndClose,
        }
            };
}

<partial name="_LayoutProgressbar" model="@progressBar" />

<div class="d-flex flex-column" style="gap: 1rem">

    <partial name="_CisExpenditureLimitHeader" model="new { ElectionYear = Model?.ElectionDate?.Year, ExpenditureLimitAmount = Model?.ExpenditureLimitAmount }" />

    @using (Html.BeginForm("Page03", "AmendCandidateRegistration", FormMethod.Post))
    {
        @Html.AntiForgeryToken()

        @Html.HiddenFor(m => m.Id, Model?.Id)
        @Html.HiddenFor(m => m.ElectionDate, Model?.ElectionDate)
        @Html.HiddenFor(m => m.ExpenditureLimitAmount, Model?.ExpenditureLimitAmount)
        @Html.HiddenFor(m => m.IsLimitRadioReadOnly, Model?.IsLimitRadioReadOnly)
        @Html.HiddenFor(m => m.IsLimitCheckboxReadOnly, Model?.IsLimitCheckboxReadOnly)
        @Html.HiddenFor(m => m.IsPersonalFundsSectionReadOnly, Model?.IsPersonalFundsSectionReadOnly)
        @if (Model?.IsPersonalFundsSectionReadOnly ?? false)
        {
            @Html.HiddenFor(m => m.ContributedPersonalExcessFundsOn)
        }

        @Html.Radio(SharedLocalizer, "ExpenditureLimitAccepted",
            Localizer[ResourceConstants.CisRegistration07ExpenditureLimitRadioLabel].Value,
            new Dictionary<string, string>
            {
                { "true",  Localizer[ResourceConstants.CisRegistration07ExpenditureLimitAccept].Value },
                { "false",  Localizer[ResourceConstants.CisRegistration07ExpenditureLimitDecline].Value },
            },
            Model?.ExpenditureLimitAccepted ?? "",
            required: true,
            readOnly: Model?.IsLimitRadioReadOnly ?? false
        )
        <div style="margin-left: 1.5rem" class="mt-1 mb-3">
            <div class="mb-1 text-secondary">
                @Localizer[ResourceConstants.AmendCis03AmendmentBody].Value
            </div>
            @Html.CheckBoxFor(
                SharedLocalizer,
                m => m.ExpenditureExceeded,
                Localizer[ResourceConstants.AmendCis03AmendmentCheckbox].Value,
                readOnly: Model?.IsLimitCheckboxReadOnly ?? false
            )
        </div>

        <div class="mb-1">@Localizer[ResourceConstants.AmendCis03PersonalFundsHeader].Value</div>
        @Html.CheckBoxFor(
            SharedLocalizer,
            m => m.DidContributedPersonalExcessFunds,
            Localizer[ResourceConstants.AmendCis03PerosnalFundsCheckbox].Value,
            readOnly: Model?.IsPersonalFundsSectionReadOnly ?? false
        )
        <div style="transform:translateY(-0.65rem)" class="mb-3 col-sm-4 col-md-5 col-lg-3">
            <label class="visually-hidden" for="ContributedPersonalExcessFundsOn">@Localizer[ResourceConstants.AmendCis03PerosnalFundsCheckbox].Value</label>
            @Html.DatePickerFor(
                SharedLocalizer,
                m => m.ContributedPersonalExcessFundsOn,
                "",
                minDate: null,
                maxDate: DateTimeSvc.GetCurrentDateTime(),
                format: "MM/dd/yyyy",
                isRequired: true,
                isReadOnly: Model?.IsPersonalFundsSectionReadOnly ?? false,
                cssClass: "override-blank",
                placeholderResourceKey: "MM/DD/YYYY"
            )
        </div>

        <partial name="_ButtonBar" model="buttonConfig" />
    }
</div>
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {

        /// Enable datepicker if personal funds checkbox checked.
        const personalFundsCheckbox = document.getElementById("DidContributedPersonalExcessFunds");
        const datepicker = document.getElementById("ContributedPersonalExcessFundsOn");
        if (datepicker instanceof HTMLInputElement && datepicker.type !== 'hidden') {
            const dateInstance = ej.base.getComponent(datepicker, 'datepicker');
            function toggleDatePicker() {
                if (personalFundsCheckbox.checked) {
                    dateInstance.enabled = true;
                    dateInstance.readonly = false;
                } else {
                    dateInstance.enabled = false;
                    dateInstance.readonly = true;
                    dateInstance.value = null;
                }
            }
            personalFundsCheckbox.addEventListener("change", toggleDatePicker);
        }

        /// Auto select 'I accept' radio if 'I did not exceed...' checkbox checked.
        const acceptRadio = document.getElementById("ExpenditureLimitAccepted_true");
        const declineRadio = document.getElementById("ExpenditureLimitAccepted_false");
        const amendmentCheckbox = document.getElementById("ExpenditureExceeded");
        const previousAccepted = @(Model?.PreviousExpenditureLimitAccepted == "true" ? "true" : "false");
        function toggleRadio() {
            // Radio should not switch when user already selected 'I accept' radio in previous accepted amendment.
            if (previousAccepted) {
                return;
            }

            if (amendmentCheckbox.checked) {
                acceptRadio.disabled = false;
                acceptRadio.checked = true;
                declineRadio.checked = false;
                declineRadio.disabled = true;
            } else {
                acceptRadio.checked = false;
                acceptRadio.disabled = true;
                declineRadio.disabled = false;
                declineRadio.checked = true;
            }
        }
        amendmentCheckbox.addEventListener("change", toggleRadio);
        

        // Hide datepicker common label when enabled. Handled by custom label.
        document.querySelector('label.form-label[for="ContributedPersonalExcessFundsOn"]')?.style.setProperty('display', 'none');
        // Hide datepicker common label when readonly. Handled by custom label.
        document.querySelector('span.readonly-label#ContributedPersonalExcessFundsOn_label')?.style.setProperty('display', 'none');
    });
</script>
