using System.Globalization;
using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Moq;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Disclosure;
using SOS.CalAccess.FilerPortal.Models.Filings;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Extensions;
using SOS.CalAccess.UI.Common.Localization;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
using OtherPaymentsToInfluenceResponse = SOS.CalAccess.FilerPortal.Generated.OtherPaymentsToInfluenceResponse;
using PaymentMadeToLobbyingCoalitionResponse = SOS.CalAccess.FilerPortal.Generated.PaymentMadeToLobbyingCoalitionResponse;
using PaymentMadeToLobbyingFirmsResponse = SOS.CalAccess.FilerPortal.Generated.PaymentMadeToLobbyingFirmsResponse;
using PaymentReceiveLobbyingCoalitionResponse = SOS.CalAccess.FilerPortal.Generated.PaymentReceiveLobbyingCoalitionResponse;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public class DisclosureCtlSvcTests
{
    private Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
    private Mock<IFilingsApi> _mockFilingsApi;
    private Mock<ILobbyistApi> _mockLobbyistApi;
    private Mock<ILobbyistEmployerApi> _mockLobbyistEmployerApi;
    private Mock<IFilingSummaryApi> _mockFilingSummaryApi;
    private Mock<IActivityExpenseApi> _mockActivityExpenseApi;
    private Mock<ILobbyistEmployerCoalitionApi> _mockLobbyistEmployerCoalitionApi;
    private Mock<IReferenceDataApi> _mockReferenceDataApi;
    private Mock<ILobbyingAdvertisementApi> _mockLobbyingAdvertisementApi;
    private Mock<ITransactionsApi> _mockTransactionsApi;
    private DisclosureCtlSvc _disclosureCtlSvc;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
        _mockActivityExpenseApi = new Mock<IActivityExpenseApi>();
        _mockFilingSummaryApi = new Mock<IFilingSummaryApi>();
        _mockFilingsApi = new Mock<IFilingsApi>();
        _mockLobbyistApi = new Mock<ILobbyistApi>();
        _mockLobbyistEmployerApi = new Mock<ILobbyistEmployerApi>();
        _mockLobbyistEmployerCoalitionApi = new Mock<ILobbyistEmployerCoalitionApi>();
        _mockReferenceDataApi = new Mock<IReferenceDataApi>();
        _mockLobbyingAdvertisementApi = new Mock<ILobbyingAdvertisementApi>();
        _mockTransactionsApi = new Mock<ITransactionsApi>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var localizedString = new LocalizedString("key", "text");
        _mockLocalizer
            .Setup(x => x[It.IsAny<string>()]).Returns(localizedString);

        _disclosureCtlSvc = new DisclosureCtlSvc(_mockFilingsApi.Object, _mockActivityExpenseApi.Object, _mockFilingSummaryApi.Object, _mockLobbyistApi.Object, _mockLobbyistEmployerApi.Object, _mockLobbyistEmployerCoalitionApi.Object, _mockLobbyingAdvertisementApi.Object, _mockTransactionsApi.Object, _mockReferenceDataApi.Object, _mockLocalizer.Object);
    }

    [Test]
    public async Task GetDisclosureFilingReports_ShouldReturnDisclosureReportList()
    {
        // Arrange
        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100,100),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200,200),
        };

        _ = _mockFilingsApi.Setup(api => api.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(mockFilingSummaries);

        // Act
        var result = await _disclosureCtlSvc.GetDisclosureFilingReportsById(It.IsAny<long>(), It.IsAny<CancellationToken>());

        // Assert
        Assert.That(result, Has.Count.EqualTo(mockFilingSummaries.Count));
    }

    [Test]
    public async Task UpdateFilingSummaryById_CallsUpdateFilingSummaryStatusById_WithCorrectParameters()
    {
        // Arrange
        long filingSummaryId = 123;
        string filingSummaryStatusName = "Approved";
        bool nothingToReport = false;
        var cancellationToken = CancellationToken.None;

        // Act
        await _disclosureCtlSvc.UpdateFilingSummaryById(filingSummaryId, filingSummaryStatusName, nothingToReport, cancellationToken);

        // Assert
        _mockFilingSummaryApi.Verify(
            api => api.UpdateFilingSummaryStatusById(
                filingSummaryId,
                It.Is<FilingSummaryRequest>(req =>
                    req.FilingSummaryStatusName == filingSummaryStatusName &&
                    req.NothingToReport == nothingToReport),
                cancellationToken),
            Times.Once
        );
    }

    [Test]
    public async Task UpdateFilingSummaryById_CallsUpdateFilingSummaryStatusById_WithCancellationToken()
    {
        // Arrange
        long filingSummaryId = 123;
        string filingSummaryStatusName = "Approved";
        bool nothingToReport = true;
        var cancellationToken = new CancellationTokenSource().Token;

        // Act
        await _disclosureCtlSvc.UpdateFilingSummaryById(filingSummaryId, filingSummaryStatusName, nothingToReport, cancellationToken);

        // Assert
        _mockFilingSummaryApi.Verify(
            api => api.UpdateFilingSummaryStatusById(
                filingSummaryId,
                It.Is<FilingSummaryRequest>(req =>
                    req.FilingSummaryStatusName == filingSummaryStatusName &&
                    req.NothingToReport == nothingToReport),
                cancellationToken),
            Times.Once
        );
    }

    [Test]
    public async Task GetGeneralInfoViewForLobbyist_ReturnsExpectedViewModel()
    {
        // Arrange
        long filerId = 1;
        long filingId = 2;

        var report = new LobbyistReportResponse(
            id: 123,
            filerId: 456,
            startDate: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            endDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            diligenceStatementVerified: false,
            filerName: "Foo",
            parentId: 123,
            status: 1,
            submittedDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            version: 1,
            filingTypeId: 16
        );

        var lobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new(
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Home",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 789,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<PhoneNumberDto>
            {
                new("+1", null, "987", 1, false, "1011231234", 1, false, "Work"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"
        );

        _mockLobbyistApi.Setup(api => api.GetLobbyistByFilerId(filerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(lobbyist);

        _mockFilingsApi.Setup(api => api.GetLobbyistReportFiling(filingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(report);

        // Act
        var result = await _disclosureCtlSvc.GetGeneralInfoViewForLobbyist(filerId, filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ReportType, Is.EqualTo("Lobbyist report"));
            Assert.That(result.FilingTypeId, Is.EqualTo(FilingTypeModel.LobbyistReport.Id));
        });
    }

    [Test]
    public async Task GetDisclosureFilingReports_ReturnsResultNotEmpty()
    {
        // Arrange
        var reports = new List<FilingReportGridDto>()
        {
            new
            (
                id: 123,
                reportName: "Test Report",
                createdDate: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
                reportPeriod: "Q1 2023",
                status: "In Progress",
                typeId: 1,
                isAllowedAmending: true
            )
        };

        // Fix: Ensure the correct type is used for the mock setup
        _mockFilingsApi.Setup(api => api.GetDisclosureFilingReports(It.IsAny<CancellationToken>()))
            .ReturnsAsync(reports);

        // Act
        var result = await _disclosureCtlSvc.GetDisclosureFilingReports();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count, Is.AtLeast(1));
    }

    [Test]
    public async Task GetDisclosureFilingReports_ReturnsNull()
    {
        // Arrange
        // Fix: Ensure the correct type is used for the mock setup
        _mockFilingsApi.Setup(api => api.GetDisclosureFilingReports(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<FilingReportGridDto>());

        // Act
        var result = await _disclosureCtlSvc.GetDisclosureFilingReports();

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsDashboardView_WhenViewNameEmpty()
    {
        // Arrange
        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100, 0),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200, 0),
        };
        var mockFilingItemResponse = new FilingItemResponse("AmendmentExplanation", _dateNow, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, _dateNow.AddMonths(-1), 1, _dateNow, 1, 1);

        _mockFilingsApi.Setup(x => x.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingSummaries);
        _mockFilingsApi.Setup(x => x.GetFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingItemResponse);
        _mockFilingsApi.Setup(x => x.GetAllFilingPeriodsForLobbying(
                It.IsAny<long>(),
                It.IsAny<long?>(),
                It.IsAny<long?>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<FilingPeriodDto>
            {
                new FilingPeriodDto (endDate: _dateNow, hasFiling: true, id: 1, startDate: _dateNow, isRemoved: false),
                new FilingPeriodDto (endDate: _dateNow, hasFiling: false, id: 2, startDate: _dateNow, isRemoved: false),
            });

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName: null,
            filerId: "123",
            filingId: "456",
            filingSummaryId: "789",
            filingStatus: "1",
            reportType: null,
            mockHttpContext.Object,
            mockTempData.Object,
            CancellationToken.None);

        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("Dashboard"));
            Assert.That(result.TransactionEntries, Has.Count.EqualTo(2));
        });
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForActivityExpenseSummary()
    {
        var viewName = nameof(FilingSummaryTypeModel.ActivityExpenseSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        // Arrange
        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100, 0),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200, 0),
        };

        var mockActivityExpenseItems = new List<ActivityExpenseItemResponse> {
            new(10, 19, 1, 1, 1, "note", "Tom", "Individual", "Tim", null)
        };

        _mockFilingsApi.Setup(x => x.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingSummaries);

        _mockActivityExpenseApi.Setup(x => x.GetAllActivityExpenseTransactionsForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ActivityExpenseSummaryResponse(
            activityExpenseItems: mockActivityExpenseItems,
            subtotal: 10
        ));

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingStatus, filingSummaryId, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActivityExpensesGridModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForCampaignContribution()
    {
        var viewName = nameof(FilingSummaryTypeModel.CampaignContributionSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        // Arrange
        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100, 0),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200, 0),
        };

        var mockContributions = new List<LobbyistEmployerCampaignContributionItemResponse> {
            new(10, 19, 1, 1, "note", It.IsAny<FilerItemResponse>(), 1, "Tom", "Individual", null)
        };

        var mockLobbyistEmployerCampaignContributionGridModel = new LobbyistEmployerCampaignContributionGridModel()
        {
            FilingId = 1
        };

        _mockFilingsApi.Setup(x => x.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingSummaries);

        _mockLobbyistEmployerCoalitionApi.Setup(x => x.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContributions);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingStatus, filingSummaryId, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.CampaignContributionsGridModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForCoalitionPayments()
    {
        var viewName = nameof(FilingSummaryTypeModel.ToLobbyingCoalitionSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        // Arrange
        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100, 0),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200, 0),
        };

        var mockReport = new LobbyistEmployerReportResponse(false, true, _dateNow, 1, "Tom", 1, true, null, _dateNow, 1, _dateNow, 10, 20, 30, 0);

        var mockPayments = new List<PaymentMadeToLobbyingCoalitionResponse> {
            new(10, "Coalition", 20, 1, 2, 1, 1, 1)
        };

        _mockFilingsApi.Setup(x => x.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingSummaries);

        _mockFilingsApi.Setup(x => x.GetLobbyistEmployerReportFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockReport);

        _mockLobbyistEmployerCoalitionApi.Setup(x => x.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockPayments);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingStatus, filingSummaryId, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.CoalitionPaymentsGridModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForOtherPayments()
    {
        var viewName = nameof(FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        // Arrange
        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100, 0),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200, 0),
        };

        var mockReport = new LobbyistEmployerReportResponse(false, true, _dateNow, 1, "Tom", 1, true, null, _dateNow, 1, _dateNow, 10, 20, 30, 0);

        var mockOtherPayments = new List<OtherPaymentsToInfluenceResponse>
        {
            new(10, 20, 1, "test", "test", It.IsAny<CandidateContact>(), "Salary (S)",
                null, "Individual"
            )
        };

        _mockFilingsApi.Setup(x => x.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingSummaries);

        _mockFilingsApi.Setup(x => x.GetLobbyistEmployerReportFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockReport);

        _mockLobbyistEmployerCoalitionApi.Setup(x => x.GetAllOtherPaymentsToInfluenceTransactionsForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockOtherPayments);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingStatus, filingSummaryId, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.OtherPaymentsGridModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForReceivedCoalitionPayments()
    {
        var viewName = nameof(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        // Arrange
        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100, 0),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200, 0),
        };

        var mockCoalitionRecievedPayments = new List<PaymentReceiveLobbyingCoalitionResponse> {
            new(10, "Coalition", It.IsAny<ContactResponseDto>(), 10, It.IsAny<long>())
        };

        _mockFilingsApi.Setup(x => x.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingSummaries);

        _mockLobbyistEmployerCoalitionApi.Setup(x => x.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockCoalitionRecievedPayments);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.CoalitionReceivedGridModel, Is.Not.Null);
        });
    }


    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForPUCActivity()
    {
        var viewName = nameof(FilingSummaryTypeModel.PucActivitySummary);
        var filerId = 123;
        var filingId = 456;
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        // Arrange

        var mockFiling = new FilingItemResponse(
            "test",
            _dateNow,
            filerId,
            "Sample name",
            1,
            "Submitted",
            "LobbyistEmployerReport",
            filingId,
            1,
            1,
            1,
            _dateNow.AddMonths(-1),
            1,
            _dateNow,
            1,
            1
            );

        _mockFilingsApi.Setup(x => x.GetFiling(
                It.IsAny<long>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFiling);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId.ToString(CultureInfo.InvariantCulture), filingId.ToString(CultureInfo.InvariantCulture), filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.TotalPaymentsPucActivity, Is.Not.Null);
        });
    }


    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForMadeToLobbyingFirmPayments()
    {
        var viewName = nameof(FilingSummaryTypeModel.MadeToLobbyingFirmsSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        // Arrange
        var mockFilingSummaries = new List<FilingSummary>
        {
            new(1, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "In Progress"), 1, new FilingSummaryType(1, 1, 1, "Activity Expense", 1), 1, 1, 1, false, [], 100, 100, 0),
            new(2, It.IsAny<Filing>(), 1, new FilingSummaryStatus(1, "Not Started"), 2, new FilingSummaryType(1, 2, 1, "Campaign Contributions", 2), 2, 2, 2, false, [], 200, 200, 0),
        };

        var mockFirmPayments = new List<PaymentMadeToLobbyingFirmsResponse> {
            new(10, "test", 1, It.IsAny<ContactResponseDto>(),10, 10, 1, "Firm", 1)
        };

        _mockFilingsApi.Setup(x => x.GetFilingSummaries(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilingSummaries);

        _mockLobbyistEmployerCoalitionApi.Setup(x => x.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFirmPayments);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingStatus, filingSummaryId, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FirmPaymentsGridModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task BuildPaymentsToInHouseLobbyistsModel_ReturnsValidModel_WhenIdsProvided()
    {
        // Arrange
        const long expectedFilerId = 123;
        const long expectedFilingId = 456;
        const double expectedTotalPayments = 1000;

        var mockReport = new LobbyistEmployerReportResponse(false, true, _dateNow, expectedFilerId, "Tom", expectedFilingId, true, null, _dateNow, 1, _dateNow, 10, expectedTotalPayments, 30, 0);


        _mockFilingsApi.Setup(x => x.GetLobbyistEmployerReportFiling(
                expectedFilingId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockReport);

        // Act
        var result = await _disclosureCtlSvc.BuildPaymentsToInHouseLobbyistsModel(
            expectedFilerId.ToString(CultureInfo.InvariantCulture),
            expectedFilingId.ToString(CultureInfo.InvariantCulture),
            CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FilerId, Is.EqualTo(expectedFilerId));
            Assert.That(result.Id, Is.EqualTo(expectedFilingId));
            Assert.That(result.TotalPaymentsToInHouseLobbyists, Is.EqualTo(expectedTotalPayments));
        });
    }

    [Test]
    public async Task BuildAmendmentExplanationModel_ReturnsValidModel_WhenIdsProvided()
    {
        // Arrange
        const long expectedFilerId = 123;
        const long expectedFilingId = 456;
        const string expectedAmendmentExplanation = "Explanation text";

        var mockFiling = new FilingItemResponse(
            expectedAmendmentExplanation,
            _dateNow,
            expectedFilerId,
            "Sample name",
            1,
            "Submitted",
            "LobbyistEmployerReport",
            expectedFilingId,
            1,
            1,
            1,
            _dateNow.AddMonths(-1),
            1,
            _dateNow,
            1,
            1
            );

        _mockFilingsApi.Setup(x => x.GetFiling(
                expectedFilingId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFiling);

        // Act
        var result = await _disclosureCtlSvc.BuildAmendmentExplanationModel(
            expectedFilerId.ToString(CultureInfo.InvariantCulture),
            expectedFilingId.ToString(CultureInfo.InvariantCulture),
            CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FilerId, Is.EqualTo(expectedFilerId));
            Assert.That(result.Id, Is.EqualTo(1));
            Assert.That(result.AmendmentExplanation, Is.EqualTo(expectedAmendmentExplanation));
        });
    }

    [Test]
    public async Task GetFilingById_CallsGetFilingWithCorrectParametersAndReturnsResult()
    {
        // Arrange
        long filingId = 123;
        var cancellationToken = CancellationToken.None;

        var expectedResponse = new FilingItemResponse
        (
            id: 123,
            endDate: _dateNow,
            filerId: 456,
            parentId: null,
            startDate: _dateNow.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "Test Filing Type",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        _ = _mockFilingsApi.Setup(api => api.GetFiling(filingId, cancellationToken))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _disclosureCtlSvc.GetFilingById(filingId, cancellationToken);

        // Assert
        _mockFilingsApi.Verify(api => api.GetFiling(filingId, cancellationToken), Times.Once);
        Assert.That(result, Is.SameAs(expectedResponse));
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForActionsLobbiedSummary()
    {
        // Arrange
        var viewName = nameof(FilingSummaryTypeModel.ActionsLobbiedSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        var mockActionsLobbiedResponse = new ActionsLobbiedSummaryResponse(new List<ActionsLobbiedResponseDto>(),
            new List<ActionsLobbiedResponseDto>(), 1, 1, new FilingSummaryStatus(1, "Draft"), "a",
            new List<ActionsLobbiedResponseDto>(), true, new List<WorkFlowError>());

        var mockAdminActions = new List<AdministrativeActionViewModel>
        {
            new() { Id = 1, AgencyId = 101, AdministrativeAction = "Test Action 1", AgencyDescription = "Agency 1", Name = "Agency 1" },
            new() { Id = 2, AgencyId = 102, AdministrativeAction = "Test Action 2", AgencyDescription = "Agency 2", Name = "Agency 2" }
        };

        var mockAssemblyBills = new List<LegislativeBillViewModel>
        {
            new() { Id = 1, Number = "AB123", Title = "Assembly Bill 123" },
            new() { Id = 2, Number = "AB456", Title = "Assembly Bill 456" }
        };

        var mockSenateBills = new List<LegislativeBillViewModel>
        {
            new() { Id = 1, Number = "SB123", Title = "Senate Bill 123" },
            new() { Id = 2, Number = "SB456", Title = "Senate Bill 456" }
        };

        _mockFilingsApi.Setup(x => x.GetActionsLobbiedSummaryForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockActionsLobbiedResponse);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.AdministrativeActionGridModel, Is.Not.Null);
            Assert.That(result.LegislationAssemblyBillGridModel, Is.Not.Null);
            Assert.That(result.LegislationSenateBillGridModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_FromTempdata_ForActionsLobbiedSummary()
    {
        // Arrange
        var viewName = nameof(FilingSummaryTypeModel.ActionsLobbiedSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        var mockActionsLobbiedResponse = new ActionsLobbiedSummaryResponse(
            new List<ActionsLobbiedResponseDto>(),
            new List<ActionsLobbiedResponseDto>(), 1, 1, new FilingSummaryStatus(1, "Draft"), "a",
            new List<ActionsLobbiedResponseDto>(), true, new List<WorkFlowError>());

        _mockFilingsApi.Setup(x =>
                x.GetActionsLobbiedSummaryForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockActionsLobbiedResponse);

        var mockHttpContext = new Mock<HttpContext>();


        var mockTempData = new Mock<ITempDataDictionary>();

        // Ensure that the mock setup for TempData returns a valid JSON-serializable object
        mockTempData.Setup(tempData => tempData["DisclosureSummaryViewModel"])
            .Returns(JsonConvert.SerializeObject(new DisclosureSummaryViewModel
            {
                Messages = new PortalAlerts
                {
                    Validations = new Dictionary<string, ValidationMessage>
                    {
                        { "ValidationKey1", new ValidationMessage { Message = "Validation message 1" } }
                    }
                }
            }));

        // Act

        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.AdministrativeActionGridModel, Is.Not.Null);
            Assert.That(result.LegislationAssemblyBillGridModel, Is.Not.Null);
            Assert.That(result.LegislationSenateBillGridModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task GetGeneralInfoViewForLobbyistEmployer_ReturnsExpectedViewModel()
    {
        // Arrange
        long filerId = 1;
        long filingId = 2;
        var expectedCumulativePeriodStart = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture);

        var lobbyistEmployer = new LobbyistEmployerResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new(
                    city: "Test City",
                    country: "USA",
                    purpose: "Business",
                    state: "CA",
                    street: "123 Business St",
                    street2: "",
                    type: "Business",
                    zip: "12345"
                )
            },
            addressListId: 789,
            businessActivity: "Lobbying Services",
            businessDescription: "Professional Lobbying",
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer Corp",
            employerType: "Business",
            filerId: filerId,
            id: 567,
            industryDescription: "Consulting",
            industryGroupClassification: "Consulting Services",
            industryPortion: "Government Relations",
            interestType: "Industry",
            legislativeSessionId: 1,
            memberNames: new List<LobbyingEmployerGroupMemberDto>(),
            name: "Test Employer Corp",
            numberOfMembers: 5,
            phoneNumberListId: 101,
            phoneNumbers: new List<PhoneNumberDto>
            {
                new(
                    selectedCountry: 1,
                    countryCode: "+1",
                    countryId: null,
                    internationalNumber: false,
                    number: "5551234",
                    extension: "",
                    type: "Business",
                    id: 1,
                    setAsPrimaryPhoneNumber: false
                )
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: new List<RegistrationAgencyDto>(),
            isLobbyingCoalition: false,
            natureAndPurpose: "purpose",
            lobbyingInterest: "interest"
        );

        var report = new LobbyistEmployerReportResponse(
            id: 123,
            filerId: filerId,
            startDate: DateTime.Parse("2023-03-01", CultureInfo.InvariantCulture),
            endDate: DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
            diligenceStatementVerified: true,
            filerName: "Test Employer Corp",
            parentId: null,
            status: 1,
            submittedDate: null,
            version: 1,
            totalPaymentsToInHouseLobbyists: 5000.0,
            totalOverheadExpense: 1000.0,
            totalUnderThresholdPayments: 500.0,
            isMemberOfLobbyingCoalition: true,
            contributionsInExistingStatements: false
        );

        _ = _mockLobbyistEmployerApi.Setup(api => api.GetLobbyistEmployerByFilerId(filerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(lobbyistEmployer);

        _ = _mockFilingsApi.Setup(api => api.GetLobbyistEmployerReportFiling(filingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(report);

        _ = _mockFilingsApi.Setup(api => api.GetCumulativePeriodStartByFilingId(filingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCumulativePeriodStart);

        // Act
        var result = await _disclosureCtlSvc.GetGeneralInfoViewForLobbyistEmployer(filerId, filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ReportType, Is.EqualTo("Report of Lobbyist Employer or Report of Lobbying Coalition"));
            Assert.That(result.FilingTypeId, Is.EqualTo(FilingTypeModel.LobbyistEmployerReport.Id));
            Assert.That(result.CumulativePeriodStartDate, Is.EqualTo(expectedCumulativePeriodStart.Date));
            Assert.That(result.Id, Is.EqualTo(report.Id));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
        });

        _mockLobbyistEmployerApi.Verify(api => api.GetLobbyistEmployerByFilerId(filerId, It.IsAny<CancellationToken>()), Times.Once);
        _mockFilingsApi.Verify(api => api.GetLobbyistEmployerReportFiling(filingId, It.IsAny<CancellationToken>()), Times.Once);
        _mockFilingsApi.Verify(api => api.GetCumulativePeriodStartByFilingId(filingId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task GetGeneralInfoViewForLobbyistEmployer_HandlesNullLobbyistEmployer()
    {
        // Arrange
        long filerId = 999;
        long filingId = 888;
        var expectedCumulativePeriodStart = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture);

        var report = new LobbyistEmployerReportResponse(
            id: 123,
            filerId: filerId,
            startDate: DateTime.Parse("2023-03-01", CultureInfo.InvariantCulture),
            endDate: DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
            diligenceStatementVerified: true,
            filerName: "Test Corp",
            parentId: null,
            status: 1,
            submittedDate: null,
            version: 1,
            totalPaymentsToInHouseLobbyists: 5000.0,
            totalOverheadExpense: 1000.0,
            totalUnderThresholdPayments: 500.0,
            isMemberOfLobbyingCoalition: true,
            contributionsInExistingStatements: false
        );

        _ = _mockFilingsApi.Setup(api => api.GetLobbyistEmployerReportFiling(filingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(report);

        _ = _mockFilingsApi.Setup(api => api.GetCumulativePeriodStartByFilingId(filingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCumulativePeriodStart);

        // Act
        var result = await _disclosureCtlSvc.GetGeneralInfoViewForLobbyistEmployer(filerId, filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ReportType, Is.EqualTo("Report of Lobbyist Employer or Report of Lobbying Coalition"));
            Assert.That(result.FilingTypeId, Is.EqualTo(FilingTypeModel.LobbyistEmployerReport.Id));
            Assert.That(result.CumulativePeriodStartDate, Is.EqualTo(expectedCumulativePeriodStart.Date));
            Assert.That(result.Id, Is.EqualTo(report.Id));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
        });
    }

    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_Report72H_ForActionsLobbiedSummary()
    {
        // Arrange
        var viewName = nameof(FilingSummaryTypeModel.ActionsLobbiedSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Report72h";

        var mockActionsLobbiedResponse = new ActionsLobbiedSummaryResponse(new List<ActionsLobbiedResponseDto>(),
            new List<ActionsLobbiedResponseDto>(), 1, 1, new FilingSummaryStatus(1, "Draft"), "a",
            new List<ActionsLobbiedResponseDto>(), true, new List<WorkFlowError>());

        _mockFilingsApi.Setup(x => x.GetActionsLobbiedSummaryForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockActionsLobbiedResponse);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.AdministrativeActionGridModel, Is.Not.Null);
            Assert.That(result.LegislationAssemblyBillGridModel, Is.Not.Null);
            Assert.That(result.LegislationSenateBillGridModel, Is.Not.Null);
            Assert.That(result.AdministrativeActionGridModel?.Columns.Any(c => c.HeaderText == "Agency/Office"), Is.True);
            Assert.That(result.AdministrativeActionGridModel?.Columns.Any(c => c.HeaderText == "Administrative Actions"), Is.True);
            Assert.That(result.AdministrativeActionGridModel?.Columns.Any(c => c.HeaderText == "Position"), Is.True);
            Assert.That(result.LegislationAssemblyBillGridModel?.Columns.Any(c => c.HeaderText == "Bill Number"), Is.True);
            Assert.That(result.LegislationAssemblyBillGridModel?.Columns.Any(c => c.HeaderText == "Bill Title"), Is.True);
            Assert.That(result.LegislationAssemblyBillGridModel?.Columns.Any(c => c.HeaderText == "Position"), Is.True);
            Assert.That(result.LegislationSenateBillGridModel?.Columns.Any(c => c.HeaderText == "Bill Number"), Is.True);
            Assert.That(result.LegislationSenateBillGridModel?.Columns.Any(c => c.HeaderText == "Bill Title"), Is.True);
            Assert.That(result.LegislationSenateBillGridModel?.Columns.Any(c => c.HeaderText == "Position"), Is.True);
        });
    }

    [Test]
    public async Task BuildPaymentToLobbyingAdvertisementModel_ReturnsValidModel_WhenIdsProvided()
    {
        // Arrange
        const long expectedFilerId = 123;
        const long expectedFilingId = 456;

        var advertisementDistributionMethods = new List<Generated.AdvertisementDistributionMethodRefResponse>
            {
                new("Direct Mail", 1, "Direct Mail")
            };

        var expectedTransaction = new LobbyingAdvertisementItemResponse(string.Empty, 1357, 1, string.Empty, 1, expectedFilerId, expectedFilingId, 1, string.Empty, _dateNow, _dateNow);

        _mockReferenceDataApi.Setup(x => x.GetAllAdvertisementDistributionMethods(It.IsAny<CancellationToken>())).ReturnsAsync(advertisementDistributionMethods);
        _mockLobbyingAdvertisementApi.Setup(x => x.GetLobbyingAdvertisementTransactionByFilingId(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(expectedTransaction);

        // Act
        var result = await _disclosureCtlSvc.BuildPaymentToLobbyingAdvertisementModel(
            expectedFilerId.ToString(CultureInfo.InvariantCulture),
            expectedFilingId.ToString(CultureInfo.InvariantCulture),
            CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FilerId, Is.EqualTo(expectedFilerId));
            Assert.That(result.Id, Is.EqualTo(expectedFilingId));
        });
    }

    [Test]
    public async Task BuildPaymentToLobbyingAdvertisementModel_ReturnsEmptyModel_WhenThrowException()
    {
        // Arrange
        const long expectedFilerId = 123;
        const long expectedFilingId = 456;

        var advertisementDistributionMethods = new List<Generated.AdvertisementDistributionMethodRefResponse>
            {
                new("Direct Mail", 1, "Direct Mail")
            };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Get,
            new HttpResponseMessage(HttpStatusCode.NotFound),
            new RefitSettings());

        _mockReferenceDataApi.Setup(x => x.GetAllAdvertisementDistributionMethods(It.IsAny<CancellationToken>())).ReturnsAsync(advertisementDistributionMethods);
        _mockLobbyingAdvertisementApi.Setup(x => x.GetLobbyingAdvertisementTransactionByFilingId(It.IsAny<long>(), It.IsAny<CancellationToken>())).ThrowsAsync(apiException);

        // Act
        var result = await _disclosureCtlSvc.BuildPaymentToLobbyingAdvertisementModel(
            expectedFilerId.ToString(CultureInfo.InvariantCulture),
            expectedFilingId.ToString(CultureInfo.InvariantCulture),
            CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task UpsertNonRegisteredLobbyist_WhenIdIsNull_ShouldCallCreate()
    {
        // Arrange
        var request = new DisclosureFilingNonRegisteredLobbyistRequest(
            filingSummaryId: 1,
            firstName: "John",
            id: null,
            lastName: "Doe"
        );

        var expectedResponse = new DisclosureFilingNonRegisteredLobbyistResponseDto(1, "FN", 1, "LN", "FN LN");

        _mockFilingSummaryApi
            .Setup(api => api.CreateNewNonRegisteredLobbyist(request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _disclosureCtlSvc.UpsertNonRegisteredLobbyist(request, CancellationToken.None);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResponse));
        _mockFilingSummaryApi.Verify(api => api.CreateNewNonRegisteredLobbyist(request, It.IsAny<CancellationToken>()), Times.Once);
        _mockFilingSummaryApi.Verify(api => api.UpdateNonRegisteredLobbyist(It.IsAny<long>(), It.IsAny<DisclosureFilingNonRegisteredLobbyistRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task UpsertNonRegisteredLobbyist_WhenIdIsPresent_ShouldCallUpdate()
    {
        // Arrange
        var request = new DisclosureFilingNonRegisteredLobbyistRequest(
            filingSummaryId: 1,
            firstName: "Jane",
            id: 42,
            lastName: "Smith"
        );

        var expectedResponse = new DisclosureFilingNonRegisteredLobbyistResponseDto(1, "FN", 1, "LN", "FN LN");

        _mockFilingSummaryApi
            .Setup(api => api.UpdateNonRegisteredLobbyist(request.Id!.Value, request, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _disclosureCtlSvc.UpsertNonRegisteredLobbyist(request, CancellationToken.None);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResponse));
        _mockFilingSummaryApi.Verify(api => api.UpdateNonRegisteredLobbyist(request.Id!.Value, request, It.IsAny<CancellationToken>()), Times.Once);
        _mockFilingSummaryApi.Verify(api => api.CreateNewNonRegisteredLobbyist(It.IsAny<DisclosureFilingNonRegisteredLobbyistRequest>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task DeleteRegisteredLobbyist_ShouldCallDeleteApi()
    {
        // Arrange
        long lobbyistId = 123;

        _mockFilingSummaryApi
            .Setup(api => api.DeleteNonRegisteredLobbyist(lobbyistId, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _disclosureCtlSvc.DeleteRegisteredLobbyist(lobbyistId, CancellationToken.None);

        // Assert
        _mockFilingSummaryApi.Verify(api => api.DeleteNonRegisteredLobbyist(lobbyistId, It.IsAny<CancellationToken>()), Times.Once);
    }



    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForLobbyistReportsSummary()
    {
        // Arrange
        var viewName = nameof(FilingSummaryTypeModel.LobbyistReportsSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        var mockResponse = new List<DisclosureFilingNonRegisteredLobbyistResponseDto> {
            new (1, "FN", 1, "LN", "FN LN"),
            new (1, "FN", 2, "LN", "FN LN"),
        };

        _mockFilingSummaryApi.Setup(x => x.GetNonRegisteredLobbyistsByFilingSummaryId(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse);

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.RegisteredLobbyistsGridModel, Is.Not.Null);
            Assert.That(result.NonRegisteredLobbyistsGridModel, Is.Not.Null);
        });
    }

    [Test]
    public void CreateEndOfSessionGridModel_ShouldReturnProperlyConfiguredGrid()
    {
        // Arrange
        var testData = new List<EndOfSessionLobbyingDto>
        {
            new(new List<ActionsLobbied>(), 100.50, default!, _dateNow, 1001, null, "Test Lobbying Firm 1", 1, null),
            new(new List<ActionsLobbied>(), 250.75, default!, _dateNow.AddDays(-30), 1002, null, "Test Lobbying Firm 2", 2, null)
        };

        // Act
        var result = _disclosureCtlSvc.CreateEndOfSessionGridModel(testData);

        // Assert
        Assert.That(result, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(result.GridId, Is.EqualTo("EndOfSessionLobbying"));
            Assert.That(result.GridType, Is.EqualTo(nameof(EndOfSessionLobbyingDto)));
            Assert.That(result.DataSource, Is.SameAs(testData));
            Assert.That(result.AllowPaging, Is.True);
            Assert.That(result.AllowTextWrap, Is.False);
            Assert.That(result.PageSize, Is.EqualTo(10));
            Assert.That(result.PageSizes, Has.Count.EqualTo(3));
            Assert.That(result.PageSizes, Contains.Item(10));
            Assert.That(result.PageSizes, Contains.Item(20));
            Assert.That(result.PageSizes, Contains.Item(50));
            Assert.That(result.AllowAdding, Is.False);
            Assert.That(result.AllowDeleting, Is.False);
            Assert.That(result.EnableExport, Is.False);
        });

        Assert.That(result.Columns, Has.Count.EqualTo(4));
        var columns = result.Columns.ToList();
        Assert.Multiple(() =>
        {
            Assert.That(columns[0].Field, Is.EqualTo("FilerId"));
            Assert.That(columns[1].Field, Is.EqualTo("FirmName"));
            Assert.That(columns[2].Field, Is.EqualTo("DateLobbyingFirmHired"));
            Assert.That(columns[2].IsUtcDate, Is.True);
            Assert.That(columns[3].Field, Is.EqualTo("Amount"));
            Assert.That(columns[3].IsCurrency, Is.True);
        });

        Assert.That(result.ActionItems, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionItems[0].Label, Is.EqualTo(CommonResourceConstants.Edit));
            Assert.That(result.ActionItems[0].Action, Is.EqualTo("editRow"));
            Assert.That(result.ActionItems[0].ControllerName, Is.EqualTo("Transaction"));
            Assert.That(result.ActionItems[0].ActionName, Is.EqualTo("EditEndOfSessionLobbyingContact"));

            Assert.That(result.ActionItems[1].Label, Is.EqualTo(CommonResourceConstants.Delete));
            Assert.That(result.ActionItems[1].Action, Is.EqualTo("deleteRow"));
            Assert.That(result.ActionItems[1].ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.ActionItems[1].ActionName, Is.EqualTo("Delete"));
        });
    }


    [Test]
    public async Task BuildSummaryViewModel_ReturnsCorrectViewModel_ForEndOfSessionLobbying()
    {
        // Arrange
        var viewName = nameof(FilingSummaryTypeModel.EndOfSessionLobbyingSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = "Lobbyist Report";

        var mockTransactions = new List<EndOfSessionLobbyingDto>
        {
            new(new List<ActionsLobbied>(), 100.50, default!, _dateNow.AddDays(-30), 1001, null, "Test Lobbying Firm 1", 1, null),
            new(new List<ActionsLobbied>(),250.75, default!, _dateNow.AddDays(-60), 1002, null, "Test Lobbying Firm 2", 2, null),
            new(new List<ActionsLobbied>(), 375.25, default!, _dateNow.AddDays(-90), 1003, null, "Test Lobbying Firm 3", 3, null)
        };

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        var mockTransactionsApi = new Mock<ITransactionsApi>();
        _ = mockTransactionsApi.Setup(x => x.GetAllEndOfSessionLobbyingTransactionsForFiling(
            It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockTransactions);

        var disclosureCtlSvc = new DisclosureCtlSvc(
            _mockFilingsApi.Object,
            _mockActivityExpenseApi.Object,
            _mockFilingSummaryApi.Object,
            _mockLobbyistApi.Object,
            _mockLobbyistEmployerApi.Object,
            _mockLobbyistEmployerCoalitionApi.Object,
            _mockLobbyingAdvertisementApi.Object,
            mockTransactionsApi.Object,
            _mockReferenceDataApi.Object,
            _mockLocalizer.Object);

        // Act
        var result = await disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.EndOfSessionGridModel, Is.Not.Null);
            Assert.That(result.Subtotal, Is.EqualTo(mockTransactions.Sum(t => t.Amount)));
            Assert.That(result.EndOfSessionGridModel?.GridId, Is.EqualTo("EndOfSessionLobbying"));
            Assert.That(result.EndOfSessionGridModel?.GridType, Is.EqualTo(nameof(EndOfSessionLobbyingDto)));
            Assert.That(result.EndOfSessionGridModel?.Columns?.Count, Is.EqualTo(4));
            Assert.That(result.EndOfSessionGridModel?.Columns.Any(c => c.Field == "FilerId"), Is.True);
            Assert.That(result.EndOfSessionGridModel?.Columns.Any(c => c.Field == "FirmName"), Is.True);
            Assert.That(result.EndOfSessionGridModel?.Columns.Any(c => c.Field == "DateLobbyingFirmHired"), Is.True);
            Assert.That(result.EndOfSessionGridModel?.Columns.Any(c => c.Field == "Amount"), Is.True);
        });

        mockTransactionsApi.Verify(
            api => api.GetAllEndOfSessionLobbyingTransactionsForFiling(
                long.Parse(filingId, CultureInfo.InvariantCulture),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Test]
    public async Task BuildSummaryViewModel_ForCampaignContribution_SetsContributionsInExistingStatementsAndRelatedFilers()
    {
        // Arrange
        var viewName = nameof(FilingSummaryTypeModel.CampaignContributionSummary);
        var filerId = "123";
        var filingId = "456";
        var filingStatus = "1";
        var filingSummaryId = "789";
        var reportType = FilingTypeModel.LobbyistEmployerReport.Name;

        var mockReport = new LobbyistEmployerReportResponse(
            id: 456,
            filerId: 123,
            startDate: _dateNow,
            endDate: _dateNow.AddMonths(3),
            diligenceStatementVerified: true,
            filerName: "Test Employer",
            parentId: null,
            status: 1,
            submittedDate: null,
            version: 1,
            totalPaymentsToInHouseLobbyists: 5000.0,
            totalOverheadExpense: 1000.0,
            totalUnderThresholdPayments: 500.0,
            isMemberOfLobbyingCoalition: true,
            contributionsInExistingStatements: true
        );

        var mockContributions = new List<LobbyistEmployerCampaignContributionItemResponse> {
        new(10, 19, 1, 1, "note", It.IsAny<FilerItemResponse>(), 1, "Tom", "Individual", null)
    };

        var mockRelatedFilers = new List<FilerSearchDto> {
        new(id: 101, name: "Related Filer 1", filerId: 201, filerType: "Committee", filerTypeId: 1, displayText: "Related Filer 1 (ID: 201)"),
        new(id: 102, name: "Related Filer 2", filerId: 202, filerType: "Committee", filerTypeId: 1, displayText: "Related Filer 2 (ID: 202)"),
    };

        _mockFilingsApi.Setup(x => x.GetLobbyistEmployerReportFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockReport);

        _mockLobbyistEmployerCoalitionApi.Setup(x => x.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContributions);

        _mockFilingsApi.Setup(x => x.GetRelatedFilersByFilingId(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockRelatedFilers);

        var mockHttpContext = new Mock<HttpContext>();
        var mockTempData = new Mock<ITempDataDictionary>();

        // Act
        var result = await _disclosureCtlSvc.BuildSummaryViewModel(
            viewName, filerId, filingId, filingSummaryId, filingStatus, reportType,
            mockHttpContext.Object, mockTempData.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.CampaignContributionsGridModel, Is.Not.Null);
            Assert.That(result.ContributionsInExistingStatements, Is.EqualTo("True"));
            Assert.That(result.RelatedFilers, Is.Not.Null);
            Assert.That(result.RelatedFilers, Has.Count.EqualTo(2));
            Assert.That(result.RelatedFilers![0].Id, Is.EqualTo(101));
            Assert.That(result.RelatedFilers![0].Name, Is.EqualTo("Related Filer 1"));
            Assert.That(result.RelatedFilers![1].Id, Is.EqualTo(102));
            Assert.That(result.RelatedFilers![1].Name, Is.EqualTo("Related Filer 2"));
            Assert.That(result.Subtotal, Is.EqualTo(mockContributions.Sum(c => c.Amount)));
        });

        _mockFilingsApi.Verify(api => api.GetLobbyistEmployerReportFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockLobbyistEmployerCoalitionApi.Verify(api => api.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(
            long.Parse(filingId, CultureInfo.InvariantCulture),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockFilingsApi.Verify(api => api.GetRelatedFilersByFilingId(
            long.Parse(filingId, CultureInfo.InvariantCulture),
            It.IsAny<CancellationToken>()), Times.Once);
    }


    [Test]
    public async Task HandleLobbyingAdvertisementTransactionSubmit_Create_ShouldAddModelError_WhenResponseIsInvalid()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var requestDto = new LobbyingAdvertisementRequestDto(string.Empty, -1, string.Empty, null, null, null, null, null); // Invalid value
        var errorResponse = new TransactionResponseDto(1, false, new List<WorkFlowError>
        {
            new("101", "error", "Amount", "Amount is invalid")
        });

        _mockLobbyingAdvertisementApi.Setup(api => api.CreateLobbyingAdvertisementTransaction(It.IsAny<long>(), It.IsAny<LobbyingAdvertisementRequestDto>(), It.IsAny<CancellationToken>()))
                                        .ReturnsAsync(errorResponse);

        // Act
        var result = await _disclosureCtlSvc.HandleLobbyingAdvertisementTransactionSubmit(1, requestDto, modelState, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.IsValid, Is.False);
            Assert.That(modelState.ContainsKey("Amount"), Is.True);
            Assert.That(modelState["Amount"]!.Errors[0].ErrorMessage, Is.EqualTo("Amount is invalid"));
        });
        _mockLobbyingAdvertisementApi.Verify(api => api.CreateLobbyingAdvertisementTransaction(It.IsAny<long>(), It.IsAny<LobbyingAdvertisementRequestDto>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task HandleLobbyingAdvertisementTransactionSubmit_Edit_ShouldAddModelError_WhenResponseIsInvalid()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var requestDto = new LobbyingAdvertisementRequestDto(string.Empty, -1, string.Empty, null, null, null, null, 1); // Invalid value
        var errorResponse = new TransactionResponseDto(1, false, new List<WorkFlowError>
        {
            new("101", "error", "Amount", "Amount is invalid")
        });

        _mockLobbyingAdvertisementApi.Setup(api => api.EditLobbyingAdvertisementTransaction(It.IsAny<long>(), It.IsAny<LobbyingAdvertisementRequestDto>(), It.IsAny<CancellationToken>()))
                                        .ReturnsAsync(errorResponse);

        // Act
        var result = await _disclosureCtlSvc.HandleLobbyingAdvertisementTransactionSubmit(1, requestDto, modelState, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState.IsValid, Is.False);
            Assert.That(modelState.ContainsKey("Amount"), Is.True);
            Assert.That(modelState["Amount"]!.Errors[0].ErrorMessage, Is.EqualTo("Amount is invalid"));
        });
        _mockLobbyingAdvertisementApi.Verify(api => api.EditLobbyingAdvertisementTransaction(It.IsAny<long>(), It.IsAny<LobbyingAdvertisementRequestDto>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task GetAdvertisementDistributionMethods_ShouldReturnValue()
    {
        var mockResponse = new List<AdvertisementDistributionMethodRefResponse>()
        {
            new ("Test method", 1, "Test")
        };
        _mockReferenceDataApi.Setup(api => api.GetAllAdvertisementDistributionMethods(It.IsAny<CancellationToken>())).ReturnsAsync(mockResponse);

        // Act
        var result = await _disclosureCtlSvc.GetAdvertisementDistributionMethods();

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Has.Count.AtLeast(1));
        });
    }

    [Test]
    public async Task HandleUpdateAmendmentExplanation_WhenResponseIsInvalid_AppliesErrorsToModelState()
    {
        // Arrange
        var filingId = 123L;
        var amendmentExplanation = "Invalid explanation";
        var model = new FilingViewModel { AmendmentExplanation = amendmentExplanation };
        var modelState = new ModelStateDictionary();
        var cancellationToken = CancellationToken.None;

        var fieldKey = "FD_LOB_LobbyistEmployer5000Firm_AmendReport02_AmendmentExplanation.AmendmentExplanation";
        var expectedModelStateKey = "AmendmentExplanation";
        var baseErrorMessage = "Invalid input for {{Field Name}}.";
        var localizedFieldName = "Amendment Explanation"; // Simulate what the localizer returns for the label key
        var expectedErrorMessage = baseErrorMessage.Replace("{{Field Name}}", localizedFieldName, StringComparison.OrdinalIgnoreCase);

        var validationErrors = new List<WorkFlowError>
        {
            new("test", "test", fieldKey, baseErrorMessage)
        };

        var response = new UpdateAmendmentExplanationResponse(1, false, validationErrors);

        _mockFilingsApi.Setup(api =>
            api.UpdateAmendmentExplanation(filingId,
                It.Is<UpdateAmendmentExplanationRequest>(req => req.AmendmentExplanation == amendmentExplanation),
                cancellationToken))
            .ReturnsAsync(response);

        _mockLocalizer.Setup(l => l[ResourceConstants.AmendmentExplanationTitle])
            .Returns(new LocalizedString(ResourceConstants.AmendmentExplanationTitle, localizedFieldName));

        // Act
        var result = await _disclosureCtlSvc.HandleUpdateAmendmentExplanation(
            filingId, model, modelState, cancellationToken);

        // Assert
        Assert.That(result, Is.EqualTo(response));
        Assert.Multiple(() =>
        {
            Assert.That(modelState.IsValid, Is.False);
            Assert.That(modelState.ContainsKey(expectedModelStateKey), Is.True);
            Assert.That(modelState[expectedModelStateKey]?.Errors.Count, Is.EqualTo(1));
            Assert.That(modelState[expectedModelStateKey]?.Errors[0].ErrorMessage, Is.EqualTo(expectedErrorMessage));
        });

        _mockFilingsApi.Verify(api =>
            api.UpdateAmendmentExplanation(filingId,
                It.IsAny<UpdateAmendmentExplanationRequest>(),
                cancellationToken), Times.Once);
    }

}
