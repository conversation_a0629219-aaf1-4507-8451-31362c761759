using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Elections;
using SOS.CalAccess.Data.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Elections;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]

public class BallotMeasureSvcTests
{
    private IBallotMeasureRepository _ballotMeasureRepositoryMock;
    private IDecisionsSvc _decisionsServiceMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private BallotMeasureSvc _service;

    [SetUp]
    public void SetUp()
    {
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _ballotMeasureRepositoryMock = Substitute.For<IBallotMeasureRepository>();
        _decisionsServiceMock = Substitute.For<IDecisionsSvc>();

        _service = new BallotMeasureSvc(_ballotMeasureRepositoryMock);
    }


    [Test]
    public async Task SearchBallotMeasuresAsync_Title_NoErrors()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        BallotMeasure measure = GenerateSampleBallotMeasure(1, "Test Measure 1");
        BallotMeasureRepository ballotMeasureRepository = new(context);
        _ = await ballotMeasureRepository.Create(measure);

        _service = new BallotMeasureSvc(ballotMeasureRepository);

        // Act
        var result = await _service.SearchBallotMeasuresAsync("test");

        // Assert
        Assert.That(result, Is.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task FindBallotMeasuresByIdOrName_Id_NoErrors()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        BallotMeasure measure = GenerateSampleBallotMeasure(100, "Test Measure 1");
        BallotMeasureRepository ballotMeasureRepository = new(context);
        _ = await ballotMeasureRepository.Create(measure);

        _service = new BallotMeasureSvc(ballotMeasureRepository);

        // Act
        var result = await _service.SearchBallotMeasuresAsync("100");

        // Assert
        Assert.That(result, Is.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task FindBallotMeasuresByIdOrName_EmptyQuery_Exception()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        BallotMeasure measure = GenerateSampleBallotMeasure(100, "Test Measure 1");
        BallotMeasureRepository ballotMeasureRepository = new(context);
        _ = await ballotMeasureRepository.Create(measure);

        _service = new BallotMeasureSvc(ballotMeasureRepository);

        // Act
        var result = await _service.SearchBallotMeasuresAsync("");

        // Assert
        Assert.That(result, Is.Empty);
    }

    #region NotImplementedYetMethods
    [Test]
    public async Task GetAllBallotMeasures_NotImplementedException()
    {
        {
            // Arrange

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _service.GetAllBallotMeasures());

        }
    }

    [Test]
    public async Task CreateBallotMeasure_NotImplementedException()
    {
        {
            // Arrange
            BallotMeasure measure = GenerateSampleBallotMeasure(100, "Test Measure 1");

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _service.CreateBallotMeasure(measure));

        }
    }

    [Test]
    public async Task UpdateBallotMeasure_NotImplementedException()
    {
        {
            // Arrange
            BallotMeasure measure = GenerateSampleBallotMeasure(100, "Test Measure 1");

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _service.UpdateBallotMeasure(measure));
        }
    }

    [Test]
    public async Task DeleteBallotMeasure_NotImplementedException()
    {
        {
            // Arrange

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _service.DeleteBallotMeasure(1));
        }
    }

    [Test]
    public async Task GetAllProponents_NotImplementedException()
    {
        {
            // Arrange

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _service.GetAllProponents());
        }
    }

    [Test]
    public async Task CreateProponents_NotImplementedException()
    {
        {
            // Arrange
            var proponent = new Proponent
            {
                Id = 1,
                LastName = "Test",
                CreatedBy = 1,
                ModifiedBy = 1,
                CreatedByReference = new User
                {
                    Id = 9999,
                    EmailAddress = "test@test",
                    FirstName = "Test",
                    LastName = "Test",
                    EntraOid = "TestOid"
                },
                ModifiedByReference = new User { Id = 9999, EmailAddress = "test@test", FirstName = "Test", LastName = "Test", EntraOid = "TestOid" }
            };

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _service.CreateProponent(proponent));
        }
    }
    #endregion

    #region private
    private static BallotMeasure GenerateSampleBallotMeasure(long id, string name)
    {
        BallotMeasure measure = new()
        {
            Id = id,
            Code = "N/A",
            Name = name,
            Description = "",
            Jurisdiction = "Statewide",
            AttorneyGeneralId = "",
            AttorneyGeneralTitle = "Test Measure 1",
            CreatedBy = 999,
            ModifiedBy = 999,
            MeasureType = "PROPOSITION",
            EffectiveDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        };
        return measure;
    }
    #endregion

}
