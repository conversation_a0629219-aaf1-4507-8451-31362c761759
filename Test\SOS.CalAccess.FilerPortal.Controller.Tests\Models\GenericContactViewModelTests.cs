using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
[TestOf(nameof(GenericContactViewModel))]
internal sealed class GenericContactViewModelTests : IDisposable
{
    #region Setup

    private GenericContactViewModel _viewModel;

    // Setup method to initialize test data
    [SetUp]
    public void SetUp()
    {
        var phoneNumber = new PhoneNumberDto("+1", null, "987", 1, false, "1011231234", 1, false, "Phone");
        var faxNumber = new PhoneNumberDto("+1", null, "678", 1, false, "2029871234", 1, false, "Fax");
        var address = new AddressDtoModel("dummy city", "dummy country", "home", "dummy state", "123 dummy street", "APT 500", "work", "96183");

        _viewModel = new GenericContactViewModel()
        {
            PhoneNumber = phoneNumber.Number,
            PhoneNumberCountryCode = phoneNumber.CountryCode,
            FaxNumber = faxNumber.Number,
            FaxNumberCountryCode = faxNumber.CountryCode,
            EmailAddress = "<EMAIL>",
            Country = address.Country,
            Street = address.Street,
            Street2 = address.Street2,
            City = address.City,
            State = address.State,
            ZipCode = address.Zip,
        };
    }

    // Teardown method to clean up after tests
    [TearDown]
    public void TearDown()
    {
        _viewModel = new GenericContactViewModel();
    }
    public void Dispose()
    {
        // Clean up any resources if necessary
    }

    #endregion

    #region Constructors and Initializing

#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

    [Test]
    public void GenericContactViewModel_ShouldInitializeCorrectly()
    {
        // Arrange
        var mockResponse = new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
                , 1
                , 1
                , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , "12345");

        // Act
        var viewModel = new GenericContactViewModel(mockResponse);

        // Assert:

        // Address
        Assert.Multiple(() =>
        {
            Assert.That(mockResponse.Id, Is.EqualTo(viewModel.Id));
            Assert.That(mockResponse.FilerId, Is.EqualTo(viewModel.FilerId));
            Assert.That(mockResponse.AddressId, Is.EqualTo(viewModel.AddressId));
            Assert.That(mockResponse.City, Is.EqualTo(viewModel.City));
            Assert.That(mockResponse.State, Is.EqualTo(viewModel.State));
            Assert.That(mockResponse.Country, Is.EqualTo(viewModel.Country));
            Assert.That(mockResponse.Street, Is.EqualTo(viewModel.Street));
            Assert.That(mockResponse.Street2, Is.EqualTo(viewModel.Street2));
            Assert.That(mockResponse.ZipCode, Is.EqualTo(viewModel.ZipCode));
            Assert.That(mockResponse.Website, Is.EqualTo(viewModel.Website));
        });

        Assert.Multiple(() =>
        {
            // Phone numbers
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Phone")?.Number, Is.EqualTo(viewModel.PhoneNumber));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Phone")?.CountryCode, Is.EqualTo(viewModel.PhoneNumberCountryCode));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Fax")?.Number, Is.EqualTo(viewModel.FaxNumber));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Fax")?.CountryCode, Is.EqualTo(viewModel.FaxNumberCountryCode));

            // Email
            Assert.That(mockResponse.EmailAddresses[0]?.Email, Is.EqualTo(viewModel.EmailAddress));
        });
    }

    [Test]
    public void GenericContactViewModel_ShouldInitializeCorrectly_WithZipCodeEmpty()
    {
        // Arrange
        var mockResponse = new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
                , 1
                , 1
                , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , string.Empty);

        // Act
        var viewModel = new GenericContactViewModel(mockResponse);

        // Assert:

        // Address
        Assert.Multiple(() =>
        {
            Assert.That(mockResponse.Id, Is.EqualTo(viewModel.Id));
            Assert.That(mockResponse.FilerId, Is.EqualTo(viewModel.FilerId));
            Assert.That(mockResponse.AddressId, Is.EqualTo(viewModel.AddressId));
            Assert.That(mockResponse.City, Is.EqualTo(viewModel.City));
            Assert.That(mockResponse.State, Is.EqualTo(viewModel.State));
            Assert.That(mockResponse.Country, Is.EqualTo(viewModel.Country));
            Assert.That(mockResponse.Street, Is.EqualTo(viewModel.Street));
            Assert.That(mockResponse.Street2, Is.EqualTo(viewModel.Street2));
            Assert.That(mockResponse.ZipCode, Is.EqualTo(viewModel.ZipCode));
            Assert.That(mockResponse.Website, Is.EqualTo(viewModel.Website));
        });

        Assert.Multiple(() =>
        {
            // Phone numbers
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Phone")?.Number, Is.EqualTo(viewModel.PhoneNumber));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Phone")?.CountryCode, Is.EqualTo(viewModel.PhoneNumberCountryCode));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Fax")?.Number, Is.EqualTo(viewModel.FaxNumber));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Fax")?.CountryCode, Is.EqualTo(viewModel.FaxNumberCountryCode));

            // Email
            Assert.That(mockResponse.EmailAddresses[0]?.Email, Is.EqualTo(viewModel.EmailAddress));
        });
    }

    [Test]
    public void GenericContactViewModel_ShouldInitializeCorrectly_WithPhoneNumberFaxNumberEmpty()
    {
        // Arrange
        var mockResponse = new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
                , 1
                , 1
                , new List<PhoneNumber>()
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , "21345");

        // Act
        var viewModel = new GenericContactViewModel(mockResponse);

        // Assert:

        // Address
        Assert.Multiple(() =>
        {
            Assert.That(mockResponse.Id, Is.EqualTo(viewModel.Id));
            Assert.That(mockResponse.FilerId, Is.EqualTo(viewModel.FilerId));
            Assert.That(mockResponse.AddressId, Is.EqualTo(viewModel.AddressId));
            Assert.That(mockResponse.City, Is.EqualTo(viewModel.City));
            Assert.That(mockResponse.State, Is.EqualTo(viewModel.State));
            Assert.That(mockResponse.Country, Is.EqualTo(viewModel.Country));
            Assert.That(mockResponse.Street, Is.EqualTo(viewModel.Street));
            Assert.That(mockResponse.Street2, Is.EqualTo(viewModel.Street2));
            Assert.That(mockResponse.ZipCode, Is.EqualTo(viewModel.ZipCode));
            Assert.That(mockResponse.Website, Is.EqualTo(viewModel.Website));
        });

        Assert.Multiple(() =>
        {
            // Phone numbers
            Assert.That(mockResponse.PhoneNumbers.FirstOrDefault(x => x.Type == "Phone")?.Number, Is.EqualTo(null));
            Assert.That(mockResponse.PhoneNumbers.FirstOrDefault(x => x.Type == "Phone")?.CountryCode, Is.EqualTo(null));
            Assert.That(mockResponse.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax")?.Number, Is.EqualTo(null));
            Assert.That(mockResponse.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax")?.CountryCode, Is.EqualTo(null));

            // Email
            Assert.That(mockResponse.EmailAddresses[0]?.Email, Is.EqualTo(viewModel.EmailAddress));
        });
    }

    [Test]
    public void GenericContactViewModel_ShouldInitializeCorrectly_WithEmailAddressEmpty()
    {
        // Arrange
        var mockResponse = new ContactItemResponse(1
                , "Test City"
                , "United States"
                , new List<EmailAddress>()
                , 1
                , 1
                , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
                , "HI"
                , "123 Sample St."
                , "APT A"
                , 2
                , true
                , new List<WorkFlowError>()
                , "https://example.com"
                , "12345");

        // Act
        var viewModel = new GenericContactViewModel(mockResponse);

        // Assert:

        // Address
        Assert.Multiple(() =>
        {
            Assert.That(mockResponse.Id, Is.EqualTo(viewModel.Id));
            Assert.That(mockResponse.FilerId, Is.EqualTo(viewModel.FilerId));
            Assert.That(mockResponse.AddressId, Is.EqualTo(viewModel.AddressId));
            Assert.That(mockResponse.City, Is.EqualTo(viewModel.City));
            Assert.That(mockResponse.State, Is.EqualTo(viewModel.State));
            Assert.That(mockResponse.Country, Is.EqualTo(viewModel.Country));
            Assert.That(mockResponse.Street, Is.EqualTo(viewModel.Street));
            Assert.That(mockResponse.Street2, Is.EqualTo(viewModel.Street2));
            Assert.That(mockResponse.ZipCode, Is.EqualTo(viewModel.ZipCode));
            Assert.That(mockResponse.Website, Is.EqualTo(viewModel.Website));
        });

        Assert.Multiple(() =>
        {
            // Phone numbers
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Phone")?.Number, Is.EqualTo(viewModel.PhoneNumber));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Phone")?.CountryCode, Is.EqualTo(viewModel.PhoneNumberCountryCode));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Fax")?.Number, Is.EqualTo(viewModel.FaxNumber));
            Assert.That(mockResponse.PhoneNumbers.First(x => x.Type == "Fax")?.CountryCode, Is.EqualTo(viewModel.FaxNumberCountryCode));

            // Email
            Assert.That(mockResponse.EmailAddresses.ElementAtOrDefault(0)?.Email, Is.EqualTo(null));
        });
    }

    [Test]
    public void Constructor_WithIndividualContactResponse_SetsBaseProperties()
    {
        // Arrange
        var context = "sample context";
        var returnUrl = "/return";
        var firstName = "FirstName";
        var middleName = "MiddleName";
        var lastName = "LastName";
        var id = 123;
        var typeId = 333;
        var addressId = 456;
        var emailList = new List<EmailAddress>();
        var phoneNumberList = new List<PhoneNumber>();
        var errorList = new List<WorkFlowError>();
        var filingId = 222;
        var reportType = "sampleReport";
        var transactionId = 1;

        var contact = new IndividualContactResponse(
                addressId,
                "TestCity",
                "USA",
                emailList,
                "Employer",
                id,
                firstName,
                123,
                lastName,
                middleName,
                "occupation",
                phoneNumberList,
                "HI",
                "Street",
                "Street2",
                typeId,
                true,
                errorList,
                "www.test.com",
                "12345");

        // Act
        var viewModel = new GenericContactViewModel(contact, context, returnUrl, filingId, reportType, transactionId);

        Assert.That(viewModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(viewModel.Context, Is.EqualTo(context));
            Assert.That(viewModel.Id, Is.EqualTo(id));
            Assert.That(viewModel.AddressId, Is.EqualTo(addressId));
            Assert.That(viewModel.TypeId, Is.EqualTo(InteropConstants.FilerContactType.Individual));
            Assert.That(viewModel.FirstName, Is.EqualTo(firstName));
            Assert.That(viewModel.MiddleName, Is.EqualTo(middleName));
            Assert.That(viewModel.LastName, Is.EqualTo(lastName));
            Assert.That(viewModel.ReportType, Is.EqualTo(reportType));
        });
    }

    [Test]
    public void Constructor_WithOrganizationContactResponse_SetsBaseProperties()
    {
        // Arrange
        var context = "sample context";
        var returnUrl = "/return";
        var organizationName = "Organization Name";
        var id = 123;
        var typeId = 333;
        var addressId = 456;
        var emailList = new List<EmailAddress>();
        var phoneNumberList = new List<PhoneNumber>();
        var errorList = new List<WorkFlowError>();
        var filingId = 222;
        var reportType = "sampleReport";

        var contact = new OrganizationContactResponse(
                addressId,
                "TestCity",
                "USA",
                emailList,
                1,
                id,
                organizationName,
                phoneNumberList,
                "HI",
                "Street",
                "Street2",
                typeId,
                true,
                errorList,
                "www.test.com",
                "12345");

        // Act
        var viewModel = new GenericContactViewModel(contact, context, returnUrl, filingId, reportType);

        Assert.That(viewModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(viewModel.Context, Is.EqualTo(context));
            Assert.That(viewModel.Id, Is.EqualTo(id));
            Assert.That(viewModel.AddressId, Is.EqualTo(addressId));
            Assert.That(viewModel.TypeId, Is.EqualTo(InteropConstants.FilerContactType.Organization));
            Assert.That(viewModel.OrganizationName, Is.EqualTo(organizationName));
            Assert.That(viewModel.ReportType, Is.EqualTo(reportType));
        });
    }

    #endregion

    #region Getters and Setters

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_Id()
    {
        // Arrange
        var expected = 1;

        // Act
        _viewModel.Id = expected;

        // Assert
        Assert.That(_viewModel.Id, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_FilerId()
    {
        // Arrange
        var expected = 1;

        // Act
        _viewModel.FilerId = expected;

        // Assert
        Assert.That(_viewModel.FilerId, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_Street()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.Street = expected;

        // Assert
        Assert.That(_viewModel.Street, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_Street2()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.Street2 = expected;

        // Assert
        Assert.That(_viewModel.Street2, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_City()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.City = expected;

        // Assert
        Assert.That(_viewModel.City, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_State()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.State = expected;

        // Assert
        Assert.That(_viewModel.State, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_Country()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.Country = expected;

        // Assert
        Assert.That(_viewModel.Country, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_ZipCode()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.ZipCode = expected;

        // Assert
        Assert.That(_viewModel.ZipCode, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_EmailAddress()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.EmailAddress = expected;

        // Assert
        Assert.That(_viewModel.EmailAddress, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_PhoneNumber()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.PhoneNumber = expected;

        // Assert
        Assert.That(_viewModel.PhoneNumber, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_PhoneNumberCountryCode()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.PhoneNumberCountryCode = expected;

        // Assert
        Assert.That(_viewModel.PhoneNumberCountryCode, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_FaxNumber()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.FaxNumber = expected;

        // Assert
        Assert.That(_viewModel.FaxNumber, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_FaxNumberCountryCode()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.FaxNumberCountryCode = expected;

        // Assert
        Assert.That(_viewModel.FaxNumberCountryCode, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_Website()
    {
        // Arrange
        var expected = "Dummy Value";

        // Act
        _viewModel.Website = expected;

        // Assert
        Assert.That(_viewModel.Website, Is.EqualTo(expected));
    }

    [Test]
    public void GenericContactViewModel_ShouldSetAndGet_TypeId()
    {
        // Arrange
        var expected = 1;

        // Act
        _viewModel.TypeId = expected;

        // Assert
        Assert.That(_viewModel.TypeId, Is.EqualTo(expected));
    }

    #endregion

#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
}
