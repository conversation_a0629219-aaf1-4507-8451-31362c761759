using Microsoft.EntityFrameworkCore;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.Efile;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Efile;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Efile;

[TestFixture]
public class ApiRequestRepositoryTest
{
    private DatabaseContext _dbContext;
    private ApiRequestRepository _apiRequestrepository;
    private IDateTimeSvc _dateTimeSvc;

    [SetUp]
    public void SetUp()
    {
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase(databaseName: "CARS")
            .Options;

        _dbContext = new DatabaseContext(options);
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _apiRequestrepository = new ApiRequestRepository(_dbContext, _dateTimeSvc);

        SeedData();
    }

    private void SeedData()
    {
        _dbContext.ApiRequestStatus.AddRange(
            new ApiRequestStatus { Id = 1, Status = "Received" },
            new ApiRequestStatus { Id = 2, Status = "Queued" }
        );

        _dbContext.ApiRequest.AddRange(
            new ApiRequest
            {
                Id = 1,
                ApiRequestStatusId = 1,
                BlobFilename = "BlobFilename",
                Path = "/api/example",
                UserId = "123",
                FilerId = "11",
                FilingId = "121",
                ReceivedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                FileSize = 55,
                TransactionCount = 0
            },
            new ApiRequest
            {
                Id = 2,
                ApiRequestStatusId = 1,
                BlobFilename = "BlobFilename",
                Path = "/api/example2",
                UserId = "456",
                FilerId = "13",
                FilingId = "180",
                ReceivedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                FileSize = 1313,
                TransactionCount = 1
            }
        );

        _dbContext.SaveChanges();
    }

    [TearDown]
    public void TearDown()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }

    /// <summary>
    /// Tests that a valid <see cref="ApiRequest"/> object is successfully persisted to the db.
    /// </summary>
    /// <remarks>
    /// This test verifies that when a properly structured <see cref="ApiRequest"/> is passed 
    /// to the repository's <c>Create</c> method, it is stored correctly, and its properties 
    /// match the expected values.
    /// </remarks>
    [Test]
    public async Task PersistApiRequests_ValidInput()
    {
        // Arrange
        const string expectedBlobFilename = "BlobFilename";
        var newApiRequest = new ApiRequest
        {
            Path = "path",
            UserId = "1",
            FilerId = "1",
            BlobFilename = expectedBlobFilename,
            ApiRequestStatusId = 1,
            ReceivedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            FileSize = 1,
            TransactionCount = 1
        };

        // Act 
        var apiRequest = await _apiRequestrepository.Create(newApiRequest);

        // Assert
        Assert.That(apiRequest.BlobFilename, Is.EqualTo(expectedBlobFilename));
    }

    [Test]
    public async Task UpdateStatusOfApiRequests_ValidInput_ShouldUpdateStatusAndAddErrors()
    {
        // Arrange
        int apiRequestId = 1;
        int apirequeststatusid = 2;
        var errors = new List<ApiError>
        {
            new() { ApiErrorCode = "ERR001", ApiErrorDescription = "Invalid schema" ,ApiErrorField="Filer Name"},
            new() { ApiErrorCode = "ERR002", ApiErrorDescription = "Missing required field" ,ApiErrorField="Country"}
        };

        await _apiRequestrepository.UpdateStatusOfApiRequests(apiRequestId, apirequeststatusid, errors);

        var updatedRequest = await _dbContext.ApiRequest.FindAsync(apiRequestId);

        Assert.That(updatedRequest?.ApiRequestStatusId, Is.EqualTo(2));

        var errorEntries = _dbContext.ApiError.Where(e => e.ApiRequestId == apiRequestId).ToList();

        Assert.That(errorEntries?.Count, Is.EqualTo(2));
    }

    [Test]
    public async Task UpdateFilingIdOfApiRequests_ValidInput_ShouldUpdateFilingId()
    {
        // Arrange
        int apiRequestId = 1;
        string filingId = "210";

        // Act
        await _apiRequestrepository.UpdateFilingIdOfApiRequests(apiRequestId, filingId);
        var updatedRequest = await _dbContext.ApiRequest.FindAsync(apiRequestId);

        // Asert
        Assert.That(updatedRequest?.FilingId, Is.EqualTo("210"));
    }
}
