// <copyright file="ICreateFilerContact.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Globalization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;

/// <summary>
/// Command handler interface for creating a contact for a filer.
/// </summary>
public interface ICreateFilerContact : ICommand<CreateFilerContactCommand, IResult<FilerContact>>;

/// <summary>
/// Command implementation of the <see cref="ICreateContact"/> interface.
/// </summary>
/// <param name="db">The database context.</param>
/// <param name="decisions">Business logic engine.</param>
/// <param name="auditService">High level audit facilities.</param>
/// <param name="logger">The logger instance.</param>
public sealed class CreateFilerContact(
    DatabaseContext db,
    IDecisionsService decisions,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc,
    ILogger<CreateFilerContact> logger) : ICreateFilerContact
{
    /// <inheritdoc />
    public async ValueTask<IResult<FilerContact>> Execute(
        CreateFilerContactCommand input,
        CancellationToken cancellationToken = default)
    {
        var filerRecord = await db.Filers.FirstOrDefaultAsync(f => f.Id == input.FilerId, cancellationToken);

        if (filerRecord is null)
        {
            logger.LogWarning("Filer was not found.");
            return new Failure<FilerContact>.NotFound("No filer was found with the requested id");
        }

        var filerContact = input.Build();

        _ = await decisions.Execute(new("Contact.Create", Context: filerContact), cancellationToken);

        _ = db.FilerContacts.Add(filerContact);

        _ = await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Create", filerContact.GetType().Name, filerContact.Id.ToString(CultureInfo.InvariantCulture), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<FilerContact>(filerContact);
    }
}
