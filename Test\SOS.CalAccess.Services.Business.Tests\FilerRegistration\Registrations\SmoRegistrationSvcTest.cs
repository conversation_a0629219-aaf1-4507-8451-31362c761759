using System.Reflection;
using NSubstitute;
using NSubstitute.ReceivedExtensions;
using NSubstitute.ReturnsExtensions;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using SOS.CalAccess.Tests.Common;
using SOS.CalAccess.Tests.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;

[TestFixture]
public class SmoRegistrationSvcTests
{
    private IRegistrationRepository _registrationRepositoryMock;
    private IFilingPeriodRepository _filingPeriodRepositoryMock;
    private IFilingRepository _filingRepositoryMock;
    private IFilingSummaryRepository _filingSummaryRepositoryMock;
    private IFilerSvc _filerSvcMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IAttestationRepository _attestationRepositoryMock;
    private IDecisionsSvc _decisionsSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IRegistrationRegistrationContactRepository _registrationRegistrationContactRepositoryMock;
    private ITransactionRepository _transactionRepositoryMock;
    private IReferenceDataSvc _referenceDataSvcMock;
    private IRegistrationModelMapper _modelMapperMock;
    private ILinkageSvc _linkageSvcMock;
    private IFilerUserRepository _filerUserRepositoryMock;
    private ILinkageRequestRepository _linkageRequestRepositoryMock;
    private IFilingContactSummaryRepository _filingContactSummaryRepositoryMock;
    private IDateTimeSvc _dateTimeSvcMock;
    private DateTime _dateNow;

    private SmoRegistrationSvc _service;

    private FilingSharedServicesDependencies _servicesDependencies;
    private FilingSharedRepositoriesDependencies _repositoriesDependencies;


    [SetUp]
    public void Setup()
    {
        _filingRepositoryMock = Substitute.For<IFilingRepository>();
        _filingSummaryRepositoryMock = Substitute.For<IFilingSummaryRepository>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _filingPeriodRepositoryMock = Substitute.For<IFilingPeriodRepository>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _attestationRepositoryMock = Substitute.For<IAttestationRepository>();
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _registrationRegistrationContactRepositoryMock = Substitute.For<IRegistrationRegistrationContactRepository>();
        _transactionRepositoryMock = Substitute.For<ITransactionRepository>();
        _referenceDataSvcMock = Substitute.For<IReferenceDataSvc>();
        _modelMapperMock = Substitute.For<IRegistrationModelMapper>();
        _linkageSvcMock = Substitute.For<ILinkageSvc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _filerUserRepositoryMock = Substitute.For<IFilerUserRepository>();
        _linkageRequestRepositoryMock = Substitute.For<ILinkageRequestRepository>();
        _filingContactSummaryRepositoryMock = Substitute.For<IFilingContactSummaryRepository>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _servicesDependencies = new FilingSharedServicesDependencies(
            _filerSvcMock,
            _decisionsSvcMock,
            _authorizationSvcMock,
            _userMaintenanceSvcMock,
            _notificationSvcMock,
            _referenceDataSvcMock,
            _linkageSvcMock,
            _dateTimeSvcMock
        );

        _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
            _filingRepositoryMock,
            _filingSummaryRepositoryMock,
            _filingPeriodRepositoryMock,
            _registrationRepositoryMock,
            _attestationRepositoryMock,
            _registrationRegistrationContactRepositoryMock,
            _transactionRepositoryMock,
            _filerUserRepositoryMock,
            _linkageRequestRepositoryMock,
            _filingContactSummaryRepositoryMock
        );

        _service = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
    }

    [Test]
    public async Task CreateSmoRegistration_ValidRequest_ReturnsSuccessfulResponse()
    {
        // Arrange
        var request = new SmoContactRequest { CheckRequiredFieldsFlag = true, Name = "Test Org" };
        var mappedOrg = new SlateMailerOrganization { Name = "Test Org", Email = "<EMAIL>", StatusId = 1 };
        var updatedMappedOrg = new SlateMailerOrganization { Name = "Test Org", Email = "<EMAIL>", StatusId = 1, FilerId = 1 };

        // Simulated successful registration response (without mocking Registration)
        var expectedResponse = new RegistrationResponseDto(123, true, new List<WorkFlowError>(), 1);

        _modelMapperMock.MapSmoContactRequestToModel(request).Returns(mappedOrg);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsSmoContact, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSmoContact>(),
                true)
            .Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors
        _authorizationSvcMock
            .GetInitiatingUserId()
            .Returns(Task.FromResult<long?>(1));
        _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));

        _registrationRepositoryMock.IsUniqueSmoName(mappedOrg.Name).Returns(Task.FromResult(true)); // Simulate valid name

        // Simulate repository returning a successful response
        _registrationRepositoryMock.Create(mappedOrg)
            .Returns(Task.FromResult<Registration>(mappedOrg));
        _registrationRepositoryMock.Update(updatedMappedOrg)
            .Returns(Task.FromResult<Registration>(updatedMappedOrg));

        // Act
        var result = await _service.CreateSmoRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilerId, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task CreateSmoRegistration_InvalidRequest_ReturnsValidationErrors()
    {
        // Arrange
        var request = new SmoContactRequest { CheckRequiredFieldsFlag = true };
        var mappedOrg = new SlateMailerOrganization { Name = "Test Org", Email = "<EMAIL>", StatusId = 1 };
        var errors = new List<WorkFlowError> { new("Email", "Err001", "Validation", "Invalid email format") };

        _modelMapperMock.MapSmoContactRequestToModel(request).Returns(mappedOrg);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsSmoContact, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSmoContact>(),
                true)
            .Returns(Task.FromResult(errors)); // Workflow returns validation errors

        // Act
        var result = await _service.CreateSmoRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Valid, Is.False);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.Null);
            Assert.That(result.ValidationErrors, Is.EqualTo(errors));
        });
    }

    [Test]
    public async Task CreateSmoRegistration_ValidRequest_RepositoryReturnsNull()
    {
        // Arrange
        var request = new SmoContactRequest { CheckRequiredFieldsFlag = true, Name = "Test Org" };
        var mappedOrg = new SlateMailerOrganization { Name = "Test Org", Email = "<EMAIL>", StatusId = 1 };

        _modelMapperMock.MapSmoContactRequestToModel(request).Returns(mappedOrg);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsSmoContact, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSmoContact>(),
                true)
            .Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors
        _authorizationSvcMock
            .GetInitiatingUserId()
            .Returns(Task.FromResult<long?>(1));
        _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));

        _registrationRepositoryMock.IsUniqueSmoName(mappedOrg.Name).Returns(Task.FromResult(true)); // Simulate valid name
        _registrationRepositoryMock.Create(mappedOrg).Returns(Task.FromResult<Registration>(default!)); // Simulate repository failure

        // Act
        var result = await _service.CreateSmoRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.Null);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public void Constructor_ValidSlateMailerOrganization_MapsFieldsCorrectly()
    {
        // Arrange
        var slateMailerOrganization = new SlateMailerOrganization
        {
            Id = 1,
            StatusId = 2,
            FilerId = 1001,
            Name = "Test Org",
            Email = "<EMAIL>",
            County = "Los Angeles",
            ActivityLevel = "High",
            QualifiedCommittee = true,
            DateQualified = new DateTime(2024, 01, 01, 0, 0, 0, 0),
            CampaignCommittee = false,
            IsSameAsCandidateAddress = true,
            CandidateAgency = "Elections Office",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                    {
                        new() { Street = "123 Main St", City = "Los Angeles", State = "CA", Zip = "90001", Country = "USA", Type = "Business", Purpose = "Organization" }
                    }
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
                    {
                        new() { Number = "5551234", CountryCode = "+1", Type = "Mobile" }
                    }
            }
        };

        // Act
        var dto = new SmoRegistrationResponseDto(slateMailerOrganization);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(dto.Id, Is.EqualTo(1));
            Assert.That(dto.FilerId, Is.EqualTo(1001));
            Assert.That(dto.Name, Is.EqualTo("Test Org"));
            Assert.That(dto.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(dto.County, Is.EqualTo("Los Angeles"));
            Assert.That(dto.ActivityLevel, Is.EqualTo("High"));
            Assert.That(dto.QualifiedCommittee, Is.True);
            Assert.That(dto.DateQualified, Is.EqualTo(new DateTime(2024, 01, 01, 0, 0, 0, 0)));
            Assert.That(dto.CampaignCommittee, Is.False);
            Assert.That(dto.IsSameAsOrganizationAddress, Is.True);
            Assert.That(dto.CandidateAgency, Is.EqualTo("Elections Office"));

            Assert.That(dto.Address, Has.Count.EqualTo(1));
            Assert.That(dto.Address.First().Street, Is.EqualTo("123 Main St"));
            Assert.That(dto.Address.First().City, Is.EqualTo("Los Angeles"));

            Assert.That(dto.PhoneNumbers, Has.Count.EqualTo(1));
            Assert.That(dto.PhoneNumbers.First().Number, Is.EqualTo("5551234"));
            Assert.That(dto.PhoneNumbers.First().CountryCode, Is.EqualTo("+1"));
        });
    }

    [Test]
    public void CancelSmoRegistration_ShouldThrowExceptionWhenRecordNotFound()
    {
        // Arrange
        long id = 1;
        _ = _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult(default(SlateMailerOrganization)));

        // Act
        // Assert
        _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(KeyNotFoundException)), async () => await _service.CancelSmoRegistration(id));
    }

    [Test]
    public void CancelSmoRegistration_ShouldThrowExceptionWhenRecordStatusIsNotDraft()
    {
        // Arrange
        long id = 1;
        var record = new SlateMailerOrganization
        {
            Name = "Name",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Submitted.Id
        };
        _ = _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(record);

        // Act
        // Assert
        _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(InvalidOperationException)), async () => await _service.CancelSmoRegistration(id));
    }

    [Test]
    public void CancelSmoRegistration_ShouldSucceedWhenStatusIsDraft()
    {
        // Arrange
        long id = 1;
        var record = new SlateMailerOrganization
        {
            Id = 1,
            Name = "Name",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Draft.Id
        };
        _ = _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(record);
        _filerUserRepositoryMock.FindFilerUsersLinkedToRegistrationContacts(Arg.Is<long>(1)).Returns(new List<FilerUser>());

        // Act
        // Assert
        Assert.DoesNotThrowAsync(async () => await _service.CancelSmoRegistration(id));
        _registrationRepositoryMock.Received()
            .Update(Arg.Any<SlateMailerOrganization>());
    }

    [Test]
    public void CancelSmoRegistration_ShouldSucceedWhenStatusIsDraft_IsAmendment()
    {
        // Arrange
        long id = 2;
        long parentId = 1;
        var currentReg = new SlateMailerOrganization
        {
            Id = id,
            ParentId = parentId,
            Name = "Name",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Draft.Id
        };
        var currentRegFilerUsers = new List<FilerUser>
        {
            new()
            {
                Id = 1000
            },
            new()
            {
                Id = 1001
            }
        };
        var parentRegFilerUsers = new List<FilerUser>
        {
            new()
            {
                Id = 1000
            }
        };
        _ = _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Is(id)).Returns(currentReg);
        _filerUserRepositoryMock.FindFilerUsersLinkedToRegistrationContacts(Arg.Is(id)).Returns(currentRegFilerUsers);
        _filerUserRepositoryMock.FindFilerUsersLinkedToRegistrationContacts(Arg.Is(parentId)).Returns(parentRegFilerUsers);

        // Act
        // Assert
        Assert.DoesNotThrowAsync(async () => await _service.CancelSmoRegistration(id));
        _registrationRepositoryMock.Received()
            .Update(Arg.Any<SlateMailerOrganization>());
        _filerUserRepositoryMock.Received(1).DeleteFilerUsers(Arg.Is<List<FilerUser>>(x => x.Count == 1 && x[0].Id == 1001));
    }

    [Test]
    public async Task CreateSmoRegistration_InvalidRequest_ThrowInvalidDataException()
    {
        // Arrange
        var request = new SmoContactRequest { CheckRequiredFieldsFlag = true };
        var mappedOrg = new SlateMailerOrganization { Name = "Test Org", Email = "<EMAIL>", StatusId = 1 };

        _modelMapperMock.MapSmoContactRequestToModel(request).Returns(mappedOrg);
        _ = _decisionsSvcMock
            .InitiateWorkflow<DecisionsSmoContact, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSmoContact>(),
                true)
            .Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors

        _ = _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>()).Returns(Task.FromResult(false)); // Simulate invalid name

        // Act
        var result = await _service.CreateSmoRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Valid, Is.False);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.Null);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
    }

    [TestCaseSource(nameof(UpdateSmoRegistrationTestCases))]
    public async Task UpdateSmoRegistration_ValidRequest_ReturnResult(SlateMailerOrganization smoRegistration, long? committeeId)
    {
        // Arrange
        var registrationId = 1;
        var request = new SlateMailerOrganizationRequest
        {
            CommitteeId = committeeId,
            CheckRequiredFieldsFlag = true,
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.Update(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(smoRegistration));
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsSmoRegistrationDetails, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSmoRegistrationDetails>(),
                true)
            .Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors
        _authorizationSvcMock
            .GetInitiatingUserId()
            .Returns(Task.FromResult<long?>(1));
        _filerSvcMock.AddFilerLinkAsync(Arg.Any<FilerLink>()).Returns(Task.FromResult);
        _filerSvcMock.UpdateFilerLinkAsync(Arg.Any<FilerLink>()).Returns(Task.FromResult);

        // Act
        var result = await _service.UpdateSmoRegistration(registrationId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
        });
    }

    [Test]
    public void UpdateFilerRoleAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;
        var request = new UpdateFilerUserRoleRequest
        {
            IsOfficer = true,
            IsTreasurer = false,
            Title = "",
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.UpdatePrimaryFilerUserRoleAsync(registrationId, request));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={registrationId}"));
    }

    [TestCaseSource(nameof(UpdateFilerRoleAsyncTestCases))]
    public async Task UpdateFilerRoleAsync_ValidRequest_ReturnResult(bool isTreasurer, bool isOfficer, string title)
    {
        // Arrange
        var filerUser = new FilerUserDto
        {
            Id = 1,
            FilerId = 1,
            FilerRoleId = 1,
            UserId = 1,
        };
        var request = new UpdateFilerUserRoleRequest
        {
            IsOfficer = isOfficer,
            IsTreasurer = isTreasurer,
            Title = title,
        };
        var smoRegistration = GenerateSampleSmo(1, "Test");

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<FilerUserDto?>(filerUser));
        _filerSvcMock.UpdateFilerUserRoleAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult);

        // Act
        await _service.UpdatePrimaryFilerUserRoleAsync(smoRegistration.Id, request);

        // Assert
        await _filerSvcMock.Received(1).UpdateFilerUserRoleAsync(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task GetSmoOfficers_ShouldReturnData()
    {
        // Arrange
        var phoneNumberList = new PhoneNumberList
        {
            Id = 1,
            PhoneNumbers = new List<PhoneNumber>
            {
                new()
                {
                    Id = 1,
                    Number = "1234567890",
                    Type = "Home",
                    CountryCode = "+1",
                    InternationalNumber = false,
                }
            }
        };

        var registration = GenerateSampleSmo(222, "Test");
        var original = GenerateSampleSmo(111, "Test");
        registration.OriginalId = 111;
        original.StatusId = RegistrationStatus.Accepted.Id;
        original.SubmittedAt = DateTime.Today;

        registration.StatusId = RegistrationStatus.Accepted.Id;
        registration.SubmittedAt = DateTime.Today;

        var registrationContacts = new List<RegistrationRegistrationContact>()
        {
            new ()
            {
                Id = 1,
                RegistrationId = 123,
                Registration = registration,
                RegistrationContactId = 1,
                RegistrationContact = new RegistrationContact
                {
                    Id = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    MiddleName = "A",
                    Email = "<EMAIL>",
                    PhoneNumberListId = 1,
                },
                CapitalContributionOver10K = true,
                TenPercentOrGreater = true,
                CanAuthorizeSlateMailerContents = true,
                Role = FilerRole.SlateMailerOrg_Officer.Name,
                Title = "President",
                CumulativeCapitalContributions = 10000,
                PercentOfOwnership = 10,
                CreatedBy = 1,
                ModifiedBy = 1,
            }
        };

        _ = _registrationRepositoryMock.FindSlateMailerOrganizationById(222).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _ = _registrationRepositoryMock.FindSlateMailerOrganizationById(111).Returns(Task.FromResult<SlateMailerOrganization?>(original));
        _ = _registrationRepositoryMock.FindById(Arg.Any<long>()).Returns(registration);
        _ = _registrationRepositoryMock.GetPhoneNumberListById(Arg.Any<long>()).Returns(phoneNumberList);
        _ = _registrationRegistrationContactRepositoryMock.GetRegistrationContacts(Arg.Any<long>()).Returns(registrationContacts);

        // Act
        var result = await _service.GetSmoOfficers(222);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count, Is.AtLeast(1));
    }

    [Test]
    public void GetSmoOfficers_ShouldThrowError()
    {
        // Arrange

        // Act & Assert
        _ = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetSmoOfficers(1));
    }

    [Test]
    public void CompleteTreasurerAcknowledgementAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.CompleteTreasurerAcknowledgementAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={id}"));
    }

    [Test]
    public void CompleteTreasurerAcknowledgementAsync_NotFoundFilerUser_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.CompleteTreasurerAcknowledgementAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"No filer user found with registration. Id={id}"));
    }

    [Test]
    public void CompleteTreasurerAcknowledgementAsync_NotFoundRegistrationContact_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);

        // Act & Asserts
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.CompleteTreasurerAcknowledgementAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"No contact matched with current user. Id={filerUser!.UserId}"));
    }

    [Test]
    public async Task CompleteTreasurerAcknowledgementAsync_SufficientPermission_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));

        // Act
        await _service.CompleteTreasurerAcknowledgementAsync(id);

        // Asserts
        _ = _registrationRegistrationContactRepositoryMock.Received(1).Update(Arg.Any<RegistrationRegistrationContact>());
    }

    [Test]
    public async Task CompleteTreasurerAcknowledgementAsync_SufficientPermission_AlreadyAttested_ShouldCompleteRegistration()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id != 2);
        var attestation = GenerateAttestation();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));
        _attestationRepositoryMock.FindByRegistrationId(Arg.Any<long>()).Returns(Task.FromResult<Attestation?>(attestation));

        // Act
        await _service.CompleteTreasurerAcknowledgementAsync(id);

        // Asserts
        _ = _registrationRegistrationContactRepositoryMock.Received(1).Update(Arg.Any<RegistrationRegistrationContact>());
        _ = _registrationRepositoryMock.Received(1).Update(Arg.Any<Registration>());
    }

    [Test]
    public void GetTreasurerAcknowledgementContactsAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetTreasurerAcknowledgementContactsAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={id}"));
    }

    [Test]
    public async Task GetTreasurerAcknowledgementContactsAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));

        // Act
        var result = await _service.GetTreasurerAcknowledgementContactsAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(smoRegistration.FilerId));
            Assert.That(result.Contacts, Is.Not.Null);
            Assert.That(result.Contacts, Is.InstanceOf<IEnumerable<SmoRegistrationContactDto>>());
        });
    }

    [Test]
    public void SendAcknowledgementNotificationsAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var permissionId = Permission.Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment.Id;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();
        var users = GenerateUsers();
        var linkageRequests = new List<SendLinkageRequestDto>()
        {
            new()
            {
                RecipientEmail = "<EMAIL>",
                RecipientName = "John Smith",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 3,
            },
            // simulate object missing RecipientEmail
            new()
            {
                RecipientName = "John Smith",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 4,
            },
            // simulate object missing RecipientName
            new()
            {
                RecipientEmail = "<EMAIL>",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 5,
            }
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);
        _notificationSvcMock.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);

        // Act & Asserts
        Assert.DoesNotThrowAsync(async () => await _service.SendAcknowledgementNotificationsAsync(id));
        _linkageSvcMock.Received(1).SendLinkageRequestToPerson(Arg.Any<SendLinkageRequestToPersonDto>());
    }

    [Test]
    public async Task GetSmoOfficer_ShouldReturnData()
    {
        // Arrange
        var phoneNumberList = new PhoneNumberList
        {
            Id = 1,
            PhoneNumbers = new List<PhoneNumber>
            {
                new()
                {
                    Id = 1,
                    Number = "1234567890",
                    Type = "Home",
                    CountryCode = "+1",
                    InternationalNumber = false,
                }
            }
        };

        var addressList = new AddressList
        {
            Id = 1,
            Addresses = new List<Address>
            {
                new()
                {
                    Id = 1,
                    Street = "123 Main St",
                    City = "Los Angeles",
                    State = "CA",
                    Zip = "90001",
                    Country = "USA",
                    Type = "Business",
                    Purpose = "Organization"
                }
            }
        };

        var registration = new DummyRegistration
        {
            Id = 123,
            Name = "Test Org",
            StatusId = RegistrationStatus.Draft.Id,
        };

        var registrationContacts = new RegistrationRegistrationContact
        {
            Id = 1,
            RegistrationId = 123,
            Registration = registration,
            RegistrationContactId = 1,
            RegistrationContact = new RegistrationContact
            {
                Id = 1,
                FirstName = "John",
                LastName = "Doe",
                MiddleName = "A",
                Email = "<EMAIL>",
                PhoneNumberListId = 1,
            },
            CapitalContributionOver10K = true,
            TenPercentOrGreater = true,
            CanAuthorizeSlateMailerContents = true,
            Role = FilerRole.SlateMailerOrg_Officer.Name,
            Title = "President",
            CumulativeCapitalContributions = 10000,
            PercentOfOwnership = 10,
            CreatedBy = 1,
            ModifiedBy = 1,
        };

        _ = _registrationRepositoryMock.FindById(Arg.Any<long>()).Returns(registration);
        _ = _registrationRepositoryMock.GetPhoneNumberListById(Arg.Any<long>()).Returns(phoneNumberList);
        _ = _registrationRepositoryMock.GetAddressListById(Arg.Any<long>()).Returns(addressList);
        _ = _registrationRegistrationContactRepositoryMock.GetRegistrationContact(Arg.Any<long>(), Arg.Any<long>()).Returns(registrationContacts);

        // Act
        var result = await _service.GetSmoOfficer(123, 1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoRegistrationContactDto>());
    }

    [Test]
    public void GetSmoOfficer_ShouldThrowError()
    {
        // Arrange
        _registrationRegistrationContactRepositoryMock.GetRegistrationContact(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetSmoOfficer(1, 1));
        Assert.That(ex.Message, Is.EqualTo($"Officer not Found."));
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ValidId_ShouldReturnRegistrationId()
    {
        // Arrange
        const long contactId = 1L;
        var registrationId = 1L;
        var contact = new RegistrationRegistrationContact()
        {
            Id = 1,
            Role = FilerRole.SlateMailerOrg_Treasurer.Name,
            Title = "",
            PercentOfOwnership = 1m,
            CapitalContributionOver10K = false,
            CumulativeCapitalContributions = 0m,
            TenPercentOrGreater = false,
            CreatedBy = 0,
            ModifiedBy = 0,
            HasAcknowledged = false,
            RegistrationContact = new RegistrationContact
            {
                Id = 1,
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Test",
                MiddleName = "",
            }
        }; // Ensure correct registrationId is used

        _registrationRepositoryMock.GetSmoRegistrationRegistrationContactById(Arg.Any<long>(), Arg.Any<long>()).Returns(contact);
        _registrationRegistrationContactRepositoryMock.Update(Arg.Any<RegistrationRegistrationContact>())
            .Returns(Task.FromResult(contact));

        // Act
        await _service.DeleteIndividualAuthorizer(registrationId, contactId);

        // Assert
        _ = _registrationRegistrationContactRepositoryMock.Received(1).Update(Arg.Any<RegistrationRegistrationContact>());
    }

    [Test]
    public void DeleteIndividualAuthorizer_InvalidId_ShouldThrowKeyNotFoundException()
    {
        // Arrange
        var contactId = 1L;
        var registrationId = 1L;
        _registrationRepositoryMock.GetSmoRegistrationRegistrationContactById(registrationId, contactId).ReturnsNull();

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.DeleteIndividualAuthorizer(registrationId, contactId));
        Assert.That(ex.Message, Is.EqualTo($"Registration Contact not Found Id={contactId}"));
    }

    #region AttestRegistrationAsync

    [Test]
    public void AttestRegistrationAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.AttestRegistrationAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={id}"));
    }

    [Test]
    public void AttestRegistrationAsync_NotFoundFilerUser_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.AttestRegistrationAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"No filer user found with registration. Id={id}"));
    }

    [Test]
    public void AttestRegistrationAsync_AlreadyAttested_ShouldSkip()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        FilerUserDto? filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);

        // Act & Asserts
        Assert.DoesNotThrowAsync(async () => await _service.AttestRegistrationAsync(id));
    }

    [Test]
    public async Task AttestRegistrationAsync_DecisionError1_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 1);

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(smoRegistration);

        List<WorkFlowError> workFlowError = new()
        {
            new("IsUserTreasurer", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
        };
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(workFlowError);

        // Act
        RegistrationResponseDto response = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Valid, Is.False);
            Assert.That(response.ValidationErrors, Is.Not.Empty);
            Assert.That(response.ValidationErrors, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task AttestRegistrationAsync_DecisionError2_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 1);

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));

        List<WorkFlowError> workFlowError = new() { };
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(DecisionsWorkflow.SmoPreAttestationRuleSet, Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(workFlowError);
        DecisionsSmoSubmissionResponse responseError = new()
        {
            Status = "Draft",
            Notifications = new()
            {
                new(false, null, null)
            },
            Result = new()
            {
                new("IsUserTreasurer", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            }
        };
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(DecisionsWorkflow.SmoPostSubmissionRuleSet, Arg.Any<DecisionsSmoSubmission>(), Arg.Any<bool>()).Returns(responseError);

        // Act
        RegistrationResponseDto response = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Valid, Is.False);
            Assert.That(response.ValidationErrors, Is.Not.Empty);
            Assert.That(response.ValidationErrors, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task AttestRegistrationAsync_AdditionalContacts_HasUncompletedAcknowledgement_PendingStatus()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 1);
        //Override person2 and 3
        var person2 = GenerateRegistrationContacts().First(x => x.Id == 2);
        var person3 = GenerateRegistrationContacts().First(x => x.Id == 3);
        person2.Role = FilerRole.SlateMailerOrg_Officer.Name;
        person2.HasAcknowledged = false;
        person2.AcknowledgedOn = null;
        person3.Role = FilerRole.SlateMailerOrg_AccountManager.Name;
        person3.HasAcknowledged = false;
        person3.AcknowledgedOn = null;
        person3.CanAuthorizeSlateMailerContents = true;
        List<RegistrationRegistrationContact> list = [.. registrationContacts];
        list.Add(person2);
        list.Add(person3);

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(list.AsEnumerable()));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));

        var permissionId = Permission.Registration_SlateMailerOrganization_Attest.Id;
        var linkageRequests = new List<SendLinkageRequestDto>();
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);

        List<WorkFlowError> workFlowError = new() { };
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(DecisionsWorkflow.SmoPreAttestationRuleSet, Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(workFlowError);
        DecisionsSmoSubmissionResponse responseError = new()
        {
            Status = "Accepted",
            Notifications = new()
            {
                new(true, 17, null)
            },
            Result = new() { }
        };
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(DecisionsWorkflow.SmoPostSubmissionRuleSet, Arg.Any<DecisionsSmoSubmission>(), Arg.Any<bool>()).Returns(responseError);

        // Act
        RegistrationResponseDto response = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Valid, Is.True);
            Assert.That(response.ValidationErrors, Is.Empty);
            Assert.That(response.Id, Is.EqualTo(id));
            Assert.That(response.StatusId, Is.EqualTo(RegistrationStatus.Pending.Id));
        });
    }

    [Test]
    public void AttestRegistrationAsync_NotMatchedRegistrationContact_ShouldThrowError()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 3);

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));

        // Act & Asserts
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.AttestRegistrationAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"No contact matched with current user. Id={filerUser!.UserId}"));
    }

    [Test]
    public async Task AttestRegistrationAsync_NotUniqueSmoName_ShouldReturnResponseWithInvalidStatus()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 1);
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Status = RegistrationStatus.Accepted.Name,
            Notifications = new()
            {
                new(true, 17, null)
            },
            Result = new()
        };
        var validationErrors = new List<WorkFlowError>
        {
            new("Name", "ErrGlobal0002", "Validation", $"Organization names must be unique. There is an active organization with this name.")
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>()).Returns(Task.FromResult(false));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(new List<WorkFlowError>());
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);

        // Act
        var result = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(false));
            Assert.That(result.ValidationErrors, Is.EqualTo(validationErrors));
        });
    }

    [Test]
    public async Task AttestRegistrationAsync_SufficientPermission_ShouldExecuteSuccessfully_PendingStatus()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 1);
        var attestation = GenerateAttestation();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Status = RegistrationStatus.Accepted.Name,
            Notifications = new()
            {
                new(true, 17, null)
            },
            Result = new()
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(new List<WorkFlowError>());
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);
        _attestationRepositoryMock.Create(Arg.Any<Attestation>()).Returns(Task.FromResult(attestation));

        var permissionId = Permission.Registration_SlateMailerOrganization_Attest.Id;
        var linkageRequests = new List<SendLinkageRequestDto>();
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);

        // Act
        var result = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(true));
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Pending.Id));
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task AttestRegistrationAsync_SufficientPermission_ShouldExecuteSuccessfully_ActiveStatus()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id && x.Id == 4)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 4);
        var attestation = GenerateAttestation();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Status = RegistrationStatus.Accepted.Name,
            Notifications = new()
            {
                new(true, 17, null)
            },
            Result = new()
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(new List<WorkFlowError>());
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);
        _attestationRepositoryMock.Create(Arg.Any<Attestation>()).Returns(Task.FromResult(attestation));

        var permissionId = Permission.Registration_SlateMailerOrganization_Attest.Id;
        var linkageRequests = new List<SendLinkageRequestDto>();
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);

        // Act
        var result = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(true));
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Accepted.Id));
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task AttestRegistrationAsync_SufficientPermission_ShouldExecuteSuccessfully_AcceptedStatus_WithNoNotifications()
    {
        // Arrange
        var id = 3;
        var smoRegistration = GenerateSampleSmo(3, "Test1", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id && x.Id == 4)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 4);
        var attestation = GenerateAttestation();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));
        _attestationRepositoryMock.Create(Arg.Any<Attestation>()).Returns(Task.FromResult(attestation));

        List<WorkFlowError> workFlowError = new() { };

        var decisionsSmoSubmissionResponse = new DecisionsSmoSubmissionResponse { Notifications = new List<NotificationTrigger> { new(false, null, null) }, Result = workFlowError, Status = "Accepted" };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(workFlowError);
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), checkRequiredFields: true).Returns(decisionsSmoSubmissionResponse);

        var permissionId = Permission.Registration_SlateMailerOrganization_Attest.Id;
        var linkageRequests = new List<SendLinkageRequestDto>();
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);

        // Act
        var result = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(true));
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Accepted.Id));
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        await _notificationSvcMock.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }

    [Test]
    public async Task AttestRegistrationAsync_SufficientPermission_ShouldExecuteSuccessfully_AcceptedStatus_WithNotifications()
    {
        // Arrange
        var id = 3;
        var smoRegistration = GenerateSampleSmo(3, "Test1", RegistrationStatus.Draft.Id);
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id && x.Id == 4)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 4);
        var attestation = GenerateAttestation();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));
        _attestationRepositoryMock.Create(Arg.Any<Attestation>()).Returns(Task.FromResult(attestation));
        _filerUserRepositoryMock.TerminateRegistrationAmendmentContactLinkages(Arg.Any<long>()).Returns(GenerateFilerUser());
        await _linkageRequestRepositoryMock.CancelContactLinkageRequestsForRegistration(Arg.Any<long>());

        List<WorkFlowError> workFlowError = new() { };

        var decisionsSmoSubmissionResponse = new DecisionsSmoSubmissionResponse { Notifications = new List<NotificationTrigger> { new(true, 17, null) }, Result = workFlowError, Status = "Accepted" };
        var decisionResponse = new DecisionResponse
        {
            Notifications = new List<NotificationTrigger>
                {
                  new(true, 59, null, NotificationRecipient.InitiatingUser),
                  new(true, 60, null, NotificationRecipient.FilerUsers)
                }
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(workFlowError);
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), checkRequiredFields: true).Returns(decisionsSmoSubmissionResponse);
        _decisionsSvcMock.InitiateWorkflow<string, DecisionResponse>(Arg.Any<DecisionsWorkflow>(), string.Empty).Returns(decisionResponse);

        var permissionId = Permission.Registration_SlateMailerOrganization_Attest.Id;
        var linkageRequests = new List<SendLinkageRequestDto>();
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);

        // Act
        var result = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(true));
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Accepted.Id));
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        await _notificationSvcMock.Received(5).SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
        await _notificationSvcMock.Received(4).SendUserNotification(Arg.Any<SendUserNotificationRequest>());
    }

    [Test]
    public async Task AttestRegistrationAsync_Termination_SufficientPermission_ShouldExecuteSuccessfully_TerminatedStatus()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        smoRegistration.TerminatedAt = DateTime.UtcNow;
        smoRegistration.Parent = new SlateMailerOrganization
        {
            Id = 0,
            Email = "test",
            Name = "SMO",
            StatusId = RegistrationStatus.Accepted.Id,
            SubmittedAt = DateTime.UtcNow,
        };
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id && x.Id == 4)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts().Where(x => x.Id == 4);
        var attestation = GenerateAttestation();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Status = RegistrationStatus.Terminated.Name,
            Notifications = new()
            {
                new(true, 17, null)
            },
            Result = new(),
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult(registrationContacts));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(filerUser));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoAttestation>(), Arg.Any<bool>()).Returns(new List<WorkFlowError>());
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTerminationSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoTerminationSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);
        _attestationRepositoryMock.Create(Arg.Any<Attestation>()).Returns(Task.FromResult(attestation));

        var permissionId = Permission.Registration_SlateMailerOrganization_Attest.Id;
        var linkageRequests = new List<SendLinkageRequestDto>();
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);

        // Act
        var result = await _service.AttestRegistrationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Valid, Is.EqualTo(true));
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Terminated.Id));
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }


    #endregion

    [Test]
    public void GetResponsibleOfficerContactsAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetResponsibleOfficerContactsAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={id}"));
    }

    [Test]
    public async Task GetResponsibleOfficerContactsAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));

        // Act
        var result = await _service.GetResponsibleOfficerContactsAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<IEnumerable<SmoRegistrationContactDto>>());
        });
    }

    [Test]
    public void GetRegistrationAttestationAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var id = 1;

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetRegistrationAttestationAsync(id));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={id}"));
    }

    [Test]
    public async Task GetRegistrationAttestationAsync_NotFoundAttestation_ShouldReturnEmptyResult()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _ = _attestationRepositoryMock.FindByRegistrationId(Arg.Any<long>());

        // Act
        var result = await _service.GetRegistrationAttestationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationAttestationResponseDto>());
            Assert.That(result.FirstName, Is.Null);
            Assert.That(result.LastName, Is.Null);
            Assert.That(result.Title, Is.Null);
            Assert.That(result.ExecutedAt, Is.Null);
        });
    }

    [Test]
    public async Task GetRegistrationAttestationAsync_FoundAttestation_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var attestation = GenerateAttestation();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _attestationRepositoryMock.FindByRegistrationId(Arg.Any<long>()).Returns(Task.FromResult<Attestation?>(attestation));

        // Act
        var result = await _service.GetRegistrationAttestationAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationAttestationResponseDto>());
            Assert.That(result.FirstName, Is.Not.Null);
            Assert.That(result.LastName, Is.Not.Null);
            Assert.That(result.Title, Is.Not.Null);
            Assert.That(result.ExecutedAt, Is.Not.Null);
        });
    }

    [Test]
    public void SendRegistrationForAttestationAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long> { 1, 2 }
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.SendRegistrationForAttestationAsync(registrationId, request));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={registrationId}"));
    }

    [Test]
    public async Task SendRegistrationForAttestationAsync_ValidNotificationRequest_NoError()
    {
        // Arrange
        var registrationId = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long> { 1, 2 }
        };
        var permissionId = Permission.Registration_SlateMailerOrganization_Attest.Id;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var registrationContacts = GenerateRegistrationContacts();
        var users = GenerateUsers();
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_AccountManager.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var linkageRequests = new List<SendLinkageRequestDto>()
        {
            new()
            {
                RecipientEmail = "<EMAIL>",
                RecipientName = "John Smith",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 3,
            },
            // simulate object missing RecipientEmail
            new()
            {
                RecipientName = "John Smith",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 4,
            },
            // simulate object missing RecipientName
            new()
            {
                RecipientEmail = "<EMAIL>",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 5,
            }
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(registrationId), Arg.Is(permissionId)).Returns(linkageRequests);
        _notificationSvcMock.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);
        DecisionsSmoSubmissionResponse decisionsResponse = new()
        {
            Status = "Pending",
            Notifications = new()
            {
                new(true, 17, null)
            },
            Result = new() { }
        };
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(DecisionsWorkflow.SmoSendForAttestationRuleSet, Arg.Any<DecisionsSmoSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);

        // Act
        RegistrationResponseDto response = await _service.SendRegistrationForAttestationAsync(registrationId, request);

        // Asserts
        await _linkageSvcMock.Received(1).SendLinkageRequestToPerson(Arg.Any<SendLinkageRequestToPersonDto>());
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Valid, Is.True);
            Assert.That(response.ValidationErrors, Is.Empty);
            Assert.That(response, Is.Not.Null);
            Assert.That(response, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(response.StatusId, Is.EqualTo(RegistrationStatus.Pending.Id));
        });
    }

    [Test]
    public async Task SendRegistrationForAttestationAsync_DecisionError1_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long> { 1, 2 }
        };
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);
        var registrationContacts = GenerateRegistrationContacts();
        var users = GenerateUsers();
        var filerUser = GenerateFilerUser()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_AccountManager.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
        _notificationSvcMock.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);

        DecisionsSmoSubmissionResponse responseError = new()
        {
            Status = "Draft",
            Notifications = new()
            {
                new(false, null, null)
            },
            Result = new()
            {
                new("IsUserTreasurer", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            }
        };
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(DecisionsWorkflow.SmoSendForAttestationRuleSet, Arg.Any<DecisionsSmoSubmission>(), Arg.Any<bool>()).Returns(responseError);

        // Act
        RegistrationResponseDto response = await _service.SendRegistrationForAttestationAsync(registrationId, request);

        // Asserts
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Valid, Is.False);
            Assert.That(response.ValidationErrors, Is.Not.Empty);
            Assert.That(response.ValidationErrors, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task GetPendingItemsAsync_ValidRequest_ShouldReturnResult()
    {
        // Act
        var registrationId = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _attestationRepositoryMock.FindByRegistrationId(Arg.Any<long>()).ReturnsNull();

        // Arrange
        var result = await _service.GetPendingItemsAsync(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<PendingItemDto>>());
        });

    }

    [Test]
    public void CreateSmoAmendmentRegistrationAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.CreateSmoAmendmentRegistrationAsync(registrationId));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={registrationId}"));
    }

    [Test]
    public async Task CreateSmoAmendmentRegistrationAsync_FoundNotAcceptedStatus_ReturnResult()
    {
        // Arrange
        var registrationId = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test", RegistrationStatus.Draft.Id);

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));

        // Act
        var result = await _service.CreateSmoAmendmentRegistrationAsync(registrationId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationResponseDto>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
        });
    }

    [Test]
    public async Task CreateSmoAmendmentRegistrationAsync_FoundAmendmentExisting_ReturnResult()
    {
        // Arrange
        var registrationId = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var smoAmendmentRegistration = GenerateSampleSmo(2, "Test", RegistrationStatus.Draft.Id);

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindExistingSmoAmendmentRegistration(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));

        // Act
        var result = await _service.CreateSmoAmendmentRegistrationAsync(registrationId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationResponseDto>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
        });
    }

    [TestCaseSource(nameof(CreateSmoAmendmentRegistrationAsyncTestCases))]
    public async Task CreateSmoAmendmentRegistrationAsync_ValidRequest_ShouldCreateAmendmentSuccessfully(SlateMailerOrganization smoRegistration, List<RegistrationRegistrationContact> contacts)
    {
        // Arrange
        var registrationId = smoRegistration.Id;
        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindExistingSmoAmendmentRegistration(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();
        _registrationRepositoryMock.Create(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsByIdNoTracking(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(contacts));
        _registrationRepositoryMock.Update(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(smoRegistration));

        // Act
        var result = await _service.CreateSmoAmendmentRegistrationAsync(registrationId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationResponseDto>());
        });
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_AssistantTreasurerAlreadyExists_ReturnsValidationError()
    {
        // Arrange
        long registrationId = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();
        var request = new SmoTreasurerTransferDto
        {
            NewTreasurerId = registrationContacts.FirstOrDefault(x => x.Role != FilerRole.SlateMailerOrg_AssistantTreasurer.Name && x.Role != FilerRole.SlateMailerOrg_Treasurer.Name)?.Id ?? 3,
            PreviousTreasurerKeep = true,
            PreviousTreasurerTitle = FilerRole.SlateMailerOrg_AssistantTreasurer.Name
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));

        // Act
        var result = await _service.PostSmoRegistrationTransferTreasurer(registrationId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
            Assert.That(result.ValidationErrors[0].FieldName, Is.EqualTo("Title"));
            Assert.That(result.ValidationErrors[0].Message, Does.Contain("assistant treasurer already exists"));
        });
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        long registrationId = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();
        var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();
        var users = GenerateUsers();
        var request = new SmoTreasurerTransferDto
        {
            NewTreasurerId = registrationContacts.FirstOrDefault(x => x.Role != FilerRole.SlateMailerOrg_Treasurer.Name)?.Id ?? 3,
            PreviousTreasurerKeep = true,
            PreviousTreasurerTitle = FilerRole.SlateMailerOrg_Officer.Name
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));
        _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));

        // Act
        var result = await _service.PostSmoRegistrationTransferTreasurer(registrationId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(registrationId));
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_Should_UpdateContact_When_Valid()
    {
        // Arrange
        var registrationId = 1;
        var smoRegistration = GenerateSampleSmo(registrationId, "");
        var contacts = GenerateRegistrationContacts();
        var contactDto = new SmoRegistrationContactDto
        {
            Title = FilerRole.SlateMailerOrg_Treasurer.Name,
            RoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
        };
        var decisionResponse = new List<WorkFlowError>();
        var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();
        var users = GenerateUsers();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(contacts));
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>())
            .Returns(Task.FromResult(decisionResponse));
        _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));
        _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));

        // Act
        var result = await _service.PostSmoRegistrationContactsPage05(registrationId, contactDto, false);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
        });
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_ValidQuery_Return()
    {
        // Arrange
        Filer filer = new();
        var committee = GenerateSampleCommittee();
        committee.AddressListId = 1;
        filer.CurrentRegistration = committee;
        List<Filer> list = new();
        list.Add(filer);

        AddressList addressList = GenerateAddressList();

        _registrationRepositoryMock.FindCommitteeByIdOrName(Arg.Any<string>()).Returns(Task.FromResult<IEnumerable<Filer>>(list.AsEnumerable()));
        _registrationRepositoryMock.GetAddressListById(Arg.Any<long>()).Returns(Task.FromResult<AddressList?>(addressList));

        // Act
        var result = await _service.SearchCommitteeByIdOrName("Test");

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_Should_AddContact_When_Valid()
    {
        // Arrange
        var registrationId = 1;
        var smoRegistration = GenerateSampleSmo(registrationId, "");
        var contacts = new List<RegistrationRegistrationContact>();
        var contactDto = new SmoRegistrationContactDto
        {
            Title = FilerRole.SlateMailerOrg_Treasurer.Name,
            RoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
        };
        var decisionResponse = new List<WorkFlowError>();
        var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();
        var users = GenerateUsers().Where(x => x.Id == 999).ToList();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(contacts));
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>())
            .Returns(Task.FromResult(decisionResponse));
        _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));
        _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));

        // Act
        var result = await _service.PostSmoRegistrationContactsPage05(registrationId, contactDto, false);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.That(result.Id, Is.EqualTo(registrationId));
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
        });
    }

    [TestCaseSource(nameof(SearchSmoRegistrationByIdOrNameAsyncTestCases))]
    public async Task SearchSmoRegistrationByIdOrNameAsync_ValidRequest_ReturnResult(long? statusId)
    {
        // Arrange
        var query = "1";
        var response = GenerateSampleSmos();

        _registrationRepositoryMock
            .SearchSmoRegistrationByIdOrName(Arg.Any<string>(), Arg.Any<long>(), Arg.Any<long?>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _service.SearchSmoRegistrationByIdOrNameAsync(query, statusId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<SmoRegistrationBasicResponseDto>>());
        });
    }

    #region SubmitSmoRegistrationForEfile

    [Test]
    public async Task SubmitSmoRegistrationForEfile_InitialFiling_SetsOriginalIdAndVersion()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Initial Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = null, // Initial filing
                VerificationExecutedAt = _dateNow
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        var validationErrors = new List<WorkFlowError>();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Accepted.Name
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        _authorizationSvcMock
           .GetInitiatingUserId()
           .Returns(Task.FromResult<long?>(1));
        _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _registrationRepositoryMock.Create(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));
        _filerSvcMock.AddFilerAsync(Arg.Any<Filer>()).Returns(Task.FromResult(1L));
        _registrationRepositoryMock.Update(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));

        // Act
        var result = await _service.SubmitSmoRegistrationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(submission.SlateMailerOrg.OriginalId, Is.Null);
            Assert.That(submission.SlateMailerOrg.Version, Is.EqualTo(0));
        });
    }

    [Test]
    public async Task SubmitSmoRegistrationForEfile_Amendment_SetsOriginalIdAndIncrementsVersion()
    {
        // Arrange
        var previousFiling = new SlateMailerOrganization
        {
            Id = 1,
            OriginalId = 100,
            Version = 2,
            Name = "Previous Filing",
            StatusId = RegistrationStatus.Draft.Id,
            Email = "<EMAIL>",
            VerificationExecutedAt = _dateNow,
        };

        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Amendment Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = 1, // Amendment
                Version = 3,
                VerificationExecutedAt = _dateNow,
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        var validationErrors = new List<WorkFlowError>();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Accepted.Name
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(1).Returns(Task.FromResult<SlateMailerOrganization?>(previousFiling));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _registrationRepositoryMock.Create(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));
        _registrationRepositoryMock.Update(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));
        _filerSvcMock.AddFilerAsync(Arg.Any<Filer>()).Returns(Task.FromResult(1L));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        _authorizationSvcMock
   .GetInitiatingUserId()
   .Returns(Task.FromResult<long?>(1));
        _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));

        // Act
        var result = await _service.SubmitSmoRegistrationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));
            Assert.That(submission.SlateMailerOrg.OriginalId, Is.EqualTo(previousFiling.OriginalId));
        });
    }

    [Test]
    public async Task SubmitSmoRegistrationForEfile_Amendment_NoPreviousFiling_AddsValidationError()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Amendment Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = 1 // Amendment
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        var validationErrors = new List<WorkFlowError>();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Draft.Name
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(1).Returns(Task.FromResult<SlateMailerOrganization?>(null)); // No previous filing
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), true)
            .Returns(Task.FromResult(decisionsResponse));

        // Act
        var result = await _service.SubmitSmoRegistrationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
            Assert.That(result.ValidationErrors.First().Message, Does.Contain("Previous filing should not be null for amendment filing"));
        });
    }

    [Test]
    public async Task SubmitSmoRegistrationForEfile_InvalidSubmission_ReturnsValidationErrors()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Test Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = null
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("Name", "Err001", "Validation", "Organization name must be unique.")
        };

        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Draft.Name
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSmoSubmission>(),
                true)
            .Returns(Task.FromResult(decisionsResponse));

        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(false));

        // Act
        var result = await _service.SubmitSmoRegistrationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors[0].Message, Is.EqualTo(validationErrors!.First().Message));
        });
    }

    #endregion
    // #region SubmitSmoTerminationForEfile

    [Test]
    public async Task SubmitSmoTerminationForEfile_Valid()
    {
        // Arrange
        var registration = GenerateSampleSmo(1, "Test");
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Initial Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = 1, // Initial filing
                VerificationExecutedAt = _dateNow,
                TerminatedAt = _dateNow,


            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        var validationErrors = new List<WorkFlowError>();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Accepted.Name
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        //ValidateSmoAttestation
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTerminationSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoTerminationSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);

        _authorizationSvcMock
           .GetInitiatingUserId()
           .Returns(Task.FromResult<long?>(1));
        _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));
        _registrationRepositoryMock.FindSlateMailerOrganizationById(1).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.Create(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));
        _filerSvcMock.AddFilerAsync(Arg.Any<Filer>()).Returns(Task.FromResult(1L));
        _registrationRepositoryMock.Update(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));

        // Act
        var result = await _service.SmoTerminationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));

        });
    }
    [Test]
    public async Task SubmitSmoTerminationForEfile_Valid_address()
    {
        // Arrange

        var Addresses = new List<Address>
                 {
          new Address
        {
            Street = "Old St",
            City = "Old City",
            Country = "USA",
            Purpose = "Organization", // must match
            State = "CA",
            Type = "Business",
            Zip = "90000"
        }
            };

        var response = GenerateSampleSmos();
        var registration = GenerateSampleSmo1(1, "Test");

        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Initial Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = 1,
                VerificationExecutedAt = _dateNow,
                TerminatedAt = _dateNow,
                AddressList = new AddressList
                {
                    Addresses = Addresses
                }
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        var validationErrors = new List<WorkFlowError>();
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Accepted.Name
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        //ValidateSmoAttestation
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTerminationSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoTerminationSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);

        _authorizationSvcMock
           .GetInitiatingUserId()
           .Returns(Task.FromResult<long?>(1));
        _filerSvcMock
            .AddFilerAsync(Arg.Any<Filer>())
            .Returns(Task.FromResult(1L));
        _registrationRepositoryMock.FindSlateMailerOrganizationById(1).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.Create(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));
        _filerSvcMock.AddFilerAsync(Arg.Any<Filer>()).Returns(Task.FromResult(1L));
        _registrationRepositoryMock.Update(Arg.Any<SlateMailerOrganization>()).Returns(Task.FromResult<Registration>(submission.SlateMailerOrg));

        // Act
        var result = await _service.SmoTerminationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(RegistrationStatus.Draft.Id));

        });
    }

    [Test]
    public async Task SubmitSmoTerminationForEfile_InvalidSubmission_ReturnsValidationErrors()
    {
        // Arrange
        var registration = GenerateSampleSmo(1, "Test");
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Test Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = null
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        var validationErrors = new List<WorkFlowError>
         {
             new("Name", "Err001", "Validation", "Organization name must be unique.")
         };

        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Draft.Name
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSmoSubmission>(),
                true)
            .Returns(Task.FromResult(decisionsResponse));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTerminationSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoTerminationSubmission>(), Arg.Any<bool>()).Returns(decisionsResponse);

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(false));

        // Act
        var result = await _service.SmoTerminationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors[0].Message, Is.EqualTo(validationErrors!.First().Message));
        });
    }

    [Test]
    public async Task SubmitSlateMailerOrganizationTerminationForm_ShouldReturnValidationErrors_WhenValidationFails()
    {
        // Arrange
        var submission = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new Models.FilerRegistration.Registrations.SlateMailerOrganization
            {
                Name = "Valid Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = 1,
                RegistrationRegistrationContacts = new List<RegistrationRegistrationContact>()
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            LinkedCommitteeId = 1234
        };



        var validationErrors = new List<WorkFlowError>
    {
        new WorkFlowError("FieldName", "ErrorCode", "ErrorType", "Organization names must be unique. There is an active organization with this name.")
    };
        var decisionsResponse = new DecisionsSmoSubmissionResponse
        {
            Result = validationErrors,
            Status = RegistrationStatus.Accepted.Name
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsSmoSubmission>(), true)
            .Returns(Task.FromResult(decisionsResponse));
        var registration = GenerateSampleSmo(1, "Test");

        _registrationRepositoryMock
      .FindSlateMailerOrganizationById(1)
      .Returns(Task.FromResult<SlateMailerOrganization?>(registration));

        _registrationRepositoryMock
            .FindSlateMailerOrganizationWithParentById(1)
            .Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        // Act
        var result = await _service.SmoTerminationForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
            Assert.That(result.ValidationErrors[0].Message, Is.EqualTo("Organization names must be unique. There is an active organization with this name."));

        });
    }
    // #endregion

    #region UpdateNoticeOfTerminationSmoRegistrationAsync
    [Test]
    public void UpdateNoticeOfTerminationSmoRegistrationAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;

        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local),
            CheckRequiredFields = true
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={registrationId}"));
    }

    [Test]
    public void UpdateNoticeOfTerminationSmoRegistrationAsync_TerminatedRegistration_ShouldSkip()
    {
        // Arrange
        var registrationId = 1;
        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local),
            CheckRequiredFields = true
        };
        var registration = GenerateSampleSmo(1, "", RegistrationStatus.Terminated.Id);

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));

        // Act & Asserts
        Assert.DoesNotThrowAsync(() => _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload));
        _registrationRepositoryMock.Received(0).Update(Arg.Any<SlateMailerOrganization>());
    }

    [Test]
    public void UpdateNoticeOfTerminationSmoRegistrationAsync_IsOriginalRegistration_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;
        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local),
            CheckRequiredFields = true
        };
        var registration = GenerateSampleSmo(1, "", RegistrationStatus.Draft.Id);
        var validationErrors = new List<WorkFlowError>();

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTermination, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoTermination>(),
            true,
            true
        ).Returns(Task.FromResult(validationErrors));

        // Act & Asserts
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload));
        Assert.That(ex.Message, Is.EqualTo($"The status of most recent registration is not {RegistrationStatus.Accepted}. FilerId={registration.FilerId.GetValueOrDefault()}"));
    }

    [Test]
    public void UpdateNoticeOfTerminationSmoRegistrationAsync_NotHaveRecentAcceptedRegistration_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;
        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local),
            CheckRequiredFields = true
        };
        var registration = GenerateSampleSmo(1, "", RegistrationStatus.Draft.Id);
        registration.Parent = new SlateMailerOrganization
        {
            Id = 0,
            Email = "test",
            Name = "SMO",
            StatusId = RegistrationStatus.Pending.Id,
        };
        var validationErrors = new List<WorkFlowError>();

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTermination, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoTermination>(),
            true,
            true
        ).Returns(Task.FromResult(validationErrors));

        // Act & Asserts
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload));
        Assert.That(ex.Message, Is.EqualTo($"The status of most recent registration is not {RegistrationStatus.Accepted}. FilerId={registration.FilerId.GetValueOrDefault()}"));
    }

    [Test]
    public async Task UpdateNoticeOfTerminationSmoRegistrationAsync_TerminateRequest_ShouldMarkRegistrationAsTerminated()
    {
        // Arrange
        var registrationId = 1;
        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local),
            CheckRequiredFields = true
        };
        var registration = GenerateSampleSmo(1, "", RegistrationStatus.Draft.Id);
        registration.Parent = new SlateMailerOrganization
        {
            Id = 0,
            Email = "test",
            Name = "SMO",
            StatusId = RegistrationStatus.Accepted.Id,
        };
        var validationErrors = new List<WorkFlowError>();

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTermination, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoTermination>(),
            true,
            true
        ).Returns(Task.FromResult(validationErrors));

        // Act
        await _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload);

        // Asserts
        _ = _registrationRepositoryMock.Received(1).Update(Arg.Any<SlateMailerOrganization>());
        Assert.That(registration.TerminatedAt, Is.Not.Null);
    }

    [Test]
    public async Task UpdateNoticeOfTerminationSmoRegistrationAsync_NotTerminateRequest_ShouldUnmarkRegistrationAsTerminated()
    {
        // Arrange
        var registrationId = 1;
        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = false,
            TerminationDate = null,
            CheckRequiredFields = true
        };
        var registration = GenerateSampleSmo(1, "", RegistrationStatus.Draft.Id);
        registration.Parent = new SlateMailerOrganization
        {
            Id = 0,
            Email = "test",
            Name = "SMO",
            StatusId = RegistrationStatus.Accepted.Id,
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));

        // Act
        await _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload);

        // Asserts
        _ = _registrationRepositoryMock.Received(1).Update(Arg.Any<SlateMailerOrganization>());
        Assert.That(registration.TerminatedAt, Is.Null);
    }

    [Test]
    public async Task UpdateNoticeOfTerminationSmoRegistrationAsync_TerminateRequest_WithDecisionErrors_ShouldReturnThem()
    {
        // Arrange
        var registrationId = 1;
        var parentId = 100;

        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local),
            CheckRequiredFields = true
        };

        var registration = GenerateSampleSmo(registrationId, "", RegistrationStatus.Draft.Id);
        var parent = GenerateSampleSmo(parentId, "", RegistrationStatus.Accepted.Id);
        parent.SubmittedAt = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local);

        registration.Parent = parent;
        registration.ParentId = parent.Id;

        var expectedErrors = new List<WorkFlowError>
        {
            new("EffectiveDateOfTermination", "E001", "Validation", "Invalid termination date.")
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(registrationId)
            .Returns(Task.FromResult<SlateMailerOrganization?>(registration));

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(parent.Id)
            .Returns(Task.FromResult<SlateMailerOrganization?>(parent));

        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoTermination, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsSmoTermination>(),
            true,
            true
        ).Returns(Task.FromResult(expectedErrors));

        // Act
        var result = await _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.ValidationErrors, Is.EquivalentTo(expectedErrors));
    }

    [Test]
    public async Task UpdateNoticeOfTerminationSmoRegistrationAsync_NotTerminating_ShouldReturnEmptyDecisionErrors()
    {
        // Arrange
        var registrationId = 1;
        var parentId = 100;

        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = false,
            TerminationDate = null,
            CheckRequiredFields = true
        };

        var registration = GenerateSampleSmo(registrationId, "", RegistrationStatus.Draft.Id);
        var parent = GenerateSampleSmo(parentId, "", RegistrationStatus.Accepted.Id);
        parent.SubmittedAt = DateTime.SpecifyKind(_dateNow, DateTimeKind.Local);

        registration.Parent = parent;
        registration.ParentId = parent.Id;

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(registrationId)
            .Returns(Task.FromResult<SlateMailerOrganization?>(registration));

        _registrationRepositoryMock.FindSlateMailerOrganizationWithParentById(parent.Id)
            .Returns(Task.FromResult<SlateMailerOrganization?>(parent));

        // Act
        var result = await _service.UpdateNoticeOfTerminationSmoRegistrationAsync(registrationId, terminationPayload);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.ValidationErrors, Is.Empty);
    }

    #endregion

    #region IsRegistrationTerminatingAsync
    [Test]
    public void IsRegistrationTerminatingAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var registrationId = 1;

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.IsRegistrationTerminatingAsync(registrationId));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={registrationId}"));
    }

    [Test]
    public async Task IsRegistrationTerminatingAsync_FoundRegistration_ShouldReturnFlag()
    {
        // Arrange
        var registrationId = 1;
        var registration = GenerateSampleSmo(1, "", RegistrationStatus.Draft.Id);
        registration.TerminatedAt = _dateNow;

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));

        // Act 
        var result = await _service.IsRegistrationTerminatingAsync(registrationId);

        // Asserts
        Assert.That(result, Is.True);
    }
    #endregion

    #region SearchLatestAcceptedSmoRegistrationOfficerByNameAsync
    [Test]
    public async Task SearchLatestAcceptedSmoRegistrationOfficerByNameAsync_ValidRequest_ReturnResult()
    {
        // Arrange
        var filerId = 1;
        var query = "1";
        var response = GenerateRegistrationContacts();

        _registrationRegistrationContactRepositoryMock
            .SearchLatestAcceptedSmoRegistrationOfficerByName(Arg.Any<long>(), Arg.Any<string>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _service.SearchLatestAcceptedSmoRegistrationOfficerByNameAsync(filerId, query);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<SmoRegistrationContactDto>>());
        });
    }
    #endregion

    [Test]
    public void UpdateRegistrationContact_ValidRequest_ShouldUpdateContact()
    {
        // Arrange
        var request = new SmoRegistrationContactDto
        {
            PhoneNumber = new PhoneNumberDto
            {
                Number = "123456",
                SelectedCountry = 1,
                CountryCode = "+1",
            }
        };
        var existingContact = GenerateRegistrationContacts().FirstOrDefault(x => x.Id == 1)?.RegistrationContact;
        var methodInfo = typeof(SmoRegistrationSvc).GetMethod("UpdateRegistrationContact", BindingFlags.NonPublic | BindingFlags.Instance);

        // Act
        var result = methodInfo?.Invoke(_service, [request, existingContact]);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationContact>());
            var mappedResult = result as RegistrationContact;
            Assert.That(mappedResult?.Id, Is.EqualTo(1));
        });
    }

    #region GetCurrentUserRoleByIdAsync
    [Test]
    public async Task GetCurrentUserRoleByIdAsync_ValidRequest_ReturnResult()
    {
        // Arrange
        var userId = 1L;
        var roleId = 91;
        SlateMailerOrganization? registration = GenerateSampleSmo(1L, "Test");
        FilerUserDto? filerUserDto = new()
        {
            FilerRoleId = roleId,
        };

        _ = _registrationRepositoryMock
            .FindSlateMailerOrganizationBasicById(Arg.Any<long>())
            .Returns(registration);
        _ = _authorizationSvcMock
            .GetInitiatingUserId()
            .Returns(userId);
        _ = _filerSvcMock
            .GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>())
            .Returns(filerUserDto);

        // Act
        var result = await _service.GetCurrentUserRoleByIdAsync(1L);

        // Assert
        Assert.That(result, Is.EqualTo(roleId));
    }

    [Test]
    public void GetCurrentUserRoleByIdAsync_InValidRegistrationId_ThrowsException()
    {
        // Arrange
        var registrationId = 1L;
        var roleId = 91;
        SlateMailerOrganization? registration = GenerateSampleSmo(1L, "Test");
        FilerUserDto? filerUserDto = new()
        {
            FilerRoleId = roleId,
        };

        // Act
        // Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetCurrentUserRoleByIdAsync(registrationId));
        Assert.That(ex.Message, Is.EqualTo($"Registration not found. Id={registrationId}"));

    }

    [Test]
    public void GetCurrentUserRoleByIdAsync_InValidUser_ThrowsException()
    {
        // Arrange
        var registrationId = 1L;
        var userId = 1L;

        SlateMailerOrganization? registration = GenerateSampleSmo(1L, "Test");
        FilerUserDto? filerUserDto = null;
        _ = _registrationRepositoryMock
            .FindSlateMailerOrganizationBasicById(Arg.Any<long>())
            .Returns(registration);
        _ = _authorizationSvcMock
            .GetInitiatingUserId()
            .Returns(userId);
        _ = _filerSvcMock
            .GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>())
            .Returns(filerUserDto);


        // Act
        // Assert
        var ex = Assert.ThrowsAsync<UnauthorizedAccessException>(() => _service.GetCurrentUserRoleByIdAsync(registrationId));
        Assert.That(ex.Message, Is.EqualTo($"The current user is not associated to the registration. ID={registrationId}"));

    }

    #endregion

    #region GetSmoRegistrationFilingSummary
    [Test]
    [TestCase(10, 10, true, true)]
    [TestCase(10, 11, true, false)]
    [TestCase(10, 10, false, false)]
    public async Task GetSmoRegistrationFilingSummary_ValidRequest_ReturnResult(long userId, long rcUserId, bool rcAck, bool isAck)
    {

        // Arrange

        var registration = GenerateSampleSmo(1, "Test");
        registration.OriginalId = 111;

        registration.StatusId = RegistrationStatus.Pending.Id;
        registration.SubmittedAt = DateTime.Today;

        var filerUser = new FilerUserDto
        {
            UserId = userId,
            User = new User
            {
                FirstName = "John",
                LastName = "Doe",
                EmailAddress = "Email",
                EntraOid = "1"
            },
            FilerRole = new FilerRole
            {
                Id = 1,
                Name = "Officer",
                Description = "description",
            }
        };

        var phoneNumberList = new PhoneNumberList
        {
            Id = 1,
            PhoneNumbers = new List<PhoneNumber>
            {
                new()
                {
                    Id = 1,
                    Number = "1234567890",
                    Type = "Home",
                    CountryCode = "+1",
                    InternationalNumber = false,
                }
            }
        };
        var contacts = new List<RegistrationRegistrationContact>()
        {
            new ()
            {
                Id = 1,
                RegistrationId = 123,
                Registration = registration,
                RegistrationContactId = 1,
                RegistrationContact = new RegistrationContact
                {
                    Id = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    MiddleName = "A",
                    Email = "<EMAIL>",
                    PhoneNumberListId = 1,
                    UserId = rcUserId
                },
                CapitalContributionOver10K = true,
                TenPercentOrGreater = true,
                CanAuthorizeSlateMailerContents = true,
                Role = FilerRole.SlateMailerOrg_Officer.Name,
                Title = "President",
                CumulativeCapitalContributions = 10000,
                PercentOfOwnership = 10,
                CreatedBy = 1,
                ModifiedBy = 1,
                HasAcknowledged = rcAck,
            }
        };

        var attestation = new Attestation
        {

        };
        _repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(registration);
        _servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(filerUser);
        _repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(contacts);
        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.FindById(Arg.Any<long>()).Returns(registration);
        _registrationRepositoryMock.GetPhoneNumberListById(Arg.Any<long>()).Returns(phoneNumberList);
        _registrationRegistrationContactRepositoryMock.GetRegistrationContacts(Arg.Any<long>()).Returns(contacts);
        _repositoriesDependencies.AttestationRepository.FindByRegistrationId(Arg.Any<long>()).Returns(attestation);
        // Act
        var result = await _service.GetSmoRegistrationFilingSummary(1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationFilingSummaryDto>());
            Assert.That(result.IsTreasurerAcknowledgement, Is.EqualTo(isAck));
        });
    }
    #endregion

    #region HandleRegistrationEditAsync
    [Test]
    public async Task HandleRegistrationEditAsync_ValidRequest_ReturnResult()
    {

        // Arrange

        var registration = GenerateSampleSmo(1, "Test");
        registration.OriginalId = 111;

        registration.StatusId = RegistrationStatus.Pending.Id;
        registration.SubmittedAt = DateTime.Today;

        _repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(registration);
        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
        _registrationRepositoryMock.FindById(Arg.Any<long>()).Returns(registration);
        _decisionsSvcMock.InitiateWorkflow<string, DecisionResponse>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<string>())
            .Returns(new DecisionResponse());

        // Act
        await _service.HandleRegistrationEditAsync(1);

        // Assert
        await _registrationRepositoryMock.Received().Update(Arg.Any<SlateMailerOrganization>());
    }
    [Test]
    public void HandleRegistrationEditAsync_RegistrationStatusNotPending_ThrowsException()
    {

        // Arrange

        var registration = GenerateSampleSmo(1, "Test");
        registration.OriginalId = 111;

        registration.StatusId = RegistrationStatus.Accepted.Id;
        registration.SubmittedAt = DateTime.Today;

        _repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(registration);
        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));

        // Assert
        Assert.ThrowsAsync<InvalidOperationException>(() => _service.HandleRegistrationEditAsync(Arg.Any<long>()));
    }
    #endregion

    #region ModifyFilerUserAsync
    [Test]
    public async Task ModifyFilerUserAsync_ReturnsWithoutUserId()
    {
        // Arrange
        var filerId = 1L;
        _filerSvcMock.GetFilerUsersAsync(Arg.Is(filerId)).Returns(new List<FilerUserDto>());

        // Act
        await PrivateMemberAccessor.InvokePrivateAsync(
            _service,
            "ModifyFilerUserAsync",
            [filerId, null, 2, false]
        );

        // Assert
        await _filerSvcMock.DidNotReceive().UpdateFilerUserRoleAsync(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task ModifyFilerUserAsync_WhenNoFilerUser_AddsNewFilerUser()
    {
        // Arrange
        var filerId = 1L;
        var userId = 3L;
        var filerRoleId = 2L;
        _filerSvcMock.GetFilerUsersAsync(Arg.Is(filerId)).Returns(new List<FilerUserDto>());

        // Act
        await PrivateMemberAccessor.InvokePrivateAsync(
            _service,
            "ModifyFilerUserAsync",
            [filerId, userId, filerRoleId, false]
        );

        // Assert
        await _filerSvcMock.Received(1).AddFilerUserAsync(
            Arg.Is<FilerUser>(x => x.FilerId == filerId && x.FilerRoleId == filerRoleId && x.UserId == userId)
        );
        await _filerSvcMock.DidNotReceive().UpdateFilerUserRoleAsync(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task ModifyFilerUserAsync_ReturnsWithoutUserId2()
    {
        // Arrange
        var filerId = 1L;
        var userId = 3L;
        var filerRoleId = 2L;
        var existingFilerUser = new FilerUserDto
        {
            Id = 10,
            UserId = userId,
        };
        _filerSvcMock.GetFilerUsersAsync(Arg.Is(filerId)).Returns(new List<FilerUserDto>() { existingFilerUser });

        // Act
        await PrivateMemberAccessor.InvokePrivateAsync(
            _service,
            "ModifyFilerUserAsync",
            [filerId, userId, filerRoleId, false]
        );

        // Assert
        await _filerSvcMock.Received(1).UpdateFilerUserRoleAsync(Arg.Is<long>(10), Arg.Is(filerRoleId));
    }

    [Test]
    public async Task ModifyFilerUserAsync_ReplaceCurrentTreasurer_ShouldUpdateFilerUserToAccountManager()
    {
        // Arrange
        var filerId = 1L;
        var userId = 1L;
        var filerRoleId = 2L;
        var existingFilerUser = new FilerUserDto
        {
            Id = 1,
            UserId = userId,
            FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
        };
        _filerSvcMock.GetFilerUsersAsync(Arg.Is(filerId)).Returns(new List<FilerUserDto>() { existingFilerUser });
        _authorizationSvcMock.GetInitiatingUserId().Returns(Task.FromResult<long?>(userId));

        // Act
        await PrivateMemberAccessor.InvokePrivateAsync(
            _service,
            "ModifyFilerUserAsync",
            [filerId, null, filerRoleId, true]
        );

        // Assert
        await _filerSvcMock.Received(1).UpdateFilerUserRoleAsync(Arg.Is<long>(1), Arg.Is(FilerRole.SlateMailerOrg_AccountManager.Id));
    }
    #endregion

    #region Private
    private static IEnumerable<object[]> UpdateFilerRoleAsyncTestCases()
    {
        yield return new object[] { true, false, "" };
        yield return new object[] { false, true, FilerRole.SlateMailerOrg_AssistantTreasurer.Name };
        yield return new object[] { false, true, FilerRole.SlateMailerOrg_Officer.Name };
        yield return new object[] { false, false, "" };
    }

    private static IEnumerable<object[]> UpdateSmoRegistrationTestCases()
    {
        yield return new object[]
        {
            new SlateMailerOrganization
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
            },
            0L
        };
        yield return new object[]
        {
            new SlateMailerOrganization
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                }
            },
            0L
        };
        yield return new object[]
        {
            new SlateMailerOrganization
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                }
            },
            2L
        };
        yield return new object[]
        {
            new SlateMailerOrganization
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                    FilerLinks = new List<FilerLink>
                    {
                        new() {
                            Id = 1,
                            FilerId = 1,
                            LinkedEntityId = 2,
                            EffectiveDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                            FilerLinkTypeId = 1,
                            CreatedBy = 1,
                            ModifiedBy = 1,
                        }
                    }
                }
            },
            2L
        };
        yield return new object[]
        {
            new SlateMailerOrganization
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                    FilerLinks = new List<FilerLink>
                    {
                        new() {
                            Id = 1,
                            FilerId = 1,
                            LinkedEntityId = 3,
                            EffectiveDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                            FilerLinkTypeId = 1,
                            CreatedBy = 1,
                            ModifiedBy = 1,
                        }
                    }
                }
            },
            2L
        };
    }

    private static IEnumerable<object?[]> CreateSmoAmendmentRegistrationAsyncTestCases()
    {
        yield return new object?[]
        {
            GenerateSampleSmos().FirstOrDefault(x => x.Id == 1),
            GenerateRegistrationContacts(),
        };
        yield return new object?[]
        {
            GenerateSampleSmos().FirstOrDefault(x => x.Id == 2),
            GenerateRegistrationContacts(),
        };
    }

    private static IEnumerable<long?> SearchSmoRegistrationByIdOrNameAsyncTestCases()
    {
        yield return null;
        yield return RegistrationStatus.Accepted.Id;
    }

    private static SlateMailerOrganization GenerateSampleSmo(long id, string sampleName, long statusId = 3)
    {
        return new()
        {
            Id = id,
            StatusId = statusId,
            Name = "Base",
            Email = "<EMAIL>",
            FilerId = 1,
            ParentId = 2,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.SlateMailerOrg.Id,
                CurrentRegistration = new GeneralPurposeCommittee()
                {
                    Id = 2,
                    StatusId = statusId,
                    CommitteeType = CommitteeType.GeneralPurpose,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    JurisdictionCounty = string.Empty,
                    JurisdictionActive = string.Empty,
                    FinancialInstitutionName = string.Empty,
                    FinancialInstitutionPhone = string.Empty,
                    FinancialInstitutionAccountNumber = string.Empty,
                },
                Users = new()
                {
                    new()
                    {
                        FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                        User = new()
                        {
                            FirstName = "FirstName",
                            LastName = "LastName",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            },
            Attestations = statusId == RegistrationStatus.Accepted.Id
            ? new List<Attestation>
            {
                GenerateAttestation()
            } : null,
        };
    }

    private static SlateMailerOrganization GenerateSampleSmo1(long id, string sampleName, long statusId = 3)
    {
        var mailingAddress = new Address
        {
            Street = "123 Main St",
            City = "Los Angeles",
            Country = "USA",
            Purpose = "Organization",
            State = "CA",
            Type = "Business",
            Zip = "90001"
        };
        return new()
        {
            Id = id,
            StatusId = statusId,
            Name = "Base",
            Email = "<EMAIL>",
            FilerId = 1,
            ParentId = 2,
            ActivityLevel = "City",
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.SlateMailerOrg.Id,
                CurrentRegistration = new GeneralPurposeCommittee()
                {
                    Id = 2,
                    StatusId = statusId,
                    CommitteeType = CommitteeType.GeneralPurpose,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    JurisdictionCounty = string.Empty,
                    JurisdictionActive = string.Empty,
                    FinancialInstitutionName = string.Empty,
                    FinancialInstitutionPhone = string.Empty,
                    FinancialInstitutionAccountNumber = string.Empty,
                },
                Users = new()
                {
                    new()
                    {
                        FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                        User = new()
                        {
                            FirstName = "FirstName",
                            LastName = "LastName",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            },
            AddressList = new()
            {
                Id = 1,
                Addresses = new List<Address>
                    {
                        new() { Street = "123 Main St", City = "Los Angeles", State = "CA", Zip = "90001", Country = "USA", Type = "Business", Purpose = "Mailing" },
                    }
            },
            Attestations = statusId == RegistrationStatus.Accepted.Id


            ? new List<Attestation>
            {
                GenerateAttestation()
            } : null,
        };
    }
    private static List<SlateMailerOrganization> GenerateSampleSmos()
    {
        return new List<SlateMailerOrganization>
        {
            new()
            {
                Id = 1,
                StatusId = RegistrationStatus.Accepted.Id,
                Name = "Base",
                Email = "<EMAIL>",
                FilerId = 1,
                ParentId = 2,
                Filer = new()
                {
                    FilerStatusId = FilerStatus.Active.Id,
                    FilerTypeId = FilerType.SlateMailerOrg.Id,
                },
                AddressList = new()
                {
                    Id = 1,
                    Addresses = new List<Address>
                    {
                        new() { Street = "123 Main St", City = "Los Angeles", State = "CA", Zip = "90001", Country = "USA", Type = "Business", Purpose = "Organization" },
                    }
                },
                PhoneNumberList = new()
                {
                    Id = 1,
                    PhoneNumbers = new List<PhoneNumber>
                    {
                        new() { Number = "5551234", CountryCode = "+1", Type = "Mobile" },
                    }
                },
                Attestations = new List<Attestation>()
                {
                    new()
                    {
                        Id = 1,
                    }
                }
            },
            new()
            {
                Id = 2,
                StatusId = RegistrationStatus.Accepted.Id,
                Name = "Base",
                Email = "<EMAIL>",
                FilerId = 2,
                ParentId = 2,
                Filer = new()
                {
                    FilerStatusId = FilerStatus.Active.Id,
                    FilerTypeId = FilerType.SlateMailerOrg.Id,
                },
            }
        };
    }

    private static List<FilerUser> GenerateFilerUser()
    {
        return new List<FilerUser>
        {
             new()
             {
                Id = 1,
                FilerId = 1,
                FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                UserId = 1,
                FilerRole = FilerRole.SlateMailerOrg_Treasurer,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 1)
             },
             new()
             {
                Id = 2,
                FilerId = 2,
                FilerRoleId = FilerRole.SlateMailerOrg_Officer.Id,
                UserId = 2,
                FilerRole = FilerRole.SlateMailerOrg_Officer,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 2)
             },
             new()
             {
                Id = 3,
                FilerId = 2,
                FilerRoleId = FilerRole.SlateMailerOrg_AccountManager.Id,
                UserId = 3,
                FilerRole = FilerRole.SlateMailerOrg_AccountManager,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 3)
             },
             new()
             {
                Id = 4,
                FilerId = 3,
                FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                UserId = 4,
                FilerRole = FilerRole.SlateMailerOrg_Treasurer,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 4)
             },
        };
    }

    private static List<RegistrationRegistrationContact> GenerateRegistrationContacts()
    {
        return new List<RegistrationRegistrationContact>
        {
            new()
            {
                Id = 1,
                Role = FilerRole.SlateMailerOrg_Treasurer.Name,
                Title = "",
                PercentOfOwnership = 1m,
                CapitalContributionOver10K = false,
                CumulativeCapitalContributions = 0m,
                TenPercentOrGreater = false,
                CreatedBy = 0,
                ModifiedBy = 0,
                HasAcknowledged = false,
                RegistrationContact = new RegistrationContact
                {
                    Id = 1,
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "Test",
                    MiddleName = "",
                    PhoneNumberList = new PhoneNumberList
                    {
                        PhoneNumbers = new List<PhoneNumber>
                        {
                            new()
                            {
                                Id = 1,
                                Number = "12345555",
                                Type = "Home",
                            }
                        }
                    },
                    UserId = 1
                }
            },
            new ()
            {
                Id = 2,
                Role = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                Title = "",
                PercentOfOwnership = 1m,
                CapitalContributionOver10K = false,
                CumulativeCapitalContributions = 0m,
                TenPercentOrGreater = false,
                CreatedBy = 0,
                ModifiedBy = 0,
                HasAcknowledged = false,
                RegistrationContact = new RegistrationContact
                {
                    Id = 2,
                    Email = "<EMAIL>",
                    FirstName = "Test1",
                    LastName = "Test1",
                    MiddleName = "",
                    AddressList = new()
                    {
                        Id = 1,
                        Addresses = new List<Address>
                        {
                            new() { Street = "123 Main St", City = "Los Angeles", State = "CA", Zip = "90001", Country = "USA", Type = "Business", Purpose = "Organization" },
                        }
                    },
                    PhoneNumberList = new()
                    {
                        Id = 1,
                        PhoneNumbers = new List<PhoneNumber>
                        {
                            new() { Number = "5551234", CountryCode = "+1", Type = "Mobile" },
                        }
                    },
                    UserId = 2
                }
            },
            new()
            {
                Id = 3,
                Role = FilerRole.SlateMailerOrg_Treasurer.Name,
                Title = "",
                PercentOfOwnership = 1m,
                CapitalContributionOver10K = false,
                CumulativeCapitalContributions = 0m,
                TenPercentOrGreater = false,
                CreatedBy = 0,
                ModifiedBy = 0,
                HasAcknowledged = true,
                AcknowledgedOn = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                RegistrationContact = new RegistrationContact
                {
                    Id = 3,
                    Email = "<EMAIL>",
                    FirstName = "Test1",
                    LastName = "Test1",
                    MiddleName= "",
                    UserId = 3
                }
            },
            new()
            {
                Id = 4,
                Role = FilerRole.SlateMailerOrg_Treasurer.Name,
                Title = "",
                PercentOfOwnership = 1m,
                CapitalContributionOver10K = false,
                CumulativeCapitalContributions = 0m,
                TenPercentOrGreater = false,
                CreatedBy = 0,
                ModifiedBy = 0,
                HasAcknowledged = true,
                AcknowledgedOn = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                RegistrationContact = new RegistrationContact
                {
                    Id = 4,
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "Test",
                    MiddleName = "",
                    UserId = 4
                }
            },
        };
    }

    private static List<User> GenerateUsers()
    {
        return new List<User>
        {
            new()
            {
                Id = 1,
                FirstName = "Test",
                LastName = "Test",
                EmailAddress = "<EMAIL>",
                UserName = "TestTest",
                EntraOid = "TestOid"
            },
            new()
            {
                Id = 2,
                FirstName = "Test2",
                LastName = "Test2",
                EmailAddress = "<EMAIL>",
                UserName = "Test2Test2",
                EntraOid = "TestOid2"
            },
            new()
            {
                Id = 3,
                FirstName = "Test1",
                LastName = "Test1",
                EmailAddress = "<EMAIL>",
                UserName = "Test1Test1",
                EntraOid = "TestOid3"
            },
            new()
            {
                Id = 999,
                FirstName = "Test2",
                LastName = "Test2",
                EmailAddress = "<EMAIL>",
                UserName = "Test2Test2",
                EntraOid = "TestOid4"
            }
        };
    }

    private static Attestation GenerateAttestation()
    {
        return new Attestation
        {
            Id = 1,
            Title = FilerRole.SlateMailerOrg_Treasurer.Name,
            ExecutedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            Name = "Test Test"
        };
    }

    private static List<FilerUser> GenerateFilerUsers()
    {
        return new List<FilerUser>
        {
            new()
            {
                Id = 1,
                UserId = 1,
                FilerRoleId = FilerRole.SlateMailerOrg_AccountManager.Id,
                FilerId = 1,
            },
            new()
            {
                Id = 2,
                UserId = 2,
                FilerRoleId = FilerRole.SlateMailerOrg_AssistantTreasurer.Id,
                FilerId = 1,
            },
            new()
            {
                Id = 3,
                UserId = 3,
                FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                FilerId = 1,
            },
            new()
            {
                Id = 4,
                UserId = 4,
                FilerRoleId = FilerRole.SlateMailerOrg_Officer.Id,
                FilerId = 1,
            },
        };
    }

    private static AddressList GenerateAddressList()
    {
        AddressList addressList = new()
        {
            Addresses = new()
            {
                new()
                {
                    Id = 1,
                    Street = "1585 Kapiolani Blvd, STE 1800",
                    Street2 = "",
                    City = "Honolulu",
                    State = "Hawaii",
                    Zip = "96814",
                    Country = "United States",
                    Type = "Residential",
                    Purpose = "Mailing",
                },
                new()
                {
                    Id = 2,
                    Street = "1585 Kapiolani Blvd, STE 1800",
                    Street2 = "",
                    City = "Honolulu",
                    State = "Hawaii",
                    Zip = "96814",
                    Country = "United States",
                    Type = "Residential",
                    Purpose = "Committee",
                },
            }
        };

        return addressList;
    }

    private static PrimarilyFormedCommittee GenerateSampleCommittee()
    {
        return new PrimarilyFormedCommittee
        {
            StatusId = RegistrationStatus.Pending.Id,
            Id = 1,
            Email = "test.com",
            JurisdictionActive = "true",
            JurisdictionCounty = "country",
            FinancialInstitutionAccountNumber = "1",
            FinancialInstitutionName = "New name",
            FinancialInstitutionPhone = "11",
            FilerId = 1,
            Name = "Committee Name",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
            {
                new() {
                    Street = "123 Test St",
                    City = "Testville",
                    State = "CA",
                    Zip = "90000",
                    Purpose = "Candidate",
                    Type = "Business",
                    Country ="USA"
                }
            }
            },
            Filer = new Filer
            {
                Users = new List<FilerUser>
            {
                new()
                {
                    FilerRole = FilerRole.RecipientCommittee_Treasurer,
                    User = new User
                    {
                        FirstName = "Test",
                        LastName = "Treasurer",
                        EmailAddress = "<EMAIL>",
                        EntraOid = "TestOid"
                    }
                }
            }
            }
        };
    }

    #endregion
}
