@using SOS.CalAccess.FilerPortal.Models.Localization
@using Microsoft.AspNetCore.Mvc.ModelBinding;
@using SOS.CalAccess.Foundation.Utils
@using SOS.CalAccess.UI.Common.Models;
@using SOS.CalAccess.UI.Common.Enums;
@using SOS.CalAccess.UI.Common.Localization;
@inject IHtmlLocalizer<SharedResources> Localizer;
@inject IDateTimeSvc DateTimeSvc

@model SOS.CalAccess.FilerPortal.Models.Registrations.AmendSmoRegistration.AmendSmoRegistrationStep04ViewModel;
@{
    // FR-SMO-A-Termination-2
    var progressItem1Name = ViewData["ProgressItem1Name"]?.ToString() ?? "";
    var progressItem2Name = ViewData["ProgressItem2Name"]?.ToString() ?? "";
    var progressItem3Name = ViewData["ProgressItem3Name"]?.ToString() ?? "";
    var progressItem4Name = ViewData["ProgressItem4Name"]?.ToString() ?? "";
    var progressItem5Name = ViewData["ProgressItem5Name"]?.ToString() ?? "";

    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            ButtonBarModel.DefaultContinue,
        },
        RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", Model.Id),
            },
            ButtonBarModel.DefaultSaveAndClose,
        }
    };

    var id = Model.Id;
    var progressItems = new List<ProgressItem>
    {
        new ProgressItem(progressItem1Name, true, false, true, $"/AmendSmoRegistration/Page01/{id}", true),
        new ProgressItem(progressItem2Name, true, false, true, $"/AmendSmoRegistration/Page03/{id}", true),
        new ProgressItem(progressItem3Name, true, false, true, $"/AmendSmoRegistration/Page09/{id}", true),
        new ProgressItem(progressItem4Name, false, true, true, $"/AmendSmoRegistration/Termination01/{id}", true),
        new ProgressItem(progressItem5Name, false, false, true, $"/AmendSmoRegistration/Page11/{id}", true),
    };
    var progressBar = new ProgressBar(progressItems);

}
<partial name="_LayoutProgressbar" model="progressBar" />

@using (Html.BeginForm("Termination02", "AmendSmoRegistration", FormMethod.Post))
{
    <div class="p-5">
        @Html.StepHeader(SharedLocalizer, ResourceConstants.RegistrationNoticeOfTerminationTitle)
        @Html.TextBlock(SharedLocalizer, ResourceConstants.RegistrationNoticeOfTerminationBody)

        <div class="mt-4 mb-5">
            @Html.DatePickerFor(
                SharedLocalizer,
                m => m.EffectiveDateOfTermination,
                Localizer[ResourceConstants.RegistrationNoticeOfTerminationEffectiveDate].Value,
                minDate: null,
                maxDate: DateTimeSvc.GetCurrentDateTime(),
                format: "MM/dd/yyyy",
                isRequired: true,
                isReadOnly: false,
                cssClass: "datepicker-container",
                placeholderResourceKey: "MM/DD/YYYY"
            )
        </div>

        <partial name="_ButtonBar" model="buttonBar" />
    </div>

}
