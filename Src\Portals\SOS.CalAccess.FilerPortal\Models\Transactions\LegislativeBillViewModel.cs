using System.Text.Json.Serialization;
using SOS.CalAccess.FilerPortal.Generated;

namespace SOS.CalAccess.FilerPortal.Models.Transactions;

public class LegislativeBillViewModel
{
    /// <summary>
    /// Gets or sets the bill's ID.
    /// </summary>
    [JsonPropertyName("Id")]
    public long? Id { get; set; }
    /// <summary>
    /// Gets or sets the bill ID.
    /// </summary>
    [JsonPropertyName("BillId")]
    public long? BillId { get; set; }
    /// <summary>
    /// Gets or sets the bill house ID.
    /// </summary>
    [JsonPropertyName("BillHouseId")]
    public long? BillHouseId { get; set; }
    /// <summary>
    /// Gets or sets the bill's number.
    /// </summary>
    [JsonPropertyName("Number")]
    public string? Number { get; set; }
    /// <summary>
    /// Gets or sets the bill's title.
    /// </summary>
    [JsonPropertyName("Title")]
    public string? Title { get; set; }

    /// <summary>
    /// Gets or sets official position id.
    /// </summary>
    [JsonPropertyName("OfficialPositionId")]
    public long? OfficialPositionId { get; set; }

    /// <summary>
    /// Gets or sets official position.
    /// </summary>
    [JsonPropertyName("OfficialPosition")]
    public string? OfficialPosition { get; set; }

    /// <summary>
    /// Gets or sets official position descripition.
    /// </summary>
    [JsonPropertyName("OfficialPositionDescription")]
    public string? OfficialPositionDescription { get; set; }

    public LegislativeBillViewModel() { }

    public LegislativeBillViewModel(ActionsLobbiedResponseDto responseDto)
    {
        Id = responseDto.Id;
        BillId = responseDto.BillId;
        Number = responseDto.BillNumber;
        Title = responseDto.BillTitle;
        OfficialPosition = responseDto.OfficialPositionName;
        OfficialPositionId = responseDto.OfficialPositionId;
    }

    public LegislativeBillViewModel(ActionsLobbied responseDto)
    {
        Id = responseDto.Id;
        BillId = responseDto.BillId;
        Number = responseDto.Bill?.Number;
        Title = responseDto.Bill?.Title;
        OfficialPosition = responseDto.OfficialPosition?.Name;
        OfficialPositionId = responseDto.OfficialPositionId;
    }
}
