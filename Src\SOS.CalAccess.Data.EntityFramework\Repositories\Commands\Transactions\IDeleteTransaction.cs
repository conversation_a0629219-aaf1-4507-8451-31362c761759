// <copyright file="IDeleteTransaction.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>


using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;

/// <summary>
/// Interface for deleting a transaction.
/// </summary>
public interface IDeleteTransaction : ICommand<long, IResult<Transaction>>;

/// <summary>
/// Class for deleting a transaction.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="DeleteTransaction"/> class.
/// </remarks>
/// <param name="db">The database context.</param>
public sealed class DeleteTransaction(DatabaseContext db, IDateTimeSvc dateTimeSvc) : IDeleteTransaction
{
    /// <summary>
    /// Executes the command to delete a transaction.
    /// </summary>
    /// <param name="id">The ID of the transaction to delete.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A result containing the deleted transaction.</returns>
    public async ValueTask<IResult<Transaction>> Execute(
         long id, CancellationToken cancellationToken = default)
    {
        var transaction = await db.Transactions
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);

        if (transaction == null)
        {
            return new Failure<Transaction>.NotFound("Requested target transaction was not found");
        }

        if (transaction.DeletedAt.HasValue)
        {
            return new Failure<Transaction>.InvalidState("Active", "Deleted", "Requested target transaction was already deleted");
        }

        transaction.DeletedAt = dateTimeSvc.GetCurrentDateTime();

        await db.SaveChangesAsync(cancellationToken);

        return new Success<Transaction>(transaction);
    }
}
