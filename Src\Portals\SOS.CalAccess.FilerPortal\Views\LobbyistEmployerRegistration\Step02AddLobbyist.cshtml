 @using SOS.CalAccess.FilerPortal.Models.Localization
 @using SOS.CalAccess.FilerPortal.Models.Transactions
 @inject IHtmlLocalizer<SharedResources> Localizer
 @model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.LobbyistEmployerRegistrationStep02AddLobbyistViewModel


@{
    var localizerRef = (string label) => Localizer["FilerPortal.LobbyistEmployerRegistration.Page03." + label].Value;
    var addNewLobbyistUrl = $"{Model.Id}/Lobbyist/";

    var progressItem1Name = ViewData["ProgressItem1Name"]?.ToString() ?? "";
    var progressItem2Name = ViewData["ProgressItem2Name"]?.ToString() ?? "";
    var progressItem3Name = ViewData["ProgressItem3Name"]?.ToString() ?? "";
    var progressItem4Name = ViewData["ProgressItem4Name"]?.ToString() ?? "";
    var progressItem5Name = ViewData["ProgressItem5Name"]?.ToString() ?? "";

    var progressBar = new ProgressBar(new List<ProgressItem>
    {
        new(progressItem1Name, true, false),
        new(progressItem2Name, false, true),
        new(progressItem3Name, false, false),
        new(progressItem4Name, false, false),
        new(progressItem5Name, false, false),
    });

    var buttonBar = new ButtonBarModel
            {
                LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            ButtonBarModel.DefaultContinue,
        },
                RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", (long?)null),
            },
            ButtonBarModel.DefaultSaveAndClose,
        }
    };

    var firmSearchViewModel = new LobbyistSearchViewModel
    {
        FilerId = 0,
        LobbyistId = Model?.LobbyistId,
        Required = false,
        SearchActionName = "SearchLobbyistByIdOrName",
        SearchDivId = "lobbyistSearch",
        SearchLabelKey = "LobbyistSearch",
        SearchLabelText = "Look up lobbyist by Filer ID# or name",
        SearchInputKey = "LobbyistSearch",
        SearchInputLabel = "Lobbyist Search",
        ContactName = Model?.Contact?.OrganizationName
    };

}
@Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationTitle2)
<h3> @SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationTitle]</h3>
<partial name="_LayoutProgressbar" model="progressBar" />

@using (Html.BeginForm("Step02AddLobbyist", "LobbyistEmployerRegistration", FormMethod.Post))
{
    <div class="d-flex flex-column gap-4 main-form">

        <h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationPage03Title]</h3>
        @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationPage03Body)

        <div class="d-flex flex-row">
            <div class="form-group">
                <partial name="_LobbyistSearch" model="firmSearchViewModel" />
            </div>

            <div class="ps-5 ms-5 border-start border-gray d-flex flex-column">
                <p class="form-label">@localizerRef("EnterInformation")</p>
                <a asp-controller="LobbyistEmployerRegistration" asp-action="@addNewLobbyistUrl" class="btn btn-outline-primary btn">
                        <i class="bi bi-plus-circle-fill"></i>
                        @localizerRef("Add")
                    </a>
            </div>
        </div>

    </div>

    <div class="main-button">
        <partial name="_ButtonBar" model="buttonBar" />
    </div>
}

<style>
    .main-form {
        border-top: 4px solid #397187;
        margin-top: 40px;
        padding: 40px;
    }

    .main-button {
        padding-left: 40px;
    }
</style>
