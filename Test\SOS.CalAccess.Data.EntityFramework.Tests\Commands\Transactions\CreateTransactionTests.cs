// <copyright file="CreateTransactionTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Transactions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Transactions;

/// <summary>
/// Tests for the transactions feature and its operations.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[TestOf(typeof(CreateTransaction))]
[Parallelizable(ParallelScope.All)]
[TestFixture]
public sealed class CreateTransactionTests
{
    private readonly IDecisionsService _decisions = Substitute.For<IDecisionsService>();
    private readonly IAuditService _auditService = Substitute.For<IAuditService>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    /// <summary>
    /// Asserts that our implementation of <see cref="CreateTransaction"/> will
    /// correctly report a dependency fail if the referenced contact is not available.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task CreateTransaction_FailsWithDependencyFailed_WhenContactDoesNotExist()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _ = await context.PrepareTransactionsData();

        var commandProcessor = new CreateTransaction(context, _decisions, _auditService, _dateTimeSvc);
        var command = new CreateExpenditureCommand(TransactionLike.Dummy, "Testing");

        var result = await commandProcessor.Execute(command);

        Assert.That(result, Is.AssignableFrom(typeof(Failure<Transaction>.DependencyFailed)));
    }

    /// <summary>
    /// Asserts that our implementation of <see cref="CreateTransaction"/> will
    /// correctly create a new transaction if the referenced contact is available.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task CreateTransaction_Succeeds_WhenContactIsAvailable()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var data = (await context.PrepareTransactionsData())[0];

        var commandProcessor = new CreateTransaction(context, _decisions, _auditService, _dateTimeSvc);
        var command = new CreateExpenditureCommand(
            TransactionLike.Dummy with { FilerId = data.FilerId, ContactId = data.ContactId }, "Testing");

        var result = await commandProcessor.Execute(command);

        Assert.That(result, Is.AssignableFrom(typeof(Success<Transaction>)));
    }
}
