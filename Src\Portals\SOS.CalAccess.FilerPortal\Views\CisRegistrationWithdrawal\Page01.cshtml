@using System.Text.Json
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal
@using SOS.CalAccess.Models.Common
@using SOS.CalAccess.Models.FilerRegistration.Registrations
@using SOS.CalAccess.UI.Common.Models
@using SOS.CalAccess.UI.Common.Enums
@inject IHtmlLocalizer<SharedResources> Localizer

@model CisRegistrationWithdrawalPage01ViewModel
@{
    var progressItem1Name = ViewData["ProgressItem1Name"]?.ToString() ?? "";
    var progressItem2Name = ViewData["ProgressItem2Name"]?.ToString() ?? "";
    var progressItems = new List<ProgressItem>
    {
        new ProgressItem(progressItem1Name, false, true),
        new ProgressItem(progressItem2Name, false, false)
    };
    var progressBar = new ProgressBar(progressItems);

    var details = new List<DetailTableRecord>
    {
        new()
        {
            Label = Html.StringHtml(Localizer, ResourceConstants.CandidateName),
            Value = Html.StringHtml(Model.Name),
        },
        new()
        {
            Label = Html.StringHtml(Localizer, ResourceConstants.CandidateEmail),
            Value = Html.StringHtml(Model.Email),
        },
        new()
        {
            Label = Html.StringHtml(Localizer, ResourceConstants.CandidateAddress),
            Value = Html.AddressDisplay(Localizer, Model.Address ?? new(), AddressDisplayMode.Vertical)
        },
        new()
        {
            Label = Html.StringHtml(Localizer, ResourceConstants.OfficeSought),
            Value = Html.StringHtml(Model.Office),
        },
        new()
        {
            Label = Html.StringHtml(Localizer, ResourceConstants.District),
            Value = Html.StringHtml(Localizer, string.IsNullOrWhiteSpace(Model.District) ? Model.District : ResourceConstants.NotApplicable),
        },
        new()
        {
            Label = Html.StringHtml(Localizer, ResourceConstants.ElectionYear),
            Value = Html.StringHtml(Model.ElectionYear.ToString()),
        },
    };

    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultContinue
        },
        RightButtons = new List<ButtonConfig>
        {
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", Model.Id)
            },
            ButtonBarModel.DefaultSaveAndClose,
        }
    };
}
<partial name="_LayoutProgressbar" model="progressBar" />

@Html.StepHeader(SharedLocalizer, ResourceConstants.WithdrawalCisPage01Header)

@Html.TextBlock(SharedLocalizer, ResourceConstants.WithdrawalCisPage01Body)

@using (Html.BeginForm("Page01", "CisRegistrationWithdrawal", FormMethod.Post))
{
    @Html.DetailTable(Localizer, details)

    <partial name="_ButtonBar" model="buttonBar" />
}
