@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration;
@using Microsoft.AspNetCore.Html;
@using SOS.CalAccess.UI.Common.Enums;
@using SOS.CalAccess.UI.Common.Localization;
@inject IHtmlLocalizer<SharedResources> Localizer
@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration.LobbyistVerificationViewModel;
@{
    var isLobbyistWithdrawal = Model.Type == RegistrationConstants.RegistrationType.LobbyistWithdrawal;
    var titleRegistration = isLobbyistWithdrawal ? ResourceConstants.WithdrawRegistration : ResourceConstants.TerminationRegistration;
    var viewDataItem1 = isLobbyistWithdrawal ? "WithdrawRegistrationStep1" : "TerminationProgressItem1Name";
    var viewDataItem2 = isLobbyistWithdrawal ? "WithdrawRegistrationStep2" : "TerminationProgressItem2Name";
    var progressItems = new List<ProgressItem>
    {
        new ProgressItem(ViewData[viewDataItem1]?.ToString() ?? "", true, false),
        new ProgressItem(ViewData[viewDataItem2]?.ToString() ?? "", false, true),
    };
    var progressBar = new ProgressBar(progressItems);

    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            new()
            {
                Type = ButtonType.Button,
                Action = FormAction.Continue,
                CssClass = "btn btn-primary me-2",
                CssId = Model.IsUserAuthorizedToAttest ? "submitButton" : "",
                InnerTextKey = Model.IsUserAuthorizedToAttest
                    ? ResourceConstants.Attest
                    : ResourceConstants.SendForAttestation,
            },
        },
        RightButtons = new List<ButtonConfig>
        {
            new()
            {
                Type = ButtonType.Custom,
                Action = SOS.CalAccess.UI.Common.Enums.FormAction.Cancel,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", Model.Id),
            },
            new()
            {
                Type = ButtonType.Button,
                Action = FormAction.Close,
                InnerTextKey = CommonResourceConstants.Close,
                CssClass = "btn btn-outline-primary",
            }
        }
    };
}
@Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistRegistration02Lobbyist)
<h3> @SharedLocalizer[titleRegistration]</h3>
<partial name="_LayoutProgressbar" model="progressBar"/>

@using (Html.BeginForm("Verification", "LobbyistRegistration", FormMethod.Post))
{
    <div class="d-flex flex-column gap-4 main-form">
        <h3>@SharedLocalizer[ResourceConstants.LobbyistRegistrationTerminationVerificationTitle]</h3>
        @if (Model.IsUserAuthorizedToAttest)
        {
            @if (!isLobbyistWithdrawal)
            {
                <div class="form-check">
                    @Html.CheckBoxFor(m => m.IsVerificationCertified, new { @class = "form-check-input", id = "certifyCheckbox" })
                    <label class="form-check-label mb-2" for="reasonableCheckbox">
                        @Localizer[ResourceConstants.LobbyistRegistrationTerminationVerificationBody01].Value
                    </label>
                </div>
                <div class="form-check">
                    @Html.CheckBoxFor(m => m.IsAgreementAccepted, new { @class = "form-check-input", id = "understandCheckbox" })
                    <label class="form-check-label mb-2" for="qualificationCheckbox">
                        @Localizer[ResourceConstants.LobbyistRegistrationTerminationVerificationBody02].Value
                    </label>
                </div>
            }

            <div class="form-check">
                @Html.CheckBoxFor(m => m.IsRequiredOtherAcknowledgements, new { @class = "form-check-input", id = "agreementCheckbox" })
                <label class="form-check-label mb-2" for="qualificationCheckbox">
                    @Localizer[ResourceConstants.LobbyistRegistrationTerminationVerificationBody03].Value
                </label>
            </div>

            @if (isLobbyistWithdrawal)
            {
                <div class="form-check">
                    @Html.CheckBoxFor(m => m.IsNotMetQualificationRequirements, new { @class = "form-check-input", id = "requirementCheckbox" })
                    <label class="form-check-label mb-2" for="qualificationCheckbox">
                        @Localizer[ResourceConstants.LobbyistRegistrationWithdrawalVerificationBody01].Value
                    </label>
                </div>
            }

            <div class="d-flex p-2 align-items-center justify-content-between border-top">
                <span class="fw-bold">@Localizer[ResourceConstants.SmoRegistration15VerificationName].Value</span>
                <span>@Model.FirstName @Model.LastName</span>
            </div>
            <div class="d-flex p-2 border-top border-bottom justify-content-between align-items-center">
                <span class="fw-bold">@Localizer[ResourceConstants.SmoRegistration15VerificationExecutedOn].Value</span>
                <span>@Model.ExecutedOn?.ToString("MM/dd/yyyy")</span>
            </div>
            <div class="pdf-preview">
                <a href="#" class="btn btn-link mb-5 d-flex align-items-center" style="text-decoration: none">
                    <span class="e-icons e-export-pdf e-large"></span>
                    <span class="ms-2">@Localizer[ResourceConstants.PreviewPdf].Value</span>
                </a>
            </div>
        }
        else
        {
            @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistRegistrationTerminationVerificationDescription)
            <p>@SharedLocalizer["FilerPortal.Disclosure.SendForAttest.OfficerMustBeSelected"].Value</p>

            <div>
                @if (isLobbyistWithdrawal)
                {
                    @Html.TextFieldFor(Localizer, m => m.FirstName, ResourceConstants.SendForAttestationResponsibleOfficer, true, "", null, true)
                }
                else
                {
                    @Html.TextFieldFor(Localizer, m => m.Email, ResourceConstants.LobbyistRegistrationTerminationVerificationEmail, true, "", null, true)
                }
                <a class="btn btn-flat-primary btn-sm mb-5">
                    <i class="fas fa-file-pdf mr-2"></i> @Localizer["FilerPortal.Filing.Verification.PreviewPDF"]
                </a>
            </div>
        }
        @Html.HiddenFor(m => m.IsUserAuthorizedToAttest)
        @Html.HiddenFor(m => m.LegislativeSessionId)
        @Html.HiddenFor(m => m.EffectiveDateOfTermination)
        @Html.HiddenFor(m => m.ExecutedOn)
        @Html.HiddenFor(m => m.Type)
        @Html.HiddenFor(m => m.EffectiveDateOfWithdrawal)

        @Html.HiddenFor(m => m.Email)
    </div>
    <div class="d-flex flex-column display-content mb-4">
        @Html.ValidationSummary(true, null, new { @class = "text-danger" })
    </div>

    <div class="display-content">
        <partial name="_ButtonBar" model="buttonBar" />
    </div>
}
<script>
    {
        var type = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model.Type));
        var certifyCheckbox = document.getElementById("certifyCheckbox");
        var understandCheckbox = document.getElementById("understandCheckbox");
        var agreementCheckbox = document.getElementById("agreementCheckbox");
        var requirementCheckbox = document.getElementById("requirementCheckbox");

        var submitButton = document.getElementById("submitButton");

        submitButton.disabled = true;

        function updateButtonState() {
            var allChecked = false;


            if (type === "LobbyistWithdrawal") {

                allChecked = agreementCheckbox.checked &&
                    requirementCheckbox.checked
            } else {

                allChecked = certifyCheckbox.checked &&
                    understandCheckbox.checked &&
                    agreementCheckbox.checked
            }

            if (allChecked) {
                submitButton.removeAttribute("disabled");
            } else {
                submitButton.setAttribute("disabled", "");
            }
        }

        // Check all checkbox of Lobbyist Withdraw
        // else check all checkbox of Lobbyist Termination
        certifyCheckbox && certifyCheckbox.addEventListener("change", updateButtonState);
        understandCheckbox && understandCheckbox.addEventListener("change", updateButtonState);
        agreementCheckbox && agreementCheckbox.addEventListener("change", updateButtonState);
        requirementCheckbox && requirementCheckbox.addEventListener("change", updateButtonState);
    }
</script>

<style>
    .main-form {
        width: 60%;
        margin: auto;
        margin-top: 80px;
    }

    .display-content {
        width: 60%;
        margin: auto;
    }

    .btn-flat-primary
    {
        width:120px;
    }
</style>
