using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Moq;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.FilerPortal.ViewHelpers;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

public class StaticClassLocalize
{
    public virtual string Localize(HttpContext a, string b) => LocalizationHelper.GetLocalizedLabel(a, b);
}

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(CandidateRegistrationController))]
internal sealed class CandidateRegistrationControllerTests : IDisposable
{
    private IAuthorizationSvc _authorizationSvc;
    private Mock<ICandidateIntentionRegistrationSvc> _candidateIntentionRegistrationSvc;
    private ICandidateRegistrationCtlSvc _candidateRegistrationCtlSvc;
    private ICandidateSvc _candidatSvc;
    private IElectionSvc _electionSvc;
    private IReferenceDataSvc _referenceDataSvc;
    private IStringLocalizer<SharedResources> _localizer;
    private IAccuMailValidatorService _accuMailValidatorService;
    private CandidateRegistrationController _controller;
    private IDateTimeSvc _dateTimeSvcMock;
    private DateTime DateNow;

    [SetUp]
    public void Setup()
    {
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _candidateIntentionRegistrationSvc = new Mock<ICandidateIntentionRegistrationSvc>();
        _candidateRegistrationCtlSvc = Substitute.For<ICandidateRegistrationCtlSvc>();
        _candidatSvc = Substitute.For<ICandidateSvc>();
        _electionSvc = Substitute.For<IElectionSvc>();
        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _accuMailValidatorService = Substitute.For<IAccuMailValidatorService>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        _controller = new CandidateRegistrationController(
            _authorizationSvc,
            _candidateIntentionRegistrationSvc.Object,
            _candidateRegistrationCtlSvc,
            _candidatSvc,
            _electionSvc,
            _referenceDataSvc,
            _dateTimeSvcMock,
            _localizer);

        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;
        DateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _localizer[Arg.Any<string>()].Returns(p => new LocalizedString((string)p[0], (string)p[0]));
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #region Page03
    [Test]
    public async Task Page03_ShouldRedirectToPage04WithPrefillDataOnContinue()
    {
        // Arrange
        var model = new CandidateIntentStep1
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        var vm = new CandidateIntentionStatementViewModel
        {
            Id = 1,
        };

        _candidateRegistrationCtlSvc.Page03Submit(model, _controller.ModelState, default, true)
            .Returns(vm);

        // Act
        var result = await _controller.Page03(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result?.Model, Is.Not.Null);
        var m = result.Model as CandidateIntentionStatementViewModel;
        Assert.That(m?.Id, Is.EqualTo(1));
    }

    [Test]
    public async Task Page03_ShouldReturnViewWhenModelStateInvalidOnContinue()
    {
        // Arrange
        var model = new CandidateIntentStep1
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        var vm = new CandidateIntentionStatementViewModel
        {
            Id = 1,
        };

        _candidateRegistrationCtlSvc.Page03Submit(model, _controller.ModelState, default, true)
            .Returns(vm);

        // Act
        var result = await _controller.Page03(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());

    }

    [Test]
    public async Task Page03_ShouldReturnPage04OnContinueWithNoPrefillData()
    {
        // Arrange
        var model = new CandidateIntentStep1
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        var vm = new CandidateIntentionStatementViewModel
        {
            Id = 1,
        };

        _candidateRegistrationCtlSvc.Page03Submit(model, _controller.ModelState, default, true)
            .Returns(Task.FromResult<CandidateIntentionStatementViewModel?>(null));

        // Act
        var result = await _controller.Page03(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result.Model, Is.InstanceOf<CandidateIntentionStatementViewModel>());
    }

    [Test]
    public async Task Page03_ShouldReturnViewWhenNoActionMatch()
    {
        // Arrange
        var model = new CandidateIntentStep1
        {
            Action = FormAction.Cancel,
            Id = 1,
        };

        // Act
        var result = await _controller.Page03(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result.Model, Is.InstanceOf<CandidateIntentStep1>());
    }

    [Test]
    public async Task Page03_ShouldRedirectToPage02OnPrevious()
    {
        // Arrange
        var model = new CandidateIntentStep1
        {
            Action = FormAction.Previous,
            Id = 1,
        };

        // Act
        var result = await _controller.Page03(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Page02"));
        });
    }
    #endregion

    #region Page04
    [Test]
    public async Task Page04_ShouldRemoveMailingAddressWhenSameAsIsTrue()
    {
        // Arrange
        var model = new CandidateIntentionStatementViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
            Addresses = new()
            {
                new()
                {
                    Purpose = "Candidate",
                    Street = "Street",
                },
                new()
                {
                    Purpose = "Mailing",
                    Street = "Street",
                }
            },
            IsSameAsCandidateAddress = true,
        };
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(true);

        // Act
        var result = await _controller.Page04(model, _accuMailValidatorService) as ViewResult;

        // Assert
        var arg0 = _accuMailValidatorService.ReceivedCalls().ToList()[0].GetArguments()[0] as CandidateIntentionStatementViewModel;
        Assert.That(arg0, Is.Not.Null);
        Assert.That(arg0.Addresses, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate"), Is.Not.Null);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing"), Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate").IsEmpty(), Is.False);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing").IsEmpty(), Is.True);
        });
    }
    [Test]
    public async Task Page04_ShouldNotRemoveMailingAddressWhenSameAsIsTrue()
    {
        // Arrange
        var model = new CandidateIntentionStatementViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
            Addresses = new()
            {
                new()
                {
                    Purpose = "Candidate",
                    Street = "Street",
                },
                new()
                {
                    Purpose = "Mailing",
                    Street = "Street",
                }
            },
            IsSameAsCandidateAddress = false,
        };
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(true);

        // Act
        var result = await _controller.Page04(model, _accuMailValidatorService) as ViewResult;

        // Assert
        var arg0 = _accuMailValidatorService.ReceivedCalls().ToList()[0].GetArguments()[0] as CandidateIntentionStatementViewModel;
        Assert.That(arg0, Is.Not.Null);
        Assert.That(arg0.Addresses, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate"), Is.Not.Null);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing"), Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate").IsEmpty(), Is.False);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing").IsEmpty(), Is.False);
        });
    }

    [Test]
    public async Task Page04_ShouldReturnViewWhenNoActionMatch()
    {
        // Arrange
        var model = new CandidateIntentionStatementViewModel
        {
            Action = FormAction.Cancel,
            Id = 1,
        };
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(false);

        // Act
        var result = await _controller.Page04(model, _accuMailValidatorService) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result.Model, Is.InstanceOf<CandidateIntentionStatementViewModel>());
    }

    [Test]
    public async Task Page04_ShouldReturnViewWhenModelStateInvalidOnSaveAndClose()
    {
        // Arrange
        var model = new CandidateIntentionStatementViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _controller.ModelState.AddModelError("Key", "Test");
        _candidateRegistrationCtlSvc
            .Page04Submit(model, _controller.ModelState, default, true)
            .Returns(1);
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(false);

        // Act
        var result = await _controller.Page04(model, _accuMailValidatorService) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.InstanceOf<CandidateIntentionStatementViewModel>());
            Assert.That(result.ViewName, Is.EqualTo("Page04"));
        });
    }

    [Test]
    public async Task Page04_ShouldRedirectToPage05WhenModelStateValidOnContinue()
    {
        // Arrange
        var model = new CandidateIntentionStatementViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        _candidateRegistrationCtlSvc
            .Page04Submit(model, _controller.ModelState, default, true)
            .Returns(1);
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(false);

        // Act
        var result = await _controller.Page04(model, _accuMailValidatorService) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page05"));
            Assert.That(result.RouteValues?["Id"], Is.EqualTo(1));
        });
    }

    [Test]
    public async Task Page04_ShouldReturnViewWhenModelStateInvalidOnContinue()
    {
        // Arrange
        var model = new CandidateIntentionStatementViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        _controller.ModelState.AddModelError("Key", "Test");
        _candidateRegistrationCtlSvc
            .Page04Submit(model, _controller.ModelState, default, true)
            .Returns(1);
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(false);

        // Act
        var result = await _controller.Page04(model, _accuMailValidatorService, default) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.InstanceOf<CandidateIntentionStatementViewModel>());
            Assert.That(result.ViewName, Is.EqualTo("Page04"));
        });
    }

    [Test]
    public async Task Page04_ShouldRedirectToPage03OnPrevious()
    {
        // Arrange
        var model = new CandidateIntentionStatementViewModel
        {
            Action = FormAction.Previous,
            Id = 1,
        };
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(false);

        // Act
        var result = await _controller.Page04(model, _accuMailValidatorService) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Page03"));
            Assert.That(result?.RouteValues?["Id"], Is.EqualTo(1));
        });
    }
    #endregion

    #region Page05
    [Test]
    public async Task Page05_Get_ShouldReturnView()
    {
        // Arrange
        long id = 1;

        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page05_Get_ShouldReturnViewWithTempData()
    {
        // Arrange
        const long id = 0;

        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page05_Get_ShouldReturnViewWithTempDataId()
    {
        // Arrange
        const long id = 0;
        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t.Peek("Id")).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var model = result.Model as CandidateIntentStep2;
        Assert.That(model, Is.Not.Null);
    }

    [Test]
    public async Task Page05_Get_ShouldReturnViewWithInvalidId()
    {
        // Arrange
        const long id = -1;
        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["NotId"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var model = result.Model as CandidateIntentStep2;
        Assert.That(model, Is.Not.Null);
    }

    [Test]
    public async Task Page05_Post_ShouldRedirectToPage06OnContinue()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Page06"));
            Assert.That(result?.RouteValues?["Id"], Is.EqualTo(1));
        });
    }

    [Test]
    public async Task Page05_Post_ShouldRedirectToDashboardOnSaveAndClose()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };

        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public async Task Page05_Post_ShouldRedirectToDashboardOnSaveAndCloseWithTempData()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.SaveAndClose,
            Id = 0,
        };

        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
        });
    }

    [Test]
    public async Task Page05_Post_ShouldRedirectToPage04OnPrevious()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.Previous,
            Id = 1,
        };
        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Page04"));
            Assert.That(result?.RouteValues?["Id"], Is.EqualTo(1));
        });
    }

    [Test]
    public async Task Page05_Post_ShouldReturnViewOnNoActionMatch()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = null,
            Id = 1,
        };
        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ContainsKey(string.Empty), Is.True);
        });
    }

    [Test]
    public async Task Page05_Post_ShouldReturnViewOnModelStateInvalid()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        _controller.ModelState.AddModelError("Key", "Error");
        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page05(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page06_Get
    [Test]
    public async Task Page04_Get_ShouldReturnViewWhenModelStateInvalid()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = await _controller.Page06(id, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page06_Post
    [Test]
    public async Task Page06_Post_ShouldReturnNotFoundWhenIdMissing()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Id = null,
        };

        // Act
        var result = await _controller.Page06(model, default);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page06_Post_ShouldRedirectToDashboardOnSaveAndClose()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _candidateRegistrationCtlSvc
            .Election02Submit(0, new CandidateIntentStep2 { Id = 1 }, _controller.ModelState, true)
            .Returns(Task.FromResult(default(object)));

        // Act
        var result = await _controller.Page06(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
    }
    [Test]
    public async Task Page06_Post_ShouldReturnViewOnSaveAndCloseWhenModelStateInvalid()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _candidateRegistrationCtlSvc
            .Election02Submit(Arg.Any<long>(), Arg.Any<CandidateIntentStep2>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)))
            .AndDoes(x => _controller.ModelState.AddModelError("Key", "Error"));

        // Act
        var result = await _controller.Page06(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ContainsKey("Key"), Is.True);
        });
    }
    [Test]
    public async Task Page06_Post_ShouldRedirectToPage07OnContinue()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.Continue,
            Id = 1,
        };

        _candidateRegistrationCtlSvc
            .Election02Submit(Arg.Any<long>(), Arg.Any<CandidateIntentStep2>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)));

        // Act
        var result = await _controller.Page06(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page07"));
    }
    [Test]
    public async Task Page06_Post_ShouldReturnViewOnContinueWhenModelStateInvalid()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.Continue,
            Id = 1,
        };

        _candidateRegistrationCtlSvc
            .Election02Submit(Arg.Any<long>(), Arg.Any<CandidateIntentStep2>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)))
            .AndDoes(x => _controller.ModelState.AddModelError("Key", "Error"));

        // Act
        var result = await _controller.Page06(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ContainsKey("Key"), Is.True);
        });
    }
    [Test]
    public async Task Page06_Post_ShouldRedirectToPage05OnPrevious()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = FormAction.Previous,
            Id = 1,
        };

        // Act
        var result = await _controller.Page06(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page05"));
    }
    [Test]
    public async Task Page06_Post_ShouldReturnViewWhenNoActionMatch()
    {
        // Arrange
        var model = new CandidateIntentStep2
        {
            Action = null,
            Id = 1,
        };

        // Act
        var result = await _controller.Page06(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page07
    [Test]
    public async Task Page07_ShouldRedirectToPage08WhenExpAmtIs0()
    {
        // Arrange
        decimal expenditureAmount = 0.00M;
        string referer = "http://localhost/CandidateRegistration/Page06/1";
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(It.IsAny<long>()))
            .ReturnsAsync(expenditureAmount);

        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers.Append("Referer", referer);
        _controller.ControllerContext.HttpContext = httpContext;

        // Act
        var result = await _controller.Page07(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page08"));
    }

    [Test]
    public async Task Page07_ShouldRedirectToPage06WhenExpAmtIs0RefIsPage08()
    {
        // Arrange
        decimal expenditureAmount = 0.00M;
        string referer = "http://localhost/CandidateRegistration/Page08/1";

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(It.IsAny<long>()))
            .ReturnsAsync(expenditureAmount);
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers.Append("Referer", referer);
        _controller.ControllerContext.HttpContext = httpContext;

        // Act
        var result = await _controller.Page07(1) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page06"));
    }

    [Test]
    public async Task Page07_ShouldReturnViewModelWithBlankStatement()
    {
        // Arrange
        decimal expenditureAmount = 1.00M;
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 0.00M,
            ExpenditureExceeded = false,
            ExpenditureLimitAccepted = false,
        };
        var election = new Election
        {
            Name = "Test",
            ElectionDate = DateNow,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(It.IsAny<long>()))
            .ReturnsAsync(expenditureAmount);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Page07(1) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var m = result.Model as CandidateIntentionStatementViewModel;
        Assert.That(m, Is.InstanceOf<CandidateIntentionStatementViewModel>());
        Assert.That(m.ExpenditureLimitStatementAcceptance, Is.EqualTo(""));
    }

    [Test]
    public async Task Page07_ShouldReturnViewModelWithSavedChoiceTrue()
    {
        // Arrange
        decimal expenditureAmount = 1.00M;
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 1.00M,
            ExpenditureLimitAccepted = true,
        };
        var election = new Election
        {
            Name = "Test",
            ElectionDate = DateNow,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(It.IsAny<long>()))
            .ReturnsAsync(expenditureAmount);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(It.IsAny<long>()))
            .ReturnsAsync(expenditureAmount);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementElection(It.IsAny<int>()))
            .ReturnsAsync(election);

        // Act
        var result = await _controller.Page07(1) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var m = result.Model as CandidateIntentionStatementViewModel;
        Assert.That(m, Is.InstanceOf<CandidateIntentionStatementViewModel>());
        Assert.That(m.ExpenditureLimitStatementAcceptance, Is.EqualTo("true"));
    }

    [Test]
    public async Task Page07_ShouldReturnViewModelWithSavedChoiceFalse()
    {
        // Arrange
        decimal expenditureAmount = 1.00M;
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 1.00M,
            ExpenditureLimitAccepted = false,
        };
        var election = new Election
        {
            Name = "Test",
            ElectionDate = DateNow,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(It.IsAny<long>()))
            .ReturnsAsync(expenditureAmount);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(It.IsAny<long>()))
            .ReturnsAsync(expenditureAmount);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementElection(It.IsAny<int>()))
            .ReturnsAsync(election);

        // Act
        var result = await _controller.Page07(1) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var m = result.Model as CandidateIntentionStatementViewModel;
        Assert.That(m, Is.InstanceOf<CandidateIntentionStatementViewModel>());
        Assert.That(m.ExpenditureLimitStatementAcceptance, Is.EqualTo("false"));
    }
    #endregion

    #region Expenditure01
    [Test]
    public async Task Expenditure01_Post_Invalid()
    {
        var model = new CandidateIntentionStatementViewModel
        {
            RegistrationId = 1,
            ExpenditureLimitStatementAcceptance = "true"
        };
        var request = new CandidateIntentionStatementExpenditureLimitRequest
        {
            ExpenditureLimitAccepted = model.ExpenditureLimitStatementAcceptance == "true",
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.UpdateCandidateIntentionStatementExpenditureLimit(It.IsAny<int>(), It.IsAny<CandidateIntentionStatementExpenditureLimitRequest>()))
            .ReturnsAsync(new RegistrationResponseDto());

        var result = await _controller.Expenditure01Post(model) as RedirectToActionResult;
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result?.ActionName, Is.EqualTo("Page08"));

    }
    #endregion

    #region Page08
    [Test]
    public async Task Page08_Get_ShouldReturnNotFoundWhenModelStateInvalid()
    {
        // Arrange
        var registration = new CandidateIntentionStatementResponseDto
        {
            Id = 1
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        _controller.ModelState.AddModelError("Key", "Test");

        // Act
        var result = await _controller.Page08(1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page08_Get_ShouldReturnNotFoundWhenNullRecord()
    {
        // Arrange
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
        .ReturnsAsync((CandidateIntentionStatementResponseDto?)null);

        // Act
        var result = await _controller.Page08(1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page08_Get_ShouldReturnView()
    {
        // Arrange
        var registration = new CandidateIntentionStatementResponseDto
        {
            Id = 1,
            ElectionRaceId = 1
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Page08(1);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page08_Post_ShouldViewWhenModelStateInvalid()
    {
        // Arrange
        var model = new VerificationViewModel
        {
            RegistrationId = 1,
        };
        _controller.ModelState.AddModelError("Key", "Test");
        var candidateIntentStep2 = new CandidateIntentStep2()
        {
            RegistrationId = 1,
            SelectedElection = 2025,
            SelectedJurisdiction = "state",
            SelectedElectionYear = "2025",
            SelectedOffice = 10,
            SelectedDistrict = 5,
            SelectedPartyAffiliation = 2
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 0.00M,
            ExpenditureExceeded = false,
            ExpenditureLimitAccepted = false,
            ElectionRaceId = 1,
            MailingAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            },
            CandidateAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            }
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page08(model, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page08_Post_ShouldRedirectToDashboardOnSaveAndClose()
    {
        // Arrange
        var model = new VerificationViewModel
        {
            RegistrationId = 1,
            Action = FormAction.SaveAndClose,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page08(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
    }
    [Test]
    public async Task Page08_Post_ShouldRedirectToPage07OnPrevious()
    {
        // Arrange
        var model = new VerificationViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Previous,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page08(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result?.ActionName, Is.EqualTo("Page07"));
    }
    [Test]

    public async Task Page08_ShouldRedirectToSubmissionOnPost()
    {
        // Arrange
        var model = new VerificationViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Submit,
            IsSubmission = true,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()

        };
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 0.00M,
            ExpenditureExceeded = false,
            ExpenditureLimitAccepted = false,
            ElectionRaceId = 1,
            MailingAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            },
            CandidateAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            }
        };
        // Authorized to attest
        _authorizationSvc.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(true);

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page08(model, default) as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Submission"));
    }
    [Test]
    public async Task Page08_ShouldRedirectToAttestationRequestSentOnPost()
    {
        // Arrange
        var model = new VerificationViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Submit,
            IsSubmission = false,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        // Not authorized to attest
        _authorizationSvc.IsAuthorized(Arg.Any<AuthorizationRequest>()).Returns(false);

        _candidateIntentionRegistrationSvc.Setup(s => s.SendForAttestation(It.IsAny<long>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page08(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("AttestationRequestSent"));
    }
    [Test]
    public async Task Page08_ShouldReturnNotFoundWhenMissingIdOnPost()
    {
        // Arrange
        var model = new VerificationViewModel
        {
            Id = null,
            Action = FormAction.Submit,
            IsSubmission = true,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page08(model, default);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    #endregion

    #region Submission
    [Test]
    public void Submission_RegistrationIdInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("key", "error");
        var id = 1;
        var model = new VerificationViewModel
        {
            RegistrationId = 0,
            IsSubmission = false,
            CandidateName = "John Doe",
            ExecutedOn = DateTime.Today
        };
        var result = _controller.Submission(id);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public void Submission_ModelStateValid_CallsSubmitCandidateIntentionStatement_AndReturnsView()
    {
        var id = 1;
        var model = new ConfirmationViewModel
        {
            Id = 1,
            ExecutedOn = DateNow
        };
        var result = _controller.Submission(id) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.ViewName, Is.EqualTo("Page09"));
            Assert.That(model.ExecutedOn, Is.LessThanOrEqualTo(DateNow));

        });
    }
    [Test]
    public async Task Submission_InvalidModel_ReturnsPage09View()
    {
        _controller.ModelState.AddModelError("key", "error");
        var model = new ConfirmationViewModel();

        var result = await _controller.Submission(1, model) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result?.ViewName, Is.EqualTo("Page09"));
            Assert.That(result?.Model, Is.SameAs(model));
        });
    }
    [Test]
    public async Task Submission_Continue_AnswerYes_RedirectsToLinkCommitteePage01()
    {
        var model = new ConfirmationViewModel
        {
            Action = FormAction.Continue,
            AnticipatesSpendingOrReceivingOverXXXX = "yes"
        };
        var registration = new CandidateIntentionStatementResponseDto();
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        var result = await _controller.Submission(123, model) as RedirectToActionResult;

        Assert.That(result?.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Page01"));
            Assert.That(result?.ControllerName, Is.EqualTo("LinkCommittee"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(123));
        });
    }

    [Test]
    public async Task Submission_Continue_AnswerNo_RedirectsToForm470()
    {
        var model = new ConfirmationViewModel
        {
            Action = FormAction.Continue,
            AnticipatesSpendingOrReceivingOverXXXX = "no"
        };
        var registration = new CandidateIntentionStatementResponseDto
        {
            FilerId = 1
        };
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        var result = await _controller.Submission(123, model) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Form470"));
        });
    }

    [Test]
    public async Task Submission_Close_RedirectsToDashboard()
    {
        var model = new ConfirmationViewModel
        {
            Action = FormAction.Close
        };
        var registration = new CandidateIntentionStatementResponseDto();
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        var result = await _controller.Submission(123, model) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }

    [Test]
    public async Task Submission_UnknownAction_RedirectsToSubmission()
    {
        var model = new ConfirmationViewModel
        {
            Action = (FormAction)999
        };
        var registration = new CandidateIntentionStatementResponseDto();
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        var result = await _controller.Submission(123, model) as RedirectToActionResult;

        Assert.That(result?.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Submission"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(123));
        });
    }
    #endregion

    #region AttestationRequestSent
    [Test]
    public void AttestationRequestSent_RegistrationIdInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("key", "error");
        var id = 1;
        var model = new ConfirmationViewModel
        {
            ExecutedOn = DateTime.Today
        };
        var result = _controller.AttestationRequestSent(id);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public void AttestationRequestSent_ModelStateValid_CallsSubmitCandidateIntentionStatement_AndReturnsView()
    {
        var id = 1;
        var model = new ConfirmationViewModel
        {
            ExecutedOn = DateNow,
            PendingItems = new List<PendingItemSharedViewModel>()
        };
        var result = _controller.AttestationRequestSent(id) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.ViewName, Is.EqualTo("Page09"));
            Assert.That(model.ExecutedOn, Is.LessThanOrEqualTo(DateNow));
        });
    }
    [Test]
    public Task AttestationRequestSent_Get_ShouldReturnNotFoundWhenModelStateInvalid()
    {
        _controller.ModelState.AddModelError("Key", "Test");

        // Act
        var result = _controller.Close();

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
        return Task.CompletedTask;
    }
    #endregion

    #region GetElectionsForYear
    [Test]
    public async Task GetElectionsForYear_ValidResponse_Success()
    {
        // Arrange
        var elections = new List<Election>
        {
            new()
            {
                Name = "Name",
                ElectionDate = DateTime.Today,
                ElectionType = new ElectionType
                {
                    Id = 3002,
                    Name = "Primary"
                }
            },
        };

        // Act
        _electionSvc.GetAllElections().Returns(elections);

        var result = await _controller.GetElectionsForYear(DateTime.Today.Year, CancellationToken.None);
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<JsonResult>());
        });
    }
    #endregion
    #region GetOfficesForElection
    [Test]
    public async Task GetOfficesForElection_ValidResponse_Success()
    {
        // Arrange
        var offices = new List<Office>
        {
            new()
            {
                Id = 1,
                Name = "Name",
            },
        };

        // Act
        _electionSvc.GetOfficesForElection(Arg.Any<long>()).Returns(offices);

        var result = await _controller.GetOfficesForElection(1, CancellationToken.None);
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<JsonResult>());
        });
    }
    #endregion
    #region GetDistrictsForOffice
    [Test]
    public async Task GetDistrictsForOffice_ValidResponse_Success()
    {
        // Arrange
        var districts = new List<District>
        {
            new()
            {
                Id = 1,
                Name = "Name",
            },
        };

        // Act
        _electionSvc.GetDistrictsForElectionAndOffice(Arg.Any<long>(), Arg.Any<long>()).Returns(districts);

        var result = await _controller.GetDistrictsForOffice(1, 1, CancellationToken.None);
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<JsonResult>());
        });
    }
    #endregion
    #region GetPartiesForOffice
    [Test]
    public async Task GetPartiesForOffice_ValidResponse_Success()
    {
        // Arrange
        var offices = new Office
        {
            Id = 1,
            Name = "Name",
        };

        var politicalParties = new List<PoliticalParty>
        {
            new()
            {
                Id = 1,
                Name = "Name",
            },
        };

        // Act
        _referenceDataSvc.GetOffice(Arg.Any<long>()).Returns(offices);
        _referenceDataSvc.GetAllPoliticalParties().Returns(politicalParties);

        var result = await _controller.GetPartiesForOffice(1, CancellationToken.None);
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<JsonResult>());
        });
    }
    #endregion
    public void Dispose()
    {
        _controller.Dispose();
    }
}
