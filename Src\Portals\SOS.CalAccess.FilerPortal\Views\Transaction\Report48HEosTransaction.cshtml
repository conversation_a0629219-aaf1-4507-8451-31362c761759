@using System.Text.Json
@using Microsoft.AspNetCore.Mvc.Localization
@using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
@using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
@model Report48HTransactionViewModel
@inject IHtmlLocalizer<SharedResources> Localizer

@{
    var localizerRef = (string label) => Localizer["FilerPortal.Transaction.Report48hEosLobbyingTransaction." + label].Value;
    ViewData[LayoutConstants.Title] = localizerRef("Title");

    var address = $"{@Model.Contact.Street}{(string.IsNullOrEmpty(Model.Contact.Street2) ? "" : " " + Model.Contact.Street2) + ", "}{@Model.Contact.City}, {@Model.Contact.State}, {@Model.Contact.ZipCode}";
    var billTablesModel = new LegislativeBillTablesViewModel
            {
                LegislationAssyBillGridModel = Model.LegislationAssyBillGridModel,
                LegislationSenateBillGridModel = Model.LegislationSenateBillGridModel,
                PopupModel = null
            };
}

<h1>@localizerRef("Title")</h1>
<h2>@localizerRef("Header")</h2>
<div class="p-5 mt-4 d-flex align-items-center justify-content-center border border-gray">
    <div class="d-flex flex-column">
        <h2>@localizerRef("AboutThePayment")</h2>
        <p>@localizerRef("Subtitle")</p>

        <table class="mb-3 table border-bottom border-gray">
            <thead class="border-gray">
                <tr>
                    <th scope="col">@localizerRef("LobbyingFirm")</th>
                    <th scope="col">@localizerRef("FirmAddress")</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>@Model.Contact.OrganizationName</td>
                    <td>@address</td>
                </tr>
            </tbody>
        </table>

        @using (Html.BeginForm("Report48HEosTransaction", "Transaction", FormMethod.Post))
        {
            @Html.HiddenFor(m => m.Id)
            @Html.HiddenFor(m => m.FilerId)
            @Html.HiddenFor(m => m.FilingId)
            @Html.HiddenFor(m => m.ContactId)
            @Html.HiddenFor(m => m.RegistrationFilingId)
            @Html.HiddenFor(m => m.ReportType)


            <div class="mb-3">
                @Html.DatePickerFor(SharedLocalizer, m => m.DateLobbyingFirmHired, localizerRef("DateHired"), minDate: null, maxDate: null, format: "MM/dd/yyyy", isRequired: true, isReadOnly: false)
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "FirmHiringDate", Localizer["Common.Date"].Value)
            </div>

            <div class="mb-4 col-lg-4">
                @Html.LabelFor(m => m.Amount, localizerRef("Amount"), new { @class = "form-label" })
                @Html.EJS().NumericTextBox("Amount").Format("c2").Value(Model.Amount).FloatLabelType(Syncfusion.EJ2.Inputs.FloatLabelType.Auto).ShowSpinButton(false).HtmlAttributes(new Dictionary<string, object> { { "aria-labelledby", "AmountPaidToFirm" } }).Render()
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "Amount", localizerRef("Amount"))
            </div>

            <div class="mb-4 col-lg-12" id="legislationSection">
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "LegislativeNumbers", "")
                <partial name="_LegBillTables" model="@billTablesModel" />
            </div>

            <hr />

            <div class="mt-4">
                <partial name="_TransactionWorkflowNavigation" model="@("Save")" />
            </div>
        }
    </div>
</div>
