﻿CREATE TABLE [dbo].[FilerLink] (
    [Id]                   BIGINT                                             IDENTITY (1, 1) NOT NULL,
    [FilerId]              BIGINT                                             NOT NULL,
    [LinkedEntityId]       BIGINT                                             NOT NULL,
    [EffectiveDate]        DATETIME2 (7)                                      NOT NULL,
    [FilerLinkTypeId]      BIGINT                                             NOT NULL,
    [CreatedBy]            BIGINT                                             NOT NULL,
    [ModifiedBy]           BIGINT                                             NOT NULL,
    [AuditableResourceTag] NVARCHAR (450)                                     NULL,
    [PeriodEnd]            DATETIME2 (7) GENERATED ALWAYS AS ROW END HIDDEN   NOT NULL,
    [PeriodStart]          DATETIME2 (7) GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    [TerminationDate]      DATETIME2 (7)                                      NULL,
    [Active]               BIT                                                DEFAULT (CONVERT([bit],(1))) NOT NULL,
    CONSTRAINT [PK_FilerLink] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_FilerLink_Filer_FilerId] FOREIGN KEY ([FilerId]) REFERENCES [dbo].[Filer] ([Id]),
    CONSTRAINT [FK_FilerLink_Filer_LinkedEntityId] FOREIGN KEY ([LinkedEntityId]) REFERENCES [dbo].[Filer] ([Id]),
    CONSTRAINT [FK_FilerLink_FilerLinkType_FilerLinkTypeId] FOREIGN KEY ([FilerLinkTypeId]) REFERENCES [dbo].[FilerLinkType] ([Id]) ON DELETE CASCADE,
    PERIOD FOR SYSTEM_TIME ([PeriodStart], [PeriodEnd])
)
WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE=[dbo].[FilerLinkHistory], DATA_CONSISTENCY_CHECK=ON));






GO
CREATE NONCLUSTERED INDEX [IX_FilerLink_LinkedEntityId]
    ON [dbo].[FilerLink]([LinkedEntityId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FilerLink_FilerLinkTypeId]
    ON [dbo].[FilerLink]([FilerLinkTypeId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FilerLink_FilerId]
    ON [dbo].[FilerLink]([FilerId] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_FilerLink_AuditableResourceTag]
    ON [dbo].[FilerLink]([AuditableResourceTag] ASC) WHERE ([AuditableResourceTag] IS NOT NULL);


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Unique resource tag.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'A string-serialized unique identifier used to link audit logs to arbitrary resource types.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'AuditableResourceTag';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Identifier of the User who last modified this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'ModifiedBy';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Identifier of the User who created this record..', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'CreatedBy';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign key reference to the Identifier of the Filer Link Type.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'FilerLinkTypeId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Effective Date of the Filer Link.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'EffectiveDate';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign key identifier of the Linked Entity, referring to the Filer table.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'LinkedEntityId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Foreign key identifier of the Primary Filer Entity, referring to the Filer table.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'FilerId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Primary Key identifier of the Filer Link.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Termination Date of the Filer Link.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'TerminationDate';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The flag to indicate the active status of this record (soft-delete).', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilerLink', @level2type = N'COLUMN', @level2name = N'Active';

