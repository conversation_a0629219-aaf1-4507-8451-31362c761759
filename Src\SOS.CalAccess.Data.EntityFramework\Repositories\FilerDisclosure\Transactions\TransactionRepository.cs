using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.OtherInfluencePayments;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Transactions;


public class TransactionRepository(DatabaseContext dbContext) : Repository<Transaction, long>(dbContext), ITransactionRepository
{
    public async void AddAttachments(IEnumerable<Attachment> attachments, long transactionId)
    {
        throw new NotImplementedException();
    }

    public async Task<IEnumerable<Transaction>> GetAllByFilerId(long filerId)
    {
        var transactions = await dbContext.Set<Transaction>().Where(t => t.FilerId == filerId).ToListAsync();

        return transactions;
    }

    public async Task<IEnumerable<Transaction>> GetAllByFilingId(long filingId)
    {

        var transactions = await dbContext.Set<Transaction>()
            .Include(x => x.FilingTransactions)
            .Include(x => x.ActionsLobbied)
            .Where(t => t.FilingTransactions.Any(x => x.FilingId == filingId) && t.Active)
            .Select(x => x).ToListAsync();

        return transactions;
    }

    public async Task RemoveAttachments(IEnumerable<long> attachmentIds, long transactionId)
    {
        throw new NotImplementedException();
    }

    public async Task AddTransactionToFiling(long transactionId, long filingId)
    {
        var filingTransaction = new FilingTransaction
        {
            FilingId = filingId,
            TransactionId = transactionId,
        };

        await dbContext.Set<FilingTransaction>().AddAsync(filingTransaction);
        await dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<LobbyingCampaignContribution>> GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(long filingId)
    {
        var transactions = await dbContext.Set<LobbyingCampaignContribution>()
            .Include(t => t.RecipientFiler)
            .ThenInclude(f => f.CurrentRegistration)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .OrderByDescending(x => x.TransactionDate)
            .AsNoTracking()
            .ToListAsync();

        return transactions;
    }

    public async Task<IEnumerable<PaymentMadeToLobbyingCoalitionResponse>> GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(long filingId, DateTime legislativeStartDate)
    {
        var transactions = await dbContext.Set<PaymentMadeToLobbyingCoalition>()
            .Include(t => t.Contact)
            .Include(t => t.FilingTransactions)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .GroupBy(t => new { t.ContactId })
            .Select(g => new PaymentMadeToLobbyingCoalitionResponse
            {
                Id = g.First().Id,
                CoalitionName = g.First().Contact is OrganizationContact ? ((OrganizationContact)g.First().Contact!).OrganizationName : string.Empty,
                FilerId = g.First().Contact != null ? g.First().Contact!.ContactFilerId : null,
                AmountThisPeriod = g.Sum(t => t.Amount),
                CumulativeAmount = dbContext.Set<PaymentMadeToLobbyingCoalition>()
                    .Where(t => t.ContactId == g.Key.ContactId)
                    .Where(t => t.FilingTransactions.Any(ft => ft.Filing!.StartDate >= legislativeStartDate))
                    .SelectMany(t => t.FilingTransactions
                        .Select(ft => new
                        {
                            t.Id,
                            t.Amount
                        }))
                    .Sum(x => x.Amount)
            })
            .AsNoTracking()
            .ToListAsync();

        return transactions;
    }

    public async Task<IEnumerable<OtherPaymentsToInfluenceResponse>> GetAllOtherPaymentsToInfluenceTransactionsForFiling(long filingId, DateTime legislativeStartDate)
    {
        var transactions = await dbContext.Set<OtherPaymentsToInfluence>()
            .Include(t => t.Contact)
            .Include(t => t.FilingTransactions)
            .Include(t => t.PaymentCode)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .Select(t => new
            {
                OtherPaymentsEntity = t,
                CumulativeAmount = dbContext.Set<OtherPaymentsToInfluence>()
                    .Where(x => x.ContactId == t.ContactId)
                    .Where(x => x.FilingTransactions.Any(ft => ft.Filing!.StartDate >= legislativeStartDate))
                    .SelectMany(x => x.FilingTransactions
                        .Select(ft => new { x.Id, x.Amount }))
                    .Sum(x => x.Amount)
            })
            .ToListAsync();

        var transactionsResponse = transactions.Select(t => new OtherPaymentsToInfluenceResponse
        {
            PaymentCodeName = t.OtherPaymentsEntity.PaymentCode?.Name ?? null,
            PayeeSnapshot = t.OtherPaymentsEntity.ContactSnapshot!.Value,
            Amount = t.OtherPaymentsEntity.Amount,
            CumulativeAmount = t.CumulativeAmount,
            PaymentCodeId = t.OtherPaymentsEntity.PaymentCodeId,
            PaymentCodeDescription = t.OtherPaymentsEntity?.PaymentCodeDescription ?? null,
            Id = t.OtherPaymentsEntity!.Id,
            OtherActionsLobbied = t.OtherPaymentsEntity.OtherActionsLobbied,
        });

        return transactionsResponse;
    }

    public async Task<OtherPaymentsToInfluence> GetOtherPaymentsToInfluenceTransactionById(long transactionId)
    {
        var transaction = await dbContext.Set<OtherPaymentsToInfluence>()
            .Include(t => t.ActionsLobbied)
            .Include(t => t.FilingTransactions)
            .FirstOrDefaultAsync(t => t.Id == transactionId) ??
            throw new KeyNotFoundException($"Not found Other payment to influence transaction {transactionId}");

        return transaction;
    }

    public async Task<CumulativeAmountResponse> GetOtherPaymentsCumulativeAmountForFilingAndContact(long filingId, long contactId, DateTime legislativeStartDate)
    {
        var cumulativeAmount = await dbContext.Set<OtherPaymentsToInfluence>()
                    .Where(x => x.ContactId == contactId)
                    .Where(x => x.FilingTransactions.Any(ft => ft.Filing!.StartDate >= legislativeStartDate))
                    .SelectMany(x => x.FilingTransactions.Select(ft => new { x.Id, x.Amount }))
                    .SumAsync(x => x.Amount);

        return new CumulativeAmountResponse { CumulativeAmount = cumulativeAmount };
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<EndOfSessionLobbyingDto>> GetAllEndOfSessionLobbyingTransactionsForFiling(long filingId)
    {
        var transactions = await dbContext.Set<EndOfSessionLobbying>()
            .Include(t => t.Contact)
            .Include(t => t.FilingTransactions)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .Select(t => new EndOfSessionLobbyingDto
            {
                Id = t.Id,
                ActionsLobbied = t.ActionsLobbied,
                Contact = t.Contact,
                FirmName = t.Contact is OrganizationContact ? ((OrganizationContact)t.Contact!).OrganizationName : string.Empty,
                FilerId = t.Contact != null ? t.Contact!.ContactFilerId : null,
                DateLobbyingFirmHired = t.DateLobbyingFirmHired,
                Amount = t.Amount,
            })
            .ToListAsync();

        return transactions;
    }

    /// <inheritdoc/>
    public async Task<EndOfSessionLobbyingDto?> GetAllEndOfSessionLobbyingTransactionById(long transactionId)
    {
        var transactions = await dbContext.Set<EndOfSessionLobbying>()
            .Include(t => t.Contact)
            .Include(t => t.FilingTransactions)
            .Include(t => t.ActionsLobbied)
                .ThenInclude(a => a!.Bill)
                    .ThenInclude(b => b!.BillHouse)
            .Where(t => t.Id == transactionId)
            .Select(t => new EndOfSessionLobbyingDto
            {
                Id = t.Id,
                ActionsLobbied = t.ActionsLobbied,
                Contact = t.Contact,
                FirmName = t.Contact is OrganizationContact ? ((OrganizationContact)t.Contact!).OrganizationName : string.Empty,
                FilerId = t.Contact != null ? t.Contact!.ContactFilerId : null,
                DateLobbyingFirmHired = t.DateLobbyingFirmHired,
                Amount = t.Amount,
                FilingId = t.FilingTransactions.FirstOrDefault() != null ? t.FilingTransactions.First().FilingId : null,
                RegistrationId = t.Contact != null && t.Contact.Filer != null ? t.Contact.Filer.CurrentRegistrationId : null
            })
            .FirstOrDefaultAsync();

        return transactions;
    }

    public async Task<IEnumerable<PaymentMadeToLobbyingFirms>> GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(long filingId, DateTime legislativeStartDate)
    {
        var transactions = await dbContext.Set<PaymentMadeToLobbyingFirms>()
            .Include(t => t.Contact)
            .ThenInclude(c => c!.AddressList)
            .ThenInclude(a => a!.Addresses)
            .Include(t => t.FilingTransactions)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .AsSplitQuery()
            .AsNoTracking()
            .ToListAsync();

        return transactions;
    }

    public async Task<List<CumulativeAmountResponse>> GetCumulativeAmountPaymentMadeToLobbyingFirms(List<long>? contactIds, DateTime legislativeStartDate)
    {
        if (contactIds is null || contactIds.Count == 0)
        {
            return new List<CumulativeAmountResponse>();
        }

        var query = await dbContext.Set<PaymentMadeToLobbyingFirms>()
                    .Where(t => t.ContactId.HasValue && contactIds.Contains(t.ContactId.Value))
                    .Where(t => t.FilingTransactions.Any(ft => ft.Filing!.StartDate >= legislativeStartDate)).ToListAsync();
        return [.. query.GroupBy(t => t.ContactId)
                    .Select(g => new CumulativeAmountResponse
                    {
                        ContactId = g.Key!.Value,
                        CumulativeAmount = g.Sum(x => x.Amount)
                    })];
    }

    public async Task<IEnumerable<ActivityExpense>> GetAllActivityExpenseTransactionsForFiling(long filingId)
    {
        var transactions = await dbContext.Set<ActivityExpense>()
            .Where(t => t.Contact != null)
            .Include(t => t.TransactionReportablePersons)
            .Include(t => t.Contact)
            .ThenInclude(c => c!.FilerContactType)
            .Include(t => t.Contact)
            .ThenInclude(c => c!.AddressList)
            .ThenInclude(al => al!.Addresses)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .OrderByDescending(x => x.TransactionDate)
            .AsSplitQuery()
            .AsNoTracking()
            .ToListAsync();

        return transactions;
    }

    public async Task<IEnumerable<MonetaryType>> GetAllMonetaryTypes()
    {
        var monetaryTypes = await dbContext.Set<MonetaryType>()
            .ToListAsync();

        return monetaryTypes;
    }

    public async Task<IEnumerable<TransactionSummaryDto>> GetTransactionsForFilingSummary(long filingId, DateTime legislativeStartDate, DateTime legislativeEndDate)
    {
        var includedFilingStatusIds = new List<long> { FilingStatus.Accepted.Id, FilingStatus.Incomplete.Id };

        // Get the current filing and its filer ID
        var currentFiling = await dbContext.Set<Filing>()
            .Where(f => f.Id == filingId)
            .FirstOrDefaultAsync() ?? throw new KeyNotFoundException($"Filing with ID {filingId} not found.");

        var filerId = currentFiling.FilerId;

        // First fetch all relevant filing IDs that we want to consider
        var relevantFilingIds = await dbContext.Set<Filing>()
            .Where(f => f.FilerId == filerId && includedFilingStatusIds.Contains(f.StatusId))
            .Where(f =>
                (f.StartDate >= legislativeStartDate && f.EndDate < legislativeEndDate) ||
                f.Id == filingId)
            .GroupBy(f => f.OriginalId ?? f.Id)
            .Select(g => g.OrderByDescending(f => f.Version).First().Id)
            .ToListAsync();

        // Always include the current filing ID if not already in the list
        if (!relevantFilingIds.Contains(filingId))
        {
            relevantFilingIds.Add(filingId);
        }

        // Query transactions for both cumulative and this period
        var cumulativeTransactions = await GetTransactionsForFilings(relevantFilingIds);
        var thisPeriodTransactions = await GetTransactionsForFilings(new List<long> { filingId });

        // Group and merge results
        var summary = cumulativeTransactions
            .Concat(thisPeriodTransactions)
            .GroupBy(x => new
            {
                x.TypeId,
                Name = GetContactNameWithId(x.Contact)
            })
            .Select(g => new TransactionSummaryDto
            {
                TransactionTypeId = g.Key.TypeId,
                Name = g.Key.Name,
                CumulativeToDate = cumulativeTransactions
                    .Where(x => x.TypeId == g.Key.TypeId &&
                                GetContactNameWithId(x.Contact) == g.Key.Name)
                    .Sum(x => x.Amount),
                AmountThisPeriod = thisPeriodTransactions
                    .Where(x => x.TypeId == g.Key.TypeId &&
                                GetContactNameWithId(x.Contact) == g.Key.Name)
                    .Sum(x => x.Amount)
            })
            .OrderBy(x => x.TransactionTypeId)
            .ThenBy(x => x.Name)
            .ToList();

        // Calculate PUC Activity amounts
        var pucActivityThisPeriod = currentFiling.TotalPaymentsPucActivity ?? 0;
        var pucActivityCumulative = await dbContext.Set<Filing>()
            .Where(f => relevantFilingIds.Contains(f.Id))
            .Select(f => f.TotalPaymentsPucActivity ?? 0)
            .SumAsync();

        // Append the PUC Activity transaction summary
        summary.Add(new TransactionSummaryDto
        {
            FilingSummaryTypeId = FilingSummaryType.PucActivitySummary.Id,
            Name = "Total Payments",
            AmountThisPeriod = pucActivityThisPeriod,
            CumulativeToDate = pucActivityCumulative
        });

        // Calculate and append Payments To In-House Lobbyists amounts for lobbyist employer report
        if (currentFiling is LobbyistEmployerReport lobbyistEmployerReport)
        {
            var paymentsInHouseLobbyistsThisPeriod = lobbyistEmployerReport.TotalPaymentsToInHouseLobbyists.Value;
            var paymentsInHouseLobbyistsCumulative = (await dbContext.Set<LobbyistEmployerReport>()
                .Where(f => relevantFilingIds.Contains(f.Id))
                .Select(f => f.TotalPaymentsToInHouseLobbyists.Value)
                .ToListAsync()).Sum();

            summary.Add(new TransactionSummaryDto
            {
                FilingSummaryTypeId = FilingSummaryType.PaymentsToInHouseLobbyists.Id,
                Name = "Total Payments",
                AmountThisPeriod = paymentsInHouseLobbyistsThisPeriod,
                CumulativeToDate = paymentsInHouseLobbyistsCumulative
            });
        }

        return summary;
    }

    public async Task<IEnumerable<PaymentReceiveLobbyingCoalition>> GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(long filingId)
    {
        return await dbContext.Set<PaymentReceiveLobbyingCoalition>()
            .Include(t => t.Contact)
            .ThenInclude(c => c!.AddressList)
            .ThenInclude(a => a!.Addresses)
            .Include(t => t.Contact)
            .ThenInclude(c => c!.FilerContactType)
            .Include(t => t.Contact)
            .ThenInclude(c => c!.EmailAddressList)
            .ThenInclude(e => e!.EmailAddresses)
            .Include(t => t.FilingTransactions)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .AsSplitQuery()
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<PaymentReceiveLobbyingCoalition?> GetPaymentReceivedLobbyingCoalitionTransactionById(long transactionId)
    {
        var transaction = await dbContext.Set<PaymentReceiveLobbyingCoalition>()
            .Include(t => t.Contact)
            .Include(t => t.FilingTransactions)
            .Where(t => t.Id == transactionId && t.FilingTransactions.Any(ft => ft.TransactionId == transactionId))
            .FirstOrDefaultAsync();

        return transaction;
    }

    public async Task<List<CumulativeAmountResponse>> GetCumulativeAmountPaymentReceiveLobbyingCoalition(List<long>? contactIds, DateTime legislativeStartDate)
    {
        if (contactIds is null || contactIds.Count == 0)
        {
            return new List<CumulativeAmountResponse>();
        }


        var query = await dbContext.Set<PaymentReceiveLobbyingCoalition>()
                    .Where(t => t.ContactId.HasValue && contactIds.Contains(t.ContactId.Value))
                    .Where(t => t.FilingTransactions.Any(ft => ft.Filing!.StartDate >= legislativeStartDate)).ToListAsync();
        return [.. query.GroupBy(t => t.ContactId)
                    .Select(g => new CumulativeAmountResponse
                    {
                        ContactId = g.Key!.Value,
                        CumulativeAmount = g.Sum(x => x.Amount)
                    })];
    }

    /// <inheritdoc/>
    public async Task<LobbyingCampaignContribution?> GetLobbyistCampaignContributionTransactionById(long transactionId)
    {
        var transaction = await dbContext.Set<LobbyingCampaignContribution>()
            .Include(t => t.FilingTransactions)
            .Include(t => t.RecipientFiler)
            .ThenInclude(f => f!.CurrentRegistration)
            .Where(t => t.Id == transactionId && t.FilingTransactions.Any(ft => ft.TransactionId == transactionId))
            .FirstOrDefaultAsync();

        return transaction;
    }

    public async Task<PaymentMadeToLobbyingCoalition?> GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(long transactionId)
    {
        var transaction = await dbContext.Set<PaymentMadeToLobbyingCoalition>()
            .Include(t => t.Contact)
            .Include(t => t.FilingTransactions)
            .Where(t => t.Id == transactionId && t.FilingTransactions.Any(ft => ft.TransactionId == transactionId))
            .FirstOrDefaultAsync();

        return transaction;
    }


    /// <inheritdoc/>
    public async Task<LobbyingAdvertisement?> GetLobbyingAdvertisementTransactionByFilingId(long filingId)
    {
        var transaction = await dbContext.Set<LobbyingAdvertisement>()
            .Include(t => t.FilingTransactions)
            .Include(t => t.DistributionMethod)
            .Where(t => t.FilingTransactions.Any(ft => ft.FilingId == filingId))
            .FirstOrDefaultAsync();

        return transaction;
    }

    /// <inheritdoc/>
    public async Task<List<TTransaction>> FindAllTransactionsByFiling<TTransaction>(long filingId, bool isPaymentMadeByAgent = false) where TTransaction : Transaction
    {
        // TD: Currently, we will pull all the records and let SyncFunction handle the pagination.
        // Later on, if there is more than a thousand records, we should consider to handle the pagination here.

        IQueryable<TTransaction> queryable = dbContext
            .Set<TTransaction>()
            .AsNoTracking()
            .Include(i => i.FilingTransactions)
            .Include(i => i.Contact);

        // Apply type-specific includes using the Test extension method
        queryable = queryable.WithTypeSpecificIncludes();

        if (typeof(TTransaction) == typeof(PaymentMade))
        {
            // Cast to IQueryable<PaymentMade> to add specific custom
            var specificQueryable = queryable
                .Cast<PaymentMade>()
                .Where(x => isPaymentMadeByAgent
                    ? !string.IsNullOrWhiteSpace(x.AgentOrIndependentContractorName)
                    : string.IsNullOrWhiteSpace(x.AgentOrIndependentContractorName));

            // Cast back to IQueryable<TTransaction> 
            queryable = specificQueryable.Cast<TTransaction>();
        }

        if (typeof(TTransaction) == typeof(PersonReceiving1000OrMore))
        {
            queryable = queryable
                .Include(i => i.Contact)
                    .ThenInclude(i => i!.FilingContactSummary)
                .Where(x => x.Contact!.FilingContactSummary == null ||
                            x.Contact.FilingContactSummary.DisclosureFilingId == filingId);
        }

        return await queryable
            .AsSplitQuery()
            .Where(x => x.FilingTransactions.Any(y => y.FilingId == filingId) &&
                        x.Active)
            .OrderBy(o => o.TransactionDate)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<TTransaction?> FindByIdAndFilingId<TTransaction>(long id, long filingId) where TTransaction : Transaction
    {
        IQueryable<TTransaction> queryable = dbContext
            .Set<TTransaction>()
            .Include(x => x.FilingTransactions)
            .Include(i => i.Contact);

        // Apply type-specific includes using the Test extension method
        queryable = queryable.WithTypeSpecificIncludes();

        return await queryable
            .Where(x => x.Id == id &&
                        x.FilingTransactions.Any(y => y.FilingId == filingId) &&
                        x.Active)
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<List<FilingTransaction>> FindAllFilingTransactionsByFilingId(long filingId)
    {
        return await dbContext.Set<FilingTransaction>()
            .AsNoTracking()
            .Where(x => x.Transaction!.Active && x.FilingId == filingId)
            .Include(i => i.Transaction)
                .ThenInclude(t => (t as PaymentReceived)!.DisclosureStanceOnCandidate)
                .ThenInclude(t => t!.UnregisteredCandidateSubject)
            .Include(i => i.Transaction)
                .ThenInclude(t => (t as PaymentReceived)!.DisclosureStanceOnBallotMeasure)
                .ThenInclude(t => t!.UnregisteredBallotMeasureSubject)
            .AsSplitQuery()
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<decimal> SumTransactionAmountsByFilingsAndContact(List<long> filingIds, long contactId, long transactionTypeId)
    {
        return await dbContext.Set<FilingTransaction>()
            .Include(i => i.Transaction)
            .Where(x => filingIds.Contains(x.FilingId) &&
                        x.Transaction != null &&
                        x.Transaction.TypeId == transactionTypeId &&
                        x.Transaction.Active &&
                        x.Transaction.ContactId == contactId)
            .SumAsync(s => s.Transaction!.Amount);
    }

    /// <summary>
    /// Represents a transaction data structure for grouping and summarizing transactions.
    /// </summary>
    private sealed class TransactionData
    {
        public long TypeId { get; set; }
        public FilerContact? Contact { get; set; }
        public decimal Amount { get; set; }
    }

    /// <summary>
    /// Fetches transactions for the specified filing IDs.
    /// </summary>
    /// <param name="filingIds"></param>
    /// <returns></returns>
    private async Task<List<TransactionData>> GetTransactionsForFilings(IEnumerable<long> filingIds)
    {
        return await dbContext.Set<FilingTransaction>()
            .Where(ft => filingIds.Contains(ft.FilingId))
            .Join(dbContext.Set<Transaction>(),
                ft => ft.TransactionId, dt => dt.Id,
                (ft, dt) => new { ft, dt })
            .GroupJoin(dbContext.Set<FilerContact>(),
                x => x.dt.ContactId, fc => fc.Id,
                (x, fcGroup) => new { x.ft, x.dt, fcGroup })
            .SelectMany(
                x => x.fcGroup.DefaultIfEmpty(),
                (x, fc) => new TransactionData
                {
                    TypeId = x.dt.TypeId,
                    Contact = fc,
                    Amount = x.dt.Amount
                })
            .ToListAsync();
    }

    /// <summary>
    /// Gets the contact name with ContactFilerId appended in parentheses if available
    /// </summary>
    /// <param name="contact">The filer contact</param>
    /// <returns>Contact name with optional ContactFilerId in parentheses</returns>
    private static string GetContactNameWithId(FilerContact? contact)
    {
        if (contact == null)
        {
            return string.Empty;
        }

        string name = ContactResponseDto.GetContactName(contact);

        if (contact.ContactFilerId.HasValue)
        {
            name = $"{name} ({contact.ContactFilerId})";
        }

        return name;
    }

    /// <inheritdoc/>
    public async Task<PaymentReceived?> GetSmoCampaignStatementPaymentRecievedByIdAsync(long id)
    {
        return await dbContext.Set<PaymentReceived>()
            .AsNoTracking()
            .Where(x => x.Active && x.Id == id)
            .Include(t => t.DisclosureStanceOnCandidate)
                .ThenInclude(c => c!.FilingContactSummary)
            .Include(t => t.DisclosureStanceOnBallotMeasure)
                .ThenInclude(b => b!.FilingContactSummary)
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<List<PaymentReceived>> GetMatchingTransactionByFilingId(List<long> filingIds, long contactId, string position, long? candidateId, long? ballotMeasureId)
    {
        return await dbContext.Set<PaymentReceived>()
            .AsNoTracking()
            .Include(t => t!.FilingTransactions)
            .Include(t => t!.DisclosureStanceOnCandidate)
              .ThenInclude(t => t!.FilingContactSummary)
            .Include(t => t!.DisclosureStanceOnBallotMeasure)
              .ThenInclude(t => t!.FilingContactSummary)
            .Where
            (p =>
                filingIds.Contains(p.FilingTransactions.FirstOrDefault()!.FilingId) &&
                p.ContactId == contactId &&
                (
                ((p.DisclosureStanceOnCandidate != null) && (p.DisclosureStanceOnCandidate.SubjectId ?? 0) == (candidateId ?? 0)) ||
                ((p.DisclosureStanceOnBallotMeasure != null) && (p.DisclosureStanceOnBallotMeasure!.SubjectId ?? 0) == (ballotMeasureId ?? 0))
                ) &&
                (
                ((p.DisclosureStanceOnCandidate != null) && (p.DisclosureStanceOnCandidate!.Position ?? string.Empty) == (position ?? string.Empty)) ||
                ((p.DisclosureStanceOnBallotMeasure != null) && (p.DisclosureStanceOnBallotMeasure!.Position ?? string.Empty) == (position ?? string.Empty))
                )
            )
            .ToListAsync();
    }

}
