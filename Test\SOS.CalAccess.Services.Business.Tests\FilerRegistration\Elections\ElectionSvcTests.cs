using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Elections;
using SOS.CalAccess.Data.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Elections;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(ElectionSvc))]
internal sealed class ElectionSvcTests
{
    private IElectionRepository _electionRepository;
    private IElectionRaceRepository _electionRaceRepository;
    private DateTime _dateNow;

    private ElectionSvc _electionSvc;

    [SetUp]
    public void SetUp()
    {
        _electionRaceRepository = Substitute.For<IElectionRaceRepository>();
        _electionRepository = Substitute.For<IElectionRepository>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _electionSvc = new ElectionSvc(_electionRepository,
            _electionRaceRepository);
    }
    [Test]
    public async Task GetElectionYears_Should()
    {
        // Arrange
        List<int> years = [2020, 2021, 2022];
        _electionRepository.GetElectionYears().Returns(years);

        // Act
        var result = await _electionSvc.GetElectionYears();

        // Assert
        await _electionRepository.Received(1).GetElectionYears();
    }

    [Test]
    public async Task GetAllBallotMeasures()
    {
        //Act
        try
        {
            _ = await _electionSvc.GetAllBallotMeasures();
            Assert.Fail();
        }
        catch (Exception)
        {
            //placeholder till implemented
        }
    }

    [Test]
    public async Task GetAllElectionCycles()
    {
        try
        {
            //Act
            _ = await _electionSvc.GetAllElectionCycles();
            Assert.Fail();
        }
        catch (Exception)
        {
            //placeholder till implemented
        }
    }

    [Test]
    public async Task GetAllElections()
    {
        //Arrange
        var expectedResult = Enumerable.Empty<Election>();
        _electionRepository.GetAllElectionsAsync().Returns(expectedResult);

        //Act
        var result = await _electionSvc.GetAllElections();

        //Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test]
    public async Task GetAllElectionTypes()
    {
        //Act
        try
        {
            _ = await _electionSvc.GetAllElectionTypes();
            Assert.Fail();
        }
        catch (Exception)
        {
            //placeholder till implemented
        }
    }

    [Test]
    public async Task GetDistrictsForElectionAndOffice()
    {
        var races = new List<ElectionRace>() {
            new (){DistrictId=1, District=new(){ Id = 1, Name="district 1"} },
            new (){DistrictId=2, District=new(){ Id = 2, Name="district 2"} },
            new (){DistrictId=2, District=new(){ Id = 2, Name="district 2"} },
            new (){DistrictId=null, District=new(){  Name="district 3"} },
        };

        _electionRaceRepository.GetElectionRacesByElectionIdAndOfficeIdAsync(224, 1982).Returns(races);

        //Act
        var result = await _electionSvc.GetDistrictsForElectionAndOffice(224, 1982);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ToList(), Has.Count.EqualTo(2));
    }

    [Test]
    public async Task GetElection()
    {
        //Arrange
        var race = new ElectionRace() { Election = new() { Name = "2026", ElectionDate = _dateNow } };
        _electionRaceRepository.GetElectionRaceByIdAsync(1982).Returns(race);

        //Act
        var result = await _electionSvc.GetElection(1982);

        //Assert
        Assert.That(result, Is.EqualTo(race.Election));
    }

    [Test]
    public async Task GetElectionRace()
    {
        //Arrange
        var expectedResult = new ElectionRace() { };
        _electionRaceRepository.GetElectionRaceByIdAsync(1982).Returns(expectedResult);

        //Act
        var result = await _electionSvc.GetElectionRace(1982);

        //Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test]
    public async Task GetOfficesForElection()
    {
        var races = new List<ElectionRace>() {
            new (){Office=new(){ Id = 1, Name="district 1"} },
            new (){Office=new(){ Id = 2, Name="district 2"} },
            new (){Office=new(){ Id = 2, Name="district 2"} },
            new (){Office=new(){ Id = 3, Name="district 3"} },
        };

        _electionRaceRepository.GetElectionRacesByElectionIdAsync(1982).Returns(races);

        //Act
        var result = await _electionSvc.GetOfficesForElection(1982);

        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ToList(), Has.Count.EqualTo(3));

    }

    [Test]
    public async Task CreateElection()
    {
        //Arrange
        var election = new Election() { ElectionDate = _dateNow, Name = "name" };
        _electionRepository.Create(election).Returns(election);

        //Act
        var result = await _electionSvc.CreateElection(election);

        //Assert
        await _electionRepository.Received().Create(election);
        Assert.That(result, Is.EqualTo(election));
    }

    [Test]
    public async Task CreateElectionRace()
    {
        //Arrange
        var electionRace = new ElectionRace() { };
        _electionRaceRepository.Create(electionRace).Returns(electionRace);

        //Act
        var result = await _electionSvc.CreateElectionRace(electionRace);

        //Assert
        await _electionRaceRepository.Received().Create(electionRace);
        Assert.That(result, Is.EqualTo(electionRace));
    }

    [Test]
    public Task UpdateElection_ShouldThrowKeyNotFoundException_WhenElectionDoesNotExist()
    {
        // Arrange
        const int electionId = 1;
        var updateRequest = new UpdateElectionRequest
        {
            Name = "Updated Election",
            ElectionDate = new DateTime(2023, 1, 1, 0, 0, 0, 0),
            ElectionTypeId = 2,
            ElectionCycleId = 2,
            FilingDeadline = new DateTime(2022, 12, 1, 0, 0, 0, 0),
            ExpenditureLimitAcceptanceDeadline = 20221101,
            CreatedBy = 2,
            ModifiedBy = 2
        };

        _electionRepository.GetElectionByIdAsync(electionId).Returns((Election?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _electionSvc.UpdateElection(electionId, updateRequest));
        Assert.That(ex.Message, Is.EqualTo($"Election with ID {electionId} not found."));
        return Task.CompletedTask;
    }

    [Test]
    public async Task UpdateElection_ShouldUpdateElection()
    {
        // Arrange
        const int electionId = 1;
        var existingElection = new Election
        {
            Id = electionId,
            Name = "Old Election",
            ElectionDate = new DateTime(2022, 1, 1, 0, 0, 0, 0),
            ElectionTypeId = 1,
            ElectionCycleId = 1,
            FilingDeadline = new DateTime(2021, 12, 1, 0, 0, 0, 0),
            ExpenditureLimitAcceptanceDeadline = 3,
            CreatedBy = 0,
            ModifiedBy = 0
        };

        var updateRequest = new UpdateElectionRequest
        {
            Name = "Updated Election",
            ElectionDate = new DateTime(2023, 1, 1, 0, 0, 0, 0),
            ElectionTypeId = 2,
            ElectionCycleId = 2,
            FilingDeadline = new DateTime(2022, 12, 1, 0, 0, 0, 0),
            ExpenditureLimitAcceptanceDeadline = 20221101,
            CreatedBy = 2,
            ModifiedBy = 2
        };

        _electionRepository.GetElectionByIdAsync(electionId).Returns(existingElection);

        // Act
        var result = await _electionSvc.UpdateElection(electionId, updateRequest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo(updateRequest.Name));
            Assert.That(result.ElectionDate, Is.EqualTo(updateRequest.ElectionDate));
            Assert.That(result.ElectionTypeId, Is.EqualTo(updateRequest.ElectionTypeId));
            Assert.That(result.ElectionCycleId, Is.EqualTo(updateRequest.ElectionCycleId));
            Assert.That(result.FilingDeadline, Is.EqualTo(updateRequest.FilingDeadline));
            Assert.That(result.ExpenditureLimitAcceptanceDeadline, Is.EqualTo(updateRequest.ExpenditureLimitAcceptanceDeadline));
            Assert.That(result.CreatedBy, Is.EqualTo(updateRequest.CreatedBy));
            Assert.That(result.ModifiedBy, Is.EqualTo(updateRequest.ModifiedBy));
        });

        await _electionRepository.Received(1).Update(existingElection);
    }

    [Test]
    public async Task UpdateElection_ShouldUpdateElection_KeepIfRequestNull()
    {
        // Arrange
        const int electionId = 1;
        var existingElection = new Election
        {
            Id = electionId,
            Name = "Old Election",
            ElectionDate = new DateTime(2022, 1, 1, 0, 0, 0, 0),
            ElectionTypeId = 1,
            ElectionCycleId = 1,
            FilingDeadline = new DateTime(2021, 12, 1, 0, 0, 0, 0),
            ExpenditureLimitAcceptanceDeadline = 3,
            CreatedBy = 0,
            ModifiedBy = 0
        };

        var updateRequest = new UpdateElectionRequest
        {
            Id = electionId,
            Name = "Updated Election",
            ElectionDate = new DateTime(2023, 1, 1, 0, 0, 0, 0),
            ElectionTypeId = 2,
            ElectionCycleId = 2,
        };

        _electionRepository.GetElectionByIdAsync(electionId).Returns(existingElection);

        // Act
        var result = await _electionSvc.UpdateElection(electionId, updateRequest);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo(updateRequest.Name));
            Assert.That(result.ElectionDate, Is.EqualTo(updateRequest.ElectionDate));
            Assert.That(result.ElectionTypeId, Is.EqualTo(updateRequest.ElectionTypeId));
            Assert.That(result.ElectionCycleId, Is.EqualTo(updateRequest.ElectionCycleId));
        });

        await _electionRepository.Received(1).Update(existingElection);
    }
}
