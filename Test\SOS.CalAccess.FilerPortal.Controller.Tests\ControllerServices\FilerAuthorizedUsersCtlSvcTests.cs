using Microsoft.Extensions.Localization;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.FilerPortal.ControllerServices.FilerDashboardCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Filers.AuthorizedUsers;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.UI.Common;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(FilerAuthorizedUsersCtlSvc))]
public class FilerAuthorizedUsersCtlSvcTests
{
    private IStringLocalizer<SharedResources> _localizer;
    private IReferenceDataSvc _referenceDataSvc;
    private Generated.IFilersApi _filerApi;
    private ILinkageSvc _linkageSvc;
    private FilerAuthorizedUsersCtlSvc _service;

    [SetUp]
    public void Setup()
    {
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _localizer[Arg.Any<string>()].Returns(ci => new LocalizedString("TestName", "TestValue"));
        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();
        _filerApi = Substitute.For<Generated.IFilersApi>();
        _linkageSvc = Substitute.For<ILinkageSvc>();
        _service = new FilerAuthorizedUsersCtlSvc(_localizer, _referenceDataSvc, _filerApi, _linkageSvc);
    }

    #region GetDashboardViewModel
    [Test]
    public async Task GetDashboardViewModel_ShouldReturnViewModel()
    {
        var filerId = 1L;
        IEnumerable<OfficerResponseDto> officers =
        [
            new()
            {
                Role = "Role",
                FullName = "John Smith"
            }
        ];
        IEnumerable<LinkageDto> users =
        [
            new()
            {
                FilerUserId = 10,
                FilerName = "Organization",
                FilerRoleName = "Admin",
                UserFullName = "Jane Doe",
            }
        ];
        IEnumerable<LinkageRequestDto> requests =
        [
            new()
            {
                FilerName = "Organization2",
                FilerRoleName = "Assistant",
                IsInvitation = true,
                LinkageRequestId = 11,
                UserFullName = "Michael Fox"
            }
        ];
        _linkageSvc.GetOfficers(Arg.Is(filerId)).Returns(officers);
        _linkageSvc.GetCurrentAuthorizedUsers(Arg.Is(filerId)).Returns(users);
        _linkageSvc.GetPendingUserLinkages(Arg.Is(filerId)).Returns(requests);
        var result = await _service.GetDashboardViewModel(filerId);
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOk(), Is.True);
            Assert.That(result.Unwrap().FilerId, Is.EqualTo(filerId));
        });
    }
    [Test]
    public async Task GetDashboardViewModel_Exception_ReturnsErrorResult()
    {
        var filerId = 1L;
        _linkageSvc.GetOfficers(Arg.Is(filerId)).Throws(new InvalidDataException());
        var result = await _service.GetDashboardViewModel(filerId);
        Assert.Multiple(() =>
        {
            Assert.That(result.IsError(), Is.True);
        });
    }
    #endregion

    #region TerminateLinkage
    [Test]
    public async Task TerminateLinkage_ReturnsSuccessResult()
    {
        var result = await _service.TerminateLinkage(1, 1);
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOk(), Is.True);
            Assert.That(result.IsError(), Is.False);
        });
    }

    [Test]
    public async Task TerminateLinkage_ReturnsError()
    {
        // Arrange
        _linkageSvc.TerminateFilerUser(Arg.Any<long>(), Arg.Any<long>()).Throws(new KeyNotFoundException("Error"));

        // Act & Assert
        var result = await _service.TerminateLinkage(1, 1);
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOk(), Is.False);
            Assert.That(result.IsError(), Is.True);
        });

    }
    #endregion

    #region AcceptLinkageRequest
    [Test]
    public async Task AcceptLinkageRequest_WhenLinkageSvcSucceeds_ReturnsOkResult()
    {
        // Arrange
        var linkageRequestId = 42L;
        _linkageSvc
            .AcceptLinkageRequest(linkageRequestId)
            .Returns(Task.CompletedTask);

        // Act
        var result = await _service.AcceptLinkageRequest(linkageRequestId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOk(), Is.True, "Should be OK when no exception is thrown");
            Assert.That(result.IsError(), Is.False, "Should not be an error result");
        });
        await _linkageSvc.Received(1).AcceptLinkageRequest(linkageRequestId);
    }

    [Test]
    public async Task AcceptLinkageRequest_WhenLinkageSvcThrows_ReturnsErrorResult()
    {
        // Arrange
        var linkageRequestId = 43L;
        var ex = new InvalidOperationException("fail");
        _linkageSvc
            .AcceptLinkageRequest(linkageRequestId)
            .ThrowsAsync(ex);

        // Act
        var result = await _service.AcceptLinkageRequest(linkageRequestId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOk(), Is.False, "Should not be OK when an exception is thrown");
            Assert.That(result.IsError(), Is.True, "Should be an error result");
            Assert.That(result.GetError(), Is.SameAs(ex), "Should preserve the original exception");
        });
        await _linkageSvc.Received(1).AcceptLinkageRequest(linkageRequestId);
    }
    #endregion

    #region RejectLinkageRequest
    [Test]
    public async Task RejectLinkageRequest_WhenServiceSucceeds_ReturnsOkResult()
    {
        // Arrange
        var linkageRequestId = 100L;
        _linkageSvc
            .RejectLinkageRequest(linkageRequestId)
            .Returns(Task.CompletedTask);

        // Act
        var result = await _service.RejectLinkageRequest(linkageRequestId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOk(), Is.True, "Expected IsOk() when no exception is thrown");
            Assert.That(result.IsError(), Is.False, "Expected IsError() to be false");
        });
        await _linkageSvc.Received(1).RejectLinkageRequest(linkageRequestId);
    }

    [Test]
    public async Task RejectLinkageRequest_WhenServiceThrows_ReturnsErrorResult()
    {
        // Arrange
        var linkageRequestId = 200L;
        var ex = new InvalidOperationException("fail");
        _linkageSvc
            .RejectLinkageRequest(linkageRequestId)
            .ThrowsAsync(ex);

        // Act
        var result = await _service.RejectLinkageRequest(linkageRequestId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsOk(), Is.False, "Expected IsOk() to be false on exception");
            Assert.That(result.IsError(), Is.True, "Expected IsError() when exception is thrown");
            Assert.That(result.GetError(), Is.SameAs(ex), "Expected GetError() to return the caught exception");
        });
        await _linkageSvc.Received(1).RejectLinkageRequest(linkageRequestId);
    }
    #endregion

    #region GetFilerRoleOptions
    [Test]
    public async Task GetFilerRoleOptions_Success_ReturnOkResult()
    {
        // Arrange
        var filerDto = new Generated.FilerDto(11, new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local), 12, 13, 10);
        _filerApi.GetFilerDto(Arg.Is<long>(1)).Returns(filerDto);
        IEnumerable<FilerRoleDto> filerRoles =
        [
            new FilerRoleDto
            {
                Id = 20,
                FilerTypeId = 13,
                Description = "Description",
                Name = "Name",
                IsDefaultFilerTypeRole = true,
                IsRequestable = true,
            }
        ];
        _referenceDataSvc.GetAllFilerRoles().Returns(filerRoles);
        // Act
        var result = await _service.GetFilerRoleOptions(1);
        // Assert
        Assert.That(result.IsOk(), Is.True);
        var data = result.Unwrap();
        Assert.That(data, Has.Count.EqualTo(1));
        var record = data[0];
        Assert.That(record, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(record.Value, Is.EqualTo("20"));
            Assert.That(record.Text, Is.EqualTo("Name"));
        });
    }
    [Test]
    public async Task GetFilerRoleOptions_Exception_ReturnErrorResult()
    {
        // Arrange
        _filerApi.GetFilerDto(Arg.Is<long>(1)).Throws(new InvalidDataException());
        // Act
        var result = await _service.GetFilerRoleOptions(1);
        // Assert
        Assert.That(result.IsError(), Is.True);
    }
    #endregion

    #region SendNewLinkageRequest
    [Test]
    public async Task SendNewLinkageRequest_Success_ReturnOkResult()
    {
        // Arrange
        var body = new RequestNewLinkageBody
        {
            FilerId = 12,
            Email = "<EMAIL>",
            FilerRoleId = 11,
        };
        // Act
        var result = await _service.SendNewLinkageRequest(body);
        // Assert
        await _linkageSvc
            .Received(1)
            .SendLinkageRequestToEmail(Arg.Is<SendLinkageRequestToEmailDto>(x => x.FilerId == body.FilerId && x.FilerRoleId == body.FilerRoleId && x.RecipientEmail == body.Email));
        Assert.That(result.IsOk(), Is.True);
    }
    [Test]
    public async Task SendNewLinkageRequest_Exception_ReturnErrorResult()
    {
        // Arrange
        var body = new RequestNewLinkageBody
        {
            FilerId = 12,
            Email = "<EMAIL>",
            FilerRoleId = 11,
        };
        _linkageSvc
            .SendLinkageRequestToEmail(Arg.Is<SendLinkageRequestToEmailDto>(x => x.FilerId == body.FilerId && x.FilerRoleId == body.FilerRoleId && x.RecipientEmail == body.Email))
            .Throws(new InvalidDataException());
        // Act
        var result = await _service.SendNewLinkageRequest(body);
        // Assert
        Assert.That(result.IsError(), Is.True);
    }
    #endregion
}
