@using Microsoft.AspNetCore.Html
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization
@inject IHtmlLocalizer<SharedResources> Localizer

@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.InHouseLobbyistViewModel;
@{
    ViewData[LayoutConstants.Title] = SharedLocalizer[ResourceConstants.DisclosureIndexTitle].Value;
    var progressItems = new List<ProgressItem>
    {
        new ProgressItem(ViewData["ProgressItem1Name"]?.ToString() ?? "", true, false),
        new ProgressItem(ViewData["ProgressItem2Name"]?.ToString() ?? "", false, true),
        new ProgressItem(ViewData["ProgressItem3Name"]?.ToString() ?? "", false, true),
        new ProgressItem(ViewData["ProgressItem4Name"]?.ToString() ?? "", false, true),
        new ProgressItem(ViewData["ProgressItem5Name"]?.ToString() ?? "", false, true),
    };

    var progressBar = new ProgressBar(progressItems);

    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelPaymentEntryButton"),
            },

            new()
            {
                Type = ButtonType.Button,
                Action = FormAction.Continue,
                CssClass = "btn btn-primary me-2",
                InnerTextKey = CommonResourceConstants.Save
            },
        },
        RightButtons = new List<ButtonConfig>()
    };
}

<partial name="_LayoutProgressbar" model="progressBar" />

@using (Html.BeginForm("LobbyistCreationOrModification", "LobbyistEmployerRegistration", FormMethod.Post))
{
    @Html.HiddenFor(m => m.LobbyistEmployerRegistrationId, Model?.LobbyistEmployerRegistrationId)
    @Html.HiddenFor(m => m.LobbyistRegistrationId, Model?.LobbyistRegistrationId)
    <div class="d-flex flex-column gap-4 main-form">
        @Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationDetailTitle)
        @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationCreationBody)
        <div class="col-sm-6">
            @Html.TextFieldFor(Localizer, m => m.FirstName, ResourceConstants.LobbyistEmployerRegistrationCreationFirstName, true, "", null, Model!.IsNotAllowToEdit.GetValueOrDefault())

            @Html.TextFieldFor(Localizer, m => m.MiddleName, ResourceConstants.LobbyistEmployerRegistrationCreationMiddleName, false, "", null, Model!.IsNotAllowToEdit.GetValueOrDefault())

            @Html.TextFieldFor(Localizer, m => m.LastName, ResourceConstants.LobbyistEmployerRegistrationCreationLastName, true, "", null, Model!.IsNotAllowToEdit.GetValueOrDefault())

            @Html.TextFieldFor(Localizer, m => m.Email, ResourceConstants.LobbyistEmployerRegistrationCreationEmail, true, "", null, Model!.IsNotAllowToEdit.GetValueOrDefault())
        </div>
    </div>
    <partial name="_ButtonBar" model="buttonBar" />
}

<style>
    .main-form {
        width: 60%;
        margin: auto;
        margin-top: 80px;
    }

    .row {
        width: 60%;
        margin: auto;
    }
</style>

