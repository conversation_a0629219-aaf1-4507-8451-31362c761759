using NSubstitute;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.WebApi.Shared;

namespace SOS.CalAccess.WebApi.Tests.Reference;
[TestFixture]
public class ReferenceDataControllerTests
{
    private IReferenceDataSvc _referenceDataSvc;
    private ReferenceDataController _controller;

    [SetUp]
    public void SetUp()
    {
        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();
        _controller = new ReferenceDataController(_referenceDataSvc);
    }

    [Test]
    public async Task GetAllFilerTypes_ReturnsFilerTypes()
    {
        var filerTypes = new List<FilerType>
            {
                new() { Id = 1, Name = "Individual" }
            };

        _ = _referenceDataSvc.GetAllFilerTypes().Returns(filerTypes);

        var result = await _controller.GetAllFilerTypes();

        Assert.That(result, Is.EquivalentTo(filerTypes));
        _ = await _referenceDataSvc.Received(1).GetAllFilerTypes();
    }

    [Test]
    public async Task GetAllFilerRoles_ReturnsFilerRoles()
    {
        var filerRoles = new List<FilerRoleDto>
            {
                new() { Id = 1, Name = "RoleName" }
            };

        _ = _referenceDataSvc.GetAllFilerRoles().Returns(filerRoles);

        var result = await _controller.GetAllFilerRoles();

        Assert.That(result, Is.EquivalentTo(filerRoles));
        _ = await _referenceDataSvc.Received(1).GetAllFilerRoles();
    }

    [Test]
    public async Task GetAllNotificationTypes_ReturnsNotificationTypes()
    {
        var types = new List<NotificationType>
            {
                new() { Id = 1, Name = "Alert" }
            };

        _ = _referenceDataSvc.GetAllNotificationTypes().Returns(types);

        var result = await _controller.GetAllNotificationTypes();

        Assert.That(result, Is.EquivalentTo(types));
        _ = await _referenceDataSvc.Received(1).GetAllNotificationTypes();
    }

    [Test]
    public async Task GetAllPoliticalParties_ReturnsParties()
    {
        var parties = new List<PoliticalParty>
            {
                new() { Id = 1, Name = "Green" }
            };

        _ = _referenceDataSvc.GetAllPoliticalParties().Returns(parties);

        var result = await _controller.GetAllPoliticalParties();

        Assert.That(result, Is.EquivalentTo(parties));
        _ = await _referenceDataSvc.Received(1).GetAllPoliticalParties();
    }

    [Test]
    public async Task GetOffice_ReturnsOffice()
    {
        var office = new Office { Id = 101, Name = "Mayor" };
        _ = _referenceDataSvc.GetOffice(101).Returns(office);

        var result = await _controller.GetOffice(101);

        Assert.That(result, Is.EqualTo(office));
        _ = await _referenceDataSvc.Received(1).GetOffice(101);
    }

    [Test]
    public async Task ListPermissions_ReturnsMappedDtos()
    {
        var permissions = new List<MaintainPermissionsDto>
            {
                new() { Id = 1, Name = "Edit", Description = "Edit permission", Granted = false }
            };

        _ = _referenceDataSvc.ListPermissions().Returns(permissions);

        var result = await _controller.ListPermissions();

        Assert.That(result, Is.EquivalentTo(permissions));
        _ = await _referenceDataSvc.Received(1).ListPermissions();
    }

    [Test]
    public async Task GetAllOfficialPositions_ReturnsOfficialPositions()
    {
        var officialPositions = new List<OfficialPosition>
        {
            OfficialPosition.Governor
        };

        _ = _referenceDataSvc.GetAllOfficialPositions().Returns(officialPositions);

        var result = await _controller.GetAllOfficialPositions();

        Assert.That(result, Is.EquivalentTo(officialPositions));
        _ = await _referenceDataSvc.Received(1).GetAllOfficialPositions();
    }

    [Test]
    public async Task GetAllAgencies_ReturnsAgencies()
    {
        var agencies = new List<Agency>
        {
            new() { Id = 1, Name = "City Agency" }
        };

        _ = _referenceDataSvc.GetAllAgencies().Returns(agencies);

        var result = await _controller.GetAllAgencies();

        Assert.That(result, Is.EquivalentTo(agencies));
        _ = await _referenceDataSvc.Received(1).GetAllAgencies();
    }

    [Test]
    public async Task GetAllPaymentCodes_ReturnsPaymentCodes()
    {
        var result = await _controller.GetAllPaymentCodes();

        _ = await _referenceDataSvc.Received(1).GetAllPaymentCodes();

    }

    [Test]
    public async Task SearchAllAgencies_WithSearchTerm_ReturnsFilteredAgencies()
    {
        // Arrange
        const string searchTerm = "Education";
        var expectedAgencies = new List<Agency>
        {
            new() { Id = 1, Name = "Department of Education" }
        };

        _referenceDataSvc.SearchAllAgencies(searchTerm).Returns(expectedAgencies);

        // Act
        var result = await _controller.SearchAllAgencies(searchTerm);

        // Assert
        Assert.That(result, Is.EquivalentTo(expectedAgencies));
        await _referenceDataSvc.Received(1).SearchAllAgencies(searchTerm);
    }

    [Test]
    public async Task SearchAllAgencies_WhitespaceQuery_ReturnsAllAgencies()
    {
        // Arrange
        var expectedAgencies = new List<Agency>
        {
            new() { Id = 1, Name = "Department of Education" },
            new() { Id = 2, Name = "Environmental Protection Agency" }
        };

        _referenceDataSvc.SearchAllAgencies("").Returns(expectedAgencies);

        // Act
        var result = await _controller.SearchAllAgencies("");

        // Assert
        Assert.That(result, Is.EquivalentTo(expectedAgencies));
        await _referenceDataSvc.Received(1).SearchAllAgencies("");
    }

    [Test]
    public async Task SearchAllBills_WithSearchTerm_ReturnsFilteredBills()
    {
        // Arrange
        const string searchTerm = "rail";
        var expectedBills = new List<Bill>
        {
            new() { Id = 1, Number = "ABC-123", Title = "City Skyline" },
            new() { Id = 1, Number = "CAB-321", Title = "Build the rail" }
        };

        _referenceDataSvc.SearchAllBills(searchTerm).Returns(expectedBills);

        // Act
        var result = await _controller.SearchAllBills(searchTerm);

        // Assert
        Assert.That(result, Is.EquivalentTo(expectedBills));
        await _referenceDataSvc.Received(1).SearchAllBills(searchTerm);
    }

    [Test]
    public async Task SearchAllBills_WhitespaceQuery_ReturnsAllBills()
    {
        // Arrange
        var expectedBills = new List<Bill>
        {
            new() { Id = 1, Number = "ABC-123", Title = "City Skyline" },
            new() { Id = 1, Number = "CAB-321", Title = "Build the rail" }
        };

        _referenceDataSvc.SearchAllBills("").Returns(expectedBills);

        // Act
        var result = await _controller.SearchAllBills("");

        // Assert
        Assert.That(result, Is.EquivalentTo(expectedBills));
        await _referenceDataSvc.Received(1).SearchAllBills("");
    }

    [Test]
    public async Task GetAllCountryCodes_ReturnsCountryCodes()
    {
        var countryCodes = new List<Country>
            {
                new() { Id = 1, PhoneCountryCode = "+1", CountryName = "United States" }
            };

        _ = _referenceDataSvc.GetAllCountryCodes().Returns(countryCodes);

        var result = await _controller.GetAllCountryCodes();

        Assert.That(result, Is.EquivalentTo(countryCodes));
        _ = await _referenceDataSvc.Received(1).GetAllCountryCodes();
    }

    public async Task GetAllExpenditureCodesAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var response = new List<ExpenditureCode>
        {
            new() { Id = 1, Abbrev = "ABC-123", Description = "Test1" },
            new() { Id = 2, Abbrev = "CAB-321", Description = "Test2" }
        };

        _ = _referenceDataSvc.GetAllExpenditureCodesAsync().Returns(Task.FromResult<IEnumerable<ExpenditureCode>>(response));

        // Act
        var result = await _controller.GetAllExpenditureCodesAsync();

        // Assert
        Assert.That(result, Is.EquivalentTo(response));
    }


    [Test]
    public async Task GetAllAdvertisementDistributionMethods_ReturnsAllAdvertisementDistributionMethods()
    {
        // Arrange
        var expectedMethods = new List<AdvertisementDistributionMethodRefResponse>
        {
            new() {Id = 1, Name = "Direct Mail" },
        };

        _ = _referenceDataSvc.GetAllAdvertisementDistributionMethods().Returns(expectedMethods);

        // Act
        var result = await _controller.GetAllAdvertisementDistributionMethods();

        // Assert
        Assert.That(result.Count, Is.AtLeast(1));
        _ = await _referenceDataSvc.Received(1).GetAllAdvertisementDistributionMethods();
    }

    [Test]
    public async Task GetAllNatureAndInterestTypes_ReturnsAllNatureAndInterestTypes()
    {
        // Arrange
        var response = new List<NatureAndInterestType>
        {
            new() {Id = 1, Name = "Individual" },
        };

        _ = _referenceDataSvc.GetAllNatureAndInterestTypesAsync().Returns(Task.FromResult<IEnumerable<NatureAndInterestType>>(response));

        // Act
        var result = await _controller.GetAllNatureAndInterestTypesAsync();

        // Assert
        Assert.That(result.Count, Is.AtLeast(1));
        _ = await _referenceDataSvc.Received(1).GetAllNatureAndInterestTypesAsync();
    }

    [Test]
    public async Task GetAllIndustryGroupClassificationTypes_ReturnsAllIndustryGroupClassificationTypes()
    {
        // Arrange
        var response = new List<IndustryGroupClassificationType>
        {
            new() {Id = 1, Name = "Agriculture" },
        };

        _ = _referenceDataSvc.GetAllIndustryGroupClassificationTypesAsync().Returns(Task.FromResult<IEnumerable<IndustryGroupClassificationType>>(response));

        // Act
        var result = await _controller.GetAllIndustryGroupClassificationTypesAsync();

        // Assert
        Assert.That(result.Count, Is.AtLeast(1));
        _ = await _referenceDataSvc.Received(1).GetAllIndustryGroupClassificationTypesAsync();
    }

    //Entertainment / Recreation

    [Test]
    public async Task GetAllBusinessSubcategories_ReturnsAllBusinessSubcategories()
    {
        // Arrange
        var response = new List<BusinessSubcategory>
        {
            new() {Id = 1, Name = "Entertainment / Recreation" },
        };

        _ = _referenceDataSvc.GetAllBusinessSubcategoriesAsync().Returns(Task.FromResult<IEnumerable<BusinessSubcategory>>(response));

        // Act
        var result = await _controller.GetAllBusinessSubcategoriesAsync();

        // Assert
        Assert.That(result.Count, Is.AtLeast(1));
        _ = await _referenceDataSvc.Received(1).GetAllBusinessSubcategoriesAsync();
    }
}
