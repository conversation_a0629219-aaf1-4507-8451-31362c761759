using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.WebApi.Transactions;

namespace SOS.CalAccess.WebApi.Tests.Transactions;

[TestFixture]
public class LobbyistEmployerCoalitionControllerTests
{
    // Common values
    private const long DisclosureFilingId = 123;
    private const decimal PaymentAmount1 = 100.00m;
    private const decimal PaymentAmount2 = 200.00m;
    private const long FilerId = 123;
    private const long RegistrationFilingId = 456;
    private const long FilerContactId = 789;
    private const long SuccessTransactionId1 = 789L;
    private const long SuccessTransactionId2 = 999L;

    // System under test and dependencies
    private IAuthorizationService _authorizationService = null!;
    private ITransactionSvc _transactionSvc = null!;
    private IFilingSvc _filingSvc = null!;
    private Mock<ITransactionSvc> _transactionSvcMock;
    private Mock<IFilingSvc> _filingSvcMock;
    private LobbyistEmployerCoalitionController _controller = null!;

    [SetUp]
    public void Setup()
    {
        _authorizationService = Substitute.For<IAuthorizationService>();
        _transactionSvc = Substitute.For<ITransactionSvc>();
        _filingSvc = Substitute.For<IFilingSvc>();
        _transactionSvcMock = new Mock<ITransactionSvc>();
        _filingSvcMock = new Mock<IFilingSvc>();
        _controller = new LobbyistEmployerCoalitionController(_transactionSvc, _filingSvc, _authorizationService);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithRegistrationFilingId_ReturnsOkWithTransactionDto()
    {
        // Arrange
        var request = new PaymentMadeToLobbyingCoalitionRequestDto(DisclosureFilingId, FilerId, PaymentAmount1, RegistrationFilingId, null);
        var expectedResponse = new TransactionResponseDto { Id = SuccessTransactionId1, ValidationErrors = new List<WorkFlowError>() };

        _ = _transactionSvc.CreatePaymentMadeToLobbyingCoalition(DisclosureFilingId, PaymentAmount1, FilerId, RegistrationFilingId, null)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.CreatePaymentMadeToLobbyingCoalition(request, _transactionSvc, CancellationToken.None);

        // Assert
        Assert.That(result.Value, Is.EqualTo(expectedResponse));
        await _transactionSvc.Received(1).CreatePaymentMadeToLobbyingCoalition(
            DisclosureFilingId, PaymentAmount1, FilerId, RegistrationFilingId, null);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithFilerContactId_ReturnsOkWithTransactionDto()
    {
        // Arrange
        var request = new PaymentMadeToLobbyingCoalitionRequestDto(DisclosureFilingId, FilerId, PaymentAmount2, null, FilerContactId);
        var expectedResponse = new TransactionResponseDto { Id = SuccessTransactionId2, ValidationErrors = new List<WorkFlowError>() };

        _ = _transactionSvc.CreatePaymentMadeToLobbyingCoalition(DisclosureFilingId, PaymentAmount2, FilerId, null, FilerContactId)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.CreatePaymentMadeToLobbyingCoalition(request, _transactionSvc, CancellationToken.None);

        // Assert
        Assert.That(result.Value, Is.EqualTo(expectedResponse));
        await _transactionSvc.Received(1).CreatePaymentMadeToLobbyingCoalition(
            DisclosureFilingId, PaymentAmount2, FilerId, null, FilerContactId);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithBothIds_ReturnsBadRequest()
    {
        // Arrange
        var request = new PaymentMadeToLobbyingCoalitionRequestDto(DisclosureFilingId, FilerId, PaymentAmount1, RegistrationFilingId, FilerContactId);

        // Act
        var result = await _controller.CreatePaymentMadeToLobbyingCoalition(request, _transactionSvc, CancellationToken.None);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        var badRequestResult = result.Result as BadRequestObjectResult;
        Assert.That(badRequestResult?.Value, Is.EqualTo("Either RegistrationFilingId or FilerContactId must be provided, but not both."));
        await _transactionSvc.DidNotReceive().CreatePaymentMadeToLobbyingCoalition(
            Arg.Any<long>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>());
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithNeitherIdProvided_ReturnsBadRequest()
    {
        // Arrange
        var request = new PaymentMadeToLobbyingCoalitionRequestDto(DisclosureFilingId, FilerId, PaymentAmount1, null, null);

        // Act
        var result = await _controller.CreatePaymentMadeToLobbyingCoalition(request, _transactionSvc, CancellationToken.None);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<BadRequestObjectResult>());
        var badRequestResult = result.Result as BadRequestObjectResult;
        Assert.That(badRequestResult?.Value, Is.EqualTo("Either RegistrationFilingId or FilerContactId must be provided, but not both."));
        await _transactionSvc.DidNotReceive().CreatePaymentMadeToLobbyingCoalition(
            Arg.Any<long>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>());
    }

    [Test]
    public async Task GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling_ReturnsOk_WithValidData()
    {
        // Arrange
        long filingId = 123;

        // Act
        var result = await _controller.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(
            filingId, _transactionSvc, _filingSvc, CancellationToken.None);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
        var okResult = (OkObjectResult)result.Result;
        Assert.That(okResult.Value, Is.InstanceOf<IReadOnlyList<PaymentReceiveLobbyingCoalitionResponse>>());
    }

    [Test]
    public async Task GetPaymentReceivedLobbyingCoalitionTransactionById_ValidTransactionId_ReturnsOkWithResponse()
    {
        // Arrange
        long transactionId = 789;
        long contactId = 456;
        decimal amount = 1500;
        var response = new PaymentReceiveLobbyingCoalitionResponse
        {
            Id = transactionId,
            AmountThisPeriod = amount,
            CoalitionName = string.Empty,
            Contact = new Services.Business.FilerDisclosure.Contacts.Models.ContactResponseDto { Id = contactId },
            CumulativeAmount = amount
        };

        _transactionSvc.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId).Returns(Task.FromResult(response));

        // Act
        var result = await _controller.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            var returnValue = result.Result as OkObjectResult;
            Assert.That(returnValue, Is.Not.Null);

            var value = returnValue!.Value as PaymentReceiveLobbyingCoalitionResponse;
            Assert.That(value, Is.Not.Null);
            Assert.That(value!.Id, Is.EqualTo(transactionId));
            Assert.That(value!.AmountThisPeriod, Is.EqualTo(amount));
            Assert.That(value!.CoalitionName, Is.EqualTo(string.Empty));
            Assert.That(value!.Contact, Is.Not.Null);
            Assert.That(value!.Contact!.Id, Is.EqualTo(contactId));
            Assert.That(value!.CumulativeAmount, Is.EqualTo(amount));
        });
    }

    [Test]
    public async Task GetAllOtherPaymentsToInfluenceTransactionsForFiling_ReturnsOk_WithValidData()
    {
        // Arrange
        long filingId = 123;
        var legislativeStartDate = new DateTime(2024, 1, 1);

        var mockTransactions = new List<OtherPaymentsToInfluenceResponse>
        {
            new()
            {
                PaymentCodeName = "ABC",
                Amount = 100m,
                CumulativeAmount = 200m,
                Id = 1,
                PayeeSnapshot = new IndividualContact
                {
                    FirstName = "John",
                    MiddleName = "A.",
                    LastName = "Doe",
                    Employer = "MapLight",
                    Occupation = "Engineer",
                    FilerId = 1,
                    AddressListId = 1,
                    PhoneNumberListId = 1,
                    CreatedBy = 1,
                    ModifiedBy = 1
                }
            }
        };

        _filingSvcMock
            .Setup(f => f.GetLegislativeStartDateForFiling(filingId))
            .ReturnsAsync(legislativeStartDate);

        _transactionSvcMock
            .Setup(t => t.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, legislativeStartDate))
            .ReturnsAsync(mockTransactions);

        // Act
        var result = await _controller.GetAllOtherPaymentsToInfluenceTransactionsForFiling(
            filingId, _transactionSvcMock.Object, _filingSvcMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
        var okResult = (OkObjectResult)result.Result;
        Assert.That(okResult.Value, Is.InstanceOf<IReadOnlyList<OtherPaymentsToInfluenceResponse>>());

        var response = (IReadOnlyList<OtherPaymentsToInfluenceResponse>)okResult.Value;
        Assert.That(response!, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(response[0].Amount, Is.EqualTo(100m));
            Assert.That(response[0].CumulativeAmount, Is.EqualTo(200m));
            Assert.That(response[0].PayeeName, Is.EqualTo("John Doe"));
        });

        // Verify service calls
        _filingSvcMock.Verify(f => f.GetLegislativeStartDateForFiling(filingId), Times.Once);
        _transactionSvcMock.Verify(t => t.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, legislativeStartDate), Times.Once);
    }


    [Test]
    public async Task CreatePaymentReceivedByLobbyingCoalition_WithValidRequest_ReturnsOkWithTransactionDto()
    {
        // Arrange
        var request = new PaymentReceiveLobbyingCoalitionRequestDto
        {
            DisclosureFilingId = DisclosureFilingId,
            ContactId = FilerContactId,
            FilerId = FilerId,
            Amount = PaymentAmount1
        };
        var expectedResponse = new TransactionResponseDto { Id = SuccessTransactionId1, ValidationErrors = new List<WorkFlowError>() };

        _ = _transactionSvc.CreatePaymentReceivedByLobbyingCoalition(request)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.CreatePaymentReceivedByLobbyingCoalition(request, _transactionSvc, CancellationToken.None);

        // Assert
        Assert.That(result.Value, Is.EqualTo(expectedResponse));
        await _transactionSvc.Received(1).CreatePaymentReceivedByLobbyingCoalition(request);
    }

    [Test]
    public async Task EditPaymentReceivedByLobbyingCoalition_WithValidRequest_ReturnsOkWithTransactionDto()
    {
        // Arrange
        long transactionId = 456;
        var request = new PaymentReceiveLobbyingCoalitionRequestDto
        {
            DisclosureFilingId = DisclosureFilingId,
            ContactId = FilerContactId,
            FilerId = FilerId,
            Amount = PaymentAmount2
        };
        var expectedResponse = new TransactionResponseDto { Id = transactionId, ValidationErrors = new List<WorkFlowError>() };

        _ = _transactionSvc.EditPaymentReceivedByLobbyingCoalition(transactionId, request)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.EditPaymentReceivedByLobbyingCoalition(transactionId, request, _transactionSvc, CancellationToken.None);

        // Assert
        Assert.That(result.Value, Is.EqualTo(expectedResponse));
        await _transactionSvc.Received(1).EditPaymentReceivedByLobbyingCoalition(transactionId, request);
    }

    [Test]
    public async Task EditPaymentMadeToLobbyingCoalition_EditsTransaction()
    {
        var amount = (decimal?)2000.75;
        // Arrange
        var request = new PaymentMadeToLobbyingCoalitionRequestDto(FilingId: 456L,
            FilerId: 1,
            Amount: amount);

        var editedTransaction = new TransactionResponseDto { Id = 2, Valid = true, ValidationErrors = new List<WorkFlowError>() };

        _transactionSvc.EditPaymentMadeToLobbyingCoalitionAmount(2, request.Amount)
            .Returns(editedTransaction);

        // Act
        var result = await _controller.EditPaymentMadeToLobbyingCoalition(
            2,
            request,
            _transactionSvc);

        // Verify response is OkObjectResult with the expected transaction
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var response = okResult.Value as TransactionResponseDto;
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Id, Is.EqualTo(2));
            Assert.That(response.Valid, Is.True);
        });

        await _transactionSvc.Received(1).EditPaymentMadeToLobbyingCoalitionAmount(2, amount);
    }

    [Test]
    public async Task GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId_ReturnsOk_WithValidData()
    {
        // Arrange
        long transactionId = 1;

        var contribution = new PaymentMadeToLobbyingCoalitionResponse
        {
            Id = transactionId,
            AmountThisPeriod = new Currency(300),
            CoalitionName = string.Empty,
            CumulativeAmount = new Currency(300)
        };

        _ = _transactionSvc.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(transactionId)
            .Returns(contribution);

        // Act
        var result = await _controller.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(
            transactionId, _transactionSvc);

        // Assert
        var actionResult = result.Result as OkObjectResult;
        Assert.That(actionResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(actionResult.StatusCode, Is.Not.Null);
            Assert.That(actionResult.StatusCode, Is.EqualTo(200));
            Assert.That(result, Is.Not.Null);
        });
    }

    [Test]
    public async Task GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId_ReturnsNotFound()
    {
        // Arrange
        long transactionId = 1;

        _ = _transactionSvc.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(transactionId)
            .Returns((PaymentMadeToLobbyingCoalitionResponse?)null);

        // Act
        var result = await _controller.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(
            transactionId, _transactionSvc);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling_ReturnsOk_WhenPaymentsExist()
    {
        // Arrange
        var filingId = 123L;
        var cancellationToken = CancellationToken.None;

        var mockTransactionSvc = new Mock<ITransactionSvc>();
        var mockFilingSvc = new Mock<IFilingSvc>();
        var mockAuthorizationService = new Mock<IAuthorizationService>();

        mockFilingSvc.Setup(svc => svc.GetLegislativeStartDateForFiling(filingId))
            .ReturnsAsync(new DateTime(2023, 1, 1));

        var payments = new List<PaymentMadeToLobbyingCoalitionResponse>
        {
            new() {
                CoalitionName = "Green Coalition for Clean Air",
                FilerId = 2,
                FilingId = 3,
                Id = 1001,
                ContactId = 202,
                AmountThisPeriod = 15000.00m,
                CumulativeAmount = 50000.00m,
                RegistrationId = 3001
            },
            new()
            {
                CoalitionName = "Clean Water Coalition",
                FilerId = 4,
                FilingId = 5,
                Id = 1002,
                ContactId = 203,
                AmountThisPeriod = 12000.00m,
                CumulativeAmount = 45000.00m,
                RegistrationId = 3002
            }
        };

        mockTransactionSvc.Setup(svc => svc.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, It.IsAny<DateTime>()))
            .ReturnsAsync(payments);

        var controller = new LobbyistEmployerCoalitionController(mockTransactionSvc.Object, mockFilingSvc.Object, mockAuthorizationService.Object);

        // Act
        var result = await controller.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, mockTransactionSvc.Object, mockFilingSvc.Object, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult<IReadOnlyList<PaymentMadeToLobbyingCoalitionResponse>>>());
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.Not.Null);
        });
    }

    [Test]
    public async Task GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling_ReturnsNotFound_WhenNoPaymentsExist()
    {
        // Arrange
        var filingId = 123L;
        var cancellationToken = CancellationToken.None;

        var mockTransactionSvc = new Mock<ITransactionSvc>();
        var mockFilingSvc = new Mock<IFilingSvc>();
        var mockAuthorizationService = new Mock<IAuthorizationService>();

        mockFilingSvc.Setup(svc => svc.GetLegislativeStartDateForFiling(filingId))
            .ReturnsAsync(new DateTime(2023, 1, 1));

        mockTransactionSvc.Setup(svc => svc.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, It.IsAny<DateTime>()))
            .ReturnsAsync(new List<PaymentMadeToLobbyingCoalitionResponse>());

        var controller = new LobbyistEmployerCoalitionController(
            mockTransactionSvc.Object,
            mockFilingSvc.Object,
            mockAuthorizationService.Object
        );

        // Act
        var result = await controller.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(
            filingId, mockTransactionSvc.Object, mockFilingSvc.Object, cancellationToken
        );

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null, "Expected OkObjectResult but got null.");
        Assert.That(okResult.StatusCode, Is.EqualTo(200), "Expected status code 200.");

        var response = okResult.Value as IReadOnlyList<PaymentMadeToLobbyingCoalitionResponse>;
        Assert.That(response, Is.Not.Null, "Expected non-null response list.");
        Assert.That(response, Is.Empty, "Expected response to be an empty list.");
    }
    [Test]
    public async Task GetAllLobbyistEmployerCampaignContributionTransactionsForFiling_ReturnsOk_WithValidData()
    {
        // Arrange
        var filingId = 123L;
        var cancellationToken = CancellationToken.None;
        var mockTransactionSvc = new Mock<ITransactionSvc>();
        var mockAuthorizationService = new Mock<IAuthorizationService>();
        var mockFilingSvc = new Mock<IFilingSvc>();

        var contributions = new List<LobbyingCampaignContribution>
        {
            new LobbyingCampaignContribution
            {
                Id = 101,
                ParentId = 10,
                FilerId = 201,
                Filer = It.IsAny<Filer>(),
                ContactId = 301,
                ExternalContactId = "EXT-123",
                Contact = null,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Amount = new Currency(50),
                Notes = "Test contribution notes",
                Parent = null,
                FilingTransactions = new List<FilingTransaction>(),
                TransactionReportablePersons = new List<TransactionReportablePerson>(),
                ActionsLobbied = new List<ActionsLobbied>()!,
                OtherActionsLobbied = "Other lobbied actions",
                MonetaryTypeId = 401,
                MonetaryType = MonetaryType.Cash,
                DeletedAt = null,
                CreatedBy = 1,
                ModifiedBy = 2,
                Active = true,
                NonCommitteeRecipientName = "Non-Committee Recipient",
                RecipientFilerId = 501,
                RecipientFiler =  new Filer
                {
                    Id = 601,
                    FilerStatusId = 1,
                    FilerStatus = FilerStatus.Active,
                    CurrentRegistrationId = 1,
                    CurrentRegistration = It.IsAny<Registration>(),
                    CreatedBy = null,
                    ModifiedBy = null,
                    EffectiveDate = null,
                    Users = new List<FilerUser>(),
                    FilerTypeId = 1,
                    FilerType = FilerType.BusinessEntity,
                    FilerLinks = null
                },
                ContributorFilerId = 601,
                ContributorFiler = It.IsAny<Filer>(),
                NonFilerContributorName = "Non-Filer Contributor",
                SeparateAccountName = "Separate Account"
            }
        };

        mockTransactionSvc.Setup(svc => svc.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(It.IsAny<long>()))
            .ReturnsAsync(contributions);

        var controller = new LobbyistEmployerCoalitionController(
            mockTransactionSvc.Object,
            mockFilingSvc.Object,
            mockAuthorizationService.Object
        );

        // Act
        var result = await controller.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(
            filingId, mockTransactionSvc.Object, cancellationToken
        );

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var response = okResult.Value as IReadOnlyList<LobbyistEmployerCampaignContributionItemResponse>;
        Assert.That(response, Is.Not.Null);
    }

    [Test]
    public async Task GetAllLobbyistEmployerCampaignContributionTransactionsForFiling_ReturnsOk_WithEmptyList()
    {
        // Arrange
        var filingId = 456L;
        var cancellationToken = CancellationToken.None;
        var mockTransactionSvc = new Mock<ITransactionSvc>();
        var mockAuthorizationService = new Mock<IAuthorizationService>();
        var mockFilingSvc = new Mock<IFilingSvc>();

        mockTransactionSvc.Setup(svc => svc.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId))
            .ReturnsAsync(new List<LobbyingCampaignContribution>());

        var controller = new LobbyistEmployerCoalitionController(
            mockTransactionSvc.Object,
            mockFilingSvc.Object,
            mockAuthorizationService.Object
        );

        // Act
        var result = await controller.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(
            filingId, mockTransactionSvc.Object, cancellationToken
        );

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var response = okResult.Value as IReadOnlyList<LobbyistEmployerCampaignContributionItemResponse>;
        Assert.That(response, Is.Not.Null);
        Assert.That(response, Is.Empty);
    }
}
