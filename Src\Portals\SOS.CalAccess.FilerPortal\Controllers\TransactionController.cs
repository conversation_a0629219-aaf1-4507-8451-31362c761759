// <copyright file="TransactionController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.Constants;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Extensions;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using ActivityExpenseDto = SOS.CalAccess.FilerPortal.Generated.ActivityExpenseDto;
using AlertType = SOS.CalAccess.UI.Common.Extensions.AlertType;
using BillHouse = SOS.CalAccess.Models.FilerDisclosure.Transactions.BillHouse;
using FilerContactType = SOS.CalAccess.Models.FilerDisclosure.Contacts.FilerContactType;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
using TransactionReportablePerson = SOS.CalAccess.Models.FilerDisclosure.Contacts.TransactionReportablePerson;
using TransactionReportablePersonDto = SOS.CalAccess.FilerPortal.Generated.TransactionReportablePersonDto;
using WorkFlowError = SOS.CalAccess.FilerPortal.Generated.WorkFlowError;

namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Controller for transaction-related actions.
/// </summary>
[Route("[controller]")]
public sealed class TransactionController : Controller
{
    /// <summary>
    /// A constant used to reference the session data saved for Activity Expenses.
    /// </summary>
    public const string ActivityExpenseData = "ActivityExpenseData";

    /// <summary>
    /// A constant used to reference the disclosure controller for any redirected actions.
    /// </summary>
    public const string DisclosureAction = "Disclosure";

    /// <summary>
    /// A constant used to reference the view used for creating and editing expenditures.
    /// </summary>
    public const string ExpenditureViewName = "Expenditure";

    /// <summary>
    /// A constant used to reference the filer controller.
    /// </summary>
    public const string Filer = "Filer";

    /// <summary>
    /// A constant used to reference the index of any actions.
    /// </summary>
    public const string Index = "Index";

    /// <summary>
    /// A constant used to reference the view used for creating and editing in-kind contributions.
    /// </summary>
    public const string InKindContributionViewName = "InKindContribution";

    /// <summary>
    /// A constant used to reference the view used for creating and editing lobbying campaign contributions.
    /// </summary>
    public const string LobbyingCampaignContributionViewName = "LobbyingCampaignContribution";

    /// <summary>
    /// A constant used to reference the view used for creating and editing lobbyist campaign contributions.
    /// </summary>
    public const string LobbyistCampaignContributionViewName = "LobbyistCampaignContribution";

    /// <summary>
    /// A constant used to reference the view used for creating and editing monetary contributions.
    /// </summary>
    public const string MonetaryContributionViewName = "MonetaryContribution";

    /// <summary>
    /// A constant used to reference the view used for creating and editing activity expenses.
    /// </summary>
    public const string ActivityExpenseView = "ActivityExpense";

    /// <summary>
    /// A constant used to reference the view used for PaymentToLobbyingCoalition Page03.
    /// </summary>
    public const string PaymentToLobbyingCoalitionPage03View = "PaymentToLobbyingCoalition/Page03";

    /// <summary>
    /// A constant used to reference the Recipient Committee Filer field.
    /// </summary>
    public const string RecipientCommitteeFilerIdName = "RecipientCommitteeFilerId";

    /// <summary>
    /// A constant used to reference this controller.
    /// </summary>
    public const string Transaction = "Transaction";

    /// <summary>
    /// A constant used to reference the contact controller.
    /// </summary>
    public const string Contact = "Contact";

    /// <summary>
    /// A constant used to reference the key in TempData that stores the Administrative Action data source.
    /// </summary>
    private const string AdministrativeActionTempDataKey = "AdministrativeAction";
    private const string LegislationAssyBillTempDataKey = "AssemblyBill";
    private const string LegislationSenateBillTempDataKey = "SenateBill";

    private const string DeleteAssemblyBillFunction = "DeleteAssemblyBill";
    private const string DeleteSenateBillFunction = "DeleteSenateBill";

    /// <summary>
    /// Edit payment received lobbying Coalition transaction
    /// </summary>
    private const string EditTransactionContactController = "CoalitionReceivedTransaction";
    private const string EditTransactionContactAction = "EnterContact";

    /// <summary>
    /// Edit other payment to infulence transaction
    /// </summary>
    private const string EditOtherPaymentContactController = "Contact";
    private const string EditOtherPaymentContactAction = "Edit";

    /// <summary>
    /// A type used to assist with formatting reportable people data for grid components.
    /// </summary>
    public record ReportablePersonGridDto(long Id, string Name, string OfficialPosition, decimal Amount);

    private readonly IToastService _toastService;
    private readonly IStringLocalizer<SharedResources> _localizer;
    private readonly IContactsApi _contactsApi;
    private readonly ITransactionCtlSvc _transactionCtlSvc;
    private readonly IDateTimeSvc _dateTimeSvc;
    private readonly IReferenceDataApi _referenceDataApi;
    private readonly IFilingsApi _filingsApi;
    private readonly ITransactionsApi _transactionsApi;

#pragma warning disable S107 // Methods should not have too many parameters
    public TransactionController(
        IToastService toastService,
        IStringLocalizer<SharedResources> localizer,
        ITransactionCtlSvc transactionCtlSvc,
        IContactsApi contactsApi,
        IReferenceDataApi referenceDataApi,
        IDateTimeSvc dateTimeSvc,
        IFilingsApi filingsApi,
        ITransactionsApi transactionsApi)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        _toastService = toastService;
        _localizer = localizer;
        _contactsApi = contactsApi;
        _transactionCtlSvc = transactionCtlSvc;
        _referenceDataApi = referenceDataApi;
        _dateTimeSvc = dateTimeSvc;
        _filingsApi = filingsApi;
        _transactionsApi = transactionsApi;
    }

    /// <summary>
    /// Initializes the view to create a new expenditure.
    /// </summary>
    /// <param name="filerId">The filer that will own the transaction.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="contactId">Pre-selected contact for the transaction.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit a new expenditure.</returns>
    public async Task<ActionResult> CreateExpenditure(
        [FromQuery, Required] long filerId,
        [FromServices] IContactsApi contactsApi,
        [FromQuery] long? contactId = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new ExpenditureViewModel
        {
            Contacts = await contactsApi.GetContacts(filerId, cancellationToken),
            FilerId = filerId,
        };

        return View(ExpenditureViewName, model);
    }

    /// <summary>
    /// Handles the creation of a new expenditure.
    /// </summary>
    /// <param name="filerId">The filer that will own the transaction.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="model">The view model.</param>
    /// <param name="returnUrl">The URL to return to after the transaction is created.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view if the model is invalid, otherwise a redirection to the filer home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> CreateExpenditure(
        [FromQuery, Required] long filerId,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        ExpenditureViewModel model,
        [FromQuery] string? returnUrl = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return await DefaultOnInvalidModel(ExpenditureViewName, contactsApi, model, filerId, cancellationToken);
        }

        _ = await transactionsApi.CreateTransaction(filerId, model.ToRequest(), cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "Expenditure created successfully.");

        return Redirect(filerId, returnUrl);
    }

    /// <summary>
    /// Initializes the view to create a new in-kind contribution.
    /// </summary>
    /// <param name="filerId">The filer that will own the transaction.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="contactId">Pre-selected contact for the transaction.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit a new in-kind contribution.</returns>
    public async Task<ActionResult> CreateInKindContribution(
        [FromQuery, Required] long filerId,
        [FromServices] IContactsApi contactsApi,
        [FromQuery] long? contactId = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new InKindContributionViewModel
        {
            Contacts = await contactsApi.GetContacts(filerId, cancellationToken),
            FilerId = filerId,
        };

        return View(InKindContributionViewName, model);
    }

    /// <summary>
    /// Handles the creation of a new in-kind contribution.
    /// </summary>
    /// <param name="filerId">The filer that will own the transaction.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="model">The view model.</param>
    /// <param name="returnUrl">The URL to return to after the transaction is created.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view if the model is invalid, otherwise a redirection to the filer home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> CreateInKindContribution(
        [FromQuery, Required] long filerId,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        InKindContributionViewModel model,
        [FromQuery] string? returnUrl = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return await DefaultOnInvalidModel(InKindContributionViewName, contactsApi, model, filerId, cancellationToken);
        }

        _ = await transactionsApi.CreateTransaction(filerId, model.ToRequest(), cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "In-kind contribution created successfully.");

        return Redirect(filerId, returnUrl);
    }

    /// <summary>
    /// Initializes the view to create a new monetary contribution.
    /// </summary>
    /// <param name="filerId">The filer that will own the transaction.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="contactId">Pre-selected contact for the transaction.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit a new monetary contribution.</returns>
    public async Task<ActionResult> CreateMonetaryContribution(
        [FromQuery, Required] long filerId,
        [FromServices] IContactsApi contactsApi,
        [FromQuery] long? contactId = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new MonetaryContributionViewModel
        {
            Contacts = await contactsApi.GetContacts(filerId, cancellationToken),
            FilerId = filerId,
        };

        return View(MonetaryContributionViewName, model);
    }

    /// <summary>
    /// Handles the creation of a new monetary contribution.
    /// </summary>
    /// <param name="filerId">The filer that will own the transaction.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="model">The view model.</param>
    /// <param name="returnUrl">The URL to return to after the transaction is created.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view if the model is invalid, otherwise a redirection to the filer home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> CreateMonetaryContribution(
        [FromQuery, Required] long filerId,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        MonetaryContributionViewModel model,
        [FromQuery] string? returnUrl = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return await DefaultOnInvalidModel(MonetaryContributionViewName, contactsApi, model, filerId, cancellationToken);
        }

        _ = await transactionsApi.CreateTransaction(filerId, model.ToRequest(), cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "Monetary contribution created successfully.");

        return Redirect(filerId, returnUrl);
    }

    /// <summary>
    /// Initializes the view to display a monetary contribution details of an existing transaction.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the details of a monetary contribution of an existing transaction.</returns>
    public async Task<ActionResult> ViewMonetaryContribution(
        long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid
            || await transactionsApi.GetTransaction(id, cancellationToken) is not MonetaryContributionResponse data)
        {
            return NotFound();
        }

        var model = new MonetaryContributionViewModel(data)
        {
            Contacts = await contactsApi.GetContacts(data.FilerId!.Value, cancellationToken),
            Readonly = true,
        };

        return View(MonetaryContributionViewName, model);
    }

    /// <summary>
    /// Initializes the view to create a new monetary contribution.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit a new monetary contribution.</returns>
    public async Task<ActionResult> EditMonetaryContribution(
        long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid
            || await transactionsApi.GetTransaction(id, cancellationToken) is not MonetaryContributionResponse data)
        {
            return NotFound();
        }

        var model = new MonetaryContributionViewModel(data)
        {
            Contacts = await contactsApi.GetContacts(data.FilerId!.Value, cancellationToken),
        };

        return View(MonetaryContributionViewName, model);
    }

    /// <summary>
    /// Handles the edition of a monetary contribution.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="model">The view model.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view if the model is invalid, otherwise a redirection to the filer home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> EditMonetaryContribution(
        [FromRoute] long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        MonetaryContributionViewModel model,
        CancellationToken cancellationToken = default)
    {
        var filerId = model.FilerId!.Value;

        if (!ModelState.IsValid)
        {
            return await DefaultOnInvalidModel(MonetaryContributionViewName, contactsApi, model, filerId, cancellationToken);
        }

        await transactionsApi.UpdateTransaction(id, model.ToRequest(), cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "Monetary contribution edited successfully.");

        return RedirectToAction(Index, Filer, new { id = filerId });
    }

    /// <summary>
    /// Initializes the view to display an in-kind contribution details of an existing transaction.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the details of an in-kind contribution of an existing transaction.</returns>
    public async Task<ActionResult> ViewInKindContribution(
        [FromRoute] long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid
            || await transactionsApi.GetTransaction(id, cancellationToken) is not InKindContributionResponse data)
        {
            return NotFound();
        }

        var model = new InKindContributionViewModel(data)
        {
            Contacts = await contactsApi.GetContacts(data.FilerId!.Value, cancellationToken),
            Readonly = true,
        };

        return View(InKindContributionViewName, model);
    }

    /// <summary>
    /// Initializes the view to edit an in-kind contribution.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit an edit to an in-kind contribution.</returns>
    public async Task<ActionResult> EditInKindContribution(
        [FromRoute] long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid
            || await transactionsApi.GetTransaction(id, cancellationToken) is not InKindContributionResponse data)
        {
            return NotFound();
        }

        var model = new InKindContributionViewModel(data)
        {
            Contacts = await contactsApi.GetContacts(data.FilerId!.Value, cancellationToken),
        };

        return View(InKindContributionViewName, model);
    }

    /// <summary>
    /// Handles the edition of an in-kind contribution.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="model">The view model.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view if the model is invalid, otherwise a redirection to the filer home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> EditInKindContribution(
        [FromRoute] long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        InKindContributionViewModel model,
        CancellationToken cancellationToken = default)
    {
        var filerId = model.FilerId!.Value;

        if (!ModelState.IsValid)
        {
            return await DefaultOnInvalidModel(InKindContributionViewName, contactsApi, model, filerId, cancellationToken);
        }

        await transactionsApi.UpdateTransaction(id, model.ToRequest(), cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "In-kind contribution edit successfully.");

        return RedirectToAction(Index, Filer, new { id = filerId });
    }

    /// <summary>
    /// Initializes the view to display an expenditure details of an existing transaction.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the details of an expenditure of an existing transaction.</returns>
    public async Task<ActionResult> ViewExpenditure(
        [FromRoute] long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid
            || await transactionsApi.GetTransaction(id, cancellationToken) is not ExpenditureResponse data)
        {
            return NotFound();
        }

        var model = new ExpenditureViewModel(data)
        {
            Contacts = await contactsApi.GetContacts(data.FilerId!.Value, cancellationToken),
            Readonly = true,
        };

        return View(ExpenditureViewName, model);
    }

    /// <summary>
    /// Initializes the view to edit an expenditure.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit an edit to an expenditure.</returns>
    public async Task<ActionResult> EditExpenditure(
        [FromRoute] long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid
            || await transactionsApi.GetTransaction(id, cancellationToken) is not ExpenditureResponse data)
        {
            return NotFound();
        }

        var model = new ExpenditureViewModel(data)
        {
            Contacts = await contactsApi.GetContacts(data.FilerId!.Value, cancellationToken),
        };

        return View(ExpenditureViewName, model);
    }

    /// <summary>
    /// Handles the edition of a monetary contribution.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="contactsApi">API client for interacting with contacts.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="model">The view model.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view if the model is invalid, otherwise a redirection to the filer home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> EditExpenditure(
        [FromRoute] long id,
        [FromServices] IContactsApi contactsApi,
        [FromServices] ITransactionsApi transactionsApi,
        ExpenditureViewModel model,
        CancellationToken cancellationToken = default)
    {
        var filerId = model.FilerId!.Value;

        if (!ModelState.IsValid)
        {
            return await DefaultOnInvalidModel(ExpenditureViewName, contactsApi, model, filerId, cancellationToken);
        }

        await transactionsApi.UpdateTransaction(id, model.ToRequest(), cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "Expenditure edited successfully.");

        return RedirectToAction(Index, Filer, new { id = filerId });
    }

    /// <summary>
    /// Handles the deletion of a transaction.
    /// </summary>
    /// <param name="id">The id of the transaction being deleted.</param>
    /// <param name="filerId">The filer that owns the transaction.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirection to the filer home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> DeleteTransaction(
        [FromRoute] long id,
        [FromQuery] long filerId,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        await transactionsApi.DeleteTransaction(id, cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "Transaction deleted successfully.");
        return RedirectToAction(Index, Filer, new { id = filerId });
    }

    [NonAction]
    private async ValueTask<ActionResult> DefaultOnInvalidModel<TModel>(
        string viewName,
        IContactsApi contactsApi,
        TModel model,
        long filerId,
        CancellationToken cancellationToken = default)
        where TModel : TransactionViewModel
    {
        if (ModelState.GetFieldValidationState(nameof(filerId)) is ModelValidationState.Invalid)
        {
            return NotFound();
        }

        this.SetGlobalAlert(
            AlertType.Danger, "There was an error with the form. Please correct the errors and try again.");

        model.Contacts = await contactsApi.GetContacts(filerId, cancellationToken);

        return View(viewName, model);
    }

    /// <summary>
    /// Creates a redirect result to the specified return URL if provided; otherwise, redirects to the Filer index.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer.</param>
    /// <param name="returnUrl">The URL to return to after the contact is created.</param>
    /// <returns>The redirect result.</returns>
    [NonAction]
    private ActionResult Redirect(long filerId, string? returnUrl)
    {
        if (!string.IsNullOrWhiteSpace(returnUrl) && Url.IsLocalUrl(returnUrl))
        {
            return LocalRedirect(returnUrl);
        }

        return RedirectToAction(Index, Filer, new { id = filerId });
    }

    /// <summary>
    /// Handles the creation of a new activity expense.
    /// </summary>
    /// <returns>The view for the new activity expense page.</returns>
    [HttpGet]
    [Route("NewActivityExpensePayee")]
    public async Task<IActionResult> NewActivityExpensePayee(
        [FromQuery] long filerId,
        [FromQuery] long filingId,
        [FromQuery] string? returnUrl,
        [FromQuery] string? reportType,
        [FromServices] IContactsApi contactsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var contacts = await contactsApi.GetContacts(filerId, cancellationToken);
        var model = new ActivityExpenseViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            Contacts = contacts.Where(c => c.TypeId != FilerContactType.Committee.Id).ToList(),
            ReturnUrl = returnUrl,
        };

        ViewBag.ReportType = reportType ?? null;
        return View("NewPayee", model);
    }

    /// <summary>
    /// Handles the creation of a new other payment.
    /// </summary>
    /// <returns>The payee selection screen for a new other payment.</returns>
    [HttpGet]
    [Route("NewOtherPaymentPayee")]
    public async Task<IActionResult> NewOtherPaymentPayee(
        [FromQuery] long filerId,
        [FromQuery] long filingId,
        [FromQuery] string? returnUrl,
        [FromQuery] string? reportType,
        [FromServices] IContactsApi contactsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var contacts = await contactsApi.GetContacts(filerId, cancellationToken);
        var model = new OtherPaymentViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            Contacts = [.. contacts.Where(c => c.TypeId != FilerContactType.Committee.Id)],
            ReturnUrl = returnUrl,
        };

        ViewBag.ReportType = reportType ?? null;
        return View("NewPayee", model);
    }

    /// <summary>
    /// Displays the new activity expense page.
    /// </summary>
    /// <returns>The view for the new activity expense page.</returns>
    [HttpGet]
    [Route("NewActivityExpense")]
    public async Task<IActionResult> NewActivityExpense(
        [FromServices] IActivityExpenseApi activityExpenseApi,
        [FromQuery] long filerId,
        [FromQuery] long filingId,
        [FromQuery] long contactId,
        [FromQuery] string? reportType = default,
        [FromQuery] string? returnUrl = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        // Initialize the form or grab the entered data from the current session
        var activityExpenseTypes = await activityExpenseApi.GetActivityExpenseTypes(cancellationToken);
        var serializedModel = HttpContext.Session.GetString(ActivityExpenseData);
        ActivityExpenseViewModel model;
        if (!string.IsNullOrEmpty(serializedModel))
        {
            model = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(serializedModel)!;
        }
        else
        {
            model = new ActivityExpenseViewModel()
            {
                ContactId = contactId,
                FilerId = filerId,
                FilingId = filingId,
                TransactionDate = _dateTimeSvc.GetCurrentDateTime(),
                ReturnUrl = returnUrl,
                ReportType = reportType,
            };
        }

        await InitializeNewActivityExpenseAsync(model, contactId, activityExpenseApi, cancellationToken);
        ViewBag.IsEditing = false;
        ViewBag.ReportType = reportType;
        return View(ActivityExpenseView, model);
    }

    /// <summary>
    /// Displays the new other payment page.
    /// </summary>
    /// <returns>The view for the new other payment page.</returns>
    [HttpGet]
    [Route("NewOtherPayment")]
#pragma warning disable S107 // Methods should not have too many parameters
    public async Task<IActionResult> NewOtherPayment(
        [FromQuery] long filerId,
        [FromQuery] long filingId,
        [FromQuery] long contactId,
        [FromServices] ITransactionsApi transactionsApi,
        [FromQuery] string? reportType = default,
        [FromQuery] string? returnUrl = default,
        [FromQuery] long? transactionId = null,
        CancellationToken cancellationToken = default)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new OtherPaymentViewModel
        {
            FilerId = filerId,
            FilingId = filingId,
            ReturnUrl = returnUrl,
        };
        // In case update transaction
        if (transactionId != null)
        {
            var transaction = await transactionsApi.GetOtherPaymentsToInfluenceTransactionById(transactionId.Value, cancellationToken);
            model.PaymentCode = transaction.PaymentCodeId;
            model.Amount = (decimal?)transaction.Amount;
            model.CumulativeAmount = model.Amount;
            model.TransactionId = transaction.Id;
            var adminActions = transaction.AdministrativeActions.Select(c => new AdministrativeActionViewModel
            {
                AdministrativeAction = c.AdministrativeAction,
                AgencyDescription = c.AgencyDescription,
                AgencyId = c.AgencyId.GetValueOrDefault(),
                Name = string.Empty,
            }).ToList();
            model.AdvertAdminActions = transaction.AdvertAdminActions.GetValueOrDefault();
            SetTempData(AdministrativeActionTempDataKey, adminActions);
        }

        _ = await InitializeNewOtherPaymentAsync(model, contactId, transactionsApi, cancellationToken);
        ViewBag.ReportType = reportType;

        return View("NewOtherPayment", model);
    }

    /// <summary>
    /// Handles the creation of a new other payment to influence.
    /// </summary>
    [HttpPost]
    [Route("NewOtherPayment")]
    public async Task<ActionResult> NewOtherPayment(
    [FromServices] ITransactionsApi transactionsApi,
    OtherPaymentViewModel model,
    [FromQuery] string? returnUrl = default,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            _toastService.Error("There was an issue with your other payment to influence data. Please try again.");
            await InitializeNewOtherPaymentAsync(model, model.ContactId!.Value, transactionsApi, cancellationToken);
            return View("NewOtherPayment", model);
        }

        double reqAmount = (double?)model.Amount <= 0 ? -1 : (double?)model.Amount ?? -1;

        var senateBills = GetTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey) ?? [];
        var assyBills = GetTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey) ?? [];
        var administrativeActions = GetTempData<List<AdministrativeActionViewModel>>(AdministrativeActionTempDataKey) ?? [];

        var senateBillsActionsLobbied = senateBills.Count < 1
        ? new List<ActionsLobbiedDto>() : senateBills.Select(sb => new ActionsLobbiedDto(
        administrativeAction: string.Empty,
        agencyDescription: string.Empty,
        agencyId: null,
        billId: sb.Id
        )).ToList();

        var assyBillsActionsLobbied = assyBills.Count < 1
        ? new List<ActionsLobbiedDto>() : assyBills.Select(sb => new ActionsLobbiedDto(
        administrativeAction: string.Empty,
        agencyDescription: string.Empty,
        agencyId: null,
        billId: sb.Id
        )).ToList();

        var adminActionsActionsLobbied = administrativeActions.Count < 1
        ? new List<ActionsLobbiedDto>() : administrativeActions.Select(aa => new ActionsLobbiedDto(
        administrativeAction: aa.AdministrativeAction ?? string.Empty,
        agencyDescription: aa.AgencyDescription ?? string.Empty,
        agencyId: aa.AgencyId,
        billId: null
        )).ToList();

        var request = new OtherInfluencePaymentDto(
            id: model.TransactionId,
            filerId: (long)model.FilerId!,
            filingId: (long)model.FilingId!,
            contactId: (long)model.ContactId!,
            paymentCodeId: model.PaymentCode,
            paymentCodeDescription: model.PaymentCodeDescription ?? string.Empty,
            amount: reqAmount,
            transactionDate: null,
            advertLegislation: model.AdvertLegislation,
            advertAdminActions: model.AdvertAdminActions,
            advertOther: model.AdvertOther,
            senateBills: senateBillsActionsLobbied,
            assemblyBills: assyBillsActionsLobbied,
            administrativeActions: adminActionsActionsLobbied,
            otherActionsLobbied: model.AdvertOtherActionsLobbied ?? string.Empty
        );

        var controller = await GetDisclosureControllerName((long)model.FilingId);

        try
        {
            if (model.TransactionId == null)
            {
                _ = await transactionsApi.CreateOtherInfluencePayment((long)model.FilerId, (long)model.FilingId, request, cancellationToken);
                _toastService.Success("Other payment to influence successfully created!");
            }
            else
            {
                _ = await transactionsApi.EditOtherInfluencePayment(model.TransactionId.Value, request, cancellationToken);
                _toastService.Success("Other payment to influence successfully updated!");
            }
            TempData.Remove(LegislationSenateBillTempDataKey);
            TempData.Remove(LegislationAssyBillTempDataKey);
            TempData.Remove(AdministrativeActionTempDataKey);
            return RedirectToAction(Index, controller, new { filerId = model.FilerId, filingId = model.FilingId, viewName = FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary.Name, reportType = model.ReportType });
        }
        catch (ApiException err)
        {
            if (err.StatusCode == (HttpStatusCode)422)
            {
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content!);
                SyncDecisionsValidationsWithViewModel(workflowErrors!, model);
            }

            if (model.ContactId.HasValue)
            {
                await InitializeNewOtherPaymentAsync(model, model.ContactId.Value, transactionsApi, cancellationToken);
            }
            return View("NewOtherPayment", model);
        }
    }

    /// <summary>
    /// Handles the creation of a new activity expense.
    /// </summary>
    [HttpPost]
    [Route("NewActivityExpense")]
    [Route("EditActivityExpense")]
    public async Task<ActionResult> HandleActivityExpense(
    [FromServices] IActivityExpenseApi activityExpenseApi,
    ActivityExpenseViewModel model,
    [FromQuery] string? returnUrl = default,
    CancellationToken cancellationToken = default)
    {
        bool isNewActivityExpense = model.Id == null;

        if (!ModelState.IsValid)
        {
            var error = isNewActivityExpense ? _localizer["FilerPortal.Transaction.NewActivityExpense.CreateErrorMessage"] : _localizer["FilerPortal.Transaction.EditActivityExpense.EditErrorMessage"];
            _toastService.Error(error);
            return View(ActivityExpenseView, model);
        }


        // Grab the reportable persons from the activity expense session and format it for the POST request
        var serializedModel = HttpContext.Session.GetString(ActivityExpenseData);
        List<TransactionReportablePersonDto> reportablePeople = new();
        if (!string.IsNullOrEmpty(serializedModel))
        {
            var activityExpenseModel = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(serializedModel!);
            if (activityExpenseModel?.TransactionReportablePeople?.Count > 0)
            {
                model.TransactionReportablePeople = activityExpenseModel.TransactionReportablePeople;
                foreach (var person in activityExpenseModel!.TransactionReportablePeople!)
                {
                    TransactionReportablePersonDto newPerson = new(
                        agency: person.Agency ?? string.Empty,
                        agencyDescription: person.AgencyDescription ?? string.Empty,
                        amount: (double)person.Amount.Value,
                        contactDisplay: "",
                        contactType: "",
                        createdDate: _dateTimeSvc.GetCurrentDateTime(),
                        filerContactId: model.ContactId,
                        id: person.Id,
                        name: person.Name ?? string.Empty,
                        officialPosition: person.OfficialPosition ?? string.Empty,
                        officialPositionDescription: person.OfficialPositionDescription ?? string.Empty,
                        registrationDisplay: "",
                        registrationId: null,
                        registrationType: "",
                        transactionId: null
                    );
                    reportablePeople.Add(newPerson);
                }
            }
        }

        double reqAmount = (double?)model.Amount <= 0 ? -1 : (double?)model.Amount ?? -1;

        ActivityExpenseDto request = new(
            model.ActivityDescription ?? string.Empty,
            model.ActivityExpenseTypeId,
            reqAmount,
            (long)model.ContactId!,
            model.CreditCardCompanyName ?? string.Empty,
            (long)model.FilerId!,
            (long)model.FilingId!,
            model.Id,
            model.IsCreditCard,
            null,
            model.Notes ?? string.Empty,
            model.TransactionDate ?? null,
            reportablePeople
        );

        var controller = await GetDisclosureControllerName((long)model.FilingId);

        try
        {
            if (isNewActivityExpense)
            {
                _ = await activityExpenseApi.CreateActivityExpense((long)model.FilerId, (long)model.FilingId, request, cancellationToken);
            }
            else
            {
                await activityExpenseApi.UpdateActivityExpense((long)model.FilerId, (long)model.FilingId, request, cancellationToken);
            }

            HttpContext.Session.Remove(ActivityExpenseData);
            var success = isNewActivityExpense ? _localizer["FilerPortal.Transaction.NewActivityExpense.CreateSuccessMessage"] : _localizer["FilerPortal.Transaction.EditActivityExpense.UpdateSuccessMessage"];
            _toastService.Success(success);
            return RedirectToAction(Index, controller, new { filerId = model.FilerId, filingId = model.FilingId, viewName = FilingSummaryTypeModel.ActivityExpenseSummary.Name, reportType = model.ReportType });
        }
        catch (ApiException err)
        {
            if (err.StatusCode == (HttpStatusCode)422)
            {
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content!);
                foreach (var error in workflowErrors!)
                {
                    SyncDecisionsValidationsWithViewModel([error], model);
                }
            }

            if (model.ContactId.HasValue)
            {
                _ = await InitializeNewActivityExpenseAsync(model, model.ContactId.Value, activityExpenseApi, cancellationToken);
            }

            ViewBag.IsEditing = !isNewActivityExpense;

            return View(ActivityExpenseView, model);
        }
    }

    [HttpGet("{reportType}/PreEditActivityExpense/{id}")]
    public async Task<ActionResult> PreEditActivityExpense(
        [FromRoute] string reportType,
        [FromRoute] long id,
        [FromServices] IActivityExpenseApi activityExpenseApi,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { }) { StatusCode = 404 };
        }

        try
        {
            var result = await activityExpenseApi.GetActivityExpenseForUpdate(id, cancellationToken);
            var activityExpenseSummaryUrl = Url.Action(Index, DisclosureAction, new { filerId = result.FilerId, filingId = result.FilingId, reportType, viewName = FilingSummaryTypeModel.ActivityExpenseSummary.Name });
            return RedirectToAction("Edit", Contact, new { filerId = result.FilerId, filingId = result.FilingId, contactId = result.ContactId, reportType, context = $"EditActivityExpense/{id}", returnUrl = activityExpenseSummaryUrl });
        }
        catch
        {
            return NotFound();
        }
    }

    [HttpGet("EditActivityExpense")]
    public async Task<ActionResult> EditActivityExpense(
        [FromServices] IActivityExpenseApi activityExpenseApi,
        [FromQuery] long id,
        [FromQuery] long filerId,
        [FromQuery] long filingId,
        [FromQuery] long contactId,
        [FromQuery] string? reportType = default,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var serializedModel = HttpContext.Session.GetString(ActivityExpenseData);
        ActivityExpenseViewModel model;
        if (!string.IsNullOrEmpty(serializedModel))
        {
            model = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(serializedModel)!;
        }
        else
        {
            var result = await activityExpenseApi.GetActivityExpenseForUpdate(id, cancellationToken);
            model = new ActivityExpenseViewModel
            {
                ActivityDescription = result.ActivityDescription,
                ActivityExpenseTypeId = result.ActivityExpenseTypeId,
                Amount = (decimal)result.Amount,
                ContactId = contactId,
                CreditCardCompanyName = result.CreditCardCompanyName,
                FilerId = filerId,
                FilingId = filingId,
                Id = id,
                IsCreditCard = result.IsCreditCard,
                Notes = result.Notes,
                TransactionDate = result.TransactionDate,
                ReportType = reportType,

                // Map the reportable people data from GetActivityExpenseForUpdate for the view
                TransactionReportablePeople = [.. result.TransactionReportablePersons
                        .Select(rp => new TransactionReportablePerson
                        {
                            Agency = rp.Agency,
                            AgencyDescription = rp.AgencyDescription,
                            Amount = (Currency)(decimal)rp.Amount!,
                            FilerContactId = rp.FilerContactId,
                            Id = (long)rp.Id!,
                            Name = rp.Name,
                            OfficialPosition = rp.OfficialPosition,
                            OfficialPositionDescription = rp.OfficialPositionDescription,
                            TransactionId = (long)rp.TransactionId!,
                        })],
            };
        }

        _ = await InitializeNewActivityExpenseAsync(model, contactId, activityExpenseApi, cancellationToken);
        HttpContext.Session.SetString(ActivityExpenseData, JsonConvert.SerializeObject(model));
        ViewBag.IsEditing = true;
        ViewBag.ReportType = reportType;
        return View(ActivityExpenseView, model);
    }

    /// </summary>
    /// <param name="model">The ActivityExpenseViewModel to initialize.</param>
    /// <param name="activityExpenseTypes">The list of activity expense types.</param>
    /// <param name="contactId">The contact ID to fetch the contact details.</param>
    /// <param name="cancellationToken">The cancellation token for async operations.</param>
    /// <returns>A populated ActivityExpenseViewModel with additional properties set.</returns>
    private async Task<ActivityExpenseViewModel> InitializeNewActivityExpenseAsync(
        ActivityExpenseViewModel model,
        long contactId,
        IActivityExpenseApi activityExpenseApi,
        CancellationToken cancellationToken)
    {
        // Initialize the payee information grid (Contact)
        List<ContactItemResponse> contactList = new();
        var contact = await _contactsApi.GetContact(contactId, cancellationToken);
        contactList.Add(contact);
        model.Contacts = contactList;

        // Grab the relevant data and convert Amount field into a double for the grid
        var reportablePeople = GetReportablePeopleForActivityExpense(model);

        model.ReportablePeopleGridModel = CreateReportablePeopleGridModel(reportablePeople);

        var activityExpenseTypes = await activityExpenseApi.GetActivityExpenseTypes(cancellationToken);
        // Set the ActivityExpenseTypes
        model.ActivityExpenseTypes = GetActivityExpenseTypesDictionary(activityExpenseTypes);

        return model;
    }

    private static List<ReportablePersonGridDto> GetReportablePeopleForActivityExpense(ActivityExpenseViewModel model)
    {
        return model.TransactionReportablePeople?
            .Select(p => new ReportablePersonGridDto(
                p.Id!,
                p.Name!,
                p.OfficialPosition!,
                p.Amount.Value))
            .ToList() ?? new List<ReportablePersonGridDto>();
    }

    private SmallDataGridModel CreateReportablePeopleGridModel(List<ReportablePersonGridDto> reportablePeople)
    {

        var allowDelete = reportablePeople.Count > 1;

        return new SmallDataGridModel
        {
            GridId = "ReportablePeopleGrid",
            GridType = nameof(TransactionReportablePerson),
            DataSource = reportablePeople,
            RowDataBound = "onRowDataBound",
            AllowPaging = false,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = allowDelete,
            PrimaryKey = nameof(TransactionReportablePerson.Id),
            DeleteConfirmationMessage = "Are you sure that you want to delete reportable person?",
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new() { Field = "Name", HeaderText = "Name of person" },
                new() { Field = "OfficialPosition", HeaderText = "Position" },
                new() { Field = "Amount", HeaderText = "Portion of Activity Expense", IsCurrency = true },
            },
            ActionItems = allowDelete
            ? new List<GridActionItem>
            {
            new() {
                Label = CommonResourceConstants.Edit,
                Action = "editRow",
                ControllerName= Transaction,
                ActionName = "EditReportablePerson"
            },
            new() {
                Label = CommonResourceConstants.Delete,
                Action = "deleteRow",
                ControllerName= Transaction,
                ActionName = "DeleteReportablePerson"
            }
            }
            : new List<GridActionItem>
            {
            new() {
                Label = CommonResourceConstants.Edit,
                Action = "editRow",
                ControllerName= Transaction,
                ActionName = "EditReportablePerson"
            }
            }
        };
    }

    [HttpDelete("DeleteReportablePerson/{id}")]
    public JsonResult DeleteReportablePerson([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { }) { StatusCode = 404 };
        }

        var result = GetPersonFromSession(id, out var model, out var person, out var errorResult);
        if (!result)
        {
            return errorResult!;
        }
        var personToRemove = person!;

        model!.TransactionReportablePeople!.Remove(personToRemove);

        HttpContext.Session.SetString(ActivityExpenseData, JsonConvert.SerializeObject(model));

        var reportablePeople = GetReportablePeopleForActivityExpense(model);

        var reportableGridModel = CreateReportablePeopleGridModel(reportablePeople);

        // Make sure the returned result to Ajax always as PascalCase
        var response = new JsonResult(reportableGridModel, new JsonSerializerOptions());

        return response;
    }

    [HttpGet("EditReportablePerson/{id}")]
    public ActionResult EditReportablePerson([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { }) { StatusCode = 404 };
        }

        var result = GetPersonFromSession(id, out var model, out var person, out var errorResult);
        if (!result)
        {
            return errorResult!;
        }
        var personToEdit = person!;

        return RedirectToAction("EditReportablePerson", Contact, new
        {
            filerId = model!.FilerId,
            id = personToEdit!.Id,
            name = personToEdit.Name,
            agency = personToEdit.Agency,
            agencyDescription = personToEdit.AgencyDescription,
            officialPosition = personToEdit.OfficialPosition,
            officialPositionDescription = personToEdit.OfficialPositionDescription,
            amount = personToEdit.Amount.Value,
            returnUrl = model.ReturnUrl
        });
    }

    private bool GetPersonFromSession(
    long id,
    out ActivityExpenseViewModel? model,
    out TransactionReportablePerson? person,
    out JsonResult? errorResult)
    {
        model = null;
        person = null;
        errorResult = null;

        var serializedModel = HttpContext.Session.GetString(ActivityExpenseData);
        if (string.IsNullOrEmpty(serializedModel))
        {
            errorResult = new JsonResult(new { error = "Session data not found" }) { StatusCode = 404 };
            return false;
        }

        model = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(serializedModel);
        if (model?.TransactionReportablePeople == null)
        {
            errorResult = new JsonResult(new { error = "Invalid model data" }) { StatusCode = 400 };
            return false;
        }

        person = model.TransactionReportablePeople.FirstOrDefault(p => p.Id == id);
        if (person == null)
        {
            errorResult = new JsonResult(new { error = "Person not found" }) { StatusCode = 404 };
            return false;
        }

        return true;
    }

    /// <summary>
    /// <param name="model">The OtherPaymentViewModel to initialize.</param>
    /// <param name="contactId">The contact ID to fetch the contact details.</param>
    /// <param name="cancellationToken">The cancellation token for async operations.</param>
    /// <returns>A populated OtherPaymentViewModel with additional properties set.</returns>
    /// </summary>
    private async Task<OtherPaymentViewModel> InitializeNewOtherPaymentAsync(
        OtherPaymentViewModel model,
        long contactId,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken)
    {
        // Initialize the payee information grid (Contact)
        List<ContactItemResponse> contactList = new();
        var contact = await _contactsApi.GetContact(contactId, cancellationToken);
        contactList.Add(contact);
        model.Contacts = contactList;

        // Initialize the payment codes
        var paymentCodes = await _referenceDataApi.GetAllPaymentCodes(cancellationToken);
        model.SetPaymentCodes(paymentCodes);



        model.CumulativeAmount = model.FilingId.HasValue
            ? await GetCumulativeAmountWithFallback(transactionsApi, model.FilingId.Value, contactId, cancellationToken)
            : 0m;

        // Initialize grids
        model.AdministrativeActionGridModel = GetAdministrativeActionGridModel();
        model.LegislationAssyBillGridModel = CreateLegislationBillTableModel(LegislationAssyBillTempDataKey, GetTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey) ?? [], DeleteAssemblyBillFunction);
        model.LegislationSenateBillGridModel = CreateLegislationBillTableModel(LegislationSenateBillTempDataKey, GetTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey) ?? [], DeleteSenateBillFunction);

        TempData.Keep(LegislationSenateBillTempDataKey);
        TempData.Keep(LegislationAssyBillTempDataKey);
        TempData.Keep(AdministrativeActionTempDataKey);

        return model;
    }

    private static async Task<decimal> GetCumulativeAmountWithFallback(
    ITransactionsApi transactionsApi,
    long filingId,
    long contactId,
    CancellationToken cancellationToken)
    {
        try
        {
            var response = await transactionsApi.GetOtherPaymentsCumulativeAmountForFilingAndContact(
                filingId,
                contactId,
                cancellationToken);

            return (decimal)response.CumulativeAmount;
        }
        catch (ApiException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            // Return 0 for 404 Not Found errors
            return 0m;
        }
    }

    /// <summary>
    /// Initializes Administrative Actions grid
    /// </summary>
    private SmallDataGridModel GetAdministrativeActionGridModel()
    {
        var dataSource = GetTempData<List<AdministrativeActionViewModel>>(AdministrativeActionTempDataKey) ?? [];
        SetTempData(AdministrativeActionTempDataKey, dataSource);

        return new SmallDataGridModel
        {
            PrimaryKey = nameof(AdministrativeActionViewModel.Id),
            GridId = "AdministrativeAction",
            GridType = nameof(AdministrativeActionViewModel),
            DataSource = dataSource,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = true,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new()
                {
                    Field = nameof(AdministrativeActionViewModel.Name),
                    HeaderText = "Agency/Office",
                },
                new()
                {
                    Field = nameof(AdministrativeActionViewModel.AdministrativeAction),
                    HeaderText = "Administrative Actions"
                }
            },
            ActionItems = new List<GridActionItem>
            {
                new() {Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = Transaction, ActionName = "DeleteAdministrativeAction"}
            }
        };
    }

    /// <summary>
    /// Helper function to help initialize the legislation bill grids (assembly bills and senate bills)
    /// <param name="gridId">To set the gridId of the grid model</param>
    /// <param name="dataSource">To point the grid to the appropriate list of bills</param>
    /// <param name="deleteAction">To point the grid to the appropriate delete action</param>
    /// </summary>
    private SmallDataGridModel CreateLegislationBillTableModel(string gridId, List<LegislativeBillViewModel> dataSource, string deleteAction)
    {
        return new SmallDataGridModel
        {
            PrimaryKey = nameof(LegislativeBillViewModel.Id),
            GridId = gridId,
            GridType = nameof(LegislativeBillViewModel),
            DataSource = dataSource,
            AllowPaging = true,
            AllowTextWrap = false,
            PageSize = 10,
            PageSizes = new List<int> { 10, 20, 50 },
            AllowAdding = false,
            AllowDeleting = true,
            EnableExport = false,
            Columns = new List<DataGridColumn>
            {
                new() { Field = nameof(LegislativeBillViewModel.Number), HeaderText = "Bill Number" },
                new() { Field = nameof(LegislativeBillViewModel.Title), HeaderText = "Bill Title" },
            },
            ActionItems = new List<GridActionItem>
            {
                new() {Label = CommonResourceConstants.Delete, Action = "deleteRow", ControllerName = Transaction, ActionName = deleteAction}
            }
        };
    }

    /// <summary>
    /// <param name="id">The Id of the record to delete.</param>
    /// Deletes an Administrative Action
    /// </summary>
    [HttpDelete]
    [Route("DeleteAdministrativeAction/{id:long}")]
    public JsonResult DeleteAdministrativeAction([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var actions = GetTempData<List<AdministrativeActionViewModel>>(AdministrativeActionTempDataKey) ?? [];

        _ = actions.RemoveAll(a => a.Id == id);

        SetTempData(AdministrativeActionTempDataKey, actions);

        var gridModel = GetAdministrativeActionGridModel();

        if (gridModel == null)
        {
            return new JsonResult(new { });
        }

        var response = new JsonResult(gridModel, new JsonSerializerOptions());

        return response;
    }

    /// <summary>
    /// <param name="body">The list of new Administrative Actions being added.</param>
    /// Adds oneor more Administrative Actions to the transaction
    /// </summary>
    [HttpPost]
    [Route("AddAdministrativeAction")]
    public List<AdministrativeActionViewModel> AddAdministrativeAction([FromBody] List<AdministrativeActionViewModel> body)
    {
        if (!ModelState.IsValid)
        {
            return new();
        }

        var actions = GetTempData<List<AdministrativeActionViewModel>>(AdministrativeActionTempDataKey) ?? [];
        if (body != null)
        {
            foreach (var r in body)
            {
                // Add a unique identifier for actions not yet created.
                r.Id = actions.Count == 0 ? 1 : actions.Max(a => a.Id) + 1;
                actions.Add(r);
            }
        }
        SetTempData(AdministrativeActionTempDataKey, actions);
        return actions;
    }

    /// <summary>
    /// <param name="key">The key in TempData that stores data.</param>
    /// Gets a string from Tempdata and converts to and object of type TOutput.
    /// </summary>
    private TOutput? GetTempData<TOutput>(string key)
    {
        var existingData = TempData[key] as string;
        TempData.Keep(key);
        if (!string.IsNullOrEmpty(existingData))
        {
            return JsonConvert.DeserializeObject<TOutput>(existingData);
        }
        return default;
    }

    /// <summary>
    /// <param name="key">The key in TempData that will store data.</param>
    /// <param name="data">The data to be stored.</param>
    /// Stores and object of type TInput in Tempdata.
    /// </summary>
    private void SetTempData<TInput>(string key, TInput data)
    {
        TempData[key] = JsonConvert.SerializeObject(data);
    }

    /// <summary>
    /// <param name="body">The list of new senate bills being added.</param>
    /// Adds one or more senate bill to the transaction
    /// </summary>
    [HttpPost]
    [Route("AddSenateBill")]
    public List<LegislativeBillViewModel> AddSenateBill([FromBody] List<LegislativeBillViewModel> body)
    {
        if (!ModelState.IsValid)
        {
            return new();
        }

        var senateBills = GetTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey) ?? [];
        if (body != null)
        {
            foreach (var r in body)
            {
                r.Id = senateBills.Count == 0 ? 1 : senateBills.Max(a => a.Id) + 1;
                senateBills.Add(r);
            }
        }

        SetTempData(LegislationSenateBillTempDataKey, senateBills);
        return senateBills;
    }

    /// <summary>
    /// <param name="id">The Id of the record to delete.</param>
    /// Deletes a senate bill from the table in the Other Payment form
    /// </summary>
    [HttpDelete]
    [Route("DeleteSenateBill/{id:long}")]
    public JsonResult DeleteSenateBill([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var bills = GetTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey) ?? [];

        _ = bills.RemoveAll(a => a.Id == id);

        SetTempData(LegislationSenateBillTempDataKey, bills);

        var gridModel = CreateLegislationBillTableModel("SenateBill", GetTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey) ?? [], DeleteSenateBillFunction);

        if (gridModel == null)
        {
            return new JsonResult(new { });
        }

        var response = new JsonResult(gridModel, new JsonSerializerOptions());
        return response;
    }

    /// <summary>
    /// <param name="body">The list of new assembly bills being added.</param>
    /// Adds one or more assembly bill to the transaction
    /// </summary>
    [HttpPost]
    [Route("AddAssemblyBill")]
    public List<LegislativeBillViewModel> AddAssemblyBill([FromBody] List<LegislativeBillViewModel> body)
    {
        if (!ModelState.IsValid)
        {
            return new();
        }

        var assyBills = GetTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey) ?? [];
        if (body != null)
        {
            foreach (var r in body)
            {
                r.Id = assyBills.Count == 0 ? 1 : assyBills.Max(a => a.Id) + 1;
                assyBills.Add(r);
            }
        }

        SetTempData(LegislationAssyBillTempDataKey, assyBills);
        return assyBills;
    }

    /// <summary>
    /// <param name="id">The Id of the record to delete.</param>
    /// Deletes an assembly bill from the table in the Other Payment form
    /// </summary>
    [HttpDelete]
    [Route("DeleteAssemblyBill/{id:long}")]
    public JsonResult DeleteAssemblyBill([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var bills = GetTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey) ?? [];

        _ = bills.RemoveAll(a => a.Id == id);

        SetTempData(LegislationAssyBillTempDataKey, bills);

        var gridModel = CreateLegislationBillTableModel("AssemblyBill", GetTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey) ?? [], DeleteAssemblyBillFunction);

        if (gridModel == null)
        {
            return new JsonResult(new { });
        }

        var response = new JsonResult(gridModel, new JsonSerializerOptions());
        return response;
    }

    /// <summary>
    /// Handles the preservation of data from the initial Activity Expense screen when navigating away to add reportable persons.
    /// </summary>
    [HttpPost]
    [Route("NewActivityExpenseReportablePerson")]
    public ActionResult PassActivityExpenseData(
    [FromServices] IActivityExpenseApi activityExpenseApi,
    [FromQuery] string? name,
    [FromQuery] string? officialPosition,
    [FromQuery] string? officialPositionDescription,
    [FromQuery] string? agency,
    [FromQuery] string? agencyDescription,
    ActivityExpenseViewModel model,
    [FromQuery] string? returnUrl = default,
    [FromQuery] bool fromTransaction = true)
    {
        if (!ModelState.IsValid)
        {
            _toastService.Error("There was an issue with your activity expense data. Please try again.");
            return View(ActivityExpenseView, model);
        }

        if (fromTransaction)
        {
            HttpContext.Session.SetString(ActivityExpenseData, JsonConvert.SerializeObject(model));
            return RedirectToAction("NewActivityExpenseReportablePerson", Transaction);
        }
        else
        {
            if (!string.IsNullOrEmpty(name) ||
            !string.IsNullOrEmpty(agency) ||
            !string.IsNullOrEmpty(agencyDescription) ||
            !string.IsNullOrEmpty(officialPosition) ||
            !string.IsNullOrEmpty(officialPositionDescription))
            {
                return RedirectToAction("CreateReportablePerson", Contact, new
                {
                    filerId = model.FilerId,
                    name,
                    agency,
                    agencyDescription,
                    officialPosition,
                    officialPositionDescription,
                    returnUrl
                });
            }
            return RedirectToAction("CreateReportablePerson", Contact, new { filerId = model.FilerId, returnUrl });
        }
    }

    /// <summary>
    /// Handles the relevant data needed for the reportable person selection screen.
    /// </summary>
    /// <returns>The view for the activity expense reportable person creation/selection.</returns>
    [HttpGet]
    [Route("NewActivityExpenseReportablePerson")]
    public async Task<IActionResult> NewActivityExpenseReportablePerson(
    [FromServices] IContactsApi contactsApi,
    [FromServices] ITransactionsApi transactionsApi,
    ActivityExpenseViewModel model,
    [FromQuery] long filerId,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var savedModelData = HttpContext.Session.GetString(ActivityExpenseData);

        if (!string.IsNullOrEmpty(savedModelData))
        {
            model = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(savedModelData);
        }

        return View(model);
    }

    /// <summary>
    /// Renders the common view for creating a lobbying campaign contribution.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer to create the contact for.</param>
    /// <param name="returnUrl">The URL to return to if creation cancelled or completed.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit a new campaign contribution.</returns>
    [HttpGet]
    [Route("CreateLobbyingCampaignContribution")]
    public async Task<ActionResult> CreateLobbyingCampaignContribution(
        [FromQuery, Required] long filerId,
        [FromQuery, Required] long filingId,
        [FromServices] IRegistrationsApi registrationsApi,
        [FromQuery] string? returnUrl = default,
        CancellationToken cancellationToken = default)
    {
        var committees = await registrationsApi.GetAllCommittees(cancellationToken);

        var model = new LobbyingCampaignContributionViewModel
        {
            FilerId = filerId,
            FilingId = filingId
        };
        if (committees.Any())
        {
            InitializeCommitteeAutoCompleteModel(model, committees);
        }
        model.Action = "New";
        return View(LobbyingCampaignContributionViewName, model);
    }

    /// <summary>
    /// Handles the creation of a new campaign contribution, specific to the Lobbyist Employer/Coalition.
    /// </summary>
    [HttpPost]
    [Route("CreateLobbyistEmployerCoalitionLobbyingCampaignContribution")]
    public async Task<ActionResult> CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
    long filerId,
    [FromServices] ITransactionsApi transactionsApi,
    [FromServices] IRegistrationsApi registrationsApi,
    LobbyingCampaignContributionViewModel model,
    [FromQuery] string? returnUrl = default,
    CancellationToken cancellationToken = default)
    {
        var committees = await registrationsApi.GetAllCommittees(cancellationToken);
        if (committees.Any())
        {
            InitializeCommitteeAutoCompleteModel(model, committees);
        }
        LobbyingCampaignContributionRequestDto request = new((double)(model.Amount ?? -1), filerId, model.FilingId, model.Id, model.IsRecipientCommittee, model.NonCommitteeRecipientName ?? string.Empty, model.RecipientCommitteeFilerId, model.TransactionDate);
        var controller = await GetDisclosureControllerName(model.FilingId);
        try
        {
            var response = await transactionsApi.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
                filerId, request, cancellationToken);

            if (response is not null)
            {
                return RedirectToAction(Index, controller, new { filerId = model.FilerId, filingId = model.FilingId, viewName = FilingSummaryTypeModel.CampaignContributionSummary.Name, reportType = FilingTypeModel.LobbyistEmployerReport.Name });
            }
        }
        catch (ApiException err) when (err.StatusCode == (HttpStatusCode)422)
        {
            if (err.Content != null)
            {
                // Deserialize workflow errors from API response
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content);

                SyncDecisionsValidationsWithViewModel(workflowErrors!, model);
                return View(LobbyingCampaignContributionViewName, model);
            }
        }
        catch (ApiException err)
        {
            // Handle other API errors
            ModelState.AddModelError("", "An error occurred while processing your request.");
            return View(LobbyingCampaignContributionViewName, model);
        }

        return View(LobbyingCampaignContributionViewName, model);
    }

    /// <summary>
    /// Renders the view for creating a lobbyist report campaign contribution. The difference between this and CreateLobbyingCampaignContribution is that it uses a model extended from LobbyingCampaignContributionViewModel, specific to just the lobbyist.
    /// </summary>
    /// <param name="returnUrl">The URL to return to if creation cancelled or completed.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit a new campaign contribution.</returns>
    [HttpGet]
    [Route("CreateLobbyistCampaignContribution")]
    public async Task<ActionResult> CreateLobbyistCampaignContribution(
        [FromQuery, Required] long filerId,
        [FromQuery, Required] long filingId,
        [FromServices] IRegistrationsApi registrationsApi,
        [FromQuery] string? returnUrl = default,
        CancellationToken cancellationToken = default)
    {
        var committees = await registrationsApi.GetAllCommittees(cancellationToken);

        var model = new LobbyistCampaignContributionViewModel
        {
            FilerId = filerId,
            FilingId = filingId,
        };

        if (committees.Any())
        {
            InitializeCommitteeAutoCompleteModel(model, committees);
        }

        return View(LobbyistCampaignContributionViewName, model);
    }

    /// <summary>
    /// Handles the creation of a new campaign contribution, specific to the lobbyist. Note that the redirect temporarily goes to "Disclosure" - other lobbyist screens are still WIP.
    /// </summary>
    [HttpPost]
    [Route("CreateLobbyistCampaignContribution")]
    public async Task<ActionResult> CreateLobbyistCampaignContribution(
    long filerId,
    [FromServices] ITransactionsApi transactionsApi,
    [FromServices] IRegistrationsApi registrationsApi,
    LobbyistCampaignContributionViewModel model,
    [FromQuery] string? returnUrl = default,
    CancellationToken cancellationToken = default)
    {
        var committees = await registrationsApi.GetAllCommittees(cancellationToken);
        if (committees.Any())
        {
            InitializeCommitteeAutoCompleteModel(model, committees);
        }
        try
        {
            if (model.IsContributorFiler == true)
            {
                model.ContributorFilerId = filerId;
            }

            LobbyistCampaignContributionRequestDto request = new((double)(model.Amount ?? -1), model.ContributorFilerId, filerId, model.FilingId, model.Id, model.IsContributorFiler,
            model.IsRecipientCommittee, model.NonCommitteeRecipientName ?? string.Empty, model.NonFilerContributorName ?? string.Empty, model.RecipientCommitteeFilerId, model.SeparateAccountName ?? string.Empty, model.TransactionDate ?? null);
            var response = model.Action == "New" ? await transactionsApi.CreateLobbyingCampaignContribution(filerId, request, cancellationToken)
                : await transactionsApi.EditLobbyistCampaignContribution(request, cancellationToken);
            var controller = await GetDisclosureControllerName(model.FilingId);
            if (response is not null)
            {
                _toastService.Success(_localizer[DisclosureConstants.Controller.UpdateTransactionSuccessMessageKey]);
                return RedirectToAction(Index, controller, new { filerId = model.FilerId, filingId = model.FilingId, viewName = FilingSummaryTypeModel.CampaignContributionSummary.Name, reportType = FilingTypeModel.LobbyistReport.Name });
            }
        }
        catch (ApiException err) when (err.StatusCode == (HttpStatusCode)422)
        {
            if (err.Content != null)
            {
                // Deserialize workflow errors from API response
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content);
                SyncDecisionsValidationsWithViewModel(workflowErrors!, model);
                return View(LobbyistCampaignContributionViewName, model);
            }
        }
        catch (ApiException err)
        {
            // Handle other API errors
            ModelState.AddModelError("", "An error occurred while processing your request.");
            return View(LobbyistCampaignContributionViewName, model);
        }

        return View(LobbyistCampaignContributionViewName, model);
    }

    /// <summary>
    /// Initializes the view to edit a lobbyist campaign contribution.
    /// </summary>
    /// <param name="id">The id of the transaction being edited.</param>
    /// <param name="registrationsApi">API client for interacting with committees.</param>
    /// <param name="transactionsApi">API client for interacting with transactions.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The view with the form to submit an edit to a lobbyist campaign contribution.</returns>
    [HttpGet("EditLobbyistCampaignContribution/{id:long}")]
    public async Task<IActionResult> EditLobbyistCampaignContribution(
        [FromRoute] long id,
        [FromServices] IRegistrationsApi registrationsApi,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        var transaction = await transactionsApi.GetLobbyistCampaignContributionTransactionById(id, cancellationToken);
        var committees = await registrationsApi.GetAllCommittees(cancellationToken);

        var model = MapLobbyistCampaignContributionItemResponseToViewModel(transaction);

        if (committees.Any())
        {
            InitializeCommitteeAutoCompleteModel(model, committees);
        }

        model.Action = "Edit";
        return View(LobbyistCampaignContributionViewName, model);
    }

    [HttpPost]
    [Route("CancelTransaction")]
    public async Task<IActionResult> CancelTransaction(
            LobbyingCampaignContributionViewModel model,
            string? reportType
        )
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        _toastService.Success(_localizer["FilerPortal.Disclosure.Dashboard.CancelTransactionSuccessMessage"]);
        var controller = await GetDisclosureControllerName(model.FilingId);
        return RedirectToAction(Index, controller, new { filerId = model.FilerId, filingId = model.FilingId, viewName = FilingSummaryTypeModel.CampaignContributionSummary.Name, reportType });
    }

    [HttpGet("{filingSummaryId:long}/{reportType}/{filingId:long}/InitializePaymentToLobbyingCoalition/{id:long}")]
    public async Task<ActionResult> InitializePaymentToLobbyingCoalition(
        [FromRoute] string? reportType,
        [FromRoute] long? filingId,
        [FromRoute] long? filingSummaryId,
        [FromRoute] long id,
        [FromServices] ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { }) { StatusCode = 404 };
        }

        try
        {
            var result = await lobbyistEmployerCoalitionApi.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(id, cancellationToken);
            return RedirectToAction("EditPaymentToLobbyingCoalition", Transaction, new { id, filerId = result.FilerId, filingId, contactId = result.ContactId, registrationFilingId = result.RegistrationId, filingSummaryId });
        }
        catch
        {
            return NotFound();
        }
    }

    /// <summary>
    /// Handles the creation of a payment made to a lobbying coalition Page 02.
    /// </summary>
    /// <returns>The view for the create payment to a lobbying coalition page 02.</returns>
    [HttpGet("EditPaymentToLobbyingCoalition")]
    public async Task<IActionResult> EditPaymentToLobbyingCoalition(
    [FromQuery] long id,
    [FromQuery] long filerId,
    [FromQuery] long contactId,
    [FromQuery] long registrationFilingId,
    [FromQuery] long filingId,
    [FromQuery] long? filingSummaryId,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            Id = id,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            FilerId = filerId,
            FilingId = filingId,
            FilingSummaryId = filingSummaryId,
            Contact = new GenericContactViewModel() { },
        };

        model = await _transactionCtlSvc.GetContactDetailForViewModel(model, cancellationToken);

        ViewBag.Title = _localizer[ResourceConstants.LobbyingCoalitionTransactionTitle];
        ViewBag.IsEditPaymentToLobbyingCoalition = true;
        return View("PaymentToLobbyingCoalition/Page02", model);
    }

    [HttpPost("EditPaymentToLobbyingCoalition")]
    public async Task<IActionResult> EditPaymentToLobbyingCoalition(PaymentMadeToLobbyingCoalitionViewModel model, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        if (model.RegistrationFilingId != 0)
        {
            return RedirectToAction("EditPaymentToLobbyingCoalitionPage03"
                , Transaction
                , new { model.Id, model.FilerId, model.RegistrationFilingId, model.ContactId, model.FilingId, model.ReturnUrl });
        }

        try
        {
            var newContactId = await _transactionCtlSvc.HandlePaymentToLobbyingCoalitionPage02Submit(model, cancellationToken);
            model.ContactId = newContactId;

            return RedirectToAction("EditPaymentToLobbyingCoalitionPage03"
                , Transaction
                , new { model.Id, model.FilerId, model.RegistrationFilingId, model.ContactId, model.FilingId, model.ReturnUrl });
        }
        catch (Exception)
        {
            _toastService.Error(_localizer[ResourceConstants.APIRequestError]);
            ViewBag.Title = _localizer[ResourceConstants.LobbyingCoalitionTransactionTitle];
            return View("EditPaymentToLobbyingCoalition", new { model.Id, model.FilerId, model.ContactId, model.RegistrationFilingId, model.FilingId, model.FilingSummaryId });
        }
    }

    /// <summary>
    /// Handles the creation of a payment made to a lobbying coalition.
    /// </summary>
    /// <returns>The view for the create payment to a lobbying coalition page.</returns>
    [HttpGet("PaymentToLobbyingCoalition/Page01")]
    public IActionResult PaymentToLobbyingCoalitionPage01(
    [FromQuery] long filerId,
    [FromQuery] long filingId,
    [FromQuery] string? returnUrl)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            ReturnUrl = returnUrl,
        };
        ViewBag.IsEditPaymentToLobbyingCoalition = false;
        return View("PaymentToLobbyingCoalition/Page01", model);
    }

    /// <summary>
    /// Handles the creation of a payment made to a lobbying coalition Page 02.
    /// </summary>
    /// <returns>The view for the create payment to a lobbying coalition page 02.</returns>
    [HttpGet("PaymentToLobbyingCoalition/Page02")]
    public async Task<IActionResult> PaymentToLobbyingCoalitionPage02Async(
    [FromQuery] long filerId,
    [FromQuery] long contactId,
    [FromQuery] long registrationFilingId,
    [FromQuery] long filingId,
    [FromQuery] string? returnUrl,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            FilerId = filerId,
            FilingId = filingId,
            ReturnUrl = returnUrl,
            Contact = new GenericContactViewModel() { },
        };

        model = await _transactionCtlSvc.GetContactDetailForViewModel(model, cancellationToken);

        ViewBag.Title = _localizer[ResourceConstants.LobbyingCoalitionTransactionTitle];
        ViewBag.IsEditPaymentToLobbyingCoalition = false;
        return View("PaymentToLobbyingCoalition/Page02", model);
    }

    /// <summary>
    /// Handles the creation of a payment made to a lobbying coalition Page 03.
    /// </summary>
    /// <returns>Handling Update/Create Contact of lobbying coalition page 03.</returns>
    [HttpPost("PaymentToLobbyingCoalition/Page02")]
    public async Task<IActionResult> PaymentToLobbyingCoalitionPage02Async(PaymentMadeToLobbyingCoalitionViewModel model, FormAction? action = null, CancellationToken cancellationToken = default)
    {
        if (action == FormAction.Cancel)
        {
            _toastService.Success(_localizer[ResourceConstants.CancelTransactionSuccessMessage]);
            var controller = await GetDisclosureControllerName(model.FilingId.GetValueOrDefault());
            return RedirectToAction(Index, controller, new
            {
                filerId = model.FilerId,
                viewName = FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name,
                filingId = model.FilingId,
                reportType = FilingTypeModel.LobbyistEmployerReport.Name
            });
        }

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        if (model.RegistrationFilingId != 0)
        {
            return RedirectToAction("PaymentToLobbyingCoalitionPage03"
                , Transaction
                , new { model.FilerId, model.RegistrationFilingId, model.ContactId, model.FilingId, model.ReturnUrl });
        }

        try
        {
            var newContactId = await _transactionCtlSvc.HandlePaymentToLobbyingCoalitionPage02Submit(model, cancellationToken);
            model.ContactId = newContactId;

            return RedirectToAction("PaymentToLobbyingCoalitionPage03"
                , Transaction
                , new { model.FilerId, model.RegistrationFilingId, model.ContactId, model.FilingId, model.ReturnUrl });
        }
        catch (Exception)
        {
            _toastService.Error(_localizer[ResourceConstants.APIRequestError]);
            ViewBag.Title = _localizer[ResourceConstants.LobbyingCoalitionTransactionTitle];
            return View("PaymentToLobbyingCoalition/Page02", model);
        }
    }

    /// <summary>
    /// Handles the creation of a payment made to a lobbying coalition Page 03.
    /// </summary>
    /// <returns>The view for the create payment to a lobbying coalition page 03.</returns>
    [HttpGet(PaymentToLobbyingCoalitionPage03View)]
    public IActionResult PaymentToLobbyingCoalitionPage03(
    [FromQuery] long filerId,
    [FromQuery] long contactId,
    [FromQuery] long registrationFilingId,
    [FromQuery] long filingId,
    [FromQuery] string? returnUrl)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = filerId,
            RegistrationFilingId = registrationFilingId,
            FilingId = filingId,
            ContactId = contactId,
            ReturnUrl = returnUrl
        };

        ViewBag.Title = _localizer[ResourceConstants.LobbyingCoalitionTransactionTitle];
        return View(PaymentToLobbyingCoalitionPage03View, model);
    }

    /// <summary>
    /// Handles the creation of a payment made to a lobbying coalition Page 03.
    /// </summary>
    /// <returns>Handling Create Payment Amount for lobbying coalition page 03.</returns>
    [HttpPost(PaymentToLobbyingCoalitionPage03View)]
    public async Task<IActionResult> PaymentToLobbyingCoalitionPage03(PaymentMadeToLobbyingCoalitionViewModel model,
        [FromServices] ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
        FormAction? action = null,
        CancellationToken cancellationToken = default)
    {
        ViewBag.Title = _localizer[ResourceConstants.LobbyingCoalitionTransactionTitle];
        var controller = await GetDisclosureControllerName(model.FilingId.GetValueOrDefault());

        if (action == FormAction.Cancel)
        {
            _toastService.Success(_localizer[ResourceConstants.CancelTransactionSuccessMessage]);
            return RedirectToAction(Index, controller, new
            {
                filerId = model.FilerId,
                viewName = FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name,
                filingId = model.FilingId,
                reportType = FilingTypeModel.LobbyistEmployerReport.Name
            });
        }

        if (!ModelState.IsValid)
        {
            return View(PaymentToLobbyingCoalitionPage03View, model);
        }

        if (model.Amount == null)
        {
            string amountField = nameof(PaymentMadeToLobbyingCoalitionViewModel.Amount);
            ModelState.AddModelError(amountField, string.Format(CultureInfo.InvariantCulture, _localizer[ResourceConstants.FieldIsRequired].Value, amountField));
            return View(PaymentToLobbyingCoalitionPage03View, model);
        }

        try
        {
            double? amountVal = model.Amount.HasValue ? Convert.ToDouble(model.Amount, CultureInfo.InvariantCulture) : null;
            var request = new PaymentMadeToLobbyingCoalitionRequestDto(amountVal
                , model.ContactId != 0 ? model.ContactId : null
                , model!.FilerId!.Value
                , model!.FilingId!.Value
                , model.RegistrationFilingId != 0 ? model.RegistrationFilingId : null
                , model.Id);

            var response = await _transactionCtlSvc.HandlePaymentToLobbyingCoalitionPage03Submit(request, ModelState, cancellationToken);

            if (response.Valid)
            {
                if (model.Id is not null)
                {
                    _toastService.Success(_localizer[DisclosureConstants.Controller.UpdateTransactionSuccessMessageKey]);
                    return RedirectToAction(
                        Index,
                        controller,
                        new
                        {
                            viewName = FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name,
                            filerId = model.FilerId,
                            filingId = model.FilingId,
                            reportType = FilingTypeModel.LobbyistEmployerReport.Name,
                            filingSummaryId = model.FilingSummaryId
                        });
                }

                return Redirect(model!.FilerId!.Value, model!.ReturnUrl!.ToString());
            }
            return View(PaymentToLobbyingCoalitionPage03View, model);
        }
        catch (Exception)
        {
            _toastService.Error(_localizer[ResourceConstants.APIRequestError]);
            return View(PaymentToLobbyingCoalitionPage03View, model);
        }
    }

    /// <summary>
    /// Handles the creation of a payment made to a lobbying coalition Page 03.
    /// </summary>
    /// <returns>The view for the create payment to a lobbying coalition page 03.</returns>
    [HttpGet("EditPaymentToLobbyingCoalitionPage03")]
    public async Task<IActionResult> EditPaymentToLobbyingCoalitionPage03(
    [FromQuery] long id,
    [FromQuery] long filerId,
    [FromQuery] long contactId,
    [FromQuery] long registrationFilingId,
    [FromQuery] long filingId,
    [FromServices] ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
    [FromQuery] string? returnUrl)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = filerId,
            RegistrationFilingId = registrationFilingId,
            FilingId = filingId,
            ContactId = contactId,
            ReturnUrl = returnUrl,
            Id = id
        };

        var transaction = await lobbyistEmployerCoalitionApi.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(id);
        model.Amount = (decimal?)transaction.AmountThisPeriod;
        ViewBag.Title = _localizer[ResourceConstants.LobbyingCoalitionTransactionTitle];
        return View(PaymentToLobbyingCoalitionPage03View, model);
    }
    #region Payments received by lobbying coalitions
    [HttpGet("{reportType}/EditPaymentReceivedByLobbyingCoalition/{filingId:long}/{filerId:long}/{transactionId:long}")]
    public async Task<ActionResult> EditPaymentReceivedByLobbyingCoalition(
        [FromRoute] string? reportType,
        [FromRoute] string filingId,
        [FromRoute] string filerId,
        [FromRoute] long transactionId,
        [FromServices] ILobbyistEmployerCoalitionApi lobbyistEmployerCoalitionApi,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        try
        {
            var result = await lobbyistEmployerCoalitionApi.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId, cancellationToken);

            return RedirectToAction(EditTransactionContactAction, EditTransactionContactController, new { reportType, filerId, filingId, contactId = result.Contact.Id, transactionId });
        }
        catch
        {
            return NotFound();
        }
    }
    #endregion

    #region Edit other payment to influence transaction
    [HttpGet("EditOtherPaymentToInfluenceTransaction/{filingId:long}/{filerId:long}/{transactionId:long}")]
    public async Task<ActionResult> EditOtherPaymentToInfluenceTransaction(
        [FromRoute] string filingId,
        [FromRoute] string filerId,
        [FromRoute] long transactionId,
        [FromServices] ITransactionsApi transactionApi,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        try
        {
            var context = "OtherPayment";
            var returnUrl = $"/{DisclosureAction}?filerId={filerId}&filingId={filingId}&viewName={FilingSummaryTypeModel.OtherPaymentsToInfluenceSummary.Name}";
            var result = await transactionApi.GetOtherPaymentsToInfluenceTransactionById(transactionId, cancellationToken);

            return RedirectToAction(EditOtherPaymentContactAction,
                                    EditOtherPaymentContactController,
                                    new { filerId, filingId, contactId = result.ContactId, transactionId, context, returnUrl });
        }
        catch
        {
            return NotFound();
        }
    }
    #endregion

    #region 48 Hour End-of-session Lobbying Report Transaction

    /// <summary>
    /// Handles the creation of an end-of-session lobbying transaction made to a 48H report.
    /// </summary>
    /// <returns>The view for the creation of an end-of-session lobbying transaction made to a 48H report.</returns>
    [HttpGet("Report48HEosTransaction")]
    public async Task<IActionResult> Report48HEosTransaction(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long? contactId,
        [FromQuery] long? registrationFilingId,
        [FromServices] IFirmPaymentTransactionCtlSvc firmPaymentTransactionCtlSvc)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var assyBills = GetTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey);
        var senateBills = GetTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey);

        var model = new Report48HTransactionViewModel
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            LegislationAssyBillGridModel = CreateLegislationBillTableModel(LegislationAssyBillTempDataKey, assyBills ?? [], DeleteAssemblyBillFunction),
            LegislationSenateBillGridModel = CreateLegislationBillTableModel(LegislationSenateBillTempDataKey, senateBills ?? [], DeleteSenateBillFunction),
            Contact = await firmPaymentTransactionCtlSvc.GetContactViewModel(contactId, registrationFilingId),
        };

        return View("Report48HEosTransaction", model);
    }

    /// <summary>
    /// Handles the edit of an end-of-session lobbying transaction made to a 48H report.
    /// </summary>
    /// <returns>The view for the edit of an end-of-session lobbying transaction made to a 48H report.</returns>
    [HttpGet("EditReport48HEosTransaction")]
    public async Task<IActionResult> EditReport48HEosTransaction(
        [FromQuery] EditReport48HEosTransactionParameters parameters,
        [FromServices] IFirmPaymentTransactionCtlSvc firmPaymentTransactionCtlSvc)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var response = await _transactionsApi.GetEndOfSessionLobbyingTransactionById(parameters.TransactionId);

        var assyBills = response.ActionsLobbied
            .Where(a => a.Bill.BillHouse.Name == BillHouse.Assembly.Name)
            .Select(a => new LegislativeBillViewModel(a));
        var senateBills = response.ActionsLobbied
            .Where(a => a.Bill.BillHouse.Name == BillHouse.Senate.Name)
            .Select(a => new LegislativeBillViewModel(a));

        SetTempData(LegislationAssyBillTempDataKey, assyBills);
        SetTempData(LegislationSenateBillTempDataKey, senateBills);

        var model = new Report48HTransactionViewModel
        {
            ReportType = parameters.ReportType,
            Id = parameters.TransactionId,
            FilingId = parameters.FilingId,
            FilerId = parameters.FilerId,
            ContactId = parameters.ContactId,
            RegistrationFilingId = parameters.RegistrationFilingId,
            LegislationAssyBillGridModel = CreateLegislationBillTableModel(LegislationAssyBillTempDataKey, [.. assyBills], DeleteAssemblyBillFunction),
            LegislationSenateBillGridModel = CreateLegislationBillTableModel(LegislationSenateBillTempDataKey, [.. senateBills], DeleteSenateBillFunction),
            Contact = await firmPaymentTransactionCtlSvc.GetContactViewModel(parameters.ContactId, parameters.RegistrationFilingId),
            Amount = (decimal)response.Amount,
            DateLobbyingFirmHired = response.DateLobbyingFirmHired
        };

        return View("Report48HEosTransaction", model);
    }

    /// <summary>
    /// Handles redirects or save of an end-of-session lobbying transaction made to a 48H report.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost("Report48HEosTransaction")]
    public async Task<IActionResult> Report48HEosTransaction(
        Report48HTransactionViewModel model,
        string action,
        [FromServices] ITransactionsApi transactionsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return action switch
        {
            "Previous" => RedirectToAction("CreateEditLobbyingFirm", Contact,
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId,
                    transactionId = model.Id,
                    registrationFilingId = model.RegistrationFilingId
                }),
            "Save" => await HandleSaveReport48HEosTransaction(model, transactionsApi),
            _ => await HandleCancelReport48HEosTransaction(model)
        };
    }

    /// <summary>
    /// Handles the save transaction action by displaying a success message and redirecting to the transaction summary.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> HandleSaveReport48HEosTransaction(Report48HTransactionViewModel model, ITransactionsApi transactionsApi)
    {
        if (model.FilingId == null || model.FilerId == null)
        {
            return NotFound();
        }

        var request = CreateEndOfSessionLobbyingRequest(model);

        var result = model.Id.HasValue ?
            await transactionsApi.EditEndOfSessionLobbying(model.Id.Value, request) :
            await transactionsApi.CreateEndOfSessionLobbying(request);

        if (result.Valid)
        {
            _toastService.Success(_localizer[ResourceConstants.CreateTransactionSuccessMessage]);
        }
        else
        {
            SyncDecisionsValidationsWithViewModel([.. result.ValidationErrors], model);
            model.LegislationAssyBillGridModel = CreateLegislationBillTableModel(LegislationAssyBillTempDataKey, GetTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey) ?? [], DeleteAssemblyBillFunction);
            model.LegislationSenateBillGridModel = CreateLegislationBillTableModel(LegislationSenateBillTempDataKey, GetTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey) ?? [], DeleteSenateBillFunction);
            return View("Report48HEosTransaction", model);
        }

        TempData.Remove(AdministrativeActionTempDataKey);
        TempData.Remove(LegislationAssyBillTempDataKey);
        TempData.Remove(LegislationSenateBillTempDataKey);
        return await RedirectTo48HEosTransactionSummary(model);
    }

    /// <summary>
    /// Handles the cancel transaction action by displaying a success message and redirecting to the transaction summary.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> HandleCancelReport48HEosTransaction(FirmPaymentTransactionViewModel model)
    {
        _toastService.Success(_localizer[ResourceConstants.CancelTransactionSuccessMessage]);
        return await RedirectTo48HEosTransactionSummary(model);
    }

    /// <summary>
    /// Redirects to the 48 Hour end-of-session lobbying transaction summary page.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<IActionResult> RedirectTo48HEosTransactionSummary(FirmPaymentTransactionViewModel model)
    {
        var isAmendment = await GetIsAmendment((long)model.FilingId!);
        var controller = isAmendment ? "AmendDisclosure" : "Disclosure";
        return RedirectToAction("Index", controller, new
        {
            viewName = FilingSummaryTypeModel.EndOfSessionLobbyingSummary.Name,
            reportType = model.ReportType,
            filingId = model.FilingId,
            filerId = model.FilerId
        });
    }

    private EndOfSessionLobbyingRequestDto CreateEndOfSessionLobbyingRequest(Report48HTransactionViewModel model)
    {
        var senateBills = GetAndKeepTempData<List<LegislativeBillViewModel>>(LegislationSenateBillTempDataKey) ?? [];
        var assyBills = GetAndKeepTempData<List<LegislativeBillViewModel>>(LegislationAssyBillTempDataKey) ?? [];

        var senateBillsActionsLobbied = BuildBillActionsLobbied(senateBills);
        var assyBillsActionsLobbied = BuildBillActionsLobbied(assyBills);

        return new EndOfSessionLobbyingRequestDto(
            model.Amount != null ? (double)model.Amount : null,
            assyBillsActionsLobbied,
            model.ContactId,
            model.DateLobbyingFirmHired,
            model.FilerId ?? 0,
            model.FilingId ?? 0,
            model.RegistrationFilingId,
            senateBillsActionsLobbied);
    }

    /// <summary>
    /// Navigates to the edit End of Session form.
    /// </summary>
    /// <returns>A Task of <see cref="RedirectToActionResult"/> with the result of the request.</returns>
    [HttpGet]
    [Route("EditEndOfSessionLobbyingContact/{id:long}")]
    public async Task<IActionResult> EditEndOfSessionLobbyingContact(
        [FromRoute] long id,
        [FromServices] ITransactionsApi transactionsApi)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var response = await transactionsApi.GetEndOfSessionLobbyingTransactionById(id);
        return RedirectToAction("CreateEditLobbyingFirm", Contact, new
        {
            reportType = FilingTypeModel.Report48h.Name,
            filingId = response.FilingId,
            filerId = response.FilerId,
            contactId = response.Contact.Id,
            transactionId = response.Id,
            registrationFilingId = response.RegistrationId
        });
    }
    #endregion

    #region Search Actions

    /// <summary>
    /// Search filer contact for select contact search component.
    /// </summary>
    /// <param name="contactsApi"></param>
    /// <param name="search"></param>
    /// <param name="filerId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<JsonResult> SearchFilerContactsByIdOrName(
        [FromServices] IContactsApi contactsApi,
        [FromQuery] string search,
        [FromQuery] long filerId,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var contactTypes = new List<string> { "Individual", "Organization" };
        var data = await contactsApi.SearchContactsByNameOrId(filerId, search, contactTypes, cancellationToken);
        return new JsonResult(data);
    }

    /// <summary>
    /// The first page in the Payment made to lobbying coalition disclosure transaction process.
    /// This is the start of the process for a new transaction
    /// </summary>
    /// <param name="contactsApi">The refitter generated http client for the Contacts endpoint in the WebApi project</param>
    /// <param name="search">Search text input. Partial of lobbying coalition name or ID#.</param>
    /// <param name="cancellationToken">Standard dotnet cancellationToken for async operations</param>
    /// <returns></returns>
    [HttpGet("SearchLobbyingCoalitionByIdOrName")]
    public async Task<JsonResult> SearchLobbyingCoalitionByIdOrName(
        [FromServices] IContactsApi contactsApi,
        [FromQuery] string search,
        [FromQuery] long filerId,
        CancellationToken cancellationToken
    )
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var data = await contactsApi.SearchLobbyingCoalitionByNameOrId(search, filerId, cancellationToken);
        return new JsonResult(data);
    }

    [HttpGet("SearchReportablePersonsByName")]
    public async Task<JsonResult> SearchReportablePersonsByName(
        [FromServices] IActivityExpenseApi activityExpenseApi,
        [FromQuery] string search,
        [FromQuery] long filerId,
        [FromQuery] long contactId,
        CancellationToken cancellationToken
    )
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var data = await activityExpenseApi.SearchReportablePersonsByName(filerId, contactId, search, cancellationToken);
        return new JsonResult(data);
    }

    [HttpGet("SearchAllAgencies")]
    public async Task<JsonResult> SearchAllAgencies(
    [FromServices] IReferenceDataApi referenceDataApi,
    [FromQuery] string search,
    CancellationToken cancellationToken
    )
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var agencies = await referenceDataApi.SearchAllAgencies(search, cancellationToken);

        var data = agencies.Select(a => new
        {
            a.Id,
            a.Name
        });

        return new JsonResult(data);
    }

    [HttpGet("SearchAllBills")]
    public async Task<JsonResult> SearchAllBills(
    [FromServices] IReferenceDataApi referenceDataApi,
    [FromQuery] string search,
    CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var bills = await referenceDataApi.SearchAllBills(search, cancellationToken);

        var data = bills.Select(a => new
        {
            a.Id,
            a.BillHouseId,
            a.Title,
            a.Number
        });

        return new JsonResult(data);
    }

    [HttpGet("SearchFilersByTypeAndQuery")]
    public async Task<JsonResult> SearchFilersByTypeAndQuery(
    [FromQuery] string query,
    [FromQuery] long filerTypeId,
    [FromServices] IFilingsApi filingsApi,
    CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new List<object>());
        }

        var filers = await filingsApi.SearchFilers(query, filerTypeId, cancellationToken);
        return new JsonResult(filers);
    }

    #endregion

    #region Helpers

    private void SyncDecisionsValidationsWithViewModel(
    List<WorkFlowError> decisions,
    IPortalAlertsContainer viewModel
    )
    {
        if (decisions.Any())
        {
            for (int i = 0; i < decisions.Count; i++)
            {
                var dsmNew = decisions[i];
                this.AddDecisionValidationToViewModel(viewModel, new ValidationMessage { ErrorCode = dsmNew.ErrorCode, FieldName = dsmNew.FieldName ?? dsmNew.ErrorCode, Message = dsmNew.Message, Type = dsmNew.ErrorType });
            }
        }
    }

    public static Dictionary<string, string> GetActivityExpenseTypesDictionary(IReadOnlyList<ActivityExpenseTypeResponse> activityExpenseTypes)
    {
        return activityExpenseTypes
            .ToDictionary(
                activityExpenseType => activityExpenseType.Id.ToString(CultureInfo.InvariantCulture),
                activityExpenseType => activityExpenseType.Name
            );
    }

    public static void InitializeCommitteeAutoCompleteModel(LobbyingCampaignContributionViewModel model, IReadOnlyList<RegistrationItemResponse> committees)
    {
        List<RegistrationItemResponse> committeeDropdownOptions = [.. committees.Where(registrationItem => registrationItem.FilerId.HasValue)];
        model.RecipientCommitteeFilerAutoCompleteModel = new AutoCompleteModel<RegistrationItemResponse>(committeeDropdownOptions, RecipientCommitteeFilerIdName, "Name", "FilerId", "Select Committee");

        var initialCommitteeId = model.RecipientCommitteeFilerId;

        if (initialCommitteeId != null)
        {
            model.RecipientCommitteeFilerAutoCompleteModel.InitialValue = committeeDropdownOptions.FirstOrDefault(x => x.FilerId == initialCommitteeId);
        }

        model.RecipientCommitteeFilerAutoCompleteModel.RefreshBaseOptions();
    }

    private static LobbyistCampaignContributionViewModel MapLobbyistCampaignContributionItemResponseToViewModel(LobbyistCampaignContributionItemResponse response)
    {
        return new LobbyistCampaignContributionViewModel
        {
            Amount = (decimal?)response.Amount,
            TransactionDate = response.TransactionDate,
            IsContributorFiler = response.IsContributorFiler,
            NonFilerContributorName = response.NonFilerContributorName,
            ContributorFilerId = response.ContributorFilerId,
            SeparateAccountName = response.SeparateAccountName,
            IsRecipientCommittee = response.IsRecipientCommittee,
            NonCommitteeRecipientName = response.RecipientName,
            RecipientCommitteeFilerId = response.RecipientFilerId,
            FilerId = response.FilerId,
            FilingId = (long)(response.FilingId is not null ? response.FilingId : 0),
            Id = response.Id
        };
    }

    /// <summary>
    /// <param name="key">The key in TempData that stores data.</param>
    /// Gets a string from Tempdata and converts to and object of type TOutput.
    /// </summary>
    private TOutput? GetAndKeepTempData<TOutput>(string key)
    {
        var existingData = TempData.Peek(key) as string;
        if (!string.IsNullOrEmpty(existingData))
        {
            return JsonConvert.DeserializeObject<TOutput>(existingData);
        }
        return default;
    }
    private static List<ActionsLobbiedRequestDto> BuildBillActionsLobbied(List<LegislativeBillViewModel> bills)
    {
        return bills.Select(bill => new ActionsLobbiedRequestDto(
            administrativeAction: null!,
            agencyDescription: null!,
            agencyId: null,
            billId: bill.BillId,
            lobbyingAdvertisementSubjects: new List<string>(),
            id: null,
            officialPositionDescription: bill.OfficialPositionDescription ?? string.Empty,
            officialPositionId: bill.OfficialPositionId
        )).ToList();
    }
    private async Task<string> GetDisclosureControllerName(long filingId)
    {
        var filing = await _filingsApi.GetFiling(filingId);
        return (filing?.Version > 0) ? "AmendDisclosure" : "Disclosure";
    }

    private async Task<bool> GetIsAmendment(long filingId)
    {
        var filing = await _filingsApi.GetFiling(filingId);
        return filing?.Version > 0;
    }

    #endregion

    public class EditReport48HEosTransactionParameters
    {
        [FromQuery(Name = "reportType")]
        public required string ReportType { get; set; }

        [FromQuery(Name = "filingId")]
        public required long FilingId { get; set; }

        [FromQuery(Name = "filerId")]
        public required long FilerId { get; set; }

        [FromQuery(Name = "transactionId")]
        public required long TransactionId { get; set; }

        [FromQuery(Name = "contactId")]
        public long? ContactId { get; set; }

        [FromQuery(Name = "registrationFilingId")]
        public long? RegistrationFilingId { get; set; }
    }

}

