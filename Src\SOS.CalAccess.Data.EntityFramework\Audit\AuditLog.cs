// <copyright file="AuditLog.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models;
using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Data.EntityFramework.Audit;

/// <summary>
/// A class for representing logs of actions
/// performed by actors in the system.
/// </summary>
[Table("AuditLog")]
[Documentation("", Context = "This table holds system audit log entries.")]
public sealed class AuditLog
{
    /// <summary>
    /// Name for the internal property name used as a resource tag
    /// for binding audit logs to entities.
    /// </summary>
    internal const string ResourceTag = "AuditableResourceTag";

    /// <summary>
    /// Annotation tag for entities and properties that should be excluded
    /// from the audit logs.
    /// </summary>
    internal const string NoAudit = "MapLight.Audit.Disable";

    /// <summary>
    /// Initializes a new instance of the <see cref="AuditLog"/> class.
    /// </summary>
    /// <param name="action">The action to audit.</param>
    /// <param name="attributedTo">The actor to attribute the action to.</param>
    /// <param name="error">Optional error message if the action failed.</param>
    [SetsRequiredMembers]
    public AuditLog(
        IAuditableAction action,
        IDateTimeSvc dateTimeSvc,
        long? attributedTo = default,
        string? error = default)
    {
        Subject = action.Subject;
        ActorId = attributedTo;
        ResourceId = action.ResourceId;
        ActionType = action.Type;
        IssuedAt = action.IssuedAt;
        Diff = action.Diff;
        Error = error;
        CompletedAt = dateTimeSvc.GetCurrentDateTime();
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="AuditLog"/> class.
    /// </summary>
    /// <remarks>
    /// This code can be safely excluded from coverage as it is here only to satisfy
    /// EF Core's requirements for entity constructors.
    /// </remarks>
    [ExcludeFromCodeCoverage]
    private AuditLog()
    {
    }

    /// <summary>
    /// Gets this audit entry's primary identifier.
    /// </summary>
    [Documentation("Audit log identifier.")]
    public int Id { get; init; }

    /// <summary>
    /// Gets the error, if any, that was encountered when
    /// the action audited by this entry was performed.
    /// </summary>
    [Documentation("Error encountered when action was performed, if any.")]
    public string? Error { get; init; }

    /// <summary>
    /// Gets the resource type name over which
    /// the action audited by this entry was performed.
    /// </summary>
    [MaxLength(64)]
    [Documentation("Resource type name.")]
    public required string Subject { get; init; }

    /// <summary>
    /// Gets the unique auditable resource tag of the resource upon which this action
    /// was performed.
    /// </summary>
    [MaxLength(64)]
    [Documentation(
        "Unique resource tag.",
        Context = "Reference to a string-serialized unique identifier used to link audit logs to arbitrary resource types.")]
    public required string ResourceId { get; init; }

    /// <summary>
    /// Gets the id of the system actor who the
    /// action audited by this entry was attributed to.
    /// </summary>
    [Documentation("System actor identifier.", Context = "Reference to a system actor.")]
    public long? ActorId { get; init; }

    /// <summary>
    /// Gets the system actor who the action
    /// audited by this entry was attributed to.
    /// </summary>
    public Actor? Actor { get; init; }

    /// <summary>
    /// Gets the action type that was audited by this entry.
    /// </summary>
    [MaxLength(64)]
    [Documentation("Action type.")]
    public required string ActionType { get; init; }

    /// <summary>
    /// Gets the timestamp detailing the time
    /// at which the action audited by this entry happened.
    /// </summary>
    [Documentation("Start time of the logged operation.")]
    public required DateTime IssuedAt { get; init; }

    /// <summary>
    /// Gets the timestamp detailing the time
    /// at which the action audited by this entry was completed.
    /// This usually refers to the time at which any related outstanding changes
    /// were persisted or finished being processed.
    /// </summary>
    [Documentation("Completion time of the logged operation.")]
    public DateTime CompletedAt { get; init; }

    /// <summary>
    /// Gets a serialized version of a before-after
    /// comparison for the resource instance that was affected
    /// by the action audited by this entry.
    /// </summary>
    [Documentation("Serialized version of before-after comparison for the resource.")]
    public string? Diff { get; init; }
}
