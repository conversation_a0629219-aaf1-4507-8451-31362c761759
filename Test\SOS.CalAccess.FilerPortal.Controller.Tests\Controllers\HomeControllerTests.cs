using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.Models;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class HomeControllerTests
{
    private ILogger<HomeController> _logger;
    private IConfiguration _configuration;
    private HomeController _controller;
    private IDateTimeSvc _dateTimeSvc;
    [SetUp]
    public void Setup()
    {
        _logger = Substitute.For<ILogger<HomeController>>();
        _configuration = Substitute.For<IConfiguration>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _controller = new HomeController(_logger, _configuration, _dateTimeSvc);
    }

    [TearDown]
    public void TearDown()
    {
        _controller.Dispose();
    }

    [Test]
    public void ReferencePage_ReturnsViewWithExpectedModel()
    {
        var referencePage = _controller.ReferencePage() as ViewResult;

        Assert.That(referencePage, Is.Not.Null);
        Assert.That(referencePage.Model, Is.InstanceOf<ReferenceViewModel>());
    }

    [Test]
    public void UnderConstruction_ReturnsExpectedView()
    {
        var result = _controller.UnderConstruction() as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ViewName, Is.EqualTo("/Views/Shared/_UnderConstruction.cshtml"));
    }
}
