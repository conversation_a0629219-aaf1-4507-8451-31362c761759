using SOS.CalAccess.UI.Common.Enums;

namespace SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration;

public class AddLobbyingFirmsViewModel
{
    /// <summary>
    /// Lobbyist Employer registration id
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// Gets or sets the list of lobbyist firm.
    /// </summary>
    public Dictionary<long, string>? LobbyingFirms { get; set; }

    /// <summary>
    /// Gets or sets the list of lobbyist firm Id's selected.
    /// </summary>
    public List<long>? SelectedLobbyingFirms { get; set; }
}
