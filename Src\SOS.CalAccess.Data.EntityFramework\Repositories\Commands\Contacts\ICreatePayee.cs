// <copyright file="ICreatePayee.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;

/// <summary>
/// Command handler interface for creating a payee for a filer.
/// </summary>
public interface ICreatePayee : ICommand<CreatePayeeCommand, IResult<FilerContact>>;

/// <summary>
/// Command implementation of the <see cref="ICreatePayee"/> interface.
/// </summary>
/// <param name="db">The database context.</param>
/// <param name="decisions">Business logic engine.</param>
/// <param name="auditService">High level audit facilities.</param>
/// <param name="logger">The logger instance.</param>
public sealed class CreatePayee(
    DatabaseContext db,
    IDecisionsService decisions,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc,
    ILogger<CreateContact> logger) : ICreatePayee
{
    /// <inheritdoc />
    public async ValueTask<IResult<FilerContact>> Execute(
        CreatePayeeCommand input,
        CancellationToken cancellationToken = default)
    {
        var filer = await db.Filers.FirstOrDefaultAsync(f => f.Id == input.FilerId, cancellationToken);

        if (filer is null)
        {
            logger.LogWarning("Filer was not found.");
            return new Failure<FilerContact>.NotFound("No filer was found with the requested id");
        }

        var contact = input.Build();

        _ = await decisions.Execute(new("Contact.Create", Context: contact), cancellationToken);

        db.FilerContacts.Add(contact);

        await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Create", contact.GetType().Name, contact.Id.ToString(), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<FilerContact>(contact);
    }
}
