// <copyright file="IUpdateFiling.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;

/// <summary>
/// Command handler interface for updating a filing in the system.
/// </summary>
public interface IUpdateFiling : ICommand<UpdateFilingCommand, IResult<Filing>>;

/// <summary>
/// Command handler implementation for updating a filing in the system.
/// </summary>
/// <param name="db">The database context.</param>
/// <param name="auditService">High level audit facilities.</param>
public sealed class UpdateFiling(
    DatabaseContext db,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc) : IUpdateFiling
{
    /// <inheritdoc />
    public async ValueTask<IResult<Filing>> Execute(
        UpdateFilingCommand input,
        CancellationToken cancellationToken = default)
    {
        var filing = await db.Filings.FirstOrDefaultAsync(f => f.Id == input.Id, cancellationToken);

        if (filing is null)
        {
            return new Failure<Filing>.NotFound("No filing was found with the requested id.");
        }

        // Only filings in "Draft" status can be updated.
        //if (filing.StatusId != FilingStatus.Draft.Id)
        //{
        //    return new Failure<Filing>.InvalidState(
        //        FilingStatus.Draft.Id,
        //        filing.StatusId,
        //        "Only filings in 'Draft' status can be updated.");
        //}

        if (input.Apply(filing).Unwrap(out _, out var failure))
        {
            return failure;
        }

        await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Update", filing.GetType().Name, filing.Id.ToString(), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<Filing>(filing);
    }
}
