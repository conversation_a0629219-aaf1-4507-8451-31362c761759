using System.Globalization;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Mapping;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using SOS.CalAccess.Services.Common.Utility;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
public class CisRegistrationWithdrawalSvc(
    IDateTimeSvc dateTimeSvc,
    IRegistrationRepository registrationRepository,
    IAttestationRepository attestationRepository,
    IFilerRepository filerRepository,
    INotificationWrapperSvc notificationWrapperSvc,
    IDecisionsSvc decisionsSvc,
    IAuthorizationSvc authorizationSvc
    ) : ICisRegistrationWithdrawalSvc
{
    /// <inheritdoc />
    public async Task<long> CreateCisWithdrawal(long registrationId)
    {
        var registration = await registrationRepository
            .FindById(registrationId) ?? throw new KeyNotFoundException($"Registration with ID {registrationId} was not found.");

        var latestRegistration = await registrationRepository
            .GetLatestAcceptedCisRegistrationByFilerId(registration.FilerId!.Value) ?? throw new InvalidOperationException($"No accepted CIS registration found for FilerId {registration.FilerId.Value}.");

        var cisWithdrawal = new CisWithdrawal
        {
            Name = latestRegistration.Name,
            FilerId = latestRegistration.FilerId,
            StatusId = RegistrationStatus.Draft.Id,
        };

        await registrationRepository.Create(cisWithdrawal);

        return cisWithdrawal.Id;
    }

    /// <inheritdoc />
    public async Task<CisRegistrationWithdrawalDto> GetCisWithdrawal(long registrationId)
    {
        var registration = await registrationRepository.GetCisRegistrationWithdrawalById(registrationId);

        if (registration == null || registration.FilerId == null)
        {
            return new CisRegistrationWithdrawalDto();
        }

        var latestAcceptedRegistration = await registrationRepository
            .GetLatestAcceptedCisRegistrationByFilerId(registration.FilerId.Value);

        if (latestAcceptedRegistration == null)
        {
            return new CisRegistrationWithdrawalDto();
        }

        latestAcceptedRegistration.Id = registrationId;

        return new CisRegistrationWithdrawalDto(latestAcceptedRegistration);
    }


    /// <inheritdoc />
    public async Task CancelCisWithdrawal(long registrationId)
    {
        var registration = await registrationRepository.FindById(registrationId) ?? throw new KeyNotFoundException($"Registration with ID {registrationId} was not found.");

        if (registration is not CisWithdrawal)
        {
            throw new InvalidOperationException("The registration is not a CIS Withdrawal.");
        }

        if (registration.StatusId != RegistrationStatus.Draft.Id && registration.StatusId != RegistrationStatus.Pending.Id)
        {
            throw new InvalidOperationException("Only Draft or Pending registrations can be canceled.");
        }

        registration.StatusId = RegistrationStatus.Canceled.Id;
        registration.SubmittedAt = dateTimeSvc.GetCurrentDateTime();

        await registrationRepository.Update(registration);
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> SendForAttestationCisWithdrawal(long registrationId)
    {
        var registration = await GetValidRegistration(registrationId);
        var filer = await GetValidFiler(registration.FilerId!.Value);

        // Call decisions to get notification templates
        var decisionResponse = await decisionsSvc
            .InitiateWorkflow<string, StandardDecisionsSubmissionResponse>(DecisionsWorkflow.CISWithdrawalSendForAttestationRuleset, string.Empty, true)
            ?? throw new InvalidOperationException($"Unable to get decisions notification templates");

        if (decisionResponse?.Errors?.Count > 0)
        {
            return new RegistrationResponseDto(registration.Id, false, decisionResponse.Errors, registration.StatusId, decisionResponse.Notifications);
        }

        await UpdateStatuses(registration, filer, decisionResponse!.Status, false);
        await SendNotifications(registrationId, filer.Id, decisionResponse!.Notifications);

        return new RegistrationResponseDto(
            registration.Id,
            true,
            decisionResponse.Errors,
            registration.StatusId,
            decisionResponse.Notifications);
    }

    /// <inheritdoc />
    public async Task<List<PendingItemDto>> GetPendingItems(long registrationId)
    {
        var pendingItems = new List<PendingItemDto> { };

        var registration = await GetValidRegistration(registrationId);

        var attestation = await attestationRepository.FindByRegistrationId(registrationId);
        if (attestation is null)
        {
            pendingItems.Add(new PendingItemDto { Item = "Attestation", Status = "In Progress" });
        }

        return pendingItems;
    }
    /// <inheritdoc />
    public async Task<RegistrationResponseDto> AttestationCisWithdrawal(CisRegistrationAttestationWithdrawalDto cisRegistrationAttestationWithdrawalDto)
    {
        var registration = await GetValidRegistration(cisRegistrationAttestationWithdrawalDto.RegistrationId);
        var filer = await GetValidFiler(registration.FilerId!.Value);

        await CreateAttestation(cisRegistrationAttestationWithdrawalDto.RegistrationId, registration.Name);

        var decisionResponse = await decisionsSvc
            .InitiateWorkflow<CisRegistrationAttestationWithdrawalDto, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.CISWithdrawalAttestationSubmitRuleset, cisRegistrationAttestationWithdrawalDto, true);

        if (decisionResponse?.Errors?.Count > 0)
        {
            return new RegistrationResponseDto(registration.Id, false, decisionResponse.Errors, registration.StatusId, decisionResponse.Notifications);
        }

        await UpdateStatuses(registration, filer, decisionResponse!.Status, true);
        await SendNotifications(cisRegistrationAttestationWithdrawalDto.RegistrationId, filer.Id, decisionResponse!.Notifications);

        return new RegistrationResponseDto(
            registration.Id,
            true,
            decisionResponse.Errors,
            registration.StatusId,
            decisionResponse.Notifications);
    }

    # region Private Methods

    private async Task<CisWithdrawal> GetValidRegistration(long registrationId)
    {
        var registration = await registrationRepository.FindById(registrationId);
        if (registration is not CisWithdrawal cisWithdrawal)
        {
            throw new InvalidOperationException("The registration is not a CIS Withdrawal.");
        }

        return cisWithdrawal;
    }

    private async Task<Filer> GetValidFiler(long filerId)
    {
        return await filerRepository.FindById(filerId)
               ?? throw new KeyNotFoundException($"Filer with ID {filerId} was not found.");
    }

    private async Task<Attestation> CreateAttestation(long registrationId, string? name)
    {
        var attestation = new Attestation
        {
            Name = name,
            RegistrationId = registrationId,
            CreatedBy = 0,
            ModifiedBy = 0,
            ExecutedAt = DateTime.Now
        };

        await attestationRepository.Create(attestation);
        return attestation;
    }

    private async Task UpdateStatuses(CisWithdrawal registration, Filer filer, string? decisionStatus, bool isAttesting)
    {
        if (!string.IsNullOrWhiteSpace(decisionStatus))
        {
            registration.StatusId = RegistrationStatusMapping.GetIdByName(decisionStatus);

            if (isAttesting)
            {
                registration.ApprovedAt = DateTime.UtcNow.ToDefaultTimeZone();
                filer.FilerStatusId = FilerStatus.Withdrawn.Id;
                await filerRepository.Update(filer);
            }
            await registrationRepository.Update(registration);

        }
    }

    private async Task SendNotifications(long registrationId, long filerId, List<NotificationTrigger>? notifications)
    {
        if (notifications == null || notifications.Count == 0)
        {
            return;
        }

        var userId = await authorizationSvc.GetInitiatingUserId();
        var notificationData = new Dictionary<string, string>
            {
                { NotificationTemplateConstants.DocumentIdPlaceholder, registrationId.ToString(CultureInfo.InvariantCulture) }
            };

        var request = new NotificationRequest
        {
            Notifications = notifications,
            UserId = userId,
            FilerId = filerId,
            FilerNotificationData = notificationData
        };

        await notificationWrapperSvc.SendNotifications(request);
    }

    #endregion
}
