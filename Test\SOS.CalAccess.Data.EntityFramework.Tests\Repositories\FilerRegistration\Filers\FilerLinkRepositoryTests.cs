using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Filers;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Filers;

[TestFixture]
[TestOf(typeof(FilerUserRepository))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class FilerLinkRepositoryTests
{
    private FilerLinkRepository _repository;
    private IDateTimeSvc _dateTimeSvc;

    [Test]
    public async Task FindByFilerIdAndLinkTypeAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, _dateTimeSvc);

        // Get seed data
        var data = GetData();

        // Add seed data into the database
        await _repository.Create(data);

        // Act
        var result = await _repository.FindByFilerIdAndLinkType(1, 1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<FilerLink>());
            Assert.That(result?.Id, Is.EqualTo(data.Id));
        });
    }

    [Test]
    public async Task FindByFilerIdAndLinkTypeAsync_NotFound_ShouldReturnNull()
    {
        // Arrange
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, _dateTimeSvc);

        // Act
        var result = await _repository.FindByFilerIdAndLinkType(1, 1);

        // Assert
        Assert.That(result, Is.Null);
    }
    #region LinkControlledCommitteeToCandidateIntentionStatement
    [Test]
    public async Task LinkControlledCommitteeToCandidateIntentionStatement_OnSuccess_CreateFilerLink()
    {
        // Arrange
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, _dateTimeSvc);

        _dateTimeSvc.GetCurrentDateTime().Returns(new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local));

        // Act
        var result = await _repository.LinkControlledCommitteeToCandidateIntentionStatement(1, 1, 1);

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    [Test]
    public async Task LinkControlledCommitteeToCandidateIntentionStatement_OnSuccess_UpdateFilerLink()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, _dateTimeSvc);

        // Get seed data
        var data = new FilerLink
        {
            Id = 1,
            FilerId = 1,
            FilerLinkTypeId = FilerLinkType.ControlledCommittee.Id,
            LinkedEntityId = 1,
            EffectiveDate = date,
            CreatedBy = 0,
            ModifiedBy = 0,
        };

        // Add seed data into the database
        await _repository.Create(data);

        // Act
        var result = await _repository.LinkControlledCommitteeToCandidateIntentionStatement(1, 2, 1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.LinkedEntityId, Is.EqualTo(2));
    }
    #endregion

    #region LinkEntityTypeToFiler
    [Test]
    public async Task LinkEntityTypeToFiler_OnSuccess_CreateFilerLink()
    {
        // Arrange
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, _dateTimeSvc);

        // Act
        var result = await _repository.LinkEntityTypeToFiler(1, 1, FilerLinkType.LobbyingFirm, 1);

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    [Test]
    public async Task LinkEntityTypeToFiler_OnSuccess_UpdateFilerLink()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, _dateTimeSvc);

        // Get seed data
        var data = new FilerLink
        {
            Id = 1,
            FilerId = 1,
            FilerLinkTypeId = FilerLinkType.LobbyistEmployer.Id,
            LinkedEntityId = 1,
            EffectiveDate = date,
            CreatedBy = 0,
            ModifiedBy = 0,
        };

        // Add seed data into the database
        await _repository.Create(data);

        // Act
        var result = await _repository.LinkEntityTypeToFiler(1, 1, FilerLinkType.LobbyistEmployer, 1, [FilerLinkType.LobbyistEmployer, FilerLinkType.LobbyingFirm]);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.LinkedEntityId, Is.EqualTo(1));
    }

    [Test]
    public async Task UnlinkLobbyistFromEmployer_OnSuccess_SetActiveFieldToFalse()
    {
        //Arrange
        long employerId = 111;
        long employerFilerId = 111;
        long lobbyistId = 2222;
        long lobbyistFilerId = 2222;

        var filerLink = await SeedEmployerFilerLinkData(_dateTimeSvc, employerId, employerFilerId, lobbyistId, lobbyistFilerId);
        // Act
        await _repository.UnlinkLobbyistFromEmployer(employerId, lobbyistId);
        // Assert
        Assert.That(filerLink.Active, Is.False);
    }

    [Test]
    public async Task UnlinkLobbyistFromEmployer_NotFound_ShouldThrowException()
    {
        // Arrange
        long employerId = -1;
        long lobbyistId = -1;
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, _dateTimeSvc);

        // Act
        var exception = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _repository.UnlinkLobbyistFromEmployer(employerId, lobbyistId));
        // Assert
        Assert.That(exception, Is.Not.Null);
        Assert.That(exception.Message, Is.EqualTo($"Lobbyist registration Id={lobbyistId} not linked to employer registration Id={employerId}"));
    }

    #endregion
    #region Private
    private static FilerLink GetData()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        return new FilerLink
        {
            Id = 1,
            FilerId = 1,
            FilerLinkTypeId = 1,
            LinkedEntityId = 1,
            EffectiveDate = date,
            CreatedBy = 1,
            ModifiedBy = 1,
        };
    }

    private async Task<FilerLink> SeedEmployerFilerLinkData(IDateTimeSvc dateTimeService, long employerId, long employerFilerId,
                                                                    long lobbyistId, long lobbyistFilerId)
    {
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new FilerLinkRepository(context, dateTimeService);
        // Get seed data
        var lobbyist = new Lobbyist()
        {
            Id = lobbyistId,
            LegislativeSessionId = 1,
            FirstName = "FirstName",
            MiddleName = "MiddleName",
            LastName = "LastName",
            Name = "Name",
            Email = "Email",
            FilerId = lobbyistFilerId,
            StatusId = RegistrationStatus.Accepted.Id,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Purpose = "Business",
                        Type = "Office"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList
            {
                Id = 1,
                PhoneNumbers = new List<PhoneNumber>
                {
                    new()
                    {
                        Id = 1,
                        CountryCode = "+1",
                        CountryId = 1,
                        Number = "8081234567",
                        Type = "Work"
                    }
                }
            }
        };
        var lobbyingEmployer = new LobbyistEmployer
        {
            Id = employerId,
            FilerId = employerFilerId,
            Name = "Employer A",
            StatusId = 1,
            Email = "<EMAIL>",
            EmployerName = "Employer A",
            EmployerType = "Type A",
            BusinessActivity = "Activity A",
            BusinessDescription = "Description A",
            InterestType = "Interest A",
            NumberOfMembers = 10,
            AddressList = new AddressList
            {
                Id = 1,
                Addresses = new List<Address>
                {
                    new() {
                        Id = 1,
                        Street = "123 Main St",
                        City = "CityA",
                        State = "CA",
                        Zip = "12345",
                        Country = "USA",
                        Type = "Business",
                        Purpose = "Primary"
                    }
                }
            }
        };

        var employerFiler = new Filer
        {
            Id = employerFilerId,
            CurrentRegistrationId = employerId,
            CurrentRegistration = lobbyingEmployer,
            EffectiveDate = DateTime.Now,
            FilerStatus = FilerStatus.Draft,
            FilerTypeId = FilerType.LobbyistEmployer.Id,
            CreatedBy = 0,
            ModifiedBy = 0
        };
        var lobbyistFiler = new Filer
        {
            Id = lobbyistFilerId,
            CurrentRegistrationId = lobbyistId,
            CurrentRegistration = lobbyist,
            EffectiveDate = DateTime.Now,
            FilerStatus = FilerStatus.Draft,
            FilerTypeId = FilerType.Lobbyist.Id,
            CreatedBy = 0,
            ModifiedBy = 0
        };

        var filerLink = new FilerLink
        {
            Id = 1,
            FilerId = lobbyistFilerId,
            FilerLinkTypeId = FilerLinkType.LobbyistEmployer.Id,
            LinkedEntityId = employerFilerId,
            EffectiveDate = DateTime.Now,
            Active = true,
            CreatedBy = 0,
            ModifiedBy = 0,
        };

        // Add seed data into the database
        await context.Set<LobbyistEmployer>().AddAsync(lobbyingEmployer);
        await context.SaveChangesAsync();
        await context.Set<Lobbyist>().AddAsync(lobbyist);
        await context.SaveChangesAsync();
        await context.Set<Filer>().AddRangeAsync(employerFiler, lobbyistFiler);
        await context.SaveChangesAsync();

        return await _repository.Create(filerLink);
    }
    #endregion
}
