using Microsoft.AspNetCore.Mvc.ModelBinding;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.ControllerServices.Form470S;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Registrations.Form470S;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using FilingModels = SOS.CalAccess.Models.FilerDisclosure.Filings.Models;
using Form470SAmendResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470SAmendResponseDto;
using Form470SFilingRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.Form470SFilingRequest;
using RegistrationModels = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using ValidatedForm470SResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.ValidatedForm470SResponseDto;
using WorkFlowError = SOS.CalAccess.Models.Common.WorkFlowError;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(CandidateRegistrationCtlSvc))]
internal sealed class Form470SCtlSvcTests
{
    private IFilingsApi _filingsApi;
    private IForm470SSvc _form470SSvc;
    private IForm470SAmendSvc _form470SAmendSvc;
    private Form470SCtlSvc _service;
    private IDecisionsValidationMapService _decisionsSvc;
    private DateTime _dateNow;


    [SetUp]
    public void Setup()
    {
        _filingsApi = Substitute.For<IFilingsApi>();
        _form470SSvc = Substitute.For<IForm470SSvc>();
        _form470SAmendSvc = Substitute.For<IForm470SAmendSvc>();
        _decisionsSvc = Substitute.For<IDecisionsValidationMapService>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _service = new Form470SCtlSvc(_filingsApi, _form470SSvc, _form470SAmendSvc, _decisionsSvc);
    }

    #region Core
    [Test]
    public async Task Cancel()
    {
        // Arrange
        long id = 1;
        // Act
        await _service.Cancel(id);

    }
    #endregion

    #region Page01
    [Test]
    public async Task Form470SPage01GetExisting_EmptyResponse_ReturnsNull()
    {
        // Arrange
        long id = 1;
        var res = new FilingModels.Form470SOverviewResponse();
        _form470SSvc.GetForm470SOverview(Arg.Any<long>()).Returns(res);
        // Act
        var result = await _service.Form470SPage01GetExisting(id);
        // Assert
        await _form470SSvc.Received(1).GetForm470SOverview(Arg.Any<long>());
        Assert.That(result, Is.Null);
    }
    [Test]
    public async Task Form470SPage01GetExisting_ValidResponse_ReturnsModel()
    {
        // Arrange
        long id = 1;
        var res = new FilingModels.Form470SOverviewResponse
        {
            CandidateIntentionStatement470S = new(),
            Form470SFiling = new(),
        };
        _form470SSvc.GetForm470SOverview(Arg.Any<long>()).Returns(res);
        var filings = new List<FilingPeriod>
        {
            new(100, "", _dateNow, new(0, "", _dateNow, 0, 0, "", _dateNow), null, _dateNow, null!, 3, [], 1, 100, null!, 1, 0, "", _dateNow),
        };
        _filingsApi.GetAllFilingPeriods().Returns(filings);
        // Act
        var result = await _service.Form470SPage01GetExisting(id);
        // Assert
        await _form470SSvc.Received(1).GetForm470SOverview(Arg.Any<long>());
        Assert.That(result, Is.Not.Null);
    }
    [Test]
    public async Task Page01ViewModelSetReadOnlyData_NoFilerId_NoChanges()
    {
        // Arrange
        var model = new Form470SPage01ViewModel();
        // Act
        var result = await _service.Form470SPage01ViewModelSetReadOnlyData(model);
        // Assert
        Assert.That(result.FilerId, Is.Null);
    }
    [Test]
    public async Task Page01ViewModelSetReadOnlyData_AppliesData()
    {
        // Arrange
        var model = new Form470SPage01ViewModel
        {
            FilerId = 1,
            CandidateName = "Name from View Model"
        };
        var res = new RegistrationModels.CandidateIntentionStatement470S
        {
            Name = "Name from API"
        };
        _form470SSvc.GetCisWithElection470SByFilerId(Arg.Any<long>()).Returns(res);

        // Act
        var result = await _service.Form470SPage01ViewModelSetReadOnlyData(model);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.CandidateName, Is.EqualTo("Name from API"));
        });
    }
    [Test]
    public async Task Form470SPage01GetViewModelFromFilerId_AppliesData()
    {
        // Arrange
        var model = new Form470SPage01ViewModel
        {
            FilerId = 1,
            CandidateName = "Name from View Model"
        };
        var res = new RegistrationModels.CandidateIntentionStatement470S
        {
            FilerId = 1,
            Name = "Name from API"
        };
        _form470SSvc.GetCisWithElection470SByFilerId(Arg.Any<long>()).Returns(res);

        // Act
        var result = await _service.Form470SPage01GetViewModelFromFilerId(1);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.CandidateName, Is.EqualTo("Name from API"));
        });
    }
    [Test]
    public async Task Form470SPage01GetViewModelFromFilerId_ReturnNull()
    {
        // Arrange
        var model = new Form470SPage01ViewModel
        {
            FilerId = 1,
            CandidateName = "Name from View Model"
        };
        var res = new RegistrationModels.CandidateIntentionStatement470S
        {
            Name = "Name from API"
        };
        _form470SSvc.GetCisWithElection470SByFilerId(Arg.Any<long>()).Returns(res);

        // Act
        var result = await _service.Form470SPage01GetViewModelFromFilerId(1);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Null);
        });
    }
    [Test]
    public async Task Form470SPage01Save_WithNullId_ShouldCallCreate()
    {
        // Arrange
        var model = new Form470SPage01ViewModel { FilerId = 1, Id = null, DateContributions = DateTime.Today };
        var response = new ValidatedForm470SResponseDto { Id = 100, ValidationErrors = new List<WorkFlowError>() };
        var modelState = new ModelStateDictionary();

        _form470SSvc.CreateOfficeHolderCandidateSupplementFormFiling(Arg.Any<Form470SFilingRequest>())
            .Returns(response);

        // Act
        var result = await _service.Form470SPage01Save(model, modelState, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(100));
            _form470SSvc.Received(1).CreateOfficeHolderCandidateSupplementFormFiling(Arg.Any<Form470SFilingRequest>());
            _decisionsSvc.Received(1).ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Is<List<WorkFlowError>>(list => list.Count == 0), modelState);
        });
    }
    [Test]
    public async Task Form470SPage01Save_WithNonNullId_ShouldCallUpdate()
    {
        // Arrange
        var model = new Form470SPage01ViewModel { FilerId = 1, Id = 5, DateContributions = DateTime.Today };
        var response = new ValidatedForm470SResponseDto
        {
            Id = 200,
            ValidationErrors = new List<WorkFlowError>()
        };
        var modelState = new ModelStateDictionary();

        _form470SSvc.UpdateOfficeHolderCandidateSupplementFormFiling(Arg.Any<Form470SFilingRequest>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _service.Form470SPage01Save(model, modelState, false);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(200));
            _form470SSvc.Received(1).UpdateOfficeHolderCandidateSupplementFormFiling(Arg.Any<Form470SFilingRequest>());
            _decisionsSvc.Received(1).ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Is<List<WorkFlowError>>(list => list.Count == 0),
                modelState
            );
        });
    }
    [Test]
    public async Task Form470SPage01Save_WithRequiredDateError_ShouldReplaceModelError()
    {
        // Arrange
        var model = new Form470SPage01ViewModel { FilerId = 1, Id = null, DateContributions = null };
        var errors = new List<WorkFlowError>
    {
        new("ContributionsOrExpenditureOverOn", "MissingDate", "Validation", "Date is required.")
    };
        var response = new ValidatedForm470SResponseDto { Id = 300, ValidationErrors = errors };
        var modelState = new ModelStateDictionary();
        modelState.AddModelError("ContributionsOrExpenditureOverOn", "Date is required.");

        _form470SSvc.CreateOfficeHolderCandidateSupplementFormFiling(Arg.Any<Form470SFilingRequest>())
            .Returns(response);

        // Act
        var result = await _service.Form470SPage01Save(model, modelState, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(300));
            _decisionsSvc.Received(1).ApplyErrorsToModelState(Arg.Any<Dictionary<string, FieldProperty>>(), errors, modelState);
            Assert.That(modelState.ContainsKey("ContributionsOrExpenditureOverOn"), Is.True);
            var entry = modelState["ContributionsOrExpenditureOverOn"];
            Assert.That(entry.Errors, Has.Count.EqualTo(1));
            Assert.That(entry.Errors[0].ErrorMessage, Is.EqualTo("Date is required."));
        });
    }

    [Test]
    public async Task Form470SPage01Save_WhenExceptionThrown_ShouldAddModelErrorAndReturnZero()
    {
        // Arrange
        var model = new Form470SPage01ViewModel { FilerId = 1, Id = null, DateContributions = DateTime.Today };
        var modelState = new ModelStateDictionary();

        _form470SSvc.CreateOfficeHolderCandidateSupplementFormFiling(Arg.Any<Form470SFilingRequest>())
            .Throws(new InvalidOperationException("Test"));

        // Act
        var result = await _service.Form470SPage01Save(model, modelState, true);

        // Assert
        Assert.That(modelState[string.Empty], Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(0));
            Assert.That(modelState[string.Empty].Errors, Has.Count.EqualTo(1));
            Assert.That(modelState[string.Empty].Errors[0].ErrorMessage, Is.EqualTo("Test"));
        });
    }
    #endregion

    #region
    [Test]
    public async Task InitializeForm470SAmendment()
    {
        // Arrange
        var filerId = 1L;
        var response = new Form470SAmendResponseDto(10, 11);
        _form470SAmendSvc.Initialize470SAmendment(Arg.Any<long>()).Returns(response);
        // Act
        var result = await _service.InitializeForm470SAmendment(filerId);
        // Assert
        Assert.That(result, Is.EqualTo(10));
    }
    [Test]
    public void InitializeForm470SAmendment_ReturnError()
    {
        // Arrange
        var filerId = 1L;
        var response = new Form470SAmendResponseDto("Error");
        _form470SAmendSvc.Initialize470SAmendment(Arg.Any<long>()).Returns(response);
        // Act & Assert
        Assert.ThrowsAsync<ArgumentException>(() => _service.InitializeForm470SAmendment(filerId));
    }
    #endregion
}
