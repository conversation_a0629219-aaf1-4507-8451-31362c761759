using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.WebApi.Elections;

namespace SOS.CalAccess.WebApi.Tests.Elections;
[TestFixture]

public class BallotMeasureControllerTests
{
    private IBallotMeasureSvc _ballotMeasureSvcMock;
    private BallotMeasureController _controller;
    private IAuthorizationService _authorizationServiceMock;

    [SetUp]
    public void SetUp()
    {
        _ballotMeasureSvcMock = Substitute.For<IBallotMeasureSvc>();
        _authorizationServiceMock = Substitute.For<IAuthorizationService>();
        _controller = new BallotMeasureController(_authorizationServiceMock, _ballotMeasureSvcMock);
    }


    [Test]
    public async Task FindBallotMeasuresByIdOrName_Name_NoError()
    {
        // Arrange
        BallotMeasure measure = GenerateSampleBallotMeasure(1, "Test");
        List<BallotMeasureDto> list = new();
        list.Add(new(measure));

        _ = _ballotMeasureSvcMock.SearchBallotMeasuresAsync(Arg.Any<string>()).Returns(list);

        // Act
        var result = await _controller.SearchBallotMeasuresAsync("test");

        // Assert
        Assert.That(result, Is.Not.Empty);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count, Is.EqualTo(1));
        });
    }

    #region NotImplementedYetMethods
    [Test]
    public async Task CreateBallotMeasure_NotImplementedException()
    {
        {
            // Arrange
            BallotMeasure measure = GenerateSampleBallotMeasure(100, "Test Measure 1");

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _controller.CreateBallotMeasure(measure));

        }
    }

    [Test]
    public async Task UpdateBallotMeasure_NotImplementedException()
    {
        {
            // Arrange
            BallotMeasure measure = GenerateSampleBallotMeasure(100, "Test Measure 1");

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _controller.UpdateBallotMeasure(measure));
        }
    }

    [Test]
    public async Task DeleteBallotMeasure_NotImplementedException()
    {
        {
            // Arrange

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _controller.DeleteBallotMeasure(1));
        }
    }

    [Test]
    public async Task GetAllProponents_NotImplementedException()
    {
        {
            // Arrange

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _controller.GetAllProponents());
        }
    }

    [Test]
    public async Task CreateProponents_NotImplementedException()
    {
        {
            // Arrange
            var proponent = new Proponent
            {
                Id = 1,
                LastName = "Test",
                CreatedBy = 1,
                ModifiedBy = 1,
                CreatedByReference = new User
                {
                    Id = 9999,
                    EmailAddress = "test@test",
                    FirstName = "Test",
                    LastName = "Test",
                    EntraOid = "TestOid"
                },
                ModifiedByReference = new User { Id = 9999, EmailAddress = "test@test", FirstName = "Test", LastName = "Test", EntraOid = "TestOid" }
            };

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _controller.CreateProponent(proponent));
        }
    }
    #endregion

    [Test]
    public async Task GetAllBallotMeasures_NotImplementedException()
    {
        {
            // Arrange
            BallotMeasure measure = GenerateSampleBallotMeasure(100, "Test Measure 1");

            // Act and Assert
            await Task.Yield();
            _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(NotImplementedException)), async () => await _controller.GetAllBallotMeasures());

        }
    }


    #region private
    private static BallotMeasure GenerateSampleBallotMeasure(long id, string name)
    {
        BallotMeasure measure = new()
        {
            Id = id,
            Code = "N/A",
            Name = name,
            Description = "",
            Jurisdiction = "Statewide",
            AttorneyGeneralId = "",
            AttorneyGeneralTitle = "",
            CreatedBy = 999,
            ModifiedBy = 999,
            MeasureType = "PROPOSITION",
            EffectiveDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        };
        return measure;
    }

    #endregion
}
