using NSubstitute;
using SOS.CalAccess.Data.Authorization;
using SOS.CalAccess.Data.Common;
using SOS.CalAccess.Data.Contracts.Authorization;
using SOS.CalAccess.Data.Contracts.Common;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Elections;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.FilerRegistration;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;

namespace SOS.CalAccess.Services.Business.Tests.Reference;

[TestFixture]
public class ReferenceDataSvcTests
{
    private IFilerTypeRepository _filerTypeRepo;
    private INotificationTypeRepository _notificationTypeRepo;
    private IOfficeRepository _officeRepo;
    private IPoliticalPartyRepository _politicalPartyRepo;
    private IPermissionRepository _permissionRepo;
    private IAgencyRepository _agencyRepo;
    private IOfficialPositionRepository _officialPositionRepository;
    private IPaymentCodeRepository _paymentCodeRepository;
    private IAdvertisementDistributionMethodRepository _advertisementDistributionMethodRepo;
    private IBillRepository _billRepository;
    private ICountryRepository _countryRepository;
    private IExpenditureCodeRepository _expenditureCodeRepositoryMock;
    private IFilerRoleRepository _filerRoleRepositoryMock;
    private INatureAndInterestTypeRepository _natureAndInterestTypeRepositoryMock;
    private IIndustryGroupClassificationTypeRepository _industryGroupClassificationTypeRepositoryMock;
    private IBusinessSubcategoryRepository _businessSubcategoryRepositoryMock;
    private ReferenceDataSvc _service;

    [SetUp]
    public void Setup()
    {
        _filerTypeRepo = Substitute.For<IFilerTypeRepository>();
        _notificationTypeRepo = Substitute.For<INotificationTypeRepository>();
        _officeRepo = Substitute.For<IOfficeRepository>();
        _politicalPartyRepo = Substitute.For<IPoliticalPartyRepository>();
        _permissionRepo = Substitute.For<IPermissionRepository>();
        _agencyRepo = Substitute.For<IAgencyRepository>();
        _officialPositionRepository = Substitute.For<IOfficialPositionRepository>();
        _paymentCodeRepository = Substitute.For<IPaymentCodeRepository>();
        _advertisementDistributionMethodRepo = Substitute.For<IAdvertisementDistributionMethodRepository>();
        _billRepository = Substitute.For<IBillRepository>();
        _countryRepository = Substitute.For<ICountryRepository>();
        _expenditureCodeRepositoryMock = Substitute.For<IExpenditureCodeRepository>();
        _filerRoleRepositoryMock = Substitute.For<IFilerRoleRepository>();
        _natureAndInterestTypeRepositoryMock = Substitute.For<INatureAndInterestTypeRepository>();
        _industryGroupClassificationTypeRepositoryMock = Substitute.For<IIndustryGroupClassificationTypeRepository>();
        _businessSubcategoryRepositoryMock = Substitute.For<IBusinessSubcategoryRepository>();

        _service = new ReferenceDataSvc(
            _filerTypeRepo,
            _notificationTypeRepo,
            _officeRepo,
            _politicalPartyRepo,
            _officialPositionRepository,
            _agencyRepo,
            _permissionRepo,
            _paymentCodeRepository,
            _billRepository,
            _countryRepository,
            _expenditureCodeRepositoryMock,
            _advertisementDistributionMethodRepo,
            _filerRoleRepositoryMock,
            _natureAndInterestTypeRepositoryMock,
            _industryGroupClassificationTypeRepositoryMock,
            _businessSubcategoryRepositoryMock
        );
    }

    [Test]
    public async Task GetAllFilerTypes_ReturnsFilerTypes()
    {
        var filerTypes = new List<FilerType>
            {
                new() { Id = 1, Name = "Individual" }
            };

        _ = _filerTypeRepo.GetAll().Returns(filerTypes);

        var result = await _service.GetAllFilerTypes();

        Assert.That(result, Is.EquivalentTo(filerTypes));
    }

    [Test]
    public async Task GetAllFilerRoles_ReturnsFilerRoles()
    {
        var filerRoles = new List<FilerRole>
            {
                new() {
                    Id = 1,
                    Name = "RoleName",
                    Description = "Role Description",
                    FilerTypeId = 1,
                    IsDefaultFilerTypeRole = true,
                }
            };

        _ = _filerRoleRepositoryMock.GetAll().Returns(filerRoles);

        var result = await _service.GetAllFilerRoles();

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task GetAllNotificationTypes_ReturnsNotificationTypes()
    {
        var types = new List<NotificationType>
            {
                new() { Id = 1, Name = "Reminder" }
            };

        _ = _notificationTypeRepo.GetAll().Returns(types);

        var result = await _service.GetAllNotificationTypes();

        Assert.That(result, Is.EquivalentTo(types));
    }

    [Test]
    public async Task GetOffice_ReturnsOfficeById()
    {
        var office = new Office { Id = 101, Name = "Mayor" };
        _ = _officeRepo.FindById(101).Returns(office);

        var result = await _service.GetOffice(101);

        Assert.That(result, Is.EqualTo(office));
    }

    [Test]
    public async Task GetAllPoliticalParties_ReturnsParties()
    {
        var parties = new List<PoliticalParty>
            {
                new() { Id = 1, Name = "Democratic" }
            };

        _ = _politicalPartyRepo.GetAll().Returns(parties);

        var result = await _service.GetAllPoliticalParties();

        Assert.That(result, Is.EquivalentTo(parties));
    }

    [Test]
    public async Task ListPermissions_ReturnsMappedDtos()
    {
        var permissions = new List<Permission>
            {
                new() { Id = 1, Name = "Create", Description = "Create access" },
                new() { Id = 2, Name = "Delete", Description = "Delete access" }
            };

        _ = _permissionRepo.GetAll().Returns(permissions);

        var result = (await _service.ListPermissions()).ToList();

        Assert.That(result, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(result[0].Id, Is.EqualTo(1));
            Assert.That(result[0].Name, Is.EqualTo("Create"));
            Assert.That(result[0].Description, Is.EqualTo("Create access"));
            Assert.That(result[0].Granted, Is.False);
        });
    }

    [Test]
    public async Task GetAllOfficialPositions_ReturnsOfficialPositions()
    {
        var officialPositions = new List<OfficialPosition>
        {
            OfficialPosition.Governor
        };

        _ = _officialPositionRepository.GetAll().Returns(officialPositions);

        var result = await _service.GetAllOfficialPositions();

        Assert.That(result, Is.EquivalentTo(officialPositions));
    }

    [Test]
    public async Task GetAllAgencies_ReturnsAgencies()
    {
        var agencies = new List<Agency>
    {
        new() { Id = 1, Name = "City Agency" }
    };

        _ = _agencyRepo.GetAll().Returns(agencies);

        var result = await _service.GetAllAgencies();

        Assert.That(result, Is.EquivalentTo(agencies));
    }

    [Test]
    public async Task GetAllPaymentCodes_ReturnsCodes()
    {
        var paymentCodes = new List<PaymentCode>
            {
                new() { Id = 1, Name = "Salary" },
                new() { Id = 2, Name = "Other" },
                new() { Id = 3, Name = "Some Payment Code" }
            };

        _ = _paymentCodeRepository.GetAll().Returns(paymentCodes);

        var result = await _service.GetAllPaymentCodes();
        var list = result.ToList();

        Assert.That(result.Count, Is.EqualTo(3));
        Assert.Multiple(() =>
        {
            Assert.That(list[0].Id, Is.EqualTo(1));
            Assert.That(list[0].Name, Is.EqualTo("Salary"));
            Assert.That(list[0].DisplayName, Is.EqualTo("Salary (S)"));
        });
        Assert.Multiple(() =>
        {
            Assert.That(list[1].Id, Is.EqualTo(2));
            Assert.That(list[1].Name, Is.EqualTo("Other"));
            Assert.That(list[1].DisplayName, Is.EqualTo("Other (O)"));
        });
        Assert.Multiple(() =>
        {
            Assert.That(list[2].Id, Is.EqualTo(3));
            Assert.That(list[2].Name, Is.EqualTo("Some Payment Code"));
            Assert.That(list[2].DisplayName, Is.EqualTo("Some Payment Code"));
        });
    }

    [Test]
    public async Task GetAllAdvertisementDistributionMethods_ReturnsMethods()
    {
        var advertisementDistributionMethods = new List<AdvertisementDistributionMethod>
            {
                new() { Id = 1, Name = "Direct Mail" }
            };

        _ = _advertisementDistributionMethodRepo.GetAll().Returns(advertisementDistributionMethods);

        var result = await _service.GetAllAdvertisementDistributionMethods();

        var list = result.ToList();

        Assert.That(list, Has.Count.EqualTo(advertisementDistributionMethods.Count));
        Assert.Multiple(() =>
        {
            Assert.That(list[0].Id, Is.EqualTo(advertisementDistributionMethods[0].Id));
            Assert.That(list[0].Name, Is.EqualTo(advertisementDistributionMethods[0].Name));
        });
    }

    [Test]
    public async Task GetAll_WithSearchQuery_ReturnsFilteredAgencies()
    {
        // Arrange
        var searchQuery = "City";
        var filteredAgencies = new List<Agency>
        {
            new() { Id = 1, Name = "City Agency" }
        };

        _agencyRepo.GetAll(searchQuery).Returns(filteredAgencies);

        // Act
        var result = await _service.SearchAllAgencies(searchQuery);

        // Assert
        Assert.That(result, Is.EquivalentTo(filteredAgencies));
        await _agencyRepo.Received(1).GetAll(searchQuery);
        await _agencyRepo.DidNotReceive().GetAll();
    }


    [Test]
    public async Task GetAll_WithSearchQuery_ReturnsFilteredBills()
    {
        // Arrange
        var searchQuery = "City";
        var fileredBills = new List<Bill>
        {
            new() { Id = 1, Number = "ABC-123", Title = "City Skyline" }
        };

        _billRepository.GetAll(searchQuery).Returns(fileredBills);

        // Act
        var result = await _service.SearchAllBills(searchQuery);

        // Assert
        Assert.That(result, Is.EquivalentTo(fileredBills));
        await _billRepository.Received(1).GetAll(searchQuery);
        await _billRepository.DidNotReceive().GetAll();
    }

    [Test]
    public async Task GetAllCountryCodes_ReturnsCountryCodes()
    {
        var countryCodes = new List<Country>
            {
                new() { Id = 1, PhoneCountryCode = "+1", CountryName = "United States" }
            };

        _ = _countryRepository.GetAll().Returns(countryCodes);

        var result = await _service.GetAllCountryCodes();

        Assert.That(result, Is.EquivalentTo(countryCodes));
    }

    [Test]
    public async Task GetAllExpenditureCodesAsync_ShouldReturnsAll()
    {
        // Arrange
        var response = new List<ExpenditureCode>
        {
            new() { Id = 1, Abbrev = "ABC-123", Description = "Test1" },
            new() { Id = 2, Abbrev = "CAB-321", Description = "Test2" }
        };

        _expenditureCodeRepositoryMock.GetAll().Returns(response);

        // Act
        var result = await _service.GetAllExpenditureCodesAsync();

        // Assert
        Assert.That(result, Is.EquivalentTo(response));
    }

    [Test]
    public async Task GetAllNatureAndInterestsAsync_ShouldReturnsAll()
    {
        // Arrange
        var response = new List<NatureAndInterestType>
        {
            new() {Id = 1, Name = "Individual" },
        };

        _natureAndInterestTypeRepositoryMock.GetAll().Returns(response);

        // Act
        var result = await _service.GetAllNatureAndInterestTypesAsync();

        // Assert
        Assert.That(result, Is.EquivalentTo(response));
    }

    [Test]
    public async Task GetAllIndustryGroupClassificationTypesAsync_ShouldReturnsAll()
    {
        // Arrange
        var response = new List<IndustryGroupClassificationType>
        {
            new() {Id = 1, Name = "Agriculture" },
        };

        _industryGroupClassificationTypeRepositoryMock.GetAll().Returns(response);

        // Act
        var result = await _service.GetAllIndustryGroupClassificationTypesAsync();

        // Assert
        Assert.That(result, Is.EquivalentTo(response));
    }

    [Test]
    public async Task GetAllBusinessSubcategoriesAsync_ShouldReturnsAll()
    {
        // Arrange
        var response = new List<BusinessSubcategory>
        {
            new() { Id = 1, Name = "Entertainment / Recreation" },
        };

        _businessSubcategoryRepositoryMock.GetAll().Returns(response);

        // Act
        var result = await _service.GetAllBusinessSubcategoriesAsync();

        // Assert
        Assert.That(result, Is.EquivalentTo(response));
    }
}
