// <copyright file="IUpdateContact.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;

/// <summary>
/// Command interface for updating a contact.
/// </summary>
public interface IUpdateContact : ICommand<UpdateContactCommand, IResult<FilerContact>>;

/// <summary>
/// Command implementation for updating a contact.
/// </summary>
/// <param name="db">The database context.</param>
/// <param name="decisions">Business logic engine.</param>
/// <param name="auditService">High level audit facilities.</param>
/// <param name="logger">The logger instance.</param>
public sealed class UpdateContact(
    DatabaseContext db,
    IDecisionsService decisions,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc,
    ILogger<UpdateContact> logger) : IUpdateContact
{
    /// <inheritdoc />
    public async ValueTask<IResult<FilerContact>> Execute(
        UpdateContactCommand input,
        CancellationToken cancellationToken = default)
    {
        var contact = await db.FilerContacts
            .Include(c => c.AddressList)
                .ThenInclude(a => a!.Addresses)
            .Include(c => c.PhoneNumberList)
                .ThenInclude(p => p!.PhoneNumbers)
            .AsSplitQuery()
            .FirstOrDefaultAsync(c => c.Id == input.Id, cancellationToken);

        if (contact is null)
        {
            logger.LogWarning("Contact with id {Id} not found", input.Id);
            return new Failure<FilerContact>.NotFound("No contact was found with the requested id");
        }

        if (input.Apply(contact).Unwrap(out _, out var failure))
        {
            return failure;
        }

        _ = await decisions.Execute(new("Contact.Update", Context: contact), cancellationToken);

        await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Update", contact.GetType().Name, contact.Id.ToString(), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<FilerContact>(contact);
    }
}
