using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Services.WebApi.Transactions;

namespace SOS.CalAccess.WebApi.Tests.Transactions;

[TestFixture]
public class ActivityExpenseItemResponseTests
{
    [Test]
    public void Test_ActivityExpense_StoresCorrectValues()
    {
        var testTransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var testActivityDescription = "Campaign Event";
        var testCreditCardCompanyName = "Visa";
        var testAmount = (Currency)10;
        var testPayeeName = "Test Organization";
        var testFilerContactType = FilerContactType.Organization;
        var testBeneficiaryName = "Jane Doe";

        var testContact = new OrganizationContact { OrganizationName = testPayeeName, FilerContactType = FilerContactType.Organization };
        var testReportablePerson = new TransactionReportablePerson
        {
            Name = testBeneficiaryName,
            Amount = testAmount,
        };

        var activityExpense = new ActivityExpense
        {
            TransactionDate = testTransactionDate,
            ActivityExpenseTypeId = 123,
            ActivityDescription = testActivityDescription,
            CreditCardCompanyName = testCreditCardCompanyName,
            Amount = testAmount,
            Contact = testContact,
            TransactionReportablePersons = new List<TransactionReportablePerson> { testReportablePerson }
        };

        // Act
        var response = new ActivityExpenseItemResponse(activityExpense)
        {
            PersonBenefiting = testBeneficiaryName,
            AmountBenefiting = testAmount
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(response.TransactionDate, Is.EqualTo(activityExpense.TransactionDate));
            Assert.That(response.Amount, Is.EqualTo(testAmount));
            Assert.That(response.PayeeName, Is.EqualTo(testPayeeName));
            Assert.That(response.PayeeType, Is.EqualTo(testFilerContactType.Name));
            Assert.That(response.PersonBenefiting, Is.EqualTo(testBeneficiaryName));
            Assert.That(response.AmountBenefiting, Is.EqualTo(testAmount));
        });
    }


}
