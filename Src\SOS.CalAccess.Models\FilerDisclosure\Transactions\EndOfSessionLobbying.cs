using SOS.CalAccess.Models.FilerDisclosure.Contacts;

namespace SOS.CalAccess.Models.FilerDisclosure.Transactions;

/// <summary>
/// Class for disclosure items that represent end-of-session payments.
/// </summary>
public sealed class EndOfSessionLobbying() : Transaction(TransactionType.EndOfSessionLobbying)
{
    /// <summary>
    /// Gets or sets the date at which the lobbying firm was hired.
    /// </summary>
    [Documentation("Date the lobbying from was hired.")]
    public DateTime? DateLobbyingFirmHired { get; set; }
}

public sealed class EndOfSessionLobbyingDto
{
    /// <summary>
    /// Gets unique key of an end-of-session transaction.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets the actions lobbied for each firm.
    /// </summary>
    public List<ActionsLobbied?>? ActionsLobbied { get; set; }

    /// <summary>
    /// Gets the contact for each firm.
    /// </summary>
    public FilerContact? Contact { get; set; }

    /// <summary>
    /// Gets the firm name for each firm.
    /// </summary>
    public required string FirmName { get; set; }

    /// <summary>
    /// Gets the filer ID / lobbying firm ID for each firm.
    /// </summary>
    public long? FilerId { get; set; }

    /// <summary>
    /// Gets the date at which the lobbying firm was hired.
    /// </summary>
    public DateTime? DateLobbyingFirmHired { get; set; }

    /// <summary>
    /// Gets the incurred amount reported on the transaction.
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Gets the filing ID.
    /// </summary>
    public long? FilingId { get; set; }

    /// <summary>
    /// Gets the registration ID.
    /// </summary>
    public long? RegistrationId { get; set; }
}
