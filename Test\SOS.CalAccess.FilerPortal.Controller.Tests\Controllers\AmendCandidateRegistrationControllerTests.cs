using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Localization;
using Moq;
using NSubstitute;
using Refit;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.AmendCandidateRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;


[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(AmendCandidateRegistrationController))]
internal sealed class AmendCandidateRegistrationControllerTests : IDisposable
{
    private IAuthorizationSvc _authorizationSvc;
    private Mock<ICandidateIntentionRegistrationSvc> _candidateIntentionRegistrationSvc;
    private IAmendCandidateRegistrationCtlSvc _amendCandidateRegistrationCtlSvc;
    private ICandidateSvc _candidatSvc;
    private IElectionSvc _electionSvc;
    private IReferenceDataSvc _referenceDataSvc;
    private Mock<IStringLocalizer<SharedResources>> _localizer;
    private IAccuMailValidatorService _accuMailValidatorService;
    private IDateTimeSvc _dateTimeSvcMock;

    private AmendCandidateRegistrationController _controller;

    public void Dispose()
    {
        _controller.Dispose();
    }

    [SetUp]
    public void Setup()
    {
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _candidateIntentionRegistrationSvc = new Mock<ICandidateIntentionRegistrationSvc>();
        _amendCandidateRegistrationCtlSvc = Substitute.For<IAmendCandidateRegistrationCtlSvc>();
        _candidatSvc = Substitute.For<ICandidateSvc>();
        _electionSvc = Substitute.For<IElectionSvc>();
        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();
        _localizer = new Mock<IStringLocalizer<SharedResources>>();
        _accuMailValidatorService = Substitute.For<IAccuMailValidatorService>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        var localizedString = new LocalizedString("key", "text");
        _localizer.Setup(x => x[It.IsAny<string>()]).Returns(localizedString);

        _localizer.Setup(l => l[ResourceConstants.CisRegistrationTitle]).Returns(new LocalizedString(ResourceConstants.CisRegistrationTitle, "CIS Registration"));
        _localizer.Setup(l => l[ResourceConstants.FilerPortalTitle]).Returns(new LocalizedString(ResourceConstants.FilerPortalTitle, "Filer Portal"));
        _localizer.Setup(l => l[ResourceConstants.CisRegistrationBreadcrumb]).Returns(new LocalizedString(ResourceConstants.CisRegistrationBreadcrumb, "Candidate Registration"));
        _localizer.Setup(l => l[ResourceConstants.Candidate]).Returns(new LocalizedString(ResourceConstants.Candidate, "Candidate"));
        _localizer.Setup(l => l[ResourceConstants.Election]).Returns(new LocalizedString(ResourceConstants.Election, "Election"));
        _localizer.Setup(l => l[ResourceConstants.Verification]).Returns(new LocalizedString(ResourceConstants.Verification, "Verification"));

        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>());
        _controller = new AmendCandidateRegistrationController(
            _authorizationSvc,
            _candidateIntentionRegistrationSvc.Object,
            _amendCandidateRegistrationCtlSvc,
            _candidatSvc,
            _electionSvc,
            _referenceDataSvc,
            _dateTimeSvcMock,
            _localizer.Object)
        {
            TempData = tempData
        };
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #region Index
    [Test]
    public async Task Index_ModelStateInvalid()
    {
        // Arrange
        long id = 1;

        // Act
        var result = await _controller.Index(id) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page01"));
            Assert.That(result.RouteValues?["Id"], Is.EqualTo(1));
        });

    }
    [Test]
    public async Task Index_ModelStateValid()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = await _controller.Index(id) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    #endregion

    #region Edit
    [Test]
    public async Task Edit_ModelStateInvalid()
    {
        // Arrange
        long id = 1;

        // Act
        var result = await _controller.Edit(id) as RedirectToActionResult;

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page01"));
            Assert.That(result.RouteValues?["Id"], Is.EqualTo(1));
        });

    }
    [Test]
    public async Task Edit_ModelStateValid()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = await _controller.Edit(id) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    #endregion

    #region RedirectToAction
    [Test]
    public void RedirectToAction_ShouldRedirectToDashboard()
    {
        // Act
        var result = _controller.RedirectToDashboard() as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    #endregion

    #region Cancel
    [Test]
    public async Task Cancel_ShouldRedirectToDashboard()
    {
        // Act
        var result = await _controller.Cancel() as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    [Test]
    public async Task Cancel_Success()
    {
        // Act
        var model = new CandidateIntentionStatementViewModel();
        _candidateIntentionRegistrationSvc.Setup(s => s.CancelCandidateIntentionStatement(It.IsAny<long>()));
        var result = await _controller.Cancel() as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    [Test]
    public async Task Cancel_NotFoundAsync()
    {
        // Arrange
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = await _controller.Cancel(0, new AmendCandidateRegistrationStep01ViewModel());

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task CancelForm_Success()
    {
        // Arrange

        // Act
        _candidateIntentionRegistrationSvc.Setup(s => s.CancelCandidateIntentionStatement(It.IsAny<long>()));
        var result = await _controller.Cancel(1, new AmendCandidateRegistrationStep01ViewModel()) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    #endregion

    #region Close
    [Test]
    public void Close_ShouldRedirectToDashboard()
    {
        // Act
        var result = _controller.Close() as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    [Test]
    public void Close_NotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = _controller.Close();

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    #endregion

    #region Page01
    [Test]
    public async Task Page01_Exists()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = await _controller.Page01(id);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page01_NullRegistration_NotFound()
    {
        // Arrange
        long id = 1;

        // Act
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync((CandidateIntentionStatementResponseDto?)null);
        // Assert
        var result = await _controller.Page01(id);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page01_Valid()
    {
        // Arrange
        long id = 1;
        var response = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 0.00M,
            ExpenditureExceeded = false,
            ExpenditureLimitAccepted = false,
            ElectionRaceId = 1,
            MailingAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            },
            CandidateAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            }
        };
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.Page01(id) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result.Model, Is.InstanceOf<AmendCandidateRegistrationStep01ViewModel>());
    }
    [Test]
    public async Task Page01_Invalid()
    {
        _controller.ModelState.AddModelError("Key", "Test");
        var id = 1;
        // Act
        var result = await _controller.Page01(id);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page01_ShouldRemoveMailingAddressWhenSameAsIsTrue()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
            Addresses = new()
            {
                new()
                {
                    Purpose = "Candidate",
                    Street = "Street",
                },
                new()
                {
                    Purpose = "Mailing",
                    Street = "Street",
                }
            },
            IsSameAsCandidateAddress = true,
        };
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(true);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as ViewResult;

        // Assert
        var arg0 = _accuMailValidatorService.ReceivedCalls().ToList()[0].GetArguments()[0] as CandidateIntentionStatementViewModel;
        Assert.That(arg0, Is.Not.Null);
        Assert.That(arg0.Addresses, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate"), Is.Not.Null);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing"), Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate").IsEmpty(), Is.False);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing").IsEmpty(), Is.True);
        });
    }
    [Test]
    public async Task Page01_ShouldNotRemoveMailingAddressWhenSameAsIsTrue()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
            Addresses = new()
            {
                new()
                {
                    Purpose = "Candidate",
                    Street = "Street",
                },
                new()
                {
                    Purpose = "Mailing",
                    Street = "Street",
                }
            },
            IsSameAsCandidateAddress = false,
        };
        _accuMailValidatorService
            .AccuMailValidationHandler(Arg.Any<CandidateIntentionStatementViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(true);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as ViewResult;

        // Assert
        var arg0 = _accuMailValidatorService.ReceivedCalls().ToList()[0].GetArguments()[0] as CandidateIntentionStatementViewModel;
        Assert.That(arg0, Is.Not.Null);
        Assert.That(arg0.Addresses, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate"), Is.Not.Null);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing"), Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Candidate").IsEmpty(), Is.False);
            Assert.That(arg0.Addresses.Find(x => x.Purpose == "Mailing").IsEmpty(), Is.False);
        });
    }
    [Test]
    public async Task Page01_ShouldReturnViewWhenNoActionMatch()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.Cancel,
            Id = 1,
        };

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.That(result.Model, Is.InstanceOf<AmendCandidateRegistrationStep01ViewModel>());
    }

    [Test]
    public async Task Page01_ShouldRedirect_Dashboard_OnSaveAndClose()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _amendCandidateRegistrationCtlSvc
            .Page01Submit(model, _controller.ModelState, default, true)
            .Returns(1);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    [Test]
    public async Task Page01_ShouldRedirect_Page02OnContinue()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
        };
        _amendCandidateRegistrationCtlSvc
            .Page01Submit(model, _controller.ModelState, default, true)
            .Returns(1);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page02"));
            Assert.That(result.RouteValues?["Id"], Is.EqualTo(1));
        });
    }
    [Test]
    public async Task Page01_Post_InValid()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _controller.ModelState.AddModelError("Key", "Test");
        var id = 1;
        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page01_Save_Valid()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _amendCandidateRegistrationCtlSvc
            .Page01Submit(model, _controller.ModelState, default, true)
            .Returns(1);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.ActionName, Is.EqualTo("Index"));
            Assert.That(result?.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    [Test]
    public async Task Page01Continue_AddressValidationResponseInvalid()
    {
        // Arrange
        var addressDto = new AddressDto
        {
            Street = "123 Main St",
            Street2 = "Apt 4",
            City = "Los Angeles",
            State = "CA",
            Country = "USA",
            Zip = "90001",
            Type = "Home"
        };

        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
            AddressValidationResponse = new FilerPortal.Models.Registrations.AddressValidationResponse
            {
                CandidateSuggestedAddress = new AddressViewModel(addressDto)
            }
        };

        _amendCandidateRegistrationCtlSvc
            .Page01Submit(model, _controller.ModelState, Arg.Any<CancellationToken>(), true)
            .Returns(1);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as ViewResult;
        Assert.That(result, Is.InstanceOf<ViewResult>());

    }
    [Test]
    public async Task Page01Save_AddressValidationResponseInvalid()
    {
        // Arrange
        var addressDto = new AddressDto
        {
            Street = "123 Main St",
            Street2 = "Apt 4",
            City = "Los Angeles",
            State = "CA",
            Country = "USA",
            Zip = "90001",
            Type = "Home"
        };

        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
            AddressValidationResponse = new FilerPortal.Models.Registrations.AddressValidationResponse
            {
                CandidateSuggestedAddress = new AddressViewModel(addressDto)
            }
        };

        _amendCandidateRegistrationCtlSvc
            .Page01Submit(model, _controller.ModelState, Arg.Any<CancellationToken>(), true)
            .Returns(1);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as ViewResult;
        Assert.That(result, Is.InstanceOf<ViewResult>());

    }
    [Test]
    public async Task Page01Save_ModelStateInvalid()
    {
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _controller.ModelState.AddModelError("Key", "Test");

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

    }
    [Test]
    public async Task Page01Continue_Success()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
        };


        _amendCandidateRegistrationCtlSvc
            .Page01Submit(model, _controller.ModelState, Arg.Any<CancellationToken>(), true)
            .Returns(1);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as RedirectToActionResult;
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page02"));
    }
    [Test]
    public async Task Page01Save_Success()
    {
        // Arrange
        var addressDto = new AddressDto
        {
            Street = "123 Main St",
            Street2 = "Apt 4",
            City = "Los Angeles",
            State = "CA",
            Country = "USA",
            Zip = "90001",
            Type = "Home"
        };

        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
            AddressValidationResponse = new FilerPortal.Models.Registrations.AddressValidationResponse
            {
                CandidateSuggestedAddress = new AddressViewModel(addressDto)
            }
        };
        _amendCandidateRegistrationCtlSvc
            .Page01Submit(model, _controller.ModelState, Arg.Any<CancellationToken>(), true)
            .Returns(1);

        // Act
        var result = await _controller.Page01(model, _accuMailValidatorService) as ViewResult;
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Page01AddressValidation_ShouldReturnEmptyUrlWhenContinueEditingBothAddresses()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Id = 1,
            CandidateAddressConfirmation = new AddressConfirmationModel { IsContinueEditing = true },
            MailingAddressConfirmation = new AddressConfirmationModel { IsContinueEditing = true }
        };

        var httpContext = new DefaultHttpContext { Request = { Scheme = "https", Host = new HostString("localhost") } };
        _controller.ControllerContext.HttpContext = httpContext;

        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page01AddressValidation(model);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result?.Value, Is.Not.Null);
    }

    [Test]
    public async Task Page01AddressValidation_ShouldReturnEmptyUrlWhenContinueEditingEitherAddress()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            CandidateAddressConfirmation = new AddressConfirmationModel { IsContinueEditing = true },
            MailingAddressConfirmation = new AddressConfirmationModel { IsContinueEditing = false }
        };

        _amendCandidateRegistrationCtlSvc.Page01Submit(model, _controller.ModelState, default, true)
            .Returns(Task.FromResult<long?>(1));

        var httpContext = new DefaultHttpContext { Request = { Scheme = "https", Host = new HostString("localhost") } };
        _controller.ControllerContext.HttpContext = httpContext;

        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page01AddressValidation(model);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result?.Value, Is.Not.Null);
    }

    [Test]
    public async Task Page01AddressValidation_ShouldRedirectToPage05WithRegistrationId()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep01ViewModel
        {
            Id = 1,
            CandidateAddressConfirmation = new AddressConfirmationModel { IsContinueEditing = false },
            MailingAddressConfirmation = new AddressConfirmationModel { IsContinueEditing = false }
        };

        _amendCandidateRegistrationCtlSvc.Page01Submit(model, _controller.ModelState, default, true)
            .Returns(Task.FromResult<long?>(1));

        var httpContext = new DefaultHttpContext { Request = { Scheme = "https", Host = new HostString("localhost") } };
        _controller.ControllerContext.HttpContext = httpContext;

        var tempData = new Mock<ITempDataDictionary>();
        tempData.Setup(t => t["Id"]).Returns(1);
        _controller.TempData = tempData.Object;

        // Act
        var result = await _controller.Page01AddressValidation(model);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result?.Value, Is.Not.Null);
    }
    #endregion

    #region Page02
    [Test]
    public async Task Page02_Get_ShouldReturnViewWhenModelStateInvalid()
    {
        // Arrange
        long id = 1;
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = await _controller.Page02(id, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page02_WhenRegistrationStatusIsIncorrect_ReturnsBadRequest()
    {
        // Arrange
        var registration = new CandidateIntentionStatementResponseDto
        {
            StatusId = 999 // invalid status
        };

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Page02(123);

        // Assert
        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }
    [Test]
    public async Task Page02_Get_RegistrationIdInvalid()
    {
        // Arrange
        var id = 1;
        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync((CandidateIntentionStatementResponseDto?)null);
        // Act
        var result = await _controller.Page02(id, default);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page02_Get_Success_OnAmendedDraft_RegistrationHadNoElectionRaceId()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var registration = new CandidateIntentionStatementResponseDto
        {
            Id = 1,
            OriginalId = 1,
            StatusId = 1
        };
        var originalRegistration = new CandidateIntentionStatementResponseDto
        {
            Id = 2,
            ElectionRaceId = 100
        };
        var electionRace = new ElectionRace { Election = new Election { Name = "Name", Id = 2, ElectionDate = date }, OfficeId = 1, DistrictId = 1 };
        _candidateIntentionRegistrationSvc
            .SetupSequence(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration)
            .ReturnsAsync(originalRegistration);

        _electionSvc
            .GetElectionRace(100)
            .Returns(electionRace);

        // Act
        var result = await _controller.Page02(1, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page02_Get_Success_OnAmendedDraft_RegistrationHasElectionRaceId()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var registration = new CandidateIntentionStatementResponseDto
        {
            Id = 1,
            OriginalId = 1,
            ElectionRaceId = 100,
            StatusId = 1
        };
        var originalRegistration = new CandidateIntentionStatementResponseDto
        {
            Id = 2,
            ElectionRaceId = 100
        };
        var electionRace = new ElectionRace { Election = new Election { Name = "Name", Id = 2, ElectionDate = date }, OfficeId = 1, DistrictId = 1 };

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        _electionSvc
            .GetElectionRace(100)
            .Returns(electionRace);

        // Act
        var result = await _controller.Page02(1, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page02_Post_ShouldReturnNotFoundWhenIdMissing()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Id = null,
        };

        // Act
        var result = await _controller.Page02(model, default);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page02_Post_ShouldRedirectToDashboardOnSaveAndClose()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _amendCandidateRegistrationCtlSvc
            .Page02Submit(0, new AmendCandidateRegistrationStep02ViewModel { Id = 1 }, _controller.ModelState, true)
            .Returns(Task.FromResult(default(object)));

        // Act
        var result = await _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
    }
    [Test]
    public async Task Page02_Post_ShouldReturnViewOnSaveAndCloseWhenModelStateInvalid()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 1,
        };
        _amendCandidateRegistrationCtlSvc
            .Page02Submit(Arg.Any<long>(), Arg.Any<AmendCandidateRegistrationStep02ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)))
            .AndDoes(x => _controller.ModelState.AddModelError("Key", "Error"));

        // Act
        var result = await _controller.Page02(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ContainsKey("Key"), Is.True);
        });
    }
    [Test]
    public async Task Page02_Post_ShouldRedirectToPage03OnContinue()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
        };

        _amendCandidateRegistrationCtlSvc
            .Page02Submit(Arg.Any<long>(), Arg.Any<AmendCandidateRegistrationStep02ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)));

        // Act
        var result = await _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page03"));
    }
    [Test]
    public async Task Page02_Post_ShouldReturnViewOnContinueWhenModelStateInvalid()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.Continue,
            Id = 1,
        };

        _amendCandidateRegistrationCtlSvc
            .Page02Submit(Arg.Any<long>(), Arg.Any<AmendCandidateRegistrationStep02ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)))
            .AndDoes(x => _controller.ModelState.AddModelError("Key", "Error"));

        // Act
        var result = await _controller.Page02(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ContainsKey("Key"), Is.True);
        });
    }
    [Test]
    public async Task Page02_Post_ShouldRedirectToPage01OnPreviousSaveSuccess()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.Previous,
            Id = 1,
        };
        _amendCandidateRegistrationCtlSvc
            .Page02Submit(Arg.Any<long>(), Arg.Any<AmendCandidateRegistrationStep02ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)));

        // Act
        var result = await _controller.Page02(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Page01"));
    }
    [Test]
    public async Task Page02_Post_ShouldViewOnPreviousWhenModelStateInvalid()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.Previous,
            Id = 1,
        };
        _amendCandidateRegistrationCtlSvc
            .Page02Submit(Arg.Any<long>(), Arg.Any<AmendCandidateRegistrationStep02ViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<bool>())
            .Returns(Task.FromResult(default(object)))
            .AndDoes(x => _controller.ModelState.AddModelError("Key", "Error"));

        // Act
        var result = await _controller.Page02(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ContainsKey("Key"), Is.True);
        });
    }
    [Test]
    public async Task Page02_Post_ShouldReturnViewWhenNoActionMatch()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = null,
            Id = 1,
        };

        // Act
        var result = await _controller.Page02(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page02_Post_ShouldReturnViewOnCancel()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.Cancel,
            Id = 1,
        };

        // Act
        var result = await _controller.Page02(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page02_Post_ShouldReturnViewOnClose()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.Close,
            Id = 1,
        };

        // Act
        var result = await _controller.Page02(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page02_Post_ShouldReturnViewWhenOnSubmit()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep02ViewModel
        {
            Action = FormAction.Submit,
            Id = 1,
        };

        // Act
        var result = await _controller.Page02(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Page03
    [Test]
    public async Task Page03_Valid()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        // Arrange
        long id = 1;
        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            Id = 1,
            Action = FormAction.Submit,
            ElectionDate = date
        };
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 1.00M,
            ExpenditureLimitAccepted = true,
            StatusId = 1,
        };
        var election = new Election
        {
            Name = "Test",
            ElectionDate = date,
        };


        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatementElection(It.IsAny<int>()))
            .ReturnsAsync(election);
        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(id))
            .ReturnsAsync(1.00M);

        _amendCandidateRegistrationCtlSvc.Page03BuildViewModel(id).Returns(model);

        // Act
        var result = await _controller.Page03(id) as ViewResult;

        // Asert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(1));
            Assert.That(model.Action, Is.EqualTo(FormAction.Submit));
            Assert.That(model.ElectionDate, Is.Not.Null);
        });
    }
    [Test]
    public async Task Page03_WhenRegistrationStatusIsIncorrect_ReturnsBadRequest()
    {
        // Arrange
        var registration = new CandidateIntentionStatementResponseDto
        {
            StatusId = 999 // invalid status
        };

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Page03(123);

        // Assert
        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }
    [Test]
    public async Task Page03_InValid()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        long id = 1;
        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            Id = 1,
            Action = FormAction.Submit,
            ElectionDate = date
        };
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 1.00M,
            ExpenditureLimitAccepted = true,
            StatusId = 1,
        };
        var election = new Election
        {
            Name = "Test",
            ElectionDate = date,
        };

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(id))
            .ReturnsAsync(registration);

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(id))
            .ReturnsAsync(100.00M);

        _amendCandidateRegistrationCtlSvc.Page03BuildViewModel(id).Returns(model);

        // Act
        var result = await _controller.Page03(id) as ViewResult;

        Assert.That(result, Is.InstanceOf<ViewResult>());
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task Page03_ReturnsNotFound_WhenRegistrationIsNull()
    {
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
                .ReturnsAsync((CandidateIntentionStatementResponseDto?)null);

        var result = await _controller.Page03(123);

        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Page03_RedirectsToPage02_WhenExpenditureIsZero_AndRefererIsPage04()
    {
        // Arrange
        long id = 1;
        var context = new DefaultHttpContext();
        context.Request.Headers.Referer = $"https://test/CandidateRegistration/Page04/{id}";

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = context
        };

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(id))
            .ReturnsAsync(new CandidateIntentionStatementResponseDto { StatusId = 1 });

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(id))
            .ReturnsAsync(0.00M);

        // Act
        var result = await _controller.Page03(id) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Page02"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(id));
        });
    }


    [Test]
    public async Task Page03_ReturnsViewWithModel_WhenModelStateIsValid()
    {
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
               .ReturnsAsync(new CandidateIntentionStatementResponseDto { ExpenditureCeilingAmount = 100M, StatusId = 1 });

        var model = new AmendCandidateRegistrationStep03ViewModel();
        _amendCandidateRegistrationCtlSvc.Page03BuildViewModel(Arg.Any<long>())
            .Returns(model);

        var result = await _controller.Page03(1) as ViewResult;

        Assert.That(result?.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_ReturnsEmptyModel_WhenModelStateIsInvalid()
    {
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
               .ReturnsAsync(new CandidateIntentionStatementResponseDto { ExpenditureCeilingAmount = 100M, StatusId = 1 });

        var model = new AmendCandidateRegistrationStep03ViewModel();
        _amendCandidateRegistrationCtlSvc.Page03BuildViewModel(Arg.Any<long>())
            .Returns(model);

        _controller.ModelState.AddModelError("key", "error");

        var result = await _controller.Page03(1) as ViewResult;

        Assert.That(result?.Model, Is.InstanceOf<AmendCandidateRegistrationStep03ViewModel>());
    }

    [Test]
    public async Task Page03_ReturnsNotFound_WhenIdIsNullOrZero()
    {
        var model = new AmendCandidateRegistrationStep03ViewModel { Id = 0 };

        var result = await _controller.Page03(model);

        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Page03_ReturnsView_WhenModelStateIsInvalid()
    {
        var model = new AmendCandidateRegistrationStep03ViewModel { Id = 1 };
        _controller.ModelState.AddModelError("key", "error");

        var result = await _controller.Page03(model) as ViewResult;

        Assert.That(result?.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task Page03_ExecutesContinueBranch_WhenActionIsContinue()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep03ViewModel { Id = 1, Action = FormAction.Continue };

        // Act
        var result = await _controller.Page03(model);

        // Assert
        Assert.That(result, Is.InstanceOf<IActionResult>());
        Assert.That(result, Is.TypeOf<RedirectToActionResult>().Or.TypeOf<ViewResult>());
    }

    [Test]
    public async Task Page03_AddsModelError_WhenActionIsUnknown()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep03ViewModel { Id = 1, Action = (FormAction)999 };

        // Act
        var result = await _controller.Page03(model) as ViewResult;

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(_controller.ModelState[string.Empty]?.Errors, Has.Count.GreaterThanOrEqualTo(1));
            Assert.That(result?.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task Page03_Fails_WhenApiExceptionThrown()
    {
        var model = new AmendCandidateRegistrationStep03ViewModel { Id = 1, Action = FormAction.Continue };

        var httpResponse = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent("error")
        };

        var apiEx = await ApiException.Create(
            new HttpRequestMessage(HttpMethod.Post, "http://test"),
            HttpMethod.Post,
            httpResponse,
            null);

        _amendCandidateRegistrationCtlSvc
            .When(x => x.Page03BuildViewModel(Arg.Any<long>()))
            .Do(_ => throw apiEx);

        var result = await _controller.Page03(model) as ViewResult;

        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task Page03_ExpenditureCeilingZero_ReferrerNotFromPage04_RedirectsToPage04()
    {
        // Arrange
        var regId = 1L;
        var model = new AmendCandidateRegistrationStep03ViewModel { Id = regId };

        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 0.00m,
            StatusId = 1
        };

        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers.Referer = "https://localhost/CandidateRegistration/Page02/1";

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = httpContext
        };

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);
        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatementExpenditureExpenseAmount(regId))
            .ReturnsAsync(0.00M);

        _authorizationSvc.VerifyAuthorization(Arg.Any<AuthorizationRequest>()).Returns(Task.CompletedTask);

        // Act
        var result = await _controller.Page03(regId) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.ActionName, Is.EqualTo("Page04"));
    }

    [Test]
    public async Task Page03_Post_WhenActionIsPrevious_AndModelIsValid_RedirectsToPage02()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            Id = 123,
            Action = FormAction.Previous
        };

        // No model state errors
        _amendCandidateRegistrationCtlSvc.Page03ValidatePersonalFundsDate(model, Arg.Any<ModelStateDictionary>());

        _amendCandidateRegistrationCtlSvc.Page03Submit(model, Arg.Any<ModelStateDictionary>(), true).Returns(Task.CompletedTask);

        // Act
        var result = await _controller.Page03(model);
        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirect, Is.Not.Null);
            Assert.That(redirect!.ActionName, Is.EqualTo("Page02"));
            Assert.That(redirect.RouteValues["id"], Is.EqualTo(123));
        });
    }
    [Test]
    public async Task Page03_Post_WhenActionIsSaveAndClose_AndModelIsValid_CallsSubmitAndRedirectsToDashboard()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            Id = 456,
            Action = FormAction.SaveAndClose
        };

        // Make model state valid
        _amendCandidateRegistrationCtlSvc.Page03ValidatePersonalFundsDate(model, Arg.Any<ModelStateDictionary>());
        _amendCandidateRegistrationCtlSvc.Page03Submit(model, Arg.Any<ModelStateDictionary>(), true).Returns(Task.CompletedTask);

        // Act
        var result = await _controller.Page03(model);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Dashboard"));
        });
    }
    #endregion

    #region Page04
    [Test]
    public async Task Page04_Get_ShouldReturnNotFoundWhenModelStateInvalid()
    {
        // Arrange
        var registration = new CandidateIntentionStatementResponseDto
        {
            Id = 1
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        _controller.ModelState.AddModelError("Key", "Test");

        // Act
        var result = await _controller.Page04(1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page04_Get_ShouldReturnNotFoundWhenNullRecord()
    {
        // Arrange
        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
        .ReturnsAsync((CandidateIntentionStatementResponseDto?)null);

        // Act
        var result = await _controller.Page04(1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Page04_Get_ShouldReturnView()
    {
        // Arrange
        var registration = new CandidateIntentionStatementResponseDto
        {
            Id = 1,
            ElectionRaceId = 1,
            StatusId = 1
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Page04(1);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page04_WhenRegistrationStatusIsIncorrect_ReturnsBadRequest()
    {
        // Arrange
        var registration = new CandidateIntentionStatementResponseDto
        {
            StatusId = 999 // invalid status
        };

        _candidateIntentionRegistrationSvc
            .Setup(s => s.GetCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Page04(123);

        // Assert
        Assert.That(result, Is.InstanceOf<BadRequestResult>());
    }
    [Test]
    public async Task Page04_Post_ShouldViewWhenModelStateInvalid()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            RegistrationId = 1,
        };
        _controller.ModelState.AddModelError("Key", "Test");
        var candidateIntentStep2 = new CandidateIntentStep2()
        {
            RegistrationId = 1,
            SelectedElection = 2025,
            SelectedJurisdiction = "state",
            SelectedElectionYear = "2025",
            SelectedOffice = 10,
            SelectedDistrict = 5,
            SelectedPartyAffiliation = 2
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 0.00M,
            ExpenditureExceeded = false,
            ExpenditureLimitAccepted = false,
            ElectionRaceId = 1,
            MailingAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            },
            CandidateAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            }
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page04(model, default);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page04_Post_ShouldRedirectToDashboardOnSaveAndClose()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            RegistrationId = 1,
            Action = FormAction.SaveAndClose,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page04(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("Dashboard"));
    }
    [Test]
    public async Task Page04_Post_ShouldRedirectToPage03OnPrevious()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Previous,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Page04(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result?.ActionName, Is.EqualTo("Page03"));
    }
    [Test]

    public async Task Page04_ShouldRedirectToSubmissionOnPost()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Submit,
            IsSubmission = true,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()

        };
        var registration = new CandidateIntentionStatementResponseDto
        {
            ExpenditureCeilingAmount = 0.00M,
            ExpenditureExceeded = false,
            ExpenditureLimitAccepted = false,
            ElectionRaceId = 1,
            MailingAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            },
            CandidateAddress = new AddressDto
            {
                Street = "123 Main St",
                City = "Test City",
                State = "CA",
            }
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page04(model, default) as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Submission"));
    }
    [Test]

    public async Task Page04_ShouldReturnViewOnPostSubmitValidationError()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Submit,
            IsSubmission = true,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
            {
                new("FieldName","Error","ErrorType","Error")
            }

        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page04(model, default) as ViewResult;
        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    [Test]
    public async Task Page04_ShouldRedirectToAttestationRequestSentOnPost()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            RegistrationId = 1,
            Action = FormAction.Submit,
            IsSubmission = false,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<long>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page04(model, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("AttestationRequestSent"));
    }
    [Test]
    public async Task Page04_ShouldReturnNotFoundWhenMissingIdOnPost()
    {
        // Arrange
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            Id = null,
            Action = FormAction.Submit,
            IsSubmission = true,
        };
        var resp = new RegistrationResponseDto
        {
            Id = 1,
            Valid = true,
        };

        _candidateIntentionRegistrationSvc.Setup(s => s.SubmitCandidateIntentionStatement(It.IsAny<int>()))
            .ReturnsAsync(resp);

        // Act
        var result = await _controller.Page04(model, default);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    #endregion

    #region Submission
    [Test]
    public void Submission_RegistrationIdInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("key", "error");
        var id = 1;
        var model = new AmendCandidateRegistrationStep04ViewModel
        {
            RegistrationId = 0,
            IsSubmission = false,
            CandidateName = "John Doe",
            ExecutedOn = DateTime.Today
        };
        var result = _controller.Submission(id);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public void Submission_ModelStateValid_CallsSubmitCandidateIntentionStatement_AndReturnsView()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var id = 1;
        var model = new AmendCandidateRegistrationStep05ViewModel
        {
            ExecutedOn = date
        };
        var result = _controller.Submission(id) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.ViewName, Is.EqualTo("Page05"));
            Assert.That(model.ExecutedOn, Is.LessThanOrEqualTo(date));

        });
    }
    #endregion

    #region Attestation
    [Test]
    public void AttestationRequestSent_RegistrationIdInvalid_ReturnsNotFound()
    {
        _controller.ModelState.AddModelError("key", "error");
        var id = 1;
        var model = new AmendCandidateRegistrationStep05ViewModel
        {
            ExecutedOn = DateTime.Today
        };
        var result = _controller.AttestationRequestSent(id);
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public void AttestationRequestSent_ModelStateValid_CallsSubmitCandidateIntentionStatement_AndReturnsView()
    {
        var id = 1;
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var model = new AmendCandidateRegistrationStep05ViewModel
        {
            ExecutedOn = date,
            PendingItems = new List<PendingItemSharedViewModel>()
        };
        var result = _controller.AttestationRequestSent(id) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.ViewName, Is.EqualTo("Page05"));
            Assert.That(model.ExecutedOn, Is.LessThanOrEqualTo(date));
        });
    }
    #endregion

    #region SetCommonViewData
    [Test]
    public void SetCommonViewData_SetsExpectedViewData()
    {
        // Act
        var httpContext = new DefaultHttpContext(); // Mock HttpContext
        var actionContext = new ActionContext(httpContext, new RouteData(), new ActionDescriptor());
        var context = new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            new Dictionary<string, object?>(),
            _controller
        );

        // Act
        _controller.OnActionExecuting(context);
        var breadcrumbs = _controller.ViewData[LayoutConstants.Breadcrumbs] as List<Breadcrumb>;
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(_controller.ViewData[LayoutConstants.Title], Is.EqualTo("CIS Registration"));
            Assert.That(_controller.ViewData["ProgressItem1Name"], Is.EqualTo("1. Candidate"));
            Assert.That(_controller.ViewData["ProgressItem2Name"], Is.EqualTo("2. Election"));
            Assert.That(_controller.ViewData["ProgressItem3Name"], Is.EqualTo("3. Verification"));
            Assert.That(breadcrumbs, Is.Not.Null);
            Assert.That(breadcrumbs?.Count, Is.EqualTo(2));
            if (breadcrumbs != null)
            {
                Assert.That(breadcrumbs[0].Name, Is.EqualTo("Filer Portal"));
                Assert.That(breadcrumbs[0].Url, Is.EqualTo("/FilerPortal"));
                Assert.That(breadcrumbs[1].Name, Is.EqualTo("Candidate Registration"));
                Assert.That(breadcrumbs[1].Url, Is.EqualTo("/CandidateRegistration"));
            }
        });

    }
    #endregion

    #region SetLocalizedToast
    [Test]
    public void SetLocalizedToast_Valid()
    {

        _controller.SetLocalizedToast("ToastMessageKey");

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(_controller.TempData["ToastMessage"], Is.EqualTo("text"));
            Assert.That(_controller.TempData["ToastType"], Is.EqualTo("e-toast-success"));
            Assert.That(_controller.TempData["ToastShowCloseButton"], Is.EqualTo("true"));
            Assert.That(_controller.TempData["ToastX"], Is.EqualTo("Right"));
            Assert.That(_controller.TempData["ToastY"], Is.EqualTo("Bottom"));
        });
    }
    #endregion
}

