@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.Models.FilerDisclosure.Filings
@using SOS.CalAccess.Models.FilerRegistration.Filers
@model SOS.CalAccess.FilerPortal.Models.Disclosure.DisclosureSummaryViewModel;
@using Microsoft.AspNetCore.Html
@using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models
@inject IHtmlLocalizer<SharedResources> Localizer

@{
    var controller = ViewContext.RouteData.Values["controller"]?.ToString();
    var targetAction = ViewBag.ReportType == FilingType.LobbyistReport.Name ? "CreateLobbyistCampaignContribution" : "CreateLobbyingCampaignContribution";
    var bodyText = ViewBag.ReportType == FilingType.LobbyistReport.Name ? SharedLocalizer[ResourceConstants.CampaignContributionsBodyLobbyist] : SharedLocalizer[ResourceConstants.CampaignContributionsBodyLobbyistEmployer];

    var yesNoOptions = new Dictionary<string, string>
    {
        { "True", Localizer["Common.Yes"].Value },
        { "False", Localizer["Common.No"].Value }
    };

    var buttonBarModel = new ButtonBarModel
    {
        LeftButtons =
        [
            new ButtonConfig
            {
                Type = ButtonType.Link,
                CssClass = "btn btn-primary btn-sm",
                InnerTextKey = SharedLocalizer["Common.SaveAndClose"].Value,
                Url = Url.Action("HandleUpdateStatusFilingSummary", controller, new
                {
                    filerId = Model.FilerId,
                    filingId = Model.Id,
                    reportType = ViewBag.ReportType,
                    viewName = ViewBag.PartialView,
                    filingSummaryStatusName = FilingSummaryStatus.InProgress.Name
                })
            }
        ],
        RightButtons =
        [
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                CssClass = "btn btn-flat-primary btn-sm ms-auto",
                HtmlContent = new HtmlString(
                    "<a asp-controller='{controller}' asp-action='Index' asp-route-viewName='' class='btn btn-flat-primary btn-sm ms-auto' " +
                    "data-bs-toggle='modal' " +
                    "data-bs-target='#cancelConfirmModal'>" +
                    @SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.Cancel"].Value +
                    "</a>"
                )
            },
        ]
    };

    var cancelModal = new CancelConfirmModal(
        Title: "Confirm Cancellation",
        Body: "Are you sure you want to cancel? Any unsaved changes will be lost.",
        CloseButtonText: "No, Stay Here",
        SubmitButtonText: "Yes, Cancel",
        ActionUrl: Url.Action("CancelTransaction", controller, new { filerId = Model.FilerId, filingId = Model.Id, reportType = ViewBag.ReportType })!
    );
}

<h2 class="card-title mb-4">@SharedLocalizer[ResourceConstants.CampaignContributionsTitle]</h2>
<p class="card-subtitle mb-3">@bodyText</p>
<p class="mb-5">
    <a href="/" class="link-primary">@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.ReviewInstruction"]</a>
</p>

@if (ViewBag.ReportType == FilingType.LobbyistEmployerReport.Name)
{
    @using (Html.BeginForm("UpdateContributionsInExistingStatements", controller, FormMethod.Post))
    {
        @Html.HiddenFor(m => m.Id)
        @Html.HiddenFor(m => m.FilingSummaryId)
        @Html.HiddenFor(m => m.FilerId)
        @Html.HiddenFor(m => m.ViewName)
        @Html.HiddenFor(m => m.Subtotal)
        <input type="hidden" name="reportType" value="@ViewBag.ReportType" />
        <input type="hidden" id="relatedFilerIdsInput" name="relatedFilerIds" value="" />

        <div id="contributionsContained" class="mb-4 border border-light-subtle p-3">
            <h4>@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.DisclosureStatements"]</h4>
            <div class="mb-3">
                @Html.Radio(SharedLocalizer, "ContributionsInExistingStatements",
                         Localizer["FilerPortal.Disclosure.CampaignContributions.ContainedInDisclosureStatements"].Value,
                         yesNoOptions, Model.ContributionsInExistingStatements ?? "", true, false)
                @if (Model.Messages.Validations != null && 
                     Model.Messages.Validations.Any() && 
                     Model.Messages.Validations.ContainsKey("IsContributionContainedInDisclosure"))
                {
                    <p class="text-danger">@Model.Messages.Validations["IsContributionContainedInDisclosure"]?.Message</p>
                }
            </div>
            <div id="contributionsDetailDiv" class="mb-3">
                <div class="mb-3 d-flex flex-row gap-2">
                    <button type="button" id="addMajorDonorBtn" class="btn btn-outline-primary btn-sm">
                        @SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.AddMajorDonor"]
                    </button>
                    <button type="button" id="addRecipientCommitteeBtn" class="btn btn-outline-primary btn-sm">
                        @SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.AddRecipientCommittee"]
                    </button>
                </div>

                <div class="table-responsive">
                    <table class="table w-100 border">
                        <thead>
                            <tr>
                                <th>@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Name"]</th>
                                <th>@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Type"]</th>
                                <th>@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.RelatedFilers.FilerId"]</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="relatedFilersTableBody">
                            @if (Model.RelatedFilers != null && Model.RelatedFilers.Any())
                            {
                                @foreach (var filer in Model.RelatedFilers)
                                {
                                    <tr data-filer-id="@filer.Id">
                                        <td>@filer.Name</td>
                                        <td>@filer.FilerType</td>
                                        <td>@filer.FilerId</td>
                                        <td class="text-end">
                                            <button type="button" class="btn p-0" onclick="deleteRelatedFiler(@filer.Id)">
                                                <i class="bi bi-trash" aria-label="Delete"></i>
                                            </button>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="4" class="text-center">
                                        @SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.RelatedFilers.NoRecordAddedYet"]
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                @if (Model.Messages.Validations != null &&
                    Model.Messages.Validations.Any() &&
                    Model.Messages.Validations.ContainsKey("RelatedFilerIds"))
                {
                    <p class="text-danger">@Model.Messages.Validations["RelatedFilerIds"]?.Message</p>
                }

                <div id="selectMajorDonorOrRecipientCommittee" style="max-width: 660px;">
                    <div class="mb-3" id="majorDonorSearchField">
                        <label class="form-label" for="majorDonorAutoComplete">@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.SearchMajorDonorLabel"]</label>
                        <div class="d-flex gap-2">
                            @Html.Raw(@Html.EJS().AutoComplete("majorDonorAutoComplete")
                                     .Fields(f => f.Value("Id").Text("DisplayText"))
                                     .Placeholder(Localizer["FilerPortal.Disclosure.CampaignContributions.SearchMajorDonorPlaceholder"].Value)
                                     .Filtering("onMajorDonorFiltering")
                                     .Change("onMajorDonorSelectionChange")
                                     .CssClass("form-control")
                                     .Render())
                            <button type="button" id="addMajorDonorSearchBtn" class="btn btn-outline-primary btn-sm">
                                @SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Add"]
                            </button>
                        </div>
                    </div>
                    <div class="mb-3" id="recipientCommitteeSearchField">
                        <label class="form-label" for="recipientCommitteeAutoComplete">@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.SearchRecipientCommitteeLabel"]</label>
                        <div class="d-flex gap-2">
                            @Html.Raw(@Html.EJS().AutoComplete("recipientCommitteeAutoComplete")
                                     .Fields(f => f.Value("Id").Text("DisplayText"))
                                     .Placeholder(Localizer["FilerPortal.Disclosure.CampaignContributions.SearchRecipientCommitteePlaceholder"].Value)
                                     .Filtering("onRecipientCommitteeFiltering")
                                     .Change("onRecipientCommitteeSelectionChange")
                                     .CssClass("form-control")
                                     .Render())
                            <button type="button" id="addRecipientCommitteeSearchBtn" class="btn btn-outline-primary btn-sm">
                                @SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.RelatedFilers.Add"]
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="">
                <button type="submit" id="saveBtn" class="btn btn-primary btn-sm">@SharedLocalizer[ResourceConstants.Save]</button>
            </div>
        </div>
    }
}

<div class="d-flex justify-content-between mb-3">
    <h3>@SharedLocalizer[ResourceConstants.CampaignContributionsTableTitle]</h3>
    <p>@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.Subtotal"]: @Model.Subtotal?.ToString("C")</p>
</div>
<p class="card-subtitle mb-3">@SharedLocalizer[ResourceConstants.CampaignContributionsTableBody]</p>

<div class="d-flex gap-2 mb-3">
    <div>
        <a asp-controller="Transaction"
           asp-action="@targetAction"
           asp-route-filerId="@Model.FilerId"
           asp-route-filingId="@Model.Id"
           class="btn btn-outline-primary btn-sm">
            @SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.AddNew"]
        </a>
    </div>
    <div>
        <button type="button" class="btn btn-outline-primary btn-sm">@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.MultipleTransaction"]</button>
    </div>
    <div>
        <button type="button" class="btn btn-outline-primary btn-sm">@SharedLocalizer["FilerPortal.Disclosure.CampaignContributions.Export"]</button>
    </div>
</div>

@await Html.PartialAsync("_SmallGrid", Model.CampaignContributionsGridModel)

<div class="button-bar-container mb-5 mt-3">
    <partial name="_ButtonBar" model="buttonBarModel" />
</div>

<partial name="_CancelConfirmModal" model="cancelModal" />

<script>
    // Track selected filers from AutoComplete components (global scope)
    let selectedMajorDonor = null;
    let selectedRecipientCommittee = null;

    // Track related filers (display data and IDs for submission)
    let relatedFilers = [];

    document.addEventListener('DOMContentLoaded', function() {
        // Get all radio inputs for ContributionsInExistingStatements
        const radioInputs = document.querySelectorAll('input[name="ContributionsInExistingStatements"]');

        // Get the search input fields by id
        const majorDonorField = document.getElementById('majorDonorSearchField');
        const recipientCommitteeField = document.getElementById('recipientCommitteeSearchField');

        // Get the selection container
        const selectionContainer = document.getElementById('selectMajorDonorOrRecipientCommittee');

        // Get the "Add Major Donor" and "Add Recipient Committee" buttons
        const addMajorDonorBtn = document.querySelector('.d-flex.flex-row.gap-2 button:first-child');
        const addRecipientCommitteeBtn = document.querySelector('.d-flex.flex-row.gap-2 button:nth-child(2)');

        // Get the "Add" buttons within each search field
        const majorDonorAddBtn = document.getElementById('addMajorDonorSearchBtn');
        const recipientCommitteeAddBtn = document.getElementById('addRecipientCommitteeSearchBtn');

        // Get the table body for adding new filers
        const filerTableBody = document.querySelector('table tbody');

        // Get the Save button
        const saveButton = document.querySelector('#contributionsContained .btn-primary');

        // Initialize relatedFilers array from existing table data
        function initializeRelatedFilersArray() {
            relatedFilers = [];
            const rows = filerTableBody.querySelectorAll('tr[data-filer-id]');

            rows.forEach(row => {
                const id = parseInt(row.getAttribute('data-filer-id'));
                const cells = row.querySelectorAll('td');

                if (cells.length >= 3) {
                    relatedFilers.push({
                        id: id,
                        name: cells[0].textContent,
                        type: cells[1].textContent,
                        filerId: cells[2].textContent
                    });
                }
            });
        }

        // Function to toggle the contributions detail div visibility
        function toggleDetailDiv() {
            const yesRadio = document.querySelector('input[name="ContributionsInExistingStatements"][value="True"]');
            const detailDiv = document.getElementById('contributionsDetailDiv');

            if (yesRadio && yesRadio.checked) {
                detailDiv.style.display = 'block';
            } else {
                detailDiv.style.display = 'none';
            }
        }

        // Function to handle showing the Major Donor search field
        function showMajorDonorField() {
            hideAllSearchFields();
            majorDonorField.style.display = 'block';
        }

        // Function to handle showing the Recipient Committee search field
        function showRecipientCommitteeField() {
            hideAllSearchFields();
            recipientCommitteeField.style.display = 'block';
        }

        // Function to hide all search fields
        function hideAllSearchFields() {
            majorDonorField.style.display = 'none';
            recipientCommitteeField.style.display = 'none';
        }

        // Function to add a filer to the table
        function addFilerToTable(name, type, filerId) {
            // Generate a temporary ID for the row
            const tempId = Date.now();

            // Add to our relatedFilers array
            relatedFilers.push({
                id: tempId,
                name: name,
                type: type,
                filerId: filerId
            });

            // Check if there's a "No record added yet" row and remove it
            const noRecordRow = filerTableBody.querySelector('tr td[colspan="4"]');
            if (noRecordRow) {
                noRecordRow.closest('tr').remove();
            }

            // Create a new row
            const newRow = document.createElement('tr');
            newRow.setAttribute('data-filer-id', tempId);

            // Add the cells
            newRow.innerHTML = `
                <td>${name}</td>
                <td>${type}</td>
                <td>${filerId}</td>
                <td class="text-end">
                    <button type="button" class="btn p-0" onclick="deleteRelatedFiler(${tempId})">
                        <i class="bi bi-trash" aria-label="Delete"></i>
                    </button>
                </td>
            `;

            // Add the row to the table
            filerTableBody.appendChild(newRow);

            // Hide the search fields
            hideAllSearchFields();
        }

        // Function to handle adding a Major Donor
        function handleAddMajorDonor() {
            if (selectedMajorDonor) {
                addFilerToTable(selectedMajorDonor.Name, selectedMajorDonor.FilerType, selectedMajorDonor.FilerId);

                // Clear the AutoComplete selection
                const majorDonorAutoComplete = document.getElementById('majorDonorAutoComplete').ej2_instances[0];
                majorDonorAutoComplete.clear();
                selectedMajorDonor = null;
            }
        }

        // Function to handle adding a Recipient Committee
        function handleAddRecipientCommittee() {
            if (selectedRecipientCommittee) {
                addFilerToTable(selectedRecipientCommittee.Name, selectedRecipientCommittee.FilerType, selectedRecipientCommittee.FilerId);

                // Clear the AutoComplete selection
                const recipientCommitteeAutoComplete = document.getElementById('recipientCommitteeAutoComplete').ej2_instances[0];
                recipientCommitteeAutoComplete.clear();
                selectedRecipientCommittee = null;
            }
        }

        // Function to handle form submission
        function handleSave() {
            // Remove any existing relatedFilerIds inputs
            const existingInputs = document.querySelectorAll('input[name="relatedFilerIds"]');
            existingInputs.forEach(input => input.remove());

            // Create individual hidden inputs for each filer ID
            const form = document.querySelector('form');
            relatedFilers.forEach(filer => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'relatedFilerIds';
                input.value = filer.filerId;
                form.appendChild(input);
            });
        }

        // Add event listeners to ContributionsInExistingStatements radio inputs
        radioInputs.forEach(function(radio) {
            radio.addEventListener('change', toggleDetailDiv);
        });

        // Add event listeners to the "Add Major Donor" and "Add Recipient Committee" buttons
        addMajorDonorBtn.addEventListener('click', showMajorDonorField);
        addRecipientCommitteeBtn.addEventListener('click', showRecipientCommitteeField);

        // Add event listeners to the "Add" buttons within each search field
        majorDonorAddBtn.addEventListener('click', handleAddMajorDonor);
        recipientCommitteeAddBtn.addEventListener('click', handleAddRecipientCommittee);

        // Add event listener to the Save button
        if (saveButton) {
            saveButton.addEventListener('click', handleSave);
        }

        // Function to delete a related filer
        window.deleteRelatedFiler = function(filerId) {
            const filerElement = document.querySelector(`[data-filer-id="${filerId}"]`);
            if (filerElement) {
                filerElement.remove();

                // Remove from our array
                relatedFilers = relatedFilers.filter(filer => filer.id !== parseInt(filerId));

                // If no more filers, show the "No record added yet" message
                if (filerTableBody.children.length === 0) {
                    const noRecordRow = document.createElement('tr');
                    noRecordRow.innerHTML = '<td colspan="4">No record added yet</td>';
                    filerTableBody.appendChild(noRecordRow);
                }
            }
        };

        // Initialize the visibility states
        toggleDetailDiv();

        // Initialize search fields to hidden until a button selection is made
        hideAllSearchFields();

        // Initialize the relatedFilers array from existing table data
        initializeRelatedFilersArray();

        // Handle form submission - prepare data before submit
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                handleSave(); // Prepare the filer IDs for submission
            });
        }
    });

    // AutoComplete change event handlers
    function onMajorDonorSelectionChange(args) {
        if (args.itemData) {
            selectedMajorDonor = args.itemData;
        } else {
            selectedMajorDonor = null;
        }
    }

    function onRecipientCommitteeSelectionChange(args) {
        if (args.itemData) {
            selectedRecipientCommittee = args.itemData;
        } else {
            selectedRecipientCommittee = null;
        }
    }

    function onMajorDonorFiltering(e) {
        if (e.text && e.text.length > 0) {
            fetch(`/Transaction/SearchFilersByTypeAndQuery?query=${encodeURIComponent(e.text)}&filerTypeId=@FilerType.MajorDonor.Id`)
                .then(response => response.json())
                .then(data => {
                    // Filter out filers that are already in the table
                    const existingFilerIds = relatedFilers.map(filer => filer.filerId.toString());
                    const filteredData = data.filter(item => !existingFilerIds.includes(item.FilerId.toString()));
                    e.updateData(filteredData);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    e.updateData([]);
                });
        } else {
            e.updateData([]);
        }
    }

    function onRecipientCommitteeFiltering(e) {
        if (e.text && e.text.length > 0) {
            fetch(`/Transaction/SearchFilersByTypeAndQuery?query=${encodeURIComponent(e.text)}&filerTypeId=@FilerType.RecipientCommittee.Id`)
                .then(response => response.json())
                .then(data => {
                    // Filter out filers that are already in the table
                    const existingFilerIds = relatedFilers.map(filer => filer.filerId.toString());
                    const filteredData = data.filter(item => !existingFilerIds.includes(item.FilerId.toString()));
                    e.updateData(filteredData);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    e.updateData([]);
                });
        } else {
            e.updateData([]);
        }
    }

</script>
