using System.Globalization;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Contacts;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using static SOS.CalAccess.Services.Business.Constants.DisclosureConstants;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;

/// <summary>
/// Implementation for the Filing Service Interface.
/// </summary>
#pragma warning disable S107 // Methods should not have too many parameters
public sealed class FilingSvc(
    FilingSvcDependencies dependencies,
    FilingSharedServicesDependencies servicesDependencies,
    ITransactionRepository transactionRepository,
    IFilerUserRepository filerUserRepository,
    IActionsLobbiedSvc actionsLobbiedSvc,
    ITransactionHelperSvc transactionHelperSvc,
    IDateTimeSvc dateTimeSvc,
    IFilerContactRepository filerContactRepository) : IFilingSvc
#pragma warning restore S107 // Methods should not have too many parameters
{
    private static readonly Dictionary<long, long> _transactionToSummaryTypeMap = new()
    {
        { TransactionType.ActivityExpense.Id, FilingSummaryType.ActivityExpenseSummary.Id },
        { TransactionType.LobbyingCampaignContribution.Id, FilingSummaryType.CampaignContributionSummary.Id},
        { TransactionType.PaymentMadeToLobbyingCoalition.Id, FilingSummaryType.ToLobbyingCoalitionSummary.Id },
        { TransactionType.PaymentReceiveLobbyingCoalition.Id, FilingSummaryType.RecieveLobbyingCoalitionSummary.Id },
        { TransactionType.PaymentMadeToLobbyingFirms.Id, FilingSummaryType.MadeToLobbyingFirmsSummary.Id },
        { TransactionType.LobbyistReports.Id, FilingSummaryType.LobbyistReportsSummary.Id },
        { TransactionType.PucActivity.Id, FilingSummaryType.PucActivitySummary.Id },
        { TransactionType.OtherPaymentsToInfluence.Id, FilingSummaryType.OtherPaymentsToInfluenceSummary.Id },
        { TransactionType.PaymentMade.Id, FilingSummaryType.PaymentMadeSummary.Id },
        { TransactionType.PaymentReceived.Id, FilingSummaryType.PaymentReceivedSummary.Id },
        { TransactionType.PersonReceiving1000OrMore.Id, FilingSummaryType.PersonReceiving1000OrMoreSummary.Id },
    };

    private readonly List<long> _filingSummaryTypes = new()
    {
        FilingSummaryType.ActionsLobbiedSummary.Id,
        FilingSummaryType.CampaignContributionSummary.Id,
        FilingSummaryType.ActivityExpenseSummary.Id,
        FilingSummaryType.ToLobbyingCoalitionSummary.Id,
        FilingSummaryType.RecieveLobbyingCoalitionSummary.Id,
        FilingSummaryType.MadeToLobbyingFirmsSummary.Id,
        FilingSummaryType.LobbyistReportsSummary.Id,
        FilingSummaryType.PucActivitySummary.Id,
        FilingSummaryType.OtherPaymentsToInfluenceSummary.Id,
        FilingSummaryType.PaymentsToInHouseLobbyists.Id
    };

    private const string NotStarted = "Not Started";
    private const string Status = "Status";
    public static long? GetFilingSummaryTypeForTransactionType(long transactionType)
    {
        if (_transactionToSummaryTypeMap.TryGetValue(transactionType, out var summaryType))
        {
            return summaryType;
        }
        return null;
    }

    /// <inheritdoc /> 
    public async Task OnTransactionCreated(Transaction transaction, long? disclosureFilingId)
    {
        if (!disclosureFilingId.HasValue)
        {
            return;
        }

        var summaryTypeId = GetFilingSummaryTypeForTransactionType(transaction.TypeId);
        if (!summaryTypeId.HasValue)
        {
            return;
        }

        // Special case PaymentMade and PaymentMadeByAgentOrIndependentContractor share the same TransactionType
        ResolveSummaryTypeForPaymentMadeByAgent(ref summaryTypeId, transaction);

        var filingSummaries = await dependencies.FilingSummaryRepository.GetAllByFilingId(disclosureFilingId.Value);
        var filingSummaryForType = filingSummaries.FirstOrDefault(fs => fs.FilingSummaryTypeId == summaryTypeId);

        if (filingSummaryForType != null)
        {
            filingSummaryForType.PeriodAmount += transaction.Amount;
            filingSummaryForType.ToDateAmount += transaction.Amount;

            // If the status of filing summary is Not Started/Nothing to Report, change to In Progress when the transaction is created
            filingSummaryForType.FilingSummaryStatusId =
                (filingSummaryForType.FilingSummaryStatusId == FilingSummaryStatus.NotStarted.Id ||
                    filingSummaryForType.FilingSummaryStatusId == FilingSummaryStatus.NothingToReport.Id)
                ? FilingSummaryStatus.InProgress.Id
                : filingSummaryForType.FilingSummaryStatusId;

            _ = await dependencies.FilingSummaryRepository.Update(filingSummaryForType);
        }

        // Update Filing Contact Summary if applicable
        await UpdateFilingContactSummaryIfApplicableAsync(disclosureFilingId.Value, transaction, transaction.Amount, null);
    }

    /// <inheritdoc /> 
    public async Task RecalculateSummaryAmountsForFiling(Filing filing)
    {

        Dictionary<long, FilingSummary> summariesByType = new();

        foreach (var summary in filing.FilingSummaries)
        {
            summariesByType.Add(summary.FilingSummaryTypeId, summary);
            //Re-initialize amounts
            summary.ToDateAmount = 0;
            summary.PeriodAmount = 0;
        }

        //initialize the ToDateAmount with sum of summary amounts from previous filings in legislative session
        Dictionary<long, decimal> perviousSubmittedReportsAmountsByType = await CalculateLegislativeSessionToDateAmountsBySummaryType(filing.FilerId, [.. summariesByType.Keys], filing.FilingPeriod!);
        foreach (var summary in filing.FilingSummaries)
        {
            summary.ToDateAmount = perviousSubmittedReportsAmountsByType[summary.FilingSummaryTypeId];
        }

        //add amounts from transactions linked to filing
        foreach (var transaction in filing.FilingTransactions)
        {
            if (transaction?.Transaction != null)
            {
                var summaryTypeId = GetFilingSummaryTypeForTransactionType(transaction.Transaction.TypeId);
                if (summaryTypeId != null)
                {
                    var filingSummaryForType = summariesByType[summaryTypeId.Value];

                    if (filingSummaryForType != null)
                    {
                        filingSummaryForType.PeriodAmount += transaction.Transaction.Amount;
                        filingSummaryForType.ToDateAmount += transaction.Transaction.Amount;
                    }
                }
            }
        }

        //Update DB with summary records
        foreach (var summary in summariesByType.Values)
        {
            _ = await dependencies.FilingSummaryRepository.Update(summary);
        }
    }

    /// <inheritdoc /> 
    public async Task OnTransactionUpdated(Transaction originalTransaction, Transaction updatedTransaction, long? disclosureFilingId)
    {
        if (!disclosureFilingId.HasValue)
        {
            return;
        }

        var summaryTypeId = GetFilingSummaryTypeForTransactionType(originalTransaction.TypeId);
        if (!summaryTypeId.HasValue)
        {
            return;
        }

        decimal amountDifference = updatedTransaction.Amount - originalTransaction.Amount;

        if (amountDifference == 0)
        {
            return;
        }

        var filingSummaries = await dependencies.FilingSummaryRepository.GetAllByFilingId(disclosureFilingId.Value);
        var filingSummaryForType = filingSummaries.FirstOrDefault(fs => fs.FilingSummaryTypeId == summaryTypeId);


        if (filingSummaryForType != null)
        {
            filingSummaryForType.PeriodAmount += amountDifference;
            filingSummaryForType.ToDateAmount += amountDifference;
            _ = await dependencies.FilingSummaryRepository.Update(filingSummaryForType);
        }
    }

    /// <inheritdoc /> 
    public async Task OnTransactionUpdatedAsync(Transaction transaction, decimal varianceAmount, long? disclosureFilingId, long? originalContactId)
    {
        if (!disclosureFilingId.HasValue)
        {
            return;
        }

        var summaryTypeId = GetFilingSummaryTypeForTransactionType(transaction.TypeId);
        if (!summaryTypeId.HasValue)
        {
            return;
        }

        // Special case PaymentMade and PaymentMadeByAgentOrIndependentContractor share the same TransactionType
        ResolveSummaryTypeForPaymentMadeByAgent(ref summaryTypeId, transaction);

        var filingSummaries = await dependencies.FilingSummaryRepository.GetAllByFilingId(disclosureFilingId.Value);
        var filingSummaryForType = filingSummaries.FirstOrDefault(fs => fs.FilingSummaryTypeId == summaryTypeId);

        if (filingSummaryForType != null)
        {
            filingSummaryForType.PeriodAmount += Math.Max(varianceAmount, -filingSummaryForType.PeriodAmount);
            filingSummaryForType.ToDateAmount += Math.Max(varianceAmount, -filingSummaryForType.ToDateAmount.GetValueOrDefault());
            _ = await dependencies.FilingSummaryRepository.Update(filingSummaryForType);
        }

        // Update Filing Contact Summary if applicable
        await UpdateFilingContactSummaryIfApplicableAsync(disclosureFilingId.Value, transaction, varianceAmount, originalContactId);
    }

    /// <inheritdoc/>
    public async Task OnTransactionDeletedAsync(Transaction transaction, long filingId)
    {
        var summaryTypeId = GetFilingSummaryTypeForTransactionType(transaction.TypeId);
        if (!summaryTypeId.HasValue)
        {
            return;
        }

        // Special case PaymentMade and PaymentMadeByAgentOrIndependentContractor share the same TransactionType
        ResolveSummaryTypeForPaymentMadeByAgent(ref summaryTypeId, transaction);

        var filingSummaries = await dependencies.FilingSummaryRepository.GetAllByFilingId(filingId);
        var filingSummary = filingSummaries.FirstOrDefault(fs => fs.FilingSummaryTypeId == summaryTypeId);

        if (filingSummary is null)
        {
            return;
        }

        filingSummary.PeriodAmount -= Math.Min(transaction.Amount, filingSummary.PeriodAmount);
        filingSummary.ToDateAmount -= Math.Min(transaction.Amount, filingSummary.ToDateAmount.GetValueOrDefault());

        await dependencies.FilingSummaryRepository.Update(filingSummary);

        // Update Filing Contact Summary if applicable
        await UpdateFilingContactSummaryIfApplicableAsync(filingId, transaction, -transaction.Amount, null);
    }

    /// <inheritdoc /> 
    public Task AmendFiling(Filing newFiling, long filingId)
    {
        throw new NotImplementedException();
    }

    public async Task<FilingResponseDto> CreateFilingAmendmentAsync(long id)
    {
        var filing = await ValidateAndGetLobbyistFilingForAmendment(id);

        var existingAmendment = await GetExistingAmendment(filing);
        if (existingAmendment != null)
        {
            return new FilingResponseDto(existingAmendment);
        }

        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();

        var settings = new JsonSerializerSettings
        {
            TypeNameHandling = TypeNameHandling.All,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            ContractResolver = new DefaultContractResolver
            {
                IgnoreSerializableAttribute = true
            }
        };

        var json = JsonConvert.SerializeObject(filing, settings);
        var filingAmendment = JsonConvert.DeserializeObject<Filing>(json, settings)!;

        filingAmendment.Id = 0;
        filingAmendment.Version = (filing.Version ?? 0) + 1;
        filingAmendment.OriginalId = filing.OriginalId ?? filing.Id;
        filingAmendment.ParentId = filing.Id;
        filingAmendment.StatusId = FilingStatus.Draft.Id;
        filingAmendment.CreatedBy = userId.GetValueOrDefault();
        filingAmendment.FilingSummaries = CloneFilingSummaries(filing.FilingSummaries);
        AddAmendmentExplanationFilingSummary(filingAmendment.FilingSummaries);
        filingAmendment.ActionsLobbied = CloneActionsLobbied(filing.ActionsLobbied);
        filingAmendment.FilingRelatedFilers = CloneFilingRelatedFilers(filing.FilingRelatedFilers);

        filingAmendment.FilingPeriodId = filing.FilingPeriodId;
        filingAmendment.FilingPeriod = null;

        filingAmendment.FilerId = filing.FilerId;
        filingAmendment.Filer = null;

        filingAmendment.FilingTransactions = [];

        await dependencies.FilingRepository.Create(filingAmendment);

        var originalTransactions = filing.FilingTransactions
            .Where(ft => ft?.Transaction != null)
            .Select(ft => ft.Transaction!)
            .ToList();

        await ProcessTransactionsForAmendment(filingAmendment.Id, originalTransactions);

        var newFilingAmendment = await dependencies.FilingRepository.GetFilingById(filingAmendment.Id);
        return new FilingResponseDto(newFilingAmendment!);
    }

    /// <inheritdoc /> 
    public async Task<long> CreateFiling(Filing filing)
    {
        var createdFiling = await dependencies.FilingRepository.Create(filing);
        return createdFiling.Id;
    }

    /// <inheritdoc /> 
    public async Task<Filing> CreateOfficeHolderCandidateShortFormFiling(long? filerId, long? filingPeriodId)
    {
        var filing = new Filing
        {
            FilerId = filerId!.Value,
            FilingPeriodId = filingPeriodId!.Value,
            Version = 0,
            StatusId = FilingStatus.Draft.Id,
            //OriginalId = 0, // TD
            //ParentId = 0, // TD
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
        };
        return await dependencies.FilingRepository.Create(filing);
    }

    /// <inheritdoc /> 
    public async Task<IEnumerable<FilingPeriod>> GetAllFilingPeriods()
    {
        var periods = await dependencies.FilingPeriodRepository.GetAll();
        return periods.OrderBy(p => p.StartDate);
    }

    /// <inheritdoc /> 
    public async Task<IEnumerable<FilingPeriodDto>> GetAllFilingPeriodsForFiler(long filerId, long? filingTypeId = null, long? filingId = null)
    {
        var filings = await dependencies.FilingRepository.GetAllByFilerId(filerId);
        var periods = (await dependencies.FilingPeriodRepository.GetAll()).AsQueryable();

        if (filingTypeId.HasValue)
        {
            filings = filings.Where(f => f.FilingTypeId == filingTypeId.Value);
            if (filingTypeId == FilingType.LobbyistReport.Id)
            {
                var lobbyist = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Lobbyist>(filerId)
                    ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {filerId} not found.");

                if (lobbyist.SubmittedAt.HasValue)
                {
                    var startDate = new DateTime(lobbyist.SubmittedAt.Value.Year, 1, 1, 0, 0, 0, DateTimeKind.Local);
                    var endDate = GetLegislativeEndDateForDate(lobbyist.SubmittedAt.Value);
                    periods = periods.Where(fp => fp.StartDate >= startDate && fp.EndDate <= endDate);
                }
            }
        }

        // Exclude the current filing if it is being edited
        if (filingId.HasValue)
        {
            filings = filings.Where(f => f.Id != filingId.Value);
        }

        return periods
            .Select(p => new FilingPeriodDto
            {

                Id = p.Id,
                StartDate = p.StartDate,
                EndDate = p.EndDate,
                HasFiling = FindDateRangeOverlappedByCreatedReports(filings, p.StartDate, p.EndDate) != null
            })
            .OrderBy(p => p.StartDate)
            .ThenBy(p => p.EndDate);
    }

    /// <inheritdoc /> 
    public async Task<IEnumerable<FilingPeriodDto>> GetAllFilingPeriodsForLobbying(long filerId, long? filingTypeId = null, long? filingId = null)
    {
        var filings = await dependencies.FilingRepository.GetAllByFilerId(filerId);

        var legislativeSessions = await GetAllLegislativeSessionsByFilerId(filerId);

        var periods =
            await dependencies.FilingPeriodRepository.GetFilingPeriodsByLegislativeSessionIds(legislativeSessions.Select(ls => ls.Id).ToList());

        var terminatedOrWithdrawnDate = await GetTerminationOrWithdrawnDateByFilerId(filerId);


        if (legislativeSessions.Count > 0 && periods.Count == 0)
        {
            periods = await GenerateFilingPeriodByLegislativeSession(legislativeSessions);
        }

        // Exclude the current filing if it is being edited
        if (filingId.HasValue)
        {
            filings = filings.Where(f => f.Id != filingId.Value);
        }

        return periods
            .Select(p => new FilingPeriodDto
            {
                Id = p.Id,
                StartDate = p.StartDate,
                EndDate = p.EndDate,
                HasFiling = FindDateRangeOverlappedByCreatedReports(filings, p.StartDate, p.EndDate) != null,
                IsRemoved = terminatedOrWithdrawnDate.HasValue &&
                            (p.StartDate > terminatedOrWithdrawnDate.Value && p.EndDate > terminatedOrWithdrawnDate.Value),
            })
            .OrderBy(p => p.StartDate)
            .ThenBy(p => p.EndDate);
    }

    private async Task<List<FilingPeriod>> GenerateFilingPeriodByLegislativeSession(List<LegislativeSession> legislativeSessions)
    {
        var newFilingPeriods = new List<FilingPeriod>();

        foreach (LegislativeSession session in legislativeSessions)
        {
            var startDate = session.StartDate;
            var endDate = session.EndDate;

            // Add quarterly periods from startDate to endDate
            var periodStart = new DateTime(startDate.Year, startDate.Month, 1, 0, 0, 0, DateTimeKind.Unspecified);
            while (periodStart < endDate)
            {
                var periodEnd = periodStart.AddMonths(3).AddDays(-1);
                if (periodEnd > endDate)
                {
                    periodEnd = endDate;
                }

                var newPeriod = new FilingPeriod
                {
                    CreatedBy = 0,
                    Description = $"{periodStart:yyyy-MM-dd} - {periodEnd:yyyy-MM-dd}",
                    DueDate = null,
                    ElectionCycleId = null,
                    StartDate = periodStart,
                    EndDate = periodEnd,
                    FilingPeriodTypeId = FilingPeriodType.QuarterlyLobbying.Id,
                    LegislativeSessionId = session.Id,
                    ModifiedBy = 0,
                    Name = "Quarterly"
                };

                newFilingPeriods.Add(newPeriod);
                _ = await dependencies.FilingPeriodRepository.Create(newPeriod);

                periodStart = periodStart.AddMonths(3);
            }
        }
        return newFilingPeriods;
    }

    private async Task<DateTime?> GetTerminationOrWithdrawnDateByFilerId(long filerId)
    {
        var registration = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(filerId);
        if (registration is null)
        {
            return null;
        }

        var acceptedStatus = new List<long> { FilingStatus.Accepted.Id, FilingStatus.Submitted.Id };

        var registrationType = await dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(registration.Id);

        if (registrationType == nameof(Lobbyist))
        {
            var termination =
                await dependencies.RegistrationRepository
                    .FindListOfRegistrationsByFilerId<LobbyistTermination>(filerId);
            var withdrawal =
                await dependencies.RegistrationRepository
                    .FindListOfRegistrationsByFilerId<LobbyistWithdrawal>(filerId);

            var firstTermination = termination?.FirstOrDefault(t => acceptedStatus.Contains(t!.StatusId));
            if (firstTermination != null)
            {
                return firstTermination.TerminatedAt;
            }

            var firstWithdrawal = withdrawal?.FirstOrDefault(t => acceptedStatus.Contains(t!.StatusId));
            return firstWithdrawal?.WithdrawnAt;
        }

        return null;
    }

    private async Task<List<LegislativeSession>> GetAllLegislativeSessionsByFilerId(long filerId)
    {
        var registration = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(filerId);
        if (registration is null)
        {
            return [];
        }

        var acceptedStatus = new List<long> { FilingStatus.Accepted.Id, FilingStatus.Submitted.Id };

        var registrationType = await dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(registration.Id);
        IEnumerable<LegislativeSession?> sessions = registrationType switch
        {
            nameof(Lobbyist) => (await dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<Lobbyist>(filerId))
                .Where(r => r?.LegislativeSessionId != null && acceptedStatus.Contains(r.StatusId))
                .GroupBy(r => r!.OriginalId)
                .Select(g => g.OrderByDescending(r => r!.Id).First()) //Get the latest registration for each original ID
                .Select(r => r!.LegislativeSession),
            nameof(LobbyistEmployer) => (await dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<LobbyistEmployer>(filerId))
                .Where(r => r?.LegislativeSessionId != null && acceptedStatus.Contains(r.StatusId))
                .GroupBy(r => r!.OriginalId)
                .Select(g => g.OrderByDescending(r => r!.Id).First())
                .Select(r => r!.LegislativeSession),
            _ => (await dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<LobbyingFirm>(filerId))
                .Where(r => r?.LegislativeSessionId != null && acceptedStatus.Contains(r.StatusId))
                .GroupBy(r => r!.OriginalId)
                .Select(g => g.OrderByDescending(r => r!.Id).First())
                .Select(r => r!.LegislativeSession)
        };

        return sessions
            .Where(s => s is not null)
            .Cast<LegislativeSession>()
            .ToList();
    }

    public Task<IEnumerable<Filing?>> GetAllFilings()
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc /> 
    public async Task<IEnumerable<Filing>> GetAllFilings(long filerId)
    {
        return await dependencies.FilingRepository.GetAllByFilerId(filerId);
    }

    /// <inheritdoc /> 
    public async Task<IEnumerable<FilingStatus>> GetAllFilingStatuses()
    {
        return await dependencies.FilingStatusRepository.GetAll();
    }

    /// <inheritdoc /> 
    public async Task<IEnumerable<FilingType>> GetAllFilingTypes()
    {
        return await dependencies.FilingTypeRepository.GetAll();
    }

    /// <inheritdoc /> 
    public async Task<Filing?> GetFiling(long filingId)
    {
        return await dependencies.FilingRepository.FindById(filingId);
    }

    /// <inheritdoc /> 
    public async Task<Filing?> GetFilingById(long id)
    {
        return await dependencies.FilingRepository.GetFilingById(id);
    }

    /// <inheritdoc /> 
    public Task InitiateAmendFiling(long filingId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc /> 
    public Task SubmitFiling(long filingId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc /> 
    public async Task UpdateFiling(Filing filing)
    {
        await dependencies.FilingRepository.Update(filing);
    }

    /// <inheritdoc /> 
    public Task UpdateFilingStatus(FilingStatus status, long filingId)
    {
        throw new NotImplementedException();
    }

    #region Lobbyist Employer
    /// <inheritdoc />
    public async Task<Filing> CreateLobbyistEmployerReport(long filerId, long? filingPeriodId)
    {
        var filingSummaryStatuses = await dependencies.FilingSummaryStatusRepository.GetAllFilingSummaryStatuses();

        // New flow will not have FilingPeriod when creating a new
        if (filingPeriodId == null)
        {
            var lobbyistEmployerReport = new LobbyistEmployerReport
            {
                StatusId = FilingStatus.Draft.Id,
                CreatedBy = filerId,
                FilerId = filerId,
                FilingTypeId = FilingType.LobbyistEmployerReport.Id,
                FilingPeriodId = filingPeriodId,
                FilingSummaries = _filingSummaryTypes
                    .Select(typeId => new FilingSummary
                    {
                        FilingSummaryTypeId = typeId,
                        PeriodAmount = 0,
                        ToDateAmount = 0,
                        FilingSummaryStatusId = filingSummaryStatuses.FirstOrDefault(s => s.Name == NotStarted)?.Id,
                        NoActivityToReport = false
                    }).ToList()!,
                StartDate = DateTime.MinValue,
                EndDate = DateTime.MinValue
            };
            var newLobbyistEmployerReport = await dependencies.FilingRepository.Create(lobbyistEmployerReport);

            newLobbyistEmployerReport.OriginalId = newLobbyistEmployerReport.Id;
            await dependencies.FilingRepository.Update(newLobbyistEmployerReport);

            return newLobbyistEmployerReport;
        }

        var existingFilings = await dependencies.FilingRepository.GetAllByFilerId(filerId);

        var existingLobbyistEmployerReport = existingFilings.FirstOrDefault(x => x.FilerId == filerId
            && x.FilingTypeId == FilingType.LobbyistEmployerReport.Id
            && x.StatusId == FilingStatus.Draft.Id // Why is this draft and not accepted
            && x.FilingPeriodId == filingPeriodId);

        if (existingLobbyistEmployerReport?.FilingSummaries.Count == 0)
        {
            var filingPeriod = await dependencies.FilingPeriodRepository.FindById(filingPeriodId ?? 0) ?? throw new ArgumentNullException(nameof(filingPeriodId), "Filing period cannot be null.");
            var legislativeStartDate = GetLegislativeStartDateForDate(filingPeriod.StartDate);

            var previousSubmittedReports = existingFilings
                .Where(x =>
                    x.FilerId == filerId
                    && x.FilingTypeId == FilingType.LobbyistEmployerReport.Id
                    && (x.StatusId == FilingStatus.Accepted.Id || x.StatusId == FilingStatus.Incomplete.Id)
                    && x.StartDate >= legislativeStartDate
                    && x.EndDate < filingPeriod.EndDate)
                .OrderBy(x => x.EndDate)
                .ToList();

            var perviousSubmittedReportsAmountsByType = _filingSummaryTypes
                .ToDictionary(
                    typeId => typeId,
                    typeId => previousSubmittedReports
                        .SelectMany(r => r.FilingSummaries)
                        .Where(fs => fs.FilingSummaryTypeId == typeId)
                        .Sum(fs => fs.PeriodAmount)
                );

            existingLobbyistEmployerReport.FilingSummaries = _filingSummaryTypes
                .Select(typeId => new FilingSummary
                {
                    FilingSummaryTypeId = typeId,
                    PeriodAmount = 0,
                    ToDateAmount = perviousSubmittedReportsAmountsByType[typeId],
                    FilingSummaryStatusId = filingSummaryStatuses.FirstOrDefault(s => s.Name == NotStarted)?.Id,
                    NoActivityToReport = false
                }).ToList()!;

            _ = await dependencies.FilingRepository.Update(existingLobbyistEmployerReport);
        }
        return existingLobbyistEmployerReport!;
    }

    /// <inheritdoc />
    public async Task<LobbyistEmployerReport?> GetLobbyistEmployerReport(long filingId)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {
            return lobbyistEmployerReport;
        }
        return null;
    }

    /// <inheritdoc />
    public async Task<UpdateLobbyistEmployerReportResponseDto?> UpdateLobbyistEmployerReport(long filingId, decimal? totalPaymentsToInHouseLobbyists)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {
            var decisionsData = new DecisionsPaymentsToInHouseLobbyists { TotalPaymentToInHouseLobbyists = totalPaymentsToInHouseLobbyists };
            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsPaymentsToInHouseLobbyists, WorkFlowError>(DecisionsWorkflow.PaymentsToInHouseLobbyistsRuleset, decisionsData, true);

            if (decisionResponse == null)
            {
                isValid = true;
                lobbyistEmployerReport.TotalPaymentsToInHouseLobbyists = (Currency)totalPaymentsToInHouseLobbyists;
                await dependencies.FilingRepository.Update(lobbyistEmployerReport);
            }
            else
            {
                validationErrors.Add(decisionResponse);
            }

            return new UpdateLobbyistEmployerReportResponseDto(filingId, isValid, validationErrors, lobbyistEmployerReport.TotalPaymentsToInHouseLobbyists);
        }
        return null;
    }

    /// <inheritdoc />
    public async Task<UpdateLobbyistEmployerLumpSumResponseDto?> UpdateLobbyistEmployerLumpSums(long filingId, decimal? totalOverheadExpense, decimal? totalUnderThresholdPayments)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {
            var decisionsData = new DecisionsLumpSumPayments { TotalOverheadExpense = totalOverheadExpense, TotalUnderThresholdPayments = totalUnderThresholdPayments };
            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsLumpSumPayments, List<WorkFlowError>>(DecisionsWorkflow.LumpSumPaymentsRuleset, decisionsData, true);

            if (decisionResponse.Count == 0)
            {
                isValid = true;
                lobbyistEmployerReport.TotalOverheadExpense = (Currency)(totalOverheadExpense ?? 0);
                lobbyistEmployerReport.TotalUnderThresholdPayments = (Currency)(totalUnderThresholdPayments ?? 0);
                await dependencies.FilingRepository.Update(lobbyistEmployerReport);
            }
            else
            {
                validationErrors = decisionResponse;
            }

            return new UpdateLobbyistEmployerLumpSumResponseDto(filingId, isValid, validationErrors, lobbyistEmployerReport.TotalOverheadExpense, lobbyistEmployerReport.TotalUnderThresholdPayments);
        }
        return null;
    }

    public async Task<LobbyistEmployerReport?> UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(long filingId, bool? isMemberOfLobbyingCoalition)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {
            lobbyistEmployerReport.IsMemberOfLobbyingCoalition = isMemberOfLobbyingCoalition;
            await dependencies.FilingRepository.Update(lobbyistEmployerReport);
            return lobbyistEmployerReport;
        }
        return null;
    }

    public async Task<LobbyistEmployerReport?> UpdateLobbyistEmployerCampaignContributions(long filingId, bool? contributionsInExistingStatements, long? relatedFilerId)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {
            if (contributionsInExistingStatements.Equals(true))
            {
                if (relatedFilerId.HasValue)
                {
                    await dependencies.FilingRelatedFilerRepository.AddFilingRelatedFiler(filingId, (long)relatedFilerId);
                }
            }
            else
            {
                await dependencies.FilingRelatedFilerRepository.DeleteAllFilingRelatedFilerforFiling(filingId);
            }

            if (lobbyistEmployerReport.ContributionsInExistingStatements != contributionsInExistingStatements)
            {
                lobbyistEmployerReport.ContributionsInExistingStatements = contributionsInExistingStatements;
                await dependencies.FilingRepository.Update(lobbyistEmployerReport);
            }

            return lobbyistEmployerReport;
        }
        return null;
    }

    public async Task<ValidatedLobbyistEmployerReport?> SendForAttestationReportLobbyistEmployer(long filingId, List<long> selectedResponsibleOfficerIds)
    {
        bool isValid = false;

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {
            var decisionInput = await HandleLobbyistEmployerReportToDecisionInput(lobbyistEmployerReport);

            var workflow = filing.Version > 0 ? DecisionsWorkflow.FD02LobbyistEmployerAmendmentReportSendforAttestation : DecisionsWorkflow.SendForAttestationLobbyistEmployerReportRuleset;

            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(workflow, decisionInput, true);
            var decisionErrors = decisionResponse.Result;

            lobbyistEmployerReport.SubmittedDate = DateTime.UtcNow;

            if (decisionErrors.Count == 0)
            {
                isValid = true;
                lobbyistEmployerReport.StatusId = FilingStatus.Pending.Id;
                await dependencies.FilingRepository.Update(lobbyistEmployerReport);
                IDictionary<string, string>? notificationData = new Dictionary<string, string>
                {
                    [Status] = FilingStatus.Pending.Name
                };

                await SendMultipleUsersNotifications(servicesDependencies, decisionResponse.Notifications, selectedResponsibleOfficerIds, filing.FilerId, notificationData);
            }

            return new ValidatedLobbyistEmployerReport(filingId, isValid, decisionErrors, lobbyistEmployerReport);
        }
        return null;
    }

    public async Task<LobbyistEmployerReport?> CancelLobbyistEmployerReport(long filingId)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId);

        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {

            if (filing.StatusId != FilingStatus.Draft.Id)
            {
                // throw an exception for status not being 'Draft'.
                throw new HttpRequestException("Cannot cancel a report that is not in 'Draft' status.", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            await dependencies.FilingRepository.UpdateProperty(filing, static r => r.StatusId, FilingStatus.Cancelled.Id);
            return lobbyistEmployerReport;
        }
        return null;
    }

    public async Task<ValidatedLobbyistEmployerReport?> SubmitLobbyistEmployerReport(long filingId, bool? diligenceStatementVerified)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistEmployerReport lobbyistEmployerReport)
        {
            // build decisions payload and implement FD-0602 call
            var decisionInput = await HandleLobbyistEmployerReportToDecisionInput(lobbyistEmployerReport);

            var workflow = filing.Version > 0 ? DecisionsWorkflow.FD02LobbyistEmployerAmendmentReportAttestation : DecisionsWorkflow.SubmitLobbyistEmployerReportRuleset;

            var decisionResponse =
                await servicesDependencies.DecisionsSvc
                    .InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport,
                        DecisionsSubmitLobbyistEmployerReportResponse>(
                        workflow, decisionInput, true);
            var decisionErrors = decisionResponse.Errors;
            if (decisionErrors.Count == 0)
            {
                isValid = true;
                lobbyistEmployerReport.StatusId = FilingStatus.Accepted.Id;
                lobbyistEmployerReport.DiligenceStatementVerified = diligenceStatementVerified;
                lobbyistEmployerReport.SubmittedDate = dateTimeSvc.GetCurrentDateTime();
                // update based on decisions response
                lobbyistEmployerReport.SubmittedLate = false;
                await dependencies.FilingRepository.Update(lobbyistEmployerReport);

                await UpdateFutureDraftReports(
                    lobbyistEmployerReport.FilerId,
                    FilingType.LobbyistEmployerReport,
                    lobbyistEmployerReport);
            }
            else
            {
                validationErrors = decisionErrors;
            }
            return new ValidatedLobbyistEmployerReport(filingId, isValid, validationErrors, lobbyistEmployerReport);
        }

        return null;
    }

    /// <inheritdoc />
    public async Task<ActionsLobbiedSummaryResponse> UpdateLobbyistEmployerReportActionsLobbied(long id, LobbyistEmployerActionsLobbiedRequest request)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(id);
        if (filing is not LobbyistEmployerReport lobbyistEmployerReport)
        {
            throw new KeyNotFoundException($"Lobbyist Employer Report not found Id={id}");
        }

        var filingSummaries = await dependencies.FilingSummaryRepository.GetAllByFilingId(id);

        var filingActionsLobbied =
            filingSummaries.FirstOrDefault(f => f.FilingSummaryTypeId == FilingSummaryType.ActionsLobbiedSummary.Id) ??
            throw new KeyNotFoundException($"Actions Lobbied Filing Summary not found.");

        var decisionInput = new DecisionsLobbyistEmployerReportActionsLobbied
        {
            LobbyingAdvertisementSubjects = request.LobbyingAdvertisementSubjects ?? [],
            BillCount = (request.SenateBills ?? []).Count + (request.AssemblyBills ?? []).Count,
            AdministrativeActionCount = (request.AdministrativeActions ?? []).Count,
            OtherActionDescription = request.OtherActionsLobbied
        };

        var decisionErrors =
            await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistEmployerReportActionsLobbied, List<WorkFlowError>>(
                DecisionsWorkflow.LobbyistEmployerReportActionsLobbiedRuleSet, decisionInput, true);

        var actionsLobbied = new ActionsLobbiedByEntityResponse();

        if (decisionErrors.Count == 0)
        {
            // Update data for Actions Lobbied by Filing ID
            isValid = true;
            _ = await actionsLobbiedSvc.UpsertActionsLobbiedForFiling(id, [.. CombineActionsLobbied(request)!]);

            // Update Other Actions Lobbied for Filing
            lobbyistEmployerReport.OtherActionsLobbied = request.OtherActionsLobbied;
            _ = await dependencies.FilingRepository.Update(lobbyistEmployerReport);

            // Update status of Filing Summary to Completed
            filingActionsLobbied.FilingSummaryStatusId = FilingSummaryStatus.InProgress.Id;
            _ = await dependencies.FilingSummaryRepository.Update(filingActionsLobbied);

            // Get the latest value of Actions Lobbied by Filing ID
            actionsLobbied = await actionsLobbiedSvc.GetActionsLobbiedByFilingId(id);
        }
        else
        {
            validationErrors = decisionErrors;
        }

        return new ActionsLobbiedSummaryResponse
        {
            FilingSummaryId = filingActionsLobbied.Id,
            FilingId = filingActionsLobbied.FilingId,
            FilingSummaryStatus = filingActionsLobbied.FilingSummaryStatus,
            OtherActionsLobbied = lobbyistEmployerReport.OtherActionsLobbied,
            AdministrativeActions = actionsLobbied.AgencyActions,
            AssemblyBillActions = actionsLobbied.AssemblyBillActions,
            SenateBillActions = actionsLobbied.SenateBillActions,
            Valid = isValid,
            ValidationErrors = validationErrors
        };
    }

    /// <inheritdoc />
    public async Task<List<WorkFlowError>> ValidateLobbyistEmployerActionsLobbiedAgencies(
        List<ActionsLobbiedRequestDto> request)
    {
        var decisionInput = request.Select(r => new DecisionsLobbyistEmployerReportActionsLobbiedAgencies
        {
            AgencyName =
                r.AgencyId.HasValue ? r.AgencyId.Value.ToString(CultureInfo.InvariantCulture) : r.AgencyDescription,
            AdministrativeActionDescription = r.AdministrativeAction,
        });

        return await servicesDependencies.DecisionsSvc
            .InitiateWorkflow<List<DecisionsLobbyistEmployerReportActionsLobbiedAgencies>, List<WorkFlowError>>(
                DecisionsWorkflow.LobbyistEmployerReportActionsLobbiedAgenciesRuleSet, [.. decisionInput], true);
    }
    #endregion

    public async Task<UpdateContributionsInExistingStatementsResponseDto?> UpdateContributionsInExistingStatements(long filingId, bool? contributionsInExistingStatements, List<long> relatedFilerIds)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is not LobbyistEmployerReport lobbyistEmployerReport)
        {
            return null;
        }

        contributionsInExistingStatements ??= false;
        relatedFilerIds ??= new List<long>();

        var decisionsData = new DecisionsContributionsInExistingStatements
        {
            IsContributionContainedInDisclosure = contributionsInExistingStatements.Value,
            RelatedFilerIds = relatedFilerIds
        };

        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            DecisionsWorkflow.ContributionsInExistingStatementsRuleset,
            decisionsData,
            true);

        if (decisionResponse.Count == 0)
        {
            isValid = true;

            // Update related filers based on contributions flag
            if (contributionsInExistingStatements.Value)
            {
                await dependencies.FilingRelatedFilerRepository.DeleteAllFilingRelatedFilerforFiling(filingId);
                foreach (var relatedFilerId in relatedFilerIds)
                {
                    await dependencies.FilingRelatedFilerRepository.AddFilingRelatedFiler(filingId, relatedFilerId);
                }
            }
            else
            {
                await dependencies.FilingRelatedFilerRepository.DeleteAllFilingRelatedFilerforFiling(filingId);
            }

            // Update the filing only if the value changed
            if (lobbyistEmployerReport.ContributionsInExistingStatements != contributionsInExistingStatements)
            {
                lobbyistEmployerReport.ContributionsInExistingStatements = contributionsInExistingStatements;
                await dependencies.FilingRepository.Update(lobbyistEmployerReport);
            }
        }
        else
        {
            validationErrors = decisionResponse;
        }

        return new UpdateContributionsInExistingStatementsResponseDto(filingId, isValid, validationErrors);
    }

    public async Task<Filing> CreateLobbyistReport(long filerId, long? filingPeriodId)
    {

        var existingLobbyistReport = await dependencies.FilingRepository.GetDraftFilingByFilerAndPeriodAndType(filerId, filingPeriodId, FilingType.LobbyistReport.Id);
        var filingSummaryStatuses = await dependencies.FilingSummaryStatusRepository.GetAllFilingSummaryStatuses();
        var filingSummaryTypes = new[]
    {
                FilingSummaryType.CampaignContributionSummary.Id,
                FilingSummaryType.ActivityExpenseSummary.Id
            };

        if (filingPeriodId == null)
        {
            var lobbyistReport = new LobbyistReport
            {
                StatusId = FilingStatus.Draft.Id,
                CreatedBy = filerId,
                FilerId = filerId,
                FilingTypeId = FilingType.LobbyistReport.Id,
                FilingPeriodId = filingPeriodId,
                FilingSummaries = filingSummaryTypes
                    .Select(typeId => new FilingSummary
                    {
                        FilingSummaryTypeId = typeId,
                        PeriodAmount = 0,
                        ToDateAmount = 0,
                        FilingSummaryStatusId = filingSummaryStatuses.FirstOrDefault(s => s.Name == NotStarted)?.Id,
                        NoActivityToReport = false
                    }).ToList()!,
                StartDate = DateTime.MinValue,
                EndDate = DateTime.MinValue
            };
            var newLobbyistReport = await dependencies.FilingRepository.Create(lobbyistReport);

            newLobbyistReport.OriginalId = newLobbyistReport.Id;
            await dependencies.FilingRepository.Update(newLobbyistReport);

            return newLobbyistReport;
        }

        var filingPeriod = await dependencies.FilingPeriodRepository.FindById(filingPeriodId.Value) ?? throw new ArgumentNullException(nameof(filingPeriodId), "Filing period cannot be null.");
        Dictionary<long, decimal> perviousSubmittedReportsAmountsByType = await CalculateLegislativeSessionToDateAmountsBySummaryType(filerId, filingSummaryTypes, filingPeriod);

        if (existingLobbyistReport?.FilingSummaries.Count == 0)
        {
            existingLobbyistReport.FilingSummaries = filingSummaryTypes
                .Select(typeId => new FilingSummary
                {
                    FilingSummaryTypeId = typeId,
                    PeriodAmount = 0,
                    ToDateAmount = perviousSubmittedReportsAmountsByType[typeId],
                    FilingSummaryStatusId = filingSummaryStatuses.FirstOrDefault(s => s.Name == NotStarted)?.Id,
                    NoActivityToReport = false
                }).ToList()!;

            _ = await dependencies.FilingRepository.Update(existingLobbyistReport);
        }

        return existingLobbyistReport;
    }

    private async Task<Dictionary<long, decimal>> CalculateLegislativeSessionToDateAmountsBySummaryType(long filerId, long[] filingSummaryTypes, FilingPeriod filingPeriod)
    {
        var existingFilings = await dependencies.FilingRepository.GetAllByFilerId(filerId);

        var legislativeStartDate = GetLegislativeStartDateForDate(filingPeriod.StartDate);

        var previousSubmittedReports = existingFilings
        .Where(x =>
            x.FilerId == filerId
            && x.FilingTypeId == FilingType.LobbyistReport.Id
            && (x.StatusId == FilingStatus.Accepted.Id || x.StatusId == FilingStatus.Incomplete.Id)
            && x.StartDate >= legislativeStartDate
            && x.EndDate < filingPeriod.EndDate)
        // are amended filings still in accepted status?  If so this needs to be updated to only get the most recent version.
        .OrderBy(x => x.EndDate)
        .ToList();

        var perviousSubmittedReportsAmountsByType = filingSummaryTypes
        .ToDictionary(
            typeId => typeId,
            typeId => previousSubmittedReports
                .SelectMany(r => r.FilingSummaries)
                .Where(fs => fs.FilingSummaryTypeId == typeId)
                .Sum(fs => fs.PeriodAmount)
        );
        return perviousSubmittedReportsAmountsByType;
    }

    /// <inheritdoc />
    public async Task<LobbyistReport?> GetLobbyistReport(long filingId)
    {
        var filing = await dependencies.FilingRepository.GetFilingById(filingId);
        if (filing is LobbyistReport lobbyistReport)
        {
            return lobbyistReport;
        }
        return null;
    }

    /// <inheritdoc />
    public async Task<DateTime> GetCumulativePeriodStartByFilingId(long id)
    {
        var filing = await dependencies.FilingRepository.GetFilingById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var legislativeStartDate = GetLegislativeStartDateForDate(filing.StartDate);
        var legislativeEndDate = GetLegislativeEndDateForDate(filing.EndDate);

        var allFilingsByFiler = await dependencies.FilingRepository.GetAllByFilerId(filing.FilerId);
        var firstFilingInSession = allFilingsByFiler
            .Where(x => x.FilingTypeId == filing.FilingTypeId
                && x.StartDate >= legislativeStartDate
                && x.EndDate < legislativeEndDate
                && (x.StatusId == FilingStatus.Accepted.Id || x.StatusId == FilingStatus.Incomplete.Id))
            .OrderBy(x => x.StartDate)
            .FirstOrDefault();

        return firstFilingInSession?.StartDate ?? legislativeStartDate;
    }

    /// <inheritdoc />
    public async Task<Filing?> CancelFiling(long filingId)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId) ?? throw new KeyNotFoundException($"Filing with ID {filingId} not found.");

        if (filing.StatusId != FilingStatus.Draft.Id)
        {
            // throw an exception for status not being 'draft'.
            throw new InvalidOperationException($"Cannot cancel a filing that is not in 'Draft' status. Id={filingId} Status={filing.StatusId}");
        }
        filing.StatusId = FilingStatus.Cancelled.Id;
        // Clear ParentId so another amendment can be created
        filing.ParentId = null;

        return await dependencies.FilingRepository.Update(filing);
    }

    public async Task<LobbyistReport?> CancelLobbyistReport(long filingId)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId);

        if (filing is LobbyistReport lobbyistReport)
        {

            if (filing.StatusId != FilingStatus.Draft.Id)
            {
                // throw an exception for status not being 'Draft'.
                throw new HttpRequestException("Cannot cancel a report that is not in 'Draft' status.", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }
            await dependencies.FilingRepository.UpdateProperty(filing, static r => r.StatusId, FilingStatus.Cancelled.Id);
            return lobbyistReport;
        }
        return null;
    }

    /// <inheritdoc />
    public async Task<ActionsLobbiedSummaryResponse> GetActionsLobbiedSummaryForFiling(long id)
    {
        // Retrieve the filing by ID
        var filing = await dependencies.FilingRepository.FindById(id);

        var report = filing switch
        {
            LobbyistEmployerReport lobbyistEmployerReport => BuildLobbyistEmployerActionsLobbiedSummaryResponse(lobbyistEmployerReport),
            Report72H report72H => BuildReport72HActionsLobbiedSummaryResponse(report72H),
            _ => throw new KeyNotFoundException($"Report not found. Id={id}")
        };

        // Retrieve the filing summaries for the given filing ID
        var filingSummaries = await dependencies.FilingSummaryRepository.GetAllByFilingId(id);

        // Find the Actions Lobbied summary
        var filingActionsLobbied = filingSummaries
            .FirstOrDefault(f => f.FilingSummaryTypeId == FilingSummaryType.ActionsLobbiedSummary.Id);

        if (filingActionsLobbied is null)
        {
            throw new KeyNotFoundException($"Actions Lobbied Filing Summary not found for Filing Id={id}");
        }

        // Retrieve the actions lobbied details
        var actionsLobbied = await actionsLobbiedSvc.GetActionsLobbiedByFilingId(id);

        // Construct and return the response
        return new ActionsLobbiedSummaryResponse
        {
            FilingId = filingActionsLobbied.FilingId,
            OtherActionsLobbied = report.OtherActionsLobbied,
            AdministrativeActions = actionsLobbied.AgencyActions,
            AssemblyBillActions = actionsLobbied.AssemblyBillActions,
            SenateBillActions = actionsLobbied.SenateBillActions,
            FilingSummaryId = filingActionsLobbied.Id,
            Valid = true,
            FilingSummaryStatus = filingActionsLobbied.FilingSummaryStatus,
        };
    }

    private static ActionsLobbiedSummaryResponse BuildLobbyistEmployerActionsLobbiedSummaryResponse(LobbyistEmployerReport report)
    {
        return new ActionsLobbiedSummaryResponse
        {
            OtherActionsLobbied = report.OtherActionsLobbied,
        };
    }

    private static ActionsLobbiedSummaryResponse BuildReport72HActionsLobbiedSummaryResponse(Report72H report)
    {
        return new ActionsLobbiedSummaryResponse
        {
            OtherActionsLobbied = report.OtherActionsLobbied,
        };
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> SubmitLobbyistReportForEfile(LobbyistReportDto submission)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        ArgumentNullException.ThrowIfNull(submission.LobbyistReport);

        var filingSummaryTypes = new[]
        {
                FilingSummaryType.CampaignContributionSummary.Id,
                FilingSummaryType.ActivityExpenseSummary.Id
            };
        if (submission.LobbyistReport.ParentId != null)
        {
            var parentFiling = await dependencies.FilingRepository.FindById((long)submission.LobbyistReport.ParentId);
            if (parentFiling != null)
            {
                submission.LobbyistReport.Parent = parentFiling;
                submission.LobbyistReport.ParentId = parentFiling.Id;
                submission.LobbyistReport.Original = parentFiling.Original;
                submission.LobbyistReport.OriginalId = parentFiling.OriginalId;
                submission.LobbyistReport.Version = parentFiling.Version++;
            }
        }


        var filer = await dependencies.FilerRepository.FindById(submission.LobbyistReport.FilerId);
        submission.LobbyistReport.Filer = filer;
        submission.LobbyistReport.RegistrationAtSubmission = filer?.CurrentRegistration;

        //CampaignContribution Contributor Filer Name
        foreach (var contribution in submission.CampaignContributions)
        {
            if (filer != null && contribution.ContributorFilerId == filer.Id)
            {
                contribution.ContributorFiler = filer;
            }
        }

        //validate Contacts and link then to transaction
        var contactsByExternalId = await FindAndUpdateExistingFilerContactsByExternalId(submission.LobbyistReport.FilerId, submission.FilerContacts);
        LinkTransactionsToContactsByExternalId(contactsByExternalId, submission.ActivityExpenses);

        //Lookup FilingPeriod based on begin/end date
        submission.LobbyistReport.FilingPeriodId = await dependencies.FilingPeriodRepository.GetFilingPeriodByStartDateAndEndDate(submission.LobbyistReport.StartDate, submission.LobbyistReport.EndDate);

        //Validate submission thru Decisions
        var decisionsData = await HandleLobbyistReportToDecisionInput(submission.LobbyistReport, submission.ActivityExpenses, submission.CampaignContributions);

        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(DecisionsWorkflow.SubmitLobbyistReportRuleset, decisionsData, true);
        validationErrors.AddRange(decisionResponse.Result);

        //Process Validation Result
        var fatalErrorsExist = validationErrors.Any(error => "fatal".Equals(error.ErrorType, StringComparison.OrdinalIgnoreCase));
        if (!fatalErrorsExist)
        {
            //Update Lobbyist report to reflect new status
            isValid = UpdateAcceptedLobbyistReport(submission.LobbyistReport, decisionResponse, true, dateTimeSvc);

            //Populate updated Transactions into LobbyistReport
            submission.LobbyistReport.FilingTransactions.AddRange(submission.ActivityExpenses.Select(a => new FilingTransaction() { Transaction = a }));
            submission.LobbyistReport.FilingTransactions.AddRange(submission.CampaignContributions.Select(a => new FilingTransaction() { Transaction = a }));
            var filingSummaryStatuses = await dependencies.FilingSummaryStatusRepository.GetAllFilingSummaryStatuses();

            submission.LobbyistReport.FilingSummaries = filingSummaryTypes
                .Select(typeId => new FilingSummary
                {
                    FilingSummaryTypeId = typeId,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    FilingSummaryStatusId = filingSummaryStatuses.FirstOrDefault(s => s.Name == NotStarted)?.Id,
                    NoActivityToReport = false
                }).ToList();
            //Create LobbyistReport in DB
            var lobbyistReport = await dependencies.FilingRepository.Create(submission.LobbyistReport);

            //insert/update FilingSummary values
            await RecalculateSummaryAmountsForFiling(lobbyistReport);

            //Create Attestion record
            submission.Attestation.FilingId = lobbyistReport.Id;
            await dependencies.AttestationRepository.Create(submission.Attestation);

            //Send Notifications
            await SendFilerNotifications(servicesDependencies, decisionResponse.ListOfNotifications,
                submission.LobbyistReport.FilerId);

            //Update Drafts
            await UpdateFutureDraftReports(
                submission.LobbyistReport.FilerId,
                FilingType.LobbyistReport,
                submission.LobbyistReport);
        }

        return new RegistrationResponseDto(submission.LobbyistReport.Id, isValid, validationErrors);
    }

    private static void LinkTransactionsToContactsByExternalId(Dictionary<string, FilerContact> contactsByExternalId, IEnumerable<Transaction>? transactions)
    {
        if (transactions != null)
        {
            foreach (var transaction in transactions)
            {
                if (transaction.ExternalContactId != null)
                {
                    transaction.Contact = contactsByExternalId[transaction.ExternalContactId];
                }
            }
        }
    }

    /// <summary>
    /// Link the Transaction.Contact to existing database records based on the external Id
    ///
    /// If a match is found the existing record will be updated with the contact information provided
    /// </summary>
    /// <param name="transactions"></param>
    /// <returns></returns>
    private async Task<Dictionary<string, FilerContact>> FindAndUpdateExistingFilerContactsByExternalId(long filerId, IEnumerable<FilerContact> incomingContacts)
    {
        Dictionary<string, FilerContact> contactsByExternalId = new();

        foreach (var incomingContact in incomingContacts)
        {
            if (incomingContact != null && incomingContact.ExternalId != null)
            {
                var existingContact = await dependencies.FilerContactRepository.GetFilerContactByFilerIdAndExternalId(filerId, incomingContact.ExternalId);
                if (existingContact != null)
                {
                    existingContact.UpdateFrom(incomingContact);
                    contactsByExternalId.Add(existingContact.ExternalId!, existingContact);
                }
                else
                {
                    contactsByExternalId.Add(incomingContact.ExternalId, incomingContact);
                }
            }
        }

        return contactsByExternalId;
    }

    public async Task<SubmitLobbyingReportResponse?> SubmitLobbyistReport(long filingId, bool? diligenceStatementVerified)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(filingId);

        if (filing is LobbyistReport lobbyistReport)
        {
            var decisionsData = await HandleLobbyistReportToDecisionInput(lobbyistReport);

            var decisionResponse =
                await servicesDependencies.DecisionsSvc
                    .InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
                        DecisionsWorkflow.SubmitLobbyistReportRuleset, decisionsData, true);

            var result = decisionResponse.Result;

            CheckFilingPeriodRangeValid(filing, result);

            if (result.Count == 0)
            {
                isValid = UpdateAcceptedLobbyistReport(lobbyistReport, decisionResponse, diligenceStatementVerified, dateTimeSvc);
                await dependencies.FilingRepository.Update(lobbyistReport);
                await SendFilerNotifications(servicesDependencies, decisionResponse.ListOfNotifications,
                    lobbyistReport.FilerId);

                await UpdateFutureDraftReports(
                    lobbyistReport.FilerId,
                    FilingType.LobbyistReport,
                    lobbyistReport);
            }
            else
            {
                validationErrors = await HandleFilingValidationErrors(lobbyistReport, result);
            }

            return new SubmitLobbyingReportResponse(filingId, isValid, validationErrors, lobbyistReport);
        }
        return null;
    }

    private static bool UpdateAcceptedLobbyistReport(LobbyistReport lobbyistReport, DecisionsSubmitLobbyistReportResponse decisionResponse, bool? diligenceStatementVerified, IDateTimeSvc dateTimeSvc)
    {
        bool isValid = true;
        lobbyistReport.StatusId = FilingStatus.Accepted.Id;
        lobbyistReport.DiligenceStatementVerified = diligenceStatementVerified;
        lobbyistReport.SubmittedDate = dateTimeSvc.GetCurrentDateTime();
        lobbyistReport.SubmittedLate = decisionResponse.IsLateFiling;
        return isValid;
    }

    /// <inheritdoc />
    public async Task<SubmitLobbyingReportResponse?> SendForAttestationLobbyistReport(long filingId)
    {
        bool isValid = false;

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is LobbyistReport lobbyistReport)
        {
            var decisionInput = await HandleLobbyistReportToDecisionInput(lobbyistReport);

            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSendForAttestationLobbyistReportResponse>(DecisionsWorkflow.SendForAttestationLobbyistReportRuleset, decisionInput, true);
            var decisionErrors = decisionResponse.Result;

            CheckFilingPeriodRangeValid(filing, decisionErrors);

            lobbyistReport.SubmittedDate = dateTimeSvc.GetCurrentDateTime();

            if (decisionErrors.Count == 0)
            {
                isValid = true;
                lobbyistReport.StatusId = FilingStatus.Pending.Id;
                lobbyistReport.SubmittedLate = decisionResponse.IsLateFiling;
                await dependencies.FilingRepository.Update(lobbyistReport);
                await SendFilerNotifications(servicesDependencies, decisionResponse.ListOfNotifications,
                    lobbyistReport.FilerId);
            }

            return new SubmitLobbyingReportResponse(filingId, isValid, decisionErrors, lobbyistReport);
        }
        return null;
    }

    private async Task<DecisionsSubmitLobbyistReport> HandleLobbyistReportToDecisionInput(LobbyistReport filing)
    {
        var activityExpenses = await transactionRepository.GetAllActivityExpenseTransactionsForFiling(filing.Id);
        var campaignContributions =
            await transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filing.Id);

        return await HandleLobbyistReportToDecisionInput(filing, activityExpenses, campaignContributions);
    }

    /// <summary>
    /// ActivityExpense.Contact should be fully populated
    /// ActivityExpense.TransactionReportablePersons should be fully populated
    /// LobbyingCampaignContribution.ContributorFiler?.CurrentRegistration?.Name
    /// </summary>
    /// <param name="lobbyistReport"></param>
    /// <param name="activityExpenses"></param>
    /// <param name="campaignContributions"></param>
    /// <returns></returns>
    private async Task<DecisionsSubmitLobbyistReport> HandleLobbyistReportToDecisionInput(LobbyistReport lobbyistReport,
        IEnumerable<CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense.ActivityExpense> activityExpenses,
        IEnumerable<LobbyingCampaignContribution> campaignContributions)
    {
        var filingPeriod = await dependencies.FilingPeriodRepository.FindById(lobbyistReport?.FilingPeriodId ?? 0);
        var endDate = filingPeriod?.EndDate ?? lobbyistReport!.EndDate;

        List<DecisionsSubmitActivityExpenseTransaction> activityExpenseTransaction = activityExpenses.Select(
            transaction => new DecisionsSubmitActivityExpenseTransaction
            {
                TransactionDate = transaction.TransactionDate,
                Notes = transaction.Notes,
                ActivityExpenseTypeId = transaction.ActivityExpenseTypeId,
                CreditCardCompanyName = transaction.CreditCardCompanyName,
                ActivityDescription = transaction.ActivityDescription,
                MonetaryTypeId = transaction.MonetaryTypeId,
                Amount = transaction.Amount.Value,
                FilerId = transaction.FilerId,
                ContactId = transaction.ContactId,
                IsCreditCard = transaction.MonetaryTypeId == MonetaryType.Credit.Id,
            }).ToList();

        List<DecisionsSubmitActivityExpensePayee?> activityExpensePayee = activityExpenses.Select(transaction =>
            {
                var contact = transaction.Contact;
                return PopulateActivityExpenseDecisionServicePayee(contact);
            })
            .Where(result => result != null)
            .ToList();

        List<DecisionsSubmitActivityExpenseReportablePerson> activityExpenseReportablePerson = activityExpenses
            .Where(transaction => transaction.TransactionReportablePersons != null)
            .SelectMany(transaction => transaction.TransactionReportablePersons!.Select(person =>
                new DecisionsSubmitActivityExpenseReportablePerson
                {
                    TransactionId = person.TransactionId,
                    FilerContactId = person.FilerContactId,
                    RegistrationId = person.RegistrationId,
                    Name = person.Name,
                    OfficialPosition = person.OfficialPosition,
                    OfficialPositionDescription = person.OfficialPositionDescription,
                    Agency = person.Agency,
                    Amount = transaction.Amount.Value,
                }))
            .ToList();

        List<DecisionsSubmitLobbyingCampaignContribution> lobbyingCampaignContribution = campaignContributions.Select(
                transaction => new DecisionsSubmitLobbyingCampaignContribution
                {
                    TransactionDate = transaction.TransactionDate,
                    Amount = transaction.Amount.Value,
                    IsRecipientCommittee = transaction.RecipientFilerId != null,
                    CommitteeId = transaction.RecipientFilerId,
                    FilerId = transaction.FilerId,
                    ContributorName = transaction.ContributorFiler?.CurrentRegistration?.Name,
                    NonCommitteeRecipentName = transaction.NonCommitteeRecipientName,
                    ContributorFilerId = transaction.ContributorFilerId,
                    NonFilerContributorName = transaction.NonFilerContributorName,
                    SeparateAccountName = transaction.SeparateAccountName,
                    IsContributorFiler = transaction.ContributorFilerId != null
                })
            .ToList();

        return new DecisionsSubmitLobbyistReport()
        {
            ActivityExpensePayee = activityExpensePayee,
            ActivityExpenseTransaction = activityExpenseTransaction,
            ActivityExpenseReportablePerson = activityExpenseReportablePerson,
            LobbyingCampaignContribution = lobbyingCampaignContribution,
            ExecutedDate = dateTimeSvc.GetCurrentDateTime(),
            FilingPeriodEndDate = endDate
        };
    }

    private async Task<DecisionsSubmitLobbyistEmployerReport> HandleLobbyistEmployerReportToDecisionInput(LobbyistEmployerReport filing)
    {
        var transactionsData = await FetchLobbyistEmployerReportTransactionsData(filing);
        var otherPayments = await BuildOtherPaymentsSection(transactionsData.OtherPaymentsToInfluenceTransaction);
        // Build Submit Lobbyist Employer Report for Decisions Input
        return new DecisionsSubmitLobbyistEmployerReport
        {
            PUCActivity = filing.TotalPaymentsPucActivity.HasValue
                ? [new DecisionsTotalPaymentsPucActivity { Amount = filing.TotalPaymentsPucActivity }]
                : [],
            LumpSums = BuildLumpSumsSection(filing),
            PaymentsToInHouseLobbyists =
            [
                new DecisionsPaymentsToInHouseLobbyists
                {
                    TotalPaymentToInHouseLobbyists = filing.TotalPaymentsToInHouseLobbyists
                }
            ],
            MajorDonorSelection = new List<DecisionsMajorDonorSelectionLobbyistEmployerReport>(),

            // Actions lobbied sections
            ActionsLobbied = BuildActionsLobbiedSection(transactionsData.ActionsLobbiedTransactions, filing),
            Agencies = BuildAgenciesSection(transactionsData.ActionsLobbiedTransactions),

            LobbyingCampaignContributon = BuildCampaignContributionSection(transactionsData.CampaignContributions),

            // Activity expenses
            ActivityExpenseTransaction = BuildActivityExpenseTransaction(transactionsData.ActivityExpenses),
            ActivityExpensePayee = transactionsData.ActivityExpensePayees!,
            ActivityExpenseReportablePerson = transactionsData.ActivityExpenseReportablePersons,
            AddPayee = BuildAddPayeeSection(transactionsData.ActivityExpensePayees),

            // Lobbying firms section
            AddFirmContact = BuildFirmContactSection(transactionsData.PaymentMadeLobbyingFirmTransactions),
            PaymentMadeToLobbyingFirm_SaveTransaction = BuildLobbyingFirmPaymentSection(transactionsData.PaymentMadeLobbyingFirmTransactions),

            OtherPayments_SaveTransaction = otherPayments,

            // Lobbying Coalition
            CoalitionPaymentAmount = transactionsData.PaymentMadeToLobbyingCoalitionResponses.Any()
                ? transactionsData.PaymentMadeToLobbyingCoalitionResponses.Select(payment =>
                        new PaymentReceiveLobbyingCoalitionPaymentAmountDs { Amount = payment.CumulativeAmount }).ToList()
                : [],
            CoalitionMemberInformation = BuildCoalitionMemberSection(transactionsData.PaymentReceiveLobbyingCoalitionResponses),
            PaymentReceivedByLobbyingCoalitions = transactionsData.PaymentReceiveLobbyingCoalitionResponses.Any()
                ?
                [
                    new PaymentReceiveLobbyingCoalitionDs
                    {
                        MemberCount = transactionsData.PaymentReceiveLobbyingCoalitionResponses.Count()
                    }
                ]
                : [],
            PaymentToLobbyingCoalition = transactionsData.PaymentMadeToLobbyingCoalitionResponses.Any()
                ? transactionsData.PaymentMadeToLobbyingCoalitionResponses.Select(payment =>
                    new PaymentMadeToLobbyingCoalitionDs { PeriodAmount = payment.AmountThisPeriod }).ToList()
                : [],
            AmendmentExplanation = filing.AmendmentExplanation
        };
    }

    private async Task<DecisionsLobbyistEmployerReportTransactionsDto> FetchLobbyistEmployerReportTransactionsData(LobbyistEmployerReport filing)
    {
        var result = new DecisionsLobbyistEmployerReportTransactionsDto();
        var legislativeStartDate = await GetLegislativeStartDateForFiling(filing.Id);

        // Retrieve transactions for the filing
        result.ActivityExpenses = await transactionRepository.GetAllActivityExpenseTransactionsForFiling(filing.Id);
        result.CampaignContributions = await transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filing.Id);
        result.PaymentMadeToLobbyingCoalitionResponses = await transactionRepository.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filing.Id, legislativeStartDate);
        result.PaymentReceiveLobbyingCoalitionResponses = await transactionHelperSvc.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filing.Id, legislativeStartDate);
        result.OtherPaymentsToInfluenceTransaction = await transactionRepository.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filing.Id, legislativeStartDate);
        result.PaymentMadeLobbyingFirmTransactions = await transactionHelperSvc.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filing.Id, legislativeStartDate);

        // Get actions lobbied of filing
        result.ActionsLobbiedTransactions = await actionsLobbiedSvc.GetActionsLobbiedByFilingId(filing.Id);

        // Map activity expense
        result.ActivityExpensePayees = result.ActivityExpenses.Any()
            ? result.ActivityExpenses
                .Select(transaction => PopulateActivityExpenseDecisionServicePayee(transaction.Contact))
                .Where(ae => ae != null)
                .Select(ae => ae!)  // Convert from nullable to non-nullable
                .ToList()
            : [];

        result.ActivityExpenseReportablePersons = result.ActivityExpenses
            .Where(transaction => transaction.TransactionReportablePersons != null)
            .SelectMany(transaction => transaction.TransactionReportablePersons!.Select(person =>
                new DecisionsSubmitActivityExpenseReportablePerson
                {
                    TransactionId = person.TransactionId,
                    FilerContactId = person.FilerContactId,
                    RegistrationId = person.Registration?.Id,
                    Name = person.Name,
                    OfficialPosition = person.OfficialPosition,
                    OfficialPositionDescription = person.OfficialPositionDescription,
                    Agency = person.Agency,
                    Amount = transaction.Amount.Value,
                }))
            .ToList();

        return result;
    }

    private static List<DecisionsLumpSumPayments> BuildLumpSumsSection(LobbyistEmployerReport filing)
    {
        return
        [
            new DecisionsLumpSumPayments
            {
                TotalOverheadExpense = filing.TotalOverheadExpense,
                TotalUnderThresholdPayments = filing.TotalUnderThresholdPayments
            }
        ];
    }


    private static List<DecisionsLobbyistEmployerReportActionsLobbied> BuildActionsLobbiedSection(
        ActionsLobbiedByEntityResponse actionsLobbied,
        LobbyistEmployerReport filing)
    {
        var advertisementSubjects = BuildLobbyingAdvertisementSubjects(actionsLobbied, filing.OtherActionsLobbied);

        // advertisementSubjects is empty, that means no actions lobbied were reported
        return advertisementSubjects.Count != 0
            ? new List<DecisionsLobbyistEmployerReportActionsLobbied>
            {
                new()
                {
                    LobbyingAdvertisementSubjects = advertisementSubjects,
                    BillCount =
                        actionsLobbied.SenateBillActions.Count() + actionsLobbied.AssemblyBillActions.Count(),
                    AdministrativeActionCount = actionsLobbied.AgencyActions.Count(),
                    OtherActionDescription = filing.OtherActionsLobbied
                }
            }
            : [];
    }

    private static List<DecisionsLobbyistEmployerReportActionsLobbiedAgencies> BuildAgenciesSection(ActionsLobbiedByEntityResponse actionsLobbied)
    {
        return actionsLobbied.AgencyActions.Select(a => new DecisionsLobbyistEmployerReportActionsLobbiedAgencies
        {
            AdministrativeActionDescription = a.AdministrativeAction,
            AgencyName = a.AgencyName
        }).ToList();
    }

    private static List<DecisionsSubmitLobbyingCampaignContribution> BuildCampaignContributionSection(IEnumerable<LobbyingCampaignContribution> campaignContributions)
    {
        IEnumerable<LobbyingCampaignContribution> lobbyingCampaignContributions = campaignContributions.ToList();
        return lobbyingCampaignContributions.Any() ?
            lobbyingCampaignContributions.Select(c => new DecisionsSubmitLobbyingCampaignContribution(c)).ToList() :
            new List<DecisionsSubmitLobbyingCampaignContribution>();
    }

    private static List<DecisionsSubmitActivityExpenseTransaction> BuildActivityExpenseTransaction(
        IEnumerable<ActivityExpense> activityExpenses)
    {
        return activityExpenses.Any()
            ? activityExpenses.Select(activity => new DecisionsSubmitActivityExpenseTransaction(activity)).ToList()
            : [];
    }

    private static List<DecisionServicePayee> BuildAddPayeeSection(
        List<DecisionsSubmitActivityExpensePayee> activityExpensePayees)
    {
        return activityExpensePayees.Count > 0
            ? activityExpensePayees.Select(payeeInfo => new DecisionServicePayee
            {
                FirstName = payeeInfo.FirstName ?? string.Empty,
                MiddleName = payeeInfo.MiddleName ?? string.Empty,
                LastName = payeeInfo.LastName ?? string.Empty,
                OrganizationName = payeeInfo.OrganizationName ?? string.Empty,
                ContactType = payeeInfo.ContactType ?? string.Empty,
                Address1 = payeeInfo.Address1 != null
                    ? new PayeeAddress
                    {
                        Street = payeeInfo.Address1.Street ?? string.Empty,
                        Street2 = payeeInfo.Address1.Street2 ?? string.Empty,
                        City = payeeInfo.Address1.City ?? string.Empty,
                        State = payeeInfo.Address1.State ?? string.Empty,
                        Zip = payeeInfo.Address1.Zip ?? string.Empty,
                        Country = payeeInfo.Address1.Country ?? string.Empty
                    }
                    : new PayeeAddress()
            }).ToList()
            : [];
    }

    private static List<PaymentMadeToLobbyingFirmContactDs> BuildFirmContactSection(IEnumerable<PaymentMadeToLobbyingFirmsResponse> firmTransactions)
    {
        return firmTransactions.Any()
            ? [.. firmTransactions.Select(firm =>
                new PaymentMadeToLobbyingFirmContactDs
                {
                    LobbyingFirmName = firm?.FirmName,
                    Address1 = firm?.Contact?.Addresses.FirstOrDefault() is { } address
                        ? new PaymentMadeToLobbyingFirmContactAddress(address)
                        : null
                })]
            : [];
    }

    private static List<PaymentMadeToLobbyingFirmsDs> BuildLobbyingFirmPaymentSection(
        IEnumerable<PaymentMadeToLobbyingFirmsResponse> firmTransactions)
    {
        return firmTransactions.Any()
            ? firmTransactions.Select(firm => new PaymentMadeToLobbyingFirmsDs
            {
                FeesRetainers = firm?.FeesAndRetainersAmount,
                ExpensesReimbursement = firm?.ReimbursementOfExpensesAmount,
                AdvancePayment = firm?.AdvancesOrOtherPaymentsAmount,
                AdvancePaymentExplanation = firm?.AdvancesOrOtherPaymentsExplanation
            }).ToList()
            : [];
    }

    private static List<PaymentReceiveLobbyingCoalitionMemberDs> BuildCoalitionMemberSection(
        IEnumerable<PaymentReceiveLobbyingCoalitionResponse> responses)
    {
        var memberContacts = responses
            .Where(p => p.Contact?.EmailAddresses.Count > 0)
            .Select(x => x.Contact);

        if (!memberContacts.Any())
        {
            return new List<PaymentReceiveLobbyingCoalitionMemberDs>();
        }

        return [.. memberContacts.Select(memberContact => new PaymentReceiveLobbyingCoalitionMemberDs
        {
            ContactType = memberContact?.TypeId switch
            {
                var t when t == FilerContactType.Individual.Id => FilerContactType.Individual.Name,
                var t when t == FilerContactType.Organization.Id => FilerContactType.Organization.Name,
                var t when t == FilerContactType.Candidate.Id => FilerContactType.Candidate.Name,
                var t when t == FilerContactType.Committee.Id => FilerContactType.Committee.Name,
                var t when t == FilerContactType.Filer.Id => FilerContactType.Filer.Name,
                _ => string.Empty
            },
            ContactFirstName = memberContact switch
            {
                CandidateContactResponseDto candidate => candidate.FirstName,
                IndividualContactResponseDto individual => individual.FirstName,
                _ => string.Empty
            },
            ContactLastName = memberContact switch
            {
                CandidateContactResponseDto candidate => candidate.LastName,
                IndividualContactResponseDto individual => individual.LastName,
                _ => string.Empty
            },
            OrganizationName = memberContact is OrganizationContactResponseDto org ? org.OrganizationName : string.Empty,
            Email = memberContact?.EmailAddresses.FirstOrDefault()?.Email ?? string.Empty,
            Address = memberContact?.Addresses?.FirstOrDefault() is { } address
                ? new PaymentReceiveLobbyingCoalitionMemberAddress(address)
                : null
        })];
    }

    private async Task<List<OtherPaymentsToInfluenceDs>> BuildOtherPaymentsSection(
        IEnumerable<OtherPaymentsToInfluenceResponse> otherPayments)
    {
        if (!otherPayments.Any())
        {
            return new List<OtherPaymentsToInfluenceDs>();
        }

        var advertisingPaymentIds = otherPayments
            .Where(x => x.PaymentCodeId == PaymentCode.Advertising.Id && x.Id > 0)
            .Select(payment => payment.Id!.Value)
            .ToList();

        var advertisingTransactionsDict = new Dictionary<long, ActionsLobbiedByEntityResponse>();
        foreach (var id in advertisingPaymentIds)
        {
            try
            {
                var actionsLobbied = await actionsLobbiedSvc.GetActionsLobbiedByTransactionId(id);
                advertisingTransactionsDict.Add(id, actionsLobbied);
            }
            catch
            {
                advertisingTransactionsDict.Add(id, new ActionsLobbiedByEntityResponse());
            }
        }

        return [.. otherPayments.Select(payment =>
        {
            // get the advertising transaction data if it exists
            ActionsLobbiedByEntityResponse advertisingTransaction = payment?.Id.HasValue == true && advertisingPaymentIds.Count > 0
                ? advertisingTransactionsDict.GetValueOrDefault(payment.Id.Value) ?? new ActionsLobbiedByEntityResponse()
                : new ActionsLobbiedByEntityResponse();

            return new OtherPaymentsToInfluenceDs
            {
                OtherPaymentDescription = payment?.PaymentCodeDescription ?? string.Empty,
                QuarterlyAmount = payment?.Amount,
                PaymentCodeId = payment?.PaymentCodeId?.ToString(CultureInfo.InvariantCulture),
                AdvertisingInfo = new AdvertisingInfoDs
                {
                    LobbyingAdvertisementSubjects = BuildLobbyingAdvertisementSubjects(
                        advertisingTransaction,
                        payment?.OtherActionsLobbied),
                    AssemblyBills = advertisingTransaction.AssemblyBillActions?
                        .Select(x => new BillInfoDs { BillNumber = x.BillNumber })
                        .ToList() ?? [],
                    SenateBills = advertisingTransaction.SenateBillActions?
                        .Select(x => new BillInfoDs { BillNumber = x.BillNumber })
                        .ToList() ?? [],
                    AdministrativeActions = advertisingTransaction.AgencyActions?
                        .Select(x => new AdministrativeActionDs
                        {
                            AgencyOrOffice = x.AgencyName,
                            ActionDescription = x.AgencyDescription
                        })
                        .ToList() ?? [],
                    OtherActionDescription = payment?.OtherActionsLobbied
                }
            };
        })];
    }

    /// Builds a list of lobbying advertisement subjects based on the actions lobbied and additional information.
    private static List<string> BuildLobbyingAdvertisementSubjects(
        ActionsLobbiedByEntityResponse action,
        string? otherActionsLobbied)
    {
        var subjects = new List<string>();

        if (action.AssemblyBillActions.Any() || action.SenateBillActions.Any())
        {
            subjects.Add(FilingConstants.AdvertisementSubject.Legislation);
        }

        if (action.AgencyActions.Any())
        {
            subjects.Add(FilingConstants.AdvertisementSubject.AdministrativeAction);
        }

        if (!string.IsNullOrEmpty(otherActionsLobbied))
        {
            subjects.Add(FilingConstants.AdvertisementSubject.Other);
        }

        return subjects;
    }

    /// <summary>
    /// Send the notifications from decisions
    /// </summary>
    /// <param name="sharedDependencies"></param>
    /// <param name="notifications">List of Notifications</param>
    /// <param name="filerId">Filer Id</param>
    /// <returns></returns>
    private static async Task SendFilerNotifications(FilingSharedServicesDependencies sharedDependencies, List<NotificationTrigger>? notifications, long filerId, IDictionary<string, string>? notificationData = null)
    {
        if (notifications is null)
        {
            return;
        }

        foreach (NotificationTrigger notification in notifications)
        {
            if (notification.SendNotification && notification.NotificationTemplateId is { } templateId)
            {
                await sharedDependencies.NotificationSvc.SendFilerNotification(new SendFilerNotificationRequest(templateId, filerId, notification.DueDate, notificationData));
            }
        }
    }

    /// <summary>
    /// Sends user notifications to a list of users based on the provided notification triggers.
    /// </summary>
    /// <param name="sharedDependencies">The shared services dependencies required for sending notifications.</param>
    /// <param name="notifications">A list of notification triggers to process. If null, no notifications are sent.</param>
    /// <param name="userIds">A list of user IDs to whom notifications will be sent. If null, no notifications are sent.</param>
    /// <param name="filerId"></param>
    /// <returns>A task representing the asynchronous operation.</returns>
    private static async Task SendMultipleUsersNotifications(
        FilingSharedServicesDependencies sharedDependencies,
        List<NotificationTrigger>? notifications,
        List<long>? userIds,
        long? filerId,
        IDictionary<string, string>? notificationData = null)
    {
        if (userIds is null || notifications is null)
        {
            return;
        }

        try
        {
            foreach (var notification in notifications)
            {
                if (notification is not { SendNotification: true, NotificationTemplateId: { } templateId })
                {
                    continue;
                }

                foreach (var userId in userIds)
                {
                    try
                    {
                        await sharedDependencies.NotificationSvc.SendUserNotification(
                            new SendUserNotificationRequest(templateId, userId, filerId, notification.DueDate, notificationData));
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
                }
            }
        }
        catch (Exception)
        {
            // ignored
        }
    }

    private async Task<List<WorkFlowError>> HandleFilingValidationErrors(Filing filing, List<WorkFlowError> errors)
    {
        var fatalErrorsExist = errors.Any(error =>
            "fatal".Equals(error.ErrorType, StringComparison.OrdinalIgnoreCase));

        if (!fatalErrorsExist)
        {
            filing.StatusId = FilingStatus.Incomplete.Id;
            await dependencies.FilingRepository.Update(filing);
        }

        return errors;
    }

    private async Task UpdateFutureDraftReports(
        long filerId,
        FilingType filingType,
        Filing report)
    {
        var existingFilings = await dependencies.FilingRepository.GetAllByFilerId(filerId);
        var legislativeStartDate = GetLegislativeEndDateForDate(report.EndDate);

        var futureDraftReports = existingFilings
            .Where(x =>
                x.FilerId == filerId
                && x.FilingTypeId == filingType.Id
                && x.StatusId == FilingStatus.Draft.Id
                && x.StartDate > report.EndDate
                && x.EndDate <= legislativeStartDate)
            .OrderBy(x => x.EndDate)
            .ToList();

        var submittedAmountsByType = report.FilingSummaries
            .ToDictionary(s => s.FilingSummaryTypeId, s => s.PeriodAmount);

        foreach (var futureDraftReport in futureDraftReports)
        {
            bool needsUpdate = false;

            foreach (var futureSummary in futureDraftReport.FilingSummaries)
            {
                if (submittedAmountsByType.TryGetValue(futureSummary.FilingSummaryTypeId, out var periodAmount))
                {
                    futureSummary.ToDateAmount += periodAmount;
                    needsUpdate = true;
                }
            }

            if (needsUpdate)
            {
                await dependencies.FilingRepository.Update(futureDraftReport);
            }
        }
    }

    public static DecisionsSubmitActivityExpensePayee? PopulateActivityExpenseDecisionServicePayee(FilerContact? contact)
    {
        if (contact == null)
        {
            throw new ArgumentNullException(nameof(contact), "Contact cannot be null.");
        }

        var address = contact.AddressList?.Addresses[0];
        return contact switch
        {
            IndividualContact individual => new DecisionsSubmitActivityExpensePayee
            {
                FirstName = individual.FirstName,
                LastName = individual.LastName,
                MiddleName = individual.MiddleName,
                ContactType = FilerContactType.Individual,
                Address1 = new()
                {
                    Country = address?.Country,
                    Street = address?.Street,
                    Street2 = address?.Street2,
                    City = address?.City,
                    State = address?.State,
                    Zip = address?.Zip,
                }
            },
            OrganizationContact organization => new DecisionsSubmitActivityExpensePayee
            {
                OrganizationName = organization.OrganizationName,
                ContactType = FilerContactType.Organization,
                Address1 = new()
                {
                    Country = address?.Country,
                    Street = address?.Street,
                    Street2 = address?.Street2,
                    City = address?.City,
                    State = address?.State,
                    Zip = address?.Zip,
                }
            },
            _ => null
        };
    }

    public async Task<DateTime> GetLegislativeStartDateForFiling(long filingId)
    {
        var filingPeriodStart = await dependencies.FilingPeriodRepository.GetFilingPeriodStartDateForFiling(filingId);
        return GetLegislativeStartDateForDate(filingPeriodStart);
    }

    public DateTime GetLegislativeStartDateForDate(DateTime startDate)
    {
        return new DateTime(
            startDate.Year % 2 == 0 ? startDate.Year - 1 : startDate.Year,
            1, 1, 0, 0, 0, DateTimeKind.Local
        );
    }

    public DateTime GetLegislativeEndDateForDate(DateTime endDate)
    {
        var legislativeStartYear = endDate.Year % 2 == 0 ? endDate.Year - 1 : endDate.Year;
        return new DateTime(
            legislativeStartYear + 2,
            1, 1, 0, 0, 0, DateTimeKind.Local
        );
    }

    /// <inheritdoc /> 
    public async Task<IEnumerable<FilingSummary>> GetAllFilingSummariesByFilingId(long filingId)
    {
        return await dependencies.FilingSummaryRepository.GetAllByFilingId(filingId);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<FilingReportGridDto>> GetFilingReports()
    {
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUsers = await filerUserRepository.FindFilerUsersByUserId((long)userId);
        var filerIds = filerUsers.Select(fu => fu.FilerId);

        var query = await dependencies.FilingRepository.GetFilingReportsByFilerIds(filerIds);
        return query
            .Select(x =>
            {
                var mappedName = FilingTypeMappings.Map.TryGetValue(x.Name, out var displayName) ? displayName : x.Name;
                var finalName = x.Version > 0 ? $"{mappedName} Amendment {x.Version:00}" : mappedName;

                return new FilingReportGridDto
                {
                    Id = x.Id,
                    CreatedDate = x.CreatedDate,
                    ReportName = finalName,
                    ReportPeriod = (x.StartDate != null && x.EndDate != null) ? $"{x.StartDate?.Date.ToShortDateString()} - {x.EndDate?.Date.ToShortDateString()}" : "N/A",
                    Status = x.Status ?? string.Empty,
                    TypeId = x.TypeId,
                    IsAllowedAmending = x.IsAllowedAmending,
                };
            });

    }

    public async Task<IEnumerable<LegislativeSession>> GetAllLegislativeSessions()
    {

        return await dependencies.FilingRepository.GetAllLegislativeSessions();
    }

    #region 72h Report

    public async Task<FilingResponseDto> CreateReport72H(long filerId)
    {
        var filingSummaryTypes = new[]
        {
            FilingSummaryType.ActionsLobbiedSummary.Id,
            FilingSummaryType.LobbyingAdvertisementSummary.Id
        };

        var filingSummaryStatuses = await dependencies.FilingSummaryStatusRepository.GetAllFilingSummaryStatuses();

        var report72H = new Report72H
        {
            StatusId = FilingStatus.Draft.Id,
            CreatedBy = filerId,
            FilerId = filerId,
            FilingTypeId = FilingType.Report72h.Id,
            FilingSummaries = filingSummaryTypes
            .Select(typeId => new FilingSummary
            {
                FilingSummaryTypeId = typeId,
                PeriodAmount = 0,
                ToDateAmount = 0,
                FilingSummaryStatusId = filingSummaryStatuses.FirstOrDefault(s => s.Name == FilingSummaryStatus.NotStarted.Name)?.Id,
                NoActivityToReport = false
            }).ToList()!
        };
        var newReport72H = await dependencies.FilingRepository.Create(report72H);

        newReport72H.OriginalId = newReport72H.Id;
        await dependencies.FilingRepository.Update(newReport72H);

        return new FilingResponseDto(newReport72H);
    }

    /// <inheritdoc />
    public async Task<Report72HResponseDto> GetReport72H(long id)
    {
        var filing = await dependencies.FilingRepository.FindById(id);
        if (filing is not Report72H report72H)
        {
            throw new KeyNotFoundException($"72H Report not found Id={id}");
        }
        var actionsLobbied = await actionsLobbiedSvc.GetActionsLobbiedByFilingId(id);
        return new Report72HResponseDto
        {
            Id = report72H.Id,
            FilerId = report72H.FilerId,
            ParentId = report72H.ParentId,
            Status = report72H.StatusId,
            FilerName = report72H.Filer?.CurrentRegistration?.Name,
            SubmittedDate = report72H.SubmittedDate,
            Version = report72H.Version,
            OtherActionsLobbied = report72H.OtherActionsLobbied,
            AgencyActions = actionsLobbied.AgencyActions,
            AssemblyBillActions = actionsLobbied.AssemblyBillActions,
            SenateBillActions = actionsLobbied.SenateBillActions
        };

    }
    public async Task<ActionsLobbiedSummaryResponse> UpdateReport72HActionsLobbied(long id, Report72HActionsLobbiedRequestDto request)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(id);
        if (filing is not Report72H report72H)
        {
            throw new KeyNotFoundException($"72H Report not found Id={id}");
        }

        var filingSummaries = await dependencies.FilingSummaryRepository.GetAllByFilingId(id);

        var filingActionsLobbied =
            filingSummaries.FirstOrDefault(f => f.FilingSummaryTypeId == FilingSummaryType.ActionsLobbiedSummary.Id) ??
            throw new KeyNotFoundException($"Actions Lobbied Filing Summary not found.");


        var decisionsData = new DecisionsReport72HActionsLobbied
        {
            LobbyingAdvertisementSubjects = request.LobbyingAdvertisementSubjects,
            BillCount = request.SenateBills.Count + request.AssemblyBills.Count,
            AdministrativeActionCount = request.AdministrativeActions.Count,
            OtherActionDescription = request.OtherActionsLobbied
        };
        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsReport72HActionsLobbied, List<WorkFlowError>>(DecisionsWorkflow.FDLOBFiling72HourReportActionsLobbied, decisionsData, true);

        var actionsLobbied = new ActionsLobbiedByEntityResponse();

        if (decisionResponse.Count == 0)
        {
            isValid = true;
            _ = await actionsLobbiedSvc.UpsertActionsLobbiedForFiling(id, [.. CombineActionsLobbied(request)]);

            report72H.OtherActionsLobbied = request.OtherActionsLobbied;
            _ = await dependencies.FilingRepository.Update(report72H);

            filingActionsLobbied.FilingSummaryStatusId = FilingSummaryStatus.InProgress.Id;
            _ = await dependencies.FilingSummaryRepository.Update(filingActionsLobbied);

            actionsLobbied = await actionsLobbiedSvc.GetActionsLobbiedByFilingId(id);
        }
        else
        {
            validationErrors = decisionResponse;
        }

        return new ActionsLobbiedSummaryResponse
        {
            FilingSummaryId = filingActionsLobbied.Id,
            FilingId = filingActionsLobbied.FilingId,
            FilingSummaryStatus = filingActionsLobbied.FilingSummaryStatus,
            OtherActionsLobbied = report72H.OtherActionsLobbied,
            AdministrativeActions = actionsLobbied.AgencyActions,
            AssemblyBillActions = actionsLobbied.AssemblyBillActions,
            SenateBillActions = actionsLobbied.SenateBillActions,
            Valid = isValid,
            ValidationErrors = validationErrors
        };
    }

    /// <inheritdoc/>
    public async Task<ValidateReport72HActionsLobbiedResponseDto> ValidateReport72HActionsLobbiedBills(ValidateReport72HActionsLobbiedRequestDto request)
    {
        var decisionsData = request.ActionsLobbied.Select(r =>
        {
            var officailPosition = r.OfficialPositionId.HasValue ? r.OfficialPositionId.Value.ToString(CultureInfo.InvariantCulture) : null;
            if (r.OfficialPositionId == OfficialPosition.Other.Id)
            {
                officailPosition = "Other";
            }
            return new DecisionsReport72HActionsLobbiedBills
            {
                OfficialPosition = officailPosition,
                OfficialPositionDescription = r.OfficialPositionDescription
            };
        });

        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<List<DecisionsReport72HActionsLobbiedBills>, List<WorkFlowError>>(DecisionsWorkflow.FDLOBFiling72HourReportActionsLobbiedBills, [.. decisionsData], true);

        return new ValidateReport72HActionsLobbiedResponseDto { ValidationErrors = decisionResponse };
    }

    /// <inheritdoc/>
    public async Task<ValidateReport72HActionsLobbiedResponseDto> ValidateReport72HActionsLobbiedAgencies(ValidateReport72HActionsLobbiedRequestDto request)
    {
        var decisionsData = request.ActionsLobbied.Select(r =>
        {
            var officailPosition = r.OfficialPositionId.HasValue ? r.OfficialPositionId.Value.ToString(CultureInfo.InvariantCulture) : null;
            if (r.OfficialPositionId == OfficialPosition.Other.Id)
            {
                officailPosition = "Other";
            }
            return new DecisionsReport72HActionsLobbiedAgencies
            {
                AgencyName = r.AgencyId.HasValue ? r.AgencyId.Value.ToString(CultureInfo.InvariantCulture) : r.AgencyDescription,
                AdministrativeActionDescription = r.AdministrativeAction,
                OfficialPosition = officailPosition,
                OfficialPositionDescription = r.OfficialPositionDescription,
            };
        });

        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<List<DecisionsReport72HActionsLobbiedAgencies>, List<WorkFlowError>>(DecisionsWorkflow.FDLOBFiling72HourReportActionsLobbiedAgencies, [.. decisionsData], true);

        return new ValidateReport72HActionsLobbiedResponseDto { ValidationErrors = decisionResponse };
    }
    #endregion

    #region Private Methods

    /// <inheritdoc/>
    public async Task<UpdatePucActivityPaymentResponseDto> UpdatePucActivityPayment(long id, UpdatePucActivityPaymentRequestDto request)
    {
        var decisionsData = new DecisionsTotalPaymentsPucActivity
        {
            Amount = request.TotalPaymentsPucActivity
        };

        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsTotalPaymentsPucActivity, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBTransactionLobbyistEmployerReportPUCActivity,
            decisionsData,
            true);

        if (decisionResponse.Count != 0)
        {
            return new UpdatePucActivityPaymentResponseDto
            {
                Valid = false,
                ValidationErrors = decisionResponse
            };
        }

        var filing = await dependencies.FilingRepository.GetFilingById(id)
            ?? throw new KeyNotFoundException($"Filing not found Id={id}");

        filing.TotalPaymentsPucActivity = request.TotalPaymentsPucActivity;
        _ = await dependencies.FilingRepository.Update(filing);

        return new UpdatePucActivityPaymentResponseDto
        {
            Valid = true,
            ValidationErrors = []
        };
    }

    public async Task<ValidatedReport72H?> SendForAttestationReport72H(long filingId, List<long> selectedResponsibleOfficerIds)
    {
        bool isValid = false;

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is Report72H report72H)
        {
            var decisionInput = await HandleReport72HToDecisionInput(filing);

            var workflow = (filing.Version > 0) ? DecisionsWorkflow.FDLOBFiling72HourReportSendAttestationAmendment : DecisionsWorkflow.SendForAttestationReport72HRuleset;

            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(workflow, decisionInput, false);
            var decisionErrors = decisionResponse.Result;

            report72H.SubmittedDate = DateTime.UtcNow;

            if (decisionErrors.Count == 0)
            {
                isValid = true;
                report72H.StatusId = FilingStatus.Pending.Id;
                await dependencies.FilingRepository.Update(report72H);
                IDictionary<string, string>? notificationData = new Dictionary<string, string>
                {
                    [Status] = FilingStatus.Pending.Name
                };

                await SendMultipleUsersNotifications(servicesDependencies, decisionResponse.Notifications, selectedResponsibleOfficerIds, filing.FilerId, notificationData);
            }

            return new ValidatedReport72H(filingId, isValid, decisionErrors, report72H);
        }
        return null;
    }

    private async Task<DecisionsSendForAttestationReport72H> HandleReport72HToDecisionInput(Filing filing)
    {
        var actionsLobbied = await GetReport72H(filing.Id);
        var lobbyingAdvertisement = await transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filing.Id);

        var lobbyingAdvertisementSubjects = new List<string>();

        if (actionsLobbied.SenateBillActions.Any() || actionsLobbied.AssemblyBillActions.Any())
        {
            lobbyingAdvertisementSubjects.Add("Legislative");
        }

        if (actionsLobbied.AgencyActions.Any())
        {
            lobbyingAdvertisementSubjects.Add("Administrative");
        }

        if (actionsLobbied.OtherActionsLobbied != null)
        {
            lobbyingAdvertisementSubjects.Add("Other");
        }

        var report72HActionsLobbied = new List<DecisionsReport72HActionsLobbied>()
        {
            new()
            {
                LobbyingAdvertisementSubjects = lobbyingAdvertisementSubjects,
                BillCount =
                    actionsLobbied.SenateBillActions.Count() + actionsLobbied.AssemblyBillActions.Count(),
                AdministrativeActionCount = actionsLobbied.AgencyActions.Count(),
                OtherActionDescription = actionsLobbied.OtherActionsLobbied
            }
        };


        var report72ActionsLobbiedBills = actionsLobbied.AssemblyBillActions
            .Select(assemblyBill => new DecisionsReport72HActionsLobbiedBills
            {
                OfficialPosition = assemblyBill.OfficialPositionId?.ToString(CultureInfo.InvariantCulture),
                OfficialPositionDescription = assemblyBill.OfficialPositionDescription,
            })
            .Concat(actionsLobbied.SenateBillActions
                .Select(senateBill => new DecisionsReport72HActionsLobbiedBills
                {
                    OfficialPosition = senateBill.OfficialPositionId?.ToString(CultureInfo.InvariantCulture),
                    OfficialPositionDescription = senateBill.OfficialPositionDescription,
                })
            ).ToList();

        var report72ActionsLobbiedAgencies = actionsLobbied.AgencyActions
             .Select(agency => new DecisionsReport72HActionsLobbiedAgencies
             {
                 AgencyName = agency.AgencyId?.ToString(CultureInfo.InvariantCulture) ?? agency.AgencyDescription,
                 AdministrativeActionDescription = agency.AdministrativeAction,
                 OfficialPosition = agency.OfficialPositionId?.ToString(CultureInfo.InvariantCulture),
                 OfficialPositionDescription = agency.OfficialPositionDescription,
             }).ToList();

        var report72LobbyingAdvertisements = new List<LobbyingAdvertisementDs>();

        if (lobbyingAdvertisement != null)
        {
            var lobbyingAdvertisementDs = new LobbyingAdvertisementDs
            {
                DistributionMethod = lobbyingAdvertisement.DistributionMethodId?.ToString(CultureInfo.InvariantCulture),
                Description = lobbyingAdvertisement.DistributionMethodDescription,
                PublicationDate = lobbyingAdvertisement.PublicationDate ?? DateTime.MinValue,
                Amount = lobbyingAdvertisement.Amount,
            };
            report72LobbyingAdvertisements.Add(lobbyingAdvertisementDs);
        }

        return new DecisionsSendForAttestationReport72H()
        {
            ActionsLobbied = report72HActionsLobbied,
            ActionsLobbied_Bills = report72ActionsLobbiedBills,
            ActionsLobbied_Agencies = report72ActionsLobbiedAgencies,
            LobbyingAdvertisement = report72LobbyingAdvertisements
        };
    }

    private static List<ActionsLobbiedRequestDto?> CombineActionsLobbied(Report72HActionsLobbiedRequestDto dto)
    {
        return (dto.AssemblyBills ?? Enumerable.Empty<ActionsLobbiedRequestDto?>())
            .Concat(dto.SenateBills ?? Enumerable.Empty<ActionsLobbiedRequestDto?>())
            .Concat(dto.AdministrativeActions ?? Enumerable.Empty<ActionsLobbiedRequestDto?>())
            .ToList();
    }

    private static List<ActionsLobbiedRequestDto?> CombineActionsLobbied(LobbyistEmployerActionsLobbiedRequest request)
    {
        return (request.AssemblyBills ?? Enumerable.Empty<ActionsLobbiedRequestDto?>())
            .Concat(request.SenateBills ?? Enumerable.Empty<ActionsLobbiedRequestDto?>())
            .Concat(request.AdministrativeActions ?? Enumerable.Empty<ActionsLobbiedRequestDto?>())
            .ToList();
    }

    #endregion

    #region 48h Report
    public async Task<FilingResponseDto> CreateReport48H(long filerId)
    {
        var filingSummaryTypes = new[]
        {
            FilingSummaryType.EndOfSessionLobbyingSummary.Id
        };

        var filingSummaryStatuses = await dependencies.FilingSummaryStatusRepository.GetAllFilingSummaryStatuses();

        var report48H = new Report48H
        {
            StatusId = FilingStatus.Draft.Id,
            CreatedBy = filerId,
            FilerId = filerId,
            FilingTypeId = FilingType.Report48h.Id,
            FilingSummaries = filingSummaryTypes
            .Select(typeId => new FilingSummary
            {
                FilingSummaryTypeId = typeId,
                PeriodAmount = 0,
                ToDateAmount = 0,
                FilingSummaryStatusId = filingSummaryStatuses.FirstOrDefault(s => s.Name == FilingSummaryStatus.NotStarted.Name)?.Id,
                NoActivityToReport = false
            }).ToList()!
        };
        var newReport48H = await dependencies.FilingRepository.Create(report48H);

        newReport48H.OriginalId = newReport48H.Id;
        await dependencies.FilingRepository.Update(newReport48H);

        return new FilingResponseDto(newReport48H);
    }

    /// <inheritdoc />
    public async Task<Report48HResponseDto> GetReport48H(long id)
    {
        var filing = await dependencies.FilingRepository.FindById(id);
        if (filing is not Report48H report48H)
        {
            throw new KeyNotFoundException($"48H Report not found Id={id}");
        }
        return new Report48HResponseDto
        {
            Id = report48H.Id,
            FilerId = report48H.FilerId,
            ParentId = report48H.ParentId,
            Status = report48H.StatusId,
            FilerName = report48H.Filer?.CurrentRegistration?.Name,
            SubmittedDate = report48H.SubmittedDate,
            Version = report48H.Version,
        };

    }

    public async Task<SubmitLobbyingReportResponse?> SubmitReport48H(long filingId, bool? diligenceStatementVerified)
    {
        bool isValid = false;
        List<WorkFlowError> validationErrors = [];

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is not Report48H report48H)
        {
            throw new KeyNotFoundException($"48H Report not found Id={filingId}");
        }
        var decisionsData = await HandleReport48HToDecisionInput(filing);

        var workflow = (filing.Version > 0) ? DecisionsWorkflow.FDLOBFiling48HourAmendReportSubmission : DecisionsWorkflow.FDLOBFiling48HourReportSubmitReport;

        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(workflow, decisionsData, true);
        var result = decisionResponse.Errors;
        if (result.Count == 0)
        {
            isValid = true;
            report48H.StatusId = FilingStatus.Accepted.Id;
            report48H.DiligenceStatementVerified = diligenceStatementVerified;
            report48H.SubmittedDate = dateTimeSvc.GetCurrentDateTime();
            _ = await dependencies.FilingRepository.Update(report48H);
            IDictionary<string, string>? notificationData = new Dictionary<string, string>
            {
                [Status] = FilingStatus.Accepted.Name
            };
            await SendFilerNotifications(servicesDependencies, decisionResponse.Notifications, report48H.FilerId, notificationData);

            await UpdateFutureDraftReports(
                report48H.FilerId,
                FilingType.Report48h,
                report48H);
        }
        else
        {
            validationErrors = await HandleFilingValidationErrors(report48H, result);
        }
        return new SubmitLobbyingReportResponse(filingId, isValid, validationErrors, filing);
    }

    public async Task<DecisionsSubmitReport48H> HandleReport48HToDecisionInput(Filing filing)
    {
        var endOfSessionTransactions = await transactionRepository.GetAllEndOfSessionLobbyingTransactionsForFiling(filing.Id);

        var addNewTransactions = new List<AddNewTransaction>();

        foreach (var transaction in endOfSessionTransactions)
        {
            var addnewTransaction = new AddNewTransaction
            {
                LobbyingFirmName = transaction.FirmName,
                FirmHiringDate = transaction.DateLobbyingFirmHired,
                LegislativeNumbers = transaction.ActionsLobbied?
                    .Where(a => !string.IsNullOrWhiteSpace(a?.BillId?.ToString(CultureInfo.InvariantCulture)))
                    .Select(a => a?.BillId?.ToString(CultureInfo.InvariantCulture))
                    .ToList(),
                Amount = transaction.Amount
            };

            if (transaction.Contact is not null)
            {
                var contact = await filerContactRepository.GetFilerContactById(transaction.Contact.Id);
                var address = contact?.AddressList?.Addresses.FirstOrDefault();

                addnewTransaction.Address1 = new AddNewTransactionAddress
                {
                    City = address?.City,
                    Street = address?.Street,
                    Street2 = address?.Street2,
                    Zip = address?.Zip,
                    Type = address?.Type,
                    Country = address?.Country,
                    State = address?.State,
                    IsMailingAddress = false
                };

                addnewTransaction.PhoneNumber = contact != null
                    ? FormatFilerContactDecisionsPhoneNumber(contact, "Business")
                    : string.Empty;
            }

            addNewTransactions.Add(addnewTransaction);
        }

        return new DecisionsSubmitReport48H
        {
            AmendmentExplanation = new() {
                AmendmentExplanation = filing.AmendmentExplanation
            },
            AddNewTransaction = new()
            {
                AddNewTransaction = addNewTransactions
            }
        };
    }

    /// <inheritdoc />
    public async Task<SubmitLobbyingReportResponse> SendForAttestationReport48H(long filingId,
        Report48HSendForAttestationRequest request)
    {
        bool isValid = false;

        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is not Report48H report48H)
        {
            throw new KeyNotFoundException($"48H Report not found Id={filingId}");
        }

        var decisionInput = await HandleReport48HToDecisionInput(filing);

        var workflow = (filing.Version > 0) ? DecisionsWorkflow.FDLOBFiling48HourAmendReportSendForAttestation : DecisionsWorkflow.FDLOBFiling48HourReportSendForAttestation;

        var decisionResponse =
            await servicesDependencies.DecisionsSvc
                .InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
                    workflow, decisionInput, true);

        var decisionErrors = decisionResponse.Errors;

        if (decisionErrors.Count == 0)
        {
            isValid = true;
            report48H.StatusId = FilingStatus.Pending.Id;
            await dependencies.FilingRepository.Update(report48H);
            IDictionary<string, string>? notificationData = new Dictionary<string, string>
            {
                [Status] = FilingStatus.Pending.Name
            };
            await SendMultipleUsersNotifications(servicesDependencies, decisionResponse.Notifications,
                request.SelectedResponsibleOfficerIds, filing.FilerId, notificationData);
        }

        return new SubmitLobbyingReportResponse(filingId, isValid, decisionErrors, report48H);
    }

    /// <inheritdoc />
    public async Task<FilerUserDto> AddAssociatedFilerUserByFiling(long filingId, AddAssociatedFilerUserByFilingRequest byFilingRequest)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is null)
        {
            throw new KeyNotFoundException($"Filing not found with Id={filingId}");
        }

        var filerId = filing.FilerId;
        var filerUser = await servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(filerId, byFilingRequest.UserId);

        // Add new FilerUser if it doesn't exist
        if (filerUser is null)
        {
            var newFilerUser = new FilerUser()
            {
                FilerId = filing.FilerId,
                UserId = byFilingRequest.UserId,
                FilerRoleId = byFilingRequest.FilerRoleId
            };

            await servicesDependencies.FilerSvc.AddFilerUserAsync(newFilerUser);
        }
        else
        {
            // Update existing FilerUser role
            await servicesDependencies.FilerSvc.UpdateFilerUserRoleAsync(filerUser.Id, byFilingRequest.FilerRoleId);
        }

        return FilerUserDto.MapToDto(new FilerUser()
        {
            FilerId = filerId,
            UserId = byFilingRequest.UserId,
            FilerRoleId = byFilingRequest.FilerRoleId
        });
    }

    #endregion

    /// <inheritdoc />
    public async Task<IEnumerable<ReponsibleOfficerDto>> GetResponsibleOfficers(long filingId)
    {
        var filing = await dependencies.FilingRepository.GetFilingById(filingId) ?? throw new KeyNotFoundException($"Disclosure Filing Report not found Id={filingId}");
        var reportType = filing.FilingType!.Name;
        var associatedUsers = await filerUserRepository.FindFilerUsersByFilerId(filing.FilerId);
        var authorizedRoleNames = reportType switch
        {
            var r when r == FilingType.Report72h.Name => new List<string>
            {
                FilerRole.Disclosure_Lobbying72H_Attest_AccountManager.Name,
                FilerRole.Disclosure_Lobbying72H_Attest_ResponsibleOfficer.Name,
            },
            var r when r == FilingType.Report48h.Name => new List<string>
            {
                FilerRole.Disclosure_Lobbying48H_Attest_AccountManager.Name,
                FilerRole.Disclosure_Lobbying48H_Attest_ResponsibleOfficer.Name,
            },
            var r when r == FilingType.LobbyistEmployerReport.Name => new List<string>
            {
                FilerRole.Disclosure_LobbyistEmployer_Attest_AccountManager.Name,
                FilerRole.Disclosure_LobbyistEmployer_Attest_ResponsibleOfficer.Name,
            },
            _ => new List<string>()
        };

        return [.. associatedUsers
            .Where(x => x.FilerRole != null && authorizedRoleNames.Contains(x.FilerRole.Name))
            .Select(x => new ReponsibleOfficerDto() {
                Id = x.UserId,
                FirstName = x.User?.FirstName,
                LastName = x.User?.LastName,
                Role = x.FilerRole!.Name,
                Title = x.FilerRole.Name,
        })];
    }

    /// <inheritdoc />
    public async Task<IEnumerable<FilerSearchDto>> SearchFilers(string query, long filerTypeId)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return new List<FilerSearchDto>();
        }

        var filers = await dependencies.FilerRepository.SearchFilers(query, filerTypeId);

        var result = filers
            .Where(f => f.CurrentRegistration != null && f.FilerType != null)
            .Select((f, index) => new FilerSearchDto
            {
                Id = index + 1, // Sequential ID for AutoComplete component
                Name = f.CurrentRegistration!.Name,
                FilerId = f.Id,
                FilerType = f.FilerType!.Name,
                FilerTypeId = f.FilerTypeId
            })
            .ToList();

        return result;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<FilerSearchDto>> GetRelatedFilersByFilingId(long filingId)
    {
        var relatedFilers = await dependencies.FilingRelatedFilerRepository.GetRelatedFilersByFilingId(filingId);

        var result = relatedFilers
            .Where(rf => rf.Filer?.CurrentRegistration != null && rf.Filer.FilerType != null)
            .Select((rf, index) => new FilerSearchDto
            {
                Id = index + 1, // Sequential ID for AutoComplete component
                Name = rf.Filer!.CurrentRegistration!.Name,
                FilerId = rf.Filer.Id,
                FilerType = rf.Filer.FilerType!.Name,
                FilerTypeId = rf.Filer.FilerTypeId
            })
            .ToList();

        return result;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<FilerUserDto>> GetFilersAssociatedUserWithFilingType(long userId, long filingTypeId)
    {
        var filers = await filerUserRepository.FindFilerUsersByUserId(userId) ?? throw new KeyNotFoundException("Not found Filers associated with User");

        var filingType = await dependencies.FilingTypeRepository.FindById(filingTypeId) ?? throw new KeyNotFoundException("Not found Filing Type.");
        IEnumerable<FilerUserDto> result;

        if (filingType.Name == FilingType.LobbyistReport.Name)
        {
            result = filers
                .Where(f => f.Filer!.FilerTypeId == FilerType.Lobbyist.Id)
                .Select(FilerUserDto.MapToDto);
        }
        else
        {
            result = filers
                .Where(f => f.Filer!.FilerTypeId == FilerType.LobbyistEmployer.Id)
                .Select(FilerUserDto.MapToDto);
        }

        return result;
    }

    /// <inheritdoc />
    public Task<IEnumerable<Filing?>> GetAllFilingsByFilerType(long filerTypeId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task<IEnumerable<Filing?>> GetAllFilingsByFilerStatus(long filerStatusId)
    {
        throw new NotImplementedException();
    }


    /// <inheritdoc />
    public Task<IEnumerable<Filing?>> GetAllLateFilings(long filerStatusId)
    {
        throw new NotImplementedException();
    }

    public async Task<FilingResponseDto> UpdateFilingCustomPeriod(long id, UpdateFilingCustomPeriodRequest request)
    {
        // Get the filing to update
        var filing = await GetFilingById(id) ?? throw new KeyNotFoundException($"Filing not found. Id={id}");
        var filerId = filing.FilerId;

        // Get all filings for this filer
        var filingsOfFiler = await dependencies.FilingRepository.GetAllByFilerId(filerId);
        var allFilingPeriods = await GetAllFilingPeriodsForFiler(filerId, filing.FilingTypeId);

        if (request.FilingPeriodId != null)
        {
            var getDateRange = allFilingPeriods.FirstOrDefault(fp => fp.Id == request.FilingPeriodId.Value);
            if (getDateRange == null)
            {
                throw new KeyNotFoundException($"Filing period not found. Id={request.FilingPeriodId}");
            }
            filing.StartDate = getDateRange.StartDate;
            filing.EndDate = getDateRange.EndDate;
            filing.FilingPeriodId = getDateRange.Id;
            await dependencies.FilingRepository.Update(filing);

            return new FilingResponseDto(filing);
        }

        // Validate date range
        if (request.EndDate <= request.StartDate)
        {
            throw new ArgumentException("End date must be after start date.");
        }

        // Check for overlaps with other filings of the same type (excluding the current filing)
        if (filingsOfFiler.Any())
        {
            filingsOfFiler = filingsOfFiler
                .Where(f => f.Id != filing.Id && f.FilingTypeId == filing.FilingTypeId);
        }
        var overlappingFiling = FindDateRangeOverlappedByCreatedReports(filingsOfFiler, request.StartDate, request.EndDate);

        if (overlappingFiling != null)
        {
            throw new ArgumentException(
                $"The requested date range ({request.StartDate:d} - {request.EndDate:d}) overlaps with " +
                $"Filing: {overlappingFiling.Id} ({overlappingFiling.StartDate:d} - {overlappingFiling.EndDate:d}).");
        }

        filing.StartDate = request.StartDate ?? dateTimeSvc.GetCurrentDateTime();
        filing.EndDate = request.EndDate ?? dateTimeSvc.GetCurrentDateTime();
        filing.FilingPeriodId = request.FilingPeriodId;
        await dependencies.FilingRepository.Update(filing);

        return new FilingResponseDto(filing);
    }

    public async Task<UpdateAmendmentExplanationResponse> UpdateAmendmentExplanation(long id, UpdateAmendmentExplanationRequest request)
    {
        var filing = await dependencies.FilingRepository.FindById(id)
            ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        if (filing.Version == 0)
        {
            throw new InvalidOperationException("Only filing amendments have amendment explanations");
        }

        DecisionsWorkflow? workflowId = filing switch
        {
            Report48H => DecisionsWorkflow.FDLOBLobbyistEmployer5000FirmAmendReport02AmendmentExplanation,
            LobbyistEmployerReport => DecisionsWorkflow.FDLOBLobbyistEmployerAmendReportAmendmentExplanation,
            _ => null
        };

        if (workflowId is null)
        {
            await dependencies.FilingRepository.Update(filing);
            return new() { FilingId = filing.Id, Valid = true };
        }

        bool isValid;
        List<WorkFlowError> errors;

        if (filing is Report48H)
        {
            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<UpdateAmendmentExplanationRequest, List<WorkFlowError>>(workflowId.Value, request, true);
            isValid = decisionResponse.Count == 0;
            errors = [.. decisionResponse.Where(e => !string.IsNullOrWhiteSpace(e.Message))];
        }
        else
        {
            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<UpdateAmendmentExplanationRequest, StandardDecisionsSubmissionResponse>(workflowId.Value, request, true);
            isValid = decisionResponse.Errors.Count == 0;
            errors = decisionResponse.Errors;
        }

        if (isValid)
        {
            filing.AmendmentExplanation = request.AmendmentExplanation;
            await dependencies.FilingRepository.Update(filing);
        }

        return new() { FilingId = filing.Id, Valid = isValid, ValidationErrors = errors };
    }

    #region Private

    private static void CheckFilingPeriodRangeValid(Filing filing, List<WorkFlowError> decisionErrors)
    {
        if (filing.StartDate == DateTime.MinValue || filing.EndDate == DateTime.MinValue)
        {
            decisionErrors.Add(new WorkFlowError(FieldName: nameof(filing.FilingPeriodId),
                ErrorCode: DecisionConstants.ValidationError.ErrorCodeGlobal0001,
                ErrorType: DecisionConstants.ValidationError.ErrorTypeValidation,
                Message: "Start date and End date of Report are invalid!"));
        }
    }

    private static Filing? FindDateRangeOverlappedByCreatedReports(IEnumerable<Filing> filingsOfFilerByType, DateTime? startDate, DateTime? endDate)
    {
        if (startDate == null || endDate == null)
        {
            return null;
        }

        return filingsOfFilerByType
            .FirstOrDefault(f =>
                // StartDate falls within an existing range
                (startDate >= f.StartDate && startDate <= f.EndDate)
                ||
                // EndDate falls within an existing range
                (endDate >= f.StartDate && endDate <= f.EndDate)
                ||
                // New range completely encompasses an existing range
                (startDate <= f.StartDate && endDate >= f.EndDate)
                ||
                // New range is completely within an existing range
                (startDate >= f.StartDate && endDate <= f.EndDate));
    }

    private async Task ProcessTransactionsForAmendment(long newFilingId, List<Transaction> transactions)
    {
        foreach (var transaction in transactions)
        {
            try
            {
                var cloned = CloneTransaction(transaction);
                var newTransactionId = await transactionRepository.Create(cloned);

                if (newTransactionId?.Id > 0)
                {
                    await transactionRepository.AddTransactionToFiling(newTransactionId.Id, newFilingId);
                }
                else
                {
                    Console.WriteLine($"Failed to create transaction for filing amendment {newFilingId}");
                    Console.WriteLine($"Transaction data: {JsonConvert.SerializeObject(cloned)}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error cloning transaction for filing amendment {newFilingId}");
                Console.WriteLine($"Exception: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }
    }

    private static Transaction CloneTransaction(Transaction original)
    {
        var settings = new JsonSerializerSettings
        {
            TypeNameHandling = TypeNameHandling.All,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            ContractResolver = new DefaultContractResolver
            {
                IgnoreSerializableAttribute = true
            }
        };

        var json = JsonConvert.SerializeObject(original, settings);
        var clone = JsonConvert.DeserializeObject<Transaction>(json, settings)!;
        clone.Id = 0;
        if (original.Contact != null)
        {
            var newContact = CloneFilerContact(original.Contact);
            clone.Contact = newContact;
        }
        clone.ActionsLobbied = CloneActionsLobbied(clone.ActionsLobbied);
        clone.TransactionReportablePersons = CloneTransactionReportablePeople(clone.TransactionReportablePersons);
        clone.FilingTransactions.Clear();
        return clone;
    }

    private static FilerContact CloneFilerContact(FilerContact original)
    {
        var settings = new JsonSerializerSettings
        {
            TypeNameHandling = TypeNameHandling.All,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            ContractResolver = new DefaultContractResolver
            {
                IgnoreSerializableAttribute = true
            }
        };


        var json = JsonConvert.SerializeObject(original, settings);
        var clone = JsonConvert.DeserializeObject<FilerContact>(json, settings)!;
        clone.Id = 0;
        ClonePhoneNumberList(clone.PhoneNumberList);
        CloneAddressList(clone.AddressList);
        return clone;
    }

    private static void CloneAddressList(AddressList? addressList)
    {
        if (addressList is null)
        {
            return;
        }

        addressList.Id = 0;
        foreach (var addr in addressList.Addresses ?? [])
        {
            addr.Id = 0;
        }
    }

    private static void ClonePhoneNumberList(PhoneNumberList? phoneNumberList)
    {
        if (phoneNumberList is null)
        {
            return;
        }

        phoneNumberList.Id = 0;
        foreach (var phone in phoneNumberList.PhoneNumbers ?? [])
        {
            phone.Id = 0;
        }
    }

    private static List<ActionsLobbied?> CloneActionsLobbied(ICollection<ActionsLobbied?> original)
    {
        if (original == null)
        {
            return new List<ActionsLobbied?>();
        }

        var settings = new JsonSerializerSettings
        {
            TypeNameHandling = TypeNameHandling.All,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            ContractResolver = new DefaultContractResolver
            {
                IgnoreSerializableAttribute = true
            }
        };

        var json = JsonConvert.SerializeObject(original, settings);
        var clone = JsonConvert.DeserializeObject<List<ActionsLobbied?>>(json, settings);

        if (clone != null)
        {
            foreach (var actionLobbied in clone)
            {
                actionLobbied!.Id = 0;
                actionLobbied!.TransactionId = null;
                actionLobbied!.FilingId = null;
            }
        }

        return clone ?? new List<ActionsLobbied?>();
    }

    private static List<FilingRelatedFiler> CloneFilingRelatedFilers(ICollection<FilingRelatedFiler> original)
    {
        if (original == null || original.Count == 0)
        {
            return new List<FilingRelatedFiler>();
        }

        var settings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            PreserveReferencesHandling = PreserveReferencesHandling.None,
            TypeNameHandling = TypeNameHandling.All
        };

        var json = JsonConvert.SerializeObject(original, settings);
        var clone = JsonConvert.DeserializeObject<List<FilingRelatedFiler>>(json, settings)!;

        foreach (var relatedFiler in clone)
        {
            relatedFiler.Id = 0;
            relatedFiler.FilingId = 0;
            relatedFiler.Filing = null;
        }

        return clone;
    }

    private static List<TransactionReportablePerson>? CloneTransactionReportablePeople(List<TransactionReportablePerson>? original)
    {
        if (original == null)
        {
            return null;
        }

        var settings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            PreserveReferencesHandling = PreserveReferencesHandling.None,
            TypeNameHandling = TypeNameHandling.All
        };

        var json = JsonConvert.SerializeObject(original, settings);
        var clone = JsonConvert.DeserializeObject<List<TransactionReportablePerson>>(json, settings);

        clone?.ForEach(person =>
        {
            person.Id = 0;
            person.TransactionId = 0;
        });

        return clone;
    }

    private static List<FilingSummary> CloneFilingSummaries(ICollection<FilingSummary> original)
    {
        if (original == null || original.Count == 0)
        {
            return new List<FilingSummary>();
        }

        var settings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            PreserveReferencesHandling = PreserveReferencesHandling.None,
            TypeNameHandling = TypeNameHandling.All
        };

        var json = JsonConvert.SerializeObject(original, settings);
        var clone = JsonConvert.DeserializeObject<List<FilingSummary>>(json, settings)!;

        foreach (var summary in clone)
        {
            summary.Id = 0;
            summary.FilingId = 0;
            summary.Filing = null;

            if (summary.NonRegisteredLobbyists != null && summary.NonRegisteredLobbyists.Count > 0)
            {
                foreach (var lobbyist in summary.NonRegisteredLobbyists)
                {
                    lobbyist.Id = 0;
                    lobbyist.FilingSummaryId = 0;
                }
            }
        }

        // Remove amendment explanation from the list of filing summaries to clone
        var amendmentExplanationSummary = clone.FirstOrDefault(x => x.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id);
        if (amendmentExplanationSummary != null)
        {
            clone.Remove(amendmentExplanationSummary);
        }

        return clone;
    }

    private static void AddAmendmentExplanationFilingSummary(List<FilingSummary> filingSummaries)
    {
        var amendmentFs = new FilingSummary
        {
            Id = 0,
            FilingId = 0,
            FilingSummaryTypeId = FilingSummaryType.AmendmentExplanation.Id,
            FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
            PeriodAmount = 0m,
            ToDateAmount = 0m,
        };

        filingSummaries.Add(amendmentFs);
    }

    private async Task<Filing> ValidateAndGetLobbyistFilingForAmendment(long id)
    {
        var filing = await dependencies.FilingRepository.FindEntireFilingById(id)
            ?? throw new KeyNotFoundException($"Filing not found. Id={id}");

        if (filing.StatusId != FilingStatus.Accepted.Id && filing.StatusId != FilingStatus.Incomplete.Id)
        {
            throw new InvalidOperationException("Only Accepted filings can be amended.");
        }

        return filing;
    }
    private async Task<Filing?> GetExistingAmendment(Filing filing)
    {
        return await dependencies.FilingRepository.FindExistingFilingAmendment(
            filing.Id,
            FilingStatus.Draft.Id);
    }

    private static void ResolveSummaryTypeForPaymentMadeByAgent(ref long? summaryTypeId, Transaction transaction)
    {
        if (transaction.TypeId != TransactionType.PaymentMade.Id)
        {
            return;
        }

        var paymentMade = transaction as PaymentMade;
        if (paymentMade is not null && !string.IsNullOrWhiteSpace(paymentMade.AgentOrIndependentContractorName))
        {
            summaryTypeId = FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id;
        }
    }


    // TODO: refactor once we align country code and number in db to team standard
    private static string FormatFilerContactDecisionsPhoneNumber(FilerContact filerContact, string type)
    {
        var result = string.Empty;

        var phone = filerContact?.PhoneNumberList?.PhoneNumbers?.FirstOrDefault(x => x.Type == type);
        if (phone != null && !string.IsNullOrWhiteSpace(phone.Number) && !string.IsNullOrWhiteSpace(phone.CountryCode))
        {
            var countryCode = phone.CountryCode.Split('-')[0];
            var number = phone.Number;

            result = countryCode + number;
        }
        return result;
    }

    private async Task UpdateFilingContactSummaryIfApplicableAsync(long filingId, Transaction transaction, decimal varianceAmount, long? originalContactId)
    {
        if (transaction is not PersonReceiving1000OrMore)
        {
            return;
        }

        // If the contact is change, we need to exclude the amount from the previous contact summary
        var isChangingContact = originalContactId.HasValue && originalContactId != transaction.ContactId;
        if (isChangingContact)
        {
            var originalAmount = transaction.Amount - varianceAmount;
            await AdjustPreviousContactSummaryAsync(filingId, originalContactId.GetValueOrDefault(), originalAmount);
        }

        // If the contact is change, we include the full amount of the transaction into the new summary
        // If not, we only need to add the variance amount
        var amountToAdjust = isChangingContact ? transaction.Amount : varianceAmount;
        var contactSummary = await dependencies.FilingContactSummaryRepository.FindByFilingIdAndContactId(filingId, transaction.ContactId.GetValueOrDefault());
        await UpsertContactSummaryAsync(filingId, contactSummary, transaction, amountToAdjust);
    }

    private async Task<decimal> GetCurrentCumulativeAmountAsync(long filingId, long contactId, long transactionTypeId)
    {
        var filing = await dependencies.FilingRepository.FindById(filingId);
        if (filing is null)
        {
            return 0m;
        }

        var previousStatementIds = (await dependencies.FilingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(filing.FilerId, filing.StartDate)).Select(x => x.Id).ToList();
        return await transactionRepository.SumTransactionAmountsByFilingsAndContact(previousStatementIds, contactId, transactionTypeId);
    }

    private async Task UpsertContactSummaryAsync(long filingId, FilingContactSummary? contactSummary, Transaction transaction, decimal varianceAmount)
    {
        var contactId = transaction.ContactId.GetValueOrDefault();
        // If the contact summary has not been created yet, create a new one
        if (contactSummary is null)
        {
            var currCumulativeAmount = await GetCurrentCumulativeAmountAsync(filingId, contactId, transaction.TypeId);
            contactSummary = new FilingContactSummary
            {
                Amount = currCumulativeAmount + Math.Max(varianceAmount, -currCumulativeAmount),
                FilerContactId = contactId,
                DisclosureFilingId = filingId,
                FilingContactSummaryTypeId = FilingContactSummaryType.PersonReceiving1000OrMore.Id,
            };
            await dependencies.FilingContactSummaryRepository.Create(contactSummary);
        }
        else
        {
            // Otherwise, update the existing one
            contactSummary.Amount += Math.Max(varianceAmount, -contactSummary.Amount);
            await dependencies.FilingContactSummaryRepository.Update(contactSummary);
        }
    }

    private async Task AdjustPreviousContactSummaryAsync(long filingId, long originalContactId, decimal amount)
    {
        var oldContactSummary = await dependencies.FilingContactSummaryRepository.FindByFilingIdAndContactId(filingId, originalContactId);
        if (oldContactSummary is null)
        {
            return;
        }

        oldContactSummary.Amount -= Math.Min(amount, oldContactSummary.Amount);
        await dependencies.FilingContactSummaryRepository.Update(oldContactSummary);
    }
    #endregion
}
