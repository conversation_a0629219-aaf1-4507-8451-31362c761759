using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SOS.CalAccess.Models.FilerRegistration.Filers;

namespace SOS.CalAccess.Data.EntityFramework.Configurations;
public sealed class FilerLinkTypeConfiguration : IEntityTypeConfiguration<FilerLink>
{
    /// <inheritdoc />
    public void Configure(EntityTypeBuilder<FilerLink> builder)
    {
        builder.ToTable(nameof(FilerLink), t => t.IsTemporal());

        builder.HasOne(f => f.Filer)
            .WithMany(f => f.FilerLinks!)
            .HasForeignKey(f => f.FilerId);

        builder.Property(x => x.Active)
            .HasDefaultValue(true);
    }
}
