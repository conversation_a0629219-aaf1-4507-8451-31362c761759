using System.Net;
using System.Text;
using System.Text.Json;
using Azure.Messaging.ServiceBus;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using SOS.CalAccess.SmsMessage.Processing;
using SOS.CalAccess.SmsMessage.Processing.Service;
using Twilio.Rest.Api.V2010.Account;

namespace SOS.CalAccess.MessageProcessing.Tests;

public class Tests
{
    [TestFixture]
    public class MessageProcessingFunctionTests
    {
        private Mock<ILogger<SmsMessageProcessingFunction>> _mockLogger;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IHttpClientFactory> _mockHttpClientFactory;
        private Mock<ServiceBusMessageActions> _mockMessageActions;
        private Mock<ITwilioService> _mockTwilioService;
        private HttpClient _httpClient;
        private Mock<MessageResource> _mockTwilioMessage;
        private SmsMessageProcessingFunction _function;
        private DateTime _dateNow;

        [TearDown]
        public void Teardown()
        {
            _httpClient?.Dispose();
        }

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<SmsMessageProcessingFunction>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockHttpClientFactory = new Mock<IHttpClientFactory>();
            _mockMessageActions = new Mock<ServiceBusMessageActions>();
            _mockTwilioService = new Mock<ITwilioService>();
            _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

            // Mock HTTP Client for API requests
            var mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.OK));

            _httpClient = new HttpClient(mockHttpMessageHandler.Object);
            _mockHttpClientFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(_httpClient);

            _mockConfiguration.Setup(c => c["AccountSid"]).Returns("test_sid");
            _mockConfiguration.Setup(c => c["AuthToken"]).Returns("test_token");
            _mockConfiguration.Setup(c => c["ApiBaseUrl"]).Returns("http://mockapi.com/");

            // Mock Twilio API Call
            _mockTwilioService
            .Setup(m => m.SendMessage(It.IsAny<CreateMessageOptions>()))
            .Returns(CreateFakeMessageResource());


            // Inject the mocked Twilio service into the function
            _function = new SmsMessageProcessingFunction(
                _mockLogger.Object,
                _mockConfiguration.Object,
                _mockHttpClientFactory.Object,
                _mockTwilioService.Object);
        }

        private ServiceBusReceivedMessage CreateTestMessage(Models.SmsMessaging.SmsMessage smsMessage)
        {
            var messageBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(smsMessage));
            return ServiceBusModelFactory.ServiceBusReceivedMessage(
                body: new BinaryData(messageBody),
                messageId: Guid.NewGuid().ToString(),
                contentType: "application/json"
            );
        }

        private MessageResource CreateFakeMessageResource()
        {
            // Get the non-public constructor of MessageResource
            var constructor = typeof(MessageResource)
                .GetConstructor(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance,
                                null,
                                new Type[0],
                                null);

            if (constructor == null)
            {
                throw new InvalidOperationException("Could not find a parameterless constructor for MessageResource.");
            }

            // Create an instance of MessageResource
            var fakeMessage = (MessageResource)constructor.Invoke(null);

            // Use reflection to set read-only properties
            typeof(MessageResource).GetProperty(nameof(MessageResource.Sid))?.SetValue(fakeMessage, "MockSid");
            typeof(MessageResource).GetProperty(nameof(MessageResource.MessagingServiceSid))?.SetValue(fakeMessage, "MockServiceSid");
            typeof(MessageResource).GetProperty(nameof(MessageResource.AccountSid))?.SetValue(fakeMessage, "MockAccountSid");
            typeof(MessageResource).GetProperty(nameof(MessageResource.Status))?.SetValue(fakeMessage, MessageResource.StatusEnum.Sent);
            typeof(MessageResource).GetProperty(nameof(MessageResource.DateSent))?.SetValue(fakeMessage, _dateNow);
            typeof(MessageResource).GetProperty(nameof(MessageResource.DateUpdated))?.SetValue(fakeMessage, _dateNow);

            return fakeMessage;
        }


        [Test]
        public async Task Run_ValidMessage_ShouldProcessSuccessfully()
        {
            var smsMessage = new Models.SmsMessaging.SmsMessage
            {
                Id = 1,
                FromPhoneNumber = "+**********",
                ToPhoneNumber = "+**********",
                Content = "Test SMS"
            };

            var message = CreateTestMessage(smsMessage);

            await _function.Run(message, _mockMessageActions.Object);

            _mockMessageActions.Verify(m => m.CompleteMessageAsync(message, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Test]
        public async Task Run_InvalidMessage_ShouldDeadLetter()
        {
            var smsMessage = new Models.SmsMessaging.SmsMessage
            {
                Id = 1,
                FromPhoneNumber = "",
                ToPhoneNumber = "+**********",
                Content = "Test SMS"
            };

            var message = CreateTestMessage(smsMessage);

            await _function.Run(message, _mockMessageActions.Object);

            _mockMessageActions.Verify(m => m.DeadLetterMessageAsync(
                message,
                It.IsAny<Dictionary<string, object>>(),
                "InvalidFormat",
                It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
        }

        [Test]
        public async Task Run_DeserializationError_ShouldDeadLetter()
        {
            var invalidJson = Encoding.UTF8.GetBytes("{ invalid json }");
            var message = ServiceBusModelFactory.ServiceBusReceivedMessage(
                body: new BinaryData(invalidJson),
                messageId: Guid.NewGuid().ToString(),
                contentType: "application/json"
            );

            await _function.Run(message, _mockMessageActions.Object);

            _mockMessageActions.Verify(m => m.DeadLetterMessageAsync(
                message,
                It.IsAny<Dictionary<string, object>>(),
                "DeserializationError",
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()
            ), Times.Once);
        }

        [Test]
        public async Task Run_TwilioSendFailure_ShouldAbandonMessage()
        {
            var smsMessage = new Models.SmsMessaging.SmsMessage
            {
                Id = 1,
                FromPhoneNumber = "+**********",
                ToPhoneNumber = "+**********",
                Content = "Test SMS"
            };

            _mockConfiguration.Setup(c => c["AccountSid"]).Throws(new Exception("Twilio error"));

            var message = CreateTestMessage(smsMessage);

            Assert.ThrowsAsync<Exception>(async () => await _function.Run(message, _mockMessageActions.Object));

            _mockMessageActions.Verify(m => m.AbandonMessageAsync(message, It.IsAny<Dictionary<string, object>>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Test]
        public async Task Run_UpdateSmsStatusFailure_ShouldDeadLetter()
        {
            var smsMessage = new Models.SmsMessaging.SmsMessage
            {
                Id = 1,
                FromPhoneNumber = "+**********",
                ToPhoneNumber = "+**********",
                Content = "Test SMS"
            };

            var message = CreateTestMessage(smsMessage);

            var mockHttpMessageHandler = new Mock<HttpMessageHandler>();

            mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.InternalServerError));

            var failingHttpClient = new HttpClient(mockHttpMessageHandler.Object);
            _mockHttpClientFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(failingHttpClient);

            var function = new SmsMessageProcessingFunction(_mockLogger.Object, _mockConfiguration.Object, _mockHttpClientFactory.Object, _mockTwilioService.Object);

            await function.Run(message, _mockMessageActions.Object);

            // Verify that the message was dead-lettered due to API failure
            _mockMessageActions.Verify(m => m.DeadLetterMessageAsync(
                message,
                It.IsAny<Dictionary<string, object>>(),
                "DatabaseUpdateError",
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

    }
}
