using System.Globalization;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.Notification;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Notifications;
public partial class NotificationMessageRepository(DatabaseContext dbContext, IDateTimeSvc dateTimeSvc,
    ILogger<NotificationMessageRepository> logger) : Repository<NotificationMessage, long>(dbContext), INotificationMessageRepository
{
    public async Task<int> CountActiveUserNotificationsByUser(string username)
    {
        var query = QueryMessagesForUser(username);
        return await query.CountAsync();
    }

    public async Task<int> CountNotificationsByUser(string username)
    {
        var query = QueryMessagesForUser(username);
        return await query.CountAsync();
    }

    public async Task<int> CountNotificationsByUserAndFilerTypeId(string username, long filerTypeId)
    {
        var query = filerTypeId == 0
        ? QueryMessagesForUser(username)
        : QueryMessagesForUserAndFilerType(username, filerTypeId);

        return await query.CountAsync();

    }

    /// <summary>
    /// Gets the count of unresolved notifications for a user
    /// </summary>
    /// <param name="username">Username of the user</param>
    /// <returns></returns>
    public Task<int> CountUnresolvedNotificationsByUser(string username)
    {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
#pragma warning disable CA1304 // Specify CultureInfo
#pragma warning disable CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning disable CA1311 // Specify a culture or use an invariant version
        var query = dbSet.Where(x => x.User.UserName.ToLower() == username.ToLower() && x.DeletedAt == null
                                && (
                                    x.ViewedAt == null ||
                                    (x.NotificationTemplate != null && x.NotificationTemplate.IsActionRequired && x.ResolvedAt == null)
                                ));
#pragma warning restore CA1311 // Specify a culture or use an invariant version
#pragma warning restore CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning restore CA1304 // Specify CultureInfo
#pragma warning restore CS8602 // Dereference of a possibly null reference.
        return query.CountAsync();
    }
    public override async Task<NotificationMessage?> FindById(long id)
    {
        return await dbSet.Include(m => m.NotificationTemplate)
            .FirstOrDefaultAsync(m => m.Id == id);
    }

    public async Task<NotificationMessageDto?> FindMessageById(long notificationId)
    {
        var message = await dbSet
            .Include(x => x.NotificationTemplate)
            .ThenInclude(x => x!.FilerType)
            .Include(x => x.NotificationTemplate)
            .ThenInclude(x => x!.NotificationType)
            .Include(x => x.NotificationTemplate)
            .ThenInclude(x => x!.Translations)
            .FirstOrDefaultAsync(x => x.Id == notificationId);

        return message is not null ? ConvertNotificationMessageToDto(message) : null;
    }

    /// <summary>
    /// Find all Active Notifications by Template Id and Filer Id
    /// </summary>
    /// <param name="templateId"> Unique Id for Notification Template</param>
    /// <param name="filerId"> Unique Id for Filer</param>
    /// <returns>List of Notification Messages</returns>
    public Task<List<NotificationMessage>> FindAllActiveNotificationsByTemplateIdAndFilerId(long templateId, long filerId)
    {
        return dbSet.Where(message => message.NotificationTemplateId == templateId && message.FilerId == filerId && message.ResolvedAt == null && message.DeletedAt == null).ToListAsync();
    }

    /// <summary>
    /// Find all Active and Priority Notifications for a User Id
    /// </summary>
    /// <param name="userId">Unique Id for User</param>
    /// <returns>List of Notification Messages</returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task<List<NotificationMessage>> FindAllActivePriorityNotificationsByUser(long userId)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// Find all notifications by user id.
    /// </summary>
    /// <param name="userId">Unique User Id for whom the notifications need to be retrieved</param>
    /// <returns>List of Notification Messages</returns>
    public async Task<List<NotificationMessageDto>> FindAllNotificationsByUser(PagedUserDataRequest request)
    {
        var sortColumn = request.SortColumn ?? nameof(NotificationMessage.CreatedAt);

        var query = QueryMessagesForUser(request.Username);

        // First, fetch the data from the database
        var notifications = await AddSorting(query, sortColumn, request.SortDirection)
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync();

        return notifications.Select(ConvertNotificationMessageToDto).ToList();
    }

    /// <summary>
    /// Parses the template data Json
    /// </summary>
    /// <param name="templateData"> Json Template data as a string</param>
    /// <returns>Returns a key-value pair</returns>
    private static string ParseTemplateData(Dictionary<string, string> dictionary, string text)
    {
        foreach (var key in dictionary.Keys)
        {
            var pattern = "{{" + key + "}}";
            text = text.Replace(pattern, dictionary[key], StringComparison.CurrentCultureIgnoreCase);
        }

        return text;
    }

    /// <summary>
    /// Gets a combination of filer name and filer Id as a string
    /// </summary>
    /// <param name="filerName">Current Registration Name for Filer</param>
    /// <param name="filerId">Unique Id for Filer</param>
    /// <returns></returns>
    private static string GetFilerName(string? filerName, long? filerId)
    {
        if (filerId == null)
        {
            return string.Empty;
        }
        if (filerName == null)
        {
            return filerId.Value.ToString(CultureInfo.InvariantCulture);
        }
        return filerName.Length > 50
        ? filerName[..50] + "..." + $"({filerId})"
        : filerName + $"({filerId})";
    }

    public async Task<List<NotificationMessageDto>> FindAllNotificationsByUserAndFilerType(PagedUserDataRequest request, long filerTypeId)
    {
        var sortColumn = request.SortColumn ?? nameof(NotificationMessage.CreatedAt);

        var query = filerTypeId == 0
        ? QueryMessagesForUser(request.Username)
        : QueryMessagesForUserAndFilerType(request.Username, filerTypeId);

        var searchQuery = AddSearching(query, request.SearchFields, request.SearchValue);

        // First, fetch the data from the database
        var notifications = await AddSorting(searchQuery, sortColumn, request.SortDirection)
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync();

        return notifications.Select(ConvertNotificationMessageToDto).ToList();
    }

    private Dictionary<string, string> ConvertTemplateDataToDictionary(string templateData)
    {
        try
        {
            return JsonSerializer.Deserialize<Dictionary<string, string>>(templateData)!;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Could not parse TemplateData to dictionary");
            return new Dictionary<string, string>();
        }
    }

    public NotificationMessageDto ConvertNotificationMessageToDto(NotificationMessage message)
    {
        var dto = new NotificationMessageDto()
        {
            CreatedAt = message.CreatedAt,
            DueDate = message.DueDate,
            FilerId = message.FilerId,
            FilerTypeId = message.NotificationTemplate!.FilerTypeId,
            FilerTypeName = message.NotificationTemplate.FilerType is not null ? message.NotificationTemplate.FilerType!.Name : null,
            FilerName = GetFilerName(message.Filer?.CurrentRegistration?.Name, message.FilerId),
            Id = message.Id,
            IsActionRequired = message.NotificationTemplate.IsActionRequired && message.ResolvedAt == null && message.DeletedAt == null,
            IsPriorityMessage = message.NotificationTemplate.IsPriorityMessage,
            NotificationTypeId = message.NotificationTemplate.NotificationTypeId,
            NotificationTypeName = message.NotificationTemplate.NotificationType!.Name,
            ResolvedAt = message.ResolvedAt,
            UserId = message.UserId,
            ViewedAt = message.ViewedAt
        };

        if (message.TemplateData is null)
        {
            dto.Subject = message.NotificationTemplate.EnglishTranslation().Subject;
            dto.Message = message.NotificationTemplate.EnglishTranslation().Message;
        }
        else
        {
            var dictionary = ConvertTemplateDataToDictionary(message.TemplateData);
            dto.Subject = ParseTemplateData(dictionary, message.NotificationTemplate.EnglishTranslation().Subject);
            dto.Message = ParseTemplateData(dictionary, message.NotificationTemplate.EnglishTranslation().Message);
        }

        return dto;
    }

    /// <summary>
    /// Find all Unread Notifications for a User Id
    /// </summary>
    /// <param name="userId">Unique Id for User</param>
    /// <returns>List of Notification Messages</returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task<List<NotificationMessage>> FindAllUnreadNotificationsByUser(long userId)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// Find all notification messages based out of search criteria
    /// </summary>
    /// <param name="searchCriteria"></param>
    /// <returns>List of Notification Messages</returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task<List<NotificationMessage>> SearchNotifications(NotificationSearchCriteria searchCriteria)
    {
        throw new NotImplementedException();
    }


    private static IQueryable<NotificationMessage> AddSearching(IQueryable<NotificationMessage> query, List<string>? searchFields, string? searchKey)
    {
        if (string.IsNullOrWhiteSpace(searchKey) || searchFields == null || searchFields.Count == 0)
        {
            return query;
        }

        // Get all search terms
        var searchTerms = ParseSearchTerms(searchKey);

#pragma warning disable CA1308 // Normalize strings to uppercase
#pragma warning disable CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning disable CA1307 // Specify StringComparison for clarity
#pragma warning disable CA1304 // Specify CultureInfo
#pragma warning disable CA1311 // Specify a culture or use an invariant version
        query = query.Where(message =>
            searchTerms.Any(term =>
                (
                    searchFields.Contains("Subject") &&
                    message.NotificationTemplate!.Translations.Any(t => t.Subject.ToLower().Contains(term.ToLower())
                    )
                ) ||
                (
                    searchFields.Contains("Message") &&
                    message.NotificationTemplate!.Translations.Any(t => t.Message.ToLower().Contains(term.ToLower())
                    )
                )
            )
        );
#pragma warning restore CA1311 // Specify a culture or use an invariant version
#pragma warning restore CA1304 // Specify CultureInfo
#pragma warning restore CA1307 // Specify StringComparison for clarity
#pragma warning restore CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning restore CA1308 // Normalize strings to uppercase

        return query;
    }

    private static List<string> ParseSearchTerms(string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
        {
            return new List<string>();
        }

        var matches = NotificationGridRegex().Matches(searchText);
        var terms = matches
            .Select(m => m.Value.Trim('"').Trim())
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .ToList();

        return terms;
    }

    [GeneratedRegex(@"[\""].+?[\""]|\S+")]
    private static partial Regex NotificationGridRegex();

    private IQueryable<NotificationMessage> QueryMessagesForUser(string username)
    {
#pragma warning disable CA1304 // Specify CultureInfo
#pragma warning disable CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning disable CA1311 // Specify a culture or use an invariant version
        return IncludeNotificationRelations()
                    .Where(message => message != null
                        && message.DeletedAt == null
                        && message.User != null
                        && message.User.UserName.ToLower() == username.ToLower()
                        && message.FilerId == message.Filer!.Id
                        && message.NotificationTemplateId == message.NotificationTemplate!.Id);
#pragma warning restore CA1311 // Specify a culture or use an invariant version
#pragma warning restore CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning restore CA1304 // Specify CultureInfo
    }

    private IQueryable<NotificationMessage> IncludeNotificationRelations()
    {
        return dbSet
            .Include(message => message.Filer)!.ThenInclude(filer => filer!.CurrentRegistration)
            .Include(message => message.NotificationTemplate)!.ThenInclude(template => template!.FilerType)
            .Include(message => message.NotificationTemplate)!.ThenInclude(template => template!.Translations)
            .Include(message => message.NotificationTemplate)!.ThenInclude(template => template!.NotificationType);
    }

    private IQueryable<NotificationMessage> QueryMessagesForUserAndFilerType(string username, long filerTypeId)
    {
#pragma warning disable CA1304 // Specify CultureInfo
#pragma warning disable CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning disable CA1311 // Specify a culture or use an invariant version
        return IncludeNotificationRelations()
                    .Where(message => message != null
                        && message.DeletedAt == null
                        && message.User != null
                        && message.User.UserName.ToLower() == username.ToLower()
                        && message.NotificationTemplate!.FilerTypeId == filerTypeId
                        && message.FilerId == message.Filer!.Id
                        && message.NotificationTemplateId == message.NotificationTemplate!.Id
                        && message.NotificationTemplate.FilerTypeId == message.NotificationTemplate.FilerType!.Id);
#pragma warning restore CA1311 // Specify a culture or use an invariant version
#pragma warning restore CA1862 // Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
#pragma warning restore CA1304 // Specify CultureInfo
    }

    private static IQueryable<NotificationMessage> AddSorting(IQueryable<NotificationMessage> query, string propertyName, SortDirection direction)
    {
#pragma warning disable CS8603 // Possible null reference return.
#pragma warning disable CS8602 // Dereference of a possibly null reference.
        Expression<Func<NotificationMessage, object>> sortBy = propertyName switch
        {
            nameof(NotificationMessage.Id) => message => message.Id,
            nameof(NotificationMessage.DueDate) => message => message.DueDate,
            nameof(NotificationMessage.CreatedAt) => message => message.CreatedAt,
            nameof(NotificationMessage.ViewedAt) => message => message.ViewedAt,
            nameof(NotificationMessage.ResolvedAt) => message => message.ResolvedAt,
            nameof(NotificationMessage.FilerId) => message => message.FilerId,
            nameof(NotificationTemplate.IsPriorityMessage) => message => message.NotificationTemplate.IsPriorityMessage,
            nameof(NotificationTemplate.IsActionRequired) => message => message.NotificationTemplate.IsActionRequired,
            nameof(NotificationTemplateTranslation.Subject) => message => message.NotificationTemplate.Translations.FirstOrDefault(translation => translation.Locale == "en-US").Subject,
            nameof(NotificationMessageDto.FilerName) => message => message.Filer.CurrentRegistration.Name,
            nameof(NotificationMessageDto.NotificationTypeName) => message => message.NotificationTemplate.NotificationType!.Name,
            _ => throw new ArgumentException("Property not defined for sorting", nameof(propertyName)),
        };
#pragma warning restore CS8602 // Dereference of a possibly null reference.
#pragma warning restore CS8603 // Possible null reference return.

        return direction == SortDirection.Ascending
            ? query.OrderBy(sortBy)
            : query.OrderByDescending(sortBy);
    }

    public new async Task<int> Delete(NotificationMessage notification)
    {
        notification.DeletedAt = dateTimeSvc.GetCurrentDateTime();
        return await dbContext.SaveChangesAsync();
    }
}
