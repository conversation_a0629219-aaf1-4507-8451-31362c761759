using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;

namespace SOS.CalAccess.Services.WebApi.Shared;

#pragma warning disable S6931

[AllowAnonymous]
[ApiController]
[Route("/")]
[ApiConventionType(typeof(DefaultApiConventions))]
public class ReferenceDataController(IReferenceDataSvc referenceDataSvc) : ControllerBase, IReferenceDataSvc
{
    /// <summary>
    /// Get all filer types
    /// </summary>
    /// <returns></returns>
    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllFilerTypesPath, Name = nameof(GetAllFilerTypes))]
    public async Task<IEnumerable<FilerType>> GetAllFilerTypes()
    {
        return await referenceDataSvc.GetAllFilerTypes();
    }

    /// <summary>
    /// Get all filer types
    /// </summary>
    /// <returns></returns>
    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllFilerRolesPath, Name = nameof(GetAllFilerRoles))]
    public async Task<IEnumerable<FilerRoleDto>> GetAllFilerRoles()
    {
        return await referenceDataSvc.GetAllFilerRoles();
    }

    /// <summary>
    /// Get all notification types
    /// </summary>
    /// <returns></returns>
    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllNotificationTypesPath, Name = nameof(GetAllNotificationTypes))]
    public async Task<IEnumerable<NotificationType>> GetAllNotificationTypes()
    {
        return await referenceDataSvc.GetAllNotificationTypes();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllPoliticalPartiesPath, Name = nameof(GetAllPoliticalParties))]
    public async Task<IEnumerable<PoliticalParty>> GetAllPoliticalParties()
    {
        return await referenceDataSvc.GetAllPoliticalParties();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetOfficePath, Name = nameof(GetOffice))]
    public async Task<Office?> GetOffice(long officeId)
    {
        return await referenceDataSvc.GetOffice(officeId);
    }

    [HttpGet(IReferenceDataSvc.ListPermissionsPath, Name = nameof(ListPermissions))]
    public async Task<IEnumerable<MaintainPermissionsDto>> ListPermissions()
    {
        return await referenceDataSvc.ListPermissions();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllOfficialPositionsPath, Name = nameof(GetAllOfficialPositions))]
    public async Task<IEnumerable<OfficialPosition>> GetAllOfficialPositions()
    {
        return await referenceDataSvc.GetAllOfficialPositions();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllAgenciesPath, Name = nameof(GetAllAgencies))]
    public async Task<IEnumerable<Agency>> GetAllAgencies()
    {
        return await referenceDataSvc.GetAllAgencies();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.SearchAllAgenciesPath, Name = nameof(SearchAllAgencies))]
    public async Task<IEnumerable<Agency>> SearchAllAgencies([FromQuery] string query)
    {
        return await referenceDataSvc.SearchAllAgencies(query);
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllPaymentCodesPath, Name = nameof(GetAllPaymentCodes))]
    public async Task<IEnumerable<PaymentCodeRefResponse>> GetAllPaymentCodes()
    {
        return await referenceDataSvc.GetAllPaymentCodes();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllAdvertisementDistributionMethodsPath, Name = nameof(GetAllAdvertisementDistributionMethods))]
    public async Task<IEnumerable<AdvertisementDistributionMethodRefResponse>> GetAllAdvertisementDistributionMethods()
    {
        return await referenceDataSvc.GetAllAdvertisementDistributionMethods();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.SearchAllBillsPath, Name = nameof(SearchAllBills))]
    public async Task<IEnumerable<Bill>> SearchAllBills([FromQuery] string query)
    {
        return await referenceDataSvc.SearchAllBills(query);
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllCountryCodesPath, Name = nameof(GetAllCountryCodes))]
    public async Task<IEnumerable<Country>> GetAllCountryCodes()
    {
        return await referenceDataSvc.GetAllCountryCodes();
    }


    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetCountryByIdPath, Name = nameof(GetCountryById))]
    public async Task<Country?> GetCountryById(long id)
    {
        return await referenceDataSvc.GetCountryById(id);
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllExpenditureCodesAsyncPath, Name = nameof(GetAllExpenditureCodesAsync))]
    public async Task<IEnumerable<ExpenditureCode>> GetAllExpenditureCodesAsync()
    {
        return await referenceDataSvc.GetAllExpenditureCodesAsync();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllNatureAndInterestTypesAsyncPath, Name = nameof(GetAllNatureAndInterestTypesAsync))]
    public async Task<IEnumerable<NatureAndInterestType>> GetAllNatureAndInterestTypesAsync()
    {
        return await referenceDataSvc.GetAllNatureAndInterestTypesAsync();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllIndustryGroupClassificationTypesAsyncPath, Name = nameof(GetAllIndustryGroupClassificationTypesAsync))]
    public async Task<IEnumerable<IndustryGroupClassificationType>> GetAllIndustryGroupClassificationTypesAsync()
    {
        return await referenceDataSvc.GetAllIndustryGroupClassificationTypesAsync();
    }

    /// <inheritdoc/>
    [HttpGet(IReferenceDataSvc.GetAllBusinessSubcategoriesAsyncPath, Name = nameof(GetAllBusinessSubcategoriesAsync))]
    public async Task<IEnumerable<BusinessSubcategory>> GetAllBusinessSubcategoriesAsync()
    {
        return await referenceDataSvc.GetAllBusinessSubcategoriesAsync();
    }
}
