// <copyright file="ITerminateFilerRegistration.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>


using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Messaging;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filers;

/// <summary>
/// Command interface for terminating a filer registration.
/// </summary>
/// <remarks>
/// When a registration is terminated the <see cref="Registration.TerminatedAt"/> field is set to the current date and time,
/// the <see cref="Registration.StatusId"/> field is set to "Terminated".
/// </remarks>
public interface ITerminateFilerRegistration : ICommand<long, IResult<Registration>>;

/// <summary>
/// Command implementation for terminating an approved filer registration.
/// </summary>
/// <param name="db">Database context.</param>
/// <param name="messaging">External communications handling.</param>
/// <param name="logger">Logging facilities.</param>
public sealed class TerminateFilerRegistration(
    DatabaseContext db,
    IMessagingHub messaging,
    IDateTimeSvc dateTimeSvc,
    ILogger<TerminateFilerRegistration> logger) : ITerminateFilerRegistration
{
    /// <inheritdoc />
    public async ValueTask<IResult<Registration>> Execute(
        long id,
        CancellationToken cancellationToken = default)
    {
        var filerCurrentRegistration = await db.Filers.Where(f => f.Id == id)
            .Select(f => f.CurrentRegistration)
            .FirstOrDefaultAsync(cancellationToken);

        // A filer must have a current registration, then if the registration is null, it means the filer was not found
        if (filerCurrentRegistration is null)
        {
            logger.LogWarning("Filer was not found.");
            return new Failure<Registration>.NotFound("No filer was found with the requested id");
        }

        if (filerCurrentRegistration.StatusId != RegistrationStatus.Accepted.Id)
        {
            logger.LogWarning("Registration was not in the correct state. Was {Actual}", filerCurrentRegistration.StatusId);

            return new Failure<Registration>.InvalidState(
                filerCurrentRegistration.StatusId,
                "Accepted",
                "Registration is not in a accepted state");
        }

        filerCurrentRegistration.StatusId = RegistrationStatus.Terminated.Id;
        filerCurrentRegistration.TerminatedAt = dateTimeSvc.GetCurrentDateTime();

        await db.SaveChangesAsync(cancellationToken);

        var content = $"A sample message: Filer Registration '{filerCurrentRegistration.Name}' was terminated";
        await messaging.Execute(content, cancellationToken);

        return new Success<Registration>(filerCurrentRegistration);
    }
}
