using System.Globalization;
using System.Reflection;
using Moq;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.Efile;
using SOS.CalAccess.Services.Business.Efile.Model;
using SlateMailerOrganization = SOS.CalAccess.Services.Business.Efile.Model.SlateMailerOrganization;

namespace SOS.CalAccess.Services.Business.Tests.Efile;

/// <summary>
/// Unit tests for the <see cref="EfileSubmissionSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[TestFixture]
[TestOf(typeof(EfileSubmissionSvc))]
public sealed class EfileSubmissionSvcTest
{
    #region Properties

    /// <summary>
    /// Maximum allowed execution time (in milliseconds).
    /// </summary>
    private const int MaxTimeAllowed = 90000;

    /// <summary>
    /// The class is used to generate a submission object.
    /// </summary>
    private EfileSubmissionSvc _submissionSvc;

    #endregion

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>

    private Mock<IReferenceDataSvc> _mockReferenceDataSvc;

    private DateTime _dateNow;


    [SetUp]
    public void SetUp()
    {
        _mockReferenceDataSvc = new Mock<IReferenceDataSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _submissionSvc = new EfileSubmissionSvc(_mockReferenceDataSvc.Object);
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateSubmissionDto_ShouldMapCorrectly()
    {
        var candidateAddress = new EfileAddress
        {
            Street = "123 Main St",
            City = "Testville",
            Country = "USA",
            State = "CA",
            Type = "Home",
            ZipCode = "90001"
        };

        var mailingAddress = new EfileAddress
        {
            Street = "456 Elm St",
            City = "Mailtown",
            Country = "USA",
            State = "CA",
            Type = "Mail",
            ZipCode = "90002"
        };

        var stmt = new EfileCandidateIntentionStatement
        {
            CandidateInformation = new CandidateInformation
            {
                CandidateAddress = candidateAddress,
                MailingAddress = mailingAddress,
                ElectionRaceId = 101,
                FirstName = "John",
                LastName = "Doe",
                ElectionJurisdiction = "Jurisdiction X",
                Email = "<EMAIL>",
                PartyAffiliation = "Independent",
                Phone = "5551234567"
            },
            Amendment = new EfileAmendment
            {
                SupercededFilingId = 789
            },
            Attestation = new EfileAttestation
            {
                Signature = "John Doe",
                ExecutedAt = _dateNow
            },
            StateCandidateExpenditureLimit = new StateCandidateExpenditureLimit
            {
                ContributedPersonalExcessFundsOn = _dateNow.AddDays(-1),
                ExpenditureExceeded = false,
                ExpenditureLimitAccepted = true
            }
        };

        var apiRequest = new ApiRequest
        {
            ReceivedAt = _dateNow,
            UserId = "1001"
        };

        var result = _submissionSvc.CreateSubmissionDto(stmt, apiRequest);
        if (result != null)
        {
            Assert.Multiple(() =>
            {
                Assert.That(result.Race!.Id, Is.EqualTo(101));

                var reg = result.Registration!;

                Assert.That(reg, Is.Not.Null);
                Assert.That(reg.FirstName, Is.EqualTo("John"));
                Assert.That(reg.LastName, Is.EqualTo("Doe"));
                Assert.That(reg.ElectionJurisdiction, Is.EqualTo("Jurisdiction X"));
                Assert.That(reg.Email, Is.EqualTo("<EMAIL>"));
                Assert.That(reg.ExpenditureLimitAccepted, Is.True);
                Assert.That(reg.ExpenditureExceeded, Is.False);
                Assert.That(reg.ContributedPersonalExcessFundsOn!.Value.Date, Is.EqualTo(stmt.StateCandidateExpenditureLimit.ContributedPersonalExcessFundsOn.Value.Date));
                Assert.That(reg.PoliticalParty!.Name, Is.EqualTo("Independent"));
                Assert.That(reg.Name, Is.EqualTo("John Doe"));
                Assert.That(reg.VerificationSignature, Is.EqualTo("John Doe"));
                Assert.That(reg.VerificationExecutedAt!.Value.Date, Is.EqualTo(stmt.Attestation!.ExecutedAt.Date));
                Assert.That(reg.StatusId, Is.EqualTo(Models.FilerRegistration.Registrations.RegistrationStatus.Draft.Id));
                Assert.That(reg.SelfRegister, Is.True);
                Assert.That(reg.SubmittedAt!.Value.Date, Is.EqualTo(apiRequest.ReceivedAt.Date));
                Assert.That(reg.ParentId, Is.EqualTo(stmt.Amendment!.SupercededFilingId)
);

                // Phone
                Assert.That(reg.PhoneNumberList!.PhoneNumbers, Has.Count.EqualTo(1));
                Assert.That(reg.PhoneNumberList.PhoneNumbers[0].Number, Is.EqualTo("+5551234567"));
                Assert.That(reg.PhoneNumberList.PhoneNumbers[0].Type, Is.EqualTo("Home"));

                // Addresse
                Assert.That(reg.AddressList!.Addresses, Has.Count.EqualTo(2));
                Assert.That(reg.AddressList.Addresses[0].Street, Is.EqualTo("123 Main St"));
                Assert.That(reg.AddressList.Addresses[0].Purpose, Is.EqualTo("Candidate"));
                Assert.That(reg.AddressList.Addresses[1].Street, Is.EqualTo("456 Elm St"));
                Assert.That(reg.AddressList.Addresses[1].Purpose, Is.EqualTo("Mailing"));

                // User
                Assert.That(result.UserId, Is.EqualTo(1001));
            });
        }
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateSubmissionDtoSMO_ShouldMapCorrectly()
    {
        var smo = new EfileSlateMailerOrganization
        {
            SlateMailerOrganization = new SlateMailerOrganization
            {
                Name = "Test Org",
                Email = "<EMAIL>",
                Address = new EfileAddress
                {
                    Street = "123 Main St",
                    City = "City",
                    Country = "USA",
                    State = "CA",
                    Type = "Organization",
                    ZipCode = "98765"
                },
                MailingAddress = null!,
                DateQualified = new DateTime(2024, 1, 1),
                ActivityLevel = "High"
            },
            Amendment = new AmendmentSmo { SupercededFilingId = "456" },
            Attestation = new AttestationSmo { Signature = "Jane Doe", ExecutedAt = new DateTime(2024, 1, 2) },
            CommitteeSmo = new CommitteeInfo
            {
                IsCommittee = true,
                CommitteeDetails = new CommitteeDetails { IdNumber = 123 }
            },
            Officers = new List<Officer>
            {
                new() { FirstName = "John", LastName = "Smith",Role = "Treasurer" }
            },
            IndividualsAuthorizingSlateMailers = new List<IndividualAuthorizingSlateMailer>
            {
                new() { FirstName = "Alice", LastName = "Brown" }
            }
        };

        var apiRequest = new ApiRequest
        {
            UserId = "1001",
            ReceivedAt = new DateTime(2024, 1, 10)
        };

        // Act
        var result = _submissionSvc.CreateSubmissionDtoSMO(smo, apiRequest, false);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.SlateMailerOrg!.Name, Is.EqualTo("Test Org"));
            Assert.That(result.SlateMailerOrg.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(result.SlateMailerOrg.ParentId, Is.EqualTo(456));
            Assert.That(result.SlateMailerOrg.StatusId, Is.EqualTo(Models.FilerRegistration.Registrations.RegistrationStatus.Draft.Id));
            Assert.That(result.SlateMailerOrg.SubmittedAt, Is.EqualTo(apiRequest.ReceivedAt));

            Assert.That(result.SlateMailerOrg.PhoneNumberList, Is.Not.Null);
            //Assert.That(result.SlateMailerOrg.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("+**********"));
            Assert.That(result.SlateMailerOrg.PhoneNumberList!.PhoneNumbers[0].Type, Is.EqualTo("Home"));

            Assert.That(result.SlateMailerOrg.AddressList!.Addresses, Has.Count.EqualTo(2));
            //Assert.That(result.SlateMailerOrg.AddressList!.Addresses[0].Purpose, Is.EqualTo("Organization"));
            //Assert.That(result.SlateMailerOrg.AddressList!.Addresses[1].Purpose, Is.EqualTo("Mailing"));

            Assert.That(result.SlateMailerOrg.VerificationSignature, Is.EqualTo("Jane Doe"));
            Assert.That(result.SlateMailerOrg.VerificationExecutedAt!.Value.Date, Is.EqualTo(smo.Attestation.ExecutedAt.Date));
            Assert.That(result.SlateMailerOrg.CampaignCommittee, Is.True);
            Assert.That(result.SlateMailerOrg.DateQualified!.Value.Date, Is.EqualTo(smo.SlateMailerOrganization.DateQualified.Date));
            Assert.That(result.SlateMailerOrg.ActivityLevel, Is.EqualTo("High"));

            Assert.That(result.SlateMailerOrg.RegistrationRegistrationContacts, Has.Count.EqualTo(2));

            var officerContact = result.SlateMailerOrg.RegistrationRegistrationContacts[0];
            Assert.That(officerContact.RegistrationContact!.FirstName, Is.EqualTo("John"));
            //Assert.That(officerContact.RegistrationContact!.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("+5550001111"));
            Assert.That(officerContact.Role, Is.EqualTo("Treasurer"));

            var individualContact = result.SlateMailerOrg.RegistrationRegistrationContacts[1];
            Assert.That(individualContact.RegistrationContact!.FirstName, Is.EqualTo("Alice"));
            //Assert.That(individualContact.RegistrationContact!.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("+9998887777"));
            Assert.That(individualContact.Role, Is.EqualTo(""));

            Assert.That(result.VendorUserId, Is.EqualTo(1001));
        });
    }
    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateSubmissionDtoSMOTermination_ShouldMapCorrectly()
    {
        var smo = new EfileSlateMailerOrganization
        {
            SlateMailerOrganization = new SlateMailerOrganization
            {
                Name = "Test Org",
                Email = "<EMAIL>",
                Address = new EfileAddress
                {
                    Street = "123 Main St",
                    City = "City",
                    Country = "USA",
                    State = "CA",
                    Type = "Organization",
                    ZipCode = "98765"
                },
                MailingAddress = null!,
                DateQualified = new DateTime(2024, 1, 1),
                ActivityLevel = "High",
                TerminationDate = new DateTime(2024, 1, 1),
                Phone = new EfilePhoneNumber
                {
                    CountryCode = "+1",
                    Number = "9165551234",
                    Extension = "101"

                },
                Fax = new EfilePhoneNumber
                {
                    CountryCode = "+1",
                    Number = "9165555678",
                    Extension = "102"
                },
            },
            Amendment = new AmendmentSmo { SupercededFilingId = "456" },
            Attestation = new AttestationSmo { Signature = "Jane Doe", ExecutedAt = new DateTime(2024, 1, 2) },
            CommitteeSmo = new CommitteeInfo
            {
                IsCommittee = true,
                CommitteeDetails = new CommitteeDetails { IdNumber = 123 }
            },
            Officers = new List<Officer>
            {
                new() { FirstName = "John", LastName = "Smith",Role = "Treasurer" }
            },
            IndividualsAuthorizingSlateMailers = new List<IndividualAuthorizingSlateMailer>
            {
                new() { FirstName = "Alice", LastName = "Brown" }
            }
        };

        var apiRequest = new ApiRequest
        {
            UserId = "1001",
            ReceivedAt = new DateTime(2024, 1, 10)
        };

        // Act
        var result = _submissionSvc.CreateSubmissionDtoSMO(smo, apiRequest, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.SlateMailerOrg!.Name, Is.EqualTo("Test Org"));
            Assert.That(result.SlateMailerOrg.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(result.SlateMailerOrg.ParentId, Is.EqualTo(456));
            Assert.That(result.SlateMailerOrg.StatusId, Is.EqualTo(Models.FilerRegistration.Registrations.RegistrationStatus.Draft.Id));
            Assert.That(result.SlateMailerOrg.SubmittedAt, Is.EqualTo(apiRequest.ReceivedAt));

            Assert.That(result.SlateMailerOrg.PhoneNumberList, Is.Not.Null);
            Assert.That(result.SlateMailerOrg.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("9165551234"));
            Assert.That(result.SlateMailerOrg.PhoneNumberList!.PhoneNumbers[0].Type, Is.EqualTo("Home"));

            Assert.That(result.SlateMailerOrg.AddressList!.Addresses, Has.Count.EqualTo(1));
            Assert.That(result.SlateMailerOrg.AddressList!.Addresses[0].Purpose, Is.EqualTo("Organization"));
            // Assert.That(result.SlateMailerOrg.AddressList!.Addresses[1].Purpose, Is.EqualTo("Mailing"));

            Assert.That(result.SlateMailerOrg.VerificationSignature, Is.EqualTo("Jane Doe"));
            Assert.That(result.SlateMailerOrg.VerificationExecutedAt!.Value.Date, Is.EqualTo(smo.Attestation.ExecutedAt.Date));
            Assert.That(result.SlateMailerOrg.CampaignCommittee, Is.True);
            Assert.That(result.SlateMailerOrg.DateQualified!.Value.Date, Is.EqualTo(smo.SlateMailerOrganization.DateQualified.Date));
            Assert.That(result.SlateMailerOrg.ActivityLevel, Is.EqualTo("High"));

            Assert.That(result.SlateMailerOrg.RegistrationRegistrationContacts, Has.Count.EqualTo(2));

            var officerContact = result.SlateMailerOrg.RegistrationRegistrationContacts[0];
            Assert.That(officerContact.RegistrationContact!.FirstName, Is.EqualTo("John"));
            //Assert.That(officerContact.RegistrationContact!.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("+5550001111"));
            Assert.That(officerContact.Role, Is.EqualTo("Treasurer"));

            var individualContact = result.SlateMailerOrg.RegistrationRegistrationContacts[1];
            Assert.That(individualContact.RegistrationContact!.FirstName, Is.EqualTo("Alice"));
            //Assert.That(individualContact.RegistrationContact!.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("+9998887777"));
            Assert.That(individualContact.Role, Is.EqualTo(""));
            Assert.That(result.SlateMailerOrg.TerminatedAt, Is.EqualTo(smo.SlateMailerOrganization.TerminationDate.Date));
            Assert.That(result.VendorUserId, Is.EqualTo(1001));
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateCandidateStatementShortSubmissionDto_ShouldMapCorrectly()
    {
        var apiRequest = new ApiRequest
        {
            FilerId = "12345",
            UserId = "1001"
        };

        var committeeDetails = new List<CandidateCampaignStatementShortCommitteeDetail>
        {
            new() { CommitteeId = "2001" },
            new() { CommitteeId = "2002" }
        };

        var attestation = new CandidateCampaignStatementShortAttestation
        {
            FirstName = "Jane",
            LastName = "Smith",
            ExecutedAtCity = "Sacramento",
            ExecutedAtState = "CA",
            ExecutedAt = new DateTime(2024, 5, 1, 0, 0, 0, DateTimeKind.Local),
            Role = "Treasurer",
            Signature = "Jane Smith"
        };

        var amendment = new CandidateCampaignStatementShortAmendment
        {
            IsAmendment = true,
            SupercededFilingId = "999"
        };

        var candidateStmtShort = new EfileCandidateCampaignStatementShort
        {
            FilingPeriodId = 2024,
            CommitteeDetails = committeeDetails,
            Attestation = attestation,
            Amendment = amendment
        };

        var svc = new EfileSubmissionSvc(_mockReferenceDataSvc.Object);
        var result = svc.CreateCandidateStatementShortSubmissionDto(candidateStmtShort, apiRequest);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.CandidateStatementShort, Is.Not.Null);
            Assert.That(result.CandidateStatementShort!.FilerId, Is.EqualTo(12345));
            Assert.That(result.CandidateStatementShort!.FilingPeriodId, Is.EqualTo(2024));
            Assert.That(result.CandidateStatementShort!.FilingTypeId, Is.EqualTo(Models.FilerDisclosure.Filings.FilingType.OfficeHolderCandidateShortForm.Id));
            Assert.That(result.CandidateStatementShort!.StatusId, Is.EqualTo(Models.FilerRegistration.Registrations.RegistrationStatus.Accepted.Id));
            Assert.That(result.CandidateStatementShort!.FilingRelatedFilers, Has.Count.EqualTo(2));
            Assert.That(result.CandidateStatementShort!.FilingRelatedFilers[0].FilerId, Is.EqualTo(2001));
            Assert.That(result.CandidateStatementShort!.FilingRelatedFilers[1].FilerId, Is.EqualTo(2002));

            Assert.That(result.Attestation, Is.Not.Null);
            Assert.That(result.Attestation!.FirstName, Is.EqualTo("Jane"));
            Assert.That(result.Attestation!.LastName, Is.EqualTo("Smith"));
            Assert.That(result.Attestation!.ExecutedAtCity, Is.EqualTo("Sacramento"));
            Assert.That(result.Attestation!.ExecutedAtState, Is.EqualTo("CA"));
            Assert.That(result.Attestation!.ExecutedAt, Is.EqualTo(new DateTime(2024, 5, 1, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(result.Attestation!.Role, Is.EqualTo("Treasurer"));
            Assert.That(result.Attestation!.Signature, Is.EqualTo("Jane Smith"));

            Assert.That(result.Amendment, Is.Not.Null);
            Assert.That(result.Amendment!.IsAmendment, Is.True);
            Assert.That(result.Amendment!.SupercededFilingId, Is.EqualTo("999"));

            Assert.That(result.UserId, Is.EqualTo(1001));
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateCandidateStatementShortSubmissionDto_ShouldHandleNullUserId()
    {
        var apiRequest = new ApiRequest
        {
            FilerId = "12345",
            UserId = null!
        };

        var candidateStmtShort = new EfileCandidateCampaignStatementShort
        {
            FilingPeriodId = 2024,
            CommitteeDetails = new List<CandidateCampaignStatementShortCommitteeDetail>(),
            Attestation = new CandidateCampaignStatementShortAttestation(),
            Amendment = new CandidateCampaignStatementShortAmendment()
        };

        var svc = new EfileSubmissionSvc(_mockReferenceDataSvc.Object);
        var result = svc.CreateCandidateStatementShortSubmissionDto(candidateStmtShort, apiRequest);

        Assert.That(result.UserId, Is.EqualTo(-1));
    }

    [Test]
    public async Task CreateSubmissionDtoLR_ShouldMapAllFieldsCorrectly()
    {
        // Arrange
        var stmt = new EfileLobbyistReport
        {
            ReportingPeriod = new ReportingPeriod
            {

                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 6, 30, 0, 0, 0, DateTimeKind.Utc)
            },
            Amendment = new EfileAmendment { SupercededFilingId = 101, AmendmentWithExplanation = "Corrected amount" },
            NothingToReportActivityExpense = true,
            NothingToReportCampaignContributions = false,
            Attestation = new EfileAttestation
            {
                ExecutedAt = new DateTime(2024, 5, 10, 0, 0, 0, DateTimeKind.Utc),
                ExecutedAtCity = "Los Angeles",
                ExecutedAtState = "CA"
            },
            ActivityExpenses = new List<ActivityExpenses>
        {
            new()
            {
                ActivityExpenseTypeId = 1,
                CardName = "Visa",
                Total = 123.45M,
                Date = new DateTime(2024, 4, 1, 0, 0, 0, DateTimeKind.Utc),
                OtherExpenses = "Travel",
                AdditionalInformation = "Client meeting",
                ReportablePersons = new List<ReportablePerson>
                {
                    new()
                    {
                        OfficialPositionId = 1,
                        AgencyId = 10,
                        EachItemAmount = 123.45M,
                        OtherOfficialPosition = "Other Position",
                        OtherAgency = "Other Agency",
                        ReportablePersonName = new PersonName { FirstName = "Jane", LastName = "Doe" }
                    }
                }
            }
        },
            Contacts = new List<Contact>
        {
            new()
            {
                ContactTypeId = FilerContactType.Individual.Id,
                ContactInfo = new ContactInfo
                {
                    PersonName = new PersonName { FirstName = "Alice", LastName = "Smith" },
                    Address = new EfileAddress
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        Country = "USA",
                        State = "CA",
                        Type = "Home",
                        ZipCode = "95814"
                    }
                }
            }
        },
            CampaignContributions = new List<CampaignContributions>
        {
            new()
            {
                NameOfRecipient = "Committee A",
                Date = new DateTime(2024, 3, 15, 0, 0, 0, DateTimeKind.Utc),
                SeparateAccountName = "Fund A",
                NonFilerContributorName = null,
                Amount = 500,
                RecipientCommitteeID = "888"
            }
        }
        };

        var apiRequest = new ApiRequest { FilerId = "123", UserId = "999" };

        // Mock reference data lookups
        _mockReferenceDataSvc.Setup(x => x.GetAllOfficialPositions())
            .ReturnsAsync(new List<OfficialPosition> { OfficialPosition.AssemblyMember });

        _mockReferenceDataSvc.Setup(x => x.GetAllAgencies())
            .ReturnsAsync(new List<Models.FilerRegistration.Agency> { new() { Id = 10, Name = "Water Board" } });

        // Act
        var result = await _submissionSvc.CreateSubmissionDtoLR(stmt, apiRequest);

        // Assert
        // Activity Expense
        Assert.That(result.ActivityExpenses, Has.Count.EqualTo(1));

        var expense = result.ActivityExpenses[0];

        Assert.Multiple(() =>
        {
            Assert.That(expense.ActivityExpenseTypeId, Is.EqualTo(1));
            Assert.That(expense.CreditCardCompanyName?.Trim(), Is.EqualTo("Visa").IgnoreCase);
            Assert.That(expense.Amount, Is.EqualTo((Currency)123.45M));
            Assert.That(expense.TransactionReportablePersons, Has.Count.EqualTo(1));
        });

        var person = expense.TransactionReportablePersons[0];

        Assert.Multiple(() =>
        {
            Assert.That(person.OfficialPosition?.Trim(), Is.EqualTo("Assembly Member").IgnoreCase);
            Assert.That(person.Agency?.Trim(), Is.EqualTo("Water Board").IgnoreCase);

            // Contacts
            Assert.That(result.FilerContacts, Has.Count.EqualTo(1));
        });

        var contact = result.FilerContacts[0] as IndividualContact;
        Assert.That(contact, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(contact!.FirstName?.Trim(), Is.EqualTo("Alice").IgnoreCase);
            Assert.That(contact.LastName?.Trim(), Is.EqualTo("Smith").IgnoreCase);
            Assert.That(contact.AddressList!.Addresses[0].City?.Trim(), Is.EqualTo("Sacramento").IgnoreCase);

            // Campaign Contribution
            Assert.That(result.CampaignContributions, Has.Count.EqualTo(1));
        });
        var contribution = result.CampaignContributions[0];

        Assert.Multiple(() =>
        {
            Assert.That(contribution.RecipientFilerId, Is.EqualTo(888));
            Assert.That(contribution.Amount, Is.EqualTo((Currency)500));
            Assert.That(contribution.SeparateAccountName?.Trim(), Is.EqualTo("Fund A").IgnoreCase);
            Assert.That(contribution.NonCommitteeRecipientName?.Trim(), Is.EqualTo("Committee A").IgnoreCase);

            Assert.That(result.LobbyistReport.StartDate.Date, Is.EqualTo(new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc).Date));
            Assert.That(result.LobbyistReport.EndDate.Date, Is.EqualTo(new DateTime(2024, 6, 30, 0, 0, 0, DateTimeKind.Utc).Date));

            // Attestation
            Assert.That(result.Attestation.ExecutedAtCity?.Trim(), Is.EqualTo("Los Angeles").IgnoreCase);
            Assert.That(result.Attestation.ExecutedAtState?.Trim(), Is.EqualTo("CA").IgnoreCase);
        });

    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public async Task CreateSubmissionDtoLR_ShouldHandleNullUserId()
    {
        // Arrange
        var apiRequest = new ApiRequest
        {
            FilerId = "123",
            UserId = null!
        };

        var stmt = new EfileLobbyistReport
        {
            ReportingPeriod = new ReportingPeriod
            {
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 6, 30, 0, 0, 0, DateTimeKind.Utc)
            },
            Amendment = new EfileAmendment
            {
                SupercededFilingId = 1001,
                AmendmentWithExplanation = "Initial correction"
            },
            NothingToReportActivityExpense = true,
            NothingToReportCampaignContributions = true,
            Attestation = new EfileAttestation
            {
                ExecutedAt = DateTime.UtcNow,
                ExecutedAtCity = "City",
                ExecutedAtState = "CA"
            },
            ActivityExpenses = new List<ActivityExpenses>(),
            Contacts = new List<Contact>(),
            CampaignContributions = new List<CampaignContributions>()
        };

        // Mock reference data lookups
        _mockReferenceDataSvc.Setup(x => x.GetAllOfficialPositions())
            .ReturnsAsync(new List<OfficialPosition> { OfficialPosition.AssemblyMember });

        _mockReferenceDataSvc.Setup(x => x.GetAllAgencies())
            .ReturnsAsync(new List<Models.FilerRegistration.Agency> { new() { Id = 10, Name = "Water Board" } });


        // Act
        var result = await _submissionSvc.CreateSubmissionDtoLR(stmt, apiRequest);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.UserId, Is.EqualTo(-1));
    }

    [Test]
    public async Task CreateSubmissionDtoLR_ShouldMapAllPropertiesCorrectly()
    {
        // Arrange
        var stmt = new EfileLobbyistReport
        {
            ReportingPeriod = new ReportingPeriod
            {
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 6, 30, 0, 0, 0, DateTimeKind.Utc)
            },
            Amendment = new EfileAmendment
            {
                SupercededFilingId = 101,
                AmendmentWithExplanation = "Correction to expense entry"
            },
            Attestation = new EfileAttestation
            {
                ExecutedAt = new DateTime(2025, 5, 19, 0, 0, 0, DateTimeKind.Utc),
                ExecutedAtCity = "Sacramento",
                ExecutedAtState = "CA"
            },
            NothingToReportActivityExpense = false,
            NothingToReportCampaignContributions = false,
            ActivityExpenses = new List<ActivityExpenses>
        {
            new()
            {
                ActivityExpenseTypeId = 1,
                CardName = "CorpCard",
                Date = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                Total = 500,
                OtherExpenses = "",
                AdditionalInformation = "Client Dinner",
                ReportablePersons = new List<ReportablePerson>
                {
                    new()
                    {
                        OfficialPositionId = OfficialPosition.SecretaryOfState.Id,
                        AgencyId = 100,
                        EachItemAmount = 250,
                        OtherAgency = "Custom Agency",
                        OtherOfficialPosition = "",
                        ReportablePersonName = new PersonName
                        {
                            FirstName = "Alice",
                            LastName = "Smith"
                        }
                    }
                }
            }
        },
            Contacts = new List<Contact>
        {
            new()
            {
                ContactTypeId = FilerContactType.Individual.Id,
                ExternalId = "C001",
                ContactInfo = new ContactInfo
                {
                    Email = "<EMAIL>",
                    Phone = "5551234567",
                    PersonName = new PersonName { FirstName = "Alice", LastName = "Smith" },
                    Address = new EfileAddress
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        ZipCode = "95814",
                        Country = "USA",
                        Type = "Home"
                    }
                }
            }
        },
            CampaignContributions = new List<CampaignContributions>
        {
            new()
            {
                NameOfRecipient = "TEST",
                Date =  new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                SeparateAccountName = "Lobbying Fund",
                NonFilerContributorName = null,
                Amount = 1000,
                RecipientCommitteeID = "202"
            }
        }
        };

        var apiRequest = new ApiRequest
        {
            FilerId = "123",
            UserId = "456"
        };

        // Mocks
        _mockReferenceDataSvc.Setup(x => x.GetAllOfficialPositions())
    .ReturnsAsync(new List<OfficialPosition>
    {
        OfficialPosition.SecretaryOfState
    });

        _mockReferenceDataSvc.Setup(x => x.GetAllAgencies())
            .ReturnsAsync(new List<Models.FilerRegistration.Agency>
            {
            new() { Id = 100, Name = "Health Agency" }
            });

        // Act
        var result = await _submissionSvc.CreateSubmissionDtoLR(stmt, apiRequest);

        // Assert
        Assert.Multiple(() =>
        {
            // LobbyistReport
            Assert.That(result.LobbyistReport.FilerId, Is.EqualTo(123));
            Assert.That(result.LobbyistReport.StartDate, Is.EqualTo(stmt.ReportingPeriod.StartDate));
            Assert.That(result.LobbyistReport.EndDate, Is.EqualTo(stmt.ReportingPeriod.EndDate));
            Assert.That(result.LobbyistReport.ParentId, Is.EqualTo(101));
            Assert.That(result.LobbyistReport.AmendmentExplanation, Is.EqualTo("Correction to expense entry"));
            Assert.That(result.LobbyistReport.FilingSummaries, Has.Count.EqualTo(2));

            // Attestation
            Assert.That(result.Attestation.ExecutedAtCity, Is.EqualTo("Sacramento"));
            Assert.That(result.Attestation.ExecutedAtState, Is.EqualTo("CA"));

            // UserId
            Assert.That(result.UserId, Is.EqualTo(456));

            // FilerContacts
            Assert.That(result.FilerContacts, Has.Count.EqualTo(1));
            var filerContact = result.FilerContacts.First() as IndividualContact;
            Assert.That(filerContact!.FirstName, Is.EqualTo("Alice"));
            Assert.That(filerContact.LastName, Is.EqualTo("Smith"));
            Assert.That(filerContact.EmailAddressList?.EmailAddresses.First().Email, Is.EqualTo("<EMAIL>"));

            // ActivityExpenses
            Assert.That(result.ActivityExpenses, Has.Count.EqualTo(1));
            var expense = result.ActivityExpenses.First();
            Assert.That(expense.Amount.Value, Is.EqualTo(500));
            Assert.That(expense.TransactionReportablePersons?.Count, Is.EqualTo(1));
            var trp = expense.TransactionReportablePersons?.First();
            Assert.That(trp?.Name, Is.EqualTo("Alice Smith"));
            Assert.That(trp?.OfficialPosition, Is.EqualTo(OfficialPosition.SecretaryOfState.Name));
            Assert.That(trp?.Agency, Is.EqualTo("Health Agency"));

            // CampaignContributions
            Assert.That(result.CampaignContributions, Has.Count.EqualTo(1));
            var contribution = result.CampaignContributions.First();
            Assert.That(contribution.NonCommitteeRecipientName, Is.EqualTo("TEST"));
            Assert.That(contribution.Amount.Value, Is.EqualTo(1000));
            Assert.That(contribution.RecipientFilerId, Is.EqualTo(202));
            Assert.That(contribution.ContributorFilerId, Is.EqualTo(123));
        });
    }

    [Test]
    public void CreateFilerContact_PrivateStatic_ShouldCreateIndividualContact()
    {
        // Arrange
        var contact = new Contact
        {
            ContactTypeId = FilerContactType.Individual.Id,
            ExternalId = "ext123",
            ContactInfo = new ContactInfo
            {
                Email = "<EMAIL>",
                Phone = "**********",
                PersonName = new PersonName { FirstName = "Jane", LastName = "Doe" },
                Address = new EfileAddress
                {
                    Street = "123 Main St",
                    City = "CityX",
                    State = "ST",
                    Country = "USA",
                    Type = "Home",
                    ZipCode = "11111"
                }
            }
        };

        long filerId = 100;

        // Act
        var method = typeof(EfileSubmissionSvc).GetMethod("CreateFilerContact", BindingFlags.NonPublic | BindingFlags.Static);
        var result = method!.Invoke(null, [contact, filerId]);

        // Assert
        Assert.That(result, Is.InstanceOf<IndividualContact>());

        var individual = result as IndividualContact;

        Assert.Multiple(() =>
        {
            Assert.That(individual!.FilerId, Is.EqualTo(filerId));
            Assert.That(individual.FirstName, Is.EqualTo("Jane"));
            Assert.That(individual.LastName, Is.EqualTo("Doe"));
            Assert.That(individual?.EmailAddressList?.EmailAddresses.FirstOrDefault()?.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(individual?.PhoneNumberList?.PhoneNumbers.FirstOrDefault()?.Number, Is.EqualTo("+**********"));
            Assert.That(individual?.AddressList?.Addresses.FirstOrDefault()?.City, Is.EqualTo("CityX"));
        });
    }

    [Test]
    public void CreateFilerContact_PrivateStatic_ShouldCreateOrganizationContact()
    {
        // Arrange
        var contact = new Contact
        {
            ContactTypeId = FilerContactType.Organization.Id,
            ExternalId = "org456",
            ContactInfo = new ContactInfo
            {
                OrganizationName = "OrgTest",
                Email = "<EMAIL>",
                Phone = "9876543210",
                Address = new EfileAddress
                {
                    Street = "789 Org St",
                    City = "OrgCity",
                    State = "OS",
                    ZipCode = "54321",
                    Country = "USA",
                    Type = "Business"
                }
            }
        };

        long filerId = 200;

        // Act
        var method = typeof(EfileSubmissionSvc).GetMethod("CreateFilerContact", BindingFlags.NonPublic | BindingFlags.Static);
        var result = method!.Invoke(null, [contact, filerId]);

        // Assert
        Assert.That(result, Is.InstanceOf<OrganizationContact>());

        var org = result as OrganizationContact;

        Assert.Multiple(() =>
        {
            Assert.That(org!.OrganizationName, Is.EqualTo("OrgTest"));
            Assert.That(org.EmailAddressList?.EmailAddresses?.FirstOrDefault()?.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(org.PhoneNumberList?.PhoneNumbers.FirstOrDefault()?.Number, Is.EqualTo("+9876543210"));
            Assert.That(org.AddressList?.Addresses.FirstOrDefault()?.City, Is.EqualTo("OrgCity"));
        });
    }

    [Test]
    public void CreateFilerContact_PrivateStatic_ShouldThrowForInvalidContactType()
    {
        // Arrange
        var contact = new Contact
        {
            ContactTypeId = -1
        };

        var method = typeof(EfileSubmissionSvc).GetMethod("CreateFilerContact", BindingFlags.NonPublic | BindingFlags.Static);

        // Act & Assert
        var ex = Assert.Throws<TargetInvocationException>(() => method!.Invoke(null, new object[] { contact, 100 }));
        Assert.That(ex!.InnerException, Is.TypeOf<InvalidOperationException>());
    }

    [Test]
    public async Task CreateSubmissionDtoLR_ShouldMapOrganizationContactCorrectly()
    {
        // Arrange
        var orgName = "Acme Corp";
        var street = "456 Corporate Blvd";
        var city = "Denver";
        var country = "USA";
        var state = "CO";
        var type = "Office";
        var zip = "80202";

        var stmt = new EfileLobbyistReport
        {

            ReportingPeriod = new ReportingPeriod
            {
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 6, 30, 0, 0, 0, DateTimeKind.Utc)
            },
            Amendment = new EfileAmendment(),
            NothingToReportActivityExpense = true,
            NothingToReportCampaignContributions = true,
            Attestation = new EfileAttestation(),
            ActivityExpenses = Enumerable.Empty<ActivityExpenses>().ToList(),
            Contacts = new List<Contact>
        {
            new() {
                ContactTypeId = FilerContactType.Organization.Id,
                ContactInfo = new ContactInfo
                {
                    OrganizationName = orgName,
                    Address = new EfileAddress
                    {
                        Street   = street,
                        City     = city,
                        Country  = country,
                        State    = state,
                        Type     = type,
                        ZipCode  = zip
                    }
                }
            }
        },
            CampaignContributions = Enumerable.Empty<CampaignContributions>().ToList()
        };

        var apiRequest = new ApiRequest { FilerId = "555", UserId = "0" };


        // Act
        var result = await _submissionSvc.CreateSubmissionDtoLR(stmt, apiRequest);

        // Assert
        Assert.That(result.FilerContacts, Has.Count.EqualTo(1), "Should have exactly one contact");

        var orgContact = result.FilerContacts[0] as OrganizationContact;
        Assert.That(orgContact, Is.Not.Null, "Contact should be an OrganizationContact");

        Assert.Multiple(() =>
        {
            Assert.That(orgContact!.FilerId, Is.EqualTo(555), "FilerId should round‐trip from ApiRequest");
            Assert.That(orgContact.OrganizationName?.Trim(), Is.EqualTo(orgName).IgnoreCase, "OrganizationName should map");

            var addr = orgContact?.AddressList?.Addresses[0];
            Assert.That(addr?.Street?.Trim(), Is.EqualTo(street).IgnoreCase, "Street should map");
            Assert.That(addr?.City?.Trim(), Is.EqualTo(city).IgnoreCase, "City should map");
            Assert.That(addr?.Country?.Trim(), Is.EqualTo(country).IgnoreCase, "Country should map");
            Assert.That(addr?.State?.Trim(), Is.EqualTo(state).IgnoreCase, "State should map");
            Assert.That(addr?.Type?.Trim(), Is.EqualTo(type).IgnoreCase, "Type should map");
            Assert.That((addr?.Zip)?.Trim(), Is.EqualTo(zip), "Zip should map");
        });
    }


    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateCandidateStatementSupplementSubmissionDto_ShouldMapCorrectly()
    {
        var apiRequest = new ApiRequest
        {
            FilerId = "12345",
            UserId = "1001"
        };

        var committeeDetails = new List<CandidateCampaignStatementSupplementCommitteeDetail>
        {
            new() { CommitteeId = "2001" },
            new() { CommitteeId = "2002" }
        };

        var attestation = new CandidateCampaignStatementSupplementAttestation
        {
            FirstName = "Jane",
            LastName = "Smith",
            ExecutedAtCity = "Sacramento",
            ExecutedAtState = "CA",
            ExecutedAt = new DateTime(2024, 5, 1, 0, 0, 0, DateTimeKind.Local),
            Role = "Treasurer",
            Signature = "Jane Smith"
        };

        var amendment = new CandidateCampaignStatementSupplementAmendment
        {
            IsAmendment = true,
            SupercededFilingId = "999"
        };

        var candidateStmtSupplement = new EfileCandidateCampaignStatementSupplement
        {
            DateThresholdReached = DateTime.Parse("2025-05-18", CultureInfo.InvariantCulture),
            CommitteeDetails = committeeDetails,
            Attestation = attestation,
            Amendment = amendment
        };

        var svc = new EfileSubmissionSvc(_mockReferenceDataSvc.Object);
        var result = svc.CreateCandidateStatementSupplementSubmissionDto(candidateStmtSupplement, apiRequest);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.CandidateStatementSupplement, Is.Not.Null);
            Assert.That(result.CandidateStatementSupplement!.FilerId, Is.EqualTo(12345));
            //Assert.That(result.CandidateStatementSupplement!.FilingPeriodId, Is.EqualTo(2024));
            //Assert.That(result.CandidateStatementSupplement!.FilingTypeId, Is.EqualTo(Models.FilerDisclosure.Filings.FilingType.OfficeHolderCandidateSupplementForm.Id));
            Assert.That(result.CandidateStatementSupplement!.StatusId, Is.EqualTo(Models.FilerRegistration.Registrations.RegistrationStatus.Accepted.Id));
            Assert.That(result.CandidateStatementSupplement!.FilingRelatedFilers, Has.Count.EqualTo(2));
            Assert.That(result.CandidateStatementSupplement!.FilingRelatedFilers[0].FilerId, Is.EqualTo(2001));
            Assert.That(result.CandidateStatementSupplement!.FilingRelatedFilers[1].FilerId, Is.EqualTo(2002));

            Assert.That(result.Attestation, Is.Not.Null);
            Assert.That(result.Attestation!.FirstName, Is.EqualTo("Jane"));
            Assert.That(result.Attestation!.LastName, Is.EqualTo("Smith"));
            Assert.That(result.Attestation!.ExecutedAtCity, Is.EqualTo("Sacramento"));
            Assert.That(result.Attestation!.ExecutedAtState, Is.EqualTo("CA"));
            Assert.That(result.Attestation!.ExecutedAt, Is.EqualTo(new DateTime(2024, 5, 1, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(result.Attestation!.Role, Is.EqualTo("Treasurer"));
            Assert.That(result.Attestation!.Signature, Is.EqualTo("Jane Smith"));

            Assert.That(result.Amendment, Is.Not.Null);
            Assert.That(result.Amendment!.IsAmendment, Is.True);
            Assert.That(result.Amendment!.SupercededFilingId, Is.EqualTo("999"));

            Assert.That(result.UserId, Is.EqualTo(1001));
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateCandidateStatementSupplementSubmissionDto_ShouldHandleNullUserId()
    {
        var apiRequest = new ApiRequest
        {
            FilerId = "12345",
            UserId = null!
        };

        var candidateStmtSupplement = new EfileCandidateCampaignStatementSupplement
        {
            DateThresholdReached = DateTime.Parse("2025-05-18", CultureInfo.InvariantCulture),
            CommitteeDetails = new List<CandidateCampaignStatementSupplementCommitteeDetail>(),
            Attestation = new CandidateCampaignStatementSupplementAttestation(),
            Amendment = new CandidateCampaignStatementSupplementAmendment()
        };

        var svc = new EfileSubmissionSvc(_mockReferenceDataSvc.Object);
        var result = svc.CreateCandidateStatementSupplementSubmissionDto(candidateStmtSupplement, apiRequest);

        Assert.That(result.UserId, Is.EqualTo(-1));
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateLobbyistRegistrationSubmissionDto_ShouldMapAllFieldsCorrectly()
    {
        var apiRequest = new ApiRequest
        {
            FilerId = "12345",
            UserId = "1001"
        };

        var registration = new EfileLobbyingCertificationLobbyist
        {
            LegislativeSessionId = 20232024,
            LobbyistDetails = new LobbyistDetails
            {
                FirstName = "Jane",
                LastName = "Doe",
                MiddleInitial = "A",
                DateQualified = new DateTime(2024, 1, 15, 0, 0, 0, DateTimeKind.Utc),
                IsLobbyistPlacementAgent = false,
                TelephoneNumber = new EfilePhoneNumber
                {
                    CountryCode = "+1",
                    Number = "9165551234",
                    Extension = "101"
                },
                FaxNumber = new EfilePhoneNumber
                {
                    CountryCode = "+1",
                    Number = "9165555678",
                    Extension = "102"
                },
                Email = "<EMAIL>",
                LobbyistEmployerOrLobbyingFirmId = 123456789
            },
            BusinessAddress = new EfileAddress
            {
                Street = "123 Main St",
                Street2 = "Suite 400",
                City = "Sacramento",
                State = "CA",
                ZipCode = "95814",
                County = "Sacramento",
                Country = "USA",
                Type = "Business"
            },
            MailingAddress = new EfileAddress
            {
                Street = "PO Box 12345",
                Street2 = "",
                City = "Sacramento",
                State = "CA",
                ZipCode = "95814",
                County = "Sacramento",
                Country = "USA",
                Type = "Mailing"
            },
            EthicsOrientation = new EthicsOrientation
            {
                IsCourseTaken = true,
                CourseCompletedDate = new DateTime(2023, 12, 1, 0, 0, 0, DateTimeKind.Utc)
            },
            AgenciesLobbied = new AgenciesLobbied
            {
                WillLobbyFromRegistration = true,
                StateLegislature = false,
                StateAgencies = new List<long> { 1001, 1002, 1003 }
            },
            Amendment = new EfileAmendment
            {
                IsAmendment = false,
                SupercededFilingId = 123
            },
            Attestation = new EfileAttestation
            {
                FirstName = "Jane",
                LastName = "Doe",
                ExecutedAt = new DateTime(2024, 5, 22, 0, 0, 0, DateTimeKind.Utc),
                ExecutedAtCity = "Sacramento",
                ExecutedAtState = "CA",
                Role = "Lobbyist",
                Signature = "Jane Doe"
            }
        };

        var result = _submissionSvc.CreateLobbyistRegistrationSubmissionDto(registration, apiRequest);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Lobbyist, Is.Not.Null);
            Assert.That(result.Lobbyist!.FirstName, Is.EqualTo("Jane"));
            Assert.That(result.Lobbyist!.LastName, Is.EqualTo("Doe"));
            Assert.That(result.Lobbyist!.MiddleName, Is.EqualTo("A"));
            Assert.That(result.Lobbyist!.Name, Is.EqualTo("Jane A Doe"));
            Assert.That(result.Lobbyist!.Email, Is.EqualTo("<EMAIL>"));
            //Assert.That(result.Lobbyist!.DateQualified, Is.EqualTo(new DateTimeOffset(2024, 1, 15, 0, 0, 0, TimeSpan.Zero)));

            Assert.That(result.Lobbyist!.PlacementAgent, Is.False);
            Assert.That(result.Lobbyist!.LegislativeSessionId, Is.EqualTo(20232024));
            Assert.That(result.Lobbyist!.StatusId, Is.EqualTo(Models.FilerRegistration.Registrations.RegistrationStatus.Accepted.Id));
            Assert.That(result.Lobbyist!.SelfRegister, Is.True);

            // Phone numbers
            Assert.That(result.Lobbyist.PhoneNumberList, Is.Not.Null);
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers, Has.Count.EqualTo(2));
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[0].Number, Is.EqualTo("9165551234"));
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[0].CountryCode, Is.EqualTo("+1"));
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[0].Extension, Is.EqualTo("101"));
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[0].Type, Is.EqualTo(RegistrationConstants.PhoneNumber.TypeHome));
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[1].Number, Is.EqualTo("9165555678"));
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[1].CountryCode, Is.EqualTo("+1"));
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[1].Extension, Is.EqualTo("101")); // Fax uses phone extension per code
            Assert.That(result.Lobbyist.PhoneNumberList!.PhoneNumbers[1].Type, Is.EqualTo(RegistrationConstants.PhoneNumber.TypeFax));

            // Addresses
            Assert.That(result.Lobbyist.AddressList, Is.Not.Null);
            Assert.That(result.Lobbyist.AddressList!.Addresses, Has.Count.EqualTo(2));
            Assert.That(result.Lobbyist.AddressList!.Addresses[0].Street, Is.EqualTo("123 Main St"));
            Assert.That(result.Lobbyist.AddressList!.Addresses[0].City, Is.EqualTo("Sacramento"));
            Assert.That(result.Lobbyist.AddressList!.Addresses[0].Type, Is.EqualTo("Business"));
            Assert.That(result.Lobbyist.AddressList!.Addresses[1].Street, Is.EqualTo("PO Box 12345"));
            Assert.That(result.Lobbyist.AddressList!.Addresses[1].Type, Is.EqualTo("Mailing"));

            // Ethics
            Assert.That(result.Lobbyist.EthicsCourseCompleted, Is.True);
            //Assert.That(result.Lobbyist.EthicsCourseCompletionDate, Is.EqualTo(new DateTimeOffset(2023, 12, 1, 0, 0, 0, TimeSpan.Zero)));

            // Agencies
            Assert.That(result.Lobbyist.LobbyOnlySpecifiedAgencies, Is.True);
            Assert.That(result.Lobbyist.StateLegislatureLobbying, Is.False);
            Assert.That(result.Lobbyist.RegistrationAgencies, Has.Count.EqualTo(3));
            Assert.That(result.Lobbyist.RegistrationAgencies[0].AgencyId, Is.EqualTo(1001));
            Assert.That(result.Lobbyist.RegistrationAgencies[1].AgencyId, Is.EqualTo(1002));
            Assert.That(result.Lobbyist.RegistrationAgencies[2].AgencyId, Is.EqualTo(1003));

            // Attestation
            Assert.That(result.Attestation, Is.Not.Null);
            Assert.That(result.Attestation!.FirstName, Is.EqualTo("Jane"));
            Assert.That(result.Attestation!.LastName, Is.EqualTo("Doe"));
            Assert.That(result.Attestation!.ExecutedAt, Is.EqualTo(new DateTime(2024, 5, 22, 0, 0, 0, DateTimeKind.Utc)));
            Assert.That(result.Attestation!.ExecutedAtCity, Is.EqualTo("Sacramento"));
            Assert.That(result.Attestation!.ExecutedAtState, Is.EqualTo("CA"));
            Assert.That(result.Attestation!.Role, Is.EqualTo("Lobbyist"));
            Assert.That(result.Attestation!.Signature, Is.EqualTo("Jane Doe"));

            // Amendment
            Assert.That(result.Amendment, Is.Not.Null);
            Assert.That(result.Amendment!.IsAmendment, Is.False);
            Assert.That(result.Amendment!.SupercededFilingId, Is.EqualTo(123));

            // UserId and EmployerOrFirmId
            Assert.That(result.UserId, Is.EqualTo(1001));
            Assert.That(result.LobbyistEmployerOrLobbyingFirmId, Is.EqualTo(123456789));
            Assert.That(result.IsSubmission, Is.True);
        });
    }

    [Test, Category("Unit"), MaxTime(MaxTimeAllowed)]
    public void CreateLobbyistRegistrationSubmissionDto_ShouldHandleNullUserId()
    {
        var apiRequest = new ApiRequest
        {
            FilerId = "12345",
            UserId = null!
        };

        var candidateStmtShort = new EfileLobbyingCertificationLobbyist
        {
            Attestation = new EfileAttestation(),
            Amendment = new EfileAmendment()
        };

        var svc = new EfileSubmissionSvc(_mockReferenceDataSvc.Object);
        var result = svc.CreateLobbyistRegistrationSubmissionDto(candidateStmtShort, apiRequest);

        Assert.That(result.UserId, Is.EqualTo(-1));
    }
}
