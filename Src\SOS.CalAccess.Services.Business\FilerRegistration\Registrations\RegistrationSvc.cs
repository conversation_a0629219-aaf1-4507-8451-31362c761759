using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Constants;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

/// <summary>
/// Implementation for the Registration Service Interface.
/// </summary>
public sealed class RegistrationSvc(
    IRegistrationRepository registrationRepository,
    ICandidateRepository candidateRepository,
    IMultipurposeOrganizationRepository multipurposeOrganizationRepository,
    IRegistrationStatusRepository registrationStatusRepository,
    IAttestationRepository attestationRepository,
    IDashboardQuerySvc dashboardQuerySvc,
    IDateTimeSvc dateTimeSvc) : IRegistrationSvc
{
    private readonly IRegistrationRepository _registrationRepository = registrationRepository;
    private readonly ICandidateRepository _candidateRepository = candidateRepository;
    private readonly IMultipurposeOrganizationRepository _multipurposeOrganizationRepository = multipurposeOrganizationRepository;
    private readonly IRegistrationStatusRepository _registrationStatusRepository = registrationStatusRepository;
    private readonly IAttestationRepository _attestationRepository = attestationRepository;
    private readonly IDashboardQuerySvc _dashboardQuerySvc = dashboardQuerySvc;
    private readonly IDateTimeSvc _dateTimeSvc = dateTimeSvc;

    /// <inheritdoc />
    public async Task AddRegistrationAddress(Address address, long registrationId)
    {
        await _registrationRepository.AddAddress(address, registrationId);
    }

    /// <inheritdoc />
    public async Task AddRegistrationPhoneNumber(PhoneNumber phoneNumber, long registrationId)
    {
        await _registrationRepository.AddPhoneNumber(phoneNumber, registrationId);
    }

    /// <inheritdoc />
    public Task AmendRegistration(Registration newRegistration, long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task ApproveRegistration(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task CompleteCandidateAttestation(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task CompleteLobbyingActivityAuthorization(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task CompleteLobbyistCertificationStatement(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<Registration> CreateRegistration(Registration registration)
    {
        var createdRegistration = await _registrationRepository.Create(registration);
        //Integrate audit service once the decision is finalized.
        return createdRegistration;
    }

    public async Task DeleteRegistration(long registrationId)
    {
        var registration = await _registrationRepository.FindById(registrationId);
        await _registrationRepository.Delete(registration);
    }

    /// <inheritdoc />
    public Task<IEnumerable<Agency>> GetAllAgencies()
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<Candidate>> GetAllCandidates()
    {
        return await _candidateRepository.GetAll();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<MultipurposeOrganization>> GetAllMultipurposeOrganizations()
    {
        return await _multipurposeOrganizationRepository.GetAll();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<Registration>> GetAllRegistrations()
    {
        return await _registrationRepository.GetAll();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<RegistrationStatus>> GetAllRegistrationStatuses()
    {
        return await _registrationStatusRepository.GetAll();
    }

    /// <inheritdoc />
    public async Task<Registration?> GetRegistration(long registrationId)
    {
        return await _registrationRepository.FindById(registrationId);
    }

    public async Task<CandidateIntentionStatement?> GetCandidateRegistrationElectionData(long registrationId)
    {
        return await _registrationRepository.FindCandidateIntentionStatementWithElectionById(registrationId);
    }

    public async Task<ExpenditureExpenseAmount?> GetExpenditureExpenseAmountData(long registrationId)
    {
        return await _registrationRepository.FindExpenditureExpenseAmount(registrationId);
    }

    public Task InitiateAmendRegistration(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task InitiateCandidateAttestation(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<Attestation> InitiateCandidateAttestation(Attestation attestation)
    {
        var createdAttestation = await _attestationRepository.Create(attestation);
        return createdAttestation;
    }


    /// <inheritdoc />
    public Task InitiateLobbyingActivityAuthorization(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task InitiateLobbyistCertificationStatement(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task<long> InitiateRenewRegistration(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task RemoveRegistrationAddress(long addressId, long registrationId)
    {
        await _registrationRepository.RemoveAddress(addressId, registrationId);
    }

    /// <inheritdoc />
    public async Task RemoveRegistrationPhoneNumber(long phoneNumberId, long registrationId)
    {
        await _registrationRepository.RemovePhoneNumber(phoneNumberId, registrationId);
    }

    /// <inheritdoc />
    public Task RenewRegistration(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<bool> IsEligibleToSubmitRegistration(long registrationId)
    {
        var registration = await _registrationRepository.FindById(registrationId);

        if (registration == null)
        {
            throw new HttpRequestException($"Registration with ID {registrationId} not found.", null, System.Net.HttpStatusCode.NotFound);
        }

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            // throw an exception for status not being 'draft'.
            throw new HttpRequestException("Cannot submit a registration that is not in 'Draft' status.", null, System.Net.HttpStatusCode.UnprocessableEntity);
        }
        // Populate attestation entity.
        var attestation = new Attestation
        {
            CreatedBy = 0,
            ExecutedAt = _dateTimeSvc.GetCurrentDateTime(),
            ModifiedBy = 0,
            Name = registration.Name,
            RegistrationId = registrationId,
        };
        var createdAttestation = await _attestationRepository.Create(attestation);
        if (createdAttestation != null && createdAttestation.Id > 0) { return true; }
        return false;
    }

    /// <inheritdoc />
    public async Task SubmitRegistration(long registrationId, RegistrationStatus status)
    {
        var registration = await _registrationRepository.FindById(registrationId);
        await _registrationRepository.UpdateProperty(registration, static r => r.StatusId, status.Id);
    }

    /// <inheritdoc />
    public Task TerminateRegistration(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task UpdateRegistration(Registration registration)
    {
        await _registrationRepository.Update(registration);
    }

    /// <inheritdoc />
    public async Task UpdateRegistrationAddress(Address address, long registrationId)
    {
        await _registrationRepository.UpdateAddress(address, registrationId);
    }

    /// <inheritdoc />
    public async Task UpdateRegistrationPhoneNumber(PhoneNumber phoneNumber, long registrationId)
    {
        await _registrationRepository.UpdatePhoneNumber(phoneNumber, registrationId);
    }

    /// <inheritdoc />
    public Task UpdateRegistrationStatus(long registrationId, RegistrationStatus status)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task WithdrawRegistration(long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<Registration> CancelRegistration(long registrationId)
    {
        var registration = await _registrationRepository.FindById(registrationId) ?? throw new KeyNotFoundException($"Registration with ID {registrationId} not found.");

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            throw new InvalidOperationException($"Cannot cancel a registration that is not in 'Draft' status. Id={registrationId} Status={registration.StatusId}");
        }

        return await _registrationRepository.UpdateProperty(registration, static r => r.StatusId, RegistrationStatus.Canceled.Id);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<Registration>> GetAllCommitteesRegistrations()
    {
        var registrations = await _registrationRepository.GetAll();

        var committees = registrations
            .Where(reg =>
                reg is CandidateControlledCommittee or GeneralPurposeCommittee or PrimarilyFormedCommittee);
        return committees;
    }

    /// <inheritdoc />
    public async Task<Registration> GetLobbyistEmployerEntityRegistration(long id)
    {
        var registrations = await _registrationRepository.GetAll();
        var lobbyistEmployer = registrations.FirstOrDefault(r => r is LobbyistEmployer && r.FilerId is not null && r.CreatedBy == id);
        return lobbyistEmployer;
    }

    /// <inheritdoc />
    public async Task<Registration> GetLobbyistEntityRegistration(long id)
    {
        var registrations = await _registrationRepository.GetAll();
        var lobbyist = registrations.FirstOrDefault(r => r is Lobbyist && r.FilerId is not null && r.CreatedBy == id);
        return lobbyist;
    }

    /// <inheritdoc />
    public async Task<List<RegistrationDashboardDto>> GetMyRegistrations(long userId)
    {
        var registrations = await _dashboardQuerySvc.GetDashboardRegistrationsForUserAsync(userId);
        var amendRegistrations = MappingTypeForAmendment(registrations);
        return MappingTypeForRenewal(amendRegistrations);
    }

    /// <inheritdoc />
    public Task<List<Registration>> GetRegistrationsForFiler(long filerId)
    {
        throw new NotImplementedException();
    }

    private static List<RegistrationDashboardDto> MappingTypeForRenewal(List<RegistrationDashboardDto> amendRegistrations)
    {
        return [..amendRegistrations.Select(r =>
        {
            bool isRenewal = !r.IsNewCertification.GetValueOrDefault(true);
            if (isRenewal)
            {
                r.Type = RegistrationConstants.RegistrationType.LobbyistRenewal;
            }
            return r;
        })];
    }

    private static List<RegistrationDashboardDto> MappingTypeForAmendment(List<RegistrationDashboardDto> list)
    {
        var amendableTypes = new[] {
                RegistrationConstants.RegistrationType.CandidateIntentionStatement,
                RegistrationConstants.RegistrationType.SlateMailerOrganization,
                RegistrationConstants.RegistrationType.Lobbyist
        };

        return [..list.Select(x =>
        {
            if (amendableTypes.Contains(x.Type) && x.Id != x.OriginalId)
            {
                switch (x.Type) {
                    case RegistrationConstants.RegistrationType.CandidateIntentionStatement:
                        x.Type = RegistrationConstants.RegistrationType.CandidateIntentionStatementAmendment;
                        break;
                    case RegistrationConstants.RegistrationType.SlateMailerOrganization:
                        x.Type = RegistrationConstants.RegistrationType.SlateMailerOrganizationAmendment;
                        break;
                    case RegistrationConstants.RegistrationType.Lobbyist:
                        x.Type = RegistrationConstants.RegistrationType.LobbyistAmendment;
                        break;
                    default:
                        break;
                }
            }

            return x;
        })];
    }
}
