// <copyright file="FilingsController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>


using System.Globalization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Services.WebApi.Authorization.ThirdParty.Decisions;
using SOS.CalAccess.Services.WebApi.Filings;
using SOS.CalAccess.Services.WebApi.Shared;

namespace SOS.CalAccess.Services.WebApi.Admin;

/// <summary>
/// API controller responsible for routing admin requests related to filings.
/// </summary>
[Route("api/admin/[controller]")]
[Authorize(Roles = "Admin")]
[ApiController]
[ApiConventionType(typeof(DefaultApiConventions))]
public sealed class FilingsController(
    IAuthorizationService auth,
    IDateTimeSvc dateTimeSvc) : AuthorizationAwareControllerBase(auth)
{
    /// <summary>
    /// Retrieves a list of all filings.
    /// </summary>
    /// <param name="getAllFilings">Command handler.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(Name = nameof(AdminGetFilings))]
    public async Task<ActionResult<FilingListResponse>> AdminGetFilings(
        [FromServices] IGetAllFilings getAllFilings,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([RulesAllowAction.ReadAll<Filing>()]);

        var filings = await getAllFilings.Execute(cancellationToken);

        FilingListResponse response =
            new(filings.Select(u => new FilingItemResponse(u)).ToList());

        return Ok(response);
    }

    /// <summary>
    /// Approves a filing, changing its status to <see cref="FilingStatus.Accepted"/>.
    /// </summary>
    /// <param name="id">The id of the filing to approve.</param>
    /// <param name="handler">The command handler to execute the approval of the filing.</param>
    /// <param name="audit">High level audit facilities.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    /// <exception cref="InvalidOperationException">When an unexpected command result is returned.</exception>
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [HttpPut("{id:long}/approve", Name = nameof(ApproveFiling))]
    public async Task<ActionResult<FilingItemResponse>> ApproveFiling(
        [FromRoute] long id,
        [FromServices] IApproveFiling handler,
        [FromServices] IAuditService audit,
        CancellationToken cancellationToken = default)
    {
        await audit.LogAction(
            BusinessAction.ChangeStatus<Filing>(id.ToString(CultureInfo.InvariantCulture), dateTimeSvc, FilingStatus.Accepted), cancellationToken);

        return await handler.Execute(new IApproveFiling.WithId(id), cancellationToken) switch
        {
            Success<Filing> s => Ok(new FilingItemResponse(s.Value)),
            Failure<Filing>.NotFound n => NotFound(n),
            Failure<Filing>.InvalidState i => BadRequest(i),
            _ => throw new InvalidOperationException("Unexpected command result"),
        };
    }
}
