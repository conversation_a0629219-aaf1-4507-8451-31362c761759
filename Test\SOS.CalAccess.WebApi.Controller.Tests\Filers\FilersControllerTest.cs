using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.WebApi.Filers;

namespace SOS.CalAccess.WebApi.Tests.Filers;
[TestFixture]
public sealed class FilersControllerTest
{
    private IAuthorizationService _authorizationService;
    private IAuditService _auditService;
    private IFilerSvc _filerSvc;
    private FilersController _controller;
    private IDateTimeSvc _dateTimeSvc;

    [SetUp]
    public void SetUp()
    {
        _authorizationService = Substitute.For<IAuthorizationService>();
        _auditService = Substitute.For<IAuditService>();
        _filerSvc = Substitute.For<IFilerSvc>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _controller = new FilersController(_authorizationService, _auditService, _filerSvc, _dateTimeSvc);
    }

    [Test]
    public async Task GetFilerDto()
    {
        var filerDto = new FilerDto
        {
            Id = 10,
            CurrentRegistrationId = 11,
            EffectiveDate = DateTime.UtcNow,
            FilerStatusId = 12,
            FilerTypeId = 13,
        };
        _filerSvc.GetFilerDto(Arg.Is<long>(1)).Returns(filerDto);

        var result = await _controller.GetFilerDto(1);

        Assert.That(result, Is.InstanceOf<FilerDto>());
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.CurrentRegistrationId, Is.EqualTo(11));
            Assert.That(result.FilerStatusId, Is.EqualTo(12));
            Assert.That(result.FilerTypeId, Is.EqualTo(13));
            Assert.That(result.EffectiveDate, Is.InstanceOf<DateTime>());
        });
    }
}
