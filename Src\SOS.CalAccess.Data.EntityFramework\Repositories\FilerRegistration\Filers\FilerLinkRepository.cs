using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using static SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts.IGetAllContacts;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Filers;
public class FilerLinkRepository(DatabaseContext dbContext, IDateTimeSvc dateTimeSvc) : Repository<FilerLink, long>(dbContext), IFilerLinkRepository
{
    /// <summary>
    /// Find FilerLink of a registration application by FilerId and FilerLinkType Id
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <param name="filerLinkTypeId">ID of filer type link</param>
    /// <returns></returns>
    public async Task<FilerLink?> FindByFilerIdAndLinkType(long filerId, long filerLinkTypeId)
    {
        return await dbContext.Set<FilerLink>()
            .Where(w => w.FilerId == filerId && w.FilerLinkTypeId == filerLinkTypeId)
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc />
    public async Task<FilerLink?> LinkControlledCommitteeToCandidateIntentionStatement(long filerId, long controlledCommitteeFilerId, long? userId)
    {
        // Get or create FilerLink
        var filerLink = await dbContext.Set<FilerLink>()
            .FirstOrDefaultAsync(fl =>
                fl.FilerId == filerId &&
                fl.FilerLinkTypeId == FilerLinkType.ControlledCommittee.Id);

        if (filerLink is null)
        {
            filerLink = new FilerLink
            {
                FilerId = filerId,
                LinkedEntityId = controlledCommitteeFilerId,
                FilerLinkTypeId = FilerLinkType.ControlledCommittee.Id,
                EffectiveDate = dateTimeSvc.GetCurrentDateTime(),
                CreatedBy = userId ?? 0,
                ModifiedBy = userId ?? 0
            };
            dbContext.Set<FilerLink>().Add(filerLink);
        }
        else
        {
            filerLink.LinkedEntityId = controlledCommitteeFilerId;
        }

        await dbContext.SaveChangesAsync();
        return filerLink;
    }

    /// <inheritdoc />
    public async Task<FilerLink?> LinkEntityTypeToFiler(long filerId, long linkedEntityId, FilerLinkType linkType, long? userId, List<FilerLinkType>? entityInLinkType = null)
    {
        var query = dbContext.Set<FilerLink>()
            .Where(fl => fl.FilerId == filerId);

        if (entityInLinkType?.Count > 0)
        {
            var linkTypeFilter = entityInLinkType.Select(flt => flt.Id).ToList();
            query = query.Where(fl => linkTypeFilter.Contains(fl.FilerLinkTypeId));
        }
        else
        {
            query = query.Where(fl => fl.FilerLinkTypeId == linkType.Id);
        }

        var filerLink = await query.FirstOrDefaultAsync();

        if (filerLink is null)
        {
            filerLink = new FilerLink
            {
                FilerId = filerId,
                LinkedEntityId = linkedEntityId,
                FilerLinkTypeId = linkType.Id,
                EffectiveDate = dateTimeSvc.GetCurrentDateTime(),
                CreatedBy = userId ?? 0,
                ModifiedBy = userId ?? 0
            };
            dbContext.Set<FilerLink>().Add(filerLink);
        }
        else
        {
            filerLink.LinkedEntityId = linkedEntityId;
            if (filerLink.FilerLinkTypeId != linkType.Id)
            {
                filerLink.FilerLinkTypeId = linkType.Id;
            }
        }

        await dbContext.SaveChangesAsync();
        return filerLink;
    }

    public async Task<bool> UnlinkLobbyistFromEmployer(long employerRegistrationId, long lobbyistRegistrationId)
    {
        var filerLink = await (from le in dbContext.LobbyistEmployers
                               join fe in dbContext.Filers on le.FilerId equals fe.Id
                               join fl in dbContext.FilerLinks on fe.Id equals fl.LinkedEntityId
                               join fil in dbContext.Filers on fl.FilerId equals fil.Id
                               join l in dbContext.Lobbyists on fil.Id equals l.FilerId
                               where fe.FilerTypeId == FilerType.LobbyistEmployer.Id && le.Id == employerRegistrationId && l.Id == lobbyistRegistrationId
                               select fl
                            ).FirstOrDefaultAsync();

        if (filerLink is not null)
        {
            filerLink.Active = false;

            dbContext.FilerLinks.Update(filerLink);

            return await dbContext.SaveChangesAsync() > 0;
        }

        throw new KeyNotFoundException($"Lobbyist registration Id={lobbyistRegistrationId} not linked to employer registration Id={employerRegistrationId}");
    }

    /// <inheritdoc />
    public async Task<List<FilerLink>> LinkLobbyistEmployerRegistrationToLobbyingFirms(long filerId, List<long> lobbyingFirmFilerIds, long userId)
    {
        var filerLinks = lobbyingFirmFilerIds.Select(id => new FilerLink
        {
            FilerId = filerId,
            LinkedEntityId = id,
            FilerLinkTypeId = FilerType.LobbyingFirm.Id,
            EffectiveDate = dateTimeSvc.GetCurrentDateTime(),
            CreatedBy = userId,
            ModifiedBy = userId
        }).ToList();

        dbContext.Set<FilerLink>().AddRange(filerLinks);
        await dbContext.SaveChangesAsync();
        return filerLinks;
    }
}
