using System.Reflection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Localization;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Constants;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices.SmoCampaignStatementCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(nameof(SmoCampaignStatementController))]
internal sealed class SmoCampaignStatementControllerTests : IDisposable
{
    private ISmoCampaignStatementCtlSvc _smoCampaignStatementCtlSvcMock;
    private ISmoCampaignStatementSvc _smoCampaignStatementSvcMock;
    private ISmoRegistrationSvc _smoRegistrationSvcMock;
    private Generated.IContactsApi _contactsApiMock;
    private IStringLocalizer<SharedResources> _localizerMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IToastService _toastServiceMock;
    private ICandidateSvc _candidateSvcMock;
    private IBallotMeasureSvc _ballotMeasureSvcMock;
    private IAccuMailValidatorService _accuMailValidatorSvcMock;
    private DateTime _dateNow;

    private SmoCampaignStatementController _controller;

    public void Dispose()
    {
        _controller.Dispose();
    }

    [SetUp]
    public void Setup()
    {
        _smoCampaignStatementCtlSvcMock = Substitute.For<ISmoCampaignStatementCtlSvc>();
        _smoCampaignStatementSvcMock = Substitute.For<ISmoCampaignStatementSvc>();
        _smoRegistrationSvcMock = Substitute.For<ISmoRegistrationSvc>();
        _contactsApiMock = Substitute.For<Generated.IContactsApi>();
        _localizerMock = Substitute.For<IStringLocalizer<SharedResources>>();
        _toastServiceMock = Substitute.For<IToastService>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _candidateSvcMock = Substitute.For<ICandidateSvc>();
        _ballotMeasureSvcMock = Substitute.For<IBallotMeasureSvc>();
        _accuMailValidatorSvcMock = Substitute.For<IAccuMailValidatorService>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        var localizedString = new LocalizedString("key", "text");
        _localizerMock[Arg.Any<string>()].Returns(localizedString);

        var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());

        _controller = new SmoCampaignStatementController(
            _smoCampaignStatementCtlSvcMock,
            _smoCampaignStatementSvcMock,
            _smoRegistrationSvcMock,
            _contactsApiMock,
            _localizerMock,
            _authorizationSvcMock,
            _toastServiceMock,
            _candidateSvcMock,
            _accuMailValidatorSvcMock,
            _ballotMeasureSvcMock)
        {
            TempData = tempData
        };
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    [Test]
    public async Task OnActionExecuting_ShouldDetermineIsAmendFromQuery()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var routeData = new RouteData();
        var actionDescriptor = new ActionDescriptor();
        var actionArguments = new Dictionary<string, object?>
        {
            { "filingId", 1 }
        };
        var actionContext = new ActionContext(httpContext, routeData, actionDescriptor);
        var context = new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            actionArguments,
            _controller
        );
        var next = new ActionExecutionDelegate(() =>
        {
            var executedContext = new ActionExecutedContext(context, new List<IFilterMetadata>(), controller: _controller);
            return Task.FromResult(executedContext);
        });

        // Act 
        await _controller.OnActionExecutionAsync(context, next);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(_controller.ViewData.ContainsKey(LayoutConstants.Title), Is.True);
            Assert.That(_controller.ViewData[LayoutConstants.Title], Is.Not.Null);
        });
    }

    [Test]
    public async Task OnActionExecuting_ShouldDetermineIsAmendFromModelOrParameter()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var routeData = new RouteData();
        var actionDescriptor = new ActionDescriptor();
        var actionArguments = new Dictionary<string, object?>
        {
            { "model", new SmoCampaignStatementTransactionEntryViewModel() { Id = 1 } }
        };
        var actionContext = new ActionContext(httpContext, routeData, actionDescriptor);
        var context = new ActionExecutingContext(
            actionContext,
            new List<IFilterMetadata>(),
            actionArguments,
            _controller
        );
        var next = new ActionExecutionDelegate(() =>
        {
            var executedContext = new ActionExecutedContext(context, new List<IFilterMetadata>(), controller: _controller);
            return Task.FromResult(executedContext);
        });

        // Act 
        await _controller.OnActionExecutionAsync(context, next);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(_controller.ViewData.ContainsKey(LayoutConstants.Title), Is.True);
            Assert.That(_controller.ViewData[LayoutConstants.Title], Is.Not.Null);
        });
    }


    [Test]
    public void RedirectToDashboard_ReturnsRedirectToAction()
    {
        // Act
        var result = _controller.RedirectToDashboard();

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    #region Shared
    [Test]
    public void Index_ValidModelState_RedirectsToReviewPage()
    {
        // Act
        var result = _controller.Index();

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Cancel_InvalidShouldReturnNotFound()
    {
        // Arrange
        var id = 1;
        _controller.ModelState.AddModelError("KEY", "ERROR");

        // Act
        var result = await _controller.Cancel(id, default) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task Cancel_ValidShouldRedirectToDashboard()
    {
        // Arrange
        var id = 1;

        // Act
        var result = await _controller.Cancel(id, default) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
    }

    [Test]
    public void Edit_ValidShouldRedirectToReview()
    {
        // Arrange
        var id = 1;

        // Act
        var result = _controller.Edit(id) as RedirectToActionResult;
        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void Edit_InvalidShouldReturnNotFound()
    {
        // Arrange
        var id = 1;
        _controller.ModelState.AddModelError("KEY", "ERROR");
        // Act
        var result = _controller.Edit(id) as NotFoundResult;
        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void OnActionExecuting_ShouldCallRedirectToDashboard()
    {
        // Arrange
        var actionContext = new ActionExecutingContext(
            new ActionContext
            {
                HttpContext = new DefaultHttpContext(),
                RouteData = new Microsoft.AspNetCore.Routing.RouteData(),
                ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
            },
            new List<IFilterMetadata>(),
            new Dictionary<string, object>()!,
            _controller
        );

        // Act & Assert
        Assert.DoesNotThrow(() => _controller.OnActionExecuting(actionContext));
    }

    [Test]
    public void CancelDiscardDraft_ReturnsRedirectToActionResult()
    {
        // Arrange
        var filingId = 1;

        // Act
        var result = _controller.CancelDiscardDraft(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(_controller.Review)));
        });
    }

    [Test]
    public void CancelDiscardDraft_ReturnsNotFound()
    {
        // Arrange
        var filingId = 1;
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = _controller.CancelDiscardDraft(filingId);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void UnderConstruction_ReturnsView()
    {
        // Act
        var result = _controller.UnderConstruction();

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Selection_Get
    [Test]
    public void Selection_Get_ReturnView()
    {
        // Act
        var result = _controller.Selection();

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task SearchSmoByIdOrName_ShouldReturnResult_WhenModelStateIsValid()
    {
        // Arrange
        var search = "";
        var expected = new List<SmoRegistrationBasicResponseDto>
        {
            new()
            {
                Id = 1,
                Name = "Test"
            }
        };
        _smoRegistrationSvcMock
            .SearchSmoRegistrationByIdOrNameAsync(search, null)
            .Returns(Task.FromResult(expected));

        // Act
        var result = await _controller.SearchSmoByIdOrName(search, default);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result.Value, Is.InstanceOf<List<SmoRegistrationBasicResponseDto>>());
        Assert.That(result.Value, Is.Not.Empty);
    }

    [Test]
    public async Task SearchSmoByIdOrName_ShouldReturnResult_WhenModelStateIsInValid()
    {
        // Arrange
        var search = "";
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.SearchSmoByIdOrName(search, default);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result.Value, Is.Empty);
    }
    #endregion

    #region Selection_Post
    [Test]
    public async Task Selection_Post_ReturnRedirect_OnFormActionCreate_WhenIdsHasValue()
    {
        // Arrange
        var model = new SmoCampaignStatementSelectionViewModel()
        {
            Action = FormAction.Create,
            RegistrationId = 1,
            FilerId = 1
        };

        // Act
        var result = await _controller.Selection(model);
        var redirectResult = (RedirectToActionResult)result;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(redirectResult.ActionName, Is.EqualTo("Review"));
        });
    }

    [Test]
    public async Task Selection_Post_ReturnRedirect_OnFormActionCreate_WhenIdsHasNoValue()
    {
        // Arrange
        var model = new SmoCampaignStatementSelectionViewModel()
        {
            Action = FormAction.Create,
            RegistrationId = null,
            FilerId = null
        };

        // Act
        var result = await _controller.Selection(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Selection_Post_ReturnRedirect_OnFormActionCreate_WhenInvalidModelState()
    {
        // Arrange
        var model = new SmoCampaignStatementSelectionViewModel()
        {
            Action = FormAction.Create,
            RegistrationId = 1,
            FilerId = 1
        };
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.Selection(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Selection_Post_ReturnRedirect_OnFormActionDefault()
    {
        // Arrange
        var model = new SmoCampaignStatementSelectionViewModel()
        {
            Action = null,
            RegistrationId = 1,
        };

        // Act
        var result = await _controller.Selection(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Review_Get
    [Test]
    public async Task Review_Get_ModeStateValid_ReturnsViewModel_WhenModelIsNotNull()
    {
        // Arrange
        var newModel = new SmoCampaignStatementReviewViewModel();

        _smoCampaignStatementCtlSvcMock
            .GetCompleteReviewPageViewModel(1)
            .Returns(new SmoCampaignStatementReviewViewModel() { Id = 1 });

        // Act
        var result = await _controller.Review(1, newModel);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Review_Get_ModeStateInvalid_ReturnsViewModel_WhenModelIsNull()
    {
        // Arrange
        var id = 1;
        List<SelectListItem> unreportedFilingPeriods = new()
        {
            new() { Value = "1", Text = "Test" }
        };
        _smoCampaignStatementCtlSvcMock
            .GetUnreportedFilingPeriods(id)
            .Returns(unreportedFilingPeriods);

        // Act
        var result = await _controller.Review(1);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Review_Get_ModeStateInvalid_ReturnsViewModel_WhenModelStateInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.Review(1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Review_WhenIsAmendingTrue_ButCompleteModelIsNull_ReturnsView()
    {
        // Arrange
        const long filingId = 123;
        var model = new SmoCampaignStatementReviewViewModel
        {
            FilerId = 1,
            IsAmending = true
        };

        _smoCampaignStatementCtlSvcMock
            .GetCompleteReviewPageViewModel(filingId)
            .Returns(Task.FromResult<SmoCampaignStatementReviewViewModel?>(model));

        _authorizationSvcMock
            .IsAuthorized(Arg.Any<AuthorizationRequest>())
            .Returns(true);

        // Act
        var result = await _controller.Review(filingId, model);

        // Assert
        Assert.That(result, Is.TypeOf<ViewResult>());
    }

    [Test]
    public async Task Review_WhenIsAmendingTrue_AndCompleteModelNotNull_SetsFlagAndReturnsView()
    {
        // Arrange
        const long filingId = 456;
        var incoming = new SmoCampaignStatementReviewViewModel
        {
            IsAmending = true
        };

        var completeModel = new SmoCampaignStatementReviewViewModel
        {
            Id = filingId,
            IsAmending = false
        };

        _smoCampaignStatementCtlSvcMock
            .GetCompleteReviewPageViewModel(filingId)
            .Returns(Task.FromResult<SmoCampaignStatementReviewViewModel?>(completeModel));

        // Act
        var result = await _controller.Review(filingId, incoming) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Model, Is.SameAs(completeModel));
            Assert.That(completeModel.IsAmending, Is.False);
        });
    }

    [Test]
    public async Task Review_WhenIsAmendingFalse_AndCompleteModelNotNull_DoesNotSetFlag()
    {
        // Arrange
        const long filingId = 789;
        var incoming = new SmoCampaignStatementReviewViewModel
        {
            IsAmending = false
        };

        var completeModel = new SmoCampaignStatementReviewViewModel
        {
            Id = filingId,
            IsAmending = false
        };

        _smoCampaignStatementCtlSvcMock
            .GetCompleteReviewPageViewModel(filingId)
            .Returns(Task.FromResult<SmoCampaignStatementReviewViewModel?>(completeModel));

        // Act
        var result = await _controller.Review(filingId, incoming)
                                     as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Model, Is.SameAs(completeModel));
            Assert.That(completeModel.IsAmending, Is.False);
        });
    }
    #endregion

    #region Review_Post
    [Test]
    public void Review_Post_FormActionSubmit_ShouldRedirectToVerification()
    {
        // Arrange
        var model = new SmoCampaignStatementReviewViewModel()
        {
            Id = 1,
            Action = FormAction.Submit
        };

        // Act
        var result = _controller.Review(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(result?.ActionName, Is.EqualTo("Verification"));
        });
    }

    [Test]
    public void Review_Post_FormActionSaveAndClose_ShouldReturnView()
    {
        // Arrange
        var model = new SmoCampaignStatementReviewViewModel()
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.Review(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Review_Post_SelectFilingPeriodOption_ReturnsRedirectToAction_WhenModelStateValid()
    {
        // Arrange
        var model = new SmoCampaignStatementReviewViewModel()
        {
            Id = 1,
        };

        // Act
        var result = await _controller.SelectFilingPeriodOption(model, 1);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task Review_Post_SelectFilingPeriodOption_ReturnsRedirectToActionWhenModelStateInvalid()
    {
        // Arrange
        var model = new SmoCampaignStatementReviewViewModel()
        {
            Id = 1,
        };
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.SelectFilingPeriodOption(model, 1);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }


    [Test]
    public void SaveReview_ShouldRedirectToDashboard()
    {
        // Arrange
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = _controller.SaveReview() as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }


    [Test]
    public async Task MarkFilingSummaryAsNothingToReport_ShouldRedirectToReview_WhenFilingSummaryIsNotNull()
    {
        // Arrange
        await _smoCampaignStatementCtlSvcMock.MarkFilingSummaryAsNothingToReport(1, 2);

        // Act
        var result = await _controller.MarkFilingSummaryAsNothingToReport(1, 2) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Review"));
    }

    [Test]
    public async Task MarkFilingSummaryAsNothingToReport_ShouldRedirectToReview_WhenFilingSummaryIsNull()
    {
        // Act
        var result = await _controller.MarkFilingSummaryAsNothingToReport(1, null) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.That(result.ActionName, Is.EqualTo("Review"));
    }
    #endregion

    #region General Information
    [Test]
    public void CampaignStatementSmoViewModel_CanAssignValues()
    {
        // Arrange
        var model = new SmoCampaignStatementGeneralInfoViewModel
        {
            Id = 42,
            Action = FormAction.SaveAndClose,
            OrganizationName = "Test Org",
            Email = "<EMAIL>",
            PhoneNumber = "1234567890",
            FaxNumber = "0987654321",
            TreasurerName = "John Doe",
            TreasurerPhoneNumber = "1112223333",
            TreasurerEmail = "<EMAIL>",
            TreasurerFaxNumber = "4445556666",
            RecipientCommitteeName = "Recipient Org",
            RecipientCommitteeId = "RC123"
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(42));
            Assert.That(model.Action, Is.EqualTo(FormAction.SaveAndClose));
            Assert.That(model.OrganizationName, Is.EqualTo("Test Org"));
            Assert.That(model.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(model.PhoneNumber, Is.EqualTo("1234567890"));
            Assert.That(model.FaxNumber, Is.EqualTo("0987654321"));
            Assert.That(model.TreasurerName, Is.EqualTo("John Doe"));
            Assert.That(model.TreasurerPhoneNumber, Is.EqualTo("1112223333"));
            Assert.That(model.TreasurerEmail, Is.EqualTo("<EMAIL>"));
            Assert.That(model.TreasurerFaxNumber, Is.EqualTo("4445556666"));
            Assert.That(model.RecipientCommitteeName, Is.EqualTo("Recipient Org"));
            Assert.That(model.RecipientCommitteeId, Is.EqualTo("RC123"));
        });
    }

    [Test]
    public void CampaignStatementGeneralInformationSmoViewModel_CanAssignValues()
    {
        // Arrange
        var generalInformationDto = new SmoGeneralInformationResponseDto
        {
            RegistrationDetail = new SmoRegistrationResponseDto
            {
                Id = 42,
                Name = "Test Org",
                Email = "<EMAIL>",
                PhoneNumbers = new()
                {
                    new() { Type = "Home", Number = "1234567890" },
                    new() { Type = "Fax", Number = "0987654321" }
                },
                Address = new()
                {
                    new() { Purpose = "Organization", Street = "123 Org St", City = "Org City", State = "OS", Zip = "12345", Country = "US" },
                    new() { Purpose = "Mailing", Street = "456 Mail St", City = "Mail City", State = "MS", Zip = "67890", Country = "US" }
                }
            },
            Treasurer = new SmoRegistrationContactDto
            {
                FirstName = "John",
                MiddleName = "A.",
                LastName = "Doe",
                Email = "<EMAIL>",
                PhoneNumber = new() { Number = "1112223333", Type = "Mobile" },
                Address = new() { Street = "789 Treasurer St", City = "Treasurer City", State = "TS", Zip = "54321", Country = "US" }
            },
            CommitteeDetail = new CommitteeSearchResultDto
            {
                Name = "Recipient Org",
                Id = 123,
                Addresses = new List<AddressDto>()
            }
        };

        var model = new SmoCampaignStatementGeneralInfoViewModel(1, generalInformationDto);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(1));
            Assert.That(model.OrganizationName, Is.EqualTo("Test Org"));
            Assert.That(model.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(model.PhoneNumber, Is.EqualTo("1234567890"));
            Assert.That(model.FaxNumber, Is.EqualTo("0987654321"));

            // Checking for Address Mapping
            Assert.That(model.OrganizationAddress?.Street, Is.EqualTo("123 Org St"));
            Assert.That(model.OrganizationAddress?.City, Is.EqualTo("Org City"));
            Assert.That(model.OrganizationAddress?.State, Is.EqualTo("OS"));
            Assert.That(model.OrganizationAddress?.Zip, Is.EqualTo("12345"));
            Assert.That(model.OrganizationAddress?.Country, Is.EqualTo("US"));

            Assert.That(model.MailingAddress?.Street, Is.EqualTo("456 Mail St"));
            Assert.That(model.MailingAddress?.City, Is.EqualTo("Mail City"));
            Assert.That(model.MailingAddress?.State, Is.EqualTo("MS"));
            Assert.That(model.MailingAddress?.Zip, Is.EqualTo("67890"));
            Assert.That(model.MailingAddress?.Country, Is.EqualTo("US"));

            // Checking Treasurer Info
            Assert.That(model.TreasurerName, Is.EqualTo("John A. Doe"));
            Assert.That(model.TreasurerPhoneNumber, Is.EqualTo("1112223333"));
            Assert.That(model.TreasurerEmail, Is.EqualTo("<EMAIL>"));
            Assert.That(model.TreasurerFaxNumber, Is.EqualTo("N/A"));

            // Committee Details
            Assert.That(model.RecipientCommitteeName, Is.EqualTo("Recipient Org"));
            Assert.That(model.RecipientCommitteeId, Is.EqualTo("123"));
        });
    }

    [Test]
    public void CampaignStatementGeneralInformationSmoViewModel_WhenTreasurerPhoneNumberIsFax_ShouldAssignFaxCorrectly()
    {
        var generalInformationDto = new SmoGeneralInformationResponseDto
        {
            RegistrationDetail = new SmoRegistrationResponseDto
            {
                Id = 1,
                Name = "Test Org"
            },
            Treasurer = new SmoRegistrationContactDto
            {
                FirstName = "Jane",
                LastName = "Doe",
                PhoneNumber = new()
                {
                    Number = "5556667777",
                    Type = "Fax"
                }
            }
        };

        var model = new SmoCampaignStatementGeneralInfoViewModel(1, generalInformationDto);

        Assert.That(model.TreasurerFaxNumber, Is.EqualTo("5556667777"));
    }

    [Test]
    public async Task GeneralInformation_Get_ModeStateValid_ReturnsViewModel()
    {
        // Act
        var result = await _controller.GeneralInformation(1);

        // Assert
        Assert.That(result, Is.InstanceOf<IActionResult>());
    }

    [Test]
    public async Task GeneralInformation_Get_ModeStateInvalid_ReturnsViewModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.GeneralInformation(1);

        // Assert
        Assert.That(result, Is.InstanceOf<IActionResult>());
    }

    [Test]
    public void GeneralInformation_Post_ModelStateValid_SaveAndClose_RedirectsToReview()
    {
        // Arrange
        var model = new SmoCampaignStatementGeneralInfoViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.GeneralInformation(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect!.ActionName, Is.EqualTo(nameof(_controller.Review)));
    }

    [Test]
    public void GeneralInformation_Post_ModelStateValid_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementGeneralInfoViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.GeneralInformation(model);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState[string.Empty]?.Errors.Count, Is.GreaterThan(0));
        });
        var viewResult = result as ViewResult;
        Assert.That(viewResult!.Model, Is.EqualTo(model));
    }

    [Test]
    public void GeneralInformation_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("SomeField", "Some error");
        var model = new SmoCampaignStatementGeneralInfoViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.GeneralInformation(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.That(viewResult!.Model, Is.EqualTo(model));
    }
    #endregion

    #region Filing Summary
    [Test]
    public void FilingSummary_SetAndGetValues()
    {
        // Arrange
        var model = new SmoCampaignStatementFilingSummaryViewModel
        {
            Id = 123,
            Action = FormAction.SaveAndClose,
            TotalPaymentsReceivedThisStatement = 1000.00m,
            TotalPaymentsReceivedYearToDate = 5000.00m,
            TotalPaymentsMadeThisStatement = 750.00m,
            TotalPaymentsMadeYearToDate = 3200.00m,
            Rows = new List<FilingSummaryRow>
        {
            new()
            {
                Label = "Filing Summary",
                Amount = new FilingSummaryAmount
                {
                    ThisStatement = "$1,000",
                    YearToDate = "$5,000"
                }
            }
        }
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(123));
            Assert.That(model.Action, Is.EqualTo(FormAction.SaveAndClose));
            Assert.That(model.TotalPaymentsReceivedThisStatement, Is.EqualTo(1000.00m));
            Assert.That(model.TotalPaymentsReceivedYearToDate, Is.EqualTo(5000.00m));
            Assert.That(model.TotalPaymentsMadeThisStatement, Is.EqualTo(750.00m));
            Assert.That(model.TotalPaymentsMadeYearToDate, Is.EqualTo(3200.00m));
            Assert.That(model.Rows[0].Label, Is.EqualTo("Filing Summary"));
            Assert.That(model.Rows[0].Amount.ThisStatement, Is.EqualTo("$1,000"));
            Assert.That(model.Rows[0].Amount.YearToDate, Is.EqualTo("$5,000"));
        });
    }

    [Test]
    public void FilingSummary_CtorWithDto_SetsPaymentReceivedSummaryFields()
    {
        // Arrange
        var dtos = new List<FilingSummaryResponseDto>
        {
            new()
            {
                FilingSummaryTypeName = "PaymentReceivedSummary" ,
                PeriodAmount = 1200m,
                ToDateAmount = 4800m
            },
            new()
            {
                FilingSummaryTypeName = "PaymentMadeSummary" ,
                PeriodAmount = 800m,
                ToDateAmount = 3000m
            }
        };

        // Act
        var model = new SmoCampaignStatementFilingSummaryViewModel(1, dtos);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.TotalPaymentsReceivedThisStatement, Is.EqualTo(1200m));
            Assert.That(model.TotalPaymentsReceivedYearToDate, Is.EqualTo(4800m));
            Assert.That(model.TotalPaymentsMadeThisStatement, Is.EqualTo(800m));
            Assert.That(model.TotalPaymentsMadeYearToDate, Is.EqualTo(3000m));
        });
    }

    [Test]
    public async Task FilingSummary_Get_ModeStateValid_ReturnsViewModel()
    {
        // Act
        var result = await _controller.FilingSummary(1);

        // Assert
        Assert.That(result, Is.InstanceOf<IActionResult>());
    }

    [Test]
    public async Task FilingInformation_Get_ModeStateInvalid_ReturnsViewModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.FilingSummary(1);

        // Assert
        Assert.That(result, Is.InstanceOf<IActionResult>());
    }

    [Test]
    public void FilingSummary_Post_ModelStateValid_SaveAndClose_RedirectsToReview()
    {
        // Arrange
        var model = new SmoCampaignStatementFilingSummaryViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.FilingSummary(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect!.ActionName, Is.EqualTo(nameof(_controller.Review)));
    }

    [Test]
    public void FilingSummary_Post_ModelStateValid_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementFilingSummaryViewModel
        {
            Id = 1,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.FilingSummary(model);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState[string.Empty]?.Errors.Count, Is.GreaterThan(0));
        });
        var viewResult = result as ViewResult;
        Assert.That(viewResult!.Model, Is.EqualTo(model));
    }

    [Test]
    public void FilingSummary_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("SomeField", "Some error");
        var model = new SmoCampaignStatementFilingSummaryViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = _controller.FilingSummary(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.That(viewResult!.Model, Is.EqualTo(model));
    }
    #endregion

    #region Payment Made
    [Test]
    public async Task PaymentMade01_Get_ModelStateValid_ReturnsViewWithExpectedModel()
    {
        // Arrange
        const long filingId = 123;
        const long filerId = 123;

        _smoCampaignStatementCtlSvcMock
            .GetFilerContactViewModelAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<string>())
            .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel()));

        // Act
        var result = await _controller.PaymentMade01(filingId, filerId, null, null) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<SmoCampaignStatementTransactionEntryViewModel>());
            var model = result.Model as SmoCampaignStatementTransactionEntryViewModel;
            Assert.That(model!.ScreenType, Is.EqualTo(SmoCampaignStatementScreenTypes.PaymentMade));
        });
    }

    [Test]
    public async Task PaymentMade01_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PaymentMade01(1L, 1L, null, null);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void PaymentMade01_Post_ModelStateValid_CancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 101,
            FilerId = 103,
            Action = FormAction.Cancel
        };

        // Act
        var result = _controller.PaymentMade01(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("TransactionSummary"));
            Assert.That(result.RouteValues!["filingId"], Is.EqualTo(model.Id));
            Assert.That(result.RouteValues!["summaryType"], Is.EqualTo("PaymentMadeSummary"));
        });
    }

    [Test]
    public void PaymentMade01_Post_ModelStateValid_ContinueAction_RedirectsToPaymentMade02()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.PaymentMade01(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("PaymentMade02"));
        });
    }

    [Test]
    public void PaymentMade01_Post_ModelStateValid_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = (FormAction)999
        };

        // Act
        var result = _controller.PaymentMade01(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
            Assert.That(_controller.ModelState[string.Empty]!.Errors[0].ErrorMessage, Is.EqualTo("text"));
        });
    }

    [Test]
    public void PaymentMade01_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel();
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = _controller.PaymentMade01(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task PaymentMade02_Get_ModelStateValid_ReturnsViewWithExpectedModel()
    {
        // Arrange
        const long filingId = 456;
        const long contactId = 123;
        var expectedModel = new SmoCampaignStatementTransactionEntryViewModel
        {
            ScreenType = SmoCampaignStatementScreenTypes.PaymentMade,
            Id = filingId
        };

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };

        _smoCampaignStatementCtlSvcMock
            .GetFilerContactViewModelAsync(Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<string>())
            .Returns(expectedModel);

        // Act
        var result = await _controller.PaymentMade02(filingId, contactId, null, null) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<SmoCampaignStatementTransactionEntryViewModel>());
            var model = result.Model as SmoCampaignStatementTransactionEntryViewModel;
            Assert.That(model!.ScreenType, Is.EqualTo(SmoCampaignStatementScreenTypes.PaymentMade));
            Assert.That(model.Id, Is.EqualTo(filingId));
        });
    }

    [Test]
    public async Task PaymentMade02_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "error message");
        _smoCampaignStatementCtlSvcMock
           .GetFilerContactViewModelAsync(Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<string>())
           .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel()));

        // Act
        var result = await _controller.PaymentMade02(1L, 1L, null, null);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PaymentMade02_Post_ModelStateValid_CancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 789,
            Action = FormAction.Cancel
        };

        // Act
        var result = await _controller.PaymentMade02(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("TransactionSummary"));
            Assert.That(result.RouteValues!["filingId"], Is.EqualTo(789));
        });
    }

    [Test]
    public async Task PaymentMade02_Post_ModelStateValid_ContinueAction_RedirectsToPaymentMade03()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            ParticipantType = "Individual"
        };

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };

        _smoCampaignStatementCtlSvcMock
            .SaveOrUpdateContactAsync(Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(), Arg.Any<string>(), Arg.Any<ModelStateDictionary>())
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.PaymentMade02(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("PaymentMade03"));
        });
    }

    [Test]
    public async Task PaymentMade02_Post_ModelStateValid_PreviousAction_RedirectsToPaymentMade01()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Previous
        };

        // Act
        var actionResult = await _controller.PaymentMade02(model, CancellationToken.None);
        var result = actionResult as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo("PaymentMade01"));
        });
    }

    [Test]
    public async Task PaymentMade02_Post_ModelStateValid_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = (FormAction)999
        };

        // Act
        var actionResult = await _controller.PaymentMade02(model, CancellationToken.None);
        var result = actionResult as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task PaymentMade02_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel();
        _controller.ModelState.AddModelError("field", "error");

        // Act
        var actionResult = await _controller.PaymentMade02(model, CancellationToken.None);
        var result = actionResult as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task PaymentMade02_Post_ActionContinue_SaveFails_ReturnsViewWithModel()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue
        };

        _smoCampaignStatementCtlSvcMock
            .SaveOrUpdateContactAsync(model, Arg.Any<string>(), Arg.Any<ModelStateDictionary>())
            .Returns(call =>
            {
                var modelState = call.Arg<ModelStateDictionary>();
                modelState.AddModelError("key", "error");
                return Task.CompletedTask;
            });

        // Act
        var result = await _controller.PaymentMade02(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task PaymentMade03_Get_ModelStateIsValid_ReturnsViewWithExpectedModel()
    {
        // Arrange
        var filingId = 1L;
        var contactId = 1L;
        _smoCampaignStatementCtlSvcMock
            .GetPaymentMade03ViewModelAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long?>())
            .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel(filingId, contactId, null)));

        // Act
        var result = await _controller.PaymentMade03(filingId, contactId, null, true) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<SmoCampaignStatementTransactionEntryViewModel>());
        });
    }

    [Test]
    public async Task PaymentMade03_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "error message");
        _smoCampaignStatementCtlSvcMock
           .GetPaymentMade03ViewModelAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long?>())
           .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel()));

        // Act
        var result = await _controller.PaymentMade03(1L, 1L, null);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PaymentMade03_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error");
        var model = new SmoCampaignStatementTransactionEntryViewModel();

        // Act
        var result = await _controller.PaymentMade03(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task PaymentMade03_ActionIsPrevious_RedirectsToPaymentMade02()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Previous };

        // Act
        var result = await _controller.PaymentMade03(model) as RedirectToActionResult;

        // Assert
        Assert.That(result!.ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.PaymentMade02)));
    }

    [Test]
    public async Task PaymentMade03_ActionIsCancel_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Cancel,
            ScreenType = SmoCampaignStatementScreenTypes.PaymentMadeByAgent
        };

        // Act
        var result = await _controller.PaymentMade03(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
            Assert.That(result.RouteValues!.ContainsKey("summaryType"), Is.True);
            Assert.That(result.RouteValues["summaryType"], Is.EqualTo("PaymentMadeByAgentOrIndependentContractorSummary"));
        });
    }

    [Test]
    public async Task PaymentMade03_ActionIsInvalid_AddsModelErrorAndReturnsView()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = (FormAction)999 };

        // Act
        var result = await _controller.PaymentMade03(model) as ViewResult;

        // Assert
        Assert.That(_controller.ModelState[string.Empty], Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(_controller.ModelState[string.Empty].Errors, Has.Count.GreaterThan(0));
        });
    }

    [Test]
    public async Task PaymentMade03_Post_SaveAndClose_IsPaidByAgentOrContractorFalse_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.SaveAndClose,
        };
        _ = _smoCampaignStatementCtlSvcMock
            .SaveTransactionAsync(Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<string>());

        // Act
        var result = await _controller.PaymentMade03(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
            Assert.That(result.RouteValues?.ContainsKey("summaryType"), Is.True);
            Assert.That(result.RouteValues?["summaryType"], Is.EqualTo("PaymentMadeSummary"));
        });
    }

    [Test]
    public async Task PaymentMade03_Post_SaveAndClose_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.SaveAndClose,
            AgentOrIndependentContractorName = "name",
        };
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.PaymentMade03(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task PaymentMade03_Post_SaveAndClose_IsPaidByAgentOrContractorTrue_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.SaveAndClose,
            AgentOrIndependentContractorName = "name",
        };

        // Act
        var result = await _controller.PaymentMade03(model) as RedirectToActionResult;

        // Assert
        Assert.That(result!.ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
    }

    #endregion

    #region Payment Received 01
    [Test]
    public void PaymentsReceived_SetAndGetValues()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 456,
            Action = FormAction.SaveAndClose,
            ContactName = "John Doe",
            ContactId = 1001,
            AddressTitleResourceKey = "Address_Title",
            ParticipantType = "Individual",
            FirstName = "John",
            MiddleName = "M.",
            LastName = "Doe",
            Employer = "Example Inc.",
            CommitteeId = 3001,
            CandidateId = 4001,
            CandidateName = "Jane A. Smith",
            CandidateFirstName = "Jane",
            CandidateMiddleName = "A.",
            CandidateLastName = "Smith",
            OfficeSought = "Mayor",
            JurisdictionName = "Cityville",
            District = "12",
            OrganizationName = "Civic Group",
            IndividualTransactorAddress = new AddressViewModel
            {
                Id = 1,
                Street = "123 Main St",
                City = "Cityville",
                State = "CA",
                Zip = "90001"
            },
            PertainsTo = "Candidate",
            Jurisdiction = "Local",
            Position = "Support",
            TransactionAmount = 500.75m,
            TransactionDate = new DateTime(2024, 5, 1, 0, 0, 0, DateTimeKind.Local),
            Notes = "Test payment",
            BallotLetter = "A",
            BallotMeasureTitle = "Measure A - Education Funding"
        };

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(456));
            Assert.That(model.Action, Is.EqualTo(FormAction.SaveAndClose));
            Assert.That(model.ContactName, Is.EqualTo("John Doe"));
            Assert.That(model.ContactId, Is.EqualTo(1001));
            Assert.That(model.AddressTitleResourceKey, Is.EqualTo("Address_Title"));
            Assert.That(model.ParticipantType, Is.EqualTo("Individual"));
            Assert.That(model.FirstName, Is.EqualTo("John"));
            Assert.That(model.MiddleName, Is.EqualTo("M."));
            Assert.That(model.LastName, Is.EqualTo("Doe"));
            Assert.That(model.Employer, Is.EqualTo("Example Inc."));
            Assert.That(model.CommitteeId, Is.EqualTo(3001));
            Assert.That(model.CandidateId, Is.EqualTo(4001));
            Assert.That(model.CandidateName, Is.EqualTo("Jane A. Smith"));
            Assert.That(model.CandidateFirstName, Is.EqualTo("Jane"));
            Assert.That(model.CandidateMiddleName, Is.EqualTo("A."));
            Assert.That(model.CandidateLastName, Is.EqualTo("Smith"));
            Assert.That(model.OfficeSought, Is.EqualTo("Mayor"));
            Assert.That(model.JurisdictionName, Is.EqualTo("Cityville"));
            Assert.That(model.District, Is.EqualTo("12"));
            Assert.That(model.OrganizationName, Is.EqualTo("Civic Group"));

            Assert.That(model.IndividualTransactorAddress, Is.Not.Null);
            Assert.That(model.IndividualTransactorAddress.Street, Is.EqualTo("123 Main St"));
            Assert.That(model.IndividualTransactorAddress.City, Is.EqualTo("Cityville"));
            Assert.That(model.IndividualTransactorAddress.State, Is.EqualTo("CA"));
            Assert.That(model.IndividualTransactorAddress.Zip, Is.EqualTo("90001"));

            Assert.That(model.PertainsTo, Is.EqualTo("Candidate"));
            Assert.That(model.Jurisdiction, Is.EqualTo("Local"));
            Assert.That(model.Position, Is.EqualTo("Support"));
            Assert.That(model.TransactionAmount, Is.EqualTo(500.75m));
            Assert.That(model.TransactionDate, Is.EqualTo(new DateTime(2024, 5, 1, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(model.Notes, Is.EqualTo("Test payment"));
            Assert.That(model.BallotLetter, Is.EqualTo("A"));
            Assert.That(model.BallotMeasureTitle, Is.EqualTo("Measure A - Education Funding"));
        });
    }

    [Test]
    public async Task PaymentReceived01_Get_ModelStateValid_ReturnsViewResultWithModel()
    {
        // Arrange
        var filingId = 1;
        var filerId = 1;

        _smoCampaignStatementCtlSvcMock
            .GetFilerContactViewModelAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<string>())
            .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel()));

        // Act
        var result = await _controller.PaymentReceived01(filingId, filerId, 1L, 1L) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
    }

    [Test]
    public async Task PaymentReceived01_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PaymentReceived01(1L, 1L, null, null);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void PaymentReceived01_Post_ModelStateValid_Cancel_RedirectsToUnderConstruction()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Cancel
        };

        var result = _controller.PaymentReceived01(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("TransactionSummary"));
        });
    }

    [Test]
    public void PaymentReceived01_Post_ModelStateValid_Continue_RedirectsToPaymentReceived02()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 15,
            ContactId = 99,
            Action = FormAction.Continue
        };

        // Act
        var result = _controller.PaymentReceived01(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            var redirect = (RedirectToActionResult)result;
            Assert.That(redirect.ActionName, Is.EqualTo("PaymentReceived02"));
        });
    }


    [Test]
    public void PaymentReceived01_Post_InvalidAction_AddsModelError_ReturnsViewResult()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = null
        };

        var result = _controller.PaymentReceived01(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
        });
    }

    [Test]
    public void PaymentReceived01Continue_ContactIdNull_ReturnViewWithModelStateIncludeError()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1L
        };
        var methodInfo = typeof(SmoCampaignStatementController).GetMethod("PaymentReceived01Continue", BindingFlags.NonPublic | BindingFlags.Instance);

        // Act
        var result = methodInfo!.Invoke(_controller, [model]);

        // Assert
        var modelState = _controller.ModelState;
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            var mappedResult = result as ViewResult;
            Assert.That(mappedResult!.ViewName, Is.EqualTo(nameof(SmoCampaignStatementController.PaymentReceived01)));
            Assert.That(modelState.ContainsKey(nameof(model.ContactId)));
        });
    }
    #endregion

    #region PaymentReceived02
    [Test]
    public async Task PaymentReceived02_Get_ModelStateValid_ReturnsViewResultWithModel()
    {
        // Arrange
        var filingId = 1L;
        var contactId = 1L;

        var expectedModel = new SmoCampaignStatementTransactionEntryViewModel { Id = filingId };
        _smoCampaignStatementCtlSvcMock
            .GetFilerContactViewModelAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<string>())
            .Returns(Task.FromResult(expectedModel));

        // Act
        var result = await _controller.PaymentReceived02(filingId, contactId, 1L, 1L) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Model, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
            Assert.That(((SmoCampaignStatementTransactionEntryViewModel)result.Model!).Id, Is.EqualTo(filingId));
        });
    }

    [Test]
    public async Task PaymentReceived02_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PaymentReceived02(1L, null, null, null);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PaymentReceived02_Post_ModelStateValid_Previous_RedirectsToPaymentReceived01()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Previous,
            Id = 123
        };

        var result = await _controller.PaymentReceived02(model, CancellationToken.None);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(_controller.PaymentReceived01)));
        });
    }

    [Test]
    public async Task PaymentReceived02_Post_ModelStateValid_Continue_RedirectsToPaymentReceived03()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            ParticipantType = "Individual",
            Id = 123
        };

        _smoCampaignStatementCtlSvcMock
            .SaveOrUpdateContactAsync(
                model,
                Arg.Any<string>(),
                Arg.Any<ModelStateDictionary>(),
                Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.PaymentReceived02(model, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PaymentReceived03"));
        });
    }

    [Test]
    public async Task PaymentReceived02_Post_ModelStateValid_Cancel_RedirectsToTransactionSummary()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Cancel,
            Id = 123
        };

        var result = await _controller.PaymentReceived02(model, CancellationToken.None);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(_controller.TransactionSummary)));
        });
    }

    [Test]
    public async Task PaymentReceived02_Post_InvalidAction_AddsModelError_ReturnsViewResult()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = null,
            Id = 123
        };

        var result = await _controller.PaymentReceived02(model, CancellationToken.None) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task PaymentReceived02_Post_Continue_ParticipantTypeNull_ReturnsViewAndAddsModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            ParticipantType = null,
            Id = 123,
            FilerId = 0,
            ContactId = null,
            TransactionId = 0,
            IndividualTransactorAddress = new AddressViewModel { Street = "123 Main" }
        };

        // Act
        var result = await _controller.PaymentReceived02(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected a ViewResult when ParticipantType is null");
        Assert.Multiple(() =>
        {
            Assert.That(result!.ViewName, Is.EqualTo(nameof(_controller.PaymentReceived02)));
            Assert.That(result.Model, Is.SameAs(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
            Assert.That(_controller.ModelState.ContainsKey(nameof(model.ParticipantType)), Is.True);
        });

        await _accuMailValidatorSvcMock.DidNotReceiveWithAnyArgs()
            .AccuMailValidationHandler(default!, default!, default);
        await _smoCampaignStatementCtlSvcMock.DidNotReceiveWithAnyArgs()
            .SaveOrUpdateContactAsync(default!, default!, default);
    }

    [Test]
    public async Task PaymentReceived02_Post_Continue_RedirectsToPaymentReceived03()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            ParticipantType = "Individual",
            Id = 123,
            FilerId = 0,
            ContactId = 456,
            TransactionId = 789,
            IndividualTransactorAddress = new AddressViewModel { Street = "123 Main" }
        };

        _smoCampaignStatementCtlSvcMock
            .SaveOrUpdateContactAsync(
                model,
                Arg.Any<string>(),
                Arg.Any<ModelStateDictionary>(),
                Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.PaymentReceived02(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.PaymentReceived03)));
            Assert.That(result.RouteValues!["filingId"], Is.EqualTo(model.Id));
            Assert.That(result.RouteValues!["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(result.RouteValues!["transactionId"], Is.EqualTo(model.TransactionId));
        });
    }

    [Test]
    public async Task PaymentReceived02Organization_Post_Continue_RedirectsToPaymentReceived03()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            ParticipantType = "Organization",
            Id = 123,
            FilerId = 0,
            ContactId = 456,
            TransactionId = 789,
            IndividualTransactorAddress = new AddressViewModel { Street = "123 Main" }
        };

        _smoCampaignStatementCtlSvcMock
            .SaveOrUpdateContactAsync(
                model,
                Arg.Any<string>(),
                Arg.Any<ModelStateDictionary>(),
                Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.PaymentReceived02(model, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.PaymentReceived03)));
            Assert.That(result.RouteValues!["filingId"], Is.EqualTo(model.Id));
            Assert.That(result.RouteValues!["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(result.RouteValues!["transactionId"], Is.EqualTo(model.TransactionId));
        });
    }
    #endregion

    #region PaymentReceived03
    [Test]
    public async Task PaymentReceived03_Get_ReturnsViewResult()
    {
        // Arrange
        var parameters = new PaymentReceivedParameters
        {
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
            Notes = ""
        };

        var expectedModel = new SmoCampaignStatementTransactionEntryViewModel { Id = 1L };
        _smoCampaignStatementCtlSvcMock
            .GetPaymentReceived03ViewModelAsync(Arg.Any<PaymentReceivedParameters>())
            .Returns(Task.FromResult(expectedModel));

        // Act
        var result = await _controller.PaymentReceived03(parameters);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = (ViewResult)result;
        Assert.That(viewResult.Model, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
    }

    [Test]
    public async Task PaymentReceived03_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        var parameters = new PaymentReceivedParameters
        {
            Id = 1L,
        };
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PaymentReceived03(parameters);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PaymentReceived03_Post_ModelStateValid_Continue_RedirectsToPaymentReceived04()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            Jurisdiction = DisclosureConstants.CandidateOrMeasure.Local,
            PertainsTo = DisclosureConstants.CandidateOrMeasure.Candidate,
        };

        var result = await _controller.PaymentReceived03(model);

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());

        var redirectResult = (RedirectToActionResult)result;
        Assert.That(redirectResult.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.RouteValues["jurisdiction"], Is.EqualTo(DisclosureConstants.CandidateOrMeasure.Local));
            Assert.That(redirectResult.RouteValues["pertainsTo"], Is.EqualTo(DisclosureConstants.CandidateOrMeasure.Candidate));
        });
    }

    [Test]
    public async Task PaymentReceived03_Post_MissingJurisdictionOrPertains_ReturnsViewWithModelErrors()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            Jurisdiction = "", // Missing
            PertainsTo = null // Also missing
        };
        _smoCampaignStatementCtlSvcMock
           .When(x => x.ValidatePaymentReceivedRequestAsync(
               Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(),
               Arg.Any<ModelStateDictionary>()))
           .Do(call =>
           {
               var modelState = call.Arg<ModelStateDictionary>();
               modelState.AddModelError("Jurisdiction", "Jurisdiction is required");
               modelState.AddModelError("PertainsTo", "PertainsTo is required");
           });

        // Act
        var result = await _controller.PaymentReceived03(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ViewName, Is.EqualTo("PaymentReceived03"));
            Assert.That(result.Model, Is.EqualTo(model));
        });

        Assert.Multiple(() =>
        {
            Assert.That(_controller.ModelState.ContainsKey(nameof(model.Jurisdiction)), Is.True);
            Assert.That(_controller.ModelState.ContainsKey(nameof(model.PertainsTo)), Is.True);
            Assert.That(_controller.ModelState[nameof(model.Jurisdiction)]!.Errors, Has.Count.EqualTo(1));
            Assert.That(_controller.ModelState[nameof(model.PertainsTo)]!.Errors, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task PaymentReceived03_Post_ModelStateValid_Previous_RedirectsToPaymentReceived02()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Previous
        };

        var result = await _controller.PaymentReceived03(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PaymentReceived02"));
        });
    }

    [Test]
    public async Task PaymentReceived03_Post_ModelStateValid_Cancel_RedirectsToTransactionSummary()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Cancel
        };

        var result = await _controller.PaymentReceived03(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
        });
    }

    [Test]
    public async Task PaymentReceived03_Post_InvalidAction_AddsModelError_ReturnsViewResult()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = null
        };

        var result = await _controller.PaymentReceived03(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
        });
    }
    #endregion

    #region PaymentReceived04
    [Test]
    public async Task PaymentReceived04_Get_InvalidJurisdictionOrPertains_RedirectsToPaymentReceived03()
    {
        var parameters = new PaymentReceivedParameters
        {

        };
        var result = await _controller.PaymentReceived04(parameters);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PaymentReceived03"));
        });
    }

    [Test]
    public async Task PaymentReceived04_Get_ValidJurisdictionAndPertains_ReturnsViewResult()
    {
        // Arrange
        var parameters = new PaymentReceivedParameters
        {
            Id = 1,
            Jurisdiction = DisclosureConstants.CandidateOrMeasure.State,
            PertainsTo = DisclosureConstants.CandidateOrMeasure.Candidate,
            Position = DisclosureConstants.CandidateOrMeasure.Support
        };
        var expectedModel = new SmoCampaignStatementTransactionEntryViewModel { Id = 1L };
        _smoCampaignStatementCtlSvcMock
            .GetPaymentReceived04ViewModelAsync(Arg.Any<PaymentReceivedParameters>())
            .Returns(Task.FromResult(expectedModel));

        // Act
        var result = await _controller.PaymentReceived04(parameters);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = (ViewResult)result;
        Assert.That(viewResult.Model, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
    }

    [Test]
    public async Task PaymentReceived04_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        var parameters = new PaymentReceivedParameters
        {
            Id = 1L,
        };
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PaymentReceived04(parameters);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PaymentReceived04_Post_ModelStateValid_Previous_RedirectsToPaymentReceived03()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Previous
        };

        var result = await _controller.PaymentReceived04(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PaymentReceived03"));
        });
    }

    [Test]
    public async Task PaymentReceived04_Post_ModelStateValid_SaveAndClose_RedirectsToTransactionSummary()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.SaveAndClose
        };

        var result = await _controller.PaymentReceived04(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
        });
    }

    [Test]
    public async Task PaymentReceived04_Post_ModelStateValid_Cancel_RedirectsToTransactionSummary()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Cancel
        };

        var result = await _controller.PaymentReceived04(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
        });
    }

    [Test]
    public async Task PaymentReceived04_Post_InvalidAction_AddsModelError_ReturnsViewResult()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = null
        };

        var result = await _controller.PaymentReceived04(model) as ViewResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task PaymentReceived04_Post_InvalidState_ShouldReturnsViewResult()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
        };
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PaymentReceived04(model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task PaymentReceived04_Save_InvalidState_ShouldReturnCurrentView()
    {
        // Arrange
        var methodInfo = typeof(SmoCampaignStatementController).GetMethod("PaymentReceived04Save", BindingFlags.NonPublic | BindingFlags.Instance)!;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1,
        };
        _controller.ModelState.AddModelError("error", "error message");
        _ = _smoCampaignStatementCtlSvcMock
            .SaveTransactionAsync(Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<string>());

        // Act
        var task = (methodInfo.Invoke(_controller, [model]) as Task<IActionResult>)!;
        var result = await task;

        // Arrange
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            var remapResult = result as ViewResult;
            Assert.That(remapResult!.ViewName, Is.EqualTo("PaymentReceived04"));
            Assert.That(remapResult!.Model, Is.EqualTo(model));
        });
    }
    #endregion

    #region SearchCommitteeByIdOrName
    [Test]
    public async Task SearchCommitteeByIdOrName_ValidModelState_ReturnsExpectedResults()
    {
        var expected = new List<CommitteeSearchResultDto> { new() { Id = 1, Name = "Committee A", Addresses = new List<AddressDto>() } };

        _smoRegistrationSvcMock
            .SearchCommitteeByIdOrName("search")
            .Returns(expected);

        var result = await _controller.SearchCommitteeByIdOrName("search", CancellationToken.None);

        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.EqualTo(expected));
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_InvalidModelState_ReturnsEmptyList()
    {
        _controller.ModelState.AddModelError("key", "error");

        var result = await _controller.SearchCommitteeByIdOrName("search", CancellationToken.None);

        Assert.Multiple(() =>
        {
            Assert.That(result!.Value, Is.Not.Null);
            Assert.That((result!.Value as IEnumerable<object>)?.Any() ?? false, Is.False);
        });
    }
    #endregion

    #region SearchFilerContactsByIdOrName
    [Test]
    public async Task SearchFilerContactsByIdOrName_WithValidFilerId_ReturnsContactResults()
    {
        // Arrange
        var expected = new List<Generated.ContactSearchResultDto>
        {
            new("Contact A", "123 Main St", 1, 2, "Individual", 99)
        };

        _contactsApiMock
            .SearchContactsByNameOrId(Arg.Any<long>(), Arg.Any<string>(), Arg.Any<List<string>>(), Arg.Any<CancellationToken>())
            .Returns(expected);

        var result = await _controller.SearchFilerContactsByIdOrName(123, "abc", CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Value, Is.EqualTo(expected));
        });
    }

    [Test]
    public async Task SearchFilerContactsByIdOrName_InvalidModelState_ReturnsEmptyList()
    {
        _controller.ModelState.AddModelError("x", "invalid");

        var result = await _controller.SearchFilerContactsByIdOrName(123, "abc", CancellationToken.None);

        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.InstanceOf<List<Generated.ContactSearchResultDto>>());
    }
    #endregion

    #region Candidates Measures Not Listed
    [Test]
    public async Task CandidatesMeasuresNotListed01_ModelStateValid_ReturnsViewWithViewModel()
    {
        // Arrange
        var parameters = new CandidatesMeasuresNotListedParameters
        {
            DisclosureWithoutPaymentId = 1,
        };

        var expectedModel = new SmoCampaignStatementTransactionEntryViewModel { Id = 1L };
        _smoCampaignStatementCtlSvcMock
            .GetCandidatesMeasuresNotListedViewModel(Arg.Any<CandidatesMeasuresNotListedParameters>())
            .Returns(Task.FromResult(expectedModel));

        // Act
        var result = await _controller.CandidatesMeasuresNotListed01(parameters) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.TypeOf<SmoCampaignStatementTransactionEntryViewModel>());
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed01_ModelStateInvalid_ReturnsNotFoundView()
    {
        // Arrange
        var parameters = new CandidatesMeasuresNotListedParameters
        {

        };
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.CandidatesMeasuresNotListed01(parameters);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CandidatesMeasuresNotListed01_Post_ModelStateValid_PreviousAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Previous
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed01(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed01_Post_ModelStateValid_ContinueAction_RedirectsToCandidatesMeasuresNotListed02()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue,
            Jurisdiction = DisclosureConstants.CandidateOrMeasure.State,
            PertainsTo = DisclosureConstants.CandidateOrMeasure.Candidate,
            Position = DisclosureConstants.CandidateOrMeasure.Support,
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed01(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.CandidatesMeasuresNotListed02)));
            Assert.That(result.RouteValues, Contains.Key("jurisdiction").WithValue(DisclosureConstants.CandidateOrMeasure.State));
            Assert.That(result.RouteValues, Contains.Key("pertainsTo").WithValue(DisclosureConstants.CandidateOrMeasure.Candidate));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed01_Post_ModelStateValid_CancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Cancel
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed01(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.TransactionSummary)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed01_Post_ModelStateValid_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = (FormAction)999 // Invalid action
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed01(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState[string.Empty]?.Errors, Has.Some.With.Property(nameof(ModelError.ErrorMessage)).EqualTo("text"));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed01_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue
        };
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.CandidatesMeasuresNotListed01(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed01_Continue_InvalidState_ShouldReturnTheCurrentView()
    {
        // Arrange
        var methodInfo = typeof(SmoCampaignStatementController).GetMethod("CandidatesMeasuresNotListed01Continue", BindingFlags.NonPublic | BindingFlags.Instance);
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1,
        };
        _ = _smoCampaignStatementCtlSvcMock.ValidateCandidateOrMeasureNotListedAsync(Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(), Arg.Any<ModelStateDictionary>());
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var task = methodInfo!.Invoke(_controller, [model]) as Task<IActionResult>;
        var result = await task!;

        // Arrange
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            var remapResult = result as ViewResult;
            Assert.That(remapResult!.Model, Is.EqualTo(model));
            Assert.That(remapResult!.ViewName, Is.EqualTo(nameof(_controller.CandidatesMeasuresNotListed01)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_ValidInputs_ModelStateValid_ReturnsViewWithModel()
    {
        // Arrange
        var parameters = new CandidatesMeasuresNotListedParameters
        {
            Id = 1,
            Jurisdiction = DisclosureConstants.CandidateOrMeasure.State,
            PertainsTo = DisclosureConstants.CandidateOrMeasure.Candidate,
            Position = DisclosureConstants.CandidateOrMeasure.Support
        };

        var expectedModel = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1L,
            Jurisdiction = DisclosureConstants.CandidateOrMeasure.State,
            PertainsTo = DisclosureConstants.CandidateOrMeasure.Candidate,
            Position = DisclosureConstants.CandidateOrMeasure.Support
        };
        _smoCampaignStatementCtlSvcMock
            .GetCandidatesMeasuresNotListedViewModel(Arg.Any<CandidatesMeasuresNotListedParameters>())
            .Returns(Task.FromResult(expectedModel));

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(parameters) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            var model = result!.Model as SmoCampaignStatementTransactionEntryViewModel;
            Assert.That(model, Is.Not.Null);
            Assert.That(model!.Jurisdiction, Is.EqualTo(parameters.Jurisdiction));
            Assert.That(model.PertainsTo, Is.EqualTo(parameters.PertainsTo));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_JurisdictionIsNull_RedirectsToCandidatesMeasuresNotListed01()
    {
        // Arrange
        var parameters = new CandidatesMeasuresNotListedParameters
        {

        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(parameters) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.CandidatesMeasuresNotListed01)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_PertainsToIsEmpty_RedirectsToCandidatesMeasuresNotListed01()
    {
        // Arrange
        var parameters = new CandidatesMeasuresNotListedParameters
        {
            Jurisdiction = DisclosureConstants.CandidateOrMeasure.Local,
            PertainsTo = ""
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(parameters) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.CandidatesMeasuresNotListed01)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var parameters = new CandidatesMeasuresNotListedParameters
        {
            Id = 1,
            Jurisdiction = DisclosureConstants.CandidateOrMeasure.State,
            PertainsTo = DisclosureConstants.CandidateOrMeasure.Candidate,
            Position = DisclosureConstants.CandidateOrMeasure.Support
        };
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(parameters);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_Post_ModelStateValid_PreviousAction_RedirectsToCandidatesMeasuresNotListed01()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Previous
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.CandidatesMeasuresNotListed01)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_Post_ModelStateValid_SaveAndCloseAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.TransactionSummary)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_Post_ModelStateValid_CancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Cancel
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.ActionName, Is.EqualTo(nameof(_controller.TransactionSummary)));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_Post_ModelStateValid_InvalidAction_ReturnsViewWithModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = (FormAction)999
        };

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState[string.Empty]?.Errors, Has.Some.With.Property(nameof(ModelError.ErrorMessage)).EqualTo("text"));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_Post_ModelStateInvalid_ReturnsViewWithModel()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.Continue
        };
        _controller.ModelState.AddModelError("key", "error");

        // Act
        var result = await _controller.CandidatesMeasuresNotListed02(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task CandidatesMeasuresNotListed02_Save_InvalidState_ShouldReturnsCurrentView()
    {
        // Arrange
        var methodInfo = typeof(SmoCampaignStatementController).GetMethod("CandidatesMeasuresNotListed02Save", BindingFlags.NonPublic | BindingFlags.Instance)!;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1,
        };
        _controller.ModelState.AddModelError("error", "error message");
        _ = _smoCampaignStatementCtlSvcMock
            .SaveCandidateOrMeasureWithoutPaymentReceivedAsync(Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(), Arg.Any<ModelStateDictionary>());

        // Act
        var task = (methodInfo.Invoke(_controller, [model]) as Task<IActionResult>)!;
        var result = await task;

        // Arrange
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            var remapResult = result as ViewResult;
            Assert.That(remapResult!.ViewName, Is.EqualTo("CandidatesMeasuresNotListed02"));
            Assert.That(remapResult!.Model, Is.EqualTo(model));
        });
    }
    #endregion

    #region TransactionSummary_Get
    [Test]
    public async Task TransactionSummary_Get_ReturnsPaymentsReceivedModel()
    {
        // Arrange
        var filingId = 1;
        var summaryType = "PaymentReceivedSummary";
        var model = new SmoCampaignStatementTransactionSummaryViewModel();
        _smoCampaignStatementCtlSvcMock
            .GetPaymentsReceived(filingId)
            .Returns(model);

        // Act
        var result = await _controller.TransactionSummary(filingId, summaryType) as ViewResult;

        // Assert
        await _smoCampaignStatementCtlSvcMock
            .Received(1)
            .GetPaymentsReceived(filingId);

        Assert.That(result!.Model, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task TransactionSummary_Get_ReturnsPaymentsMade()
    {
        // Arrange
        var filingId = 1;
        var summaryType = "PaymentMadeSummary";
        var model = new SmoCampaignStatementTransactionSummaryViewModel();
        _smoCampaignStatementCtlSvcMock
            .GetPaymentsMade(filingId)
            .Returns(model);

        // Act
        var result = await _controller.TransactionSummary(filingId, summaryType) as ViewResult;

        // Assert
        await _smoCampaignStatementCtlSvcMock
            .Received(1)
            .GetPaymentsMade(filingId);

        Assert.That(result!.Model, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task TransactionSummary_Get_ReturnsPaymentsMadeByAgentOrIndependentContractor()
    {
        // Arrange
        var filingId = 1;
        var summaryType = "PaymentMadeByAgentOrIndependentContractorSummary";
        var model = new SmoCampaignStatementTransactionSummaryViewModel();
        _smoCampaignStatementCtlSvcMock
            .GetPaymentsMadeByAnAgentOrIndependentContractor(filingId)
            .Returns(model);

        // Act
        var result = await _controller.TransactionSummary(filingId, summaryType) as ViewResult;

        // Assert
        await _smoCampaignStatementCtlSvcMock
            .Received(1)
            .GetPaymentsMadeByAnAgentOrIndependentContractor(filingId);

        Assert.That(result!.Model, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task TransactionSummary_Get_ReturnsPersonsReceiving1000OrMore()
    {
        // Arrange
        var filingId = 1;
        var summaryType = "PersonReceiving1000OrMoreSummary";
        var model = new SmoCampaignStatementTransactionSummaryViewModel();
        _smoCampaignStatementCtlSvcMock
            .GetPersonsReceiving1000OrMore(filingId)
            .Returns(model);

        // Act
        var result = await _controller.TransactionSummary(filingId, summaryType) as ViewResult;

        // Assert
        await _smoCampaignStatementCtlSvcMock
            .Received(1)
            .GetPersonsReceiving1000OrMore(filingId);

        Assert.That(result!.Model, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task TransactionSummary_Get_ReturnsCandidatesAndMeasuresOnPaymentsReceived()
    {
        // Arrange
        var filingId = 1;
        var summaryType = "CandidateOrMeasureSupportedOrOpposedSummary";
        var model = new SmoCampaignStatementTransactionSummaryViewModel();
        _smoCampaignStatementCtlSvcMock
            .GetCandidatesAndMeasureOnPaymentsReceived(filingId)
            .Returns(model);

        // Act
        var result = await _controller.TransactionSummary(filingId, summaryType) as ViewResult;

        // Assert
        await _smoCampaignStatementCtlSvcMock
            .Received(1)
            .GetCandidatesAndMeasureOnPaymentsReceived(filingId);

        Assert.That(result!.Model, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task TransactionSummary_Get_ReturnsNotFound_NotFound()
    {
        // Arrange
        var filingId = 1;
        var summaryType = "CandidateOrMeasureSupportedOrOpposedSummary";
        _controller.ModelState.AddModelError("Test", "Error");


        // Act
        var result = await _controller.TransactionSummary(filingId, summaryType);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    #endregion

    #region TransactionSummary_Post
    [Test]
    public async Task TransactionSummary_Post_SaveAndClose_ReturnsRedirectToAction()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.TransactionSummary(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(_controller.Review)));
        });
    }

    [Test]
    public async Task TransactionSummary_Post_InvalidModelState_ReturnsView()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            Action = FormAction.SaveAndClose
        };
        _controller.ModelState.AddModelError("Test", "Error");

        // Act
        var result = await _controller.TransactionSummary(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task TransactionSummarySave_ShouldCallSvcAndRedirectToReview()
    {
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            Action = FormAction.SaveAndClose,
            SummaryType = "PaymentReceivedSummary"
        };

        // Act
        var result = await _controller.TransactionSummarySave(model);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public async Task TransactionSummarySave_ShouldReturnViewWithErrors_WithPaymentReceivedSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            FilerId = 1,
            FilingSummaryId = 2,
            Action = FormAction.SaveAndClose,
            SummaryType = "PaymentReceivedSummary",
            UnitemizedPaymentLessThan100 = -10
        };
        _controller.ModelState.AddModelError("Test", "Error");

        var newModel = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            Action = FormAction.SaveAndClose,
            SummaryType = "PaymentReceivedSummary",
        };
        _smoCampaignStatementCtlSvcMock.GetPaymentsReceived(model.Id.Value)
            .Returns(newModel);

        // Act
        var result = await _controller.TransactionSummarySave(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task TransactionSummarySave_ShouldReturnViewWithErrors_WithPaymentMadeSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            FilerId = 1,
            FilingSummaryId = 2,
            Action = FormAction.SaveAndClose,
            SummaryType = "PaymentMadeSummary",
            UnitemizedPaymentLessThan100 = -10
        };
        _controller.ModelState.AddModelError("Test", "Error");

        var newModel = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            Action = FormAction.SaveAndClose,
            SummaryType = "PaymentMadeSummary",
        };
        _smoCampaignStatementCtlSvcMock.GetPaymentsMade(model.Id.Value)
            .Returns(newModel);

        // Act
        var result = await _controller.TransactionSummarySave(model);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Person Receiving
    [Test]
    public async Task PersonReceiving01_Get_ReturnsViewResultWithModel()
    {
        // Arrange
        var parameters = new PersonReceivingParameters
        {
            Id = 1L,
            FilerId = 1L,
        };
        _smoCampaignStatementCtlSvcMock
            .GetPersonReceivingFilerContactViewModelAsync(Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel(parameters.Id)));

        // Act
        var result = await _controller.PersonReceiving01(parameters) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
    }

    [Test]
    public async Task PersonReceiving01_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        var parameters = new PersonReceivingParameters
        {
            Id = 1L,
            FilerId = 1L,
        };
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PersonReceiving01(parameters);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PersonReceiving01_Post_ModelStateValid_Continue_RedirectsToPersonReceiving02()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Continue };

        var result = await _controller.PersonReceiving01(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PersonReceiving02"));
        });
    }

    [Test]
    public async Task PersonReceiving01_Post_ModelStateValid_Cancel_RedirectsToTransactionSummary()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Cancel };

        var result = await _controller.PersonReceiving01(model);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
        });
    }

    [Test]
    public async Task PersonReceiving01_Post_ModelStateValid_InvalidAction_AddsModelError_ReturnsView()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = null };

        var result = await _controller.PersonReceiving01(model) as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
        });
        Assert.That(result.Model, Is.EqualTo(model));
    }

    [Test]
    public async Task PersonReceiving02_Get_ReturnsViewResultWithModel()
    {
        // Arrange
        var parameters = new PersonReceivingParameters
        {
            Id = 1L
        };
        _smoCampaignStatementCtlSvcMock
            .GetPersonReceivingFilerContactViewModelAsync(Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel(parameters.Id)));

        // Act
        var result = await _controller.PersonReceiving02(parameters) as ViewResult;

        // Arrange
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
    }

    [Test]
    public async Task PersonReceiving02_Get_InvalidState_ShouldReturnsNotFound()
    {
        // Arrange
        var parameters = new PersonReceivingParameters
        {
            Id = 1L,
            FilerId = 1L,
        };
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.PersonReceiving02(parameters);

        // Arrange
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PersonReceiving02_Post_ModelStateValid_Previous_RedirectsToPersonReceiving01()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Previous };

        // Act
        var result = await _controller.PersonReceiving02(model, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PersonReceiving01"));
        });
    }

    [Test]
    public async Task PersonReceiving02_Post_ModelStateValid_Continue_RedirectsToPersonReceiving03()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Continue };

        // Act
        var result = await _controller.PersonReceiving02(model, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PersonReceiving03"));
        });
    }

    [Test]
    public async Task PersonReceiving02_Post_ModelStateValid_Cancel_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Cancel };

        // Act
        var result = await _controller.PersonReceiving02(model, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
        });
    }

    [Test]
    public async Task PersonReceiving02_Post_ModelStateValid_InvalidAction_AddsModelError_ReturnsView()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = null };

        // Act
        var result = await _controller.PersonReceiving02(model, CancellationToken.None) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task PersonReceiving02Continue_ModelStateValid_InvalidAction_AddsModelError_ReturnsView()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1
        };
        var methodInfo = typeof(SmoCampaignStatementController)
            .GetMethod("PersonReceiving02Continue", BindingFlags.NonPublic | BindingFlags.Instance)!;
        _controller.ModelState.AddModelError("error", "error message");
        _ = _smoCampaignStatementCtlSvcMock
            .SaveOrUpdateContactAsync(Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(), Arg.Any<string>(), Arg.Any<ModelStateDictionary>(), Arg.Any<CancellationToken>());

        // Act
        var task = (methodInfo.Invoke(_controller, [model, CancellationToken.None]) as Task<IActionResult>)!;
        var result = await task;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
            var mappedResult = result as ViewResult;
            Assert.That(mappedResult!.Model, Is.EqualTo(model));
            Assert.That(mappedResult!.ViewName, Is.EqualTo(nameof(_controller.PersonReceiving02)));
        });
    }

    [Test]
    public async Task PersonReceiving03_Get_ReturnsViewResultWithModel()
    {
        // Arrange
        var filingId = 1L;
        var contactId = 1L;
        _smoCampaignStatementCtlSvcMock
            .GetPersonReceiving03ViewModelAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long?>())
            .Returns(Task.FromResult(new SmoCampaignStatementTransactionEntryViewModel(filingId, contactId, null)));

        // Act
        var result = await _controller.PersonReceiving03(filingId, contactId, null) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Model, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
    }

    [Test]
    public async Task PersonReceiving03_Post_ModelStateValid_Previous_RedirectsToPersonReceiving02()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Previous };

        // Act
        var result = await _controller.PersonReceiving03(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo("PersonReceiving02"));
        });
    }

    [Test]
    public async Task PersonReceiving03_Post_ModelStateValid_SaveAndClose_RedirectsToUnderConstruction()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.PersonReceiving03(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(_controller.TransactionSummary)));
        });
    }

    [Test]
    public async Task PersonReceiving03_Post_ModelStateValid_Cancel_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = FormAction.Cancel };

        // Act
        var result = await _controller.PersonReceiving03(model);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(((RedirectToActionResult)result).ActionName, Is.EqualTo(nameof(SmoCampaignStatementController.TransactionSummary)));
        });
    }

    [Test]
    public async Task PersonReceiving03_Post_ModelStateValid_InvalidAction_AddsModelError_ReturnsView()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel { Action = null };

        // Act
        var result = await _controller.PersonReceiving03(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
            Assert.That(result!.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task PersonReceiving03SaveAsync_ModelStateValid_InvalidAction_AddsModelError_ReturnsView()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1
        };
        var methodInfo = typeof(SmoCampaignStatementController)
            .GetMethod("PersonReceiving03SaveAsync", BindingFlags.NonPublic | BindingFlags.Instance)!;
        _controller.ModelState.AddModelError("error", "error message");
        _ = _smoCampaignStatementCtlSvcMock
            .SaveTransactionAsync(Arg.Any<SmoCampaignStatementTransactionEntryViewModel>(), Arg.Any<ModelStateDictionary>(), Arg.Any<string>());

        // Act
        var task = (methodInfo.Invoke(_controller, [model]) as Task<IActionResult>)!;
        var result = await task;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(1));
            var mappedResult = result as ViewResult;
            Assert.That(mappedResult!.Model, Is.EqualTo(model));
            Assert.That(mappedResult!.ViewName, Is.EqualTo(nameof(_controller.PersonReceiving03)));
        });
    }
    #endregion

    #region Verification
    [Test]
    public async Task Verification_Get_ShouldReturnViewModel()
    {
        // Arrange
        long filingId = 123;
        var smoCampaignStatementResponse = new FilingOverviewResponseDto
        {
            FilerId = 1,
        };

        _smoCampaignStatementCtlSvcMock
            .GetVerificationPageViewModel(filingId, true)
            .Returns(new SmoCampaignStatementVerificationViewModel());

        _smoCampaignStatementSvcMock
            .GetSmoCampaignStatementOverviewAsync(filingId)
            .Returns(smoCampaignStatementResponse);


        // Act
        var result = await _controller.Verification(filingId) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }
    [Test]
    public async Task Verification_Get_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Verification(123) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }
    [Test]
    public async Task Verification_Post_ShouldReturnViewConfirmation_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = true,
            IsVerificationCertified = true,
        };

        // Act
        var result = await _controller.Verification(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
            Assert.That(result?.ActionName, Is.EqualTo("Confirmation"));
        });
    }
    [Test]
    public async Task Verification_Post_ShouldRemainPage15_IfHaveError_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            Action = FormAction.Continue,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
            IsVerificationCertified = false,
        };

        // Act
        _smoCampaignStatementCtlSvcMock
            .When(x => x.SendForAttestation(Arg.Any<SmoCampaignStatementVerificationViewModel>(), Arg.Any<ModelStateDictionary>()))
            .Do(callInfo =>
            {
                var modelState = callInfo.Arg<ModelStateDictionary>();
                modelState.AddModelError("Test", "Test");
            });
        var result = await _controller.Verification(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());
            Assert.That(result?.Model, Is.EqualTo(model));
            Assert.That(_controller.ModelState.IsValid, Is.False);
            Assert.That(_controller.ModelState["Test"]?.Errors.Count, Is.GreaterThan(0));
        });
    }

    [Test]
    public async Task Verification_Post_ShouldRedirect_OnFormActionPrevious()
    {
        // Arrange
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            Action = FormAction.Previous,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };

        // Act
        var result = await _controller.Verification(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }
    [Test]
    public async Task Verification_Post_ShouldRedirect_OnFormActionCancel()
    {
        // Arrange
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            Action = FormAction.Cancel,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Verification(model) as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Verification_Post_ShouldRedirect_OnFormActionSaveAndClose()
    {
        // Arrange
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            Action = FormAction.SaveAndClose,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };
        var tempData = Substitute.For<ITempDataDictionary>();
        _controller.TempData = tempData;

        // Act
        var result = await _controller.Verification(model) as ActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ActionResult>());
    }

    [Test]
    public async Task Verification_Post_ShouldError_AddModelError()
    {
        // Arrange
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            Action = (FormAction)999,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };

        // Act
        var result = await _controller.Verification(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }

    [Test]
    public async Task Verification_Post_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            Action = null,
            FirstName = "First Name",
            LastName = "Last Name",
            Title = "Treasurer",
            ExecutedOn = _dateNow,
            ResponsibleOfficers = new List<ResponsibleOfficerSharedViewModel>
            {
                new() { ContactId = 1, FirstName = "First Name 1", LastName = "Last Name 1", Title = "Treasurer"},
                new() { ContactId = 2, FirstName = "First Name 2", LastName = "Last Name 2", Title = "Assistant Treasurer"},
            },
            IsUserAuthorizedToAttest = false,
        };
        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Verification(model) as ViewResult;

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
    }
    #endregion

    #region Confirmation
    [Test]
    public async Task Confirmation_Get_ShouldReturnView()
    {
        // Arrange
        var filingId = 1;
        var model = new SmoCampaignStatementConfirmationViewModel
        {
            ExecutedOn = _dateNow,
            IsSubmission = true,
            PendingItems = new List<PendingItemSharedViewModel>
            {
                new() { Item = "Mock Treasurer Acknowledgement", Status = "In Progress"},
                new() { Item = "Mock Assistance Treasurer Acknowledgement", Status = "Complete"}
            }
        };

        _smoCampaignStatementCtlSvcMock
            .GetConfirmationPageViewModel(filingId)
            .Returns(model);

        // Act
        var result = await _controller.Confirmation(123, default) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<ViewResult>());
        });
    }

    [Test]
    public async Task Confirmation_Get_ShouldReturnView_InvalidModelState()
    {
        // Arrange
        var model = new SmoCampaignStatementConfirmationViewModel
        {
            ExecutedOn = _dateNow,
            Action = FormAction.Close,
            IsSubmission = true,
            PendingItems = new List<PendingItemSharedViewModel>
            {
                new() { Item = "Mock Treasurer Acknowledgement", Status = "In Progress"},
                new() { Item = "Mock Assistance Treasurer Acknowledgement", Status = "Complete"}
            }
        };

        _controller.ModelState.AddModelError("key", "error"); // Adding an error to make the model state invalid

        // Act
        var result = await _controller.Confirmation(123, default) as ViewResult;

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public void Confirmation_Post_ShouldReturnRedirect_OnFormActionContinue()
    {
        // Arrange
        var model = new SmoCampaignStatementConfirmationViewModel
        {
            ExecutedOn = _dateNow,
            Action = FormAction.Close,
            IsSubmission = true,
        };

        // Act
        var result = _controller.Confirmation(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }
    [Test]
    public void Confirmation_Post_ShouldReturnRedirect_OnInvalidFormAction()
    {
        // Arrange
        var model = new SmoCampaignStatementConfirmationViewModel
        {
            ExecutedOn = _dateNow,
            IsSubmission = true,
        };

        // Act
        var result = _controller.Confirmation(model) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Null);
    }
    #endregion

    #region SearchBallotMeasureByIdOrName
    [Test]
    public async Task SearchBallotMeasureByIdOrName_InvalidModelState_ReturnsEmptyList()
    {
        // Arrange
        var search = "test";
        _controller.ModelState.AddModelError("x", "invalid");

        // Act
        var result = await _controller.SearchBallotMeasureByIdOrName(search, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<BallotMeasureDto>>());
        });
    }

    [Test]
    public async Task SearchBallotMeasureByIdOrName_EmptyQuery_ReturnsEmptyList()
    {
        // Arrange
        var search = "";

        // Act
        var result = await _controller.SearchBallotMeasureByIdOrName(search, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<BallotMeasureDto>>());
        });
    }

    [Test]
    public async Task SearchBallotMeasureByIdOrName_ValidRequest_ReturnsResultList()
    {
        // Arrange
        var search = "123";
        var expected = new List<BallotMeasureDto>
        {
            new()
            {
                Id = 1,
                Code = "Code",
                Title = ""
            }
        };

        _ballotMeasureSvcMock.SearchBallotMeasuresAsync(Arg.Any<string>()).Returns(Task.FromResult<IEnumerable<BallotMeasureDto>>(expected));

        // Act
        var result = await _controller.SearchBallotMeasureByIdOrName(search, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Not.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<BallotMeasureDto>>());
        });
    }
    #endregion

    #region SearchCandidateByIdOrName
    [Test]
    public async Task SearchCandidateByIdOrName_InvalidModelState_ReturnsEmptyList()
    {
        // Arrange
        var search = "test";
        _controller.ModelState.AddModelError("x", "invalid");

        // Act
        var result = await _controller.SearchCandidateByName(search, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<CandidateSearchResultDto>>());
        });
    }

    [Test]
    public async Task SearchCandidateByIdOrName_EmptyQuery_ReturnsEmptyList()
    {
        // Arrange
        var search = "";

        // Act
        var result = await _controller.SearchCandidateByName(search, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<CandidateSearchResultDto>>());
        });
    }

    [Test]
    public async Task SearchCandidateByIdOrName_ValidRequest_ReturnsResultList()
    {
        // Arrange
        var search = "123";
        var expected = new List<CandidateSearchResultDto>
        {
            new()
            {
                Id = 1,
                Name = "Name",
                LastName = "LastName",
                LastElectionDate = _dateNow,
                LastElection = "string",
            }
        };

        _candidateSvcMock.SearchCandidateByName(Arg.Any<string>()).Returns(Task.FromResult<IEnumerable<CandidateSearchResultDto>>(expected));

        // Act
        var result = await _controller.SearchCandidateByName(search, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Not.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<CandidateSearchResultDto>>());
        });
    }
    #endregion

    #region SearchSmoOfficerByName
    [Test]
    public async Task SearchSmoOfficerByName_InvalidModelState_ReturnsEmptyList()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var search = "test";
        _controller.ModelState.AddModelError("x", "invalid");

        // Act
        var result = await _controller.SearchSmoOfficerByName(filerId, filingId, search);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<SmoRegistrationContactDto>>());
        });
    }

    [Test]
    public async Task SearchSmoOfficerByName_EmptyQuery_ReturnsEmptyList()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var search = "";

        // Act
        var result = await _controller.SearchSmoOfficerByName(filerId, filingId, search);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<SmoRegistrationContactDto>>());
        });
    }

    [Test]
    public async Task SearchSmoOfficerByName_ValidRequest_ReturnsResultList()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var search = "123";
        var expected = new List<SmoRegistrationContactDto>
        {
            new()
            {
                Id = 1,
                FirstName = "Name",
                LastName = "LastName",
                RegistrationId = 1,
            }
        };

        _smoCampaignStatementCtlSvcMock
            .SearchSmoOfficersAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<string>())
            .Returns(Task.FromResult(expected));

        // Act
        var result = await _controller.SearchSmoOfficerByName(filerId, filingId, search);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Value, Is.Not.Empty);
            Assert.That(result!.Value, Is.InstanceOf<List<SmoRegistrationContactDto>>());
        });
    }
    #endregion

    #region EditDisclosureWithoutPaymentReceived
    [Test]
    public void EditDisclosureWithoutPaymentReceived_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("x", "invalid");

        // Act
        var result = _controller.EditDisclosureWithoutPaymentReceived(1, 1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void EditDisclosureWithoutPaymentReceived_ValidModelState_RedirectCandidatesMeasuresNotListed01()
    {
        // Arrange

        // Act
        var result = _controller.EditDisclosureWithoutPaymentReceived(1, 1);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var mapResult = result as RedirectToActionResult;
        Assert.That(mapResult!.ActionName, Is.EqualTo(nameof(_controller.CandidatesMeasuresNotListed01)));
    }

    #endregion

    #region Amendment Explanation
    [Test]
    public async Task AmendmentExplanation_Get_ShouldReturnView_WhenModelStateIsValid()
    {
        // Arrange
        var filingId = 123;
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel()
        {
            Id = filingId,
            AmendmentExplanation = "Amendment Explanation"
        };
        _smoCampaignStatementCtlSvcMock
            .GetAmendmentExplanation(filingId)
            .Returns(model);

        // Act
        var result = await _controller.AmendmentExplanation(filingId) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "expected a ViewResult");
            Assert.That(result?.Model, Is.TypeOf<AmendSmoCampaignStatementAmendmentExplanationViewModel>());
        });
    }

    [Test]
    public async Task AmendmentExplanation_Get_ShouldReturnNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("x", "error");
        var filingId = 321;

        // Act
        var result = await _controller.AmendmentExplanation(filingId);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task AmendmentExplanation_Post_ShouldRedirectToReview_OnSaveAndClose()
    {
        // Arrange
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel
        {
            Id = 555,
            AmendmentExplanation = "Description",
            Action = FormAction.SaveAndClose
        };

        // Act
        var result = await _controller.AmendmentExplanation(model) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "expected a redirect");
            Assert.That(result?.ActionName, Is.EqualTo("Review"));
            Assert.That(result?.RouteValues?["filingId"], Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task AmendmentExplanation_Post_ShouldReturnView_WithModelError_OnDefaultAction()
    {
        // Arrange
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel
        {
            Id = 777,
            AmendmentExplanation = "Description",
            Action = FormAction.Continue
        };

        // Act
        var result = await _controller.AmendmentExplanation(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "expected a ViewResult");

            Assert.That(result?.Model, Is.SameAs(model));

            Assert.That(_controller.ModelState[string.Empty]?.Errors, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public async Task AmendmentExplanation_Post_ShouldReturnView_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("y", "oops");
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel
        {
            Id = 888,
            AmendmentExplanation = "Description",
            Action = FormAction.SaveAndClose  // would�ve redirected, but ModelState is invalid
        };

        // Act
        var result = await _controller.AmendmentExplanation(model) as ViewResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "expected a ViewResult");
            Assert.That(result?.Model, Is.SameAs(model));
        });
    }

    [Test]
    public void AmendmentExplanation_Post_NonCancelAction_DoesNotClearModelState()
    {
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel
        {
            Action = FormAction.SaveAndClose,
            Id = 100,
            AmendmentExplanation = string.Empty
        };
        _controller.ModelState.AddModelError("SomeError", "error");
        _ = _controller.AmendmentExplanation(model);

        // ModelState should NOT be cleared
        Assert.That(_controller.ModelState.IsValid, Is.False);
    }

    [Test]
    public async Task Amend_WithValidModelState_ShouldRedirectToReviewWithIsAmending()
    {
        // Arrange
        const long filingId = 16;
        const long amendFilingId = 17;

        _smoCampaignStatementCtlSvcMock
            .InitializeSmoCampaignStatementAmendment(filingId)
            .Returns(amendFilingId);
        // ModelState is valid by default

        // Act
        var result = await _controller.Amend(filingId) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected a RedirectToActionResult");
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("Review"));
            Assert.That(result.RouteValues!["filingId"], Is.EqualTo(amendFilingId));
        });
    }

    [Test]
    public async Task Amend_WithInvalidModelState_ShouldRedirectToDashboard()
    {
        // Arrange
        const long filingId = 42;
        _controller.ModelState.AddModelError("someKey", "someError");

        // Act
        var result = await _controller.Amend(filingId) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected a RedirectToActionResult");
        Assert.That(result!.ActionName, Is.EqualTo("Index"), "Invalid ModelState should route to Index");
        _toastServiceMock.DidNotReceive().Error(Arg.Any<string>());
    }
    #endregion

    #region Shared
    [Test]
    public void EditTransactionEntry_ShouldRedirect_ForPaymentReceived()
    {
        // Arrange
        var filingId = 1;
        var transactionType = "PaymentReceived";
        var transactionId = 2;

        // Act
        var result = _controller.EditTransactionEntry(filingId, transactionType, transactionId);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void EditTransactionEntry_ShouldRedirect_ForPaymentMade()
    {
        // Arrange
        var filingId = 1;
        var transactionType = "PaymentMade";
        var transactionId = 2;

        // Act
        var result = _controller.EditTransactionEntry(filingId, transactionType, transactionId);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void EditTransactionEntry_ShouldRedirect_ForPaymentMadeByAgentOrIndependentContractor()
    {
        // Arrange
        var filingId = 1;
        var transactionType = "PaymentMadeByAgentOrIndependentContractor";
        var transactionId = 2;

        // Act
        var result = _controller.EditTransactionEntry(filingId, transactionType, transactionId);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }

    [Test]
    public void EditTransactionEntry_ShouldRedirect_ForPersonReceiving1000OrMore()
    {
        // Arrange
        var filingId = 1;
        var transactionType = "PersonReceiving1000OrMore";
        var transactionId = 2;

        // Act
        var result = _controller.EditTransactionEntry(filingId, transactionType, transactionId);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
    }
    #endregion

    #region DeleteTransactionEntry
    [Test]
    public async Task DeleteTransactionEntry_ShouldCallSvcAndReturnOk()
    {
        // Arrange
        var filingId = 1;
        var transactionId = 2;

        // Act
        var result = await _controller.DeleteTransactionEntry(filingId, transactionId);

        // Assert
        Assert.That(result, Is.InstanceOf<OkResult>());
    }

    [Test]
    public async Task DeleteTransactionEntry_InvalidState_ShouldReturnNotFound()
    {
        // Arrange
        var filingId = 1;
        var transactionId = 2;
        _controller.ModelState.AddModelError("error", "error message");

        // Act
        var result = await _controller.DeleteTransactionEntry(filingId, transactionId);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    #endregion

    #region DeleteDisclosureWithoutPaymentReceived
    [Test]
    public async Task DeleteDisclosureWithoutPaymentReceived_ShouldCallSvcAndReturnOk()
    {
        // Arrange
        var filingId = 1;
        var disclosureWithoutPaymentId = 2;

        // Act
        var result = await _controller.DeleteDisclosureWithoutPaymentReceived(filingId, disclosureWithoutPaymentId);

        // Assert
        Assert.That(result, Is.InstanceOf<OkResult>());
    }
    #endregion
}
