CREATE TABLE [dbo].[FilingTransaction] (
    [Id]            BIGINT                                             IDENTITY (1, 1) NOT NULL,
    [FilingId]      BIGINT                                             NOT NULL,
    [TransactionId] BIGINT                                             NOT NULL,
    [CreatedBy]     BIGINT                                             NOT NULL,
    [ModifiedBy]    BIGINT                                             NOT NULL,
    [PeriodEnd]     DATETIME2 (7) GENERATED ALWAYS AS ROW END HIDDEN   NOT NULL,
    [PeriodStart]   DATETIME2 (7) GENERATED ALWAYS AS ROW START HIDDEN NOT NULL,
    [Active]        BIT                                                DEFAULT (CONVERT([bit],(1))) NOT NULL,
    CONSTRAINT [PK_FilingTransaction] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_FilingTransaction_DisclosureFiling_FilingId] FOREIGN KEY ([FilingId]) REFERENCES [dbo].[DisclosureFiling] ([Id]),
    CONSTRAINT [FK_FilingTransaction_DisclosureTransaction_TransactionId] FOREIGN KEY ([TransactionId]) REFERENCES [dbo].[DisclosureTransaction] ([Id]),
    PERIOD FOR SYSTEM_TIME ([PeriodStart], [PeriodEnd])
)
WITH (SYSTEM_VERSIONING = ON (HISTORY_TABLE=[dbo].[FilingTransactionHistory], DATA_CONSISTENCY_CHECK=ON));








GO
CREATE NONCLUSTERED INDEX [IX_FilingTransaction_TransactionId]
    ON [dbo].[FilingTransaction]([TransactionId] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FilingTransaction_FilingId]
    ON [dbo].[FilingTransaction]([FilingId] ASC);


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who last modified this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'ModifiedBy';


GO



GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The identifier of the user who created this record.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'CreatedBy';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Transaction linked to the filing.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'TransactionId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a transaction.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'TransactionId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Filing to which the transaction is linked.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'FilingId';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'Reference to a filing.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'FilingId';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'Relationship identifier.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'Id';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction';


GO
EXECUTE sp_addextendedproperty @name = N'Context', @value = N'This table holds the relationship between filings and transactions linked to that filing.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction';


GO
EXECUTE sp_addextendedproperty @name = N'Description', @value = N'The flag indicates the active status of this record (soft-delete). The default value is true(1).', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FilingTransaction', @level2type = N'COLUMN', @level2name = N'Active';

