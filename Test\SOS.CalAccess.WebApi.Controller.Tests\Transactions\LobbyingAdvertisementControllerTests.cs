using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.WebApi.Transactions;

namespace SOS.CalAccess.WebApi.Tests.Transactions;

[TestFixture]
public class LobbyingAdvertisementControllerTests
{
    // Common values
    private const long DisclosureFilingId = 123;
    private const decimal PaymentAmount1 = 100.00m;
    private const long FilerId = 123;
    private const long RegistrationFilingId = 456;
    private const long FilerContactId = 789;
    private const long SuccessTransactionId1 = 789L;
    private const long SuccessTransactionId2 = 999L;

    // System under test and dependencies
    private IAuthorizationService _authorizationService = null!;
    private ITransactionSvc _transactionSvc = null!;
    private LobbyingAdvertisementController _controller = null!;

    [SetUp]
    public void Setup()
    {
        _authorizationService = Substitute.For<IAuthorizationService>();
        _transactionSvc = Substitute.For<ITransactionSvc>();
        _controller = new LobbyingAdvertisementController(_transactionSvc, _authorizationService);
    }

    [Test]
    public async Task CreateLobbyingAdvertisementTransaction_WithFilingId_ReturnsOkWithTransactionDto()
    {
        // Arrange
        var request = new LobbyingAdvertisementRequestDto()
        {
            Amount = PaymentAmount1,
            DistributionMethodId = 1,
            AdditionalInformation = string.Empty,
            PublicationDate = DateTime.UtcNow,
        };
        var expectedResponse = new TransactionResponseDto { Id = SuccessTransactionId2, Valid = true, ValidationErrors = new List<WorkFlowError>() };

        _ = _transactionSvc.CreateLobbyingAdvertisementTransaction(DisclosureFilingId, request)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.CreateLobbyingAdvertisementTransaction(DisclosureFilingId, request, CancellationToken.None);

        // Assert
        var actionResult = result.Result as OkObjectResult;
        Assert.Multiple(() =>
        {
            Assert.That(actionResult, Is.Not.Null);
            Assert.That(actionResult!.Value, Is.EqualTo(expectedResponse));
        });
        await _transactionSvc.Received(1).CreateLobbyingAdvertisementTransaction(DisclosureFilingId, request);
    }

    [Test]
    public async Task EditLobbyingAdvertisementTransaction_WithFilingId_ReturnsOkWithTransactionDto()
    {
        // Arrange
        var request = new LobbyingAdvertisementRequestDto()
        {
            TransactionId = SuccessTransactionId1,
            Amount = PaymentAmount1,
            DistributionMethodId = 1,
            AdditionalInformation = string.Empty,
            PublicationDate = DateTime.UtcNow,
        };
        var expectedResponse = new TransactionResponseDto { Id = SuccessTransactionId1, Valid = true, ValidationErrors = new List<WorkFlowError>() };

        _ = _transactionSvc.EditLobbyingAdvertisementTransaction(Arg.Any<long>(), request)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.EditLobbyingAdvertisementTransaction(DisclosureFilingId, request, CancellationToken.None);

        // Assert
        var actionResult = result.Result as OkObjectResult;
        Assert.Multiple(() =>
        {
            Assert.That(actionResult, Is.Not.Null);
            Assert.That(actionResult!.Value, Is.EqualTo(expectedResponse));
        });
        await _transactionSvc.Received(1).EditLobbyingAdvertisementTransaction(Arg.Any<long>(), request);
    }

    [Test]
    public async Task GetLobbyingAdvertisementTransactionByFilingId_ReturnsOk()
    {
        // Arrange
        var request = new PaymentMadeToLobbyingCoalitionRequestDto(DisclosureFilingId, FilerId, PaymentAmount1, RegistrationFilingId, FilerContactId);

        var expectedResponse = new LobbyingAdvertisement
        {
            Id = 1,
            Amount = new Currency(300),
            PublicationDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            DistributionMethodId = 1,
            LegislatorId = null,
            DistributionMethodDescription = "Description",
            AdditionalInformation = null,
        };

        _ = _transactionSvc.GetLobbyingAdvertisementTransactionByFilingId(DisclosureFilingId)
            .Returns(expectedResponse);

        var expectedResult = new LobbyingAdvertisementItemResponse(expectedResponse);

        // Act
        var result = await _controller.GetLobbyingAdvertisementTransactionByFilingId(DisclosureFilingId, CancellationToken.None);

        // Assert
        var actionResult = result.Result as OkObjectResult;
        Assert.That(actionResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(actionResult.StatusCode, Is.Not.Null);
            Assert.That(actionResult.StatusCode, Is.EqualTo(200));
        });

        var response = actionResult.Value as LobbyingAdvertisementItemResponse;
        Assert.Multiple(() =>
        {
            Assert.That(response, Is.Not.Null);
            Assert.That(response!.Id, Is.EqualTo(expectedResult.Id));
        });
    }

    [Test]
    public async Task GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId_ReturnsNotFound()
    {
        // Arrange
        _ = _transactionSvc.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(DisclosureFilingId)
            .Returns((PaymentMadeToLobbyingCoalitionResponse?)null);

        // Act
        var result = await _controller.GetLobbyingAdvertisementTransactionByFilingId(DisclosureFilingId);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

}
