// <copyright file="UpdateFilerContactTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Contacts;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Contacts;

/// <summary>
/// Contains tests for the <see cref="UpdateFilerContact" /> operation.
/// </summary>
[TestFixture]
[TestOf(typeof(UpdateFilerContact))]
[Parallelizable(ParallelScope.All)]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class UpdateFilerContactTests
{
    private readonly IDecisionsService _decisions = Substitute.For<IDecisionsService>();
    private readonly IAuditService _auditService = Substitute.For<IAuditService>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();
    private readonly ILogger<UpdateFilerContact> _logger = Substitute.For<ILogger<UpdateFilerContact>>();

    /// <summary>
    /// Tests that the <see cref="UpdateFilerContact.Execute" /> method returns
    /// a <see cref="Failure{FilerContact}.NotFound" /> instance when the contact is not found.
    /// </summary>
    /// <returns>A task that represents the asynchronous test.</returns>
    [Test]
    public async Task Execute_WhenContactIsNotFound_ReturnsNotFound()
    {
        await using var factory = new DatabaseContextFactory();
        await using var db = await factory.CreateContext();

        var data = await db.PrepareContactsData();

        var contact = data.Contacts[0];

        var command = new TestCommand(default, contact);

        var handler = new UpdateFilerContact(db, _decisions, _auditService, _dateTimeSvc, _logger);

        var result = await handler.Execute(command);

        Assert.That(result, Is.InstanceOf<Failure<FilerContact>.NotFound>());
    }

    /// <summary>
    /// Tests that the <see cref="UpdateFilerContact.Execute" /> method returns
    /// a <see cref="Failure{FilerContact}.InvalidState" /> instance when <see cref="UpdateFilerContactCommand.Apply" />
    /// fails.
    /// </summary>
    /// <returns>A task that represents the asynchronous test.</returns>
    [Test]
    public async Task Execute_WhenApplyFails_ReturnsInvalidState()
    {
        await using var factory = new DatabaseContextFactory();
        await using var db = await factory.CreateContext();

        var data = await db.PrepareContactsData();

        var contact = data.Contacts[0];

        var command = new TestCommand(contact.Id, contact, fails: true);

        var handler = new UpdateFilerContact(db, _decisions, _auditService, _dateTimeSvc, _logger);

        var result = await handler.Execute(command) as Failure<FilerContact>.InvalidState;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(command.ApplyWasCalled, Is.True);
            Assert.That(result!.Expected, Is.EqualTo(TestCommand.ExpectedType));
            Assert.That(result.Actual, Is.EqualTo(TestCommand.ActualType));
            Assert.That(result.Message, Is.EqualTo(TestCommand.InvalidStateMessage));
        });
    }

    /// <summary>
    /// Tests that the <see cref="UpdateFilerContact.Execute" /> method calls the SaveChanges method on the
    /// database context.
    /// </summary>
    /// <returns>A task that represents the asynchronous test.</returns>
    [Test]
    public async Task Execute_Calls_SaveChanges()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareContactsData();

        var eventHandler = Substitute.For<EventHandler<SavedChangesEventArgs>>();

        context.SavedChanges += eventHandler;

        var contact = data.Contacts[0];

        var command = new TestCommand(contact.Id, contact);

        var handler = new UpdateFilerContact(context, _decisions, _auditService, _dateTimeSvc, _logger);

        await handler.Execute(command);

        eventHandler.Received(1)
            .Invoke(Arg.Any<object>(), Arg.Is<SavedChangesEventArgs>(e => e.EntitiesSavedCount == 1));
    }

    /// <summary>
    /// Tests that the <see cref="UpdateFilerContact.Execute" /> method returns a <see cref="Success{FilerContact}" />
    /// instance when the operation is successful.
    /// </summary>
    /// <returns>A task that represents the asynchronous test.</returns>
    [Test]
    public async Task Execute_Returns_Success()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareContactsData();

        var contact = data.Contacts[0];

        var command = new TestCommand(contact.Id, contact);

        var handler = new UpdateFilerContact(context, _decisions, _auditService, _dateTimeSvc, _logger);

        var result = await handler.Execute(command) as Success<FilerContact>;

        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.EqualTo(contact));
    }

    private sealed class TestCommand : UpdateFilerContactCommand
    {
        public const string InvalidStateMessage = "Invalid state.";

        public const string ExpectedType = "ExpectedType";

        public const string ActualType = "ActualType";

        private readonly FilerContact _contact;

        [SetsRequiredMembers]
        public TestCommand(long id, FilerContact contact, bool fails = false)
        {
            Id = id;
            Fails = fails;
            _contact = contact;
        }

        public bool ApplyWasCalled { get; private set; }

        private bool Fails { get; }

        public override IResult<FilerContact> Apply(FilerContact contact)
        {
            ApplyWasCalled = true;

            return Fails
                ? new Failure<FilerContact>.InvalidState(ExpectedType, ActualType, InvalidStateMessage)
                : new Success<FilerContact>(ModifyContact(_contact));
        }

        private static FilerContact ModifyContact(FilerContact contact)
        {
            contact.PhoneNumberList!.PhoneNumbers.Add(new() { Number = "+12312312345", Type = "Extra" });

            return contact;
        }
    }
}
