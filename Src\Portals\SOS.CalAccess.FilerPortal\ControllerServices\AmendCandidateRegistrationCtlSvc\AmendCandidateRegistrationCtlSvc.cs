using System.Globalization;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.AmendCandidateRegistration;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.ControllerServices.AmendCandidateRegistrationCtlSvc;

public class AmendCandidateRegistrationCtlSvc(
    ICandidateIntentionRegistrationSvc candidateIntentionRegistrationSvc,
    IDecisionsValidationMapService decisionsValidationMapService, IDateTimeSvc dateTimeSvc,
    IStringLocalizer<SharedResources> localizer) : BaseCandidateRegistrationCtlSvc, IAmendCandidateRegistrationCtlSvc
{
    #region Page01
    /// <summary>
    /// Handles submission of Page01 form data to WebApi
    /// If the model passed contains an ID, this will call the UPDATE endpoint, otherwise it will call the CREATE endpoint.
    /// </summary>
    /// <param name="model">View model used for Page01 form</param>
    /// <param name="modelState">ModelState property from View calling this method</param>
    /// <param name="cancellationToken"></param>
    /// <param name="isSubmission">Flag indicating whether endpoint should check for all required fields or not</param>
    /// <returns></returns>
    public async Task<long?> Page01Submit(AmendCandidateRegistrationStep01ViewModel model, ModelStateDictionary modelState, CancellationToken cancellationToken, bool isSubmission = true)
    {
        var previousRegistration = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(model.RegistrationId);

        CandidateIntentionStatementRequest request = MapCandidateIntentionStatementViewModelToRequest(model);
        request.CheckRequiredFieldsFlag = isSubmission;
        request.Version = previousRegistration?.Version;
        request.ParentId = previousRegistration?.Id;
        var statusApproved = RegistrationStatus.Accepted.Id;
        RegistrationResponseDto res;
        // Case: Amendment from original filing
        if (model.RegistrationId == model.OriginalId)
        {
            request.PoliticalPartyId = previousRegistration?.PoliticalPartyId;
            request.ExpenditureLimitAccepted = previousRegistration?.ExpenditureLimitAccepted;
            request.Version = previousRegistration?.Version.GetValueOrDefault() + 1;
            res = await candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(request);
        }
        // Case: Amendment of an approved amendment
        else if (model.StatusId == statusApproved)
        {
            // RegistrationId will be the previous filing
            request.ParentId = model.RegistrationId;
            request.PoliticalPartyId = previousRegistration?.PoliticalPartyId;
            request.ExpenditureLimitAccepted = previousRegistration?.ExpenditureLimitAccepted;
            request.ContributedPersonalExcessFundsOn = previousRegistration?.ContributedPersonalExcessFundsOn;
            // Map election race to registration
            request.ElectionRaceId = previousRegistration?.ElectionRaceId;
            request.Version = previousRegistration?.Version.GetValueOrDefault() + 1;
            res = await candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(request);
        }
        else
        {
            res = await candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatement(model.RegistrationId, request);
        }
        if (res.AddressValidationResult?.AddressResults?.Count > 0)
        {
            model.AddressValidationResponse = new();
            PopulateAddressValidation(res.AddressValidationResult.AddressResults, model.AddressValidationResponse);
        }
        if (!res.Valid)
        {
            decisionsValidationMapService.ApplyErrorsToModelState(GetCisFieldValidationMap(model), res.ValidationErrors, modelState);
        }

        return res.Id;
    }

    /// <summary>
    /// Handles submission of AddressConfirmation with candidate form data to WebApi
    /// If the model passed contains an ID, this will call the UPDATE endpoint, otherwise it will call the CREATE endpoint.
    /// </summary>
    /// <param name="model">View model used for Page01 form</param>
    /// <param name="modelState">ModelState property from View calling this method</param>
    /// <param name="cancellationToken"></param>  
    /// <returns></returns>
    public async Task<long?> Page01Apply(AmendCandidateRegistrationStep01ViewModel model, ModelStateDictionary modelState, CancellationToken cancellationToken)
    {
        if (model.CandidateAddressConfirmation != null && model.CandidateAddressConfirmation.IsSuggested && model.AddressValidationResponse != null && model.AddressValidationResponse.CandidateAddressValidationStatus == AddressValidationStatus.SuggestedChanges)
        {
            model.CandidateAddress.Street = model.AddressValidationResponse.CandidateSuggestedAddress.Street;
            model.CandidateAddress.Street2 = model.AddressValidationResponse.CandidateSuggestedAddress.Street2;
            model.CandidateAddress.City = model.AddressValidationResponse.CandidateSuggestedAddress.City;
            model.CandidateAddress.State = model.AddressValidationResponse.CandidateSuggestedAddress.State;
            model.CandidateAddress.Zip = model.AddressValidationResponse.CandidateSuggestedAddress.Zip;
        }
        if (model.MailingAddressConfirmation != null && model.MailingAddressConfirmation.IsSuggested && model.AddressValidationResponse != null && model.AddressValidationResponse.MailingAddressValidationStatus == AddressValidationStatus.SuggestedChanges)
        {
            model.MailingAddress.Street = model.AddressValidationResponse.MailingSuggestedAddress.Street;
            model.MailingAddress.Street2 = model.AddressValidationResponse.MailingSuggestedAddress.Street2;
            model.MailingAddress.City = model.AddressValidationResponse.MailingSuggestedAddress.City;
            model.MailingAddress.State = model.AddressValidationResponse.MailingSuggestedAddress.State;
            model.MailingAddress.Zip = model.AddressValidationResponse.MailingSuggestedAddress.Zip;
        }

        CandidateIntentionStatementRequest request = MapCandidateIntentionStatementViewModelToRequest(model);

        RegistrationResponseDto response;
        if (model.RegistrationId != model.OriginalId)
        {
            response = await candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatement(model.RegistrationId, request);
        }
        else
        {
            response = await candidateIntentionRegistrationSvc.CreateCandidateIntentionStatement(request);
        }
        if (response.AddressValidationResult?.AddressResults?.Count > 0)
        {
            model.AddressValidationResponse = new();
            PopulateAddressValidation(response.AddressValidationResult.AddressResults, model.AddressValidationResponse);
        }
        if (!response.Valid)
        {
            decisionsValidationMapService.ApplyErrorsToModelState(GetCisFieldValidationMap(model), response.ValidationErrors, modelState);
        }
        return response.Id;
    }
    #endregion

    #region Page02
    /// <summary>
    /// Calls WebApi to save selections made in the Amend Election step.
    /// </summary>
    /// <param name="registrationId">ID of the registration filing record. Parent ID of this form.</param>
    /// <param name="model">Election02 ViewModel source for building the request body</param>
    /// <param name="modelState">Reference to current Action's ModelState so that workflow errors in the response can be set to it.</param>
    /// <param name="isSubmission">Flag indicating whether the user is calling this endpoint to save or submit</param>
    /// <returns></returns>
    public async Task Page02Submit(long registrationId, AmendCandidateRegistrationStep02ViewModel model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        var electionreq = new UpdateRegistrationElectionRequest
        {
            CountyId = model.SelectedDistrict ?? 0, //countynumber 
            DistrictId = model.SelectedDistrict ?? 0, //districtnumber
            ElectionId = model.SelectedElection ?? 0, //electiontypeid            
            ElectionYear = model.SelectedElectionYear ?? "", //electionyear
            Jurisdiction = model.SelectedJurisdiction ?? "", //jurisdiction
            OfficeId = model.SelectedOffice ?? 0, //officeid
            PartyId = model.SelectedPartyAffiliation ?? 0, // partyid
            CheckRequiredFieldsFlag = isSubmission
        };
        var response = await candidateIntentionRegistrationSvc.LinkElectionToCandidateIntentionStatement(registrationId, electionreq);

        if (!response.Valid)
        {
            Page02ApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    /// <summary>
    /// Maps workflow errors to ModelState so that they can be displayed next to the appropriate field.
    /// </summary>
    /// <param name="errors">List of validation errors received from Decisions.</param>
    /// <param name="modelState">Reference to current Action's ModelState so that workflow errors in the response can be set to it.</param>
    private void Page02ApplyErrorsToModelState(List<WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            switch (error.FieldName)
            {
                case "FR_CR_Filing_CandidateIntention_ElectionInformation_Data.Party":
                case "PoliticalParty":
                    modelState.AddModelError("SelectedPartyAffiliation", ReplaceFieldName(error.Message, localizer[ResourceConstants.Party].Value));
                    break;
                default:
                    modelState.AddModelError("", error.Message);
                    break;
            }
        }
    }
    #endregion

    #region Page03
    /// <summary>
    /// Get preveious registartion if one exists.
    /// </summary>
    /// <param name="parentId">Parent Id</param>
    /// <param name="originalId">Original Id</param>
    /// <returns></returns>
    public async Task<CandidateIntentionStatementResponseDto?> Page03GetPastRegistration(long? parentId, long? originalId)
    {
        long? previousId = parentId ?? originalId;

        if (previousId == null)
        {
            return null;
        }

        var data = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement((long)previousId);
        return data;
    }

    /// <summary>
    /// Get view model before or on deadline.
    /// </summary>
    /// <param name="latestAmendment">Latest amendment</param>
    /// <param name="previousAmendment">Previuos amendment</param>
    /// <returns></returns>
    public async Task<AmendCandidateRegistrationStep03ViewModel> Page03GetViewModelBeforeOnDeadline(
        CandidateIntentionStatementResponseDto latestAmendment,
        CandidateIntentionStatementResponseDto previousAmendment,
        DateTime electionDate,
        decimal limitAmount,
        bool isPersonalFundsReadonly,
        long registrationId
        )
    {
        int changeCount = 0;
        bool? lastValue = previousAmendment.ExpenditureLimitAccepted;
        long? parentId = previousAmendment.ParentId;
        long? originalId = previousAmendment.OriginalId;

        // Iterate backwards through the parentId to determine if two changes
        // were made or not.
        while (changeCount < 2)
        {
            var previous = await Page03GetPastRegistration(parentId, originalId);
            if (previous == null)
            {
                break;
            }

            if (previous.ExpenditureLimitAccepted != lastValue)
            {
                changeCount++;
                lastValue = previous.ExpenditureLimitAccepted;
            }

            if (previous.Id == originalId)
            {
                break;
            }
            parentId = previous.ParentId;
        }

        bool isRadioReadOnly = changeCount >= 2;
        var model = new AmendCandidateRegistrationStep03ViewModel
        {
            Id = registrationId,
            ElectionDate = electionDate,
            ExpenditureLimitAccepted = latestAmendment.ExpenditureLimitAccepted ? "true" : "false",
            ExpenditureLimitAmount = limitAmount,
            ExpenditureExceeded = latestAmendment.ExpenditureExceeded,
            DidContributedPersonalExcessFunds = latestAmendment.ContributedPersonalExcessFundsOn != null,
            ContributedPersonalExcessFundsOn = latestAmendment.ContributedPersonalExcessFundsOn,
            IsLimitRadioReadOnly = isRadioReadOnly,
            IsLimitCheckboxReadOnly = true,
            IsPersonalFundsSectionReadOnly = isPersonalFundsReadonly
        };
        return model;
    }

    public AmendCandidateRegistrationStep03ViewModel Page03BuildViewModelAfterDeadline(
        long registrationId, Election? election, CandidateIntentionStatementResponseDto? previousAmendment,
        CandidateIntentionStatementResponseDto? latestAmendment, decimal limitAmount, bool isPersonalFundsReadonly)
    {
        // NOTE: ExpenditureLimitAccepted and ExpenditureExceeded use previous accepted amendment value
        // to cover rare business case where deadline is shifted forward and edits are no longer allowed.
        return new AmendCandidateRegistrationStep03ViewModel
        {
            Id = registrationId,
            ElectionDate = election?.ElectionDate,
            ExpenditureLimitAccepted = previousAmendment?.ExpenditureLimitAccepted == true ? "true" : "false",
            ExpenditureLimitAmount = limitAmount,
            ExpenditureExceeded = previousAmendment?.ExpenditureExceeded ?? false,
            DidContributedPersonalExcessFunds = latestAmendment?.ContributedPersonalExcessFundsOn != null,
            ContributedPersonalExcessFundsOn = latestAmendment?.ContributedPersonalExcessFundsOn,
            IsLimitRadioReadOnly = true,
            IsLimitCheckboxReadOnly = true,
            IsPersonalFundsSectionReadOnly = isPersonalFundsReadonly
        };
    }

    /// <summary>
    /// Build ViewModel for Page03 based on business rules.
    /// </summary>
    /// <param name="registrationId">Registration Id</param>
    /// <returns></returns>
    public async Task<AmendCandidateRegistrationStep03ViewModel> Page03BuildViewModel(long registrationId)
    {
        // For deadlines
        var election = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatementElection(registrationId);
        // Latest amendment
        var latestAmendment = await candidateIntentionRegistrationSvc.GetCandidateIntentionStatement(registrationId);
        // Previous registration
        var previousAmendment = await Page03GetPastRegistration(latestAmendment?.ParentId, latestAmendment?.OriginalId);

        // FilingDeadline - is the date deadline before election
        // ExpenditureLimitAcceptanceDeadline - X days after election
        double daysAfterElectionDeadline = election?.ExpenditureLimitAcceptanceDeadline ?? 0.0;
        var expenditureDeadlineDate = election?.ElectionDate.AddDays(daysAfterElectionDeadline);
        var today = dateTimeSvc.GetCurrentDateTime();
        decimal limitAmount = latestAmendment?.ExpenditureCeilingAmount ?? 0.0m;
        bool isPersonalFundsReadonly = previousAmendment?.ContributedPersonalExcessFundsOn != null;

        // Case: before/on deadline
        if (today <= election?.FilingDeadline)
        {
            if (latestAmendment == null || previousAmendment == null)
            {
                throw new ArgumentException("latestAmendment or previousAmendment is null");
            }

            var model = await Page03GetViewModelBeforeOnDeadline(latestAmendment, previousAmendment,
                election.ElectionDate, limitAmount, isPersonalFundsReadonly, registrationId);
            return model;
        }
        // case: after FilingDeadline, but before election date
        if (today > election?.FilingDeadline && today <= election?.ElectionDate)
        {
            return new AmendCandidateRegistrationStep03ViewModel
            {
                Id = registrationId,
                ElectionDate = election?.ElectionDate,
                ExpenditureLimitAccepted = latestAmendment?.ExpenditureLimitAccepted == true ? "true" : "false",
                ExpenditureLimitAmount = limitAmount,
                ExpenditureExceeded = latestAmendment?.ExpenditureExceeded ?? false,
                DidContributedPersonalExcessFunds = latestAmendment?.ContributedPersonalExcessFundsOn != null,
                ContributedPersonalExcessFundsOn = latestAmendment?.ContributedPersonalExcessFundsOn,
                IsLimitRadioReadOnly = true,
                IsLimitCheckboxReadOnly = true,
                IsPersonalFundsSectionReadOnly = isPersonalFundsReadonly
            };
        }
        // case: after election date, but before deadline
        if (today < expenditureDeadlineDate)
        {
            return new AmendCandidateRegistrationStep03ViewModel
            {
                Id = registrationId,
                ElectionDate = election?.ElectionDate,
                PreviousExpenditureLimitAccepted = previousAmendment?.ExpenditureLimitAccepted == true ? "true" : "false",
                ExpenditureLimitAccepted = latestAmendment?.ExpenditureLimitAccepted == true ? "true" : "false",
                ExpenditureLimitAmount = limitAmount,
                ExpenditureExceeded = latestAmendment?.ExpenditureExceeded ?? false,
                DidContributedPersonalExcessFunds = latestAmendment?.ContributedPersonalExcessFundsOn != null,
                ContributedPersonalExcessFundsOn = latestAmendment?.ContributedPersonalExcessFundsOn,
                IsLimitRadioReadOnly = true,
                IsLimitCheckboxReadOnly = false,
                IsPersonalFundsSectionReadOnly = isPersonalFundsReadonly
            };
        }
        // case: after deadline
        if (today > expenditureDeadlineDate)
        {
            return Page03BuildViewModelAfterDeadline(registrationId, election, previousAmendment,
                latestAmendment, limitAmount, isPersonalFundsReadonly);
        }

        return new AmendCandidateRegistrationStep03ViewModel();
    }

    public void Page03ValidatePersonalFundsDate(AmendCandidateRegistrationStep03ViewModel model,
        ModelStateDictionary modelState)
    {
        if (model.DidContributedPersonalExcessFunds && model.ContributedPersonalExcessFundsOn == null)
        {
            modelState.AddModelError("ContributedPersonalExcessFundsOn",
                string.Format(CultureInfo.CurrentCulture, localizer["Common.FieldIsRequired"].Value, "Date"));

        }
    }

    /// <summary>
    /// Submit for Page03.
    /// </summary>
    /// <param name="model">AmendCandidateRegistrationStep03ViewModel</param>
    /// <param name="modelState">Model state.</param>
    /// <param name="isSubmission">Is submission</param>
    /// <returns></returns>
    public async Task Page03Submit(
        AmendCandidateRegistrationStep03ViewModel model,
        ModelStateDictionary modelState,
        bool isSubmission = true
    )
    {
        try
        {
            CandidateIntentionStatementExpenditureLimitRequest payload = new()
            {
                ExpenditureLimitAccepted = model.ExpenditureLimitAccepted == "true",
                ContributedPersonalExcessFundsOn = model.ContributedPersonalExcessFundsOn,
                ExpenditureExceeded = model.ExpenditureExceeded
            };
            await candidateIntentionRegistrationSvc.UpdateCandidateIntentionStatementExpenditureLimit(model.Id!.Value, payload);
        }
        catch (Exception ex)
        {
            throw new ArgumentException("Invalid request:" + ex);
        }
    }
    #endregion
}
