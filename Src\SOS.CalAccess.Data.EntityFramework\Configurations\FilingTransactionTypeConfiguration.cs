using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Configurations;

/// <summary>
/// Configures the <see cref="FilingTransaction"/> entity.
/// </summary>
public class FilingTransactionTypeConfiguration : IEntityTypeConfiguration<FilingTransaction>
{
    /// <inheritdoc />
    public void Configure(EntityTypeBuilder<FilingTransaction> builder)
    {
        builder.ToTable(nameof(FilingTransaction), t => t.IsTemporal());

        builder.HasOne(ft => ft.Filing)
            .WithMany(f => f.FilingTransactions)
            .HasForeignKey(x => x.FilingId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(ft => ft.Transaction)
            .WithMany(t => t.FilingTransactions)
            .HasForeignKey(x => x.TransactionId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(x => x.Active)
            .HasDefaultValue(true);

        // Disable audits as this entity
        // type and its definitions are not meant to be
        // modified at runtime.
        builder.DisableAudits();
    }
}
