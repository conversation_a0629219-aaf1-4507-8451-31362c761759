// <copyright file="UpdateFilingTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Tests.SeedData;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filings;

/// <summary>
/// Contains tests for the <see cref="UpdateFiling" /> command handler.
/// </summary>
[TestFixture]
[TestOf(typeof(UpdateFiling))]
[Parallelizable(ParallelScope.All)]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public sealed class UpdateFilingTests
{
    private readonly IAuditService _audit = Substitute.For<IAuditService>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    /// <summary>
    /// Asserts that the <see cref="UpdateFiling" /> Execute method returns a NotFound failure when the
    /// specified filing does not exist in the database.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_ReturnsFailure_WhenFilingDoesNotExist()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        _ = await context.PrepareFilingData();

        var handler = new UpdateFiling(context, _audit, _dateTimeSvc);

        var command = new TestCommand(default);

        var result = await handler.Execute(command);

        Assert.That(result, Is.AssignableTo<Failure<Filing>.NotFound>());
    }

    /// <summary>
    /// Asserts that the <see cref="UpdateFiling" /> Execute method returns a InvalidState failure when the
    /// <see cref="UpdateFilingCommand.Apply(Filing)" /> method returns a failure.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_WhenApplyFails_ReturnsInvalidState()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var filing = data.DraftFiling;

        var command = new TestCommand(filing.Id, fails: true);

        var handler = new UpdateFiling(context, _audit, _dateTimeSvc);

        var result = await handler.Execute(command);

        Assert.Multiple(() =>
        {
            Assert.That(command.ApplyWasCalled, Is.True);
            Assert.That(result, Is.InstanceOf<Failure<Filing>.InvalidState>());
        });
    }

    /// <summary>
    /// Asserts that the <see cref="UpdateFiling" /> Execute method returns an InvalidState failure when the
    /// filing is not in the "Pending" status.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    [Ignore("Temp disable invalid state logic")]
    public async Task Execute_WhenFilingIsNotPending_ReturnsInvalidState()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var filing = data.ApprovedFiling;

        var command = new TestCommand(filing.Id);

        var handler = new UpdateFiling(context, _audit, _dateTimeSvc);

        var result = await handler.Execute(command);

        Assert.That(result, Is.InstanceOf<Failure<Filing>.InvalidState>());
    }

    /// <summary>
    /// Asserts that the <see cref="UpdateFiling" /> Execute method calls the SaveChanges method on the database
    /// context.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_Calls_SaveChanges()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var eventHandler = Substitute.For<EventHandler<SavedChangesEventArgs>>();

        context.SavedChanges += eventHandler;

        var filing = data.DraftFiling;

        var command = new TestCommand(filing.Id);

        var handler = new UpdateFiling(context, _audit, _dateTimeSvc);

        _ = await handler.Execute(command);

        eventHandler.Received(1)
            .Invoke(Arg.Any<object>(), Arg.Is<SavedChangesEventArgs>(e => e.EntitiesSavedCount == 1));
    }

    /// <summary>
    /// Asserts that the <see cref="UpdateFiling" /> Execute method returns a Success result.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_Returns_Success()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var filing = data.DraftFiling;

        var command = new TestCommand(filing.Id);

        var handler = new UpdateFiling(context, _audit, _dateTimeSvc);

        var result = await handler.Execute(command) as Success<Filing>;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Has.Property(nameof(Success<Filing>.Value)).EqualTo(command.FilingApplyArgument));
        });
    }

    private sealed class TestCommand : UpdateFilingCommand
    {
        private readonly bool _fails;

        [SetsRequiredMembers]
        public TestCommand(long id, bool fails = false)
        {
            Id = id;
            _fails = fails;
        }

        public bool ApplyWasCalled { get; private set; }

        public Filing? FilingApplyArgument { get; private set; }

        public override IResult<Filing> Apply(Filing filing)
        {
            ApplyWasCalled = true;
            FilingApplyArgument = filing;

            if (_fails)
            {
                return new Failure<Filing>.InvalidState(new(), new(), "Test failure.");
            }

            return new Success<Filing>(ModifyFiling(filing));
        }

        private static Filing ModifyFiling(Filing filing)
        {
            var fixedStartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
            filing.StartDate = fixedStartDate.AddDays(-1);
            return filing;
        }
    }
}
