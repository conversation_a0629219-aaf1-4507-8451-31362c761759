// <copyright file="CreateFilingTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Tests.SeedData;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filings;

/// <summary>
/// Contains tests for the <see cref="CreateFiling" /> command handler.
/// </summary>
[TestFixture]
[TestOf(typeof(CreateFiling))]
[Parallelizable(ParallelScope.All)]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class CreateFilingTests
{
    private readonly IAuditService _audit = Substitute.For<IAuditService>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    /// <summary>
    /// Asserts that the <see cref="CreateFiling" /> Execute method returns a NotFound failure when the
    /// specified filer does not exist in the database.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_ReturnsFailure_WhenFilerDoesNotExist()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        _ = await context.PrepareFilingData();

        var handler = new CreateFiling(context, _audit, _dateTimeSvc);

        var command = new TestCommand(default, _dateTimeSvc);

        var result = await handler.Execute(command);

        Assert.That(result, Is.AssignableTo<Failure<Filing>.NotFound>());
    }

    /// <summary>
    /// Asserts that the <see cref="CreateFiling" /> Execute method returns a InvalidState failure when the
    /// specified start date is after the end date.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_ReturnsFailure_WhenStartDateIsAfterEndDate()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var handler = new CreateFiling(context, _audit, _dateTimeSvc);

        var command = new TestCommand(data.Filer.Id, _dateTimeSvc, true);

        var result = await handler.Execute(command);

        Assert.That(result, Is.AssignableTo<Failure<Filing>.InvalidState>());
    }

    /// <summary>
    /// Asserts that the <see cref="CreateFiling" /> Execute method returns a Success when the
    /// specified filer exists in the database.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_Calls_Build()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var command = new TestCommand(data.Filer.Id, _dateTimeSvc);

        var handler = new CreateFiling(context, _audit, _dateTimeSvc);

        // Sanity check
        Assert.That(command.BuildWasCalled, Is.False);

        _ = await handler.Execute(command);

        Assert.That(command.BuildWasCalled, Is.True);
    }

    /// <summary>
    /// Asserts that the <see cref="CreateFiling" /> Execute method calls the SaveChanges method on the database context.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_Calls_SaveChanges()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var eventHandler = Substitute.For<EventHandler<SavedChangesEventArgs>>();

        context.SavedChanges += eventHandler;

        var command = new TestCommand(data.Filer.Id, _dateTimeSvc);

        var handler = new CreateFiling(context, _audit, _dateTimeSvc);

        _ = await handler.Execute(command);

        // Assert
        Assert.That(context.ChangeTracker.HasChanges(), Is.False, "There should be no pending changes after Execute.");

        eventHandler.Received(1).Invoke(
            Arg.Any<object>(),
            Arg.Is<SavedChangesEventArgs>(e => e.EntitiesSavedCount > 0)
        );
    }

    /// <summary>
    /// Asserts that the <see cref="CreateFiling" /> Execute method returns a <see cref="Success{Filing}" /> instance.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
    [Test]
    public async Task Execute_ReturnsSuccess()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var handler = new CreateFiling(context, _audit, _dateTimeSvc);

        var command = new TestCommand(data.Filer.Id, _dateTimeSvc);

        var result = await handler.Execute(command) as Success<Filing>;

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.EqualTo(command.FilingReturned));
    }

    private sealed class TestCommand : CreateFilingCommand
    {
        private readonly bool _fails;
        private readonly IDateTimeSvc _dateTimeSvc;

        [SetsRequiredMembers]
        public TestCommand(long filerId, IDateTimeSvc dateTimeSvc, bool fails = false)
        {
            FilerId = filerId;
            _fails = fails;
            _dateTimeSvc = dateTimeSvc;
        }

        public bool BuildWasCalled { get; private set; }

        public Filing? FilingReturned { get; private set; }

        public override IResult<Filing> Build()
        {
            BuildWasCalled = true;

            if (_fails)
            {
                return new Failure<Filing>.InvalidState(new(), new(), "Test failure.");
            }

            var date = _dateTimeSvc.GetCurrentDateTime();

            FilingReturned = new Filing
            {
                FilerId = FilerId,
                StartDate = date,
                StatusId = FilingStatus.Pending.Id,
                EndDate = date.AddDays(1),
                FilingTypeId = FilingType.AdHocFiling.Id,
            };

            return new Success<Filing>(FilingReturned);
        }
    }
}
