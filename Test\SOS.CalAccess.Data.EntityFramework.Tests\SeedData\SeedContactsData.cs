// <copyright file="SeedData.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using SOS.CalAccess.Data.EntityFramework.Repositories.SeedData;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Contacts;

/// <summary>
/// Provides methods to seed data for tests.
/// </summary>
public static class SeedContactsData
{

    /// <summary>
    /// Gets a new random long id.
    /// There is no guarantee that these are unique.
    /// </summary>
    /// <returns>A new long.</returns>
    public static long RandomId() => Random.Shared.NextInt64(1, long.MaxValue);

    /// <summary>
    /// Prepares the data for testing by seeding the database with a registration and associated filer.
    /// </summary>
    /// <param name="context">The database context to use for seeding data.</param>
    /// <returns>An <see cref="AssertionReferences"/> object containing references to the seeded data.</returns>
    public static async Task<AssertionReferences> PrepareContactsData(this DatabaseContext context)
    {
        var registration = GetRegistration();

        context.CandidateIntentionStatements.Add(registration);

        await context.SaveChangesAsync();

        var filer = GetFiler(registration.Id);
        registration.Filer = filer;

        await context.SaveChangesAsync();

        IReadOnlyList<FilerContact> contacts =
            [GetCommitteeContact(filer.Id), GetIndividualContact(filer.Id), GetOrganizationContact(filer.Id)];

        context.FilerContacts.AddRange(contacts);

        await context.SaveChangesAsync();

        return new AssertionReferences(contacts, filer);
    }

    /// <summary>
    /// Creates an organization contact with the specified filer ID.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>An <see cref="OrganizationContact"/> instance.</returns>
    internal static OrganizationContact GetOrganizationContact(long filerId) => new()
    {
        FilerId = filerId,
        OrganizationName = "OrganizationName",
        PhoneNumberList = new Models.Common.PhoneNumberList()
        {
            PhoneNumbers = new List<Models.Common.PhoneNumber>()
            {
                new()
                {
                    Number = "123123",
                    Type = "Phone",
                    Extension = "202",
                    CountryCode = "USA",
                },
                new()
                {
                    Number = "123123",
                    Type = "Fax",
                    Extension = "202",
                    CountryCode = "USA",
                }
            }
        },
        AddressList = new Models.Common.AddressList()
        {
            Addresses = new List<Models.Common.Address>()
            {
                new()
                {
                    City = "Dummy City",
                    Country = "Dummy Country",
                    Purpose = "Home",
                    State = "CA",
                    Street = "123 Honolulu",
                    Type = "Home",
                    Zip = "96812",
                    Street2 = "Salt Lake"
                }
            }
        },
        EmailAddressList = new Models.Common.EmailAddressList()
        {
            EmailAddresses = new List<Models.Common.EmailAddress>()
            {
                new()
                {
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "OK"
                }
            }
        },
    };

    private static CommitteeContact GetCommitteeContact(long filerId) => new()
    {
        CommitteeName = "CommitteeName",
        CommitteeType = "CommitteeType",
        AddressList = new()
        {
            Addresses = new List<Models.Common.Address>()
            {
                new()
                {
                    City = "Dummy City",
                    Country = "Dummy Country",
                    Purpose = "Home",
                    State = "CA",
                    Street = "123 Honolulu",
                    Type = "Home",
                    Zip = "96812",
                    Street2 = "Salt Lake"
                }
            }
        },
        PhoneNumberList = new()
        {
            PhoneNumbers = new List<Models.Common.PhoneNumber>()
            {
                new()
                {
                    Number = "123123",
                    Type = "Phone",
                    Extension = "202",
                    CountryCode = "USA",
                },
                new()
                {
                    Number = "123123",
                    Type = "Fax",
                    Extension = "202",
                    CountryCode = "USA",
                }
            }
        },
        EmailAddressList = new()
        {
            EmailAddresses = new List<Models.Common.EmailAddress>()
            {
                new()
                {
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "OK"
                }
            }
        },
        FilerId = filerId,
    };

    private static IndividualContact GetIndividualContact(long filerId) => new()
    {
        FilerId = filerId,
        FirstName = "FirstName",
        MiddleName = "MiddleName",
        LastName = "LastName",
        Employer = "Employer",
        Occupation = "Occupation",
        AddressList = new()
        {
            Addresses = new List<Models.Common.Address>()
            {
                new()
                {
                    City = "Dummy City",
                    Country = "Dummy Country",
                    Purpose = "Home",
                    State = "CA",
                    Street = "123 Honolulu",
                    Type = "Home",
                    Zip = "96812",
                    Street2 = "Salt Lake"
                }
            }
        },
        PhoneNumberList = new()
        {
            PhoneNumbers = new List<Models.Common.PhoneNumber>()
            {
                new()
                {
                    Number = "123123",
                    Type = "Phone",
                    Extension = "202",
                    CountryCode = "USA",
                },
                new()
                {
                    Number = "123123",
                    Type = "Fax",
                    Extension = "202",
                    CountryCode = "USA",
                }
            }
        },
        EmailAddressList = new()
        {
            EmailAddresses = new List<Models.Common.EmailAddress>()
            {
                new()
                {
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "OK"
                }
            }
        }
    };

    private static Filer GetFiler(long currentRegistrationId) => new()
    {
        CurrentRegistrationId = currentRegistrationId,
        FilerStatusId = FilerStatus.Active.Id,
    };
    private static CandidateIntentionStatement GetRegistration() => new()
    {
        StatusId = RegistrationStatus.Accepted.Id,
        Name = "Test Candidate",
        ApprovedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        AddressList = SeedValues.GetTestAddressList(),
        PhoneNumberList = SeedValues.GetTestPhoneNumberList(),
    };

    /// <summary>
    /// Represents references to the seeded data for assertions.
    /// </summary>
    public sealed record AssertionReferences(IReadOnlyList<FilerContact> Contacts, Filer Filer);
}
