// <copyright file="CreateFilerContactTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Contacts;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Contacts;

/// <summary>
/// Contains tests for the <see cref="CreateFilerContact" /> command.
/// </summary>
[TestFixture]
[TestOf(typeof(CreateFilerContact))]
[Parallelizable(ParallelScope.All)]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public sealed class CreateFilerContactTests
{
    private readonly IDecisionsService _decisions = Substitute.For<IDecisionsService>();
    private readonly IAuditService _auditService = Substitute.For<IAuditService>();
    private readonly ILogger<CreateFilerContact> _logger =
        Substitute.For<ILogger<CreateFilerContact>>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    /// <summary>
    /// Asserts that the <see cref="CreateFilerContact" /> command handler
    /// returns a <see cref="Failure{FilerContact}.NotFound" /> instance
    /// when the specified filer does not exist in the database.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task CreateFilerContact_ReturnsNotFoundFailure_WhenFilerDoesNotExist()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        _ = await context.PrepareContactsData();

        var handler = new CreateFilerContact(context, _decisions, _auditService, _dateTimeSvc, _logger);

        var command = new TestCommand(default, SeedContactsData.GetOrganizationContact(default));

        var result = await handler.Execute(command);

        Assert.That(result, Is.AssignableTo<Failure<FilerContact>.NotFound>());
    }

    /// <summary>
    /// Asserts that the <see cref="CreateFilerContact" /> command handler calls the
    /// Build method on the command instance.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task CreateFilerContact_Calls_Build()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareContactsData();

        var command = new TestCommand(data.Filer.Id, SeedContactsData.GetOrganizationContact(data.Filer.Id));

        var handler = new CreateFilerContact(context, _decisions, _auditService, _dateTimeSvc, _logger);

        _ = await handler.Execute(command);

        Assert.That(command.BuildWasCalled, Is.True);
    }

    /// <summary>
    /// Asserts that the <see cref="CreateFilerContact" /> command handler
    /// calls the SaveChanges method on the database context.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task CreateFilerContact_Calls_SaveChanges()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareContactsData();

        var eventHandler = Substitute.For<EventHandler<SavedChangesEventArgs>>();

        context.SavedChanges += eventHandler;

        var handler = new CreateFilerContact(context, _decisions, _auditService, _dateTimeSvc, _logger);

        _ = await handler.Execute(new TestCommand(data.Filer.Id, SeedContactsData.GetOrganizationContact(data.Filer.Id)));

        // three entities get saved:
        // - a new contact
        // - the associated address list
        // - the associated phone list
        // - the associated email address list
        eventHandler.Received(1).Invoke(Arg.Any<object>(), Arg.Is<SavedChangesEventArgs>(e => e.EntitiesSavedCount == 8));
    }

    /// <summary>
    /// Asserts that the <see cref="CreateFilerContact" /> command handler
    /// returns a <see cref="Success{FilerContact}" /> instance.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task CreateFilerContact_ReturnsSuccess_WhenContactIsCreated()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareContactsData();

        var expectedContact = SeedContactsData.GetOrganizationContact(data.Filer.Id);

        var command = new TestCommand(data.Filer.Id, expectedContact);

        var handler = new CreateFilerContact(context, _decisions, _auditService, _dateTimeSvc, _logger);

        var result = await handler.Execute(command) as Success<FilerContact>;

        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.EqualTo(expectedContact));
    }

    private sealed class TestCommand : CreateFilerContactCommand
    {
        private readonly FilerContact _contact;

        [SetsRequiredMembers]
        public TestCommand(long filerId, FilerContact contact)
        {
            FilerId = filerId;
            _contact = contact;
        }

        public bool BuildWasCalled { get; private set; }

        public override FilerContact Build()
        {
            BuildWasCalled = true;
            return _contact;
        }
    }
}
