using System.ComponentModel.DataAnnotations.Schema;
using SOS.CalAccess.Models.Common;

namespace SOS.CalAccess.Models.FilerRegistration.Filers;
public sealed class FilerLink : IIdentifiable<long>
{
    /// <summary>
    /// Gets or sets the Primary Key identifier.
    /// </summary>
    [Documentation("Primary Key identifier of the Filer Link.")]
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets the FilerId
    /// </summary>
    [Documentation("Foreign key identifier of the Primary Filer Entity, referring to the Filer table.")]
    public required long FilerId { get; set; }

    /// <summary>
    /// Foreign Key relationship with Filer.
    /// </summary>
    [ForeignKey("FilerId")]
    public Filer Filer { get; set; } = default!;

    /// <summary>
    /// Gets or sets the Linked Entity Id.
    /// </summary>
    [Documentation("Foreign key identifier of the Linked Entity, referring to the Filer table.")]
    public required long LinkedEntityId { get; set; }

    /// <summary>
    /// Foreign Key relationship with Filer
    /// </summary>
    public Filer LinkedEntity { get; set; } = default!;

    /// <summary>
    /// Gets or sets the Effective Date
    /// </summary>
    [Documentation("Effective Date of the Filer Link.")]
    public required DateTime EffectiveDate { get; set; }

    /// <summary>
    /// Gets or sets the Termination Date.
    /// </summary>
    [Documentation("Termination Date of the Filer Link.")]
    public DateTime? TerminationDate { get; set; }

    /// <summary>
    /// Gets or sets the Filer Link Type.
    /// </summary>
    [Documentation("Foreign key reference to the Identifier of the Filer Link Type.")]
    public required long FilerLinkTypeId { get; set; }

    /// <summary>
    /// Foreign Key relationship with FilerLinkType.
    /// </summary>
    [ForeignKey("FilerLinkTypeId")]
    public FilerLinkType FilerLinkType { get; set; } = default!;

    /// <summary>
    /// Gets or sets the identifier of the user who created the record.
    /// </summary>
    [Documentation("Identifier of the User who created this record..")]
    public required long CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the identifier of the user who last modified the record.
    /// </summary>
    [Documentation("Identifier of the User who last modified this record.")]
    public required long ModifiedBy { get; set; }

    /// <summary>
    /// Gets or sets the active status of a transaction
    /// </summary>
    [Documentation("The flag to indicate the active status of this record (soft-delete).")]
    public bool Active { get; set; } = true;
}
