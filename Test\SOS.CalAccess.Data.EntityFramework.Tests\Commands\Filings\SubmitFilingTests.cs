// <copyright file="SubmitFilingTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>


using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Audit.Attribution;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Messaging;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Data.EntityFramework.Tests.SeedData;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filings;

/// <summary>
/// Tests for the <see cref="SubmitFiling"/> command handler implementation.
/// </summary>
[TestFixture]
[TestOf(typeof(SubmitFiling))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
internal sealed class SubmitFilingTests
{
    private readonly ISendCorrespondence _correspondence = Substitute.For<ISendCorrespondence>();
    private readonly ISendNotification _notification = Substitute.For<ISendNotification>();
    private readonly IActionAttributionProvider _actionAttribution = Substitute.For<IActionAttributionProvider>();
    private readonly IGetAllTransactions _getAllTransactions = Substitute.For<IGetAllTransactions>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    /// <summary>
    /// Test the failure behavior of the command when the target filing does not exist.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task SubmitFiling_ReturnsNotFound_WhenFilingIsNotAvailable()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var submit = GetSUT(context);

        var result = await submit.Execute(default);

        Assert.That(result, Is.InstanceOf<Failure<Filing>.NotFound>());
    }

    /// <summary>
    /// Test the failure behavior of the command when the target filing is not in a valid state.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task SubmitFiling_ReturnsInvalidState_WhenFilingIsNotPending()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var submit = GetSUT(context);

        var result = await submit.Execute(data.ApprovedFiling.Id);

        Assert.That(result, Is.InstanceOf<Failure<Filing>.InvalidState>());
    }

    /// <summary>
    /// Test that external communication services are called when the command succeeds.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task SubmitFiling_CallsServicesStubs_OnSuccess()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        _getAllTransactions
            .Execute(Arg.Is<Filing>((f) => f.Id == data.DraftFiling.Id), Arg.Any<CancellationToken>())
            .Returns(data.Transactions);

        var submit = GetSUT(context);

        await submit.Execute(data.DraftFiling.Id);

        await _correspondence.Received(1)
            .Execute(Arg.Any<ISendCorrespondence.Command>(), Arg.Any<CancellationToken>());

        await _notification.Received(1)
            .Execute(Arg.Any<ISendNotification.Command>(), Arg.Any<CancellationToken>());
    }

    /// <summary>
    /// Test that the command returns a success result when execution completes without issue.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task SubmitFiling_ReturnsSuccess_OnCompletion()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        _getAllTransactions
            .Execute(Arg.Is<Filing>((f) => f.Id == data.DraftFiling.Id), Arg.Any<CancellationToken>())
            .Returns(data.Transactions);

        var submit = GetSUT(context);

        var result = await submit.Execute(data.DraftFiling.Id);

        if (result.Unwrap(out var success, out var failure))
        {
            Assert.Fail("Should have succeeded");
        }
        else
        {
            Assert.Multiple(() =>
            {
                Assert.That(success.FilingTransactions.Select(m => m.Transaction), Is.EquivalentTo(data.Transactions));
                Assert.That(success.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            });
        }
    }

    /// <summary>
    /// Test that the command returns a success result when execution completes without issue.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task SubmitFiling_AutomaticallyApproves_WhenFilingMeetsCondition()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        _getAllTransactions
            .Execute(Arg.Is<Filing>((f) => f.Id == data.AutoApprovableFiling.Id), Arg.Any<CancellationToken>())
            .Returns(data.Transactions);

        var submit = GetSUT(context);

        var result = await submit.Execute(data.AutoApprovableFiling.Id);

        if (result.Unwrap(out var success, out var failure))
        {
            Assert.Fail("Should have succeeded");
        }
        else
        {
            Assert.Multiple(() =>
            {
                Assert.That(success.FilingTransactions.Select(m => m.Transaction), Is.EquivalentTo(data.Transactions));
                Assert.That(success.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            });
        }
    }

    private SubmitFiling GetSUT(DatabaseContext context)
    {
        var hub = new MockMessagingHub(_actionAttribution, _correspondence, _notification);

        return new(
            context,
            Substitute.For<IDecisionsService>(),
            Substitute.For<IAuditService>(),
            _getAllTransactions,
            new AutoApprovalCheck(),
            new ApproveFiling(context, hub, _dateTimeSvc),
            hub,
            _dateTimeSvc);
    }
}
