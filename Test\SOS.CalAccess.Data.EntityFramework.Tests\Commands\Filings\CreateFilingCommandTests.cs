// <copyright file="CreateFilingCommandTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging.Abstractions;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filings;

/// <summary>
/// Tests for the <see cref="CreateFilingCommand"/> class.
/// </summary>
[TestFixture]
[Parallelizable(ParallelScope.All)]
[TestOf(typeof(CreateFilingCommand))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public sealed class CreateFilingCommandTests
{
    /// <summary>
    /// Tests that the <see cref="CreateFilingCommand.Build"/> method returns a <see cref="Filing"/> object with the
    /// correct properties.
    /// </summary>
    [Test]
    public void Build_WhenCalled_ReturnsFiling()
    {
        // Arrange
        var dateTimeSvc = new DateTimeSvc(NullLogger<DateTimeSvc>.Instance, new DateTimeSvcOptions(false, null, "Pacific Standard Time"));
        var endDate = dateTimeSvc.GetCurrentDateTime();
        var startDate = dateTimeSvc.GetCurrentDateTime().AddDays(-1);

        var command = new CreateFilingCommand { EndDate = endDate, StartDate = startDate, FilerId = default, };

        // Act
        var filing = (command.Build() as Success<Filing>)?.Value;

        // Assert
        Assert.That(filing, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(filing!.EndDate, Is.EqualTo(endDate));
            Assert.That(filing.StartDate, Is.EqualTo(startDate));
            Assert.That(filing.FilerId, Is.EqualTo(command.FilerId));
            Assert.That(filing.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
        });
    }

    /// <summary>
    /// Tests that the <see cref="CreateFilingCommand.Build"/> method returns a <see cref="Failure{Filing}.InvalidState"/>
    /// result when the start date is after the end date.
    /// </summary>
    [Test]
    public void Build_WhenStartDateIsAfterEndDate_ReturnsInvalidStateFailure()
    {
        // Arrange
        var date = new DateTime(2025, 01, 01, 12, 00, 00, DateTimeKind.Local);
        var startDate = date.AddDays(1);
        var endDate = date;

        var command = new CreateFilingCommand { EndDate = endDate, StartDate = startDate, FilerId = default, };

        // Act
        var result = command.Build();

        // Assert
        Assert.That(result, Is.InstanceOf<Failure<Filing>.InvalidState>());
    }
}
