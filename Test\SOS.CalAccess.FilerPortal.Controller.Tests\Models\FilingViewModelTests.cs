using System.ComponentModel.DataAnnotations;
using NUnit.Framework;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Filings;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
public class FilingViewModelTests
{
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }
    [Test]
    public void Constructor_WithFilingItemResponse_SetsPropertiesCorrectly()
    {
        // Arrange
        var response = new FilingItemResponse("AmendmentExplanation", _dateNow, 1, "Sample name", 1, "Submitted", "LobbyistEmployerReport", 2, 1, 1, 1, _dateNow.AddMonths(-1), 1, _dateNow, 1, 1);


        // Act
        var viewModel = new FilingViewModel(response);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(viewModel.StartDate, Is.EqualTo(response.StartDate));
            Assert.That(viewModel.EndDate, Is.EqualTo(response.EndDate));
            Assert.That(viewModel.AmendmentExplanation, Is.EqualTo("AmendmentExplanation"));
        });
    }

    [Test]
    public void ActionPrefix_ReturnsEdit_WhenIdIsSet()
    {
        var model = new FilingViewModel { Id = 1 };
        Assert.That(model.ActionPrefix, Is.EqualTo("Edit"));
    }

    [Test]
    public void ActionPrefix_ReturnsNew_WhenIdIsNull()
    {
        var model = new FilingViewModel { Id = null };
        Assert.That(model.ActionPrefix, Is.EqualTo("New"));
    }

    [Test]
    public void Validate_ReturnsError_WhenStartDateIsAfterOrEqualToEndDate()
    {
        var model = new FilingViewModel
        {
            StartDate = new DateTime(2025, 4, 29, 0, 0, 0, 0),
            EndDate = new DateTime(2025, 4, 29, 0, 0, 0, 0)
        };

        var context = new ValidationContext(model);
        var results = new List<ValidationResult>(model.Validate(context));

        Assert.Multiple(() =>
        {
            Assert.That(results, Is.Not.Empty);
            Assert.That(results[0].ErrorMessage, Is.EqualTo("The start date must be before the end date."));
        });
    }

    [Test]
    public void Validate_DoesNotReturnError_WhenStartOrEndDateIsMinValue()
    {
        var modelWithMinStart = new FilingViewModel
        {
            StartDate = DateTime.MinValue,
            EndDate = new DateTime(2025, 4, 29, 0, 0, 0, 0)
        };

        var context = new ValidationContext(modelWithMinStart);
        var results1 = new List<ValidationResult>(modelWithMinStart.Validate(context));
        Assert.That(results1, Is.Empty, "Should not return error when StartDate is MinValue.");

        var modelWithMinEnd = new FilingViewModel
        {
            StartDate = new DateTime(2025, 4, 29, 0, 0, 0, 0),
            EndDate = DateTime.MinValue
        };

        context = new ValidationContext(modelWithMinEnd);
        var results2 = new List<ValidationResult>(modelWithMinEnd.Validate(context));
        Assert.That(results2, Is.Empty, "Should not return error when EndDate is MinValue.");
    }

    [Test]
    public void Validate_ReturnsNoErrors_WhenDatesAreValid()
    {
        var model = new FilingViewModel
        {
            StartDate = new DateTime(2025, 1, 1, 0, 0, 0, 0),
            EndDate = new DateTime(2025, 6, 1, 0, 0, 0, 0)
        };

        var context = new ValidationContext(model);
        var results = new List<ValidationResult>(model.Validate(context));

        Assert.That(results, Is.Empty);
    }

    [Test]
    public void ToRequest_ConvertsModelToRequestCorrectly()
    {
        var model = new FilingViewModel
        {
            StartDate = new DateTime(2025, 1, 1, 0, 0, 0, 0),
            EndDate = new DateTime(2025, 6, 1, 0, 0, 0, 0),
            AmendmentExplanation = "Test explanation",
            LegislativeSessionId = 1
        };

        var request = model.ToRequest();

        Assert.Multiple(() =>
        {
            Assert.That(request.StartDate, Is.EqualTo(model.StartDate));
            Assert.That(request.EndDate, Is.EqualTo(model.EndDate));
            Assert.That(request.AmendmentExplanation, Is.EqualTo("Test explanation"));
            Assert.That(request.LegislativeSessionId, Is.EqualTo(1));
        });
    }

    [Test]
    public void ToRequest_UsesEmptyString_WhenAmendmentExplanationIsNull()
    {
        var model = new FilingViewModel
        {
            StartDate = _dateNow.AddDays(-1),
            EndDate = _dateNow,
            AmendmentExplanation = null
        };

        var request = model.ToRequest();

        Assert.That(request.AmendmentExplanation, Is.EqualTo(string.Empty));
    }
}
