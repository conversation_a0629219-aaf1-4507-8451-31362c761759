using System.Globalization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NSubstitute;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.WebApi.Filings;

namespace SOS.CalAccess.WebApi.Tests.Filings;
public sealed class FilingsControllerTest
{
    private readonly Mock<IFilingSvc> _filingSvcMock;
    private readonly Mock<IReport72HSvc> _report72hSvcMock;
    private readonly FilingsController _controller;
    private readonly Mock<IAuthorizationService> _authServiceMock;
    private readonly IDateTimeSvc _dateTimeSvc;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    public FilingsControllerTest()
    {
        _filingSvcMock = new Mock<IFilingSvc>();
        _report72hSvcMock = new Mock<IReport72HSvc>();
        _authServiceMock = new Mock<IAuthorizationService>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _controller = new FilingsController(_authServiceMock.Object, _dateTimeSvc);
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ReturnsOkOnSuccess()
    {
        // Arrange
        var model = new UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto { IsMemberOfLobbyingCoalition = true };
        var response = new LobbyistEmployerReport
        {
            Id = 1,
            EndDate = _dateNow,
            FilerId = 1001,
            ParentId = 2001,
            StartDate = _dateNow.AddMonths(-1),
            StatusId = 2,
            SubmittedDate = _dateNow.AddDays(-10),
            Version = 1,
            TotalPaymentsToInHouseLobbyists = new Currency(500.00m),
            IsMemberOfLobbyingCoalition = true
        };

        _filingSvcMock.Setup(x => x.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(1, model.IsMemberOfLobbyingCoalition)).ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(1, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value?.IsMemberOfLobbyingCoalition, Is.True);
    }

    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ReturnsNotFoundIfReportIsNull()
    {
        // Arrange
        var model = new UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalitionDto { IsMemberOfLobbyingCoalition = true };
        _filingSvcMock.Setup(x => x.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(
            It.IsAny<long>(),
            model.IsMemberOfLobbyingCoalition))
            .ReturnsAsync(null as LobbyistEmployerReport);

        // Act
        var result = await _controller.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(1, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_ReturnsOkOnSuccess()
    {
        // Arrange
        var body = new SubmitLobbyistEmployerReportDto { DiligenceStatementVerified = true };
        var report = new LobbyistEmployerReport
        {
            Id = 1,
            EndDate = _dateNow,
            FilerId = 1001,
            ParentId = 2001,
            StartDate = _dateNow.AddMonths(-1),
            StatusId = 2,
            SubmittedDate = _dateNow.AddDays(-10),
            Version = 1,
            DiligenceStatementVerified = body.DiligenceStatementVerified
        };
        var response = new ValidatedLobbyistEmployerReport(1, true, [], report);


        _filingSvcMock.Setup(x => x.SubmitLobbyistEmployerReport(1, body.DiligenceStatementVerified)).ReturnsAsync(response);

        // Act
        var result = await _controller.SubmitLobbyistEmployerReport(1, body, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value?.DiligenceStatementVerified, Is.EqualTo(body.DiligenceStatementVerified));
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_ReturnsNotFoundIfReportIsNull()
    {
        // Arrange
        var body = new SubmitLobbyistEmployerReportDto { DiligenceStatementVerified = true };
        _filingSvcMock.Setup(x => x.SubmitLobbyistEmployerReport(
            It.IsAny<long>(),
            body.DiligenceStatementVerified))
            .ReturnsAsync(null as ValidatedLobbyistEmployerReport);

        // Act
        var result = await _controller.SubmitLobbyistEmployerReport(1, body, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task SubmitLobbyistReport_ReturnsOkOnSuccess()
    {
        // Arrange
        var body = new SubmitLobbyistReportDto { DiligenceStatementVerified = true };
        var report = new LobbyistReport
        {
            Id = 1,
            EndDate = _dateNow,
            FilerId = 1001,
            ParentId = 2001,
            StartDate = _dateNow.AddMonths(-1),
            StatusId = 2,
            SubmittedDate = _dateNow.AddDays(-10),
            Version = 1,
            DiligenceStatementVerified = body.DiligenceStatementVerified
        };
        var response = new SubmitLobbyingReportResponse(1, true, [], report);


        _filingSvcMock.Setup(x => x.SubmitLobbyistReport(1, body.DiligenceStatementVerified)).ReturnsAsync(response);

        // Act
        var result = await _controller.SubmitLobbyistReport(1, body, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value?.DiligenceStatementVerified, Is.EqualTo(body.DiligenceStatementVerified));
    }

    [Test]
    public async Task SubmitLobbyistReport_ReturnsNotFoundIfReportIsNull()
    {
        // Arrange
        var body = new SubmitLobbyistReportDto { DiligenceStatementVerified = true };
        _filingSvcMock.Setup(x => x.SubmitLobbyistReport(
            It.IsAny<long>(),
            body.DiligenceStatementVerified))
            .ReturnsAsync(null as SubmitLobbyingReportResponse);

        // Act
        var result = await _controller.SubmitLobbyistReport(1, body, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task SubmitReport48H_ReturnsOkOnSuccess()
    {
        // Arrange
        var body = new SubmitLobbyistEmployerReportDto { DiligenceStatementVerified = true };
        var report = new Report48H
        {
            Id = 1,
            EndDate = _dateNow,
            FilerId = 1001,
            ParentId = 2001,
            StartDate = _dateNow.AddMonths(-1),
            StatusId = 2,
            SubmittedDate = _dateNow.AddDays(-10),
            Version = 1,
            DiligenceStatementVerified = body.DiligenceStatementVerified
        };
        var response = new SubmitLobbyingReportResponse(1, true, [], report);


        _filingSvcMock.Setup(x => x.SubmitReport48H(1, body.DiligenceStatementVerified)).ReturnsAsync(response);

        // Act
        var result = await _controller.SubmitReport48H(1, body, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value?.DiligenceStatementVerified, Is.EqualTo(body.DiligenceStatementVerified));
    }

    [Test]
    public async Task SubmitReport48H_ReturnsNotFoundIfReportIsNull()
    {
        // Arrange
        var body = new SubmitLobbyistEmployerReportDto { DiligenceStatementVerified = true };
        _filingSvcMock.Setup(x => x.SubmitReport48H(
            It.IsAny<long>(),
            body.DiligenceStatementVerified))
            .ReturnsAsync(null as SubmitLobbyingReportResponse);

        // Act
        var result = await _controller.SubmitReport48H(1, body, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateLobbyistEmployerCampaignContributions_ReturnsOk_WhenReportExists()
    {
        // Arrange
        var id = 1L;
        var model = new UpdateLobbyistEmployerCampaignContributionsDto
        {
            ContributionsInExistingStatements = true,
            RelatedFilerId = 123
        };
        var report = new LobbyistEmployerReport
        {
            Id = 1,
            EndDate = _dateNow,
            FilerId = 1001,
            ParentId = 2001,
            StartDate = _dateNow.AddMonths(-1),
            StatusId = 2,
            SubmittedDate = _dateNow.AddDays(-10),
            Version = 1,
            ContributionsInExistingStatements = true
        };

        var response = new LobbyistEmployerReportResponse(report);

        _filingSvcMock.Setup(svc => svc.UpdateLobbyistEmployerCampaignContributions(It.IsAny<long>(), model.ContributionsInExistingStatements, model.RelatedFilerId))
            .ReturnsAsync(report);

        // Act
        var result = await _controller.UpdateLobbyistEmployerCampaignContributions(id, model, _filingSvcMock.Object, CancellationToken.None);

        // Assert

        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task UpdateLobbyistEmployerCampaignContributions_ReturnsNotFound_WhenReportIsNull()
    {
        // Arrange
        var id = 1L;
        var model = new UpdateLobbyistEmployerCampaignContributionsDto
        {
            ContributionsInExistingStatements = true,
            RelatedFilerId = 123
        };

        _filingSvcMock.Setup(svc => svc.UpdateLobbyistEmployerCampaignContributions(It.IsAny<long>(), model.ContributionsInExistingStatements, model.RelatedFilerId))
            .ReturnsAsync(null as LobbyistEmployerReport);

        // Act
        var result = await _controller.UpdateLobbyistEmployerCampaignContributions(id, model, _filingSvcMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task CancelFiling_ReturnsOk_WhenFilingExists()
    {
        // Arrange
        var filingId = 123;
        var mockFiling = new Filing { Id = filingId, StatusId = FilingStatus.Pending.Id };

        _filingSvcMock
            .Setup(svc => svc.CancelFiling(filingId))
            .ReturnsAsync(mockFiling);

        // Act
        var result = await _controller.CancelFiling(filingId, _filingSvcMock.Object);

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        Assert.That(okResult?.StatusCode, Is.EqualTo(200));

        var response = okResult?.Value as FilingItemResponse;
        Assert.That(response, Is.Not.Null);
        Assert.That(response?.Id, Is.EqualTo(filingId));
    }

    [Test]
    public async Task CancelFiling_ReturnsNotFound_WhenFilingIsNull()
    {
        // Arrange
        var filingId = 999;

        _filingSvcMock
            .Setup(svc => svc.CancelFiling(filingId))
            .ReturnsAsync((Filing?)null);

        // Act
        var result = await _controller.CancelFiling(filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateLobbyistEmployerLumpSums_ShouldReturnOk_WhenReportIsNotNull()
    {
        // Arrange
        long filingId = 1;
        var model = new UpdateLobbyistEmployerReportDto
        {
            Id = filingId,
            TotalOverheadExpense = 500.25m,
            TotalUnderThresholdPayments = 200.75m
        };

        var expectedResponse = new UpdateLobbyistEmployerLumpSumResponseDto(
            filingId,
            true,
            new List<Models.Common.WorkFlowError>(),
            (Currency)model.TotalOverheadExpense.Value,
            (Currency)model.TotalUnderThresholdPayments.Value
        );

        _filingSvcMock
            .Setup(svc => svc.UpdateLobbyistEmployerLumpSums(
                filingId,
                model.TotalOverheadExpense,
                model.TotalUnderThresholdPayments))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateLobbyistEmployerLumpSums(filingId, model, _filingSvcMock.Object);

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var dto = okResult?.Value as UpdateLobbyistEmployerLumpSumResponseDto;
        Assert.That(dto, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(dto?.FilingId, Is.EqualTo(filingId));
            Assert.That(dto?.TotalOverheadExpense, Is.EqualTo((Currency)model.TotalOverheadExpense.Value));
            Assert.That(dto?.TotalUnderThresholdPayments, Is.EqualTo((Currency)model.TotalUnderThresholdPayments.Value));
        });
    }



    [Test]
    public async Task GetFiling_ReturnsFilingItemResponse_WhenFilingExists()
    {
        // Arrange
        var id = 123;
        var mockFiling = new Filing
        {
            Id = id,
            StatusId = FilingStatus.Pending.Id,
            FilingPeriodId = 100,
            FilingPeriod = new FilingPeriod { Id = 100, StartDate = _dateNow.AddDays(1), EndDate = _dateNow.AddDays(5) }
        };

        _filingSvcMock
            .Setup(svc => svc.GetFilingById(id))
            .ReturnsAsync(mockFiling);

        // Act
        var result = await _controller.GetFiling(id, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.Null);
        var response = result.Value;
        Assert.That(response, Is.Not.Null);
        Assert.That(response.Id, Is.EqualTo(id));
    }

    [Test]
    public async Task GetFiling_ReturnsNotFound_WhenFilingDoesNotExist()
    {
        // Arrange
        var id = 999;
        _filingSvcMock
            .Setup(svc => svc.GetFilingById(id))
            .ReturnsAsync((Filing?)null);

        // Act
        var result = await _controller.GetFiling(id, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task GetDisclosureFilingReports_ReturnsValue()
    {
        // Arrange
        _filingSvcMock
            .Setup(svc => svc.GetFilingReports())
            .ReturnsAsync(new List<FilingReportGridDto>());

        // Act
        var result = await _controller.GetDisclosureFilingReports(_filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task CreateReport72H_ReturnsExpectedResponse()
    {
        // Arrange
        var filerId = 1L;
        var filing = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.Report72h.Id,
            StatusId = FilingStatus.Pending.Id,
        };

        var response = new FilingResponseDto(filing);

        _filingSvcMock
            .Setup(x => x.CreateReport72H(filerId))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateReport72H(filerId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.Status, Is.EqualTo(FilingStatus.Pending.Id));
        });
    }

    [Test]
    public async Task GetAllFilingPeriodsForFiler_ReturnsExpectedCollection_WhenFilingTypeIdIsNull()
    {
        // Arrange
        var filerId = 1;
        var expectedFilingPeriods = new List<FilingPeriodDto>
        {
            new() { Id = 1, StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture), EndDate = DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture), HasFiling = true },
            new() { Id = 2, StartDate = DateTime.Parse("2023-04-01", CultureInfo.InvariantCulture), EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture), HasFiling = false }
        };

        _ = _filingSvcMock
            .Setup(svc => svc.GetAllFilingPeriodsForFiler(filerId, null, null))
            .ReturnsAsync(expectedFilingPeriods);

        // Act
        var result = await _controller.GetAllFilingPeriodsForFiler(filerId, null, null, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedFilingPeriods));
        _filingSvcMock.Verify(svc => svc.GetAllFilingPeriodsForFiler(filerId, null, null), Times.Once);
    }

    [Test]
    public async Task GetAllFilingPeriodsForFiler_ReturnsExpectedCollection_WhenFilingTypeIdIsProvided()
    {
        // Arrange
        var filerId = 123L;
        var filingTypeId = 456L;
        var expectedFilingPeriods = new List<FilingPeriodDto>
        {
            new() { Id = 1, StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture), EndDate = DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture), HasFiling = true }
        };

        _ = _filingSvcMock
            .Setup(svc => svc.GetAllFilingPeriodsForFiler(filerId, filingTypeId, 1))
            .ReturnsAsync(expectedFilingPeriods);

        // Act
        var result = await _controller.GetAllFilingPeriodsForFiler(filerId, filingTypeId, 1, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedFilingPeriods));
        _filingSvcMock.Verify(svc => svc.GetAllFilingPeriodsForFiler(filerId, filingTypeId, 1), Times.Once);
    }

    [Test]
    public async Task GetAllFilingPeriodsForFiler_ReturnsEmptyCollection_WhenNoPeriodsExist()
    {
        // Arrange
        var filerId = 999L;
        var expectedFilingPeriods = new List<FilingPeriodDto>();

        _ = _filingSvcMock
            .Setup(svc => svc.GetAllFilingPeriodsForFiler(filerId, null, null))
            .ReturnsAsync(expectedFilingPeriods);

        // Act
        var result = await _controller.GetAllFilingPeriodsForFiler(filerId, null, 1, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task CreateLobbyistEmployerReportFiling_ReturnsFilingItemResponse_WhenCreationSucceeds()
    {
        // Arrange
        var id = 123L;
        var model = new CreateLobbyistEmployerReportDto
        {
            FilerId = id,
            FilingPeriodId = 456L
        };

        var mockFiling = new Filing
        {
            Id = 789L,
            FilerId = id,
            FilingPeriodId = model.FilingPeriodId.Value,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id
        };

        _ = _filingSvcMock
            .Setup(svc => svc.CreateLobbyistEmployerReport(model.FilerId, model.FilingPeriodId))
            .ReturnsAsync(mockFiling);

        // Act
        var result = await _controller.CreateLobbyistEmployerReportFiling(id, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        var response = result.Value;
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Id, Is.EqualTo(mockFiling.Id));
            Assert.That(response.FilerId, Is.EqualTo(id));
        });
        _filingSvcMock.Verify(svc => svc.CreateLobbyistEmployerReport(model.FilerId, model.FilingPeriodId), Times.Once);
    }

    [Test]
    public async Task CreateLobbyistReportFiling_ReturnsFilingItemResponse_WhenCreationSucceeds()
    {
        // Arrange
        var id = 123L;
        var model = new CreateLobbyistReportDto
        {
            FilerId = id,
            FilingPeriodId = 456L
        };

        var mockFiling = new Filing
        {
            Id = 789L,
            FilerId = id,
            FilingPeriodId = model.FilingPeriodId.Value,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistReport.Id
        };

        _ = _filingSvcMock
            .Setup(svc => svc.CreateLobbyistReport(model.FilerId, model.FilingPeriodId))
            .ReturnsAsync(mockFiling);

        // Act
        var result = await _controller.CreateLobbyistReportFiling(id, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        var response = result.Value;
        Assert.That(response, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(response.Id, Is.EqualTo(mockFiling.Id));
            Assert.That(response.FilerId, Is.EqualTo(id));
        });
        _filingSvcMock.Verify(svc => svc.CreateLobbyistReport(model.FilerId, model.FilingPeriodId), Times.Once);
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedBills_ReturnsValidationResult_WhenCalled()
    {
        // Arrange
        var request = new ValidateReport72HActionsLobbiedRequestDto
        {
            ActionsLobbied = new List<ActionsLobbiedRequestDto>
            {
                new() { OfficialPositionId = 1, OfficialPositionDescription = "Support" }
            }
        };

        var expectedResponse = new ValidateReport72HActionsLobbiedResponseDto
        {
            ValidationErrors = new List<WorkFlowError>
            {
                new WorkFlowError("OfficialPositionId", "Code", "Validation", "Required")
            }
        };

        _ = _filingSvcMock
            .Setup(svc => svc.ValidateReport72HActionsLobbiedBills(request))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedBills(request, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ValidationErrors, Is.EqualTo(expectedResponse.ValidationErrors));
        _filingSvcMock.Verify(svc => svc.ValidateReport72HActionsLobbiedBills(request), Times.Once);
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedAgencies_ReturnsValidationResult_WhenCalled()
    {
        // Arrange
        var request = new ValidateReport72HActionsLobbiedRequestDto
        {
            ActionsLobbied = new List<ActionsLobbiedRequestDto>
            {
                new() { AgencyId = 5, OfficialPositionId = 1, OfficialPositionDescription = "Test", AdministrativeAction = "Test"}
            }
        };

        var expectedResponse = new ValidateReport72HActionsLobbiedResponseDto
        {
            ValidationErrors = new List<WorkFlowError>()
        };

        _ = _filingSvcMock
            .Setup(svc => svc.ValidateReport72HActionsLobbiedAgencies(request))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.ValidateReport72HActionsLobbiedAgencies(request, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.ValidationErrors, Is.EqualTo(expectedResponse.ValidationErrors));
        _filingSvcMock.Verify(svc => svc.ValidateReport72HActionsLobbiedAgencies(request), Times.Once);
    }




    [Test]
    public async Task GetLegistlativeSessions_ReturnsOk_WhenSessionsExist()
    {
        // Arrange
        var mockSessions = new List<LegislativeSession>
        {
            new() { Id = 1, Name = "Session 2020", StartDate = _dateNow.AddYears(-1), EndDate = _dateNow },
            new() { Id = 2, Name = "Session 2021", StartDate = _dateNow, EndDate = _dateNow.AddYears(1) }
        };

        _filingSvcMock.Setup(f => f.GetAllLegislativeSessions())
            .Returns(Task.FromResult(mockSessions.AsEnumerable()));

        // Act
        var result = await _controller.GetLegistlativeSessions(_filingSvcMock.Object, CancellationToken.None);

        // Assert
        var actionResult = result;
        Assert.That(actionResult, Is.Not.Null);
        var okResult = actionResult.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var response = okResult.Value as LegislativeSessionResponseList;
        Assert.That(response, Is.Not.Null);
        Assert.That(response.Sessions, Has.Count.EqualTo(2));
    }

    [Test]
    public async Task GetLegistlativeSessions_ReturnsNotFound_WhenNoSessionsExist()
    {
        // Arrange
        _filingSvcMock.Setup(f => f.GetAllLegislativeSessions())
            .Returns(Task.FromResult(Enumerable.Empty<LegislativeSession>()));

        // Act
        var result = await _controller.GetLegistlativeSessions(_filingSvcMock.Object, CancellationToken.None);

        // Assert
        var actionResult = result;
        Assert.That(actionResult, Is.Not.Null);
        var notFoundResult = actionResult.Result as NotFoundResult;
        Assert.That(notFoundResult, Is.Not.Null);
    }

    [Test]
    public async Task CreateReport48H_ReturnsExpectedResponse()
    {
        // Arrange
        var filerId = 1L;
        var filing = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.Report48h.Id,
            StatusId = FilingStatus.Pending.Id,
        };

        var response = new FilingResponseDto(filing);

        _filingSvcMock
            .Setup(x => x.CreateReport48H(filerId))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateReport48H(filerId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.Status, Is.EqualTo(FilingStatus.Pending.Id));
        });
    }
    [Test]
    public async Task GetLobbyistEmployerReportActionsLobbied_ReturnsOk_WhenActionsExist()
    {
        // Arrange
        var id = 1L;
        var response = new ActionsLobbiedSummaryResponse
        {
            OtherActionsLobbied = "Test Action",
            AdministrativeActions = new List<ActionsLobbiedResponseDto>
            {
                new() { Id = 1, AdministrativeAction = "Admin Action 1" },
                new() { Id = 2, AdministrativeAction = "Admin Action 2" }
            }
        };

        _filingSvcMock
            .Setup(svc => svc.GetActionsLobbiedSummaryForFiling(id))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.GetActionsLobbiedSummaryForFiling(id, _filingSvcMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var resultValue = okResult?.Value as ActionsLobbiedSummaryResponse;
        Assert.That(resultValue, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(resultValue?.OtherActionsLobbied, Is.EqualTo("Test Action"));
            Assert.That(resultValue?.AdministrativeActions.Count(), Is.EqualTo(2));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerReportActionsLobbied_ReturnsOk_WhenUpdateIsSuccessful()
    {
        // Arrange
        var id = 1L;
        var request = new LobbyistEmployerActionsLobbiedRequest
        {
            OtherActionsLobbied = "Updated Action",
            AdministrativeActions = new List<ActionsLobbiedRequestDto>
            {
                new() { Id = 1, AdministrativeAction = "Updated Admin Action 1" }
            }
        };
        var response = new ActionsLobbiedSummaryResponse
        {
            OtherActionsLobbied = "Updated Action",
            AdministrativeActions = new List<ActionsLobbiedResponseDto>
            {
                new() { Id = 1, AdministrativeAction = "Updated Admin Action 1" }
            }
        };

        _filingSvcMock
            .Setup(svc => svc.UpdateLobbyistEmployerReportActionsLobbied(id, request))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateLobbyistEmployerReportActionsLobbied(id, request, _filingSvcMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var resultValue = okResult?.Value as ActionsLobbiedSummaryResponse;
        Assert.That(resultValue, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(resultValue?.OtherActionsLobbied, Is.EqualTo("Updated Action"));
            Assert.That(resultValue?.AdministrativeActions.Count(), Is.EqualTo(1));
        });
    }

    [Test]
    public async Task ValidateLobbyistEmployerReportActionsLobbiedAgencies_ReturnsValidationErrors_WhenErrorsExist()
    {
        // Arrange
        var request = new List<ActionsLobbiedRequestDto>
        {
            new() { AgencyId = 1, OfficialPositionId = 2, OfficialPositionDescription = "Test" }
        };
        var validationErrors = new List<WorkFlowError>
        {
            new("AgencyId", "Code", "Validation", "Required")
        };

        _filingSvcMock
            .Setup(svc => svc.ValidateLobbyistEmployerActionsLobbiedAgencies(request))
            .ReturnsAsync(validationErrors);

        // Act
        var result = await _controller.ValidateLobbyistEmployerReportActionsLobbiedAgencies(request, _filingSvcMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.EqualTo(validationErrors));
    }

    [Test]
    public async Task ValidateLobbyistEmployerReportActionsLobbiedAgencies_ReturnsEmptyList_WhenNoErrorsExist()
    {
        // Arrange
        var request = new List<ActionsLobbiedRequestDto>
        {
            new() { AgencyId = 1, OfficialPositionId = 2, OfficialPositionDescription = "Test" }
        };

        _filingSvcMock
            .Setup(svc => svc.ValidateLobbyistEmployerActionsLobbiedAgencies(request))
            .ReturnsAsync(new List<WorkFlowError>());

        // Act
        var result = await _controller.ValidateLobbyistEmployerReportActionsLobbiedAgencies(request, _filingSvcMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.Empty);
    }



    [Test]
    public async Task SubmitReport48H_ReturnsExpectedResponse()
    {
        // Arrange
        long filingId = 123L;
        bool? diligenceVerified = true;

        var model = new SubmitLobbyistEmployerReportDto
        {
            DiligenceStatementVerified = diligenceVerified
        };

        var report = new Report48H
        {
            Id = 1,
            EndDate = _dateNow,
            FilerId = 1001,
            ParentId = 2001,
            StartDate = _dateNow.AddMonths(-1),
            StatusId = FilingStatus.Accepted.Id,
            SubmittedDate = _dateNow.AddDays(-10),
            Version = 1,
            DiligenceStatementVerified = true
        };

        var expectedResponse = new SubmitLobbyingReportResponse(
            filingId,
            valid: true,
            validationErrors: [],
            report
        );

        _filingSvcMock
            .Setup(x => x.SubmitReport48H(filingId, diligenceVerified))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SubmitReport48H(filingId, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Value?.Valid, Is.True);
            Assert.That(result.Value?.Id, Is.EqualTo(filingId));
            Assert.That(result.Value?.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.Value?.DiligenceStatementVerified, Is.EqualTo(diligenceVerified));
            Assert.That(result.Value?.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task UpdatePucActivityPayment_ValidRequest_ReturnsExpectedResponse()
    {
        // Arrange
        long filingId = 12345;
        var request = new UpdatePucActivityPaymentRequestDto()
        {
            TotalPaymentsPucActivity = 1000
        };
        var expectedResponse = new UpdatePucActivityPaymentResponseDto()
        {
            Valid = true,
            ValidationErrors = []
        };

        _filingSvcMock
            .Setup(svc => svc.UpdatePucActivityPayment(filingId, request))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePucActivityPayment(filingId, request, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResponse));
        _filingSvcMock.Verify(svc => svc.UpdatePucActivityPayment(filingId, request), Times.Once);
    }

    [Test]
    public async Task GetCumulativePeriodStartByFilingId_ReturnsDateTime_WhenFilingExists()
    {
        // Arrange
        var filingId = 123L;
        var expectedDate = new DateTime(2023, 1, 1, 0, 0, 0, 0);

        _ = _filingSvcMock
            .Setup(svc => svc.GetCumulativePeriodStartByFilingId(filingId))
            .ReturnsAsync(expectedDate);

        // Act
        var result = await _controller.GetCumulativePeriodStartByFilingId(filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.EqualTo(expectedDate));
        _filingSvcMock.Verify(svc => svc.GetCumulativePeriodStartByFilingId(filingId), Times.Once);
    }

    [Test]
    public void GetCumulativePeriodStartByFilingId_PropagatesException_WhenServiceThrowsKeyNotFoundException()
    {
        // Arrange
        var filingId = 999L;
        var expectedException = new KeyNotFoundException($"Filing not found. Id={filingId}");

        _ = _filingSvcMock
            .Setup(svc => svc.GetCumulativePeriodStartByFilingId(filingId))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _controller.GetCumulativePeriodStartByFilingId(filingId, _filingSvcMock.Object));

        Assert.That(ex.Message, Is.EqualTo(expectedException.Message));
        _filingSvcMock.Verify(svc => svc.GetCumulativePeriodStartByFilingId(filingId), Times.Once);
    }

    [Test]
    public void GetCumulativePeriodStartByFilingId_PropagatesException_WhenServiceThrowsGenericException()
    {
        // Arrange
        var filingId = 456L;
        var expectedException = new InvalidOperationException("Something went wrong");

        _ = _filingSvcMock
            .Setup(svc => svc.GetCumulativePeriodStartByFilingId(filingId))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _controller.GetCumulativePeriodStartByFilingId(filingId, _filingSvcMock.Object));

        Assert.That(ex.Message, Is.EqualTo(expectedException.Message));
        _filingSvcMock.Verify(svc => svc.GetCumulativePeriodStartByFilingId(filingId), Times.Once);
    }

    [Test]
    public async Task SendForAttestationReport48H_ReturnsExpectedResponse()
    {
        // Arrange
        long filingId = 123L;
        var request = new Report48HSendForAttestationRequest
        {
            SelectedResponsibleOfficerIds = [1, 2]
        };
        var expectedResponse = new SubmitLobbyingReportResponse(123, true, new List<WorkFlowError>(), new Filing { Id = 123, StatusId = FilingStatus.Pending.Id });

        _filingSvcMock
            .Setup(svc => svc.SendForAttestationReport48H(filingId, request))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SendForAttestationReport48H(filingId, request, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));
        _filingSvcMock.Verify(svc => svc.SendForAttestationReport48H(filingId, request), Times.Once);
    }

    [Test]
    public async Task AddAssociatedFilerUserByFiling_ReturnsExpectedDto()
    {
        // Arrange
        long filingId = 456L;
        var request = new AddAssociatedFilerUserByFilingRequest
        {
            FilerRoleId = 61,
            UserId = 789
        };
        var expectedDto = new FilerUserDto { FilerRoleId = 61, UserId = 789 };

        _filingSvcMock
            .Setup(svc => svc.AddAssociatedFilerUserByFiling(filingId, request))
            .ReturnsAsync(expectedDto);

        // Act
        var result = await _controller.AddAssociatedFilerUserByFiling(filingId, request, _filingSvcMock.Object);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FilerRoleId, Is.EqualTo(61));
            Assert.That(result.UserId, Is.EqualTo(789));
        });
        _filingSvcMock.Verify(svc => svc.AddAssociatedFilerUserByFiling(filingId, request), Times.Once);
    }

    [Test]
    public async Task SendForAttestationReport72H_ReturnsNotFound_WhenReportIsNull()
    {
        // Arrange
        long filingId = 1;
        var responsibleOfficerIds = new List<long> { 10, 20 };

        _filingSvcMock
            .Setup(svc => svc.SendForAttestationReport72H(filingId, responsibleOfficerIds))
            .ReturnsAsync((ValidatedReport72H?)null);

        // Act
        var result = await _controller.SendForAttestationReport72H(filingId, responsibleOfficerIds, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task GetResponsibleOfficers_ReturnsExpectedList()
    {
        // Arrange
        long filingId = 1;
        var officers = new List<ReponsibleOfficerDto>
    {
        new() { Id = 1, FirstName = "Alice", LastName = "Smith", Role = "Officer", Title = "Officer" },
        new ReponsibleOfficerDto { Id = 2, FirstName = "Bob", LastName = "Johnson", Role = "Officer", Title = "Officer" }
    };

        _filingSvcMock
            .Setup(svc => svc.GetResponsibleOfficers(filingId))
            .ReturnsAsync(officers);

        // Act
        var result = await _controller.GetResponsibleOfficers(filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.First().FirstName, Is.EqualTo("Alice"));
        });
    }


    [Test]
    public async Task AttestReport72H_ReturnsOkOnSuccess()
    {
        // Arrange
        var response = new ValidateReport72HSubmissionResponseDto(1, true, [], FilingStatus.Accepted.Id);

        _report72hSvcMock.Setup(x => x.AttestReport72H(1, true)).ReturnsAsync(response);

        // Act
        var result = await _controller.AttestReport72H(1, new SubmitLobbyistEmployerReportDto { DiligenceStatementVerified = true }, _report72hSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_ReturnsOk_WhenUpdateSucceeds()
    {
        // Arrange
        var filingId = 123L;
        var request = new UpdateFilingCustomPeriodRequest { FilingPeriodId = 456 };
        var filing = new Filing
        {
            FilerId = 1,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StatusId = FilingStatus.Pending.Id,
        };
        var expectedResponse = new FilingResponseDto(filing);

        _filingSvcMock
            .Setup(svc => svc.UpdateFilingCustomPeriod(filingId, request))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateFilingCustomPeriod(filingId, request, _filingSvcMock.Object);

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        Assert.That(okResult?.Value, Is.EqualTo(expectedResponse));
        _filingSvcMock.Verify(svc => svc.UpdateFilingCustomPeriod(filingId, request), Times.Once);
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_ReturnsBadRequest_WhenArgumentExceptionThrown()
    {
        // Arrange
        var filingId = 123L;
        var request = new UpdateFilingCustomPeriodRequest { FilingPeriodId = 456 };
        var errorMessage = "Invalid period";
        _filingSvcMock
            .Setup(svc => svc.UpdateFilingCustomPeriod(filingId, request))
            .ThrowsAsync(new ArgumentException(errorMessage));

        // Act
        var result = await _controller.UpdateFilingCustomPeriod(filingId, request, _filingSvcMock.Object);

        // Assert
        var badRequest = result.Result as BadRequestObjectResult;
        Assert.That(badRequest, Is.Not.Null);
        Assert.That(badRequest?.Value, Is.InstanceOf<object>());
        _filingSvcMock.Verify(svc => svc.UpdateFilingCustomPeriod(filingId, request), Times.Once);
    }

    [Test]
    public async Task GetFilersAssociatedUser_ReturnsExpectedList_WhenUsersExist()
    {
        // Arrange
        long userId = 10;
        long filingTypeId = 20;
        var expected = new List<FilerUserDto>
        {
            new() { UserId = 10 },
            new() { UserId = 11 }
        };

        _filingSvcMock
            .Setup(svc => svc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId))
            .ReturnsAsync(expected);

        // Act
        var result = await _controller.GetFilersAssociatedUser(userId, filingTypeId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.First().UserId, Is.EqualTo(10));
    }

    [Test]
    public async Task GetFilersAssociatedUser_ReturnsEmptyList_WhenNoUsersExist()
    {
        // Arrange
        long userId = 10;
        long filingTypeId = 20;
        var expected = new List<FilerUserDto>();

        _filingSvcMock
            .Setup(svc => svc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId))
            .ReturnsAsync(expected);

        // Act
        var result = await _controller.GetFilersAssociatedUser(userId, filingTypeId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Empty);
        _filingSvcMock.Verify(svc => svc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId), Times.Once);
    }

    #region UpdateContributionsInExistingStatements Tests

    [Test]
    public async Task UpdateContributionsInExistingStatements_ReturnsOk_WhenReportExists()
    {
        // Arrange
        var model = new UpdateContributionsInExistingStatementsDto
        {
            ContributionsInExistingStatements = true,
            RelatedFilerIds = new List<long> { 1, 2, 3 }
        };
        var mockResponse = new UpdateContributionsInExistingStatementsResponseDto(1, true, new List<WorkFlowError>());

        _filingSvcMock.Setup(x => x.UpdateContributionsInExistingStatements(123, model.ContributionsInExistingStatements, model.RelatedFilerIds))
            .ReturnsAsync(mockResponse);

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(123, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        _filingSvcMock.Verify(x => x.UpdateContributionsInExistingStatements(123, model.ContributionsInExistingStatements, model.RelatedFilerIds), Times.Once);
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ReturnsNotFound_WhenReportIsNull()
    {
        // Arrange
        var model = new UpdateContributionsInExistingStatementsDto
        {
            ContributionsInExistingStatements = false,
            RelatedFilerIds = new List<long>()
        };

        _filingSvcMock.Setup(x => x.UpdateContributionsInExistingStatements(123, model.ContributionsInExistingStatements, model.RelatedFilerIds))
            .ReturnsAsync(null as UpdateContributionsInExistingStatementsResponseDto);

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(123, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_HandlesNullRelatedFilerIds()
    {
        // Arrange
        var model = new UpdateContributionsInExistingStatementsDto
        {
            ContributionsInExistingStatements = false,
            RelatedFilerIds = []
        };
        var mockResponse = new UpdateContributionsInExistingStatementsResponseDto(1, true, new List<WorkFlowError>());


        _filingSvcMock.Setup(x => x.UpdateContributionsInExistingStatements(123, model.ContributionsInExistingStatements, model.RelatedFilerIds))
            .ReturnsAsync(mockResponse);

        // Act
        var result = await _controller.UpdateContributionsInExistingStatements(123, model, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        _filingSvcMock.Verify(x => x.UpdateContributionsInExistingStatements(123, model.ContributionsInExistingStatements, model.RelatedFilerIds), Times.Once);
    }

    #endregion

    #region SearchFilers Tests

    [Test]
    public async Task SearchFilers_ReturnsOk_WithValidQuery()
    {
        // Arrange
        var query = "test filer";
        var filerTypeId = 1L;
        var expectedFilers = new List<FilerSearchDto>
        {
            new() { Id = 1, Name = "Test Filer 1" },
            new() { Id = 2, Name = "Test Filer 2" }
        };

        _filingSvcMock.Setup(x => x.SearchFilers(query, filerTypeId))
            .ReturnsAsync(expectedFilers);

        // Act
        var result = await _controller.SearchFilers(query, filerTypeId, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var filers = okResult?.Value as IEnumerable<FilerSearchDto>;
        Assert.That(filers?.Count(), Is.EqualTo(2));
        _filingSvcMock.Verify(x => x.SearchFilers(query, filerTypeId), Times.Once);
    }

    [Test]
    public async Task SearchFilers_ReturnsOk_WithEmptyResult()
    {
        // Arrange
        var query = "";
        var filerTypeId = 1L;
        var expectedFilers = new List<FilerSearchDto>();

        _filingSvcMock.Setup(x => x.SearchFilers(query, filerTypeId))
            .ReturnsAsync(expectedFilers);

        // Act
        var result = await _controller.SearchFilers(query, filerTypeId, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var filers = okResult?.Value as IEnumerable<FilerSearchDto>;
        Assert.That(filers?.Count(), Is.EqualTo(0));
        _filingSvcMock.Verify(x => x.SearchFilers(query, filerTypeId), Times.Once);
    }

    #endregion

    #region GetRelatedFilersByFilingId Tests

    [Test]
    public async Task GetRelatedFilersByFilingId_ReturnsOk_WithRelatedFilers()
    {
        // Arrange
        var filingId = 123L;
        var expectedRelatedFilers = new List<FilerSearchDto>
        {
            new() { Id = 1, Name = "Test Filer 1" },
            new() { Id = 2, Name = "Test Filer 2" },
            new() { Id = 3, Name = "Test Filer 3" }
        };

        _filingSvcMock.Setup(x => x.GetRelatedFilersByFilingId(filingId))
            .ReturnsAsync(expectedRelatedFilers);

        // Act
        var result = await _controller.GetRelatedFilersByFilingId(filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var relatedFilers = okResult?.Value as IEnumerable<FilerSearchDto>;
        Assert.That(relatedFilers?.Count(), Is.EqualTo(3));
    }

    [Test]
    public async Task GetRelatedFilersByFilingId_ReturnsOk_WithEmptyResult()
    {
        // Arrange
        var filingId = 123L;
        var expectedRelatedFilers = new List<FilerSearchDto>();

        _filingSvcMock.Setup(x => x.GetRelatedFilersByFilingId(filingId))
            .ReturnsAsync(expectedRelatedFilers);

        // Act
        var result = await _controller.GetRelatedFilersByFilingId(filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var relatedFilers = okResult?.Value as IEnumerable<FilerSearchDto>;
        Assert.That(relatedFilers?.Count(), Is.EqualTo(0));
        _filingSvcMock.Verify(x => x.GetRelatedFilersByFilingId(filingId), Times.Once);
    }

    [Test]
    public async Task GetRelatedFilersByFilingId_ReturnsOk_WhenFilingHasNoRelatedFilers()
    {
        // Arrange
        var filingId = 999L;
        var expectedRelatedFilers = new List<FilerSearchDto>();

        _filingSvcMock.Setup(x => x.GetRelatedFilersByFilingId(filingId))
            .ReturnsAsync(expectedRelatedFilers);

        // Act
        var result = await _controller.GetRelatedFilersByFilingId(filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        var relatedFilers = okResult?.Value as IEnumerable<FilerSearchDto>;
        Assert.That(relatedFilers, Is.Not.Null);
        Assert.That(relatedFilers.Count(), Is.EqualTo(0));
        _filingSvcMock.Verify(x => x.GetRelatedFilersByFilingId(filingId), Times.Once);
    }

    #endregion

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_ReturnsNotFound_WhenReportIsNull()
    {
        // Arrange
        long filingId = 1;
        var responsibleOfficerIds = new List<long> { 10, 20 };

        _filingSvcMock
            .Setup(svc => svc.SendForAttestationReportLobbyistEmployer(filingId, responsibleOfficerIds))
            .ReturnsAsync((ValidatedLobbyistEmployerReport?)null);

        // Act
        var result = await _controller.SendForAttestationReportLobbyistEmployer(filingId, responsibleOfficerIds, _filingSvcMock.Object);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task UpdateAmendmentExplanation_ReturnsResponse_WhenServiceReturnsResult()
    {
        // Arrange
        long filingId = 123;
        var request = new UpdateAmendmentExplanationRequest { AmendmentExplanation = "Updated reason" };

        var expectedResponse = new UpdateAmendmentExplanationResponse
        {
            FilingId = filingId,
            Valid = true
        };

        var mockFilingSvc = new Mock<IFilingSvc>();
        mockFilingSvc
            .Setup(svc => svc.UpdateAmendmentExplanation(filingId, request))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateAmendmentExplanation(filingId, request, mockFilingSvc.Object);

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        Assert.That(okResult!.Value, Is.EqualTo(expectedResponse));

        mockFilingSvc.Verify(svc => svc.UpdateAmendmentExplanation(filingId, request), Times.Once);
    }

    // Add to the FilingsControllerTest class

    [Test]
    public async Task GetAllFilingPeriodsForLobbying_ReturnsExpectedCollection_WhenPeriodsExist()
    {
        // Arrange
        var filerId = 1L;
        var filingTypeId = 2L;
        var filingId = 3L;
        var expectedPeriods = new List<FilingPeriodDto>
        {
            new()
            {
                Id = 1,
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 3, 31, 0, 0, 0, DateTimeKind.Utc),
                HasFiling = true
            },
            new()
            {
                Id = 2,
                StartDate = new DateTime(2024, 4, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 6, 30, 0, 0, 0, DateTimeKind.Utc),
                HasFiling = false
            }
        };

        _filingSvcMock
            .Setup(svc => svc.GetAllFilingPeriodsForLobbying(filerId, filingTypeId, filingId))
            .ReturnsAsync(expectedPeriods);

        // Act
        var result =
            await _controller.GetAllFilingPeriodsForLobbying(filerId, filingTypeId, filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.First().Id, Is.EqualTo(1));
            Assert.That(result.Last().HasFiling, Is.False);
        });
        _filingSvcMock.Verify(svc => svc.GetAllFilingPeriodsForLobbying(filerId, filingTypeId, filingId), Times.Once);
    }

    [Test]
    public async Task GetAllFilingPeriodsForLobbying_ReturnsEmptyCollection_WhenNoPeriodsExist()
    {
        // Arrange
        var filerId = 99L;
        var filingTypeId = 100L;
        var filingId = 101L;
        var expectedPeriods = new List<FilingPeriodDto>();

        _filingSvcMock
            .Setup(svc => svc.GetAllFilingPeriodsForLobbying(filerId, filingTypeId, filingId))
            .ReturnsAsync(expectedPeriods);

        // Act
        var result =
            await _controller.GetAllFilingPeriodsForLobbying(filerId, filingTypeId, filingId, _filingSvcMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Empty);
        _filingSvcMock.Verify(svc => svc.GetAllFilingPeriodsForLobbying(filerId, filingTypeId, filingId), Times.Once);
    }
}
