using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Transactions;

namespace SOS.CalAccess.WebApi.Tests.Transactions;

[TestFixture]
public class ActivityExpenseControllerTests
{
    private Mock<IActivityExpenseSvc> _activityExpenseMock;
    private Mock<ITransactionSvc> _transactionMock;
    private Mock<IAuthorizationService> _authorizationMock;
    private Mock<ITransactionReportablePersonSvc> _transactionReportablePersonSvcMock;
    private IDecisionsSvc _decisionsSvc;
    private IGetAllContacts _getAllContacts;
    private ActivityExpenseController _controller;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _getAllContacts = Substitute.For<IGetAllContacts>();
        _activityExpenseMock = new Mock<IActivityExpenseSvc>();
        _transactionMock = new Mock<ITransactionSvc>();
        _authorizationMock = new Mock<IAuthorizationService>();
        _transactionReportablePersonSvcMock = new Mock<ITransactionReportablePersonSvc>();

        _controller = new ActivityExpenseController(
            _authorizationMock.Object,
            _activityExpenseMock.Object,
            _transactionMock.Object
        );
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    [Test]
    public async Task CreateActivityExpense_ShouldReturnCreatedResponse_WhenExpenseIsSuccessfullyCreated_CreditTrue()
    {
        {
            // Arrange
            var testFilerId = 123;
            var testFilingId = 456;
            var testTransactionDate = _dateNow;
            var testActivityDescription = "Campaign Event";
            var testCreditCardCompanyName = "Visa";
            var testAmount = (Currency)10;
            var testPayeeName = "Test Organization";
            var testFilerContactType = FilerContactType.Organization;
            var testBeneficiaryName = "Jane Doe";
            var testAddressList = new AddressList();
            var testAddress = new Address
            {
                Country = "United States",
                Street = "711 Kapiolani Blvd.",
                City = "Honolulu",
                State = "HI",
                Zip = "96826",
                Type = "Business",
                Purpose = "Payee"
            };
            testAddressList.Addresses.Add(testAddress);

            var testContact = new OrganizationContact { Id = 1, OrganizationName = testPayeeName, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

            var testReportablePerson = new TransactionReportablePersonDto
            {
                Name = testBeneficiaryName,
                Amount = testAmount,
            };
            var activityExpenseDTO = new ActivityExpenseDto
            {
                FilerId = testFilerId,
                FilingId = testFilingId,
                ActivityExpenseTypeId = 1,
                ContactId = 1,
                ActivityDescription = testActivityDescription,
                Amount = testAmount,
                TransactionDate = testTransactionDate,
                CreditCardCompanyName = testCreditCardCompanyName,
                IsCreditCard = true,
                TransactionReportablePersons = new List<TransactionReportablePersonDto> { testReportablePerson }
            };

            var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

            _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

            var createdActivityExpense = new ActivityExpense
            {
                Id = 1,
                FilerId = activityExpenseDTO.FilerId,
                ActivityDescription = activityExpenseDTO.ActivityDescription,
                Amount = (Currency)activityExpenseDTO.Amount,
                TransactionDate = activityExpenseDTO.TransactionDate,
                CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
                MonetaryTypeId = MonetaryType.Credit.Id
            };

            var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

            _activityExpenseMock
                .Setup(svc => svc.GetActivityExpenseTypes())
                .ReturnsAsync(expectedTypes);

            var mockContacts = new List<FilerContact> { testContact };
            var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);


#pragma warning disable CA2012
            _getAllContacts
                .Execute(
                    Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                    Arg.Any<CancellationToken>())
                .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012

            _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
                DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
                .Returns(Task.FromResult(new List<WorkFlowError>()));

            _activityExpenseMock.Setup(a => a.CreateActivityExpense(activityExpenseDTO))
                .ReturnsAsync(createdActivityExpense);

            // Act
            var result = await _controller.CreateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<CreatedAtRouteResult>());

            var createdAtRouteResult = result.Result as CreatedAtRouteResult;
            Assert.That(createdAtRouteResult, Is.Not.Null);

            var transactionItemResponse = createdAtRouteResult.Value as TransactionItemResponse;
            Assert.That(transactionItemResponse, Is.Not.Null);

            Assert.Multiple(() =>
            {
                Assert.That(transactionItemResponse.FilerId, Is.EqualTo(createdActivityExpense.FilerId));
                Assert.That(transactionItemResponse.Amount, Is.EqualTo(createdActivityExpense.Amount));
                Assert.That(transactionItemResponse.TransactionDate, Is.EqualTo(createdActivityExpense.TransactionDate)); Assert.That(createdAtRouteResult.RouteName, Is.EqualTo(nameof(TransactionsController.GetTransaction)));
                Assert.That(createdAtRouteResult.RouteValues?["id"], Is.EqualTo(createdActivityExpense.Id));
            });

        }
    }

    [Test]
    public async Task CreateActivityExpense_ShouldReturnCreatedResponse_WhenExpenseIsSuccessfullyCreated_IndividualPayee()
    {
        {
            // Arrange
            var testFilerId = 123;
            var testFilingId = 456;
            var testTransactionDate = _dateNow;
            var testActivityDescription = "Campaign Event";
            var testCreditCardCompanyName = "Visa";
            var testAmount = (Currency)10;
            var testFirstName = "Tony";
            var testMiddleName = "Iron Man";
            var testLastName = "Stark";
            var testEmployer = "CGI";
            var testOccupation = "Superhero";
            var testFilerContactType = FilerContactType.Individual;
            var testBeneficiaryName = "Jane Doe";
            var testAddressList = new AddressList();
            var testAddress = new Address
            {
                Country = "United States",
                Street = "711 Kapiolani Blvd.",
                City = "Honolulu",
                State = "HI",
                Zip = "96826",
                Type = "Business",
                Purpose = "Payee"
            };
            testAddressList.Addresses.Add(testAddress);

            var testContact = new IndividualContact { Id = 1, FirstName = testFirstName, MiddleName = testMiddleName, LastName = testLastName, Employer = testEmployer, Occupation = testOccupation, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

            var testReportablePerson = new TransactionReportablePersonDto
            {
                Name = testBeneficiaryName,
                Amount = testAmount,
            };
            var activityExpenseDTO = new ActivityExpenseDto
            {
                FilerId = testFilerId,
                FilingId = testFilingId,
                ActivityExpenseTypeId = 1,
                ContactId = 1,
                ActivityDescription = testActivityDescription,
                Amount = testAmount,
                TransactionDate = testTransactionDate,
                CreditCardCompanyName = testCreditCardCompanyName,
                IsCreditCard = true,
                TransactionReportablePersons = new List<TransactionReportablePersonDto> { testReportablePerson }
            };

            var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

            _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

            var createdActivityExpense = new ActivityExpense
            {
                Id = 1,
                FilerId = activityExpenseDTO.FilerId,
                ActivityDescription = activityExpenseDTO.ActivityDescription,
                Amount = (Currency)activityExpenseDTO.Amount,
                TransactionDate = activityExpenseDTO.TransactionDate,
                CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
                MonetaryTypeId = MonetaryType.Credit.Id
            };

            var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

            _activityExpenseMock
                .Setup(svc => svc.GetActivityExpenseTypes())
                .ReturnsAsync(expectedTypes);

            var mockContacts = new List<FilerContact> { testContact };
            var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);


#pragma warning disable CA2012
            _getAllContacts
                .Execute(
                    Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                    Arg.Any<CancellationToken>())
                .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012

            _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
                DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
                .Returns(Task.FromResult(new List<WorkFlowError>()));

            _activityExpenseMock.Setup(a => a.CreateActivityExpense(activityExpenseDTO))
                .ReturnsAsync(createdActivityExpense);

            // Act
            var result = await _controller.CreateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<CreatedAtRouteResult>());

            var createdAtRouteResult = result.Result as CreatedAtRouteResult;
            Assert.That(createdAtRouteResult, Is.Not.Null);

            var transactionItemResponse = createdAtRouteResult.Value as TransactionItemResponse;
            Assert.That(transactionItemResponse, Is.Not.Null);

            Assert.Multiple(() =>
            {
                Assert.That(transactionItemResponse.FilerId, Is.EqualTo(createdActivityExpense.FilerId));
                Assert.That(transactionItemResponse.Amount, Is.EqualTo(createdActivityExpense.Amount));
                Assert.That(transactionItemResponse.TransactionDate, Is.EqualTo(createdActivityExpense.TransactionDate)); Assert.That(createdAtRouteResult.RouteName, Is.EqualTo(nameof(TransactionsController.GetTransaction)));
                Assert.That(createdAtRouteResult.RouteValues?["id"], Is.EqualTo(createdActivityExpense.Id));
            });

        }
    }

    [Test]
    public async Task CreateActivityExpense_ShouldReturnCreatedResponse_WhenExpenseIsSuccessfullyCreated_CreditFalse()
    {
        {
            // Arrange
            var testFilerId = 123;
            var testFilingId = 456;
            var testTransactionDate = _dateNow;
            var testActivityDescription = "Campaign Event";
            var testCreditCardCompanyName = "Visa";
            var testAmount = (Currency)10;
            var testPayeeName = "Test Organization";
            var testFilerContactType = FilerContactType.Organization;
            var testBeneficiaryName = "Jane Doe";
            var testAddressList = new AddressList();
            var testAddress = new Address
            {
                Country = "United States",
                Street = "711 Kapiolani Blvd.",
                City = "Honolulu",
                State = "HI",
                Zip = "96826",
                Type = "Business",
                Purpose = "Payee"
            };
            testAddressList.Addresses.Add(testAddress);

            var testContact = new OrganizationContact { Id = 1, OrganizationName = testPayeeName, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

            var testReportablePerson = new TransactionReportablePersonDto
            {
                Name = testBeneficiaryName,
                Amount = testAmount,
            };
            var activityExpenseDTO = new ActivityExpenseDto
            {
                FilerId = testFilerId,
                FilingId = testFilingId,
                ActivityExpenseTypeId = 1,
                ContactId = 1,
                ActivityDescription = testActivityDescription,
                Amount = testAmount,
                TransactionDate = testTransactionDate,
                CreditCardCompanyName = testCreditCardCompanyName,
                IsCreditCard = false,
                TransactionReportablePersons = new List<TransactionReportablePersonDto> { testReportablePerson }
            };

            var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

            _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

            var createdActivityExpense = new ActivityExpense
            {
                Id = 1,
                FilerId = activityExpenseDTO.FilerId,
                ActivityDescription = activityExpenseDTO.ActivityDescription,
                Amount = (Currency)activityExpenseDTO.Amount,
                TransactionDate = activityExpenseDTO.TransactionDate,
                CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
                MonetaryTypeId = null
            };

            var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

            _activityExpenseMock
                .Setup(svc => svc.GetActivityExpenseTypes())
                .ReturnsAsync(expectedTypes);

            var mockContacts = new List<FilerContact> { testContact };
            var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);

#pragma warning disable CA2012
            _getAllContacts
                .Execute(
                    Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                    Arg.Any<CancellationToken>())
                .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012

            _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
            DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

            _activityExpenseMock.Setup(a => a.CreateActivityExpense(activityExpenseDTO))
                .ReturnsAsync(createdActivityExpense);

            // Act
            var result = await _controller.CreateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<CreatedAtRouteResult>());

            var createdAtRouteResult = result.Result as CreatedAtRouteResult;
            Assert.That(createdAtRouteResult, Is.Not.Null);

            var transactionItemResponse = createdAtRouteResult.Value as TransactionItemResponse;
            Assert.That(transactionItemResponse, Is.Not.Null);

            Assert.Multiple(() =>
            {
                Assert.That(transactionItemResponse.FilerId, Is.EqualTo(createdActivityExpense.FilerId));
                Assert.That(transactionItemResponse.Amount, Is.EqualTo(createdActivityExpense.Amount));
                Assert.That(transactionItemResponse.TransactionDate, Is.EqualTo(createdActivityExpense.TransactionDate)); Assert.That(createdAtRouteResult.RouteName, Is.EqualTo(nameof(TransactionsController.GetTransaction)));
                Assert.That(createdAtRouteResult.RouteValues?["id"], Is.EqualTo(createdActivityExpense.Id));
            });

        }
    }

    [Test]
    public async Task CreateActivityExpense_ShouldReturnUnprocessableEntity_WhenTransactionReportablePersonsIsEmpty()
    {
        // Arrange
        var testFilerId = 123;
        var testFilingId = 456;
        var testTransactionDate = _dateNow;
        var testActivityDescription = "Campaign Event";
        var testAmount = (Currency)10;
        var testCreditCardCompanyName = "Visa";
        var testPayeeName = "Test Organization";
        var testFilerContactType = FilerContactType.Organization;
        var testBeneficiaryName = "Jane Doe";
        var testAddressList = new AddressList();
        var testAddress = new Address
        {
            Country = "United States",
            Street = "711 Kapiolani Blvd.",
            City = "Honolulu",
            State = "HI",
            Zip = "96826",
            Type = "Business",
            Purpose = "Payee"
        };
        testAddressList.Addresses.Add(testAddress);

        var activityExpenseDTO = new ActivityExpenseDto
        {
            FilerId = testFilerId,
            FilingId = testFilingId,
            ActivityExpenseTypeId = 1,
            ContactId = 1,
            ActivityDescription = testActivityDescription,
            Amount = testAmount,
            TransactionDate = testTransactionDate,
            CreditCardCompanyName = testCreditCardCompanyName,
            IsCreditCard = false,
            TransactionReportablePersons = new List<TransactionReportablePersonDto>()
        };

        var testContact = new OrganizationContact { Id = 1, OrganizationName = testPayeeName, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

        var testReportablePerson = new TransactionReportablePersonDto
        {
            Name = testBeneficiaryName,
            Amount = testAmount,
        };

        var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

        _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

        var createdActivityExpense = new ActivityExpense
        {
            Id = 1,
            FilerId = activityExpenseDTO.FilerId,
            ActivityDescription = activityExpenseDTO.ActivityDescription,
            Amount = (Currency)activityExpenseDTO.Amount,
            TransactionDate = activityExpenseDTO.TransactionDate,
            CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
            MonetaryTypeId = null
        };

        var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

        _activityExpenseMock
            .Setup(svc => svc.GetActivityExpenseTypes())
            .ReturnsAsync(expectedTypes);

        var mockContacts = new List<FilerContact> { testContact };
        var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);

#pragma warning disable CA2012
        _getAllContacts
            .Execute(
                Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                Arg.Any<CancellationToken>())
            .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012


        var expectedErrors = new List<WorkFlowError>
        {
            new("TransactionReportablePersons", "Manual", "Validation", "One or more {{Field Name}} is required")
        };

        _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
            DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.CreateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Result, Is.InstanceOf<UnprocessableEntityObjectResult>());

        var unprocessableEntityResult = result.Result as UnprocessableEntityObjectResult;
        Assert.That(unprocessableEntityResult, Is.Not.Null);

        var validationErrors = unprocessableEntityResult.Value as List<WorkFlowError>;
        Assert.That(validationErrors, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(validationErrors, Has.Count.EqualTo(1));
            Assert.That(validationErrors[0].FieldName, Is.EqualTo("TransactionReportablePersons"));
            Assert.That(validationErrors[0].Message, Is.EqualTo("One or more {{Field Name}} is required"));
        });
    }

    [Test]
    public async Task UpdateActivityExpense_ShouldReturnCreatedResponse_WhenExpenseIsSuccessfullyCreated_CreditTrue()
    {
        {
            // Arrange
            var testFilerId = 123;
            var testFilingId = 456;
            var testTransactionDate = _dateNow;
            var testActivityDescription = "Campaign Event";
            var testCreditCardCompanyName = "Visa";
            var testAmount = (Currency)10;
            var testPayeeName = "Test Organization";
            var testFilerContactType = FilerContactType.Organization;
            var testBeneficiaryName = "Jane Doe";
            var testAddressList = new AddressList();
            var testAddress = new Address
            {
                Country = "United States",
                Street = "711 Kapiolani Blvd.",
                City = "Honolulu",
                State = "HI",
                Zip = "96826",
                Type = "Business",
                Purpose = "Payee"
            };
            testAddressList.Addresses.Add(testAddress);

            var testContact = new OrganizationContact { Id = 1, OrganizationName = testPayeeName, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

            var testReportablePerson = new TransactionReportablePersonDto
            {
                Name = testBeneficiaryName,
                Amount = testAmount,
            };
            var activityExpenseDTO = new ActivityExpenseDto
            {
                FilerId = testFilerId,
                FilingId = testFilingId,
                ActivityExpenseTypeId = 1,
                ContactId = 1,
                ActivityDescription = testActivityDescription,
                Amount = testAmount,
                TransactionDate = testTransactionDate,
                CreditCardCompanyName = testCreditCardCompanyName,
                IsCreditCard = true,
                TransactionReportablePersons = new List<TransactionReportablePersonDto> { testReportablePerson }
            };

            var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

            _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

            var createdActivityExpense = new ActivityExpense
            {
                Id = 1,
                FilerId = activityExpenseDTO.FilerId,
                ActivityDescription = activityExpenseDTO.ActivityDescription,
                Amount = (Currency)activityExpenseDTO.Amount,
                TransactionDate = activityExpenseDTO.TransactionDate,
                CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
                MonetaryTypeId = MonetaryType.Credit.Id
            };

            var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

            _activityExpenseMock
                .Setup(svc => svc.GetActivityExpenseTypes())
                .ReturnsAsync(expectedTypes);

            var mockContacts = new List<FilerContact> { testContact };
            var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);


#pragma warning disable CA2012
            _getAllContacts
                .Execute(
                    Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                    Arg.Any<CancellationToken>())
                .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012

            _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
                DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
                .Returns(Task.FromResult(new List<WorkFlowError>()));

            _activityExpenseMock.Setup(a => a.UpdateActivityExpense(activityExpenseDTO))
                .ReturnsAsync(createdActivityExpense);

            // Act
            var result = await _controller.UpdateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<CreatedAtRouteResult>());

            var createdAtRouteResult = result.Result as CreatedAtRouteResult;
            Assert.That(createdAtRouteResult, Is.Not.Null);

            var transactionItemResponse = createdAtRouteResult.Value as TransactionItemResponse;
            Assert.That(transactionItemResponse, Is.Not.Null);

            Assert.Multiple(() =>
            {
                Assert.That(transactionItemResponse.FilerId, Is.EqualTo(createdActivityExpense.FilerId));
                Assert.That(transactionItemResponse.Amount, Is.EqualTo(createdActivityExpense.Amount));
                Assert.That(transactionItemResponse.TransactionDate, Is.EqualTo(createdActivityExpense.TransactionDate)); Assert.That(createdAtRouteResult.RouteName, Is.EqualTo(nameof(TransactionsController.GetTransaction)));
                Assert.That(createdAtRouteResult.RouteValues?["id"], Is.EqualTo(createdActivityExpense.Id));
            });

        }
    }

    [Test]
    public async Task UpdateActivityExpense_ShouldReturnCreatedResponse_WhenExpenseIsSuccessfullyCreated_IndividualPayee()
    {
        {
            // Arrange
            var testFilerId = 123;
            var testFilingId = 456;
            var testTransactionDate = _dateNow;
            var testActivityDescription = "Campaign Event";
            var testCreditCardCompanyName = "Visa";
            var testAmount = (Currency)10;
            var testFirstName = "Tony";
            var testMiddleName = "Iron Man";
            var testLastName = "Stark";
            var testEmployer = "CGI";
            var testOccupation = "Superhero";
            var testFilerContactType = FilerContactType.Individual;
            var testBeneficiaryName = "Jane Doe";
            var testAddressList = new AddressList();
            var testAddress = new Address
            {
                Country = "United States",
                Street = "711 Kapiolani Blvd.",
                City = "Honolulu",
                State = "HI",
                Zip = "96826",
                Type = "Business",
                Purpose = "Payee"
            };
            testAddressList.Addresses.Add(testAddress);

            var testContact = new IndividualContact { Id = 1, FirstName = testFirstName, MiddleName = testMiddleName, LastName = testLastName, Employer = testEmployer, Occupation = testOccupation, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

            var testReportablePerson = new TransactionReportablePersonDto
            {
                Name = testBeneficiaryName,
                Amount = testAmount,
            };
            var activityExpenseDTO = new ActivityExpenseDto
            {
                FilerId = testFilerId,
                FilingId = testFilingId,
                ActivityExpenseTypeId = 1,
                ContactId = 1,
                ActivityDescription = testActivityDescription,
                Amount = testAmount,
                TransactionDate = testTransactionDate,
                CreditCardCompanyName = testCreditCardCompanyName,
                IsCreditCard = true,
                TransactionReportablePersons = new List<TransactionReportablePersonDto> { testReportablePerson }
            };

            var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

            _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

            var createdActivityExpense = new ActivityExpense
            {
                Id = 1,
                FilerId = activityExpenseDTO.FilerId,
                ActivityDescription = activityExpenseDTO.ActivityDescription,
                Amount = (Currency)activityExpenseDTO.Amount,
                TransactionDate = activityExpenseDTO.TransactionDate,
                CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
                MonetaryTypeId = MonetaryType.Credit.Id
            };

            var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

            _activityExpenseMock
                .Setup(svc => svc.GetActivityExpenseTypes())
                .ReturnsAsync(expectedTypes);

            var mockContacts = new List<FilerContact> { testContact };
            var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);


#pragma warning disable CA2012
            _getAllContacts
                .Execute(
                    Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                    Arg.Any<CancellationToken>())
                .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012

            _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
                DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
                .Returns(Task.FromResult(new List<WorkFlowError>()));

            _activityExpenseMock.Setup(a => a.UpdateActivityExpense(activityExpenseDTO))
                .ReturnsAsync(createdActivityExpense);

            // Act
            var result = await _controller.UpdateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<CreatedAtRouteResult>());

            var createdAtRouteResult = result.Result as CreatedAtRouteResult;
            Assert.That(createdAtRouteResult, Is.Not.Null);

            var transactionItemResponse = createdAtRouteResult.Value as TransactionItemResponse;
            Assert.That(transactionItemResponse, Is.Not.Null);

            Assert.Multiple(() =>
            {
                Assert.That(transactionItemResponse.FilerId, Is.EqualTo(createdActivityExpense.FilerId));
                Assert.That(transactionItemResponse.Amount, Is.EqualTo(createdActivityExpense.Amount));
                Assert.That(transactionItemResponse.TransactionDate, Is.EqualTo(createdActivityExpense.TransactionDate)); Assert.That(createdAtRouteResult.RouteName, Is.EqualTo(nameof(TransactionsController.GetTransaction)));
                Assert.That(createdAtRouteResult.RouteValues?["id"], Is.EqualTo(createdActivityExpense.Id));
            });

        }
    }

    [Test]
    public async Task UpdateActivityExpense_ShouldReturnCreatedResponse_WhenExpenseIsSuccessfullyCreated_CreditFalse()
    {
        {
            // Arrange
            var testFilerId = 123;
            var testFilingId = 456;
            var testTransactionDate = _dateNow;
            var testActivityDescription = "Campaign Event";
            var testCreditCardCompanyName = "Visa";
            var testAmount = (Currency)10;
            var testPayeeName = "Test Organization";
            var testFilerContactType = FilerContactType.Organization;
            var testBeneficiaryName = "Jane Doe";
            var testAddressList = new AddressList();
            var testAddress = new Address
            {
                Country = "United States",
                Street = "711 Kapiolani Blvd.",
                City = "Honolulu",
                State = "HI",
                Zip = "96826",
                Type = "Business",
                Purpose = "Payee"
            };
            testAddressList.Addresses.Add(testAddress);

            var testContact = new OrganizationContact { Id = 1, OrganizationName = testPayeeName, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

            var testReportablePerson = new TransactionReportablePersonDto
            {
                Name = testBeneficiaryName,
                Amount = testAmount,
            };
            var activityExpenseDTO = new ActivityExpenseDto
            {
                FilerId = testFilerId,
                FilingId = testFilingId,
                ActivityExpenseTypeId = 1,
                ContactId = 1,
                ActivityDescription = testActivityDescription,
                Amount = testAmount,
                TransactionDate = testTransactionDate,
                CreditCardCompanyName = testCreditCardCompanyName,
                IsCreditCard = false,
                TransactionReportablePersons = new List<TransactionReportablePersonDto> { testReportablePerson }
            };

            var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

            _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

            var createdActivityExpense = new ActivityExpense
            {
                Id = 1,
                FilerId = activityExpenseDTO.FilerId,
                ActivityDescription = activityExpenseDTO.ActivityDescription,
                Amount = (Currency)activityExpenseDTO.Amount,
                TransactionDate = activityExpenseDTO.TransactionDate,
                CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
                MonetaryTypeId = null
            };

            var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

            _activityExpenseMock
                .Setup(svc => svc.GetActivityExpenseTypes())
                .ReturnsAsync(expectedTypes);

            var mockContacts = new List<FilerContact> { testContact };
            var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);

#pragma warning disable CA2012
            _getAllContacts
                .Execute(
                    Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                    Arg.Any<CancellationToken>())
                .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012

            _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
            DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

            _activityExpenseMock.Setup(a => a.UpdateActivityExpense(activityExpenseDTO))
                .ReturnsAsync(createdActivityExpense);

            // Act
            var result = await _controller.UpdateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<CreatedAtRouteResult>());

            var createdAtRouteResult = result.Result as CreatedAtRouteResult;
            Assert.That(createdAtRouteResult, Is.Not.Null);

            var transactionItemResponse = createdAtRouteResult.Value as TransactionItemResponse;
            Assert.That(transactionItemResponse, Is.Not.Null);

            Assert.Multiple(() =>
            {
                Assert.That(transactionItemResponse.FilerId, Is.EqualTo(createdActivityExpense.FilerId));
                Assert.That(transactionItemResponse.Amount, Is.EqualTo(createdActivityExpense.Amount));
                Assert.That(transactionItemResponse.TransactionDate, Is.EqualTo(createdActivityExpense.TransactionDate)); Assert.That(createdAtRouteResult.RouteName, Is.EqualTo(nameof(TransactionsController.GetTransaction)));
                Assert.That(createdAtRouteResult.RouteValues?["id"], Is.EqualTo(createdActivityExpense.Id));
            });

        }
    }

    [Test]
    public async Task UpdateActivityExpense_ShouldReturnUnprocessableEntity_WhenTransactionReportablePersonsIsEmpty()
    {
        // Arrange
        var testFilerId = 123;
        var testFilingId = 456;
        var testTransactionDate = _dateNow;
        var testActivityDescription = "Campaign Event";
        var testAmount = (Currency)10;
        var testCreditCardCompanyName = "Visa";
        var testPayeeName = "Test Organization";
        var testFilerContactType = FilerContactType.Organization;
        var testBeneficiaryName = "Jane Doe";
        var testAddressList = new AddressList();
        var testAddress = new Address
        {
            Country = "United States",
            Street = "711 Kapiolani Blvd.",
            City = "Honolulu",
            State = "HI",
            Zip = "96826",
            Type = "Business",
            Purpose = "Payee"
        };
        testAddressList.Addresses.Add(testAddress);

        var activityExpenseDTO = new ActivityExpenseDto
        {
            FilerId = testFilerId,
            FilingId = testFilingId,
            ActivityExpenseTypeId = 1,
            ContactId = 1,
            ActivityDescription = testActivityDescription,
            Amount = testAmount,
            TransactionDate = testTransactionDate,
            CreditCardCompanyName = testCreditCardCompanyName,
            IsCreditCard = false,
            TransactionReportablePersons = new List<TransactionReportablePersonDto>()
        };

        var testContact = new OrganizationContact { Id = 1, OrganizationName = testPayeeName, FilerContactType = FilerContactType.Organization, AddressList = testAddressList } as FilerContact;

        var testReportablePerson = new TransactionReportablePersonDto
        {
            Name = testBeneficiaryName,
            Amount = testAmount,
        };

        var mockMonetaryTypes = new List<MonetaryType>
            {
                MonetaryType.Cash, MonetaryType.Credit
            };

        _transactionMock.Setup(t => t.GetAllMonetaryTypes()).ReturnsAsync(mockMonetaryTypes);

        var createdActivityExpense = new ActivityExpense
        {
            Id = 1,
            FilerId = activityExpenseDTO.FilerId,
            ActivityDescription = activityExpenseDTO.ActivityDescription,
            Amount = (Currency)activityExpenseDTO.Amount,
            TransactionDate = activityExpenseDTO.TransactionDate,
            CreditCardCompanyName = activityExpenseDTO.CreditCardCompanyName,
            MonetaryTypeId = null
        };

        var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

        _activityExpenseMock
            .Setup(svc => svc.GetActivityExpenseTypes())
            .ReturnsAsync(expectedTypes);

        var mockContacts = new List<FilerContact> { testContact };
        var successResult = new Success<IReadOnlyList<FilerContact>>(mockContacts);

#pragma warning disable CA2012
        _getAllContacts
            .Execute(
                Arg.Is<IGetAllContacts.ByFilerId>(q => q.FilerId == testFilerId),
                Arg.Any<CancellationToken>())
            .Returns(new ValueTask<IResult<IReadOnlyList<FilerContact>>>(successResult));
#pragma warning restore CA2012


        var expectedErrors = new List<WorkFlowError>
        {
            new("TransactionReportablePersons", "Manual", "Validation", "One or more {{Field Name}} is required")
        };

        _decisionsSvc.InitiateWorkflow<ActivityExpenseDs, List<WorkFlowError>>(
            DecisionsWorkflow.ActivityExpenseTransactionRuleset, Arg.Any<ActivityExpenseDs>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.UpdateActivityExpense(testFilerId, testFilingId, _decisionsSvc, _getAllContacts, activityExpenseDTO, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Result, Is.InstanceOf<UnprocessableEntityObjectResult>());

        var unprocessableEntityResult = result.Result as UnprocessableEntityObjectResult;
        Assert.That(unprocessableEntityResult, Is.Not.Null);

        var validationErrors = unprocessableEntityResult.Value as List<WorkFlowError>;
        Assert.That(validationErrors, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(validationErrors, Has.Count.EqualTo(1));
            Assert.That(validationErrors[0].FieldName, Is.EqualTo("TransactionReportablePersons"));
            Assert.That(validationErrors[0].Message, Is.EqualTo("One or more {{Field Name}} is required"));
        });
    }

    [Test]
    public async Task ValidateReportablePersons_ReturnsOk_WhenNoValidationErrors()
    {
        // Arrange
        long filerId = 123;
        var request = new ValidateReportablePersonDs[]
        {
            new ()
            {
                Name = "Mark Scout",
                OfficialPosition = "MDR",
                OfficialPositionDescription = "Chief of Staff",
                Agency = "Lumon",
                Amount = (Currency)10
            }
        };
        _decisionsSvc.InitiateWorkflow<ValidateReportablePersonDs[], List<WorkFlowError>>(
            DecisionsWorkflow.ActivityExpenseReportablePersonRuleset, request, true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.ValidateReportablePersons(filerId, request, _decisionsSvc, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<OkResult>());
            _decisionsSvc.Received(1).InitiateWorkflow<ValidateReportablePersonDs[], List<WorkFlowError>>(
                DecisionsWorkflow.ActivityExpenseReportablePersonRuleset, request, true);
        });
    }

    [Test]
    public async Task ValidateReportablePersons_ReturnsUnprocessableEntity_WhenValidationFails()
    {
        // Arrange
        long filerId = 123;
        var request = new ValidateReportablePersonDs[]
        {
            new ()
            {
                Name = "Mark Scout",
                OfficialPosition = "MDR",
                OfficialPositionDescription = "Chief of Staff",
                Agency = "Lumon",
                Amount = (Currency)10
            }
        };
        var errors = new List<WorkFlowError>
        {
            new("Name", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            new("OfficialPosition", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            new("Agency", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            new("Amount", "ErrGlobal0001", "Validation", "{{Field Name}} is required")
        };
        _decisionsSvc.InitiateWorkflow<ValidateReportablePersonDs[], List<WorkFlowError>>(
            DecisionsWorkflow.ActivityExpenseReportablePersonRuleset, request, true)
            .Returns(Task.FromResult(errors));

        // Act
        var result = await _controller.ValidateReportablePersons(filerId, request, _decisionsSvc, CancellationToken.None) as ObjectResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<UnprocessableEntityObjectResult>());
            Assert.That(result?.StatusCode, Is.EqualTo(422));
            Assert.That(result?.Value, Is.EqualTo(errors)); // Ensure returned errors match expected
            _decisionsSvc.Received(1).InitiateWorkflow<ValidateReportablePersonDs[], List<WorkFlowError>>(
                DecisionsWorkflow.ActivityExpenseReportablePersonRuleset, request, true);
        });
    }

    [Test]
    public async Task GetActivityExpenseTypes_ReturnsOk_WithList()
    {
        // Arrange
        var expectedTypes = new List<ActivityExpenseType>
            {
                new() { Id = 1, Name = "Travel", CreatedBy = 0, ModifiedBy = 0},
                new() { Id = 2, Name = "Meals", CreatedBy = 0, ModifiedBy = 0 }
            };

        _activityExpenseMock
            .Setup(svc => svc.GetActivityExpenseTypes())
            .ReturnsAsync(expectedTypes);

        // Act
        var result = await _controller.GetActivityExpenseTypes();

        // Assert
        var actionResult = result.Result as OkObjectResult;
        Assert.That(actionResult, Is.Not.Null);
        var actualTypes = actionResult.Value as List<ActivityExpenseTypeResponse>;
        Assert.That(actualTypes, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(actionResult.StatusCode, Is.Not.Null);
            Assert.That(actionResult.StatusCode, Is.EqualTo(200));
            Assert.That(actualTypes, Has.Count.EqualTo(expectedTypes.Count));
        });
    }

    [Test]
    public async Task GetActivityExpenseTypes_WhenServiceThrowsException_ReturnsBadRequest()
    {
        // Arrange
        _activityExpenseMock
            .Setup(svc => svc.GetActivityExpenseTypes())
            .ThrowsAsync(new InvalidOperationException("Something went wrong"));

        // Act
        var result = await _controller.GetActivityExpenseTypes();

        // Assert
        var actionResult = result.Result as BadRequestObjectResult;
        Assert.That(actionResult, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(actionResult.StatusCode, Is.EqualTo(400));
            Assert.That(actionResult.Value, Is.EqualTo("Something went wrong"));
        });
    }

    [Test]
    public async Task GetAllActivityExpenseTransactionsForFiling_ShouldReturnOkWithMappedResponses()
    {
        var filingId = 1001L;

        var testContact = new IndividualContact
        {
            Id = 1,
            Employer = null,
            Occupation = null,
            FirstName = "Tom",
            MiddleName = null,
            LastName = "Cruise"
        };


        var mockExpenses = new List<ActivityExpense>
        {
            new ()
            {
                Id = 1,
                ActivityDescription = "Flyers",
                Amount = (Currency)100,
                Contact = testContact,
                TransactionReportablePersons = new List<TransactionReportablePerson>
                {
                    new () { Name = "John Doe", Amount = (Currency)50},
                    new () { Name = "Jane Smith", Amount = (Currency)50 }
                }
            },
            new ()
            {
                Id = 2,
                Contact = testContact,
                ActivityDescription = "Ads",
                Amount = (Currency)200,
                TransactionReportablePersons = null
            }
        };

        _transactionMock
            .Setup(svc => svc.GetAllActivityExpenseTransactionsForFiling(filingId))
            .ReturnsAsync(mockExpenses);

        // Act
        var result = await _controller.GetAllActivityExpenseTransactionsForFiling(filingId, _transactionMock.Object);

        // Assert
        var okResult = result.Result as OkObjectResult;
        Assert.That(okResult, Is.Not.Null);
        Assert.That(okResult.StatusCode, Is.EqualTo(200));

        var response = okResult.Value as ActivityExpenseSummaryResponse;
        Assert.That(response, Is.Not.Null);
        var resActivityExpenseItems = response.ActivityExpenseItems;
        var resSubtotal = response.Subtotal;
        Assert.That(response.ActivityExpenseItems, Has.Count.EqualTo(2));

        var first = resActivityExpenseItems[0];
        Assert.That(first, Is.Not.Null);
        var second = resActivityExpenseItems[1];
        Assert.That(second, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(first.PersonBenefiting, Is.EqualTo("John Doe"));
            Assert.That(first.PayeeName, Is.EqualTo("Tom Cruise"));
            Assert.That(first.PayeeType, Is.EqualTo("Individual"));
            Assert.That(first.AmountBenefiting, Is.EqualTo((Currency)50));
            Assert.That(second.PersonBenefiting, Is.EqualTo("Jane Smith"));
            Assert.That(second.PayeeName, Is.EqualTo("Tom Cruise"));
            Assert.That(second.PayeeType, Is.EqualTo("Individual"));
            Assert.That(second.AmountBenefiting, Is.EqualTo((Currency)50));
            Assert.That(resSubtotal, Is.EqualTo((Currency)300));
        });
    }

    [Test]
    public async Task SearchReportablePersonsByName_ReturnsDistinctResults_WhenMatchesExist()
    {
        // Arrange
        var filerId = 123;
        var contactId = 21;
        var searchTerm = "John";
        var cancellationToken = new CancellationToken();

        var mockResults = new List<TransactionReportablePerson>
            {
                new ()
                {
                    Id = 1,
                    Name = "John Doe",
                    Agency = "State Dept",
                    OfficialPosition = "Director",
                    Transaction = new ActivityExpense { FilerId = filerId, Amount = (Currency)20 }
                },
                new ()
                {
                    Id = 2,
                    Name = "John Doe",
                    Agency = "State Dept",
                    OfficialPosition = "Director",
                    Transaction = new ActivityExpense { FilerId = filerId, Amount = (Currency)20 }
                },
                new ()
                {
                    Id = 3,
                    Name = "John Smith",
                    Agency = "Treasury",
                    OfficialPosition = "Analyst",
                    Transaction = new ActivityExpense { FilerId = filerId, Amount = (Currency)20 }
                }
            };

        _transactionReportablePersonSvcMock
            .Setup(x => x.SearchReportablePersonsByName(filerId, contactId, searchTerm))
            .ReturnsAsync(mockResults);

        // Act
        var result = await _controller.SearchReportablePersonsByName(
            filerId,
            contactId,
            searchTerm,
            _transactionReportablePersonSvcMock.Object,
            cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<List<TransactionReportablePerson>>());
        Assert.That(result, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(result.Any(p => p.Name == "John Doe"), Is.True);
            Assert.That(result.Any(p => p.Name == "John Smith"), Is.True);
        });
    }

    [Test]
    public async Task SearchReportablePersonsByName_ReturnsEmptyList_WhenNoMatches()
    {
        // Arrange
        var filerId = 123;
        var contactId = 21;
        var searchTerm = "Nonexistent";
        var cancellationToken = new CancellationToken();

        _transactionReportablePersonSvcMock
            .Setup(x => x.SearchReportablePersonsByName(filerId, contactId, searchTerm))
            .ReturnsAsync(new List<TransactionReportablePerson>());

        // Act
        var result = await _controller.SearchReportablePersonsByName(
            filerId,
            contactId,
            searchTerm,
            _transactionReportablePersonSvcMock.Object,
            cancellationToken);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task SearchReportablePersonsByName_IsCaseInsensitive()
    {
        // Arrange
        var filerId = 123;
        var contactId = 21;
        var searchTerm = "john";
        var cancellationToken = new CancellationToken();

        var mockResults = new List<TransactionReportablePerson>
            {
                new ()
                {
                    Id = 1,
                    Name = "John Doe",
                    FilerContactId = contactId,
                    Transaction = new ActivityExpense { FilerId = filerId, Amount =(Currency) 20 }
                }
            };

        _transactionReportablePersonSvcMock
            .Setup(x => x.SearchReportablePersonsByName(filerId, contactId, searchTerm))
            .ReturnsAsync(mockResults);

        // Act
        var result = await _controller.SearchReportablePersonsByName(
            filerId,
            contactId,
            searchTerm,
            _transactionReportablePersonSvcMock.Object,
            cancellationToken);

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        Assert.That(result[0].Name, Is.EqualTo("John Doe"));
    }

    [Test]
    public async Task GetActivityExpenseForUpdate_ReturnsOkWithDto_WhenExpenseExists()
    {
        // Arrange
        var testId = 1L;
        var expectedDto = new ActivityExpenseDto
        {
            Id = testId,
            FilerId = 123,
            FilingId = 456,
            Notes = "Test Expense",
            TransactionReportablePersons = new List<TransactionReportablePersonDto>()
        };

        _activityExpenseMock.Setup(x => x.GetActivityExpenseForUpdate(testId))
                          .ReturnsAsync(expectedDto);

        // Act
        var actionResult = await _controller.GetActivityExpenseForUpdate(testId);

        // Assert
        Assert.That(actionResult, Is.InstanceOf<ActionResult<ActivityExpenseDto>>());

        // Get the actual result
        var result = actionResult.Result as OkObjectResult;

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.StatusCode, Is.EqualTo(StatusCodes.Status200OK));
            Assert.That(result.Value, Is.EqualTo(expectedDto));
        });

        _activityExpenseMock.Verify(x => x.GetActivityExpenseForUpdate(testId), Times.Once);
    }

    [Test]
    public async Task GetActivityExpenseForUpdate_ReturnsNotFound_WhenExpenseDoesNotExist()
    {
        // Arrange
        var testId = 999L;
        _activityExpenseMock.Setup(x => x.GetActivityExpenseForUpdate(testId))
                          .ReturnsAsync((ActivityExpenseDto?)null);

        // Act
        var actionResult = await _controller.GetActivityExpenseForUpdate(testId);

        // Assert
        Assert.That(actionResult, Is.InstanceOf<ActionResult<ActivityExpenseDto>>());
        Assert.That(actionResult.Result, Is.InstanceOf<NotFoundResult>());

        _activityExpenseMock.Verify(x => x.GetActivityExpenseForUpdate(testId), Times.Once);
    }
}
