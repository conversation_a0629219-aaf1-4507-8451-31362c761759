// <copyright file="ApproveFilingTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit.Attribution;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Messaging;
using SOS.CalAccess.Data.EntityFramework.Tests.SeedData;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filings;

/// <summary>
/// Tests for the <see cref="ApproveFiling"/> command handler.
/// </summary>
[TestFixture]
[TestOf(typeof(ApproveFiling))]
[Parallelizable(ParallelScope.All)]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public sealed class ApproveFilingTests
{
    private readonly ISendNotification _sendNotification = Substitute.For<ISendNotification>();
    private readonly ISendCorrespondence _sendCorrespondence = Substitute.For<ISendCorrespondence>();
    private readonly IActionAttributionProvider _attributionProvider = Substitute.For<IActionAttributionProvider>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();
    /// <summary>
    /// Asserts that our implementation of the <see cref="ApproveFiling" /> command
    /// returns a <see cref="Failure{TResult}.NotFound"/> instance when the referenced
    /// target is not present in the database.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task Execute_ReturnsNotFoundFailure_WhenTheFilingDoesNotExist()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var sut = GetSut(context, _dateTimeSvc);

        var result = await sut.Execute(new IApproveFiling.WithId(default));

        Assert.That(result, Is.InstanceOf<Failure<Filing>.NotFound>());
    }

    /// <summary>
    /// Asserts that our implementation of the <see cref="ApproveFiling" /> command
    /// returns a <see cref="Failure{TResult}.InvalidState"/> instance when the referenced
    /// target is not in a submitted state.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task Execute_ReturnsInvalidStateFailure_WhenTheFilingIsNotSubmitted()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var sut = GetSut(context, _dateTimeSvc);

        var result = await sut.Execute(new IApproveFiling.WithId(data.DraftFiling.Id));

        Assert.That(result, Is.InstanceOf<Failure<Filing>.InvalidState>());
    }

    /// <summary>
    /// Asserts that our implementation of the <see cref="ApproveFiling" /> command
    /// correctly approves the filing when the referenced target is in a pending state.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task Execute_CorrectlyApprovesTheFiling()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var sut = GetSut(context, _dateTimeSvc);

        var success = await sut.Execute(new IApproveFiling.WithId(data.PendingFiling.Id)) as Success<Filing>;

        Assert.That(success, Is.Not.Null);
        Assert.That(success!.Value.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
    }

    /// <summary>
    /// Asserts that our implementation of the <see cref="ApproveFiling" /> command
    /// sets the <see cref="Filing.ApprovedAt"/> field when the filing is approved.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task Execute_SetsTheApprovedAtField()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilingData();

        var fixedNow = new DateTime(2025, 01, 01, 12, 00, 00, DateTimeKind.Local);
        var dateTimeSvc = Substitute.For<IDateTimeSvc>();
        dateTimeSvc.GetCurrentDateTime().Returns(fixedNow);

        var command = GetSut(context, dateTimeSvc);

        var success = await command.Execute(new IApproveFiling.WithId(data.PendingFiling.Id)) as Success<Filing>;

        Assert.That(success, Is.Not.Null);
    }

    /// <summary>
    /// Asserts that our implementation of the <see cref="ApproveFiling" /> command
    /// calls the <see cref="ISendNotification"/> and <see cref="ISendCorrespondence"/>
    /// command processors when the filing is approved.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task Execute_CallsHandleExternalCommunications()
    {
        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var dateTimeSvc = Substitute.For<IDateTimeSvc>();
        var data = await context.PrepareFilingData();

        var sut = GetSut(context, dateTimeSvc);

        await sut.Execute(new IApproveFiling.WithId(data.PendingFiling.Id));

        await _sendNotification.Received(1).Execute(Arg.Any<ISendNotification.Command>());
        await _sendCorrespondence.Received(1).Execute(Arg.Any<ISendCorrespondence.Command>());
    }

    private ApproveFiling GetSut(DatabaseContext db, IDateTimeSvc dateTimeSvc) =>
        new(db, new MockMessagingHub(_attributionProvider, _sendCorrespondence, _sendNotification), dateTimeSvc);
}
