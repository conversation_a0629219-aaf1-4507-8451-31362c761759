// <copyright file="IJwtTokenGenerator.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Security.Claims;
using System.Text;

using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Tokens;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Services.WebApi.Authentication;

/// <summary>
/// Interface for generating JWT tokens.
/// </summary>
public interface IJwtTokenGenerator
{
    /// <summary>
    /// Generates a JWT token for the specified user ID.
    /// </summary>
    /// <param name="options">Options for configuring the token generation, including the user id.</param>
    /// <returns>A JWT token as a string.</returns>
    string GenerateToken(TokenOptions options);

    /// <summary>
    /// Options record for configuring the generated
    /// JWT token and security descriptor.
    /// </summary>
    /// <param name="UserId">The actor id on behalf of whom the token is emitted.</param>
    /// <param name="Email">Includes the user email (if available) in the claims.</param>
    /// <param name="IsAdmin">Optional value to attach the admin role to the token. Defaults to false.</param>
    public sealed record TokenOptions(long UserId, string Username, bool IsAdmin = false, string? Email = default);
}

/// <summary>
/// Implementation of the IJwtTokenGenerator interface.
/// </summary>
public sealed class JwtTokenGenerator(IOptions<JwtOptions> jwtOptions, IDateTimeSvc dateTimeSvc) : IJwtTokenGenerator
{
    /// <summary>
    /// The environment variable key for the JWT secret.
    /// </summary>
    public const string Key = "JWT_KEY";

    private readonly JwtOptions _jwtOptions = jwtOptions.Value;

    /// <inheritdoc/>
    public string GenerateToken(IJwtTokenGenerator.TokenOptions options)
    {
        var secret = _jwtOptions.Secret ??
                     throw new InvalidOperationException("JWT key is not configured.");

        var signingCredentials = new SigningCredentials(
            new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secret)),
            SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>();
        claims.Add(new(ClaimTypes.NameIdentifier, options.UserId.ToString()));
        claims.Add(new(ClaimTypes.Name, options.Username));
        claims.Add(new(ClaimTypes.Email, options.Email ?? ""));
        claims.Add(new(ClaimTypes.Role, "DevUser"));
        claims.Add(new(ClaimTypes.Role, "FilerPortalUsers"));
        if (options.IsAdmin)
        {
            claims.Add(new(ClaimTypes.Role, "Admin"));
        }

        var subject = new ClaimsIdentity(claims);

        var descriptor = new SecurityTokenDescriptor
        {
            Audience = _jwtOptions.Audience,
            Expires = DateTime.UtcNow.AddHours(_jwtOptions.Ttl),
            Issuer = _jwtOptions.Issuer,
            SigningCredentials = signingCredentials,
            Subject = subject,
        };

        return new JsonWebTokenHandler().CreateToken(descriptor);
    }
}
