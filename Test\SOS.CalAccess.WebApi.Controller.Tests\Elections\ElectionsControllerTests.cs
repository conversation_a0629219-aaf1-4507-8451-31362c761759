using NSubstitute;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections;
using SOS.CalAccess.Services.Business.FilerRegistration.Elections.Models;
using SOS.CalAccess.Services.WebApi.Elections;

namespace SOS.CalAccess.WebApi.Tests.Elections;

[TestFixture]
public class ElectionsControllerTests
{
    private IElectionSvc _electionsSvc;
    private ElectionsController _controller;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _electionsSvc = Substitute.For<IElectionSvc>();
        _controller = new ElectionsController(_electionsSvc);
    }

    [Test]
    public async Task GetAllElectionCycles()
    {
        //Act
        _ = await _controller.GetAllElectionCycles();

        //Assert
        await _electionsSvc.Received().GetAllElectionCycles();
    }

    [Test]
    public async Task GetAllElections()
    {
        //Act
        _ = await _controller.GetAllElections();

        //Assert
        await _electionsSvc.Received().GetAllElections();
    }

    [Test]
    public async Task GetAllElectionTypes()
    {
        //Act
        _ = await _controller.GetAllElectionTypes();

        //Assert
        await _electionsSvc.Received().GetAllElectionTypes();
    }

    [Test]
    public async Task GetDistrictsForElectionAndOffice()
    {
        //Act
        _ = await _controller.GetDistrictsForElectionAndOffice(224, 1982);

        //Assert
        await _electionsSvc.Received().GetDistrictsForElectionAndOffice(224, 1982);
    }

    [Test]
    public async Task GetElection()
    {
        //Act
        _ = await _controller.GetElection(1982);

        //Assert
        await _electionsSvc.Received().GetElection(1982);
    }

    [Test]
    public async Task GetElectionRace()
    {
        //Act
        _ = await _controller.GetElectionRace(1982);

        //Assert
        await _electionsSvc.Received().GetElectionRace(1982);
    }

    [Test]
    public async Task GetElectionYears()
    {
        //Act
        _ = await _controller.GetElectionYears();

        //Assert
        await _electionsSvc.Received().GetElectionYears();
    }

    [Test]
    public async Task GetOfficesForElection()
    {
        //Act
        _ = await _controller.GetOfficesForElection(1982);

        //Assert
        await _electionsSvc.Received().GetOfficesForElection(1982);
    }

    [Test]
    public async Task CreateElection()
    {
        //Arrange
        var election = new Election() { ElectionDate = _dateNow, Name = "name" };
        _electionsSvc.CreateElection(election).Returns(election);

        //Act
        var result = await _controller.CreateElection(election);

        //Assert
        await _electionsSvc.Received().CreateElection(election);
        Assert.That(result, Is.EqualTo(election));
    }

    [Test]
    public async Task CreateElectionRace()
    {
        //Arrange
        var electionRace = new ElectionRace() { };
        _electionsSvc.CreateElectionRace(electionRace).Returns(electionRace);

        //Act
        var result = await _controller.CreateElectionRace(electionRace);

        //Assert
        await _electionsSvc.Received().CreateElectionRace(electionRace);
        Assert.That(result, Is.EqualTo(electionRace));
    }
    [Test]
    public async Task UpdateElection()
    {
        // Arrange
        var id = 1982;
        var updateRequest = new UpdateElectionRequest
        {
            ElectionDate = _dateNow,
            ElectionTypeId = 1,
            ElectionCycleId = 1,
            Name = "Updated Election"
        };
        var updatedElection = new Election
        {
            Id = id,
            ElectionDate = updateRequest.ElectionDate,
            ElectionTypeId = updateRequest.ElectionTypeId,
            ElectionCycleId = updateRequest.ElectionCycleId,
            Name = updateRequest.Name
        };
        _electionsSvc.UpdateElection(id, updateRequest).Returns(updatedElection);

        // Act
        var result = await _controller.UpdateElection(id, updateRequest);

        // Assert
        await _electionsSvc.Received().UpdateElection(id, updateRequest);
        Assert.That(result, Is.EqualTo(updatedElection));
    }

}
