using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Lobbyists;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

/// <summary>
/// Static utility class containing mapping operations from and to
/// registration-related models.
/// </summary>
public class RegistrationModelMapper : IRegistrationModelMapper
{
    private readonly IReferenceDataSvc _referenceDataSvc;

    public RegistrationModelMapper(IReferenceDataSvc referenceDataSvc)
    {
        _referenceDataSvc = referenceDataSvc;
    }

    /// <summary>
    /// Maps a request to create a new registration into a new
    /// registration instance.
    /// </summary>
    /// <param name="request">The CreateCandidateIntentionStatementRequest to map.</param>
    /// <returns>The new registration.</returns>
    public CandidateIntentionStatement MapCandidateIntentionStatementRequestToModel(CandidateIntentionStatementRequest request)
    {
        PhoneNumberList phoneNumberList = new();
        UpdatePhoneNumber(phoneNumberList, request.Telephone, RegistrationConstants.PhoneNumber.TypeHome);
        UpdatePhoneNumber(phoneNumberList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);

        return new()
        {
            Name = string.Join(" ", new[] { request.FirstName, request.MiddleName, request.LastName }
                              .Where(s => !string.IsNullOrWhiteSpace(s))),
            PreviousCandidate = request.PreviousCandidate.GetValueOrDefault(false),
            SelfRegister = request.SelfRegister.GetValueOrDefault(false),
            FirstName = request.FirstName,
            LastName = request.LastName,
            MiddleName = request.MiddleName == string.Empty ? null : request.MiddleName,
            Email = request.Email,
            AddressList = GetAddressList(request),
            PhoneNumberList = phoneNumberList,
            StatusId = RegistrationStatus.Draft.Id,
            IsSameAsCandidateAddress = request.IsSameAsCandidateAddress.GetValueOrDefault(false),
            CandidateId = request.CandidateId,
            OriginalId = request.OriginalId,
            ParentId = request.ParentId,
            PoliticalPartyId = request.PoliticalPartyId,
            ExpenditureLimitAccepted = request.ExpenditureLimitAccepted.GetValueOrDefault(false),
            ContributedPersonalExcessFundsOn = request.ContributedPersonalExcessFundsOn,
            ElectionRaceId = request.ElectionRaceId,
            Version = request.Version ?? 0
        };
    }

    /// <summary>
    /// Maps a request to update an existing registration.
    /// </summary>
    /// <param name="request">The CreateCandidateIntentionStatementRequest to map.</param>
    /// <param name="existingCandidateIntentionStatement">exisitng registration that is in database.</param>
    /// <returns></returns>
    public CandidateIntentionStatement UpdateCandidateIntentionStatement(CandidateIntentionStatement existingCandidateIntentionStatement, CandidateIntentionStatementRequest request)
    {
        if (request.CandidateAddress != null)
        {
            var candidateAddress = existingCandidateIntentionStatement.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeCandidate);
            if (candidateAddress != null)
            {

                candidateAddress.City = request.CandidateAddress.City;
                candidateAddress.Country = request.CandidateAddress.Country;
                candidateAddress.Street2 = request.CandidateAddress.Street2;
                candidateAddress.Street = request.CandidateAddress.Street;
                candidateAddress.State = request.CandidateAddress.State;
                candidateAddress.Zip = request.CandidateAddress.Zip ?? string.Empty;
                candidateAddress.Type = request.CandidateAddress.Type;
            }
            else
            {
                // CandidateAddress creation.
                var addressList = new AddressList
                {
                    Addresses = new List<Address>()
                };
                PopulateAddress(request.CandidateAddress, addressList);
            }
        }

        if (request.CandidateMailingAddress != null)
        {
            var candidateMailingAddress = existingCandidateIntentionStatement.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeMailing);

            if (candidateMailingAddress != null)
            {

                candidateMailingAddress.City = request.CandidateMailingAddress.City;
                candidateMailingAddress.Country = request.CandidateMailingAddress.Country;
                candidateMailingAddress.Street2 = request.CandidateMailingAddress.Street2;
                candidateMailingAddress.Street = request.CandidateMailingAddress.Street;
                candidateMailingAddress.State = request.CandidateMailingAddress.State;
                candidateMailingAddress.Zip = request.CandidateMailingAddress.Zip ?? string.Empty;
                candidateMailingAddress.Type = request.CandidateMailingAddress.Type;
            }
            else
            {
                // CandidateAddress creation.
                var addressList = new AddressList
                {
                    Addresses = new List<Address>()
                };
                PopulateAddress(request.CandidateMailingAddress, addressList, RegistrationConstants.Address.PurposeMailing);
            }
        }

        //create phonenumber list if does not exist
        existingCandidateIntentionStatement.PhoneNumberList ??= new PhoneNumberList();
        UpdatePhoneNumber(existingCandidateIntentionStatement.PhoneNumberList, request.Telephone, RegistrationConstants.PhoneNumber.TypeHome);
        UpdatePhoneNumber(existingCandidateIntentionStatement.PhoneNumberList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);

        existingCandidateIntentionStatement.Name = string.Join(" ", new[]
            { request.FirstName, request.MiddleName, request.LastName }
            .Where(s => !string.IsNullOrWhiteSpace(s)));

        existingCandidateIntentionStatement.PreviousCandidate = request.PreviousCandidate.GetValueOrDefault(false);
        existingCandidateIntentionStatement.SelfRegister = request.SelfRegister.GetValueOrDefault(false);
        existingCandidateIntentionStatement.FirstName = request.FirstName;
        existingCandidateIntentionStatement.LastName = request.LastName;
        existingCandidateIntentionStatement.MiddleName = string.IsNullOrWhiteSpace(request.MiddleName) ? null : request.MiddleName;
        existingCandidateIntentionStatement.Email = request.Email;
        existingCandidateIntentionStatement.StatusId = RegistrationStatus.Draft.Id;
        existingCandidateIntentionStatement.IsSameAsCandidateAddress = request.IsSameAsCandidateAddress.GetValueOrDefault(false);
        existingCandidateIntentionStatement.OriginalId = request.OriginalId;

        return existingCandidateIntentionStatement;
    }

    /// <summary>
    /// Maps a request to create a new slateMailerOrganization registration into a new
    /// registration instance.
    /// </summary>
    /// <param name="request">The SmoContactRequest to map.</param>
    /// <returns>The new smo registration.</returns>
    public SlateMailerOrganization MapSmoContactRequestToModel(SmoContactRequest request)
    {
        PhoneNumberList phoneNumberList = new();
        UpdatePhoneNumber(phoneNumberList, request.PhoneNumber, RegistrationConstants.PhoneNumber.TypeHome);
        UpdatePhoneNumber(phoneNumberList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);

        return new()
        {
            Name = request.Name ?? string.Empty,
            Email = request.Email ?? string.Empty,
            AddressList = GetAddressList(request.OrganizationAddress, request.MailingAddress),
            PhoneNumberList = phoneNumberList,
            StatusId = RegistrationStatus.Draft.Id,
            IsSameAsCandidateAddress = request.IsSameAsOrganizationAddress.GetValueOrDefault(false),
            County = request.County ?? string.Empty,
        };
    }

    /// <summary>
    /// Maps a request to create a new slateMailerOrganization registration into a new
    /// registration instance.
    /// </summary>
    /// <param name="request">The SmoContactRequest to map.</param>
    /// <returns>The new smo registration.</returns>
    public SlateMailerOrganization UpdateSlateMailerOrganization(SlateMailerOrganization existing, SmoContactRequest request)
    {
        existing.Name = request.Name ?? string.Empty;
        existing.Email = request.Email ?? string.Empty;
        existing.County = request.County ?? string.Empty;
        existing.IsSameAsCandidateAddress = request.IsSameAsOrganizationAddress ?? false;
        existing.StatusId = RegistrationStatus.Draft.Id;

        // handle organization address
        if (request.OrganizationAddress is not null)
        {
            existing.AddressList ??= new AddressList
            {
                Addresses = new List<Address>()
            };

            var existingOrgAddress = existing.AddressList.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeOrganization);

            if (existingOrgAddress is not null)
            {
                ApplyAddressUpdates(request.OrganizationAddress, existingOrgAddress);
            }
            else
            {
                PopulateAddress(request.OrganizationAddress, existing.AddressList, RegistrationConstants.Address.PurposeOrganization);
            }
        }

        // handle mailing address
        if (request.MailingAddress is not null)
        {
            existing.AddressList ??= new AddressList
            {
                Addresses = new List<Address>()
            };

            var existingOrgAddress = existing.AddressList.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeMailing);

            if (existingOrgAddress is not null)
            {
                ApplyAddressUpdates(request.MailingAddress, existingOrgAddress);
            }
            else
            {
                PopulateAddress(request.MailingAddress, existing.AddressList, RegistrationConstants.Address.PurposeMailing);
            }
        }

        // handle phone number
        if (request.PhoneNumber is not null)
        {
            existing.PhoneNumberList ??= new PhoneNumberList();
            UpdatePhoneNumber(existing.PhoneNumberList, request.PhoneNumber, RegistrationConstants.PhoneNumber.TypeHome);
        }

        // handle fax number
        if (request.FaxNumber is not null)
        {
            existing.PhoneNumberList ??= new PhoneNumberList();
            UpdatePhoneNumber(existing.PhoneNumberList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);
        }

        return existing;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="referenceDataSvc"></param>
    /// <param name="existingPhoneNumberList"></param>
    /// <param name="requestPhoneNumber"></param>
    /// <param name="type"></param>
    private void UpdatePhoneNumber(PhoneNumberList existingPhoneNumberList, PhoneNumberDto? requestPhoneNumber, string type)
    {
        var existingPhoneNumber = existingPhoneNumberList.PhoneNumbers.FirstOrDefault(x => x.Type == type);

        // Remove number
        if (requestPhoneNumber != null)
        {
            // Add/update number
            if (existingPhoneNumber != null)
            {
                requestPhoneNumber.Type = type;
                requestPhoneNumber.UpdateModel(_referenceDataSvc, existingPhoneNumber);
            }
            else
            {
                existingPhoneNumberList.PhoneNumbers ??= new List<PhoneNumber>();

                var phoneNumber = requestPhoneNumber.CreateModel(_referenceDataSvc);
                phoneNumber.Type = type;
                existingPhoneNumberList.PhoneNumbers.Add(phoneNumber);
            }
        }
        else
        {
            if (existingPhoneNumber != null)
            {
                // Remove existing home number if request.Telephone is null
                existingPhoneNumberList.PhoneNumbers?.Remove(existingPhoneNumber);
            }

        }
    }

    /// Maps a request to create a new lobbyist registration into a new
    /// registration instance.
    /// </summary>
    /// <param name="request">The LobbyistRegistrationRequestDto request to map.</param>
    /// <returns>The new lobbyist registration.</returns>
    public Lobbyist MapLobbyistRegistrationRequestToModel(LobbyistRegistrationRequestDto request)
    {
        //create phonenumber list if does not exist
        var phoneList = new PhoneNumberList();
        UpdatePhoneNumber(phoneList, request.PhoneNumber, RegistrationConstants.PhoneNumber.TypeHome);
        UpdatePhoneNumber(phoneList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);

        return new()
        {
            // Initalizers
            StatusId = RegistrationStatus.Draft.Id,
            EmployerName = string.Empty,

            // General Information - Filer Contact
            SelfRegister = request.SelfRegister,
            EffectiveDateOfChanges = request.EffectiveDateOfChanges,
            Name = string.Join(" ", new[]
            { request.FirstName, request.MiddleName, request.LastName }
            .Where(s => !string.IsNullOrWhiteSpace(s))),
            FirstName = request.FirstName,
            MiddleName = request.MiddleName,
            LastName = request.LastName,
            Email = request.Email,
            AddressList = GetAddressList(request),
            PhoneNumberList = phoneList,

            // General Information - Misc
            LegislativeSessionId = request.LegislativeSessionId,
            DateQualified = request.DateOfQualification,
            PlacementAgent = request.IsPlacementAgent,

            // General Information - Ethics Orientation Course
            EthicsCourseCompletedWithinPastYear = request.CompletedEthicsCourseWithinPastYear,
            EthicsCourseCompleted = request.CompletedEthicsCourse,
            EthicsCourseCompletionDate = request.CompletedCourseDate,
            IsNewCertification = request.IsNewCertification,
            IsSameAsCandidateAddress = request.IsSameAsCandidateAddress,

            // Agencies Lobbied
            LobbyOnlySpecifiedAgencies = request.LobbyOnlySpecifiedAgencies,
            RegistrationAgencies = GetRegistrationAgencies(request.Agencies),
            StateLegislatureLobbying = request.IsLobbyingStateLegislature
        };
    }

    /// <summary>
    /// Maps a request to update an existing lobbyist registration.
    /// </summary>
    /// <param name="request">The LobbyistRegistrationRequestDto to map.</param>
    /// <param name="existingLobbyistRegisration">exisitng registration that is in database.</param>
    /// <returns></returns>
    public Lobbyist UpdateLobbyist(LobbyistRegistrationRequestDto request, Lobbyist existingLobbyistRegisration)
    {
        if (request.BusinessAddress != null)
        {
            var businessAddress = existingLobbyistRegisration.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeBusiness);
            if (businessAddress != null)
            {

                businessAddress.City = request.BusinessAddress.City ?? string.Empty;
                businessAddress.Country = request.BusinessAddress.Country ?? string.Empty;
                businessAddress.Street2 = request.BusinessAddress.Street2;
                businessAddress.Street = request.BusinessAddress.Street ?? string.Empty;
                businessAddress.State = request.BusinessAddress.State ?? string.Empty;
                businessAddress.Zip = request.BusinessAddress.Zip ?? string.Empty;
                businessAddress.Type = request.BusinessAddress.Type ?? string.Empty;
            }
            else
            {
                var addressList = new AddressList
                {
                    Addresses = new List<Address>()
                };
                PopulateAddress(request.BusinessAddress, addressList, RegistrationConstants.Address.PurposeBusiness);
            }
        }

        if (request.MailingAddress != null)
        {
            var mailingAddress = existingLobbyistRegisration.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeMailing);

            if (mailingAddress != null)
            {

                mailingAddress.City = request.MailingAddress.City ?? string.Empty;
                mailingAddress.Country = request.MailingAddress.Country ?? string.Empty;
                mailingAddress.Street2 = request.MailingAddress.Street2 ?? string.Empty;
                mailingAddress.Street = request.MailingAddress.Street ?? string.Empty;
                mailingAddress.State = request.MailingAddress.State ?? string.Empty;
                mailingAddress.Zip = request.MailingAddress.Zip ?? string.Empty;
                mailingAddress.Type = request.MailingAddress.Type ?? string.Empty;
            }
            else
            {
                var addressList = new AddressList
                {
                    Addresses = new List<Address>()
                };
                PopulateAddress(request.MailingAddress, addressList, RegistrationConstants.Address.PurposeMailing);
            }
        }
        existingLobbyistRegisration.IsSameAsCandidateAddress = request.IsSameAsCandidateAddress;

        //create phonenumber list if does not exist
        existingLobbyistRegisration.PhoneNumberList ??= new PhoneNumberList();
        UpdatePhoneNumber(existingLobbyistRegisration.PhoneNumberList, request.PhoneNumber, RegistrationConstants.PhoneNumber.TypeHome);
        UpdatePhoneNumber(existingLobbyistRegisration.PhoneNumberList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);

        existingLobbyistRegisration.SelfRegister = request.SelfRegister.GetValueOrDefault(false);

        // General Information - Filer Contact
        existingLobbyistRegisration.Name = string.Join(" ", new[]
            { request.FirstName, request.MiddleName, request.LastName }
            .Where(s => !string.IsNullOrWhiteSpace(s)));
        existingLobbyistRegisration.FirstName = request.FirstName;
        existingLobbyistRegisration.LastName = request.LastName;
        existingLobbyistRegisration.MiddleName = string.IsNullOrWhiteSpace(request.MiddleName) ? null : request.MiddleName;
        existingLobbyistRegisration.Email = request.Email;
        existingLobbyistRegisration.StatusId = RegistrationStatus.Draft.Id;

        // General Information - Misc
        existingLobbyistRegisration.LegislativeSessionId = request.LegislativeSessionId;
        existingLobbyistRegisration.DateQualified = request.DateOfQualification;
        existingLobbyistRegisration.PlacementAgent = request.IsPlacementAgent;

        // General Information - Ethics Orientation Course
        existingLobbyistRegisration.EthicsCourseCompletedWithinPastYear = request.CompletedEthicsCourseWithinPastYear;
        existingLobbyistRegisration.EthicsCourseCompleted = request.CompletedEthicsCourse;
        existingLobbyistRegisration.EthicsCourseCompletionDate = request.CompletedCourseDate;
        existingLobbyistRegisration.IsNewCertification = request.IsNewCertification;

        // Agencies Lobbied
        existingLobbyistRegisration.LobbyOnlySpecifiedAgencies = request.LobbyOnlySpecifiedAgencies;
        existingLobbyistRegisration.RegistrationAgencies = GetRegistrationAgencies(request.Agencies);
        existingLobbyistRegisration.StateLegislatureLobbying = request.IsLobbyingStateLegislature;

        // For amend
        existingLobbyistRegisration.EffectiveDateOfChanges = request.EffectiveDateOfChanges;

        return existingLobbyistRegisration;
    }

    public LobbyistEmployer MapLobbyistEmployerGeneralInfoToModel(LobbyistEmployerGeneralInfoRequest request)
    {
        PhoneNumberList phoneNumberList = new();
        UpdatePhoneNumber(phoneNumberList, request.PhoneNumber, RegistrationConstants.PhoneNumber.TypeHome);
        UpdatePhoneNumber(phoneNumberList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);

        return new()
        {
            Name = request.EmployerName ?? string.Empty,
            EmployerName = request.EmployerName ?? string.Empty,
            Email = request.Email ?? string.Empty,
            AddressList = GetAddressList(request),
            PhoneNumberList = phoneNumberList,
            StatusId = RegistrationStatus.Draft.Id,
            IsLobbyingCoalition = request.IsLobbyingCoalition.GetValueOrDefault(false),
            LegislativeSessionId = request.LegislativeSessionId,
            DateQualified = request.QualificationDate,
        };
    }

    public LobbyistEmployer UpdateLobbyistEmployerGeneralInfo(LobbyistEmployer existing, LobbyistEmployerGeneralInfoRequest request)
    {
        existing.EmployerName = request.EmployerName ?? string.Empty;
        existing.Name = request.EmployerName ?? string.Empty;
        existing.Email = request.Email;
        existing.IsLobbyingCoalition = request.IsLobbyingCoalition ?? false;
        existing.LegislativeSessionId = request.LegislativeSessionId;
        existing.DateQualified = request.QualificationDate;
        existing.StatusId = RegistrationStatus.Draft.Id;

        if (request.BusinessAddress is not null)
        {
            existing.AddressList ??= new AddressList
            {
                Addresses = new List<Address>()
            };

            var existingBusinessAddress = existing.AddressList.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeBusiness);

            if (existingBusinessAddress is not null)
            {
                ApplyAddressUpdates(request.BusinessAddress, existingBusinessAddress);
            }
            else
            {
                PopulateAddress(request.BusinessAddress, existing.AddressList, RegistrationConstants.Address.PurposeBusiness);
            }
        }

        if (request.MailingAddress is not null)
        {
            existing.AddressList ??= new AddressList
            {
                Addresses = new List<Address>()
            };

            var existingMailingAddress = existing.AddressList.Addresses.FirstOrDefault(x => x.Purpose == RegistrationConstants.Address.PurposeMailing);

            if (existingMailingAddress is not null)
            {
                ApplyAddressUpdates(request.MailingAddress, existingMailingAddress);
            }
            else
            {
                PopulateAddress(request.MailingAddress, existing.AddressList, RegistrationConstants.Address.PurposeMailing);
            }
        }

        if (request.PhoneNumber is not null)
        {
            existing.PhoneNumberList ??= new PhoneNumberList();
            UpdatePhoneNumber(existing.PhoneNumberList, request.PhoneNumber, RegistrationConstants.PhoneNumber.TypeHome);
        }

        if (request.FaxNumber is not null)
        {
            existing.PhoneNumberList ??= new PhoneNumberList();
            UpdatePhoneNumber(existing.PhoneNumberList, request.FaxNumber, RegistrationConstants.PhoneNumber.TypeFax);
        }

        return existing;
    }

    public LobbyistEmployer UpdateLobbyistEmployerStateAgencies(LobbyistEmployer existing, LobbyistEmployerStateAgenciesRequest request)
    {
        existing.StateLegislatureLobbying = request.IsLobbyingStateLegislature;
        existing.RegistrationAgencies = GetRegistrationAgencies(request.Agencies);

        return existing;
    }

    public LobbyistEmployer UpdateLobbyistEmployerLobbyingInterests(LobbyistEmployer existing, LobbyistEmployerLobbyingInterestsRequest request)
    {
        existing.LobbyingInterest = new LobbyingInterest()
        {
            Name = request.FilingInterestsDescription ?? string.Empty,
            CreatedBy = 0,
            ModifiedBy = 0
        };

        return existing;
    }


    private static List<RegistrationAgency> GetRegistrationAgencies(List<RegistrationAgencyDto>? registrationAgencies)
    {
        if (registrationAgencies == null)
        {
            return new();
        }
        return registrationAgencies
            .Where(dto => dto.AgencyId.HasValue)
            .Select(dto => new RegistrationAgency()
            {
                RegistrationId = dto.RegistrationId!.Value,
                AgencyId = dto.AgencyId!.Value,
                CreatedBy = 0,
                ModifiedBy = 0
            }).ToList();
    }

    private static void ApplyAddressUpdates(AddressDto source, Address target)
    {
        target.Country = source.Country ?? string.Empty;
        target.Street = source.Street ?? string.Empty;
        target.Street2 = source.Street2 ?? string.Empty;
        target.City = source.City ?? string.Empty;
        target.State = source.State ?? string.Empty;
        target.Zip = source.Zip ?? string.Empty;
        target.Type = source.Type ?? string.Empty;
    }

    private static AddressList GetAddressList(CandidateIntentionStatementRequest request)
    {
        var addressList = new AddressList
        {
            Addresses = new List<Address>()
        };

        PopulateAddress(request.CandidateAddress, addressList);
        PopulateAddress(request.CandidateMailingAddress, addressList, RegistrationConstants.Address.PurposeMailing);

        return addressList;
    }

    private static AddressList GetAddressList(IHasBusinessMailingAddresses request)
    {
        var addressList = new AddressList
        {
            Addresses = new List<Address>()
        };

        PopulateAddress(request.BusinessAddress, addressList, RegistrationConstants.Address.PurposeBusiness);
        PopulateAddress(request.MailingAddress, addressList, RegistrationConstants.Address.PurposeMailing);

        return addressList;
    }

    private static AddressList GetAddressList(AddressDto? organizationAddress, AddressDto? mailingAddress)
    {
        var addressList = new AddressList
        {
            Addresses = new List<Address>()
        };

        PopulateAddress(organizationAddress, addressList, RegistrationConstants.Address.PurposeOrganization);
        PopulateAddress(mailingAddress, addressList, RegistrationConstants.Address.PurposeMailing);

        return addressList;
    }


    private static void PopulateAddress(AddressDto? addressPayload, AddressList addressList, string purpose = RegistrationConstants.Address.PurposeCandidate)
    {
        if (addressPayload != null)
        {
            addressList.Addresses.Add(new Address
            {
                Type = addressPayload?.Type ?? string.Empty,
                Street = addressPayload?.Street ?? string.Empty,
                Street2 = addressPayload?.Street2 ?? string.Empty,
                City = addressPayload?.City ?? string.Empty,
                State = addressPayload?.State ?? string.Empty,
                Country = addressPayload?.Country ?? string.Empty,
                Zip = addressPayload?.Zip ?? string.Empty,
                Purpose = purpose,
            });
        }
    }
}
