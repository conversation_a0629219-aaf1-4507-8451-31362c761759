using SOS.CalAccess.Data.SmsMessaging;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.SmsMessaging;
using SOS.CalAccess.Services.Common.Queuing;
using SOS.CalAccess.Services.Common.SmsMessaging.Model;

namespace SOS.CalAccess.Services.Common.SmsMessaging;

/// <summary>
/// SMS messaging service implementation
/// </summary>
/// <param name="queueConnectionString"></param>
/// <param name="queueName"></param>
public class SmsMessagingSvc(IMessageQueueSvc messageQueueSvc, ISmsMessageRepository smsMessageRepository, IDateTimeSvc dateTimeSvc, SmsMessagingOptions smsMessagingOptions) : ISmsMessagingSvc
{

    /// <summary>
    /// Queue SMS message to send it to the recipients 
    /// </summary>
    /// <param name="recipients"></param>
    /// <param name="filerId"></param>
    /// <param name="message"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task SendSmsMessage(SmsMessageRequest request)
    {
        if (string.IsNullOrEmpty(request.Message))
        {
            throw new ArgumentException("Message content cannot be blank");
        }

        foreach (var recipient in request.Recipients)
        {
            if (string.IsNullOrEmpty(recipient.PhoneNumber))
            {
                throw new ArgumentException("Recipient phone number is required");
            }
        }

        foreach (var recipient in request.Recipients)
        {
            // Build SMS message object
            SmsMessage smsMessage = new()
            {
                ToPhoneNumber = recipient.PhoneNumber,
                Content = request.Message,
                FromPhoneNumber = smsMessagingOptions.From,
                DateUpdated = dateTimeSvc.GetCurrentDateTime(),
                Status = SmsMessageStatusEnum.InternallyCreated.ToString(),
                UserId = recipient.UserId
            };

            if (request.FilerId != 0)
            {
                smsMessage.FilerId = request.FilerId;
            }
            if (recipient.NotificationId != 0)
            {
                smsMessage.NotificationMessageId = recipient.NotificationId;
            }

            // Create SMS entry into SmsMessages table
            smsMessage = await smsMessageRepository.Create(smsMessage);

            // Send SMS message to Azure Queue
            await messageQueueSvc.SendJsonMessage(QueueName.SmsRequest, smsMessage);

            // Update status to Queued
            if (smsMessage != null)
            {
                smsMessage.Status = SmsMessageStatusEnum.InternallyQueued.ToString();
            }

            // Update status in the SmsMessages Table
            smsMessage = await smsMessageRepository.Update(smsMessage);
        }
    }

    /// <summary>
    /// Updates the SMS Message record in the database
    /// </summary>
    /// <param name="providerId">
    /// Unique Id from twilio to locate the SMS Message record in the database.
    /// </param>
    /// <param name="status">
    /// Updated Status of the SMS Message recod
    /// </param>
    /// <param name="errorCode">
    /// Error Code if the SMS message has not been sent
    /// </param>
    /// <param name="errorMessage">
    /// Error Message if the SMS message has not been sent 
    /// </param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task UpdateSmsStatus(string providerId, string status, string errorCode, string errorMessage)
    {
        throw new NotImplementedException();
    }
    /// <summary>
    /// Updates the SMS Message record in the database
    /// </summary>
    /// <param name="updateSmsRequest">
    /// Request object containing updated SMS message details, such as MessagingServiceSId,
    /// AccountSid, SId, Status, ErrorMessage, ErrorCode, DateSent and DateUpdate timestamps
    /// </param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    /// <exception cref="KeyNotFoundException"></exception>
    public async Task UpdateSmsMessageStatus(UpdateSmsRequest updateSmsRequest)
    {
        SmsMessage smsMessage;
        try
        {
            smsMessage = await smsMessageRepository.FindById(updateSmsRequest.Id);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Could not update the Sms Message Status.", ex);
        }

        if (smsMessage == null)
        {
            throw new KeyNotFoundException($"No Sms message found for ID: {updateSmsRequest.Id}");
        }

        try
        {
            if (updateSmsRequest.DateUpdated == null || updateSmsRequest.SId == null)
            {
                await smsMessageRepository.UpdateProperty(smsMessage, s => s.ErrorMessage, updateSmsRequest.ErrorMessage);
            }
            else
            {
                smsMessage.MessagingServiceSId = updateSmsRequest.MessagingServiceSId;
                smsMessage.AccountSId = updateSmsRequest.AccountSid;
                smsMessage.SId = updateSmsRequest.SId;
                smsMessage.Status = updateSmsRequest.Status;
                smsMessage.ErrorCode = updateSmsRequest.ErrorCode;
                smsMessage.ErrorMessage = updateSmsRequest.ErrorMessage;
                smsMessage.DateSent = updateSmsRequest.DateSent;
                smsMessage.DateUpdated = updateSmsRequest.DateUpdated;

                await smsMessageRepository.Update(smsMessage);
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Could not update the Sms Message record.", ex);
        }
    }
}
