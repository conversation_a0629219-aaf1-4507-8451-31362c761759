using System.Text.Json;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ReceivedExtensions;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Data.UserAccountMaintenance;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.Email;
using SOS.CalAccess.Services.Common.Email.Model;
using SOS.CalAccess.Services.Common.Notification;
using SOS.CalAccess.Services.Common.SmsMessaging;
using SOS.CalAccess.Services.Common.SmsMessaging.Model;

namespace SOS.CalAccess.Services.Business.Tests.Notification;
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(NotificationSvc))]
public class NotificationSvcTests
{
    private NotificationSvc _notificationSvc;
    private IEmailSvc _emailSvc;
    private ISmsMessagingSvc _smsSvc;
    private INotificationMessageRepository _notificationMessageRepository;
    private IUserNotificationPreferenceRepository _notificationPreferenceRepository;
    private INotificationTemplateRepository _notificationTemplateRepository;
    private IFilerUserRepository _filerUserRepository;
    private ILogger<NotificationSvc> _logger;
    private IDateTimeSvc _dateTimeSvc;
    private DateTime _dateNow;

    /// <summary>
    /// Setting up objects
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _emailSvc = Substitute.For<IEmailSvc>();
        _smsSvc = Substitute.For<ISmsMessagingSvc>();
        _notificationMessageRepository = Substitute.For<INotificationMessageRepository>();
        _notificationPreferenceRepository = Substitute.For<IUserNotificationPreferenceRepository>();
        _notificationTemplateRepository = Substitute.For<INotificationTemplateRepository>();
        _filerUserRepository = Substitute.For<IFilerUserRepository>();
        _logger = Substitute.For<ILogger<NotificationSvc>>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _notificationSvc = new NotificationSvc(_emailSvc,
                                                _smsSvc,
                                                _dateTimeSvc,
                                                _notificationMessageRepository,
                                                _notificationPreferenceRepository,
                                                _notificationTemplateRepository,
                                                _filerUserRepository,
                                                _logger);
    }

    [Test]
    public async Task SendUserNotification_Standard_SmsAndEmail()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.Received().SendSmsMessage(Arg.Is<SmsMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Message == "Received by SMS"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].PhoneNumber == "5555551234"
            && m.Recipients[0].NotificationId == 24
        ));

        await _emailSvc.Received().SendPlainEmail(Arg.Is<EmailMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Subject == "Notification Subject"
            && m.Message == "Notifcation Body"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].EmailAddress == "<EMAIL>"
            && m.Recipients[0].NotificationId == 24));
    }

    [Test]
    public async Task SendUserNotification_Standard_NoSmsAndNoEmail()
    {
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        preferences.ReceivePriorityNotificationsByEmail = true;
        preferences.ReceivePriorityNotificationsBySms = true;
        preferences.ReceiveStandardNotificationsByEmail = false;
        preferences.ReceiveStandardNotificationsBySms = false;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }

    [Test]
    public async Task SendUserNotification_Standard_EmailNoSms()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        preferences.ReceivePriorityNotificationsByEmail = false;
        preferences.ReceivePriorityNotificationsBySms = true;
        preferences.ReceiveStandardNotificationsByEmail = true;
        preferences.ReceiveStandardNotificationsBySms = false;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.Received().SendPlainEmail(Arg.Is<EmailMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Subject == "Notification Subject"
            && m.Message == "Notifcation Body"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].EmailAddress == "<EMAIL>"
            && m.Recipients[0].NotificationId == 24));
    }


    [Test]
    public async Task SendUserNotification_Standard_SmsAndNoEmail()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        preferences.ReceivePriorityNotificationsByEmail = true;
        preferences.ReceivePriorityNotificationsBySms = false;
        preferences.ReceiveStandardNotificationsByEmail = false;
        preferences.ReceiveStandardNotificationsBySms = true;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.Received().SendSmsMessage(Arg.Is<SmsMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Message == "Received by SMS"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].PhoneNumber == "5555551234"
            && m.Recipients[0].NotificationId == 24
        ));

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }

    [Test]
    public async Task SendUserNotification_Priority_SmsAndEmail()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        template.IsPriorityMessage = true;

        preferences.ReceivePriorityNotificationsByEmail = true;
        preferences.ReceivePriorityNotificationsBySms = true;
        preferences.ReceiveStandardNotificationsByEmail = false;
        preferences.ReceiveStandardNotificationsBySms = false;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.Received().SendSmsMessage(Arg.Is<SmsMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Message == "Received by SMS"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].PhoneNumber == "5555551234"
            && m.Recipients[0].NotificationId == 24
        ));

        await _emailSvc.Received().SendPlainEmail(Arg.Is<EmailMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Subject == "Notification Subject"
            && m.Message == "Notifcation Body"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].EmailAddress == "<EMAIL>"
            && m.Recipients[0].NotificationId == 24));
    }

    [Test]
    public async Task SendUserNotification_Priority_NoSmsAndNoEmail()
    {
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        template.IsPriorityMessage = true;

        preferences.ReceivePriorityNotificationsByEmail = false;
        preferences.ReceivePriorityNotificationsBySms = false;
        preferences.ReceiveStandardNotificationsByEmail = true;
        preferences.ReceiveStandardNotificationsBySms = true;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }

    [Test]
    public async Task SendUserNotification_Priority_EmailNoSms()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        template.IsPriorityMessage = true;

        preferences.ReceivePriorityNotificationsByEmail = true;
        preferences.ReceivePriorityNotificationsBySms = false;
        preferences.ReceiveStandardNotificationsByEmail = false;
        preferences.ReceiveStandardNotificationsBySms = true;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.Received().SendPlainEmail(Arg.Is<EmailMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Subject == "Notification Subject"
            && m.Message == "Notifcation Body"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].EmailAddress == "<EMAIL>"
            && m.Recipients[0].NotificationId == 24));
    }


    [Test]
    public async Task SendUserNotification_Priority_SmsAndNoEmail()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        template.IsPriorityMessage = true;

        preferences.ReceivePriorityNotificationsByEmail = false;
        preferences.ReceivePriorityNotificationsBySms = true;
        preferences.ReceiveStandardNotificationsByEmail = true;
        preferences.ReceiveStandardNotificationsBySms = false;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.Received().SendSmsMessage(Arg.Is<SmsMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Message == "Received by SMS"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].PhoneNumber == "5555551234"
            && m.Recipients[0].NotificationId == 24
        ));

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }


    [Test]
    public async Task SendUserNotification_MissingSmsNumber_MissingEmailAddress()
    {
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        preferences.ReceivePriorityNotificationsByEmail = true;
        preferences.ReceivePriorityNotificationsBySms = true;
        preferences.ReceiveStandardNotificationsByEmail = true;
        preferences.ReceiveStandardNotificationsBySms = true;

        preferences.SmsPhoneNumber = null;
        preferences.EmailAddress = null;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }

    [Test]
    public async Task SendUserNotification_MissingFilerSpecificPreferences()
    {
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        request = new SendUserNotificationRequest(2, 1982, 24, _dateNow, null);

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns((UserNotificationPreference?)null);

        //Act
        try
        {
            await _notificationSvc.SendUserNotification(request);
            Assert.Fail("Expected exception not thrown");
        }
        catch (KeyNotFoundException)
        {
            //do nothing exception expected
        }

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }

    [Test]
    public async Task SendUserNotification_MissingPreferences()
    {
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns((UserNotificationPreference?)null);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserIdAndFilerId(request.UserId, Arg.Any<long>()).Returns((UserNotificationPreference?)null);

        //Act
        try
        {
            await _notificationSvc.SendUserNotification(request);
            Assert.Fail("Expected exception not thrown");
        }
        catch (KeyNotFoundException)
        {
            //do nothing exception expected
        }

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }
    [Test]
    public async Task SendUserNotification_MissingNotificationTemplate()
    {
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns((NotificationTemplate?)null);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        try
        {
            await _notificationSvc.SendUserNotification(request);
            Assert.Fail("Expected exception not thrown");
        }
        catch (KeyNotFoundException)
        {
            //do nothing exception expected
        }

        //Assert
        await _notificationMessageRepository.DidNotReceive().Create(Arg.Any<NotificationMessage>());

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.DidNotReceive().SendPlainEmail(Arg.Any<EmailMessageRequest>());
    }

    [Test]
    public async Task SendUserNotification_WithMessageVariables_NoFilerSpecificPreferences()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);

        var data = new Dictionary<string, string>();
        data.Add("count", "3");
        data.Add("greeting", "Hello");
        data.Add("name", "Notification User");

        request = new SendUserNotificationRequest(2, 1982, 24, _dateNow, data);

        var templateMessage = "This message has {{count}} variable replacements.  {{greeting}} {{name}} ";
        var expectedOutput = "This message has 3 variable replacements.  Hello Notification User ";
        template.EnglishTranslation().Subject = templateMessage;
        template.EnglishTranslation().Message = templateMessage;
        template.EnglishTranslation().SmsMessage = templateMessage;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserIdAndFilerId(request.UserId, request.FilerId!.Value).Returns((UserNotificationPreference?)null);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && JsonSerializer.Deserialize<Dictionary<string, string>>(m.TemplateData!, (JsonSerializerOptions?)null)!.Count == 3
        ));

        await _smsSvc.Received().SendSmsMessage(Arg.Is<SmsMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Message == expectedOutput
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].PhoneNumber == "5555551234"
            && m.Recipients[0].NotificationId == 24
        ));

        await _emailSvc.Received().SendPlainEmail(Arg.Is<EmailMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Subject == expectedOutput
            && m.Message == expectedOutput
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].EmailAddress == "<EMAIL>"
            && m.Recipients[0].NotificationId == 24));
    }

    [Test]
    public async Task SendUserNotification_FilerSpecificNotificationPreferences()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences, out UserNotificationPreference filerSpecificPreferences);

        request = new SendUserNotificationRequest(2, 1982, 24, null, null);
        preferences.ReceiveStandardNotificationsBySms = true;
        filerSpecificPreferences.ReceiveStandardNotificationsBySms = false;

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserIdAndFilerId(request.UserId, request.FilerId!.Value).Returns(filerSpecificPreferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == request.UserId
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.DidNotReceive().SendSmsMessage(Arg.Any<SmsMessageRequest>());

        await _emailSvc.Received().SendPlainEmail(Arg.Is<EmailMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Subject == "Notification Subject"
            && m.Message == "Notifcation Body"
            && m.Recipients.Count == 1
            && m.Recipients[0].UserId == request.UserId
            && m.Recipients[0].EmailAddress == "<EMAIL>"
            && m.Recipients[0].NotificationId == 24));
    }

    [Test]
    public async Task SendFilerNotification()
    {
        //Arrange

        var request = new SendFilerNotificationRequest(2, 24, null, null);
        var template = SetupNotificationTemplate();
        var notification = new NotificationMessage()
        {
            Id = 24,
            UserId = 1, //This is only value returned from create shouldn't impact if not in sync
            FilerId = request.FilerId,
            NotificationTemplateId = request.NotificationTemplateId,
            CreatedAt = _dateNow,
            DueDate = request.DueDate,
            TemplateData = null
        };

        var allPreference = SetupPreferences(1, 1);//userId/FilerId shouldn't matter on preference
        allPreference.ReceiveStandardNotificationsByEmail = true;
        allPreference.ReceiveStandardNotificationsBySms = true;

        var emailOnlyPreference = SetupPreferences(1, 1);//userId/FilerId shouldn't matter on preference
        emailOnlyPreference.ReceiveStandardNotificationsByEmail = true;
        emailOnlyPreference.ReceiveStandardNotificationsBySms = false;

        var smsOnlyPreference = SetupPreferences(1, 1);//userId/FilerId shouldn't matter on preference
        smsOnlyPreference.ReceiveStandardNotificationsByEmail = false;
        smsOnlyPreference.ReceiveStandardNotificationsBySms = true;

        var nonePreference = SetupPreferences(1, 1);//userId/FilerId shouldn't matter on preference
        nonePreference.ReceiveStandardNotificationsByEmail = false;
        nonePreference.ReceiveStandardNotificationsBySms = false;

        var filerUsers = new List<long>() { 1, 2, 3, 4, 5 };

        _filerUserRepository.GetUserIdsByFilerId(request.FilerId).Returns(filerUsers);

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);

        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(1).Returns(allPreference);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(2).Returns(smsOnlyPreference);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(3).Returns((UserNotificationPreference?)null);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserIdAndFilerId(3, request.FilerId).Returns((UserNotificationPreference?)null);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserIdAndFilerId(4, request.FilerId).Returns(emailOnlyPreference);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserIdAndFilerId(5, request.FilerId).Returns(nonePreference);

        //Act
        await _notificationSvc.SendFilerNotification(request);

        //Assert
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == 1
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == 2
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == 3
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == 4
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));
        await _notificationMessageRepository.Received().Create(Arg.Is<NotificationMessage>(m =>
            m.UserId == 5
            && m.FilerId == request.FilerId
            && m.NotificationTemplateId == request.NotificationTemplateId
            && m.CreatedAt != null
            && m.DueDate == request.DueDate
            && m.TemplateData == null
        ));

        await _smsSvc.Received().SendSmsMessage(Arg.Is<SmsMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Message == "Received by SMS"
            && m.Recipients.Count == 2
            && m.Recipients.All(x => x.UserId == 1 || x.UserId == 2)
        ));

        await _emailSvc.Received().SendPlainEmail(Arg.Is<EmailMessageRequest>(m =>
            m.FilerId.Equals(request.FilerId)
            && m.Subject == "Notification Subject"
            && m.Message == "Notifcation Body"
            && m.Recipients.Count == 2
            && m.Recipients.All(x => x.UserId == 1 || x.UserId == 4)
        ));
    }


    private static void SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences)
    {
        SetupBasicUserNotification(out request, out template, out notification, out preferences, out _);
    }

    private static void SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences, out UserNotificationPreference filerSpecificPreferences)
    {
        request = new SendUserNotificationRequest(2, 1982, null, null, null);
        template = SetupNotificationTemplate();
        notification = SetupNotificationMessageRecord(request);
        preferences = SetupPreferences(request.UserId, request.FilerId);
        filerSpecificPreferences = SetupPreferences(request.UserId, request.FilerId);
    }

    private static NotificationTemplate SetupNotificationTemplate()
    {
        return new NotificationTemplate()
        {
            Id = 2,
            Translations = new List<NotificationTemplateTranslation>() {
                new() {
                    Locale = NotificationTemplateTranslation.EnglishLocale,
                    SmsMessage = "Received by SMS",
                    Subject = "Notification Subject",
                    Message = "Notifcation Body",
                }
            }
        };
    }

    private static UserNotificationPreference SetupPreferences(long userId, long? filerId)
    {
        return new UserNotificationPreference()
        {
            UserId = userId,
            FilerId = filerId,
            SmsPhoneNumber = "5555551234",
            EmailAddress = "<EMAIL>",
            ReceivePriorityNotificationsByEmail = false,
            ReceivePriorityNotificationsBySms = false,
            ReceiveStandardNotificationsByEmail = true,
            ReceiveStandardNotificationsBySms = true,
        };
    }

    private static NotificationMessage SetupNotificationMessageRecord(SendUserNotificationRequest request)
    {
        return new NotificationMessage()
        {
            Id = 24,
            UserId = request.UserId,
            FilerId = request.FilerId,
            NotificationTemplateId = request.NotificationTemplateId,
            CreatedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            DueDate = request.DueDate,
            TemplateData = null
        };
    }

    /// <summary>
    /// List all notifications Throw falilure on exception
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task ListAllNotifications_NotNull()
    {
        //Arrange
        var request = new PagedUserDataRequest("testuser", 10, 10, "Date", SortDirection.Ascending, null, null);
        long filerId = 1;
        //Act
        var notifications = await _notificationSvc.ListAllNotifications(request, filerId);

        // Assert
        Assert.That(notifications, Is.Not.Null);

    }

    [Test]
    public async Task ResolveNotification_ShouldUpdateNotifications_WhenActiveNotificationsExist()
    {

        // Arrange
        var request = new ResolveNotificationRequest(1, 2);
        var activeNotifications = new List<NotificationMessage>
        {
            new() { ResolvedAt = null },
            new() { ResolvedAt = null }
        };

        _notificationMessageRepository
            .FindAllActiveNotificationsByTemplateIdAndFilerId(request.NotificationTemplateId, request.FilerId)
            .Returns(Task.FromResult(activeNotifications));

        // Act
        await _notificationSvc.ResolveNotification(request);

        // Assert
        await _notificationMessageRepository.Received(1).UpdateRange(Arg.Any<List<NotificationMessage>>());
        Assert.That(activeNotifications.TrueForAll(n => n.ResolvedAt != null), Is.True, "All notifications should be marked as resolved.");
    }

    [Test]
    public async Task ResolveNotification_ShouldNotUpdate_WhenNoActiveNotificationsExist()
    {
        // Arrange
        var request = new ResolveNotificationRequest(1, 2);
        var emptyNotifications = new List<NotificationMessage>();

        _notificationMessageRepository
            .FindAllActiveNotificationsByTemplateIdAndFilerId(request.NotificationTemplateId, request.FilerId)
            .Returns(Task.FromResult(emptyNotifications));

        // Act
        await _notificationSvc.ResolveNotification(request);

        // Assert
        await _notificationMessageRepository.DidNotReceive().UpdateRange(Arg.Any<List<NotificationMessage>>());
    }

    [Test]
    public async Task ResolveNotification_ShouldNotThrowException_WhenRepositoryReturnsNull()
    {
        // Arrange
        var request = new ResolveNotificationRequest(1, 2);

        _notificationMessageRepository
            .FindAllActiveNotificationsByTemplateIdAndFilerId(request.NotificationTemplateId, request.FilerId)
            .Returns(Task.FromResult(new List<NotificationMessage>()));

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _notificationSvc.ResolveNotification(request));
        await _notificationMessageRepository.DidNotReceive().UpdateRange(Arg.Any<List<NotificationMessage>>());
    }

    [Test]
    public async Task ListAllNotifications_CountZero_ShouldReturnEmptyResults()
    {
        // Arrange
        var request = new PagedUserDataRequest("user1", 0, 10, "CreatedAt", SortDirection.Descending, null, "");
        _notificationMessageRepository
            .CountNotificationsByUserAndFilerTypeId("user1", Arg.Any<long>())
            .Returns(0);


        // Act
        var result = await _notificationSvc.ListAllNotifications(request, 1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Result, Is.Empty);
            Assert.That(result.Count, Is.EqualTo(0));
        });

        await _notificationMessageRepository.Received(1).CountNotificationsByUserAndFilerTypeId("user1", 1);
        await _notificationMessageRepository.DidNotReceive().FindAllNotificationsByUserAndFilerType(Arg.Any<PagedUserDataRequest>(), Arg.Any<long>());
    }

    [Test]
    public async Task ListAllNotifications_CountGreaterThanZero_NoSearchValue_ReturnsRepositoryCount()
    {
        // Arrange
        var request = new PagedUserDataRequest("user1", 0, 10, "CreatedAt", SortDirection.Descending, null, "");
        var fakeData = new List<NotificationMessageDto> { new() { Subject = "Test" } };

        _notificationMessageRepository
            .CountNotificationsByUserAndFilerTypeId("user1", 1)
            .Returns(5);

        _notificationMessageRepository
            .FindAllNotificationsByUserAndFilerType(request, 1)
            .Returns(fakeData);

        // Act
        var result = await _notificationSvc.ListAllNotifications(request, 1);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Result, Has.Count.EqualTo(1));
            Assert.That(result.Count, Is.EqualTo(5)); // From Count repo call
        });

        await _notificationMessageRepository.Received(1).CountNotificationsByUserAndFilerTypeId("user1", 1);
        _ = await _notificationMessageRepository.Received(1).FindAllNotificationsByUserAndFilerType(request, 1);
    }

    [Test]
    public async Task ListAllNotifications_SearchValueSet_ShouldOverrideCountWithResultsCount()
    {
        // Arrange
        var request = new PagedUserDataRequest("user1", 0, 10, "CreatedAt", SortDirection.Descending, new List<string> { "Subject" }, "searchTerm");
        var fakeData = new List<NotificationMessageDto>
    {
        new() { Subject = "Test1" },
        new() { Subject = "Test2" }
    };

        _notificationMessageRepository
            .CountNotificationsByUserAndFilerTypeId("user1", 1)
            .Returns(10);

        _notificationMessageRepository
            .FindAllNotificationsByUserAndFilerType(request, 1)
            .Returns(fakeData);

        // Act
        var result = await _notificationSvc.ListAllNotifications(request, 1);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Result, Has.Count.EqualTo(2));
            Assert.That(result.Count, Is.EqualTo(2));
        });

        await _notificationMessageRepository.Received(1).CountNotificationsByUserAndFilerTypeId("user1", 1);
        await _notificationMessageRepository.Received(1).FindAllNotificationsByUserAndFilerType(request, 1);
    }

    [Test]
    public async Task DeleteNotificationCanceledWhenActionRequired()
    {
        var actionRequiredTemplate = new NotificationTemplate()
        {
            Id = 2,
            IsActionRequired = true,
            NotificationTypeId = 1,
            Name = "Notification template",
            Description = "Description",
        };

        var notification = new NotificationMessage()
        {
            Id = 1,
            NotificationTemplate = actionRequiredTemplate,
            UserId = 1,
        };

        _notificationMessageRepository.FindById(actionRequiredTemplate.Id)
            .Returns(notification);

        var recordsAffected = await _notificationSvc.DeleteNotification(actionRequiredTemplate.Id);

        Assert.That(recordsAffected, Is.EqualTo(0));
    }

    [Test]
    public async Task DeleteNotificationSucceedsWhenNoActionRequired()
    {
        //Arrange
        var standardTemplate = new NotificationTemplate()
        {
            Id = 8,
            IsActionRequired = false,
            NotificationTypeId = 2,
            Name = "Standard template",
            Description = "Lorem ipsum",
        };

        var notification = new NotificationMessage()
        {
            Id = 8,
            NotificationTemplate = standardTemplate,
            UserId = 1,
        };

        _notificationMessageRepository.FindById(standardTemplate.Id)
            .Returns(notification);

        //Act
        var result = await _notificationSvc.DeleteNotification(standardTemplate.Id);

        //Assert
        await _notificationMessageRepository.Received(1).Delete(notification);
    }

    [Test]
    public async Task CountUnresolvedNotificationsForUserServiceTest()
    {
        // Arrange
        string testUser = "testUser";

        _notificationMessageRepository
            .CountUnresolvedNotificationsByUser(Arg.Is(testUser))
            .Returns(1);

        // Act
        var result = await _notificationSvc.CountUnresolvedeUserNotifications(testUser);

        // Assert
        Assert.That(result, Is.EqualTo(1));
    }

    [Test]
    public async Task SendUserNotification_TemplatedEmail()
    {
        //Arrange
        SetupBasicUserNotification(out SendUserNotificationRequest request, out NotificationTemplate template, out NotificationMessage notification, out UserNotificationPreference preferences);
        template.EnglishTranslation().EmailTemplateId = "c-21eb38";

        _notificationTemplateRepository.FindById(request.NotificationTemplateId).Returns(template);
        _notificationMessageRepository.Create(Arg.Any<NotificationMessage>()).Returns(notification);
        _notificationPreferenceRepository.FindNotificationPreferenceByUserId(request.UserId).Returns(preferences);

        //Act
        await _notificationSvc.SendUserNotification(request);

        //Assert
        await _emailSvc.Received(1).SendTemplatedEmail(Arg.Any<EmailTemplateRequest>());
    }

    [Test]
    public async Task ViewUnreadNotificationReturnsDto()
    {
        //Arrange
        var request = new SendUserNotificationRequest(2, 1982, null, null, null);
        var notification = SetupNotificationMessageRecord(request);
        var dto = new NotificationMessageDto
        {
            Id = notification.Id,
            NotificationTypeId = 1,
            UserId = notification.UserId,
            Subject = "Subject",
            Message = "Message"
        };

        _notificationMessageRepository.FindMessageById(notification.Id).Returns(dto);
        _notificationMessageRepository.FindById(notification.Id).Returns(notification);

        //Act
        var result = await _notificationSvc.ViewNotification(notification.Id);

        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task MarkReadOrUnread()
    {
        var notification = SetupNotificationMessageRecord(new SendUserNotificationRequest(8, 2, null, null, null));

        await _notificationSvc.MarkReadOrUnread(notification.Id);

        Assert.That(_notificationMessageRepository.ReceivedCalls().Any(), Is.True);
    }

}
