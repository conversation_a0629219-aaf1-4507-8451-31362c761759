using System.Net;
using System.Reflection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Refit;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Extensions;
using SOS.CalAccess.UI.Common.Services;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class CoalitionReceivedTransactionControllerTests
{
    #region Test Constants

    private const long TestFilerId = 123;
    private const long TestFilingId = 456;
    private const string TestReportType = "TestReportType";

    private const string SelectContactViewName = "01_SelectContact";

    #endregion

    #region Test Fixtures

    private CoalitionReceivedTransactionController _controller;
    private IHttpContextAccessor _httpContextAccessorMock;
    private ITempDataDictionaryFactory _tempDataFactoryMock;
    private IToastService _toastService;
    private ILogger<CoalitionReceivedTransactionController> _logger;
    private IStringLocalizer<SharedResources> _localizer;
    private ILobbyistEmployerCoalitionApi _lobbyistEmployerCoalitionApi;
    private IContactsApi _contactsApi;
    private ICoalitionReceivedTransactionCtlSvc _coalitionReceivedTransactionCtlSvc;
    private IFilingsApi _filingsApi;

    #endregion

    #region Setup

    [SetUp]
    public void SetupBeforeEachTest()
    {
        var tempDataMock = Substitute.For<ITempDataDictionary>();
        var httpContextMock = Substitute.For<HttpContext>();
        _httpContextAccessorMock = Substitute.For<IHttpContextAccessor>();
        _tempDataFactoryMock = Substitute.For<ITempDataDictionaryFactory>();
        _tempDataFactoryMock.GetTempData(httpContextMock).Returns(tempDataMock);
        _httpContextAccessorMock.HttpContext.Returns(httpContextMock);
        _toastService = Substitute.For<IToastService>();
        _logger = Substitute.For<ILogger<CoalitionReceivedTransactionController>>();
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _lobbyistEmployerCoalitionApi = Substitute.For<ILobbyistEmployerCoalitionApi>();
        _contactsApi = Substitute.For<IContactsApi>();
        _coalitionReceivedTransactionCtlSvc = Substitute.For<ICoalitionReceivedTransactionCtlSvc>();
        _filingsApi = Substitute.For<IFilingsApi>();
        _controller = new CoalitionReceivedTransactionController(_toastService, _logger, _localizer, _lobbyistEmployerCoalitionApi, _contactsApi, _coalitionReceivedTransactionCtlSvc, _filingsApi);
    }

    #endregion

    #region Helper Methods
    /// <summary>
    /// Asserts that an IActionResult is a valid view result with the expected view name and model properties
    /// </summary>
    private static void AssertViewResultIsValid(
        IActionResult result,
        string expectedViewName)
    {
        Assert.That(result, Is.InstanceOf<ViewResult>(), "Result should be a ViewResult");

        var viewResult = result as ViewResult;
        Assert.That(viewResult?.ViewName, Is.EqualTo(expectedViewName), "View name should match");

        var viewModel = viewResult.Model as CoalitionReceivedTransactionViewModel;
        Assert.That(viewModel, Is.Not.Null, "ViewModel should not be null");

        Assert.Multiple(() =>
        {
            Assert.That(viewModel.FilerId, Is.EqualTo(TestFilerId), "FilerId should match");
            Assert.That(viewModel.FilingId, Is.EqualTo(TestFilingId), "FilingId should match");
            Assert.That(viewModel.ReportType, Is.EqualTo(TestReportType), "ReportType should match");
        });
    }

    #endregion

    #region SelectContact Action Tests

    [Test]
    public async Task SelectContact_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("FilerId", "Required");

        // Act
        var result = await _controller.SelectContact(TestReportType, TestFilingId, TestFilerId, null);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SelectContact_WithValidModelState_ReturnsViewWithNewModel()
    {
        var expectedModel = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId
        };

        _ = _coalitionReceivedTransactionCtlSvc.GetSelectContactViewModel(TestReportType, TestFilingId, TestFilerId, null, Arg.Any<CancellationToken>()).Returns(expectedModel);

        // Act
        var result = await _controller.SelectContact(TestReportType, TestFilingId, TestFilerId, null);

        // Assert
        AssertViewResultIsValid(result, SelectContactViewName);
    }

    [Test]
    public async Task SelectContact_WithCancellationToken_ProcessesRequestCorrectly()
    {
        // Arrange
        var cancellationToken = new CancellationToken();
        var expectedModel = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId
        };

        _ = _coalitionReceivedTransactionCtlSvc.GetSelectContactViewModel(TestReportType, TestFilingId, TestFilerId, null, Arg.Any<CancellationToken>()).Returns(expectedModel);

        // Act
        var result = await _controller.SelectContact(TestReportType, TestFilingId, TestFilerId, null, cancellationToken);

        // Assert
        AssertViewResultIsValid(result, SelectContactViewName);
    }

    [Test]
    public async Task SelectContact_Post_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId
        };
        _controller.ModelState.AddModelError("ContactId", "Required");

        // Act
        var result = await _controller.SelectContact(model, "Post");

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SelectContact_Post_WithValidModelState_RedirectsToEnterContact()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789
        };

        // Act
        var result = await _controller.SelectContact(model, "Continue");

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo(nameof(CoalitionReceivedTransactionController.EnterContact)));
            Assert.That(redirectResult.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
            Assert.That(redirectResult.RouteValues?["contactId"], Is.EqualTo(789));
        });
    }

    #endregion

    #region SearchFilerContactsByIdOrName Action Tests

    [Test]
    public async Task SearchFilerContactsByIdOrName_WithValidModelState_ReturnsSearchResults()
    {
        // Arrange
        var searchText = "test";
        var expectedContactTypes = new List<string> { "Individual", "Organization" };
        var expectedResults = new List<ContactSearchResultDto>
        {
            new("123 Main St", string.Empty, 1, TestFilerId, string.Empty, null),
            new("234 Broadway Blvd", "Suite 100", 2, TestFilerId, string.Empty, null)
        };

        var contactsApi = Substitute.For<IContactsApi>();
        _ = contactsApi.SearchContactsByNameOrId(
            TestFilerId,
            searchText,
            Arg.Is<IEnumerable<string>>(types =>
                types.Count() == expectedContactTypes.Count &&
                types.All(t => expectedContactTypes.Contains(t))),
            Arg.Any<CancellationToken>())
            .Returns(expectedResults);

        // Act
        var result = await _controller.SearchFilerContactsByIdOrName(contactsApi, searchText, TestFilerId, CancellationToken.None);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.InstanceOf<JsonResult>());
            Assert.That(result?.Value, Is.SameAs(expectedResults));
        });
        _ = await contactsApi.Received(1).SearchContactsByNameOrId(
            TestFilerId,
            searchText,
            Arg.Any<IEnumerable<string>>(),
            Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task SearchFilerContactsByIdOrName_WithNoResults_ReturnsEmptyList()
    {
        // Arrange
        var searchText = "nonexistent";
        var emptyResults = new List<ContactSearchResultDto>();

        var contactsApi = Substitute.For<IContactsApi>();
        contactsApi.SearchContactsByNameOrId(
            Arg.Any<long>(),
            Arg.Any<string>(),
            Arg.Any<IEnumerable<string>>(),
            Arg.Any<CancellationToken>())
            .Returns(emptyResults);

        // Act
        var result = await _controller.SearchFilerContactsByIdOrName(contactsApi, searchText, TestFilerId, CancellationToken.None);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.InstanceOf<JsonResult>());
            Assert.That(result?.Value, Is.Empty);
        });
    }

    #endregion

    #region EnterContact Action Tests
    [Test]
    public async Task EnterContact_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("ContactId", "Required");

        // Act
        var result = await _controller.EnterContact(TestReportType, TestFilingId, TestFilerId, null, null);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EnterContact_WithValidModelState_ReturnsViewWithModel()
    {
        // Arrange
        var expectedModel = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789
        };
        _ = _coalitionReceivedTransactionCtlSvc.GetEnterContactViewModel(TestReportType, TestFilingId, TestFilerId, 789, Arg.Any<CancellationToken>())
            .Returns(expectedModel);

        // Act
        var result = await _controller.EnterContact(TestReportType, TestFilingId, TestFilerId, 789, 1);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("02_EnterContact"));
            Assert.That(viewResult?.Model, Is.SameAs(expectedModel));
        });
    }

    [Test]
    public async Task EnterContact_Post_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId
        };
        _controller.ModelState.AddModelError("Contact", "Required");

        // Act
        var result = await _controller.EnterContact(model, "Continue");

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EnterContact_Post_WithPreviousAction_RedirectsToSelectContact()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            Contact = new GenericContactViewModel { TypeId = 1 }
        };

        // Act
        var result = await _controller.EnterContact(model, "Previous");

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult?.ActionName, Is.EqualTo("SelectContact"));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
        });
    }

    [Test]
    public async Task EnterContact_Post_WithContinueAction_ExistingContact_RedirectsToEnterAmount()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789,
            TransactionId = 1,
            Contact = new GenericContactViewModel { Id = 1, TypeId = CalAccess.Models.FilerDisclosure.Contacts.FilerContactType.Organization.Id }
        };

        var contactsApi = Substitute.For<IContactsApi>();
        var contactsApiField = typeof(CoalitionReceivedTransactionController)
            .GetField("_contactsApi", BindingFlags.NonPublic | BindingFlags.Instance);
        if (contactsApiField != null)
        {
            contactsApiField.SetValue(_controller, contactsApi);
        }
        else
        {
            throw new InvalidOperationException("The field '_contactsApi' was not found.");
        }

        _controller.ModelState.Clear();

        // Act
        var result = await _controller.EnterContact(model, "Continue");

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult?.ActionName, Is.EqualTo("EnterAmount"));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
            Assert.That(redirectResult?.RouteValues?["contactId"], Is.EqualTo(789));
        });

        // Verify that UpdateFilerContact was called once with the expected parameters
        await contactsApi.Received(1).UpdateFilerContact(
            Arg.Is(789L),
            Arg.Any<UpsertFilerContactRequest>(),
            Arg.Any<CancellationToken>());
    }

#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

    [Test]
    public async Task EnterContact_Post_WithContinueAction_NewContact_CreatesContactAndRedirects()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = null,
            Contact = new GenericContactViewModel
            {
                Id = 1,
                TypeId = CalAccess.Models.FilerDisclosure.Contacts.FilerContactType.Organization.Id,
                OrganizationName = "Test Organization"
            }
        };

        var contactsApi = Substitute.For<IContactsApi>();
        var newContactResponse = new ContactItemResponse(
            addressId: null,
            city: "Sample City",
            country: "Sample Country",
            emailAddresses: new List<EmailAddress> { new("<EMAIL>", 1, 1, "Active", "12345") },
            filerId: 123,
            id: 456,
            phoneNumbers: new List<PhoneNumber> { new(null, "+1", 1, 1, "222", 1, false, false, 1, "1234567890", 1, "Mobile") },
            state: "Sample State",
            street: "123 Sample St",
            street2: "Apt 4",
            typeId: 1,
            valid: true,
            validationErrors: new List<WorkFlowError>(),
            website: "http://example.com",
            zipCode: "12345"
        );

        var idProperty = typeof(ContactItemResponse).GetProperty("Id");
        if (idProperty != null)
        {
            idProperty.SetValue(newContactResponse, 999L);
        }
        else
        {
            throw new InvalidOperationException("The property 'Id' was not found.");
        }

        _ = contactsApi.CreateFilerContact(
            Arg.Is(TestFilerId),
            Arg.Any<UpsertFilerContactRequest>(),
            Arg.Any<CancellationToken>())
            .Returns(newContactResponse);

        var contactsApiField = typeof(CoalitionReceivedTransactionController)
            .GetField("_contactsApi", BindingFlags.NonPublic | BindingFlags.Instance);
        if (contactsApiField != null)
        {
            contactsApiField.SetValue(_controller, contactsApi);
        }
        else
        {
            throw new InvalidOperationException("The field '_contactsApi' was not found.");
        }

        // Act
        var result = await _controller.EnterContact(model, "Continue");

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult?.ActionName, Is.EqualTo("EnterAmount"));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
            Assert.That(redirectResult?.RouteValues?["contactId"], Is.EqualTo(999L));
        });

        _ = await contactsApi.Received(1).CreateFilerContact(
            Arg.Is(TestFilerId),
            Arg.Any<UpsertFilerContactRequest>(),
            Arg.Any<CancellationToken>());
    }
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.

    [Test]
    public async Task EnterContact_Post_WithValidationErrors_ReturnsViewWithModelErrors()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = null,
            Contact = new GenericContactViewModel
            {
                Id = 1,
                TypeId = 999,
            }
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("Field", "Invalid value", "", "")
        };

        var errorResponse = new ContactItemResponse(
            addressId: null,
            city: "Sample City",
            country: "Sample Country",
            emailAddresses: new List<EmailAddress>(),
            filerId: TestFilerId,
            id: 0,
            phoneNumbers: new List<PhoneNumber>(),
            state: "Sample State",
            street: "123 Sample St",
            street2: "Apt 4",
            typeId: 1,
            valid: false,
            validationErrors: validationErrors,
            website: "",
            zipCode: "12345"
        );

        var contactsApi = Substitute.For<IContactsApi>();
        _ = contactsApi.CreateFilerContact(
            Arg.Is(TestFilerId),
            Arg.Any<UpsertFilerContactRequest>(),
            Arg.Any<CancellationToken>())
            .Returns(errorResponse);

        var contactsApiField = typeof(CoalitionReceivedTransactionController)
            .GetField("_contactsApi", BindingFlags.NonPublic | BindingFlags.Instance);
        contactsApiField?.SetValue(_controller, contactsApi);

        // Act
        var result = await _controller.EnterContact(model, "Continue");

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("02_EnterContact"));
            Assert.That(viewResult?.Model, Is.EqualTo(model));
        });

        _ = await contactsApi.Received(1).CreateFilerContact(
            Arg.Is(TestFilerId),
            Arg.Any<UpsertFilerContactRequest>(),
            Arg.Any<CancellationToken>());
    }


    [Test]
    public async Task EnterContact_Post_WithContinueAction_ApiException_ReturnsViewWithError()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789,
            Contact = new GenericContactViewModel { TypeId = CalAccess.Models.FilerDisclosure.Contacts.FilerContactType.Individual.Id }
        };

        var contactsApi = Substitute.For<IContactsApi>();
        _ = contactsApi.UpdateFilerContact(Arg.Any<long>(), Arg.Any<UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Throws(new InvalidOperationException("Test exception"));

        var contactsApiField = typeof(CoalitionReceivedTransactionController)
            .GetField("_contactsApi", BindingFlags.NonPublic | BindingFlags.Instance);
        if (contactsApiField != null)
        {
            contactsApiField.SetValue(_controller, contactsApi);
        }
        else
        {
            throw new InvalidOperationException("The field '_contactsApi' was not found.");
        }

        // Act
        var result = await _controller.EnterContact(model, "Continue");

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("03_EnterAmount"));
            Assert.That(viewResult?.Model, Is.SameAs(model));
        });
    }

    [Test]
    public async Task EnterContact_Post_UpdateWithValidationErrors_ReturnsViewWithModelErrors()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789,
            Contact = new GenericContactViewModel { TypeId = CalAccess.Models.FilerDisclosure.Contacts.FilerContactType.Individual.Id }
        };

        var fakeResponse = new HttpResponseMessage(HttpStatusCode.UnprocessableEntity)
        {
            Content = new StringContent(JsonConvert.SerializeObject(new[]
            {
                new
                {
                    FieldName = "Amount",
                    Message = "Invalid amount",
                    ErrorType = "Validation"
                }
            }))
        };

        var apiException = await ApiException.Create(
             new HttpRequestMessage(),
             HttpMethod.Post,
             fakeResponse,
             new RefitSettings());

        var contactsApi = Substitute.For<IContactsApi>();
        _ = contactsApi.UpdateFilerContact(Arg.Any<long>(), Arg.Any<UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Throws(apiException);

        var contactsApiField = typeof(CoalitionReceivedTransactionController)
            .GetField("_contactsApi", BindingFlags.NonPublic | BindingFlags.Instance);
        if (contactsApiField != null)
        {
            contactsApiField.SetValue(_controller, contactsApi);
        }
        else
        {
            throw new InvalidOperationException("The field '_contactsApi' was not found.");
        }

        // Act
        var result = await _controller.EnterContact(model, "Continue");

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("02_EnterContact"));
            Assert.That(viewResult?.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task EnterContact_Post_WithCancelAction_RedirectsToIndex()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789
        };

        // Act
        var result = await _controller.EnterContact(model, "Cancel");

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult?.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult?.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult?.RouteValues?["viewName"], Is.EqualTo(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
        });
    }
    #endregion

    #region EnterAmount Action Tests

    [Test]
    public async Task EnterAmount_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Amount", "Required");

        // Act
        var result = await _controller.EnterAmount(TestReportType, TestFilingId, TestFilerId, 789, null, _lobbyistEmployerCoalitionApi);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EnterAmount_WithValidModelState_ReturnsViewWithModel()
    {
        // Arrange
        var expectedModel = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789
        };
        _ = _coalitionReceivedTransactionCtlSvc.GetEnterAmountViewModel(TestReportType, TestFilingId, TestFilerId, 789, Arg.Any<CancellationToken>())
            .Returns(expectedModel);
        var transactionId = 1;
        var contact = new ContactResponseDto(new List<AddressDto>(), new List<EmailAddress>(), 1, 1, new List<PhoneNumberDto>(), 1);
        var response = new PaymentReceiveLobbyingCoalitionResponse(10, "", contact, 1, 1);

        _lobbyistEmployerCoalitionApi.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId).Returns(Task.FromResult(response));

        // Act
        var result = await _controller.EnterAmount(TestReportType, TestFilingId, TestFilerId, 789, transactionId, _lobbyistEmployerCoalitionApi);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("03_EnterAmount"));
            Assert.That(viewResult?.Model, Is.SameAs(expectedModel));
        });
    }

    [Test]
    public async Task EnterAmount_Post_WithInvalidModelState_AddsValidationMessagesToModel()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789,
            Messages = new PortalAlerts()
        };

        _controller.ModelState.AddModelError("Amount", "Amount is required");

        // Act
        var result = await _controller.EnterAmount(model, "Continue");

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("03_EnterAmount"));
            Assert.That(viewResult?.Model, Is.SameAs(model));

            var viewModel = viewResult?.Model as CoalitionReceivedTransactionViewModel;
            Assert.That(viewModel?.Messages.Validations.ContainsKey("Amount"), Is.True);
            Assert.That(viewModel?.Messages.Validations["Amount"].Message, Is.EqualTo("Amount is required"));
        });
    }

    [Test]
    public async Task EnterAmount_Post_WithPreviousAction_RedirectsToEnterContact()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789
        };

        // Act
        var result = await _controller.EnterAmount(model, "Previous");

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult?.ActionName, Is.EqualTo(nameof(CoalitionReceivedTransactionController.EnterContact)));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
            Assert.That(redirectResult?.RouteValues?["contactId"], Is.EqualTo(789));
        });
    }

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    public async Task EnterAmount_Post_WithSaveAction_CreatesOrUpdateTransactionAndRedirects(bool isCreate)
    {
        // Arrange
        var transactionId = 1;
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789,
            Amount = 1000,
            Id = isCreate ? null : transactionId
        };

        var request = new PaymentReceiveLobbyingCoalitionRequestDto(
            (double)model.Amount,
            model.ContactId.Value,
            model.FilingId.Value,
            model.FilerId.Value);

        var response = new TransactionResponseDto(1, true, []);

        _lobbyistEmployerCoalitionApi
        .CreatePaymentReceivedByLobbyingCoalition(Arg.Any<PaymentReceiveLobbyingCoalitionRequestDto>(), Arg.Any<CancellationToken>())
        .Returns(response);

        _lobbyistEmployerCoalitionApi
          .EditPaymentReceivedByLobbyingCoalition(transactionId, Arg.Any<PaymentReceiveLobbyingCoalitionRequestDto>(), Arg.Any<CancellationToken>())
          .Returns(response);

        // Act
        var result = await _controller.EnterAmount(model, "Save");

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult?.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult?.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult?.RouteValues?["viewName"], Is.EqualTo(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
        });

        if (isCreate)
        {
            _ = await _lobbyistEmployerCoalitionApi.Received(1).CreatePaymentReceivedByLobbyingCoalition(Arg.Is<PaymentReceiveLobbyingCoalitionRequestDto>(r =>
                Math.Abs(r.Amount - request.Amount) < 0.0001 &&
                r.DisclosureFilingId == request.DisclosureFilingId &&
                r.ContactId == request.ContactId &&
                r.FilerId == request.FilerId), Arg.Any<CancellationToken>());
        }
        else
        {
            _ = await _lobbyistEmployerCoalitionApi.Received(1).EditPaymentReceivedByLobbyingCoalition(transactionId, Arg.Is<PaymentReceiveLobbyingCoalitionRequestDto>(r =>
                Math.Abs(r.Amount - request.Amount) < 0.0001 &&
                r.DisclosureFilingId == request.DisclosureFilingId &&
                r.ContactId == request.ContactId &&
                r.FilerId == request.FilerId), Arg.Any<CancellationToken>());
        }
    }

    [Test]
    public async Task SavePaymentReceivedAsync_WithInvalidResponse_ReturnsViewWithValidationErrors()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789,
            Amount = 1000
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("errCode", "errType", "Amount", "Amount must be greater than zero.")
        };

        var response = new TransactionResponseDto(1, false, validationErrors);

        var apiMock = Substitute.For<ILobbyistEmployerCoalitionApi>();
        apiMock.CreatePaymentReceivedByLobbyingCoalition(Arg.Any<PaymentReceiveLobbyingCoalitionRequestDto>(), Arg.Any<CancellationToken>())
               .Returns(response);

        var apiField = typeof(CoalitionReceivedTransactionController)
            .GetField("_lobbyistEmployerCoalitionApi", BindingFlags.NonPublic | BindingFlags.Instance);
        apiField?.SetValue(_controller, apiMock);

        // Act
        var result = await _controller.EnterAmount(model, "Save");

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("03_EnterAmount"));
            Assert.That(viewResult?.Model, Is.EqualTo(model));
            Assert.That(model.Messages.Validations.ContainsKey("Amount"));
            Assert.That(model.Messages.Validations["Amount"].Message, Is.EqualTo("Amount must be greater than zero."));
        });

        await apiMock.Received(1)
            .CreatePaymentReceivedByLobbyingCoalition(Arg.Any<PaymentReceiveLobbyingCoalitionRequestDto>(), Arg.Any<CancellationToken>());
    }


    [Test]
    public async Task EnterAmount_Post_WithSaveAction_HandlesExceptionAndReturnsView()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789,
            Amount = 1000
        };

        var request = new PaymentReceiveLobbyingCoalitionRequestDto(
            (double)model.Amount,
            model.ContactId.Value,
            model.FilingId.Value,
            model.FilerId.Value);

        _ = _lobbyistEmployerCoalitionApi.CreatePaymentReceivedByLobbyingCoalition(Arg.Any<PaymentReceiveLobbyingCoalitionRequestDto>(), Arg.Any<CancellationToken>())
            .Throws(new InvalidOperationException("Test exception"));

        // Act
        var result = await _controller.EnterAmount(model, "Save");

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult?.ViewName, Is.EqualTo("03_EnterAmount"));
            Assert.That(viewResult?.Model, Is.SameAs(model));
        });

        _ = await _lobbyistEmployerCoalitionApi.Received(1).CreatePaymentReceivedByLobbyingCoalition(Arg.Is<PaymentReceiveLobbyingCoalitionRequestDto>(r =>
            r.Amount == request.Amount &&
            r.DisclosureFilingId == request.DisclosureFilingId &&
            r.ContactId == request.ContactId &&
            r.FilerId == request.FilerId), Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task EnterAmount_Post_WithCancelAction_RedirectsToIndex()
    {
        // Arrange
        var model = new CoalitionReceivedTransactionViewModel
        {
            ReportType = TestReportType,
            FilingId = TestFilingId,
            FilerId = TestFilerId,
            ContactId = 789
        };

        // Act
        var result = await _controller.EnterAmount(model, "Cancel");

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult?.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult?.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult?.RouteValues?["viewName"], Is.EqualTo(FilingSummaryTypeModel.RecieveLobbyingCoalitionSummary.Name));
            Assert.That(redirectResult?.RouteValues?["reportType"], Is.EqualTo(TestReportType));
            Assert.That(redirectResult?.RouteValues?["filingId"], Is.EqualTo(TestFilingId));
            Assert.That(redirectResult?.RouteValues?["filerId"], Is.EqualTo(TestFilerId));
        });
    }
    #endregion

    #region TearDown

    [TearDown]
    public void TearDownAfterEachTest()
    {
        _controller?.Dispose();
    }

    #endregion
}
