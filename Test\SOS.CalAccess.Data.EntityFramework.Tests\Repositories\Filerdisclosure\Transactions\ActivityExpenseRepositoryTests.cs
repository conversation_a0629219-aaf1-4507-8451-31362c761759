using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Transactions;

/// <summary>
/// Contains unit tests for the <see cref="ActivityExpenseRepository"/> class.
/// </summary>
[TestFixture]
[TestOf(typeof(ActivityExpenseRepository))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class ActivityExpenseRepositoryTests
{
    private ActivityExpenseRepository _repository;
    private DatabaseContext _context;
    private static DatabaseContextFactory _factory;

    [OneTimeSetUp]
    public static void OneTimeSetUp()
    {
        _factory = new DatabaseContextFactory();
    }

    [SetUp]
    public async Task SetUp()
    {
        _context = await _factory.CreateContext();
        _repository = new ActivityExpenseRepository(_context);
    }

    [TearDown]
    public async Task TearDown()
    {
        await _context.DisposeAsync();
    }

    private ActivityExpense NewActivityExpense()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        return new ActivityExpense
        {
            FilerId = 1,
            ActivityExpenseTypeId = 1,
            Amount = (Currency)100.00m,
            Notes = "Test Activity Expense",
            TransactionDate = date,
        };
    }

    [Test]
    public async Task Create_Succeeds_WithValidActivityExpense()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        // Arrange
        var activityExpense = NewActivityExpense();
        // Act
        var result = await _repository.Create(activityExpense);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.GreaterThan(0));
            Assert.That(result.Notes, Is.EqualTo("Test Activity Expense"));
        });
    }

    [Test]
    public async Task AddReportablePersonsToTransaction_WithFilerId_Succeeds_WithValidReportablePersons()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());

        var reportablePersons = new List<TransactionReportablePersonDto>
        {
            new() { FilerContactId = 1, Amount = (Currency)100.00m },
        };

        // Act
        await _repository.AddReportablePersonsToTransaction(activityExpense.Id, reportablePersons);

        // Assert
        var transactionReportablePersons = await _context.Set<TransactionReportablePerson>()
            .Where(tc => tc.TransactionId == activityExpense.Id)
            .ToListAsync();

        Assert.That(transactionReportablePersons, Has.Count.EqualTo(1));
        Assert.That(transactionReportablePersons.Select(tc => tc.FilerContactId), Is.EquivalentTo(reportablePersons.Select(rp => rp.FilerContactId)));
    }


    [Test]
    public async Task AddReportablePersonsToTransaction_WithRegistrationId_Succeeds_WithValidReportablePersons()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());

        var reportablePersons = new List<TransactionReportablePersonDto>
        {
            new() { RegistrationId = 1, Amount = (Currency)100.00m },
        };

        // Act
        await _repository.AddReportablePersonsToTransaction(activityExpense.Id, reportablePersons);

        // Assert
        var transactionReportablePersons = await _context.Set<TransactionReportablePerson>()
            .Where(tc => tc.TransactionId == activityExpense.Id)
            .ToListAsync();

        Assert.That(transactionReportablePersons, Has.Count.EqualTo(1));
        Assert.That(transactionReportablePersons.Select(tc => tc.RegistrationId), Is.EquivalentTo(reportablePersons.Select(rp => rp.RegistrationId)));
    }

    [Test]
    public async Task AddReportablePersonsToTransaction_WithCustomContact_Succeeds_WithValidReportablePersons()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());

        var reportablePersons = new List<TransactionReportablePersonDto>
        {
            new() { Amount = (Currency)100.00m, Name = "Test Name", OfficialPosition = "Test Position", OfficialPositionDescription = "Test Position Description", Agency = "Test Agency" },
        };

        // Act
        await _repository.AddReportablePersonsToTransaction(activityExpense.Id, reportablePersons);

        // Assert
        var transactionReportablePersons = await _context.Set<TransactionReportablePerson>()
            .Where(tc => tc.TransactionId == activityExpense.Id)
            .ToListAsync();

        Assert.That(transactionReportablePersons, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(transactionReportablePersons.Select(tc => tc.Name), Is.EquivalentTo(reportablePersons.Select(rp => rp.Name)));
            Assert.That(transactionReportablePersons.Select(tc => tc.OfficialPosition), Is.EquivalentTo(reportablePersons.Select(rp => rp.OfficialPosition)));
            Assert.That(transactionReportablePersons.Select(tc => tc.OfficialPositionDescription), Is.EquivalentTo(reportablePersons.Select(rp => rp.OfficialPositionDescription)));
            Assert.That(transactionReportablePersons.Select(tc => tc.Agency), Is.EquivalentTo(reportablePersons.Select(rp => rp.Agency)));
        });
    }

    [Test]
    public async Task AddReportablePersonsToTransaction_DoesNothing_WhenReportablePersonsAreEmpty()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());

        // Act
        await _repository.AddReportablePersonsToTransaction(activityExpense.Id, Array.Empty<TransactionReportablePersonDto>());

        // Assert
        var transactionReportablePersons = await _context.Set<TransactionReportablePerson>()
            .Where(tc => tc.TransactionId == activityExpense.Id)
            .ToListAsync();

        Assert.That(transactionReportablePersons, Is.Empty);
    }

    [Test]
    public async Task Update_Succeeds_WithValidActivityExpense()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());
        activityExpense.Notes = "Updated Description";

        // Act
        var result = await _repository.Update(activityExpense);

        // Assert
        Assert.That(result.Notes, Is.EqualTo("Updated Description"));
        var savedExpense = await _repository.FindById(result.Id);
        Assert.That(savedExpense.Notes, Is.EqualTo("Updated Description"));
    }

    [Test]
    public async Task Delete_Succeeds_WithExistingActivityExpense()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());

        // Act
        var result = await _repository.Delete(activityExpense);

        // Assert
        Assert.That(result, Is.True);
        var deletedExpense = await _repository.FindById(activityExpense.Id);
        Assert.That(deletedExpense, Is.Null);
    }

    [Test]
    public async Task Update_ThrowsException_WithNonExistentActivityExpense()
    {
        // Arrange
        var activityExpense = NewActivityExpense();
        activityExpense.Id = 99999;

        // Act & Assert
        Assert.ThrowsAsync<DbUpdateConcurrencyException>(() => _repository.Update(activityExpense));
    }

    [Test]
    public async Task GetActivityExpenseForUpdate_ReturnsCorrectData_WhenExists()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var testExpense = new ActivityExpense
        {
            FilerId = 1,
            ContactId = 1,
            ActivityExpenseTypeId = 1,
            Amount = (Currency)100.00m,
            Notes = "Test Expense",
            TransactionDate = date,
            MonetaryTypeId = MonetaryType.Credit.Id,
            CreditCardCompanyName = "VISA",
            FilingTransactions = new List<FilingTransaction>
            {
                new() { FilingId = 100 }
            },
            TransactionReportablePersons = new List<TransactionReportablePerson>
            {
                new()
                {
                    Name = "John Doe",
                    Amount = (Currency)50.00m,
                    Agency = "Test Agency"
                }
            }
        };

        _context.Transactions.Add(testExpense);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActivityExpenseForUpdate(testExpense.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(testExpense.Id));
            Assert.That(result.FilingId, Is.EqualTo(100));
            Assert.That(result.Notes, Is.EqualTo("Test Expense"));
            Assert.That(result.TransactionReportablePersons, Has.Count.EqualTo(1));
            Assert.That(result.TransactionReportablePersons![0].Name, Is.EqualTo("John Doe"));
            Assert.That(result.IsCreditCard, Is.True);
        });
    }

    [Test]
    public async Task GetActivityExpenseForUpdate_ReturnsNull_WhenNotFound()
    {
        // Act
        var result = await _repository.GetActivityExpenseForUpdate(99999);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task RemoveReportablePersonsForTransaction_RemovesAllPersons_WhenTheyExist()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());

        // Add some reportable persons
        var reportablePersons = new List<TransactionReportablePersonDto>
        {
            new() { FilerContactId = 1, Amount = (Currency)100.00m },
            new() { RegistrationId = 2, Amount = (Currency)200.00m }
        };
        await _repository.AddReportablePersonsToTransaction(activityExpense.Id, reportablePersons);

        // Verify they were added
        var initialCount = await _context.Set<TransactionReportablePerson>()
            .Where(x => x.TransactionId == activityExpense.Id)
            .CountAsync();
        Assert.That(initialCount, Is.EqualTo(2), "Precondition failed: Reportable persons not added correctly");

        // Act
        await _repository.RemoveReportablePersonsForTransaction(activityExpense.Id);

        // Assert
        var remainingPersons = await _context.Set<TransactionReportablePerson>()
            .Where(x => x.TransactionId == activityExpense.Id)
            .ToListAsync();

        Assert.That(remainingPersons, Is.Empty, "All reportable persons should have been removed");
    }

    [Test]
    public async Task RemoveReportablePersonsForTransaction_DoesNothing_WhenNoPersonsExist()
    {
        // Arrange
        var activityExpense = await _repository.Create(NewActivityExpense());

        // Verify no reportable persons exist initially
        var initialCount = await _context.Set<TransactionReportablePerson>()
            .Where(x => x.TransactionId == activityExpense.Id)
            .CountAsync();
        Assert.That(initialCount, Is.EqualTo(0), "Precondition failed: Should start with no reportable persons");

        // Act
        await _repository.RemoveReportablePersonsForTransaction(activityExpense.Id);

        // Assert
        var remainingPersons = await _context.Set<TransactionReportablePerson>()
            .Where(x => x.TransactionId == activityExpense.Id)
            .ToListAsync();

        Assert.That(remainingPersons, Is.Empty, "No persons should exist after removal attempt");
    }
}
