// <copyright file="UpdateFilingCommandTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filings;


/// <summary>
/// Tests for the <see cref="UpdateFilingCommand"/> class.
/// </summary>
[TestFixture]
[Parallelizable(ParallelScope.All)]
[TestOf(typeof(UpdateFilingCommand))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public sealed class UpdateFilingCommandTests
{
    /// <summary>
    /// Tests that the <see cref="UpdateFilingCommand.Apply"/> method mutates the specified <see cref="Filing"/> object.
    /// </summary>
    [Test]
    public void Apply_WhenCalled_MutatesFiling()
    {
        // Arrange
        var fixedNow = new DateTime(2025, 1, 1, 12, 0, 0, DateTimeKind.Local);
        var endDate = fixedNow;
        var startDate = fixedNow.AddDays(-1);

        var command = new UpdateFilingCommand { Id = default, EndDate = endDate, StartDate = startDate, };

        var filing = new Filing
        {
            EndDate = DateTime.MinValue,
            StartDate = DateTime.MinValue,
            StatusId = FilingStatus.Pending.Id,
        };

        // Act
        var result = command.Apply(filing) as Success<Filing>;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result?.Value, Is.EqualTo(filing));
            Assert.That(filing.EndDate, Is.EqualTo(endDate));
            Assert.That(filing.StartDate, Is.EqualTo(startDate));
        });
    }

    /// <summary>
    /// Tests that the <see cref="UpdateFilingCommand.Apply"/> method returns a <see cref="Failure{Filing}.InvalidState"/>
    /// instance when the start date is after the end date.
    /// </summary>
    [Test]
    public void Apply_WhenStartDateIsAfterEndDate_ReturnsInvalidState()
    {
        // Arrange
        var fixedNow = new DateTime(2025, 1, 1, 12, 0, 0, DateTimeKind.Local);
        var endDate = fixedNow;
        var startDate = fixedNow.AddDays(1);

        var command = new UpdateFilingCommand { Id = default, EndDate = endDate, StartDate = startDate, };

        var filing = new Filing
        {
            EndDate = DateTime.MinValue,
            StartDate = DateTime.MinValue,
            StatusId = FilingStatus.Pending.Id,
        };

        // Act
        var result = command.Apply(filing) as Failure<Filing>.InvalidState;

        // Assert
        Assert.That(result, Is.Not.Null);
    }
    [Test]
    public void Apply_WhenStartOrEndDateIsNull_ShouldNotSetDates()
    {
        // Arrange
        var fixedEndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        var command = new UpdateFilingCommand
        {
            Id = default,
            StartDate = null,
            EndDate = fixedEndDate,
        };

        var filing = new Filing
        {
            EndDate = DateTime.MinValue,
            StartDate = DateTime.MinValue,
            StatusId = FilingStatus.Pending.Id,
        };

        // Act
        var result = command.Apply(filing);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<Success<Filing>>());
            Assert.That(filing.StartDate, Is.EqualTo(DateTime.MinValue));
            Assert.That(filing.EndDate, Is.EqualTo(DateTime.MinValue));
        });
    }

    [Test]
    public void Apply_WhenStartDateIsAfterEndDate_ShouldReturnFailure()
    {
        // Arrange
        var command = new UpdateFilingCommand
        {
            Id = default,
            StartDate = new DateTime(2025, 5, 1, 0, 0, 0, 0),
            EndDate = new DateTime(2025, 4, 1, 0, 0, 0, 0)
        };

        var filing = new Filing
        {
            EndDate = DateTime.MinValue,
            StartDate = DateTime.MinValue,
            StatusId = FilingStatus.Pending.Id,
        };

        // Act
        var result = command.Apply(filing);

        // Assert
        Assert.That(result, Is.InstanceOf<Failure<Filing>.InvalidState>());
    }

    [Test]
    public void Apply_WhenAmendmentExplanationIsNullOrEmpty_ShouldNotUpdateFiling()
    {
        // Arrange
        var command = new UpdateFilingCommand
        {
            Id = default,
            StartDate = null,
            EndDate = null,
            AmendmentExplanation = null
        };

        var filing = new Filing
        {
            EndDate = DateTime.MinValue,
            StartDate = DateTime.MinValue,
            StatusId = FilingStatus.Pending.Id,
        };

        // Act
        var result = command.Apply(filing);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<Success<Filing>>());
            Assert.That(filing.AmendmentExplanation, Is.Null);
        });
    }

    [Test]
    public void Apply_WhenAmendmentExplanationIsProvided_ShouldUpdateFiling()
    {
        // Arrange
        var command = new UpdateFilingCommand
        {
            Id = default,
            StartDate = null,
            EndDate = null,
            AmendmentExplanation = "Valid reason"
        };

        var filing = new Filing
        {
            EndDate = DateTime.MinValue,
            StartDate = DateTime.MinValue,
            StatusId = FilingStatus.Pending.Id,
        };

        // Act
        var result = command.Apply(filing);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<Success<Filing>>());
            Assert.That(filing.AmendmentExplanation, Is.EqualTo("Valid reason"));
        });
    }
}
