using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;
using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class FirmPaymentTransactionControllerTests
{
    private FirmPaymentTransactionController _controller;
    private IFirmPaymentTransactionCtlSvc _firmPaymentTransactionCtlSvc;
    private IToastService _toastService;
    private IStringLocalizer<SharedResources> _localizer;
    private IFilingsApi _filingsApi;

    [SetUp]
    public void Setup()
    {
        _firmPaymentTransactionCtlSvc = Substitute.For<IFirmPaymentTransactionCtlSvc>();
        _toastService = Substitute.For<IToastService>();
        _localizer = Substitute.For<IStringLocalizer<SharedResources>>();
        _filingsApi = Substitute.For<IFilingsApi>();

        _controller = new FirmPaymentTransactionController(
            _toastService,
            _localizer,
            _firmPaymentTransactionCtlSvc,
            _filingsApi
        );
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    [Test]
    public async Task SelectContact_Get_WithValidModel_ReturnsViewWithModel()
    {
        // Arrange
        string reportType = FilingTypeModel.LobbyistEmployerReport.Name;
        long filingId = 123;
        long filerId = 456;

        var expectedViewModel = new FirmPaymentTransactionViewModel
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId
        };

        _ = _firmPaymentTransactionCtlSvc
            .Get01ViewModel(reportType, filingId, filerId, null, null, Arg.Any<CancellationToken>())
            .Returns(expectedViewModel);

        // Act
        var result = await _controller.SelectContact(reportType, filingId, filerId, null, null);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = (ViewResult)result;

        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("~/Views/Contact/SelectLobbyingFirm.cshtml"));

        var model = viewResult.Model as FirmPaymentTransactionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.ReportType, Is.EqualTo(reportType));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
        });
    }

    [Test]
    public async Task SelectContact_Get_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        string reportType = FilingTypeModel.LobbyistEmployerReport.Name;
        long filingId = 123;
        long filerId = 456;

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.SelectContact(reportType, filingId, filerId, null, null);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SelectContact_Post_WithContinueAction_RedirectsToSelectContact()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };
        string action = "Continue";

        // Act
        var result = await _controller.SelectContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("EnterContact"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(redirectResult.RouteValues["registrationFilingId"], Is.EqualTo(model.RegistrationFilingId));
        });
    }

    [Test]
    public async Task SelectContact_Post_WithCancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456
        };
        string action = "Cancel";

        // Act
        var result = await _controller.SelectContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["viewName"], Is.EqualTo("MadeToLobbyingFirmsSummary"));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    [Test]
    public async Task SelectContact_Post_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456
        };
        string action = "Continue";

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.SelectContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SearchLobbyingFirmsByIdOrName_WithValidParameters_ReturnsExpectedResults()
    {
        // Arrange
        string search = "TestFirm";
        long filerId = 456;

        var expectedJsonResult = new JsonResult(new { Results = "Test Results" });

        _ = _firmPaymentTransactionCtlSvc
            .SearchLobbyingFirmsByIdOrName(search, filerId, Arg.Any<CancellationToken>())
            .Returns(expectedJsonResult);

        // Act
        var result = await _controller.SearchLobbyingFirmsByIdOrName(search, filerId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result, Is.SameAs(expectedJsonResult));

        _ = await _firmPaymentTransactionCtlSvc
            .Received(1)
            .SearchLobbyingFirmsByIdOrName(search, filerId, Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task SearchLobbyingFirmsByIdOrName_WithInvalidModelState_ReturnsEmptyJsonResult()
    {
        // Arrange
        string search = "TestFirm";
        long filerId = 456;

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.SearchLobbyingFirmsByIdOrName(search, filerId, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());

        var jsonResult = result;
        Assert.That(jsonResult, Is.Not.Null);
        Assert.That(jsonResult.Value, Is.InstanceOf<object>());
        Assert.That(jsonResult.Value.ToString(), Is.EqualTo("{ }"));

        _ = await _firmPaymentTransactionCtlSvc
            .DidNotReceive()
            .SearchLobbyingFirmsByIdOrName(Arg.Any<string>(), Arg.Any<long>(), Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task EnterContact_Get_WithValidParameters_ReturnsViewWithModel()
    {
        // Arrange
        string reportType = FilingTypeModel.LobbyistEmployerReport.Name;
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;

        var expectedViewModel = new FirmPaymentTransactionViewModel
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Lobbying Firm"
            }
        };

        _ = _firmPaymentTransactionCtlSvc
            .GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, Arg.Any<CancellationToken>())
            .Returns(expectedViewModel);

        // Act
        var result = await _controller.EnterContact(reportType, filingId, filerId, contactId, registrationFilingId);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;

        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("~/Views/Contact/LobbyingFirmForm.cshtml"));

        var model = viewResult.Model as FirmPaymentTransactionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.ReportType, Is.EqualTo(reportType));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId));
            Assert.That(model.Contact, Is.Not.Null);
            Assert.That(model.Contact.OrganizationName, Is.EqualTo("Test Lobbying Firm"));
        });

        _ = await _firmPaymentTransactionCtlSvc
            .Received(1)
            .GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task EnterContact_Get_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        string reportType = FilingTypeModel.LobbyistEmployerReport.Name;
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.EnterContact(reportType, filingId, filerId, contactId, registrationFilingId);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        _ = await _firmPaymentTransactionCtlSvc
            .DidNotReceive()
            .GetViewModel(Arg.Any<string>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task EnterContact_Post_WithPreviousAction_RedirectsToSelectContact()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };
        string action = "Previous";

        // Act
        var result = await _controller.EnterContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("SelectContact"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    [Test]
    public async Task EnterContact_Post_WithContinueActionAndRegistrationFilingId_RedirectsToEnterAmount()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456,
            RegistrationFilingId = 101112
        };
        string action = "Continue";

        // Act
        var result = await _controller.EnterContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("EnterAmount"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(redirectResult.RouteValues["registrationFilingId"], Is.EqualTo(model.RegistrationFilingId));
        });
    }

    [Test]
    public async Task EnterContact_Post_WithContinueActionAndNoRegistrationFilingId_SavesContactAndRedirects()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Organization",
                Street = "123 Main St",
                City = "Sacramento",
                State = "CA",
                ZipCode = "95814",
                Country = "USA"
            }
        };
        string action = "Continue";

        // Act
        var result = await _controller.EnterContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("EnterAmount"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
        });

        await _firmPaymentTransactionCtlSvc
            .Received(1)
            .SaveFilerContact(
                Arg.Is<FirmPaymentTransactionViewModel>(m => m == model),
                Arg.Any<ModelStateDictionary>());
    }

    [Test]
    public async Task EnterContact_Post_WithContinueActionAndNullFilingOrFiler_ReturnsNotFound()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = null, // Missing required field
            FilerId = 456,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Organization"
            }
        };
        string action = "Continue";

        // Act
        var result = await _controller.EnterContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        await _firmPaymentTransactionCtlSvc
            .DidNotReceive()
            .SaveFilerContact(Arg.Any<FirmPaymentTransactionViewModel>(), Arg.Any<ModelStateDictionary>());
    }

    [Test]
    public async Task EnterContact_Post_WithContinueActionAndSaveContactFailure_ReturnsViewWithModel()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            Contact = new GenericContactViewModel
            {
                OrganizationName = "Test Organization"
            }
        };
        string action = "Continue";

        _ = _firmPaymentTransactionCtlSvc
            .SaveFilerContact(Arg.Any<FirmPaymentTransactionViewModel>(), Arg.Any<ModelStateDictionary>())
            .Returns(Task.CompletedTask)
            .AndDoes(x => x.Arg<ModelStateDictionary>().AddModelError("Contact.OrganizationName", "Required"));

        // Act
        var result = await _controller.EnterContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;

        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.ViewName, Is.EqualTo("~/Views/Contact/LobbyingFirmForm.cshtml"));
            Assert.That(viewResult.Model, Is.SameAs(model));
        });
    }

    [Test]
    public async Task EnterContact_Post_WithCancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456
        };
        string action = "Cancel";

        // Act
        var result = await _controller.EnterContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["viewName"], Is.EqualTo("MadeToLobbyingFirmsSummary"));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    [Test]
    public async Task EnterContact_Post_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = FilingTypeModel.LobbyistEmployerReport.Name,
            FilingId = 123,
            FilerId = 456
        };
        string action = "Continue";

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.EnterContact(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EnterAmount_Get_WithValidParameters_ReturnsViewWithModel()
    {
        // Arrange
        string reportType = "Lobbyist Employer";
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;

        var expectedViewModel = new FirmPaymentTransactionViewModel
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            FeesAndRetainersAmount = 1000M,
            ReimbursementOfExpensesAmount = 500M,
            AdvancesOrOtherPaymentsAmount = 250M
        };

        _ = _firmPaymentTransactionCtlSvc
            .GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, Arg.Any<CancellationToken>())
            .Returns(expectedViewModel);

        // Act
        var result = await _controller.EnterAmount(reportType, filingId, filerId, contactId, registrationFilingId);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;

        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("EnterAmount"));

        var model = viewResult.Model as FirmPaymentTransactionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.ReportType, Is.EqualTo(reportType));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId));
            Assert.That(model.FeesAndRetainersAmount, Is.EqualTo(1000M));
            Assert.That(model.ReimbursementOfExpensesAmount, Is.EqualTo(500M));
            Assert.That(model.AdvancesOrOtherPaymentsAmount, Is.EqualTo(250M));
        });

        _ = await _firmPaymentTransactionCtlSvc
            .Received(1)
            .GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task EnterAmount_Get_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        string reportType = "Lobbyist Employer";
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.EnterAmount(reportType, filingId, filerId, contactId, registrationFilingId);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        _ = await _firmPaymentTransactionCtlSvc
            .DidNotReceive()
            .GetViewModel(Arg.Any<string>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<long?>(), Arg.Any<long?>(), Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task EnterAmount_Post_WithPreviousAction_RedirectsToEnterContact()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = "Lobbyist Employer",
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };
        string action = "Previous";

        // Act
        var result = await _controller.EnterAmount(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("EnterContact"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(redirectResult.RouteValues["registrationFilingId"], Is.EqualTo(model.RegistrationFilingId));
        });
    }

    [Test]
    public async Task EnterAmount_Post_WithSaveAction_SavesAndRedirectsToSummary()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = "Lobbyist Employer",
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            FeesAndRetainersAmount = 1000M,
            ReimbursementOfExpensesAmount = 500M,
            AdvancesOrOtherPaymentsAmount = 250M,
            AdvancesOrOtherPaymentsExplanation = "Test explanation"
        };
        string action = "Save";

        var transactionId = 999L;
        _ = _firmPaymentTransactionCtlSvc
            .SaveTransaction(Arg.Any<FirmPaymentTransactionViewModel>(), Arg.Any<ModelStateDictionary>())
            .Returns(transactionId);

        _ = _localizer[ResourceConstants.CreateTransactionSuccessMessage]
            .Returns(new LocalizedString(ResourceConstants.CreateTransactionSuccessMessage, "Success"));

        // Act
        var result = await _controller.EnterAmount(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["viewName"], Is.EqualTo("MadeToLobbyingFirmsSummary"));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });

        _ = await _firmPaymentTransactionCtlSvc
            .Received(1)
            .SaveTransaction(Arg.Is<FirmPaymentTransactionViewModel>(m => m == model), Arg.Any<ModelStateDictionary>());

        _toastService
            .Received(1)
            .Success(Arg.Any<string>());
    }

    [Test]
    public async Task EnterAmount_Post_WithSaveActionAndValidationFailure_ReturnsViewWithModel()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = "Lobbyist Employer",
            FilingId = 123,
            FilerId = 456,
            ContactId = 789
        };
        string action = "Save";

        var contactViewModel = new GenericContactViewModel { OrganizationName = "Test Firm" };

        _ = _firmPaymentTransactionCtlSvc
            .SaveTransaction(Arg.Any<FirmPaymentTransactionViewModel>(), Arg.Any<ModelStateDictionary>())
            .Returns(Task.FromResult<long?>(null))
            .AndDoes(x => x.Arg<ModelStateDictionary>().AddModelError("FeesAndRetainersAmount", "Required"));

        _ = _firmPaymentTransactionCtlSvc
            .GetContactViewModel(model.ContactId, model.RegistrationFilingId)
            .Returns(contactViewModel);

        // Act
        var result = await _controller.EnterAmount(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;

        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("EnterAmount"));

        var resultModel = viewResult.Model as FirmPaymentTransactionViewModel;
        Assert.That(resultModel, Is.Not.Null);
        Assert.That(resultModel.Contact, Is.SameAs(contactViewModel));

        _ = await _firmPaymentTransactionCtlSvc
            .Received(1)
            .SaveTransaction(Arg.Is<FirmPaymentTransactionViewModel>(m => m == model), Arg.Any<ModelStateDictionary>());

        _ = await _firmPaymentTransactionCtlSvc
            .Received(1)
            .GetContactViewModel(model.ContactId, model.RegistrationFilingId);
    }

    [Test]
    public async Task EnterAmount_Post_WithCancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = "Lobbyist Employer",
            FilingId = 123,
            FilerId = 456
        };
        string action = "Cancel";

        // Act
        var result = await _controller.EnterAmount(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["viewName"], Is.EqualTo("MadeToLobbyingFirmsSummary"));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    [Test]
    public async Task EnterAmount_Post_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = "Lobbyist Employer",
            FilingId = 123,
            FilerId = 456
        };
        string action = "Save";

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.EnterAmount(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        _ = await _firmPaymentTransactionCtlSvc
            .DidNotReceive()
            .SaveTransaction(Arg.Any<FirmPaymentTransactionViewModel>(), Arg.Any<ModelStateDictionary>());
    }

    [Test]
    public async Task EnterAmount_Post_WithMissingRequiredData_ReturnsNotFound()
    {
        // Arrange
        var model = new FirmPaymentTransactionViewModel
        {
            ReportType = "Lobbyist Employer",
            FilingId = null, // Missing required field
            FilerId = 456
        };
        string action = "Save";

        // Act
        var result = await _controller.EnterAmount(model, action);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        _ = await _firmPaymentTransactionCtlSvc
            .DidNotReceive()
            .SaveTransaction(Arg.Any<FirmPaymentTransactionViewModel>(), Arg.Any<ModelStateDictionary>());
    }
}
