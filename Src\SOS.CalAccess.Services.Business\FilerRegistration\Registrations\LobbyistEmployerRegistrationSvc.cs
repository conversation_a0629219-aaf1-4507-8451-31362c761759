// <copyright file="LobbyistEmployerRegistrationSvc.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Globalization;
using System.Net.Http.Headers;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Filers;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using static SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts.IGetAllContacts;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

/// <summary>
/// Service for managing Lobbyist Employer registrations.
/// </summary>
public sealed class LobbyistEmployerRegistrationSvc(
    LobbyistEmployerRegistrationSvcDependencies dependencies,
    IRegistrationModelMapper modelMapper) : ILobbyistEmployerRegistrationSvc
{
    /// <inheritdoc />
    public async Task<LobbyistEmployerResponseDto?> GetLobbyistEmployer(long id)
    {
        var lobbyistEmployer = await dependencies.RegistrationRepository.FindLobbyistEmployerById(id)
            ?? throw new KeyNotFoundException($"Lobbyist Employer registration with ID {id} not found.");

        return new LobbyistEmployerResponseDto(lobbyistEmployer);
    }

    /// <inheritdoc />
    public async Task<LobbyistEmployerResponseDto?> GetLobbyistEmployerByFilerId(long id)
    {
        var lobbyistEmployer = await dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<LobbyistEmployer>(id)
            ?? throw new KeyNotFoundException($"Lobbyist Employer registration with Filer ID {id} not found.");

        return new LobbyistEmployerResponseDto(lobbyistEmployer);
    }

    public async Task<LobbyistEmployerResponseDto?> GetLobbyistEmployerByName(string name)
    {
        var lobbyistEmployer = await dependencies.RegistrationRepository.FindLobbyistEmployerByName(name)
            ?? throw new KeyNotFoundException($"Lobbyist Employer registration with Name {name} not found.");

        return new LobbyistEmployerResponseDto(lobbyistEmployer);
    }

    public async Task<RegistrationResponseDto> CreateLobbyistEmployerRegistrationPage03(LobbyistEmployerGeneralInfoRequest request)
    {
        bool isValid = false;

        var lobbyistEmployerRequest = modelMapper.MapLobbyistEmployerGeneralInfoToModel(request);
        DecisionsLobbyistEmployerGeneralInfo decisionsInput = PopulateDecisionServiceRequest(lobbyistEmployerRequest);
        var validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistEmployerGeneralInfo, List<WorkFlowError>>(DecisionsWorkflow.FRLOBFilingLobbyistEmployerInfo, decisionsInput, request.CheckRequiredFieldsFlag);

        await CheckLobbyistEmployerNameUniqueness(request.EmployerName ?? string.Empty, validationErrors);

        if (validationErrors.Count > 0)
        {
            return new RegistrationResponseDto(null, isValid, validationErrors, null);
        }

        isValid = true;

        var registration = await dependencies.RegistrationRepository.Create(lobbyistEmployerRequest);

        if (registration is not null)
        {
            var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();

            var filerRequest = new Filer
            {
                CurrentRegistrationId = registration.Id,
                FilerStatusId = FilerStatus.Draft.Id,
                FilerTypeId = FilerType.LobbyistEmployer.Id,
                CreatedBy = userId,
                Users = new List<FilerUser>
                {
                    new()
                    {
                        FilerRoleId = FilerRole.LobbyistEmployer_AccountManager.Id,
                        UserId = userId.Value,
                    }
                }
            };

            var filerId = await dependencies.FilerSvc.AddFilerAsync(filerRequest);

            registration.OriginalId = registration.Id;
            registration.FilerId = filerId;
            registration.CreatedBy = userId.GetValueOrDefault();
            await dependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(registration?.Id, isValid, validationErrors, registration?.StatusId, filerId: registration?.FilerId);
    }

    public async Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage03(long id, LobbyistEmployerGeneralInfoRequest request)
    {
        bool isValid = false;

        var existingRegistration = await dependencies.RegistrationRepository.FindLobbyistEmployerById(id);
        if (existingRegistration is not { } existing)
        {
            throw new KeyNotFoundException($"Registration not found. Id={id}");
        }

        var registration = modelMapper.UpdateLobbyistEmployerGeneralInfo(existing, request);
        registration.Id = id;

        DecisionsLobbyistEmployerGeneralInfo decisionsInput = PopulateDecisionServiceRequest(registration);
        var validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistEmployerGeneralInfo, List<WorkFlowError>>(DecisionsWorkflow.FRLOBFilingLobbyistEmployerInfo, decisionsInput, request.CheckRequiredFieldsFlag);

        await CheckLobbyistEmployerNameUniqueness(request.EmployerName ?? string.Empty, validationErrors, existingRegistration.ParentId);

        if (validationErrors?.Count == 0)
        {
            isValid = true;
            await dependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(id, isValid, validationErrors, registration.StatusId);
    }

    public async Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage04(long id, LobbyistEmployerStateAgenciesRequest request)
    {
        bool isValid = false;

        var existingRegistration = await dependencies.RegistrationRepository.FindLobbyistEmployerById(id);
        if (existingRegistration is not { } existing)
        {
            throw new KeyNotFoundException($"Registration not found. Id={id}");
        }

        var registration = modelMapper.UpdateLobbyistEmployerStateAgencies(existing, request);
        registration.Id = id;

        DecisionsLobbyistEmployerStateAgencies decisionsInput = PopulateStateAgenciesDecisionServiceRequest(registration);
        var validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistEmployerStateAgencies, List<WorkFlowError>>(DecisionsWorkflow.FRLOBFilingLobbyistEmployerStateAgenciesToBeInfluenced, decisionsInput, request.CheckRequiredFieldsFlag);

        if (validationErrors?.Count == 0)
        {
            isValid = true;
            await dependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(id, isValid, validationErrors, registration.StatusId);
    }

    public async Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage05(long id, LobbyistEmployerLobbyingInterestsRequest request)
    {
        bool isValid = false;

        var existingRegistration = await dependencies.RegistrationRepository.FindLobbyistEmployerById(id);
        if (existingRegistration is not { } existing)
        {
            throw new KeyNotFoundException($"Registration not found. Id={id}");
        }

        var registration = modelMapper.UpdateLobbyistEmployerLobbyingInterests(existing, request);
        registration.Id = id;

        DecisionsLobbyistEmployerLobbyingInterests decisionsInput = PopulateLobbyingInterestsDecisionServiceRequest(registration);
        var validationErrors = await dependencies.DecisionsSvc.InitiateWorkflow<DecisionsLobbyistEmployerLobbyingInterests, List<WorkFlowError>>(DecisionsWorkflow.FRLOBFilingLobbyistEmployerLobbyingInterestsDescription, decisionsInput, request.CheckRequiredFieldsFlag);

        if (validationErrors?.Count == 0)
        {
            isValid = true;
            if (existing.LobbyingInterestId.HasValue)
            {
                var lobbyingInterest = await dependencies.LobbyingInterestRepository.FindById(existing.LobbyingInterestId.Value);

                if (lobbyingInterest is null)
                {
                    throw new KeyNotFoundException($"LobbyingInterest not found. Id={id}");
                }

                await dependencies.LobbyingInterestRepository.Delete(lobbyingInterest);
            }
            await dependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(id, isValid, validationErrors, registration.StatusId);
    }

    /// <summary>
    /// Cancels a Draft Lobbyist Employer Registration
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task CancelLobbyistEmployerRegistration(long id)
    {
        if (await dependencies.RegistrationRepository.FindLobbyistEmployerById(id) is not LobbyistEmployer registration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            throw new InvalidOperationException($"Cannot cancel a registration that is not in 'Draft' status. Id={id} Status={registration.StatusId}");
        }

        registration.StatusId = RegistrationStatus.Canceled.Id;

        await dependencies.RegistrationRepository.Update(registration);
    }

    /// <summary>
    /// Get inhouse lobbyists of employer registration
    /// </summary>
    /// <param name="employerRegistrationId">Id of Lobbyist employer registration</param>
    /// <returns>Inhouse lobbyists of employer registration</returns>
    public async Task<List<InHouseLobbyistResponseDto>> GetLobbyistRegistrationByEmployerRegistration(long employerRegistrationId)
    {
        var registrations = await dependencies.RegistrationRepository.FindLobbyistRegistrationByEmployer(employerRegistrationId);

        var lobbyists = new List<InHouseLobbyistResponseDto>();

        if (registrations != null)
        {
            lobbyists = registrations.Select(registration => new InHouseLobbyistResponseDto(registration)).ToList();
        }

        return lobbyists;
    }

    /// <summary>
    /// Link lobbying firms to lobbyist employer
    /// </summary>
    /// <param name="lobbyistEmployerId"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<RegistrationResponseDto> LinkLobbyingFirmsToLobbyistEmployer(long id, LobbyistEmployerToLobbyingFirmRequestDto request)
    {
        var registration = await dependencies.RegistrationRepository.FindLobbyistEmployerById(id);

        var userId = await dependencies.AuthorizationSvc.GetInitiatingUserId();

        await dependencies.FilerLinkRepository.LinkLobbyistEmployerRegistrationToLobbyingFirms(registration!.FilerId.GetValueOrDefault(), request.LobbyingFirmFilerIds, userId ?? 0);

        return new RegistrationResponseDto(registration!.Id, true, null, registration!.StatusId, filerId: registration!.FilerId);
    }

    /// <summary>
    /// Get all lobbying firm search dto
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<LobbyingFirmSearchDto>> GetAllLobbyingFirms()
    {
        var lobbyistFirms = await dependencies.RegistrationRepository.GetAllLobbyingFirms();
        return lobbyistFirms.Select(f => new LobbyingFirmSearchDto()
        {
            Id = f.Id,
            Name = f.Name,
            FilerId = f.FilerId ?? 0
        });
    }

    private static DecisionsLobbyistEmployerGeneralInfo PopulateDecisionServiceRequest(LobbyistEmployer lobbyistEmployer)
    {
        var decisionsInput = new DecisionsLobbyistEmployerGeneralInfo
        {
            LobbyingEntityName = lobbyistEmployer.Name,
            BusinessAddress = MapAddress(lobbyistEmployer.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == "Business")),
            MailingAddress = MapAddress(lobbyistEmployer.AddressList?.Addresses.FirstOrDefault(x => x.Purpose == "Mailing")),
            EmailAddress = lobbyistEmployer.Email,
            FaxNumber = PopulatePhoneAndFaxNumbers(lobbyistEmployer, RegistrationConstants.PhoneNumber.TypeFax),
            PhoneNumber = PopulatePhoneAndFaxNumbers(lobbyistEmployer, RegistrationConstants.PhoneNumber.TypeHome),
            LegislativeSessionId = lobbyistEmployer.LegislativeSessionId,
            QualificationDate = lobbyistEmployer.DateQualified ?? DateTime.MinValue,
            IsForLobbyingCoalition = lobbyistEmployer.IsLobbyingCoalition
        };

        return decisionsInput;
    }

    private static DecisionsLobbyistEmployerStateAgencies PopulateStateAgenciesDecisionServiceRequest(LobbyistEmployer lobbyistEmployer)
    {
        var decisionsInput = new DecisionsLobbyistEmployerStateAgencies
        {
            Agencies = [.. lobbyistEmployer.RegistrationAgencies.Select(static a => a.AgencyId.ToString(CultureInfo.InvariantCulture))],
            IsLobbyingStateLegislature = lobbyistEmployer.StateLegislatureLobbying,
        };

        return decisionsInput;
    }

    private static DecisionsLobbyistEmployerLobbyingInterests PopulateLobbyingInterestsDecisionServiceRequest(LobbyistEmployer lobbyistEmployer)
    {
        var decisionsInput = new DecisionsLobbyistEmployerLobbyingInterests
        {
            LobbyingInterestsDescription = lobbyistEmployer?.LobbyingInterest?.Name
        };

        return decisionsInput;
    }

    private static DecisionsAddress? MapAddress(Address? address)
    {
        if (address == null)
        {
            return null;
        }

        return new DecisionsAddress
        {
            Street = address.Street,
            Street2 = address.Street2,
            City = address.City,
            Country = address.Country,
            Purpose = address.Purpose,
            State = address.State,
            Type = address.Type,
            Zip = address.Zip
        };
    }

    private static string? PopulatePhoneAndFaxNumbers(LobbyistEmployer lobbyistEmployer, string type)
    {
        var phoneNumber = lobbyistEmployer.PhoneNumberList?.PhoneNumbers?.FirstOrDefault(x => x.Type == type);

        return !string.IsNullOrWhiteSpace(phoneNumber?.Number) ? $"{phoneNumber.CountryCode}{phoneNumber.Number}" : string.Empty;
    }

    private async Task CheckLobbyistEmployerNameUniqueness(string name, List<WorkFlowError> validationErrors, long? parentId = null)
    {
        var isUnique = await dependencies.RegistrationRepository.IsLobbyistEmployerNameUnique(name, parentId);
        if (string.IsNullOrWhiteSpace(name) || !isUnique)
        {
            validationErrors.Add(CreateUniqueLobbyistEmployerNameValidationError());
        }
    }

    private static WorkFlowError CreateUniqueLobbyistEmployerNameValidationError()
    {
        return new WorkFlowError("Name", RegistrationConstants.ValidationError.ErrorCodeGlobal0002, RegistrationConstants.ValidationError.ErrorTypeValidation, $"Lobbyist employer names must be unique. There is an active lobbyist employer with this name.");
    }

    public async Task<IEnumerable<LobbyistSearchResultDto>> SearchLobbyistByIdOrName(string q)
    {
        var filers = await dependencies.RegistrationRepository.FindLobbyistByIdOrName(q);

        List<LobbyistSearchResultDto> list = new();
        foreach (Filer filer in filers)
        {
            LobbyistSearchResultDto item = new()
            {
                Id = filer.Id,
                Name = filer.CurrentRegistration!.Name,
                Email = filer.CurrentRegistration.Email,
                IsDisabled = filer.CurrentRegistration.StatusId == RegistrationStatus.Accepted.Id && filer.CurrentRegistration.TerminatedAt == null,

            };
            list.Add(item);
        }
        return list;
    }

    public async Task<bool> UnlinkLobbyistFromEmployer(long employerRegistrationId, long lobbyistRegistrationId)
    {
        return await dependencies.FilerLinkRepository.UnlinkLobbyistFromEmployer(employerRegistrationId, lobbyistRegistrationId);
    }
}
