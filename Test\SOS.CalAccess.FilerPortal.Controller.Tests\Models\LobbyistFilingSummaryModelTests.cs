using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Filings;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
public class LobbyistFilingSummaryModelTests
{
    [Test]
    public void DefaultConstructor_ShouldInitializeCollections()
    {
        // Act
        var model = new LobbyingFilingSummaryViewModel();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Transactions, Is.Not.Null);
            Assert.That(model.ActivityExpenses, Is.Not.Null);
            Assert.That(model.CampaignContributions, Is.Not.Null);
            Assert.That(model.Transactions, Is.Empty);
            Assert.That(model.ActivityExpenses, Is.Empty);
            Assert.That(model.CampaignContributions, Is.Empty);
        });
    }

    [Test]
    public void Constructor_WithValidFilingItem_ShouldSetProperties()
    {
        // Arrange
        var filing = new FilingItemResponse(
             amendmentExplanation: "AmendmentExplanation",
             endDate: new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
             filerId: 456,
             filerName: "Filer Name",
             filingStatus: "active",
             filingType: "ActivityExpense",
             id: 13,
             parentId: 13,
             startDate: new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
             status: 0,
             submittedDate: null,
             version: 1,
             legislativeSessionId: 1,
             totalPaymentsPucActivity: 1,
             filingTypeId: 1,
             filingPeriodId: 1)
        {
            Id = 13,
            FilerId = 456
        };


        // Act
        var model = new LobbyingFilingSummaryViewModel(filing);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(filing.Id));
            Assert.That(model.FilerId, Is.EqualTo(filing.FilerId));
            Assert.That(model.Transactions, Is.Not.Null);
            Assert.That(model.Transactions, Is.Empty);
        });
    }

    [Test]
    public void Constructor_WithNullFiling_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        _ = Assert.Throws<ArgumentNullException>(static () => new LobbyingFilingSummaryViewModel(null));
    }
}
