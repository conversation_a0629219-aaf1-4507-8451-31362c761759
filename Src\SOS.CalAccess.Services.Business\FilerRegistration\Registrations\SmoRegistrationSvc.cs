using System.Data;
using System.Globalization;
using Newtonsoft.Json;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Mapping;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

public class SmoRegistrationSvc(
    FilingSharedServicesDependencies servicesDependencies,
    FilingSharedRepositoriesDependencies repositoriesDependencies,
    IRegistrationModelMapper modelMapper) : ISmoRegistrationSvc
{
    /// <inheritdoc />
    public async Task<RegistrationResponseDto> CreateSmoRegistrationPage03(SmoContactRequest request)
    {
        bool isValid = false;

        var slateMailerOrganizationRequest = modelMapper.MapSmoContactRequestToModel(request);
        DecisionsSmoContact decisionsInput = PopulateDecisionServiceRequest(slateMailerOrganizationRequest);
        var validationErrors = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoContact, List<WorkFlowError>>(DecisionsWorkflow.SmoContactRuleSet, decisionsInput, request.CheckRequiredFieldsFlag);

        //Check SMO name is unique or not
        await CheckSmoUniquenessName(request.Name ?? string.Empty, validationErrors);

        if (validationErrors.Count > 0)
        {
            return new RegistrationResponseDto(null, false, validationErrors, null);
        }

        isValid = true;

        // Create SMO registration
        var registration = await repositoriesDependencies.RegistrationRepository.Create(slateMailerOrganizationRequest);

        // If registration created return
        // Create a filer & filer user
        if (registration is not null)
        {
            // Get the current user
            var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();

            var filerRequest = new Filer
            {
                CurrentRegistrationId = registration.Id,
                FilerStatusId = FilerStatus.Draft.Id,
                FilerTypeId = FilerType.SlateMailerOrg.Id,
                CreatedBy = userId,
                Users = new List<FilerUser>
                {
                    new()
                    {
                        FilerRoleId = FilerRole.SlateMailerOrg_AccountManager.Id,
                        UserId = userId.Value,
                    }
                }
            };

            var filerId = await servicesDependencies.FilerSvc.AddFilerAsync(filerRequest);

            // Update SMO registration with filer ID and OriginalId
            registration.OriginalId = registration.Id;
            registration.FilerId = filerId;
            registration.CreatedBy = userId.GetValueOrDefault();
            await repositoriesDependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(registration?.Id, isValid, validationErrors, registration?.StatusId, filerId: registration?.FilerId);
    }

    public async Task<RegistrationResponseDto> UpdateSmoRegistrationPage03(long id, SmoContactRequest request)
    {
        bool isValid = false;
        var validationErrors = new List<WorkFlowError>();

        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id);
        if (existingRegistration is not { } existing)
        {
            throw new KeyNotFoundException($"Registration not found. Id={id}");
        }

        var registration = modelMapper.UpdateSlateMailerOrganization(existing, request);
        registration.Id = id;

        DecisionsSmoContact decisionsInput = PopulateDecisionServiceRequest(registration);
        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoContact, List<WorkFlowError>>(DecisionsWorkflow.SmoContactRuleSet, decisionsInput, request.CheckRequiredFieldsFlag);
        validationErrors.AddRange(decisionResponse);

        //Check SMO name is unique or not
        await CheckSmoUniquenessName(request.Name ?? string.Empty, validationErrors, existingRegistration.ParentId);

        if (validationErrors.Count == 0)
        {
            isValid = true;
            await repositoriesDependencies.RegistrationRepository.Update(registration);
        }

        return new RegistrationResponseDto(id, isValid, validationErrors, registration.StatusId);
    }

    /// <summary>
    /// Gets the role name from id
    /// </summary>
    /// <param name="role">Role Id</param>
    /// <returns></returns>
    private static string GetRoleNameFromId(long roleId)
    {
        if (ContactTitleRoleMapping.RoleIdToName.TryGetValue(roleId, out var role))
        {
            return role;
        }

        return string.Empty;
    }
    /// <summary>
    /// Gets all registration contacts, Treasurer/Officers/Authorized Users
    /// </summary>
    /// <param name="id">Smo Registration Filing Id</param>
    /// <returns></returns>
    private async Task<IEnumerable<SmoRegistrationContactDto>> GetSmoAllRegistrationContacts(long id)
    {
        // Find existing registration
        var enumerable = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(id);

        return enumerable.Select(x => SmoRegistrationContactDto.MapToDto(x, servicesDependencies.DateTimeSvc));
    }

    /// <summary>
    /// Returns the Treasurer
    /// </summary>
    /// <param name="id">Smo Registration Filing Id</param>
    /// <returns></returns>
    public async Task<SmoRegistrationContactDto?> GetSmoTreasurerPage02(long id)
    {
        // Find existing registration
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id) ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        var enumerable = await GetSmoAllRegistrationContacts(id);
        List<SmoRegistrationContactDto> contacts = [.. enumerable];
        SmoRegistrationContactDto treasurer = contacts.FirstOrDefault(c => c.RoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            ?? new SmoRegistrationContactDto { };

        // Map if the current/filer user is a Treasurer
        if (treasurer.Id is not null)
        {
            // Get Filer User
            var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
            var filerUser = await servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(existingRegistration.FilerId.GetValueOrDefault(), userId.GetValueOrDefault());
            treasurer.IsUserTreasurer = filerUser?.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id;
        }

        return treasurer;
    }

    /// <summary>
    /// Returns a list of RegistrationContacts attached to this filing.
    /// </summary>
    /// <param name="id">Registration Filing Id</param>
    /// <returns></returns>
    public async Task<IEnumerable<SmoRegistrationContactDto>> GetSmoRegistrationContactsPage04(long id)
    {
        //Find existing registration
        var enumerable = await GetSmoAllRegistrationContacts(id);
        List<SmoRegistrationContactDto> contacts = [.. enumerable];
        var officers = contacts
            .Where(c => c.RoleId != FilerRole.SlateMailerOrg_AccountManager.Id)
            .OrderBy(c => c.Id)
            .ToList();
        return officers;
    }

    /// <inheritdoc/>
    public async Task<RegistrationResponseDto> PostSmoRegistrationContactsPage05(long id, SmoRegistrationContactDto request, bool isCreateOrUpdateAuthorizer = false)
    {
        RegistrationRegistrationContact? contact = null;
        bool isValid = false;
        var validationMessages = new List<WorkFlowError>();

        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        var contacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(id);

        if (request.Title == FilerRole.SlateMailerOrg_AssistantTreasurer.Name)
        {
            var isAssistantTreasurerExisted = contacts
                .Any(c => c.Role == FilerRole.SlateMailerOrg_AssistantTreasurer.Name && c.RegistrationId == id && c.Id != request.Id);

            if (isAssistantTreasurerExisted)
            {
                validationMessages.Add(new WorkFlowError("Title", RegistrationConstants.ValidationError.ErrorCodeGlobal0002, RegistrationConstants.ValidationError.ErrorTypeValidation, $"An assistant treasurer already exists. Please select another title."));
            }
        }

        // Check if this is replacing the Treasurer
        // If yes, change the Current Treasurer to inactive contact
        var isReplacingExistingTreasurer = ReplaceExistingTreasurer(request, contacts);

        contact = contacts
            .Where(contact => contact.RegistrationId == id && contact.Id == request.Id)
            .Select(contact => contact)
            .FirstOrDefault();

        RegistrationContact registrationContact;
        string role = GetRoleNameFromId(request.RoleId);
        if (contact == null)
        {
            registrationContact = CreateRegistrationContact(request);
            contact = new()
            {
                Role = role,
                Title = request.Title ?? string.Empty,
                RegistrationId = id,
                RegistrationContact = registrationContact,
                CreatedBy = request.UserId ?? 0,
                ModifiedBy = request.UserId ?? 0,
                CanAuthorizeSlateMailerContents = request.CanAuthorize == false ? null : true,

                // Default required fields
                TenPercentOrGreater = false,
                CapitalContributionOver10K = false,
                CumulativeCapitalContributions = 0,
                PercentOfOwnership = 100,
            };
        }
        else
        {
            ArgumentNullException.ThrowIfNull(contact.RegistrationContact);
            contact.CanAuthorizeSlateMailerContents = request.CanAuthorize == false ? null : true;
            contact.CreatedBy = request.UserId ?? 0;
            contact.ModifiedBy = request.UserId ?? 0;
            contact.Title = request.Title ?? string.Empty;
            contact.Role = role;

            registrationContact = UpdateRegistrationContact(request, contact.RegistrationContact);
        }

        // Do decisions on either the whole registration or just the contact (newContact / existingContact)
        DecisionsSmoRegistrationContact decisionsSmoOfficerInput = PopulateDecisionServiceRequestOfficer(registrationContact, contact.Title);
        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(GetDecisionWorkflowByRole(contact.Role, isCreateOrUpdateAuthorizer), decisionsSmoOfficerInput, request.CheckRequiredFieldsFlag);

        validationMessages.AddRange(decisionResponse);

        if (validationMessages.Count == 0)
        {
            isValid = true;
            _ = await repositoriesDependencies.RegistrationRegistrationContactRepository.Update(contact);
            await ModifyFilerUserAsync(existingRegistration.FilerId.GetValueOrDefault(), request.UserId, request.RoleId, isReplacingExistingTreasurer);
        }

        // Return standard response object
        return new RegistrationResponseDto(id, isValid, validationMessages, RegistrationStatus.Draft.Id);
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> PostSmoRegistrationTransferTreasurer(long id, SmoTreasurerTransferDto request)
    {
        var isValid = false;
        List<WorkFlowError> errors = new();

        //FindById
        if (await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id) is not SlateMailerOrganization existingSmoRegistration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        var contacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(id) ?? new List<RegistrationRegistrationContact>();
        // Find the existing Asst. Treasurer
        var existingAsstTreasurer = contacts.FirstOrDefault(x => x.Role == FilerRole.SlateMailerOrg_AssistantTreasurer.Name);

        // If the current Asst. Treasurer change to Treasurer, it allow other officer to transfer to Asst. Treasurer role.
        // Or if the registration haven't had an Asst. Treasurer yet.
        var isAllowedTransferToAsstTreasurer = existingAsstTreasurer is null || request.NewTreasurerId == existingAsstTreasurer.Id;

        // Treasurer is a special case, it have no selectable title
        RegistrationRegistrationContact contact = contacts
            .Where(contact => string.Equals(contact.Role, FilerRole.SlateMailerOrg_Treasurer.Name, StringComparison.Ordinal))
            .Select(contact => contact)
            .FirstOrDefault() ?? throw new ArgumentException($"Treasurer not found");

        RegistrationRegistrationContact newTreasurer = contacts
            .Where(contact => contact.Id == request.NewTreasurerId)
            .Select(contact => contact)
            .FirstOrDefault() ?? throw new ArgumentException($"Officer not found");

        if (contact.Id == newTreasurer.Id)
        {
            throw new ArgumentException($"Transfer to the same person not allowed.");
        }

        if (!isAllowedTransferToAsstTreasurer && request.PreviousTreasurerTitle == FilerRole.SlateMailerOrg_AssistantTreasurer.Name)
        {
            errors.Add(new WorkFlowError("Title", RegistrationConstants.ValidationError.ErrorCodeGlobal0002, RegistrationConstants.ValidationError.ErrorTypeValidation, $"An assistant treasurer already exists. Please select another title."));
            return new RegistrationResponseDto(id, false, errors);
        }

        // Update the FilerRole of old Treasurer and new Treasurer
        var oldTreasurerUserId = contact.RegistrationContact?.UserId;
        var newTreasurerUserId = newTreasurer.RegistrationContact?.UserId;
        await UpdateTransferTreasurerFilerRole(existingSmoRegistration.FilerId.GetValueOrDefault(), oldTreasurerUserId.GetValueOrDefault(), newTreasurerUserId.GetValueOrDefault(), request);

        // Update RegistrationRegistrationContact
        if (request.PreviousTreasurerKeep)
        {
            contact.Role = string.Equals(request.PreviousTreasurerTitle, FilerRole.SlateMailerOrg_AssistantTreasurer.Name, StringComparison.Ordinal) ? FilerRole.SlateMailerOrg_AssistantTreasurer.Name : FilerRole.SlateMailerOrg_Officer.Name;
            contact.Title = request.PreviousTreasurerTitle ?? string.Empty;
            _ = await repositoriesDependencies.RegistrationRegistrationContactRepository.Update(contact);
        }
        else
        {
            _ = await repositoriesDependencies.RegistrationRegistrationContactRepository.UpdateProperty(contact, x => x.Active, false);
        }

        newTreasurer.Role = FilerRole.SlateMailerOrg_Treasurer.Name;
        newTreasurer.Title = FilerRole.SlateMailerOrg_Treasurer.Name;
        _ = await repositoriesDependencies.RegistrationRegistrationContactRepository.Update(newTreasurer);
        isValid = true;
        return new RegistrationResponseDto(id, isValid, errors);
    }

    /// <summary>
    /// Removes the Officer from the List
    /// </summary>
    /// <param name="registrationId"></param>
    /// <param name="contactId"></param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    /// <exception cref="ArgumentException"></exception>
    public async Task<RegistrationResponseDto> DeleteSmoRegistrationContactsPage06(long registrationId, long contactId)
    {
        if (await repositoriesDependencies.RegistrationRepository.GetSmoRegistrationRegistrationContactById(registrationId, contactId) is not RegistrationRegistrationContact contact)
        {
            throw new KeyNotFoundException($"Registration Contact not Found Id={contactId}");
        }
        if (FilerRole.SlateMailerOrg_Treasurer.Name.Equals(contact.Role, StringComparison.Ordinal))
        {
            throw new ArgumentException($"Cannot delete Officer");
        }
        List<WorkFlowError> decisionResponse = new();
        bool isValid = true;
        _ = await repositoriesDependencies.RegistrationRegistrationContactRepository.UpdateProperty(contact, x => x.Active, false);

        // Update the filer role of the deleted contact to authorized user
        var contactName = GetContactUserName(contact.RegistrationContact);
        var filerUsers = await servicesDependencies.FilerSvc.GetFilerUsersAsync(contact.Registration?.FilerId ?? default);
        var user = (await servicesDependencies.UserMaintenanceSvc.GetListUsersByUserNameAsync(new List<string> { $"{contactName}" })).FirstOrDefault();
        if (filerUsers is not null && filerUsers.Count != 0)
        {
            var matchedFilerUser = filerUsers.FirstOrDefault(x => x.UserId == user?.Id);

            if (matchedFilerUser != null)
            {
                await servicesDependencies.FilerSvc.UpdateFilerUserRoleAsync(matchedFilerUser.Id, FilerRole.SlateMailerOrg_AccountManager.Id);
            }
        }

        return new RegistrationResponseDto(registrationId, isValid, decisionResponse, RegistrationStatus.Draft.Id);
    }

    /// <summary>
    /// Gets SMO Authorizer
    /// </summary>
    /// <param name="id">Smo Registration Filing Id</param>
    /// <returns></returns>
    public async Task<IEnumerable<SmoRegistrationContactDto>> GetSmoAuthorizerPage04(long id)
    {
        //Find existing registration
        var enumerable = await GetSmoAllRegistrationContacts(id);
        List<SmoRegistrationContactDto> contacts = [.. enumerable];
        var authorizers = contacts
            .Where(c => c.CanAuthorize == true)
            .OrderBy(c => c.Id)
            .ToList();
        return authorizers;
    }

    /// <summary>
    /// Remove Authorized Users.  If officers/treasurer just change 
    /// </summary>
    /// <param name="registrationId"></param>
    /// <param name="contactId"></param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    public async Task<RegistrationResponseDto> DeleteSmoAuthorizerPage04(long registrationId, long contactId)
    {
        if (await repositoriesDependencies.RegistrationRepository.GetSmoRegistrationRegistrationContactById(registrationId, contactId) is not RegistrationRegistrationContact contact)
        {
            throw new KeyNotFoundException($"Registration Contact not Found Id={contactId}");
        }

        List<WorkFlowError> decisionResponse = new();
        bool isValid = true;

        if (FilerRole.SlateMailerOrg_AccountManager.Name.Equals(contact.Role, StringComparison.Ordinal))
        {
            // Authorized Users Can Remove
            _ = await repositoriesDependencies.RegistrationRegistrationContactRepository.UpdateProperty(contact, x => x.Active, false);
        }
        else
        {
            // Officers
            contact.CanAuthorizeSlateMailerContents = false;
            _ = await repositoriesDependencies.RegistrationRegistrationContactRepository.Update(contact);
        }
        return new RegistrationResponseDto(registrationId, isValid, decisionResponse, RegistrationStatus.Canceled.Id);
    }

    public async Task<RegistrationResponseDto> UpdateSmoRegistration(long id, SlateMailerOrganizationRequest request)
    {
        bool isValid = false;

        //FindbyId
        if (await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id) is not SlateMailerOrganization existingSmoRegistration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        // Update existing registration with new values
        existingSmoRegistration.Id = id;
        existingSmoRegistration.ActivityLevel = request.ActivityLevel ?? "";
        existingSmoRegistration.QualifiedCommittee = request.IsQualifiedCommittee.GetValueOrDefault();
        existingSmoRegistration.DateQualified = request.DateQualified;
        existingSmoRegistration.CampaignCommittee = request.IsCampaignCommittee.GetValueOrDefault();

        // Call Decisions
        var committeeId = request.CommitteeId.GetValueOrDefault() != 0 ? request.CommitteeId?.ToString(CultureInfo.InvariantCulture) : null;
        var decisionsInput = new DecisionsSmoRegistrationDetails
        {
            OrganizationLevelOfActivity = request.ActivityLevel ?? "",
            IsOrganizationQualified = request.IsQualifiedCommittee.GetValueOrDefault(),
            DateQualifiedAsSMO = request.DateQualified,
            IsOrganizationCampaignCommittee = request.IsCampaignCommittee.GetValueOrDefault(),
            CommitteeIDOrName = request.IsCampaignCommittee.GetValueOrDefault() ? committeeId : "",
        };
        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoRegistrationDetails, List<WorkFlowError>>(DecisionsWorkflow.SmoRegistrationsDetailsRuleSet, decisionsInput, request.CheckRequiredFieldsFlag);
        if (decisionResponse.Count != 0)
        {
            return new RegistrationResponseDto(existingSmoRegistration.Id, false, decisionResponse);
        }

        // Update registration
        await repositoriesDependencies.RegistrationRepository.Update(existingSmoRegistration);
        isValid = true;

        // Handle Recipient Committee link to filer
        if (existingSmoRegistration.Filer is not null)
        {
            var linkedCommittee = existingSmoRegistration.Filer.FilerLinks?.FirstOrDefault(x => x!.FilerLinkTypeId == FilerLinkType.RecipientCommittee.Id);
            await LinkRecipientCommitteeToFilerAsync(existingSmoRegistration.FilerId!.Value, request.CommitteeId, linkedCommittee);
        }

        return new RegistrationResponseDto(existingSmoRegistration.Id, isValid, decisionResponse, existingSmoRegistration.StatusId);
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationResponseDto?> GetRegistrationFilingById(long id)
    {
        var registrationFiling = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id) ?? throw new KeyNotFoundException($"Registration with ID {id} not found.");

        return new SmoRegistrationResponseDto(registrationFiling);
    }

    private static DecisionsSmoContact PopulateDecisionServiceRequest(SlateMailerOrganization slateMailerOrganization)
    {
        var decisionsSmoContactInput = new DecisionsSmoContact
        {
            Name = slateMailerOrganization.Name,
            Email = slateMailerOrganization.Email,
            FaxNumber = PopulatePhoneAndFaxNumbers(slateMailerOrganization, RegistrationConstants.PhoneNumber.TypeFax),
            PhoneNumber = PopulatePhoneAndFaxNumbers(slateMailerOrganization, RegistrationConstants.PhoneNumber.TypeHome),
            OrganizationAddress = slateMailerOrganization.AddressList != null
                              ? MapAddress(slateMailerOrganization.AddressList, RegistrationConstants.Address.PurposeOrganization, slateMailerOrganization.County ?? string.Empty)
                              : null,
            MailingAddress = slateMailerOrganization.AddressList != null
                              ? MapAddress(slateMailerOrganization.AddressList, RegistrationConstants.Address.PurposeMailing)
                              : null
        };

        return decisionsSmoContactInput;
    }

    private static DecisionsAddress? MapAddress(AddressList addressList, string purpose, string county = "")
    {
        if (addressList?.Addresses.Count == 0)
        {
            return null;
        }

        var address = addressList!.Addresses.FirstOrDefault(x => x.Purpose == purpose);
        if (address == null)
        {
            return null;
        }

        return new DecisionsAddress
        {
            Street = address.Street ?? string.Empty,
            Street2 = address.Street2 ?? string.Empty,
            City = address.City ?? string.Empty,
            Country = address.Country ?? string.Empty,
            Purpose = address.Purpose ?? string.Empty,
            State = address.State ?? string.Empty,
            Type = address.Type ?? string.Empty,
            Zip = address.Zip,
            County = county
        };
    }

    private static string PopulatePhoneAndFaxNumbers(SlateMailerOrganization slateMailerOrganization, string type)
    {
        var phoneNumber = slateMailerOrganization.PhoneNumberList?.PhoneNumbers?.FirstOrDefault(x => x.Type == type);

        return !string.IsNullOrWhiteSpace(phoneNumber?.Number) ? $"{phoneNumber.CountryCode}{phoneNumber.Number}" : string.Empty;
    }

    /// <summary>
    /// Update the Existing Registration Contact from the SmoRegistrationContact DTO
    /// </summary>
    /// <param name="request">Contact information to update</param>
    /// <param name="existing">Previous contact information</param>
    /// <returns></returns>
    private RegistrationContact UpdateRegistrationContact(SmoRegistrationContactDto request, RegistrationContact existing)
    {
        existing.RegistrationContactTypeId = RegistrationContactType.Individual.Id;
        existing.FirstName = request.FirstName ?? string.Empty;
        existing.MiddleName = request.MiddleName ?? string.Empty;
        existing.LastName = request.LastName ?? string.Empty;
        existing.Email = request.Email ?? string.Empty;
        existing.ModifiedBy = request.UserId ?? 0;

        PhoneNumberList phoneList = existing.PhoneNumberList ?? new PhoneNumberList();
        existing.PhoneNumberList = phoneList;
        List<PhoneNumber> phoneNumbers = phoneList.PhoneNumbers;

        // If no Phone Number given remove the current Phone Number
        if (request.PhoneNumber == null)
        {
            if (phoneNumbers.Count > 0)
            {
                phoneNumbers.Clear();
            }
        }
        else
        {
            // Should have only 1
            if (phoneNumbers.Count == 0)
            {
                PhoneNumber phoneNumber = request.PhoneNumber.CreateModel(servicesDependencies.ReferenceDataSvc);
                phoneList.PhoneNumbers.Add(phoneNumber);
            }
            else
            {
                UpdatePhoneNumber(phoneNumbers, request.PhoneNumber);
            }
        }

        AddressList addressList = existing.AddressList ?? new AddressList();
        existing.AddressList = addressList;
        List<Address> addresses = addressList.Addresses;

        // If no Address given remove the current address
        if (request.Address == null)
        {
            if (addresses.Count > 0)
            {
                addresses.Clear();
            }
        }
        else
        {
            // Should have only 1
            if (addresses.Count == 0)
            {
                Address address = CreateAddress(request.Address);
                addressList.Addresses.Add(address);
            }
            else
            {
                Address address = addresses[0];
                _ = UpdateAddress(address, request.Address);
            }
        }

        return existing;
    }

    /// <summary>
    /// Create a Registration Contact Object from the Registration Contact DTO
    /// </summary>
    /// <param name="request">Officer contact information</param>
    /// <returns></returns>
    private RegistrationContact CreateRegistrationContact(SmoRegistrationContactDto request)
    {
        ArgumentNullException.ThrowIfNull(request);
        RegistrationContact registrationContact = new()
        {
            RegistrationContactTypeId = 1,
            FirstName = request.FirstName ?? string.Empty,
            MiddleName = request.MiddleName ?? string.Empty,
            LastName = request.LastName ?? string.Empty,
            Email = request.Email ?? string.Empty,
            PhoneNumberList = (request.PhoneNumber != null) ? CreatePhoneNumberList(request.PhoneNumber) : null,
            AddressList = (request.Address != null) ? CreateAddressList(request.Address) : null,
            UserId = request.UserId,
        };
        return registrationContact;
    }

    /// <summary>
    /// Gets the SMO Treasurer and Officer List
    /// </summary>
    /// <param name="id">Registration Id</param>
    /// <returns>List of Treasurer and Officers</returns>
    public async Task<IEnumerable<SmoOfficerGridDto>> GetSmoOfficers(long id)
    {
        //Find existing registration
        if (await repositoriesDependencies.RegistrationRepository.FindById(id) is null)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        var officers = new List<SmoOfficerGridDto>();

        var registrationRegistrationContacts = await repositoriesDependencies.RegistrationRegistrationContactRepository.GetRegistrationContacts(id);
        var orderedContacts = registrationRegistrationContacts
            .OrderByDescending(x => x.Role == FilerRole.SlateMailerOrg_Treasurer.Name)
            .ThenBy(x => x.Id);

        foreach (var item in orderedContacts)
        {
            var contact = item.RegistrationContact!;

            long phoneListId = contact.PhoneNumberListId;
            PhoneNumberList? phoneList = await repositoriesDependencies.RegistrationRepository.GetPhoneNumberListById(phoneListId);

            contact.PhoneNumberList = phoneList;
            PhoneNumber? phoneNumber = phoneList?.PhoneNumbers.FirstOrDefault();

            var startDate = item.Registration?.StatusId == RegistrationStatus.Accepted.Id ? item.Registration.SubmittedAt!.Value.ToLocalTime() : DateTime.Today;

            officers.Add(
                new()
                {
                    Id = item.Id,
                    ContactId = item.Id,
                    CanAuthorize = item.CanAuthorizeSlateMailerContents,
                    OfficerName = string.IsNullOrEmpty(contact.MiddleName) ? $"{contact.FirstName} {contact.LastName}" : $"{contact.FirstName} {contact.MiddleName} {contact.LastName}",
                    Email = contact.Email,
                    StartDate = startDate,
                    Title = item.Title,
                    Role = item.Role,
                    PhoneNumber = phoneNumber != null ? new(phoneNumber) : default,
                });
        }

        // Return standard response object
        return officers;
    }

    /// <summary>
    /// Gets the SMO Officer details
    /// </summary>
    /// <param name="registrationId">Registration Id</param>
    /// <param name="contactId">Contact Id (RegistrationRegistrationContact)</param>
    /// <returns>Officer Details</returns>
    public async Task<SmoRegistrationContactDto> GetSmoOfficer(long registrationId, long contactId)
    {
        if (await repositoriesDependencies.RegistrationRegistrationContactRepository.GetRegistrationContact(registrationId, contactId) is not RegistrationRegistrationContact registrationContact)
        {
            throw new KeyNotFoundException($"Officer not Found.");
        }

        var phoneListId = registrationContact.RegistrationContact?.PhoneNumberListId;
        var phoneList = await repositoriesDependencies.RegistrationRepository.GetPhoneNumberListById(phoneListId ?? 0);
        var phoneNumber = phoneList?.PhoneNumbers.FirstOrDefault();

        var addressListId = registrationContact.RegistrationContact?.AddressListId;
        var addressList = await repositoriesDependencies.RegistrationRepository.GetAddressListById(addressListId ?? 0);
        var address = addressList?.Addresses.FirstOrDefault();

        var startDate = registrationContact.Registration?.StatusId == RegistrationStatus.Accepted.Id ? registrationContact.Registration.ApprovedAt!.Value : DateTime.Today;

        var officerContact = new SmoRegistrationContactDto
        {
            Id = registrationContact.Id,
            CanAuthorize = registrationContact.CanAuthorizeSlateMailerContents,
            FirstName = registrationContact.RegistrationContact?.FirstName,
            MiddleName = registrationContact.RegistrationContact?.MiddleName,
            LastName = registrationContact.RegistrationContact?.LastName,
            Email = registrationContact.RegistrationContact?.Email,
            StartDate = startDate,
            Title = registrationContact.Title,
            Role = registrationContact.Role,
            PhoneNumber = phoneNumber != null ? new(phoneNumber) : default,
            Address = address != null ? new AddressDto
            {
                Purpose = address.Purpose,
                Type = address.Type,
                Country = address.Country,
                Street = address.Street,
                Street2 = address.Street2,
                City = address.City,
                State = address.State,
                Zip = address.Zip,
            } : default,
            RegistrationId = registrationContact.RegistrationId,
            UserId = registrationContact.RegistrationContact?.UserId,
        };

        // Return standard response object
        return officerContact;
    }

    /// <inheritdoc />
    public async Task DeleteIndividualAuthorizer(long registrationId, long contactId)
    {
        if (await repositoriesDependencies.RegistrationRepository.GetSmoRegistrationRegistrationContactById(registrationId, contactId) is not RegistrationRegistrationContact contact)
        {
            throw new KeyNotFoundException($"Registration Contact not Found Id={contactId}");
        }

        // Individual Authorizer is hidden when AuthorizeSMContent is false
        contact.CanAuthorizeSlateMailerContents = false;

        await repositoriesDependencies.RegistrationRegistrationContactRepository.Update(contact);
    }

    /// <summary>
    /// Cancels a Draft SMO Registration
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    /// <exception cref="InvalidOperationException"></exception>
    public async Task CancelSmoRegistration(long id)
    {
        if (await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id) is not SlateMailerOrganization registration)
        {
            throw new KeyNotFoundException($"Registration not Found Id={id}");
        }

        if (registration.StatusId != RegistrationStatus.Draft.Id)
        {
            throw new InvalidOperationException($"Cannot cancel a registration that is not in 'Draft' status. Id={id} Status={registration.StatusId}");
        }

        registration.StatusId = RegistrationStatus.Canceled.Id;

        await repositoriesDependencies.RegistrationRepository.Update(registration);

        // Cancel all linkage requests associated with this registration
        await repositoriesDependencies.LinkageRequestRepository.CancelContactLinkageRequestsForRegistration(registration.Id);

        // Remove all linkages created by this registration (added by form flow or linkage requests that were accepted)
        var filerUsers = await DeleteLinkagesCreatedByRegistration(registration.Id, registration.ParentId);

        // Call decisions and notifications(SendToUser-59,SendToFiler-60) only for deleted FilerUsers.
        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<string, DecisionResponse>(DecisionsWorkflow.SmoCancellationDelinkOfficersRuleSet, string.Empty);
        if (decisionResponse != null)
        {
            await SendDelinkNotificationsFromDecisions(filerUsers, decisionResponse.Notifications);
        }
    }

    /// <summary>
    /// Find and delete FilerUsers that are linked to the registration via RegistrationContact
    /// If ParentRegistrationId is passed, do a diff to determine which FilerUsers are newly added by the current registration.
    /// Exclude FilerUsers with Treasurer role
    /// </summary>
    /// <param name="registrationId"></param>
    /// <param name="parentRegistrationId"></param>
    /// <returns>FilerUsers deleted</returns>
    private async Task<List<FilerUser>> DeleteLinkagesCreatedByRegistration(long registrationId, long? parentRegistrationId)
    {
        var filerUsers = new List<FilerUser>();
        var currentFilerUsersFromContacts = await repositoriesDependencies.FilerUserRepository.FindFilerUsersLinkedToRegistrationContacts(registrationId);

        if (parentRegistrationId is { } parentId)
        {
            var parentFilerUsersFromContacts = await repositoriesDependencies.FilerUserRepository.FindFilerUsersLinkedToRegistrationContacts(parentId);
            // Get FilerUsers that were added by current registration FilerUserContacts
            var currentOnlyFilerUsers = currentFilerUsersFromContacts.Where(c => !parentFilerUsersFromContacts.Any(p => c.Id == p.Id));
            filerUsers.AddRange(currentOnlyFilerUsers);
        }
        else
        {
            filerUsers.AddRange(currentFilerUsersFromContacts);
        }

        // Exclude Treasurer from deletion
        var filerUsersToDelete = filerUsers.Where(x => x.FilerRoleId != FilerRole.SlateMailerOrg_Treasurer.Id).ToList();

        return await repositoriesDependencies.FilerUserRepository.DeleteFilerUsers(filerUsersToDelete);
    }

    /// <summary>
    /// Sends notifications with data for delinking. To be used after calling Decisions.
    /// </summary>
    /// <param name="filerUsers"></param>
    /// <param name="notifications"></param>
    /// <returns></returns>
    private async Task SendDelinkNotificationsFromDecisions(List<FilerUser> filerUsers, List<NotificationTrigger> notifications)
    {
        foreach (var filerUser in filerUsers)
        {
            // Send to User
            var sendToUserNotificationTemplateData = new Dictionary<string, string>
            {
                { NotificationTemplateConstants.FilerNamePlaceholder, filerUser.FilerName }
            };

            var fullName = string.Join(" ",
                           new[] { filerUser.User?.FirstName, filerUser.User?.MiddleName, filerUser.User?.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));

            // Send to Filer
            var sendToFilerNotificationTemplateData = new Dictionary<string, string>
            {
                { NotificationTemplateConstants.UserNamePlaceholder, fullName }
            };

            await SendNotifications(
               notifications,
               filerUser.UserId,
               filerUser.FilerId,
               sendToUserNotificationTemplateData,
               sendToFilerNotificationTemplateData);
        }
    }

    /// <summary>
    /// Search for Recipient Committees by Id and Name
    /// </summary>
    /// <param name="q">query term to partially search for</param>
    /// <returns></returns>
    /// <exception cref="KeyNotFoundException"></exception>
    public async Task<IEnumerable<CommitteeSearchResultDto>> SearchCommitteeByIdOrName(string q)
    {
        var filers = await repositoriesDependencies.RegistrationRepository.FindCommitteeByIdOrName(q);

        List<CommitteeSearchResultDto> list = new();
        foreach (Filer filer in filers)
        {
            Committee reg = (Committee)filer.CurrentRegistration!;
            long listId = reg.AddressListId.GetValueOrDefault();
            AddressList? addressList = await repositoriesDependencies.RegistrationRepository.GetAddressListById(listId) ?? new AddressList();
            IEnumerable<AddressDto> listDto = addressList.Addresses.Select(a => new AddressDto(a));

            CommitteeSearchResultDto item = new()
            {
                Id = filer.Id,
                Name = filer.CurrentRegistration!.Name,
                Addresses = listDto,
            };
            list.Add(item);
        }
        return list;
    }

    /// <inheritdoc />
    public async Task<CommitteeSearchResultDto?> GetLinkedRecipientCommitteeAsync(long filerId)
    {
        CommitteeSearchResultDto? committee = null;
        var filerLink = await servicesDependencies.FilerSvc.GetFilerLinkByFilerIdAndLinkTypeAsync(filerId, FilerLinkType.RecipientCommittee.Id);
        if (filerLink is not null)
        {
            var linkedEntityId = filerLink.LinkedEntityId;
            committee = (await SearchCommitteeByIdOrName(linkedEntityId.ToString(CultureInfo.InvariantCulture))).FirstOrDefault();
        }

        return committee ?? new CommitteeSearchResultDto { Id = 0, Name = "", Addresses = new List<AddressDto>() };
    }

    /// <inheritdoc />
    public async Task UpdatePrimaryFilerUserRoleAsync(long id, UpdateFilerUserRoleRequest request)
    {
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // Get Filer User of the user
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUser = await servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(existingRegistration.FilerId.GetValueOrDefault(), userId.GetValueOrDefault());

        // If adding a new officer and current role is treasurer, or editting treasurer, skip
        if (filerUser is null
            || (request.IsTreasurer == null && filerUser.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            || (request.Title == FilerRole.SlateMailerOrg_Treasurer.Name && filerUser.FilerRoleId != FilerRole.SlateMailerOrg_Treasurer.Id))
        {
            return;
        }

        await servicesDependencies.FilerSvc.UpdateFilerUserRoleAsync(filerUser.Id, MappingFilerUserRole(request.IsTreasurer, request.IsOfficer, request.Title));
    }

    /// <inheritdoc />
    public async Task CompleteTreasurerAcknowledgementAsync(long id)
    {
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");
        var filerUser = await GetFilerUserAsync(existingRegistration);
        var (matchedRegContact, registrationContacts) = await GetMatchedAndAllRegistrationContactsAsync(id, filerUser);

        await UpdateAcknowledgmentAsync(matchedRegContact);
        await CompleteRegistrationIfApplicableAsync(existingRegistration, registrationContacts);
    }

    /// <inheritdoc />
    public async Task<TreasurerAcknowledgementContactResponseDto> GetTreasurerAcknowledgementContactsAsync(long id)
    {
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // Get all contacts
        var registrationContacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(id);

        return new TreasurerAcknowledgementContactResponseDto
        {
            FilerId = existingRegistration.FilerId.GetValueOrDefault(),
            Contacts = registrationContacts
                .Where(x => x.Role == FilerRole.SlateMailerOrg_Treasurer.Name       // Only Treasurer and Asst. Treasurer
                    || x.Role == FilerRole.SlateMailerOrg_AssistantTreasurer.Name)
                .Select(x => new SmoRegistrationContactDto
                {
                    Id = x.Id,
                    FirstName = x.RegistrationContact?.FirstName,
                    LastName = x.RegistrationContact?.LastName,
                    Role = x.Role,
                    Title = x.Title,
                    HasAcknowledged = x.HasAcknowledged,
                    AcknowledgedOn = x.AcknowledgedOn,
                    UserId = x.RegistrationContact?.UserId
                })
        };
    }

    /// <inheritdoc />
    public async Task SendAcknowledgementNotificationsAsync(long id)
    {
        // Get acknowledgement contacts
        var response = await GetTreasurerAcknowledgementContactsAsync(id);

        // Send Linkage Requests to contacts that can acknowledge
        await SendLinkageRequestsToContactsWithPermission(id, Permission.Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment.Id);

        // Send notification to uncompleted contacts          
        await SendUserNotificationsAsync(response.FilerId.GetValueOrDefault(), response.Contacts.Where(x => !x.HasAcknowledged.GetValueOrDefault()), NotificationTemplate.TreasurerAcknowledgement.Id);
    }

    public async Task<RegistrationResponseDto> SubmitSmoRegistrationForEfile(EfileSlateMailerOrganizationDto submission)
    {
        ArgumentNullException.ThrowIfNull(submission);
        ArgumentNullException.ThrowIfNull(submission.AttesterName);
        ArgumentNullException.ThrowIfNull(submission.AttesterRole);
        ArgumentNullException.ThrowIfNull(submission.LinkedCommitteeId);
        var validationErrors = new List<WorkFlowError> { };
        var registration = submission.SlateMailerOrg!;
        // Call Decisions to validate business rule - Run post-submission workflow validation
        DecisionsSmoSubmissionResponse response = await ValidateSmoAttestation(servicesDependencies, true, submission.AttesterName, submission.AttesterRole, true, registration, registration.RegistrationRegistrationContacts, submission.LinkedCommitteeId);
        validationErrors.AddRange(response?.Result ?? new List<WorkFlowError> { });

        //Check SMO name is unique or not
        await CheckSmoUniquenessName(registration.Name, validationErrors, registration.ParentId);
        // Handle initial filing vs amendment
        if (submission.SlateMailerOrg!.ParentId == null || submission.SlateMailerOrg!.ParentId == 0)    // initial filing
        {
            submission.SlateMailerOrg.OriginalId = null;
            submission.SlateMailerOrg.Version = 0;
        }
        else    // amendment
        {
            SlateMailerOrganization? previousFiling = await repositoriesDependencies.RegistrationRepository.
                      FindSlateMailerOrganizationById((long)submission.SlateMailerOrg.ParentId!);
            if (previousFiling != null)
            {
                submission.SlateMailerOrg.OriginalId = previousFiling!.OriginalId ?? previousFiling!.Id;
                submission.SlateMailerOrg.Version = previousFiling!.Version++;
            }
            else
            {
                validationErrors.Add(CreateUniqueSmoAmendmentValidationError());
            }

        }
        if (validationErrors.Count == 0)
        {
            // Only send notifications to filer if the status is Accepted
            if (registration.StatusId == RegistrationStatus.Accepted.Id)
            {
                // Mark the registration approved
                registration.ApprovedAt = servicesDependencies.DateTimeSvc.GetCurrentDateTime();
            }
            submission.SlateMailerOrg.Attestations = new List<Attestation>() {
        new() {
            CreatedBy = 0,
            ExecutedAt = submission.SlateMailerOrg.VerificationExecutedAt!.Value,
            ModifiedBy = 0,
            Name = submission.SlateMailerOrg.VerificationSignature
        }
    };

            //Insert Record to DB
            await repositoriesDependencies.RegistrationRepository.Create(registration);

            // Link Vendor to registration
            var fileruser = new FilerUser() { UserId = submission.VendorUserId, FilerRoleId = FilerRole.SlateMailerOrg_ThirdPartyVendor.Id };
            var filer = new Filer() { CurrentRegistration = registration, FilerStatusId = FilerStatus.Active.Id, FilerTypeId = FilerType.SlateMailerOrg.Id, Users = new() { fileruser } };
            registration.Filer = filer;
            registration = (SlateMailerOrganization)await repositoriesDependencies.RegistrationRepository.Update(registration);
            //Link Committee
            await LinkRecipientCommitteeToFilerAsync(filer.Id, submission.LinkedCommitteeId, null);
            // Send Notifications to filer user
            await SendFilerNotificationsAsync(filer.Id, response?.Notifications, null);
        }

        return new RegistrationResponseDto(registration.Id, true, validationErrors, registration.StatusId);
    }

    public async Task<RegistrationResponseDto> SmoTerminationForEfile(EfileSlateMailerOrganizationDto submission)
    {
        ArgumentNullException.ThrowIfNull(submission);
        ArgumentNullException.ThrowIfNull(submission.SlateMailerOrg);
        ArgumentNullException.ThrowIfNull(submission.AttesterName);
        ArgumentNullException.ThrowIfNull(submission.AttesterRole);
        ArgumentNullException.ThrowIfNull(submission.SlateMailerOrg.RegistrationRegistrationContacts);
        long id = submission.SlateMailerOrg?.ParentId ?? 0;
        var validationErrors = new List<WorkFlowError> { };
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");
        var parentRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationWithParentById(id);

        // Set parent and activity level
        submission.SlateMailerOrg!.Parent = parentRegistration;
        submission.SlateMailerOrg.ActivityLevel = parentRegistration?.ActivityLevel;

        var existingAddress = parentRegistration?.AddressList?.Addresses.FirstOrDefault(s => s.Purpose == "Mailing");
        if (existingAddress != null)
        {
            // 2. Clone the old mailing address
            var newMailingAddress = new Address
            {
                Street = existingAddress.Street,
                City = existingAddress.City,
                Country = existingAddress.Country,
                Purpose = existingAddress.Purpose,
                State = existingAddress.State,
                Type = existingAddress.Type,
                Zip = existingAddress.Zip
            };

            // 3. Remove any existing mailing address from the new registration
            submission.SlateMailerOrg?.AddressList?.Addresses.RemoveAll(a => a.Purpose == "Mailing");

            // 4. Add the copied mailing address to the new registration
            submission.SlateMailerOrg?.AddressList?.Addresses.Add(newMailingAddress);
        }
        DecisionsSmoSubmissionResponse response = await ValidateSmoAttestation(servicesDependencies, true, submission.AttesterName ?? string.Empty, submission.AttesterRole ?? "", true, submission.SlateMailerOrg!, submission.SlateMailerOrg?.RegistrationRegistrationContacts!, existingRegistration.CommitteeId);
        validationErrors.AddRange(response.Result ?? new List<WorkFlowError> { });
        //Check SMO name is unique or not
        await CheckSmoUniquenessName(submission.SlateMailerOrg!.Name, validationErrors, submission.SlateMailerOrg.ParentId);
        if (validationErrors.Count > 0)
        {
            return new RegistrationResponseDto(existingRegistration.Id, false, validationErrors, existingRegistration.StatusId);
        }
        else
        {
            // Only send notifications to filer if the status is Accepted
            if (submission.SlateMailerOrg.StatusId == RegistrationStatus.Accepted.Id)
            {
                // Mark the registration approved
                submission.SlateMailerOrg.ApprovedAt = servicesDependencies.DateTimeSvc.GetCurrentDateTime();
            }
            submission.SlateMailerOrg.Attestations = new List<Attestation>() {
        new() {
            CreatedBy = 0,
            ExecutedAt = submission.SlateMailerOrg.VerificationExecutedAt!.Value,
            ModifiedBy = 0,
            Name = submission.SlateMailerOrg.VerificationSignature
            }
        };

            await repositoriesDependencies.RegistrationRepository.Create(submission.SlateMailerOrg);

            // Link Vendor to registration
            var fileruser = new FilerUser() { UserId = submission.VendorUserId, FilerRoleId = FilerRole.SlateMailerOrg_ThirdPartyVendor.Id };
            var filer = new Filer() { CurrentRegistration = submission.SlateMailerOrg, FilerStatusId = FilerStatus.Active.Id, FilerTypeId = FilerType.SlateMailerOrg.Id, Users = new() { fileruser } };
            submission.SlateMailerOrg.Filer = filer;
            submission.SlateMailerOrg = (SlateMailerOrganization)await repositoriesDependencies.RegistrationRepository.Update(submission.SlateMailerOrg);
            //Link Committee
            await LinkRecipientCommitteeToFilerAsync(filer.Id, submission.LinkedCommitteeId, null);
            // Send Notifications to filer user
            await SendFilerNotificationsAsync(filer.Id, response.Notifications, null);
            return new RegistrationResponseDto(submission.SlateMailerOrg.Id, true, validationErrors, submission.SlateMailerOrg.StatusId);
        }
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> AttestRegistrationAsync(long id)
    {
        var validationErrors = new List<WorkFlowError> { };
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        var parentRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationWithParentById(id);

        if (parentRegistration != null)
        {
            existingRegistration.Parent = parentRegistration.Parent;
        }

        var filerUser = await GetFilerUserAsync(existingRegistration);

        // Check if the registration already attested
        // If a registration is attested, return
        if (existingRegistration.StatusId == RegistrationStatus.Accepted.Id)
        {
            return new RegistrationResponseDto(existingRegistration.Id, true, validationErrors, existingRegistration.StatusId);
        }

        var (matchedRegContact, registrationContacts) = await GetMatchedAndAllRegistrationContactsAsync(id, filerUser);

        // Call Decisions to validate business rule - Run post-submission workflow validation
        DecisionsSmoSubmissionResponse response = await ValidateSmoAttestation(existingRegistration, registrationContacts, filerUser, true);
        validationErrors.AddRange(response.Result ?? new List<WorkFlowError> { });

        //Check SMO name is unique or not
        await CheckSmoUniquenessName(existingRegistration.Name, validationErrors, existingRegistration.ParentId);
        if (validationErrors.Count > 0)
        {
            return new RegistrationResponseDto(existingRegistration.Id, false, validationErrors, existingRegistration.StatusId);
        }

        await CreateAttestationAsync(id, matchedRegContact);
        await UpdateRegistrationSubmissionAsync(existingRegistration, true, response.Status, registrationContacts);
        // Ensure that all officers receive linkage requests 
        await SendLinkageRequestsToContactsWithPermission(id, Permission.Registration_SlateMailerOrganization_Attest.Id);
        await SendFilerNotificationsAsync(filerUser.FilerId, response.Notifications, null);

        return new RegistrationResponseDto(existingRegistration.Id, true, validationErrors, existingRegistration.StatusId);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<SmoRegistrationContactDto>> GetResponsibleOfficerContactsAsync(long id)
    {
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // Get all contacts
        var registrationContacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(existingRegistration.Id);

        // Only Treasurer, Asst. Treasurer and Officer role can attest the registration
        var authorizedRoleNames = new List<string>
        {
            FilerRole.SlateMailerOrg_Treasurer.Name,
            FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
            FilerRole.SlateMailerOrg_Officer.Name
        };

        return registrationContacts
            .Where(x => authorizedRoleNames.Contains(x.Role))
            .Select(x => new SmoRegistrationContactDto
            {
                Id = x.Id,
                FirstName = x.RegistrationContact?.FirstName,
                LastName = x.RegistrationContact?.LastName,
                Role = x.Role,
                Title = x.Title,
                UserId = x.RegistrationContact?.UserId,
            });
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationAttestationResponseDto> GetRegistrationAttestationAsync(long id)
    {
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // Find the attestation by registration ID
        var attestation = await repositoriesDependencies.AttestationRepository.FindByRegistrationId(existingRegistration.Id);
        if (attestation == null)
        {
            return new SmoRegistrationAttestationResponseDto { };
        }

        var (firstName, lastName) = SplitName(attestation.Name);
        return new SmoRegistrationAttestationResponseDto
        {
            FirstName = firstName,
            LastName = lastName,
            Title = attestation.Title,
            ExecutedAt = attestation.ExecutedAt,
        };
    }

    /// <inheritdoc />
    public async Task<RegistrationResponseDto> SendRegistrationForAttestationAsync(long id, SmoRegistrationSendForAttestationRequest request)
    {
        var validationErrors = new List<WorkFlowError> { };
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id)
               ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        var parentRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationWithParentById(id);

        if (parentRegistration != null)
        {
            existingRegistration.Parent = parentRegistration.Parent;
        }

        // Check if the registration already attested
        // If a registration is attested, return
        if (existingRegistration.Attestations?.Count > 0)
        {
            return new RegistrationResponseDto(existingRegistration.Id, true, validationErrors, existingRegistration.StatusId);
        }

        var filerUser = await GetFilerUserAsync(existingRegistration);

        // Get all contacts
        var registrationContacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(existingRegistration.Id);

        // Call Decisions to validate business rule - Run post-submission workflow validation
        DecisionsSmoSubmissionResponse response = await ValidateSmoAttestation(existingRegistration, registrationContacts, filerUser, false);
        validationErrors.AddRange(response.Result ?? new List<WorkFlowError> { });

        if (validationErrors.Count > 0)
        {
            return new RegistrationResponseDto(existingRegistration.Id, false, validationErrors, existingRegistration.StatusId);
        }

        await UpdateRegistrationSubmissionAsync(existingRegistration, false, response.Status, registrationContacts);
        await SendLinkageRequestsToContactsWithPermission(existingRegistration.Id, Permission.Registration_SlateMailerOrganization_Attest.Id);
        await SendOfficerNotificationsAsync(existingRegistration.FilerId.GetValueOrDefault(), registrationContacts, request.RegistrationRegistrationContactIds, response.Notifications);

        return new RegistrationResponseDto(existingRegistration.Id, true, new(), existingRegistration.StatusId);
    }

    /// <inheritdoc />
    public async Task<List<PendingItemDto>> GetPendingItemsAsync(long id)
    {
        var pendingItems = new List<PendingItemDto> { };

        // Get uncompleted treasurer acknowledgement
        var response = await GetTreasurerAcknowledgementContactsAsync(id);

        // Check if there is any uncompleted Treasurer Acknowledgement
        var hasUncompletedTreasurerAcknowledgement = response.Contacts.Any(x => !x.HasAcknowledged.GetValueOrDefault());
        if (hasUncompletedTreasurerAcknowledgement)
        {
            pendingItems.Add(CreatePendingItem(PendingItemTreasurerAcknowledgement));
        }

        // Check if there is any uncompleted Attestation
        var attestation = await repositoriesDependencies.AttestationRepository.FindByRegistrationId(id);
        if (attestation is null)
        {
            pendingItems.Add(CreatePendingItem(PendingItemAttestation));
        }

        return pendingItems;
    }

    /// <inheritdoc />
    public async Task<SmoRegistrationResponseDto> CreateSmoAmendmentRegistrationAsync(long id)
    {
        // ID could be the Parent or Amendment registration
        var registration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // Only Accepted status can create an amendment
        if (registration.StatusId != RegistrationStatus.Accepted.Id)
        {
            return new SmoRegistrationResponseDto(registration);
        }

        // If the amendment already created, return it
        var existingAmendmentRegistration = await repositoriesDependencies.RegistrationRepository.FindExistingSmoAmendmentRegistration(registration.Id, RegistrationStatus.Draft.Id);
        if (existingAmendmentRegistration is not null)
        {
            return new SmoRegistrationResponseDto(existingAmendmentRegistration);
        }

        // Get UserID
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();

        var settings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            PreserveReferencesHandling = PreserveReferencesHandling.None
        };

        // Serialize/De-serialize object to remove the tracking.
        var json = JsonConvert.SerializeObject(registration, settings);
        var amendmentRegistration = JsonConvert.DeserializeObject<SlateMailerOrganization>(json)!;

        // To create a new amendment, we will clone everything from the parent, increase the version, change the parent, and set the status to Draft
        // Reset some related entities and set amendment-specific properties 
        ResetAmendmentRegistration(amendmentRegistration, registration, userId.GetValueOrDefault());

        // Re-create some related entities IDs.
        // Clone Address
        CloneAddressList(amendmentRegistration.AddressList);

        // Clone Phone Number
        ClonePhoneNumberList(amendmentRegistration.PhoneNumberList);

        await repositoriesDependencies.RegistrationRepository.Create(amendmentRegistration);

        // Clone Contact
        var existingContacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsByIdNoTracking(id);
        if (existingContacts is not null)
        {
            foreach (var rrContacts in existingContacts)
            {
                rrContacts.Registration = null;
            }

            var contactJson = JsonConvert.SerializeObject(existingContacts, settings);
            var amendmentContacts = JsonConvert.DeserializeObject<List<RegistrationRegistrationContact>>(contactJson)!;

            CloneRegistrationContact(amendmentContacts);

            foreach (var rrContacts in existingContacts)
            {
                rrContacts.Registration = amendmentRegistration;
            }

            amendmentRegistration.RegistrationRegistrationContacts = amendmentContacts!;
            await repositoriesDependencies.RegistrationRepository.Update(amendmentRegistration);
        }

        return new SmoRegistrationResponseDto(amendmentRegistration);
    }

    /// <inheritdoc/>
    private async Task ModifyFilerUserAsync(long filerId, long? userId, long filerRoleId, bool isReplacingExistingTreasurer)
    {
        // Get all the FilerUser by filerId
        var filerUsers = await servicesDependencies.FilerSvc.GetFilerUsersAsync(filerId);

        if (userId is not { } uid)
        {
            // Edge case: Current user is Treasure and replace by a new Treasurer
            // Revert user role to Account Manager
            if (!isReplacingExistingTreasurer)
            {
                return;
            }

            uid = (await servicesDependencies.AuthorizationSvc.GetInitiatingUserId()).GetValueOrDefault();
            var currentFilerUserTreasurer = filerUsers.FirstOrDefault(x => x.UserId == uid && x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id);
            if (currentFilerUserTreasurer is not null)
            {
                await servicesDependencies.FilerSvc.UpdateFilerUserRoleAsync(currentFilerUserTreasurer.Id, FilerRole.SlateMailerOrg_AccountManager.Id);
            }

            return;
        }

        var filerUserData = filerUsers.FirstOrDefault(x => x.UserId == userId);

        // Not found Filer User, create a new one
        if (filerUserData is null)
        {
            var filerUser = new FilerUser
            {
                FilerId = filerId,
                FilerRoleId = filerRoleId,
                UserId = uid,
            };

            await servicesDependencies.FilerSvc.AddFilerUserAsync(filerUser);
            return;
        }

        // Update the current one
        await servicesDependencies.FilerSvc.UpdateFilerUserRoleAsync(filerUserData.Id, filerRoleId);
    }

    /// <inheritdoc />
    public async Task<SmoGeneralInformationResponseDto> GetSmoGeneralInformationAsync(long id)
    {
        SmoRegistrationResponseDto? registration = await GetRegistrationFilingById(id) ?? throw new KeyNotFoundException($"Registration not found. Id={id}");
        ArgumentNullException.ThrowIfNull(registration.FilerId);
        CommitteeSearchResultDto? committee = await GetLinkedRecipientCommitteeAsync((long)registration.FilerId);
        SmoRegistrationContactDto? treasurer = await GetSmoTreasurerPage02(id);

        return new SmoGeneralInformationResponseDto(
            registration,
            committee,
            treasurer);
    }

    /// <inheritdoc/>
    public async Task<List<SmoRegistrationBasicResponseDto>> SearchSmoRegistrationByIdOrNameAsync(string query, long? statusId)
    {
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
        var result = await repositoriesDependencies.RegistrationRepository.SearchSmoRegistrationByIdOrName(query, statusId ?? RegistrationStatus.Accepted.Id, userId);

        return [.. result.Select(SmoRegistrationBasicResponseDto.MapToDto)];
    }

    /// <inheritdoc/>
    public async Task<RegistrationResponseDto> UpdateNoticeOfTerminationSmoRegistrationAsync(long id, NoticeOfTerminationRequest request)
    {
        bool isValid = true;
        List<WorkFlowError> validationErrors = new(); // Default empty list

        var registration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationWithParentById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        // If the registration already terminated, skip
        if (registration.StatusId == RegistrationStatus.Terminated.Id)
        {
            return new RegistrationResponseDto(registration.Id, isValid, validationErrors);
        }

        if (request.IsTerminating)
        {
            // Run Decisions only when terminating
            await ValidateTerminationAsync(registration, request, validationErrors);

            TerminateSmoRegistration(registration, request.TerminationDate);
        }
        else
        {
            registration.TerminatedAt = null;
        }

        if (validationErrors.Count != 0)
        {
            isValid = false;
            return new RegistrationResponseDto(id, isValid, validationErrors);
        }

        await repositoriesDependencies.RegistrationRepository.Update(registration);

        return new RegistrationResponseDto(id, isValid, validationErrors);
    }

    /// <inheritdoc/>
    public async Task<bool> IsRegistrationTerminatingAsync(long id)
    {
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");

        return existingRegistration.TerminatedAt.HasValue;
    }

    /// <inheritdoc/>
    public async Task<List<SmoRegistrationContactDto>> SearchLatestAcceptedSmoRegistrationOfficerByNameAsync(long filerId, string query)
    {
        var officers = await repositoriesDependencies.RegistrationRegistrationContactRepository.SearchLatestAcceptedSmoRegistrationOfficerByName(filerId, query);

        return [.. officers.Select(x => SmoRegistrationContactDto.MapToDto(x, servicesDependencies.DateTimeSvc))];
    }

    /// <inheritdoc/>
    public async Task<long> GetCurrentUserRoleByIdAsync(long id)
    {
        var existingRegistration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationBasicById(id)
            ?? throw new KeyNotFoundException($"Registration not found. Id={id}");
        long? userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUser = await servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(existingRegistration.FilerId.GetValueOrDefault(), userId.GetValueOrDefault())
            ?? throw new UnauthorizedAccessException($"The current user is not associated to the registration. ID={id}");

        // If the contact is removed from the registration, override the current role (no need to wait till the linkage is terminated)
        // Reuse the function that contains the throw
        // Catch the error and return a proper result
        try
        {
            var (matchedRegContact, _) = await GetMatchedAndAllRegistrationContactsAsync(id, filerUser!);

            ContactTitleRoleMapping.TitleOrRoleNameToRoleId.TryGetValue(matchedRegContact.Role, out var roleId);

            return roleId;
        }
        catch (Exception)
        {
            // Error on not found the matched contact, means the officer has already been removed from the list
            // This is an expected behavior
            return FilerRole.SlateMailerOrg_AccountManager.Id;
        }
    }

    /// <inheritdoc/>
    public async Task<SmoRegistrationFilingSummaryDto> GetSmoRegistrationFilingSummary(long id)
    {
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();

        var registration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id);
        var filerUser = await servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(registration!.FilerId.GetValueOrDefault(), userId.GetValueOrDefault());
        var fullName = filerUser?.User is not null ? $"{filerUser.User.FirstName} {filerUser.User.LastName}" : string.Empty;
        var registrationStatus = RegistrationStatusMapping.RegistrationStatusByName.Values.FirstOrDefault(s => s.Id == registration.StatusId)?.Name;
        var matchedRegContact = await GetRegistrationContactByUserId(id, filerUser!.UserId);
        var isTreasurerAcknowledgement = matchedRegContact?.HasAcknowledged ?? false;
        var officers = await GetSmoOfficers(id);
        var individualAuthorizers = (await GetSmoOfficers(id)).Where(x => x.CanAuthorize == true);
        var pendingItems = await GetPendingItemsAsync(id);
        var attestation = await GetRegistrationAttestationAsync(id);

        return new SmoRegistrationFilingSummaryDto
        {
            FilerName = fullName,
            FilingStatus = registrationStatus,
            Registration = new SmoRegistrationResponseDto(registration),
            Officers = officers,
            IndividualAuthorizers = individualAuthorizers,
            PendingItems = pendingItems,
            Attestation = attestation,
            IsTreasurerAcknowledgement = isTreasurerAcknowledgement
        };
    }

    public async Task HandleRegistrationEditAsync(long id)
    {
        var registration = await repositoriesDependencies.RegistrationRepository.FindSlateMailerOrganizationById(id);

        if (registration?.StatusId != RegistrationStatus.Pending.Id)
        {
            throw new InvalidOperationException("Only pending registrations can be edited.");
        }
        // Update Registration Status to Draft
        await UpdateRegistrationStatusAsync(registration!, RegistrationStatus.Draft.Id);

        // Call decisions
        var decisionsResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<string, DecisionResponse>(
            DecisionsWorkflow.SmoNoticeOfEditRuleSet,
            string.Empty);

        var notificationData = new Dictionary<string, string>
        {
            { NotificationTemplateConstants.DocumentIdPlaceholder, id.ToString(CultureInfo.InvariantCulture) }
        };

        // Send Notification to Filer user
        await SendFilerNotificationsAsync(registration!.FilerId!.Value, decisionsResponse.Notifications, notificationData);
    }

    #region Private
    private const string PendingItemTreasurerAcknowledgement = "Treasurer Acknowledgment";
    private const string PendingItemAttestation = "Attestation";
    private const string PendingItemStatusInProgress = "In Progress";

    /// <summary>
    /// Creates a PhoneNumberList from the PhoneNumberDto
    /// </summary>
    /// <param name="phoneNumberDto">Phone Number DTO</param>
    /// <returns></returns>
    private PhoneNumberList CreatePhoneNumberList(PhoneNumberDto phoneNumberDto)
    {
        PhoneNumberList list = new();
        phoneNumberDto.Type = string.IsNullOrWhiteSpace(phoneNumberDto.Type) ? RegistrationConstants.PhoneNumber.TypeHome : phoneNumberDto.Type;
        list.PhoneNumbers.Add(phoneNumberDto.CreateModel(servicesDependencies.ReferenceDataSvc));
        return list;
    }

    /// <summary>
    /// Creates an AddressList from the AddressDto
    /// </summary>
    /// <param name="addressDto">Address Dto</param>
    /// <returns></returns>
    private static AddressList CreateAddressList(AddressDto addressDto)
    {
        AddressList list = new();
        list.Addresses.Add(CreateAddress(addressDto));
        return list;
    }

    /// <summary>
    /// Creates a Address Object from the AddressDto
    /// </summary>
    /// <param name="addressDto">Address Dto</param>
    /// <returns></returns>
    private static Address CreateAddress(AddressDto addressDto)
    {
        return new()
        {
            Country = addressDto.Country ?? string.Empty,
            Type = addressDto.Type ?? string.Empty,
            Purpose = RegistrationConstants.Address.PurposeOfficer,
            Street = addressDto.Street ?? string.Empty,
            Street2 = addressDto.Street2 ?? string.Empty,
            City = addressDto.City ?? string.Empty,
            State = addressDto.State ?? string.Empty,
            Zip = (ZipCode)addressDto.Zip
        };
    }

    /// <summary>
    /// Updates an existing Address with values from a Address DTO
    /// </summary>
    /// <param name="address">Existing Address Record</param>
    /// <param name="addressDto">Address DTO</param>
    /// <returns></returns>
    private static Address UpdateAddress(Address address, AddressDto addressDto)
    {
        address.Country = addressDto?.Country ?? string.Empty;
        address.Type = addressDto?.Type ?? string.Empty;
        address.Purpose = RegistrationConstants.Address.PurposeOfficer;
        address.Street = addressDto?.Street ?? string.Empty;
        address.Street2 = addressDto?.Street2 ?? string.Empty;
        address.City = addressDto?.City ?? string.Empty;
        address.State = addressDto?.State ?? string.Empty;
        address.Zip = (ZipCode)addressDto?.Zip;
        return address;
    }

    private void UpdatePhoneNumber(List<PhoneNumber> phoneNumbers, PhoneNumberDto dto)
    {
        // Set default value for PhoneNumber type if it's empty
        dto.Type = string.IsNullOrWhiteSpace(dto.Type) ? RegistrationConstants.PhoneNumber.TypeHome : dto.Type;
        PhoneNumber phoneNumber = phoneNumbers[0];
        dto.UpdateModel(servicesDependencies.ReferenceDataSvc, phoneNumber);
    }

    /// <summary>
    /// Returns an decisions input request body to validate officer information (including treasurer)
    /// </summary>
    /// <param name="address">Existing Address Record</param>
    /// <param name="addressDto">Address DTO</param>
    /// <returns></returns>
    private static DecisionsSmoRegistrationContact PopulateDecisionServiceRequestOfficer(RegistrationContact contact, string? title)
    {
        // Telephone Number
        var phoneNumber = contact?.PhoneNumberList?.PhoneNumbers?.FirstOrDefault(x => x.Type == RegistrationConstants.PhoneNumber.TypeHome);
        var decisionsSmoOfficerRequest = new DecisionsSmoRegistrationContact
        {
            Title = string.IsNullOrEmpty(title) ? null : title,
            FirstName = contact?.FirstName ?? string.Empty,
            MiddleName = contact?.MiddleName ?? string.Empty,
            LastName = contact?.LastName ?? string.Empty,
            Email = contact?.Email ?? string.Empty,
            TelephoneNumber = !string.IsNullOrWhiteSpace(phoneNumber?.Number) ? $"{phoneNumber.CountryCode}{phoneNumber.Number}" : string.Empty,
            Address = contact?.AddressList != null
                              ? MapAddress(contact.AddressList, RegistrationConstants.Address.PurposeOfficer)
                              : null,
        };

        return decisionsSmoOfficerRequest;
    }

    private async Task LinkRecipientCommitteeToFilerAsync(long filerId, long? committeeId, FilerLink? linkedCommittee)
    {
        // If committeeId is null, 0 or no change
        // No execution further
        if (!committeeId.HasValue || committeeId == 0 || committeeId == linkedCommittee?.LinkedEntityId)
        {
            return;
        }

        // Get the current user
        var userId = (await servicesDependencies.AuthorizationSvc.GetInitiatingUserId()).Value;

        // If there is no committee has been linked
        if (linkedCommittee is null)
        {
            var newFilerLink = new FilerLink
            {
                FilerId = filerId,
                LinkedEntityId = committeeId.Value,
                FilerLinkTypeId = FilerLinkType.RecipientCommittee.Id,
                EffectiveDate = servicesDependencies.DateTimeSvc.GetCurrentDateTime(),
                CreatedBy = userId,
                ModifiedBy = 0,
            };
            await servicesDependencies.FilerSvc.AddFilerLinkAsync(newFilerLink);
        }
        // Replace the current link
        else
        {
            linkedCommittee.LinkedEntityId = committeeId.Value;
            linkedCommittee.ModifiedBy = userId;
            await servicesDependencies.FilerSvc.UpdateFilerLinkAsync(linkedCommittee);
        }
    }

    private static long MappingFilerUserRole(bool? isTreasurer, bool? isOfficer, string title = "")
    {
        if (isTreasurer.GetValueOrDefault())
        {
            return FilerRole.SlateMailerOrg_Treasurer.Id;
        }

        // Is officer
        if (isOfficer is null || isOfficer.GetValueOrDefault())
        {
            return title == FilerRole.SlateMailerOrg_AssistantTreasurer.Name
                ? FilerRole.SlateMailerOrg_AssistantTreasurer.Id
                : FilerRole.SlateMailerOrg_Officer.Id;
        }

        return FilerRole.SlateMailerOrg_AccountManager.Id;
    }

    private async Task SendUserNotificationsAsync(long filerId, IEnumerable<SmoRegistrationContactDto> contacts, long templateId)
    {
        // Try to catch error from notification service
        // Currently, the user preference has not working properly
        foreach (var contact in contacts)
        {
            try
            {
                // Skip creating a notification task if userId is null
                var userId = contact.UserId;
                if (userId is not null)
                {
                    await servicesDependencies.NotificationSvc.SendUserNotification(
                        new SendUserNotificationRequest(
                            templateId,
                            userId.Value,
                            filerId,
                            servicesDependencies.DateTimeSvc.GetCurrentDateTime(),
                            null
                        ));
                }
            }
            catch (Exception)
            {
                // Not throw to the FE
            }
        }
    }

    private static bool HasUncompletedTreasurerAcknowledgementContacts(IEnumerable<RegistrationRegistrationContact> contacts)
    {
        var authorizedRoleNames = new List<string>
        {
            FilerRole.SlateMailerOrg_Treasurer.Name,
            FilerRole.SlateMailerOrg_AssistantTreasurer.Name
        };

        return contacts.Any(x => authorizedRoleNames.Contains(x.Role) && !x.HasAcknowledged.GetValueOrDefault());
    }

    private async Task CreateAttestationAsync(long id, RegistrationRegistrationContact contact)
    {
        var fullName = contact.RegistrationContact is not null
            ? $"{contact.RegistrationContact.FirstName} {contact.RegistrationContact.LastName}"
            : string.Empty;
        var attestation = new Attestation
        {
            RegistrationId = id,
            Name = fullName,
            Role = contact.Role,
            Title = contact.Title,
            ExecutedAt = servicesDependencies.DateTimeSvc.GetCurrentDateTime(),
            CreatedBy = 0,
            ModifiedBy = 0,
        };
        await repositoriesDependencies.AttestationRepository.Create(attestation);
    }

    /// <summary>
    /// Validates the SMO for Attestation
    /// </summary>
    /// <param name="filerUser">current filerUser</param>
    /// <param name="registration">smo registration</param>
    /// <returns></returns>
    private async Task<DecisionsSmoSubmissionResponse> ValidateSmoAttestation(SlateMailerOrganization registration, IEnumerable<RegistrationRegistrationContact> registrationContacts, FilerUserDto filerUser, bool isAttesting)
    {
        var fullName = filerUser.User is not null ? $"{filerUser.User.FirstName} {filerUser.User.LastName}" : string.Empty;
        // Should call Decisions to validate business rule - Run post-submission workflow validation
        bool userIsTreasurer = (filerUser.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id) || (filerUser.FilerRoleId == FilerRole.SlateMailerOrg_AssistantTreasurer.Id);

        //Populate PhoneList
        //: this should be able to be removed by using EF includes when initially fetching SMO record
        long? phoneListId = registration?.PhoneNumberListId;
        var phoneList = await repositoriesDependencies.RegistrationRepository.GetPhoneNumberListById(phoneListId ?? 0);
        registration!.PhoneNumberList = phoneList;

        //Populate Address List
        long addressListId = registration.AddressListId.GetValueOrDefault();
        var addressList = await repositoriesDependencies.RegistrationRepository.GetAddressListById(addressListId);
        registration!.AddressList = addressList;

        var linkedCommittee = registration.Filer?.FilerLinks?.FirstOrDefault(x => x!.FilerLinkTypeId == FilerLinkType.RecipientCommittee.Id);

        return await ValidateSmoAttestation(servicesDependencies, isAttesting, fullName, filerUser.FilerRole!.Name, userIsTreasurer, registration, registrationContacts, linkedCommittee?.Id);
    }

#pragma warning disable S107
    /// <summary>
    /// Validates the SMO for Attestation
    /// </summary>
    /// <param name="dependencies">dependencies</param>
    /// <param name="filerUser">current filerUser</param>
    /// <param name="existingRegistration">smo registration</param>
    /// <returns></returns>
    private static async Task<DecisionsSmoSubmissionResponse> ValidateSmoAttestation(FilingSharedServicesDependencies servicesDependencies, bool isAttesting, string fullName, string filerRole, bool userIsTreasurer, SlateMailerOrganization existingRegistration, IEnumerable<RegistrationRegistrationContact> registrationContacts, long? linkedCommitteeId)
    {
        var decisionsAttestation = new DecisionsSmoAttestation
        {
            Attestation = new()
            {
                AttestationName = fullName,
                AttestationRole = filerRole,
            },
            TreasurerAcknowledgement = new()
            {
                IsUserTreasurer = userIsTreasurer,
                HasAcknowledged = userIsTreasurer,
            }
        };

        // If attesting, run attestation pre-checks
        if (isAttesting)
        {
            var errors = await servicesDependencies.DecisionsSvc
                .InitiateWorkflow<DecisionsSmoAttestation, List<WorkFlowError>>(
                    DecisionsWorkflow.SmoPreAttestationRuleSet,
                    decisionsAttestation,
                    checkRequiredFields: true);

            if (errors?.Count > 0)
            {
                return new DecisionsSmoSubmissionResponse
                {
                    Result = errors
                };
            }
        }

        var decisionsSubmission = PopulateDecisionsSmoSubmission(decisionsAttestation, existingRegistration, registrationContacts, linkedCommitteeId);
        DecisionsSmoSubmissionResponse decisionResponse;

        var isTerminating = existingRegistration.TerminatedAt is not null;

        if (isTerminating)
        {
            var decisionsRuleSet = GetDecisionsRuleSet(isAttesting, isTerminating);
            var terminationSubmission = new DecisionsSmoTerminationSubmission
            {
                DecisionsSmoTermination = new DecisionsSmoTermination
                {
                    SubmissionDate = existingRegistration.Parent!.SubmittedAt,
                    EffectiveDateOfTermination = existingRegistration.TerminatedAt
                },
                DecisionsSmoTerminationFinalSubmission = decisionsSubmission
            };
            decisionResponse = await servicesDependencies.DecisionsSvc
                .InitiateWorkflow<DecisionsSmoTerminationSubmission, DecisionsSmoSubmissionResponse>(
                    decisionsRuleSet,
                    terminationSubmission,
                    checkRequiredFields: true);
        }
        else
        {
            var decisionsRuleSet = GetDecisionsRuleSet(isAttesting, isTerminating);
            decisionResponse = await servicesDependencies.DecisionsSvc
                .InitiateWorkflow<DecisionsSmoSubmission, DecisionsSmoSubmissionResponse>(
                    decisionsRuleSet,
                    decisionsSubmission,
                    checkRequiredFields: true);
        }

        return decisionResponse;
    }
#pragma warning restore S107

    /// <summary>
    /// Send the notifications from decisions
    /// </summary>
    /// <param name="notifications">List of Notifications</param>
    /// <param name="filerId">Filer Id</param>
    /// <returns></returns>
    private async Task SendFilerNotificationsAsync(long filerId, List<NotificationTrigger>? notifications, Dictionary<string, string>? notificationData)
    {
        if (notifications is null)
        {
            return;
        }

        foreach (NotificationTrigger notification in notifications)
        {
            if (notification.SendNotification && notification.NotificationTemplateId is { } templateId)
            {
                await servicesDependencies.NotificationSvc.SendFilerNotification(new SendFilerNotificationRequest(templateId, filerId, notification.DueDate, notificationData));
            }
        }
    }

    /// <summary>
    /// Populates the Decisions Input
    /// </summary>
    /// <param name="decisionsAttestation">Decisions PreAttestation</param>
    /// <param name="existingRegistration">Smo Registration</param>
    /// <param name="registrationContacts">All the RegistrationRegistrationContacts</param>
    /// <param name="filerUser">Current FilerUser</param>
    /// <returns></returns>
    private static DecisionsSmoSubmission PopulateDecisionsSmoSubmission(DecisionsSmoAttestation decisionsAttestation, SlateMailerOrganization? existingRegistration, IEnumerable<RegistrationRegistrationContact> registrationContacts, long? linkedCommitteeId)
    {
        // Split registrationContacts
        DecisionsSmoRegistrationContact treasurer = new();
        List<DecisionsSmoRegistrationContact> officers = new();
        List<DecisionsSmoRegistrationContact> authorizers = new();

        foreach (RegistrationRegistrationContact contact in registrationContacts)
        {
            long roleId = SmoRegistrationContactDto.GetRoleIdFromName(contact.Role);
            if (roleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            {
                treasurer = PopulateDecisionServiceRequestOfficer(contact.RegistrationContact!, contact.Title);
            }
            else if ((roleId == FilerRole.SlateMailerOrg_AssistantTreasurer.Id) || (roleId == FilerRole.SlateMailerOrg_Officer.Id))
            {
                officers.Add(PopulateDecisionServiceRequestOfficer(contact.RegistrationContact!, contact.Title));
            }
            else if (contact.CanAuthorizeSlateMailerContents ?? false)
            {
                authorizers.Add(PopulateDecisionServiceRequestOfficer(contact.RegistrationContact!, contact.Title));
            }
        }

        DecisionsSmoSubmission decisionsSubmission = new()
        {
            SMOContactInformation = PopulateDecisionServiceRequest(existingRegistration!),
            SDMRegistrationDetails = new DecisionsSmoRegistrationDetails
            {
                OrganizationLevelOfActivity = existingRegistration!.ActivityLevel ?? "",
                IsOrganizationQualified = existingRegistration!.QualifiedCommittee.GetValueOrDefault(false),
                DateQualifiedAsSMO = existingRegistration!.DateQualified,
                IsOrganizationCampaignCommittee = existingRegistration!.CampaignCommittee.GetValueOrDefault(false),
                CommitteeIDOrName = linkedCommitteeId is not null ? linkedCommitteeId.Value.ToString(CultureInfo.CurrentCulture) : "",
            },
            SMOTreasurerInformation = treasurer,
            SMOPrincipalOfficer = officers,
            SMOAuthorizersInformation = authorizers,
            SMOAttestation = decisionsAttestation,
        };

        return decisionsSubmission;
    }

    private async Task UpdateRegistrationStatusAsync(SlateMailerOrganization registration, long statusId)
    {
        // Update Registration Status
        registration.StatusId = statusId;

        // Update Filer Status & relink Filer to the latest Accepted registration if the status is Accepted
        if (registration.Filer is not null && statusId == RegistrationStatus.Accepted.Id)
        {
            registration.Filer.FilerStatusId = FilerStatus.Active.Id;
            registration.Filer.CurrentRegistrationId = registration.Id;
        }

        await repositoriesDependencies.RegistrationRepository.Update(registration);
    }

    private static (string? FirstName, string? LastName) SplitName(string? fullName)
    {
        if (string.IsNullOrWhiteSpace(fullName))
        {
            return (null, null);
        }

        var splitedParts = fullName.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries);
        return (splitedParts.ElementAtOrDefault(0), splitedParts.ElementAtOrDefault(1));
    }

    private static PendingItemDto CreatePendingItem(string item)
    {
        return new PendingItemDto
        {
            Item = item,
            Status = PendingItemStatusInProgress
        };
    }

    private static WorkFlowError CreateUniqueSmoNameValidationError()
    {
        return new WorkFlowError("Name", RegistrationConstants.ValidationError.ErrorCodeGlobal0002, RegistrationConstants.ValidationError.ErrorTypeValidation, $"Organization names must be unique. There is an active organization with this name.");
    }

    private static WorkFlowError CreateUniqueSmoAmendmentValidationError()
    {
        return new WorkFlowError("Name", RegistrationConstants.ValidationError.ErrorCodeGlobal0002, RegistrationConstants.ValidationError.ErrorTypeValidation, $"Previous filing should not be null for amendment filing ");
    }

    private static void ResetAmendmentRegistration(SlateMailerOrganization amendmentRegistration, SlateMailerOrganization originalRegistration, long userId)
    {
        amendmentRegistration.Id = 0;
        amendmentRegistration.Version = (originalRegistration.Version ?? 0) + 1;
        amendmentRegistration.ParentId = originalRegistration.Id;
        amendmentRegistration.StatusId = RegistrationStatus.Draft.Id;
        amendmentRegistration.SubmittedAt = null;
        amendmentRegistration.ApprovedAt = null;
        amendmentRegistration.TerminatedAt = null;
        amendmentRegistration.CreatedBy = userId;

        // Keep the Filer remain
        amendmentRegistration.Filer = null;
        amendmentRegistration.Attestations = [];
        amendmentRegistration.RegistrationRegistrationContacts = []; // Set the contacts to empty to seperate the clone as is is a deep nested object
    }

    private static void CloneAddressList(AddressList? addressList)
    {
        if (addressList is null)
        {
            return;
        }

        addressList.Id = 0;
        foreach (var addr in addressList.Addresses ?? [])
        {
            addr.Id = 0;
        }
    }

    private static void ClonePhoneNumberList(PhoneNumberList? phoneNumberList)
    {
        if (phoneNumberList is null)
        {
            return;
        }

        phoneNumberList.Id = 0;
        foreach (var phone in phoneNumberList.PhoneNumbers ?? [])
        {
            phone.Id = 0;
        }
    }

    private static void CloneRegistrationContact(List<RegistrationRegistrationContact> contacts)
    {
        if (contacts is null)
        {
            return;
        }

        foreach (var contact in contacts)
        {
            contact.Id = 0;

            if (contact.RegistrationContact is null)
            {
                return;
            }

            contact.RegistrationContact.Id = 0;

            if (contact.RegistrationContact.AddressList is not null)
            {
                contact.RegistrationContact.AddressList.Id = 0;
                foreach (var addr in contact.RegistrationContact.AddressList.Addresses ?? [])
                {
                    addr.Id = 0;
                }
            }

            if (contact.RegistrationContact.PhoneNumberList is not null)
            {
                contact.RegistrationContact.PhoneNumberList.Id = 0;
                foreach (var phone in contact.RegistrationContact.PhoneNumberList.PhoneNumbers ?? [])
                {
                    phone.Id = 0;
                }
            }
        }
    }

    private async Task CheckSmoUniquenessName(string name, List<WorkFlowError> validationErrors, long? parentId = null)
    {
        var isUnique = await repositoriesDependencies.RegistrationRepository.IsUniqueSmoName(name, parentId);
        if (string.IsNullOrWhiteSpace(name) || !isUnique)
        {
            validationErrors.Add(CreateUniqueSmoNameValidationError());
        }
    }

    private static DecisionsWorkflow GetDecisionWorkflowByRole(string role, bool isCreateOrUpdateAuthorizer = false)
    {
        if (isCreateOrUpdateAuthorizer || role == FilerRole.SlateMailerOrg_AccountManager.Name)
        {
            return DecisionsWorkflow.SmoAuthorizerRuleSet;
        }

        if (role == FilerRole.SlateMailerOrg_Treasurer.Name)
        {
            return DecisionsWorkflow.SmoTreasurerRuleSet;
        }

        return DecisionsWorkflow.SmoOfficerRuleSet;
    }

    /// <summary>
    /// Update the FilerUser role during transfer
    /// </summary>
    /// <param name="filerUser">The current FilerUser</param>
    /// <param name="treasurer">The new treasurer RegistrationRegistrationContact</param>
    /// <param name="request">The Transfer Request</param>
    /// <returns></returns>
    private async Task UpdateTransferTreasurerFilerRole(long filerId, long oldTreasurerUserId, long newTreasurerUserId, SmoTreasurerTransferDto request)
    {
        var filerUsers = await servicesDependencies.FilerSvc.GetFilerUsersAsync(filerId);
        var oldTreasurerFilerUser = filerUsers.FirstOrDefault(x => x.UserId == oldTreasurerUserId);
        var newTreasurerFilerUser = filerUsers.FirstOrDefault(x => x.UserId == newTreasurerUserId);

        // If found FilerUser of new Treasurer, update
        // Otherwise, skip
        if (newTreasurerFilerUser is not null)
        {
            await servicesDependencies.FilerSvc.UpdateFilerUserRoleAsync(newTreasurerFilerUser.Id, FilerRole.SlateMailerOrg_Treasurer.Id);
        }

        // If could not find the FilerUser of old Treasurer, skip
        // Otherwise, update their role
        if (oldTreasurerFilerUser is null)
        {
            return;
        }

        // Identify new role for the old treasurer
        long filerRoleId;
        if (request.PreviousTreasurerKeep)
        {
            // Switch to Officer
            if (string.Equals(request.PreviousTreasurerTitle, FilerRole.SlateMailerOrg_AssistantTreasurer.Name, StringComparison.Ordinal))
            {
                filerRoleId = FilerRole.SlateMailerOrg_AssistantTreasurer.Id;
            }
            else
            {
                filerRoleId = FilerRole.SlateMailerOrg_Officer.Id;
            }
        }
        else
        {
            filerRoleId = FilerRole.SlateMailerOrg_AccountManager.Id;
        }

        await servicesDependencies.FilerSvc.UpdateFilerUserRoleAsync(oldTreasurerFilerUser.Id, filerRoleId);
    }

    private static string GetContactUserName(RegistrationContact? contact)
    {
        if (contact is null)
        {
            return string.Empty;
        }

        return $"{contact.FirstName}{contact.LastName}";
    }

    private static bool ReplaceExistingTreasurer(SmoRegistrationContactDto request, IEnumerable<RegistrationRegistrationContact> contacts)
    {
        if (request.Id is null &&
            request.RoleId == FilerRole.SlateMailerOrg_Treasurer.Id &&
            contacts.Any(x => x.Role == FilerRole.SlateMailerOrg_Treasurer.Name))
        {
            var oldTreasurer = contacts.FirstOrDefault(contact => contact.Role == FilerRole.SlateMailerOrg_Treasurer.Name);

            if (oldTreasurer != null)
            {
                oldTreasurer.Active = false;

                return true;
            }
        }

        return false;
    }

    private static void TerminateSmoRegistration(SlateMailerOrganization registration, DateTime? terminationDate)
    {
        // Check the most recent (previous) registration has status Accepted
        if (registration.Parent is null || registration.Parent.StatusId != RegistrationStatus.Accepted.Id)
        {
            throw new InvalidOperationException($"The status of most recent registration is not {RegistrationStatus.Accepted}. FilerId={registration.FilerId.GetValueOrDefault()}");
        }

        registration.TerminatedAt = terminationDate;
    }

    private async Task<FilerUserDto> GetFilerUserAsync(SlateMailerOrganization existingRegistration)
    {
        // Need to find the Registration Contact through the Filer User
        // If we can associate User with Registration Contact, the condition is simplified
        // Get Filer User of the current user
        var userId = await servicesDependencies.AuthorizationSvc.GetInitiatingUserId();
        var filerUser = await servicesDependencies.FilerSvc.GetFilerUserByUserIdAsync(existingRegistration.FilerId.GetValueOrDefault(), userId.GetValueOrDefault())
            ?? throw new InvalidOperationException($"No filer user found with registration. Id={existingRegistration.Id}");
        return filerUser;
    }

    private async Task<(RegistrationRegistrationContact, IEnumerable<RegistrationRegistrationContact>)> GetMatchedAndAllRegistrationContactsAsync(long id, FilerUserDto filerUser)
    {
        // Get all contacts of SMO registration
        // Currently, we will do the comparison by name. In the future, we need another way to find it properly
        var registrationContacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(id);
        var matchedRegContact = registrationContacts.FirstOrDefault(x =>
            x.RegistrationContact?.UserId == filerUser.UserId)
            ?? throw new InvalidOperationException($"No contact matched with current user. Id={filerUser.UserId}");
        return (matchedRegContact, registrationContacts);
    }

    private async Task<RegistrationRegistrationContact?> GetRegistrationContactByUserId(long id, long userId)
    {
        var contacts = await repositoriesDependencies.RegistrationRepository.FindSmoRegistrationContactsById(id);
        return contacts.FirstOrDefault(x => x.RegistrationContact?.UserId == userId);
    }

    private async Task UpdateAcknowledgmentAsync(RegistrationRegistrationContact matchedRegContact)
    {
        // If user already acknowledged, skip
        if (matchedRegContact.HasAcknowledged.GetValueOrDefault())
        {
            return;
        }

        matchedRegContact.HasAcknowledged = true;
        matchedRegContact.AcknowledgedOn = servicesDependencies.DateTimeSvc.GetCurrentDateTime();

        await repositoriesDependencies.RegistrationRegistrationContactRepository.Update(matchedRegContact);
    }

    private async Task SendOfficerNotificationsAsync(long filerId, IEnumerable<RegistrationRegistrationContact> contacts, List<long> selectedContactIds, List<NotificationTrigger>? notifications)
    {
        if (notifications is null)
        {
            return;
        }

        // Send notification to selected contacts
        var selectedContacts = contacts
            .Where(x => selectedContactIds.Contains(x.Id))
            .Select(x => new SmoRegistrationContactDto
            {
                Id = x.Id,
                FirstName = x.RegistrationContact?.FirstName,
                LastName = x.RegistrationContact?.LastName,
                Role = x.Role,
                Title = x.Title,
                UserId = x.RegistrationContact?.UserId,
            });

        foreach (NotificationTrigger notification in notifications)
        {
            if (notification.SendNotification && notification.NotificationTemplateId is { } templateId)
            {
                await SendUserNotificationsAsync(filerId, selectedContacts, templateId);
            }
        }
    }

    private async Task CompleteRegistrationIfApplicableAsync(SlateMailerOrganization registration, IEnumerable<RegistrationRegistrationContact> registrationContacts)
    {
        // If the registration already attested and this is the last person to complete the treasurer acknowledgement
        // Change the status to Accepted/Terminated
        var hasUncompletedTreasurerAcknowledgement = HasUncompletedTreasurerAcknowledgementContacts(registrationContacts);
        if (!hasUncompletedTreasurerAcknowledgement && registration.Attestations?.Count > 0)
        {
            // Mark the registration approved
            registration.ApprovedAt = servicesDependencies.DateTimeSvc.GetCurrentDateTime();

            // If TerminatedAt has a value, it means the registration is terminating
            var statusId = registration.TerminatedAt.HasValue ? RegistrationStatus.Terminated.Id : RegistrationStatus.Accepted.Id;
            await UpdateRegistrationStatusAsync(registration, statusId);
        }
    }

    private async Task UpdateRegistrationSubmissionAsync(SlateMailerOrganization registration, bool isAttesting, string? decisionStatus, IEnumerable<RegistrationRegistrationContact> contacts)
    {
        // Mark the registration submitted
        registration.SubmittedAt = servicesDependencies.DateTimeSvc.GetCurrentDateTime();

        var statusId = RegistrationStatus.Pending.Id;
        if (!string.IsNullOrWhiteSpace(decisionStatus))
        {
            statusId = RegistrationStatusMapping.GetIdByName(decisionStatus);
        }

        if (isAttesting)
        {
            // Check uncomplete treasurer acknowledgement
            // If there is any pending items, change status to Pending
            // Otherwise, remain the status
            var hasUncompletedTreasurerAcknowledgement = HasUncompletedTreasurerAcknowledgementContacts(contacts);
            statusId = hasUncompletedTreasurerAcknowledgement ? RegistrationStatus.Pending.Id : statusId;
        }

        // Complete registration
        if (statusId == RegistrationStatus.Accepted.Id || statusId == RegistrationStatus.Terminated.Id)
        {
            // Mark the registration approved
            registration.ApprovedAt = servicesDependencies.DateTimeSvc.GetCurrentDateTime();
        }

        // Terminate the linkage when the amendment is Accepted.
        if (statusId == RegistrationStatus.Accepted.Id && registration.FilerId.HasValue)
        {
            // Use the output from this method for the notification template data
            var filerUsers = await repositoriesDependencies.FilerUserRepository.TerminateRegistrationAmendmentContactLinkages(registration.Id);
            await repositoriesDependencies.LinkageRequestRepository.CancelContactLinkageRequestsForRegistration(registration.ParentId);

            // Call decisions and notifications(SendToUser-59,SendToFiler-60) only for deleted FilerUsers.
            var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<string, DecisionResponse>(DecisionsWorkflow.SmoAttestationDelinkOfficersRuleSet, string.Empty);
            if (decisionResponse != null && decisionResponse.Notifications.Count > 0)
            {
                await SendDelinkNotificationsFromDecisions(filerUsers, decisionResponse.Notifications);
            }
        }

        // Update registration status
        await UpdateRegistrationStatusAsync(registration, statusId);
    }

    private async Task SendNotifications(List<NotificationTrigger> notifications, long userId, long filerId, Dictionary<string, string> userNotificationData, Dictionary<string, string> filerNotificationData)
    {
        foreach (var notification in notifications)
        {
            if (!notification.SendNotification || notification.NotificationTemplateId is null)
            {
                continue;
            }

            switch (notification.NotificationRecipient)
            {
                case NotificationRecipient.InitiatingUser:
                    await servicesDependencies.NotificationSvc.SendUserNotification(
                        new SendUserNotificationRequest(
                            notification.NotificationTemplateId.Value,
                            userId,
                            filerId,
                            null,
                            userNotificationData));
                    break;

                case NotificationRecipient.FilerUsers:
                    await servicesDependencies.NotificationSvc.SendFilerNotification(
                        new SendFilerNotificationRequest(
                            notification.NotificationTemplateId.Value,
                            filerId,
                            null,
                            filerNotificationData));
                    break;
                default:
                    break;
            }
        }
    }

    private async Task ValidateTerminationAsync(SlateMailerOrganization registration, NoticeOfTerminationRequest request, List<WorkFlowError> validationErrors)
    {
        var decisionsInput = new DecisionsSmoTermination
        {
            SubmissionDate = registration.Parent?.SubmittedAt,
            EffectiveDateOfTermination = request.TerminationDate
        };

        var decisionResponse = await servicesDependencies.DecisionsSvc.InitiateWorkflow<DecisionsSmoTermination, List<WorkFlowError>>(
            DecisionsWorkflow.SmoNoticeOfTerminationRuleSet,
            decisionsInput,
            checkRequiredFields: request.CheckRequiredFields,
            includeEmptyErrors: true
        );
        validationErrors.AddRange(decisionResponse);
    }

    private static DecisionsWorkflow GetDecisionsRuleSet(bool isAttesting, bool isTermination)
    {
        if (isTermination)
        {
            return isAttesting
                ? DecisionsWorkflow.SmoTerminationSubmitAttestationRuleSet
                : DecisionsWorkflow.SmoTerminationSendForAttestationRuleSet;
        }
        else
        {
            return isAttesting
                ? DecisionsWorkflow.SmoPostSubmissionRuleSet
                : DecisionsWorkflow.SmoSendForAttestationRuleSet;
        }
    }

    private async Task SendLinkageRequestsToContactsWithPermission(long registrationId, long permissionId)
    {
        var linkageRequests = await repositoriesDependencies.RegistrationRegistrationContactRepository
            .GetLinkageRequestRecipientsForPermission(registrationId, permissionId);
        foreach (var request in linkageRequests)
        {
            if (request.RecipientEmail is not null && request.RecipientName is not null)
            {
                await servicesDependencies.LinkageSvc.SendLinkageRequestToPerson(new()
                {
                    RecipientEmail = request.RecipientEmail,
                    RecipientName = request.RecipientName,
                    FilerId = request.FilerId,
                    FilerRoleId = request.FilerRoleId,
                    RegistrationContactId = request.RegistrationContactId,
                });
            }
        }
    }

    #endregion

}
