@using Microsoft.AspNetCore.Html
@using System.Text.Encodings.Web
@using SOS.CalAccess.FilerPortal.Models.Registrations.Form470S
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.Foundation.Utils
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization
@inject IDateTimeSvc DateTimeSvc

@inject IHtmlLocalizer<SharedResources> Localizer

@model Form470SPage01ViewModel
@{
    // FD-CF-470S
    var buttonBar = new ButtonBarModel
    {
        LeftButtons = new()
        {
            new()
            {
                Type = ButtonType.Button,
                Action = FormAction.Continue,
                CssClass = ButtonBarModel.ContinueCssClass,
                InnerTextKey = CommonResourceConstants.Submit,
            }
        },
        RightButtons = new()
        {
            new()
            {
                Type = ButtonType.Custom,
                HtmlContent = await Html.PartialAsync("_CancelDraftButton", Model.Id)
            },
            ButtonBarModel.DefaultSaveAndClose
        }
    };
}

 @Html.StepHeader(SharedLocalizer, ResourceConstants.Form470SPage01Title)
 @Html.TextBlock(SharedLocalizer, ResourceConstants.Form470SPage01Body)

 @using (Html.BeginForm("Page01", "Form470S", FormMethod.Post))
 {
     @Html.AntiForgeryToken()
     @Html.HiddenFor(m => m.FilerId)

     <partial name="_Form470OfficerHolderTable" model="@Model" />

     <div class="pb-3">
        <h4>@Localizer[ResourceConstants.Form470SPage01DateContributionsTitle].Value</h4>
        @Html.DatePickerFor(
            SharedLocalizer,
            m => m.DateContributions,
            Localizer[ResourceConstants.Form470SPage01DateContributionsBody].Value,
            minDate: null,
            maxDate: DateTimeSvc.GetCurrentDateTime(),
            format: "MM/dd/yyyy",
            isRequired: true,
            isReadOnly: false,
            cssClass: "datepicker-container",
            placeholderResourceKey: "MM/DD/YYYY")
     </div>

    <partial name="_ButtonBar" model="buttonBar" />
 }
