using Microsoft.AspNetCore.Mvc.ModelBinding;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistRegistration;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using RegistrationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.RegistrationResponseDto;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

public class LobbyistRegistrationCtlSvc(
    ILobbyistRegistrationSvc lobbyistRegistrationSvc,
    IUserMaintenanceSvc userMaintenanceSvc,
    IDecisionsValidationMapService decisionsValidationMapService,
    ISmoCampaignStatementSvc smoCampaignStatementSvc,
    IDateTimeSvc dateTimeSvc
    ) : ILobbyistRegistrationCtlSvc
{
    private const string BusinessAddress = "Business";
    private const string MailingAddress = "Mailing";

    public async Task<LobbyistRegistrationStep01ViewModel> GetPage03ViewModel(long? id, bool selfRegister, CancellationToken cancellationToken)
    {
        var model = new LobbyistRegistrationStep01ViewModel
        {
            Addresses = new List<AddressViewModel>
            {
                new() { Purpose = BusinessAddress, Country = "United States" },
                new() { Purpose = MailingAddress }
            },
        };

        if (selfRegister)
        {
            Services.Business.UserAccountMaintenance.Models.BasicUserDto userResponse = await userMaintenanceSvc.GetCurrentUser();
            model.FirstName = userResponse.FirstName;
            model.LastName = userResponse.LastName;
            model.Email = userResponse.Email;
            model.SelfRegister = selfRegister;
        }
        return model;
    }

    public LobbyistRegistrationWithdrawalViewModel MapWithdrawLobbyistRegistrationViewModel(LobbyistResponseDto dto)
    {
        var model = new LobbyistRegistrationWithdrawalViewModel()
        {
            Id = dto.Id,
            LegislativeSessionId = dto.LegislativeSessionId,
            LobbyistName = dto.Name,
            EffectiveDateOfWithdrawal = dto.WithdrawnAt,
            LobbyistFirmNameOrEmployer = dto.LobbyistEmployerOrLobbyingFirmName,
            BusinessAddress = GetBusinessAddress(dto.Addresses)
        };
        return model;
    }

    public LobbyistTerminationNoticeViewModel MapTerminateLobbyistRegistrationViewModel(LobbyistResponseDto dto)
    {
        var phoneNumber = dto.PhoneNumbers?.FirstOrDefault(x => x.Type == "Home");
        var faxNumber = dto.PhoneNumbers?.FirstOrDefault(x => x.Type == "Fax");

        return new LobbyistTerminationNoticeViewModel()
        {
            Id = dto.Id,
            Name = dto.Name,
            LobbyistEmployerOrLobbyingFirmName = dto.LobbyistEmployerOrLobbyingFirmName,
            FilerId = dto.FilerId,
            Email = dto.Email,
            PhoneNumber = phoneNumber?.ToString(),
            FaxNumber = faxNumber?.ToString(),
            EffectiveDateOfTermination = dto.TerminatedAt,
            LegislativeSessionId = dto.LegislativeSessionId,
            BusinessAddress = GetBusinessAddress(dto.Addresses)
        };
    }

    public async Task<LobbyistRegistrationStep03ViewModel> GetPage07ViewModel(long id, CancellationToken cancellationToken)
    {
        var registration = await lobbyistRegistrationSvc.GetLobbyistRegistration(id)
            ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {id} not found.");

        var model = new LobbyistRegistrationStep03ViewModel
        {
            Id = id,
            Email = registration.Email ?? "N/A",
            Name = registration.Name,
            ExecutedOn = dateTimeSvc.GetCurrentDateTime(),
            SelfRegister = (bool)registration.SelfRegister!,
            Version = registration.Version,
        };

        return model;
    }
    public ConfirmationViewModel Page08GetViewModel(long? id)
    {
        if (id is null)
        {
            throw new KeyNotFoundException($"No id exists for this registration");
        }

        return new ConfirmationViewModel
        {
            Id = id.GetValueOrDefault(),
            ExecutedOn = dateTimeSvc.GetCurrentDateTime(),
            IsSubmission = true,
        };
    }

    public async Task<long?> Page03Submit(LobbyistRegistrationStep01ViewModel model, ModelStateDictionary modelState, bool isSubmission = true)
    {
        LobbyistRegistrationRequestDto request = MapLobbyistViewModelToRequest(model);
        request.CheckRequiredFieldsFlag = isSubmission;

        RegistrationResponseDto response = await SubmitLobbyistRegistrationForm(model, modelState, request);

        return response.Id;
    }

    public LobbyistRegistrationRequestDto MapLobbyistViewModelToRequest(LobbyistRegistrationStep01ViewModel model)
    {
        AddressViewModel sourceBusinessAddress = model.Addresses.FirstOrDefault(x => x.Purpose == BusinessAddress) ?? new AddressViewModel();
        AddressViewModel sourceMailingAddress = model.Addresses.FirstOrDefault(x => x.Purpose == MailingAddress) ?? new AddressViewModel();

        AddressDto businessAddress = sourceBusinessAddress.ToAddressDto();

        AddressDto mailingAddress = model.IsSameAsBusinessAddress
            ? businessAddress
            : sourceMailingAddress.ToAddressDto();

        LobbyistRegistrationRequestDto request = new()
        {
            FirstName = model.FirstName,
            MiddleName = model.MiddleName,
            LastName = model.LastName,
            Email = model.Email,
            SelfRegister = model.SelfRegister,
            EffectiveDateOfChanges = model.EffectiveDateOfChanges,
            PhoneNumber = GetFullPhoneNumber(model.PhoneNumberCountryCode ?? "", model.PhoneNumber ?? ""),
            FaxNumber = GetFullPhoneNumber(model.FaxNumberCountryCode ?? "", model.FaxNumber ?? ""),
            LobbyistEmployerOrLobbyingFirmId = model.LobbyistEmployerOrLobbyingFirmId,
            BusinessAddress = businessAddress,
            MailingAddress = mailingAddress,
            LegislativeSessionId = model.LegislativeSessionId,
            DateOfQualification = model.QualificationDate,
            IsPlacementAgent = model.PlacementAgent,
            CompletedEthicsCourse = model.EthicsCourseCompleted,
            CompletedCourseDate = model.EthicsCourseCompletedOn,
            LobbyOnlySpecifiedAgencies = model.LobbyOnlySpecifiedAgencies,
            Agencies = model.SelectedAgencies?.Count > 0 ? [.. model.SelectedAgencies.Select(x => new RegistrationAgencyDto
            {
                RegistrationId = model.Id.GetValueOrDefault(),
                AgencyId = x
            })] : [],
            IsLobbyingStateLegislature = model.IsLobbyingStateLegislature,
            IsSameAsCandidateAddress = model.IsSameAsBusinessAddress,
            IsNewCertification = model.IsNewCertification,
            Photo = model.UploadedFilename
        };

        return request;
    }

    /// <summary>
    /// Handler for creating a new or updating an existing lobbyist registration record 
    /// </summary>
    public async Task<RegistrationResponseDto> SubmitLobbyistRegistrationForm(LobbyistRegistrationStep01ViewModel model, ModelStateDictionary modelState, LobbyistRegistrationRequestDto request)
    {
        RegistrationResponseDto response;

        if (request.SelfRegister == null)
        {
            request.SelfRegister = false;
        }

        if (model.Id is null or 0 || model.IsNewCertification == false)
        {
            response = await lobbyistRegistrationSvc.CreateLobbyistRegistrationPage03(request);
        }
        else
        {
            response = await lobbyistRegistrationSvc.UpdateLobbyistRegistration(model.Id.GetValueOrDefault(), request);
        }

        if (!response.Valid)
        {
            decisionsValidationMapService.ApplyErrorsToModelState(GetLobbyistFieldValidationMap(model), response.ValidationErrors, modelState);
        }

        return response;
    }
    /// <summary>
    /// Handler for lobbyist registration final submission or sending for attestation
    /// </summary>
    public async Task<RegistrationResponseDto> SubmitLobbyistRegistration(LobbyistRegistrationStep03ViewModel model)
    {
        return await lobbyistRegistrationSvc.SubmitLobbyistRegistration(model.Id);
    }

    public async Task<LobbyistVerificationViewModel> GetLobbyistVerificationViewModel(long id)
    {
        var registration = await lobbyistRegistrationSvc.GetRegistrationById(id)
            ?? throw new KeyNotFoundException($"Lobbyist registration with Filer ID {id} not found.");

        return new LobbyistVerificationViewModel
        {
            Id = id,
            Email = registration.Email ?? "N/A",
            FirstName = registration.FirstName ?? registration!.Name,
            LastName = registration.LastName,
            ExecutedOn = dateTimeSvc.GetCurrentDateTime(),
            LegislativeSessionId = registration.LegislativeSessionId,
            EffectiveDateOfTermination = registration.TerminatedAt,
            EffectiveDateOfWithdrawal = registration.WithdrawnAt,
            Type = registration.Type
        };
    }

    public void MappingViewModelFromDto(LobbyistRegistrationStep01ViewModel model, Generated.LobbyistResponseDto dto)
    {
        model.Id = dto.Id;
        model.Version = dto.Version;
        model.Email = dto.Email ?? string.Empty;

        if (dto.Addresses != null)
        {
            model.Addresses = [.. model.Addresses.Select(x =>
            {
                var address = dto.Addresses.FirstOrDefault(d => d.Purpose == x.Purpose);
                if (address is not null)
                {
                    x.Country = address.Country;
                    x.Street = address.Street;
                    x.Street2 = address.Street2;
                    x.City = address.City;
                    x.State = address.State;
                    x.Zip = address.Zip;
                }
                return x;
            })];
        }

        if (dto.PhoneNumbers != null)
        {
            var phoneNumber = dto.PhoneNumbers.FirstOrDefault(x => x.Type == "Home");
            var faxNumber = dto.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax");
            model.PhoneNumber = string.IsNullOrWhiteSpace(phoneNumber?.Number) ? string.Empty : phoneNumber.Number;
            model.PhoneNumberCountryCode = string.IsNullOrWhiteSpace(phoneNumber?.CountryCode) ? string.Empty : phoneNumber.CountryCode;
            model.FaxNumber = string.IsNullOrWhiteSpace(faxNumber?.Number) ? string.Empty : faxNumber.Number;
            model.FaxNumberCountryCode = string.IsNullOrWhiteSpace(faxNumber?.CountryCode) ? string.Empty : faxNumber.CountryCode;
        }

        model.SelfRegister = dto.SelfRegister;
        model.FirstName = dto.FirstName;
        model.MiddleName = dto.MiddleName;
        model.LastName = dto.LastName;
        model.Email = dto.Email;
        model.LobbyistEmployerOrLobbyingFirmId = dto.LobbyistEmployerOrLobbyingFirmId ?? null;
        model.LegislativeSessionId = dto.LegislativeSessionId;
        model.PlacementAgent = dto.IsPlacementAgent;
        model.IsSameAsBusinessAddress = dto.IsSameAsCandidateAddress;
        model.SelectedAgencies = [.. dto.Agencies.Select(x => x.AgencyId!.Value)];
        model.QualificationDate = dto?.DateOfQualification;
        model.EthicsCourseCompleted = dto?.CompletedEthicsCourse;
        model.EthicsCourseCompletedOn = dto?.CompletedCourseDate;
        model.LobbyOnlySpecifiedAgencies = dto?.LobbyOnlySpecifiedAgencies;
        model.IsLobbyingStateLegislature = dto?.IsLobbyingStateLegislature;
        model.IsNewCertification = dto?.IsNewCertification;
        //model.Photo = dto.Photo;
    }

    public async Task<long> CreateAmendLobbyistRegistration(long id)
    {
        LobbyistResponseDto response = await lobbyistRegistrationSvc.CreateLobbyistAmendmentRegistrationAsync(id);

        return response.Id;
    }

    public async Task<LobbyistConfirmationViewModel?> GetConfirmationViewModel(long filingId)
    {
        var model = new LobbyistConfirmationViewModel
        {
            Id = filingId,
            ExecutedOn = dateTimeSvc.GetCurrentDateTime(),
            IsSubmission = true,
            PendingItems = new List<PendingItemSharedViewModel>()
            {
                new()
                {
                    Item = "Lobbying firm notified",
                    Status = "Complete"
                },
                new()
                {
                    Item = "Submit final activity report",
                    Status = "In Progress"
                }
            },
        };
        return await Task.FromResult(model);
    }


    #region Private
    private static string? GetBusinessAddress(List<AddressDtoModel> addresses)
    {
        var businessAddr = addresses.FirstOrDefault(d => d.Purpose == BusinessAddress);
        var addressDto = new AddressViewModel()
        {
            City = businessAddr?.City,
            Country = businessAddr?.Country,
            Purpose = businessAddr?.Purpose,
            State = businessAddr?.State,
            Street = businessAddr?.Street,
            Street2 = businessAddr?.Street2,
            Type = businessAddr?.Type,
            Zip = businessAddr?.Zip,
        };
        var addressParts = new List<string?>();
        addressParts.AddRange(addressDto.ToAddressParts(UI.Common.Enums.AddressDisplayMode.Vertical));
        addressParts.Add(addressDto.Country);

        return businessAddr == null ? null : string.Join("\n", addressParts.Where(x => !string.IsNullOrWhiteSpace(x)));
    }

    //TODO: replace when model directly uses PhoneNumberDto
    private static PhoneNumberDto? GetFullPhoneNumber(string countryCode, string number)
    {
        if (!string.IsNullOrEmpty(number))
        {
            string code = string.IsNullOrEmpty(countryCode) ? "" : countryCode.Split('-')[0];
            return new()
            {
                Id = 0,
                CountryCode = code ?? string.Empty,
                SelectedCountry = 0,
                InternationalNumber = false,
                Number = number,
                Extension = null,
                Type = string.Empty,
            };

        }
        return null;
    }

    /// <inheritdoc/>
    private static Dictionary<string, FieldProperty> GetLobbyistFieldValidationMap(LobbyistRegistrationStep01ViewModel model)
    {
        int businessAddressIndex = model.Addresses.FindIndex(x => x.Purpose == BusinessAddress);
        int mailingAddressIndex = model.Addresses.FindIndex(x => x.Purpose == MailingAddress);

        return new()
        {
            { "EffectiveDateOfChanges", new FieldProperty("EffectiveDateOfChanges", ResourceConstants.LobbyistRegistration03EffectiveDateOfChanges) },
            { "Name", new FieldProperty("FirstName", ResourceConstants.LobbyistRegistration03FirstName) },
            { "FirstName", new FieldProperty("FirstName", ResourceConstants.LobbyistRegistration03FirstName) },
            { "MiddleName", new FieldProperty("MiddleName", ResourceConstants.LobbyistRegistration03MiddleName) },
            { "LastName", new FieldProperty("LastName", ResourceConstants.LobbyistRegistration03LastName) },
            { "Email", new FieldProperty("Email", ResourceConstants.LobbyistRegistration03Email) },
            { "PhoneNumber", new FieldProperty("PhoneNumber", ResourceConstants.LobbyistRegistration03PhoneNumber) },
            { "FaxNumber", new FieldProperty("FaxNumber", ResourceConstants.LobbyistRegistration03FaxNumber) },

            { "OrganizationAddress.Country", new FieldProperty($"Addresses[{businessAddressIndex}].Country", ResourceConstants.LobbyistRegistration03Country) },
            { "OrganizationAddress.Street", new FieldProperty($"Addresses[{businessAddressIndex}].Street", ResourceConstants.LobbyistRegistration03Street) },
            { "OrganizationAddress.Street2", new FieldProperty($"Addresses[{businessAddressIndex}].Street2", ResourceConstants.LobbyistRegistration03Street2) },
            { "OrganizationAddress.City", new FieldProperty($"Addresses[{businessAddressIndex}].City", ResourceConstants.LobbyistRegistration03City) },
            { "OrganizationAddress.State", new FieldProperty($"Addresses[{businessAddressIndex}].State", ResourceConstants.LobbyistRegistration03State) },
            { "OrganizationAddress.Zip", new FieldProperty($"Addresses[{businessAddressIndex}].Zip", ResourceConstants.LobbyistRegistration03Zip) },
            { "LobbyingEmployerOrFirmId", new FieldProperty("LobbyistEmployerOrLobbyingFirmId", ResourceConstants.LobbyistRegistration03LobbyistEmployerOrLobbyingFirm) },

            { "MailingAddress.Country", new FieldProperty($"Addresses[{mailingAddressIndex}].Country", ResourceConstants.LobbyistRegistration03Country) },
            { "MailingAddress.Street", new FieldProperty($"Addresses[{mailingAddressIndex}].Street", ResourceConstants.LobbyistRegistration03Street) },
            { "MailingAddress.Street2", new FieldProperty($"Addresses[{mailingAddressIndex}].Street2", ResourceConstants.LobbyistRegistration03Street2) },
            { "MailingAddress.City", new FieldProperty($"Addresses[{mailingAddressIndex}].City", ResourceConstants.LobbyistRegistration03City) },
            { "MailingAddress.State", new FieldProperty($"Addresses[{mailingAddressIndex}].State", ResourceConstants.LobbyistRegistration03State) },
            { "MailingAddress.Zip", new FieldProperty($"Addresses[{mailingAddressIndex}].Zip", ResourceConstants.LobbyistRegistration03Zip) },

            { "IsSameAsBusinessAddress", new FieldProperty("IsSameAsBusinessAddress", ResourceConstants.LobbyistRegistration03SameAs) },
            { "LegislativeSession", new FieldProperty("LegislativeSessionId", ResourceConstants.LobbyistRegistration03LegislativeSession) },
            { "DateOfQualification", new FieldProperty("QualificationDate", ResourceConstants.LobbyistRegistration03QualificationDate) },
            { "IsPlacementAgent", new FieldProperty("PlacementAgent", ResourceConstants.LobbyistRegistration03PlacementAgent) },
            { "CompletedEthicsCourse", new FieldProperty("EthicsCourseCompleted", ResourceConstants.LobbyistRegistration03EthicsCourse) },
            { "CompletedCourseDate", new FieldProperty("EthicsCourseCompletedOn", "Date Completed") },
            { "AgenciesLobbied", new FieldProperty("LobbyOnlySpecifiedAgencies", ResourceConstants.LobbyistRegistration03AgenciesLobbied) },
            { "Agencies", new FieldProperty("Agency", ResourceConstants.LobbyistRegistration03StateAgenciesLobbied) },
            { "IsLobbyingStateLegislature", new FieldProperty("IsLobbyingStateLegislature", ResourceConstants.LobbyistRegistration03WillYouLobby) },
            { "Photo", new FieldProperty("UploadedFilename", ResourceConstants.LobbyistRegistration03Upload) },
        };
    }

    #endregion
}
