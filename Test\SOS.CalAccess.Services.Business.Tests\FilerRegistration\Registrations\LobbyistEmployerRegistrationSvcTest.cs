using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Lobbyists;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Lobbyists;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;

[TestFixture]
public class LobbyistEmployerRegistrationSvcTests
{
    private IDecisionsSvc _decisionsSvcMock;
    private IRegistrationRepository _registrationRepositoryMock;
    private IRegistrationRegistrationContactRepository _registrationRegistrationContactRepositoryMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private IFilerLinkRepository _filerLinkRepositoryMock;
    private LobbyistEmployerRegistrationSvcDependencies _dependencies;
    private IReferenceDataSvc _referenceDataSvcMock;
    private IRegistrationModelMapper _modelMapper;
    private ILinkageSvc _linkageSvc;
    private IAttestationRepository _attestationRepository;
    private ILobbyingInterestRepository _lobbyingInterestRepository;
    private LobbyistEmployerRegistrationSvc _service;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _registrationRegistrationContactRepositoryMock = Substitute.For<IRegistrationRegistrationContactRepository>();
        _filerLinkRepositoryMock = Substitute.For<IFilerLinkRepository>();
        _referenceDataSvcMock = Substitute.For<IReferenceDataSvc>();
        _linkageSvc = Substitute.For<ILinkageSvc>();
        _attestationRepository = Substitute.For<IAttestationRepository>();
        _lobbyingInterestRepository = Substitute.For<ILobbyingInterestRepository>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _modelMapper = Substitute.For<IRegistrationModelMapper>();

        _dependencies = new LobbyistEmployerRegistrationSvcDependencies
        (
            _decisionsSvcMock,
            _authorizationSvcMock,
            _filerSvcMock,
            _notificationSvcMock,
            _userMaintenanceSvcMock,
            _registrationRepositoryMock,
            _registrationRegistrationContactRepositoryMock,
            _filerLinkRepositoryMock,
            _linkageSvc,
            _attestationRepository,
            _lobbyingInterestRepository
        );

        _service = new LobbyistEmployerRegistrationSvc(_dependencies, _modelMapper);
    }

    [Test]
    public async Task GetLobbyistEmployer_ReturnsLobbyistEmployerResponseDto_WhenLobbyistEmployerExists()
    {
        // Arrange
        var lobbyistEmployer = new LobbyistEmployer
        {
            Id = 1,
            Name = "Test Lobbyist Employer",
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = 1,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Type",
            BusinessActivity = "Activity",
            BusinessDescription = "Description",
            InterestType = "Interest",
            IndustryDescription = "Industry Description",
            IndustryPortion = "Portion",
            NumberOfMembers = 10,
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            LegislativeSessionId = 1,
            AddressList = new AddressList(),
            PhoneNumberList = new PhoneNumberList(),
        };

        _ = _dependencies.RegistrationRepository.FindLobbyistEmployerById(Arg.Any<long>()).Returns(lobbyistEmployer);

        // Act
        var result = await _service.GetLobbyistEmployer(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(lobbyistEmployer.Id));
            Assert.That(result.Name, Is.EqualTo(lobbyistEmployer.Name));
            Assert.That(result.Email, Is.EqualTo(lobbyistEmployer.Email));
        });
    }

    [Test]
    public void GetLobbyistEmployer_ThrowsKeyNotFoundException_WhenLobbyistEmployerDoesNotExist()
    {
        // Arrange
        _ = _dependencies.RegistrationRepository.FindLobbyistEmployerById(Arg.Any<long>()).Returns((LobbyistEmployer?)null);

        // Act & Assert
        _ = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetLobbyistEmployer(1));
    }

    [Test]
    public void GetLobbyistEmployerByFilerId_ThrowsKeyNotFoundException_WhenLobbyistEmployerDoesNotExist()
    {
        // Arrange
        _ = _dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<LobbyistEmployer>(Arg.Any<long>())
            .Returns((LobbyistEmployer?)null);

        // Act & Assert
        _ = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetLobbyistEmployerByFilerId(1));
    }

    [Test]
    public async Task GetLobbyistEmployerByFilerId_ReturnsLobbyistEmployerResponseDto_WhenLobbyistEmployerExists()
    {
        // Arrange
        var filerId = 1;
        var lobbyistEmployer = new LobbyistEmployer
        {
            Id = 1,
            Name = "Test Lobbyist Employer",
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = filerId,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Type",
            BusinessActivity = "Activity",
            BusinessDescription = "Description",
            InterestType = "Interest",
            IndustryDescription = "Industry Description",
            IndustryPortion = "Portion",
            NumberOfMembers = 10,
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            LegislativeSessionId = 1,
            AddressList = new AddressList(),
            PhoneNumberList = new PhoneNumberList(),
        };

        _ = _dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<LobbyistEmployer>(Arg.Any<long>())
            .Returns(lobbyistEmployer);

        // Act
        var result = await _service.GetLobbyistEmployerByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(lobbyistEmployer.Id));
            Assert.That(result.FilerId, Is.EqualTo(lobbyistEmployer.FilerId));
            Assert.That(result.Name, Is.EqualTo(lobbyistEmployer.Name));
        });
    }

    [Test]
    public async Task GetLobbyistEmployerByName_ReturnsLobbyistEmployerResponseDto_WhenLobbyistEmployerExists()
    {
        // Arrange
        var lobbyistEmployer = new LobbyistEmployer
        {
            Id = 1,
            Name = "Test Lobbyist Employer",
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = 1,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Type",
            BusinessActivity = "Activity",
            BusinessDescription = "Description",
            InterestType = "Interest",
            IndustryDescription = "Industry Description",
            IndustryPortion = "Portion",
            NumberOfMembers = 10,
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            LegislativeSessionId = 1,
            AddressList = new AddressList(),
            PhoneNumberList = new PhoneNumberList(),
        };

        // Arrange
        _ = _dependencies.RegistrationRepository.FindLobbyistEmployerByName(Arg.Any<string>()).Returns(lobbyistEmployer);

        // Act
        var result = await _service.GetLobbyistEmployerByName("Test Lobbyist Employer");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(lobbyistEmployer.Id));
            Assert.That(result.Name, Is.EqualTo(lobbyistEmployer.Name));
            Assert.That(result.Email, Is.EqualTo(lobbyistEmployer.Email));
        });
    }

    [Test]
    public void GetLobbyistEmployerByName_ThrowsKeyNotFoundException_WhenLobbyistEmployerDoesNotExist()
    {
        // Arrange
        _ = _dependencies.RegistrationRepository.FindLobbyistEmployerByName(Arg.Any<string>()).Returns((LobbyistEmployer?)null);

        // Act & Assert
        _ = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.GetLobbyistEmployerByName("test"));
    }

    [Test]
    public void CancelLobbyistEmployerRegistration_ShouldThrowExceptionWhenRecordNotFound()
    {
        // Arrange
        long id = 1;
        _ = _dependencies.RegistrationRepository.FindLobbyistEmployerById(Arg.Any<long>()).Returns(Task.FromResult(default(LobbyistEmployer)));

        // Act
        // Assert
        _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(KeyNotFoundException)), async () => await _service.CancelLobbyistEmployerRegistration(id));
    }

    [Test]
    public void CancelLobbyistEmployerRegistration_ShouldThrowExceptionWhenRecordStatusIsNotDraft()
    {
        // Arrange
        long id = 1;
        var record = new LobbyistEmployer
        {
            Name = "Name",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Submitted.Id
        };
        _ = _dependencies.RegistrationRepository.FindLobbyistEmployerById(Arg.Any<long>()).Returns(record);

        // Act
        // Assert
        _ = Assert.ThrowsAsync(Is.AssignableTo(typeof(InvalidOperationException)), async () => await _service.CancelLobbyistEmployerRegistration(id));
    }

    [Test]
    public void CancelLobbyistEmployerRegistration_ShouldSucceedWhenStatusIsDraft()
    {
        // Arrange
        long id = 1;
        var record = new LobbyistEmployer
        {
            Name = "Name",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Draft.Id
        };
        _ = _dependencies.RegistrationRepository.FindLobbyistEmployerById(Arg.Any<long>()).Returns(record);

        // Act
        // Assert
        Assert.DoesNotThrowAsync(async () => await _service.CancelLobbyistEmployerRegistration(id));
        _dependencies.RegistrationRepository.Received()
            .Update(Arg.Any<LobbyistEmployer>());
    }

    [Test]
    public async Task CreateLobbyistEmployerRegistration_ValidRequest_ReturnsSuccessfulResponse()
    {
        // Arrange
        var sampleBusinessAddress = new AddressDto { Type = "sampleType", Street = "123 Road", Street2 = "APT A", City = "Test City", State = "HI", Country = "US", Zip = "12345", Purpose = "Business" };
        var sampleMailAddress = new AddressDto { Type = "sampleType", Street = "456 Road", Street2 = "APT B", City = "Sample City", State = "HI", Country = "US", Zip = "67890", Purpose = "Mailing" };
        var sampleRegistrationAgencies = new List<RegistrationAgencyDto>()
        {
            new() { AgencyId = 1 },
            new() { AgencyId = 2 }
        };
        var request = new LobbyistEmployerGeneralInfoRequest { BusinessAddress = sampleBusinessAddress, MailingAddress = sampleMailAddress, CheckRequiredFieldsFlag = true, EmployerName = "Test LobbyistEmployer" };
        var mappedLobbyistEmployer = new LobbyistEmployer
        {
            Name = "Test LobbyistEmployer",
            Email = "<EMAIL>",
            StatusId = 1,
            AddressList = new AddressList
            {
                Id = 50,
                Addresses = new List<Address>
            {
                new()
                {
                    Id = 501,
                    Type = "sampleType", Street = "456 Road", Street2 = "APT B", City = "Sample City", State = "HI", Country = "US", Zip = "67890", Purpose = "Mailing"
                },
                new()
                {
                    Id = 502,
                    Type = "sampleType", Street = "456 Road", Street2 = "APT B", City = "Sample City", State = "HI", Country = "US", Zip = "67890", Purpose = "Mailing"
                }
            }
            },
        };
        var updatedMappedLobbyistEmployer = new LobbyistEmployer
        {
            Name = "Test LobbyistEmployer",
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = 1,
            AddressList = new AddressList
            {
                Id = 50,
                Addresses = new List<Address>
                {
                    new()
                    {
                        Id = 501,
                        Type = "sampleType", Street = "456 Road", Street2 = "APT B", City = "Sample City", State = "HI", Country = "US", Zip = "67890", Purpose = "Mailing"
                    },
                                    new()
                    {
                        Id = 502,
                        Type = "sampleType", Street = "456 Road", Street2 = "APT B", City = "Sample City", State = "HI", Country = "US", Zip = "67890", Purpose = "Mailing"
                    }
                }
            }
        };

        // Simulated successful registration response (without mocking Registration)
        var expectedResponse = new RegistrationResponseDto(123, true, new List<WorkFlowError>(), 1);

        _modelMapper.MapLobbyistEmployerGeneralInfoToModel(request).Returns(mappedLobbyistEmployer);
        _decisionsSvcMock
        .InitiateWorkflow<DecisionsLobbyistEmployerGeneralInfo, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(),
            Arg.Any<DecisionsLobbyistEmployerGeneralInfo>(),
            true)
        .Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors
        _authorizationSvcMock
        .GetInitiatingUserId()
        .Returns(Task.FromResult<long?>(1));
        _filerSvcMock
        .AddFilerAsync(Arg.Any<Filer>())
        .Returns(Task.FromResult(1L));

        _registrationRepositoryMock.IsLobbyistEmployerNameUnique(mappedLobbyistEmployer.Name).Returns(Task.FromResult(true)); // Simulate valid name

        // Simulate repository returning a successful response
        _registrationRepositoryMock.Create(mappedLobbyistEmployer)
        .Returns(Task.FromResult<Registration>(mappedLobbyistEmployer));
        _registrationRepositoryMock.Update(updatedMappedLobbyistEmployer)
        .Returns(Task.FromResult<Registration>(updatedMappedLobbyistEmployer));

        // Act
        var result = await _service.CreateLobbyistEmployerRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilerId, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task CreateLobbyistEmployerRegistration_InvalidRequest_ReturnsValidationErrors()
    {
        // Arrange
        var request = new LobbyistEmployerGeneralInfoRequest { CheckRequiredFieldsFlag = true };
        var mappedOrg = new LobbyistEmployer { Name = "Test Org", Email = "<EMAIL>", StatusId = 1 };
        var errors = new List<WorkFlowError> { new("Email", "Err001", "Validation", "Invalid email format") };

        _modelMapper.MapLobbyistEmployerGeneralInfoToModel(request).Returns(mappedOrg);
        _decisionsSvcMock
            .InitiateWorkflow<DecisionsLobbyistEmployerGeneralInfo, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsLobbyistEmployerGeneralInfo>(),
                true)
            .Returns(Task.FromResult(errors)); // Workflow returns validation errors

        // Act
        var result = await _service.CreateLobbyistEmployerRegistrationPage03(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Valid, Is.False);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.Null);
            Assert.That(result.ValidationErrors, Is.EqualTo(errors));
        });
    }

    [TestCaseSource(nameof(UpdateLobbyistEmployeeRegistrationTestCases))]
    public async Task UpdateLobbyistEmployeeRegistration_ValidRequest_ReturnResult(LobbyistEmployer lobbyistEmployer, long registrationId)
    {
        // Arrange
        var request = new LobbyistEmployerGeneralInfoRequest
        {
            EmployerName = "Test LobbyistEmployer",
            CheckRequiredFieldsFlag = true,
        };

        _registrationRepositoryMock.FindLobbyistEmployerById(Arg.Any<long>()).Returns(Task.FromResult<LobbyistEmployer?>(lobbyistEmployer));
        _modelMapper.UpdateLobbyistEmployerGeneralInfo(lobbyistEmployer, request).Returns(lobbyistEmployer);
        _registrationRepositoryMock.Update(Arg.Any<LobbyistEmployer>()).Returns(Task.FromResult<Registration>(lobbyistEmployer));
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistEmployerGeneralInfo, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsLobbyistEmployerGeneralInfo>(),
                true).Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors
        _registrationRepositoryMock.IsLobbyistEmployerNameUnique(request.EmployerName).Returns(Task.FromResult(true)); // Simulate valid name

        // Act
        var result = await _service.UpdateLobbyistEmployerRegistrationPage03(registrationId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
        });
    }

    [TestCaseSource(nameof(UpdateLobbyistEmployeeRegistrationTestCases))]
    public async Task UpdateLobbyistEmployeeRegistrationPage04_ValidRequest_ReturnResult(LobbyistEmployer lobbyistEmployer, long registrationId)
    {
        // Arrange
        var request = new LobbyistEmployerStateAgenciesRequest
        {
            Agencies = new()
            {
                new RegistrationAgencyDto{ AgencyId = 1, AgencyName = "dummy agency", RegistrationId = 1 },
                new RegistrationAgencyDto{ AgencyId = 2, AgencyName = "dummy agency 2", RegistrationId = 1 }
            },
            IsLobbyingStateLegislature = true,
            CheckRequiredFieldsFlag = true,
        };

        _registrationRepositoryMock.FindLobbyistEmployerById(Arg.Any<long>()).Returns(Task.FromResult<LobbyistEmployer?>(lobbyistEmployer));
        _modelMapper.UpdateLobbyistEmployerStateAgencies(lobbyistEmployer, request).Returns(lobbyistEmployer);
        _registrationRepositoryMock.Update(Arg.Any<LobbyistEmployer>()).Returns(Task.FromResult<Registration>(lobbyistEmployer));
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistEmployerStateAgencies, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsLobbyistEmployerStateAgencies>(),
                true).Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors

        // Act
        var result = await _service.UpdateLobbyistEmployerRegistrationPage04(registrationId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
        });
    }

    [TestCaseSource(nameof(UpdateLobbyistEmployeeRegistrationTestCases))]
    public async Task UpdateLobbyistEmployeeRegistrationPage05_ValidRequest_ReturnResult(LobbyistEmployer lobbyistEmployer, long registrationId)
    {
        // Arrange
        var request = new LobbyistEmployerLobbyingInterestsRequest
        {
            FilingInterestsDescription = "Some lobbying interest",
            CheckRequiredFieldsFlag = true,
        };

        _registrationRepositoryMock.FindLobbyistEmployerById(Arg.Any<long>()).Returns(Task.FromResult<LobbyistEmployer?>(lobbyistEmployer));
        _modelMapper.UpdateLobbyistEmployerLobbyingInterests(lobbyistEmployer, request).Returns(lobbyistEmployer);
        _registrationRepositoryMock.Update(Arg.Any<LobbyistEmployer>()).Returns(Task.FromResult<Registration>(lobbyistEmployer));
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistEmployerLobbyingInterests, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsLobbyistEmployerLobbyingInterests>(),
                true).Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors

        // Act
        var result = await _service.UpdateLobbyistEmployerRegistrationPage05(registrationId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
        });
    }

    [TestCaseSource(nameof(UpdateLobbyistEmployeeRegistrationTestCases))]
    public async Task UpdateLobbyistEmployeeRegistrationPage05_ValidRequest_HasInterest_ReturnResult(LobbyistEmployer lobbyistEmployer, long registrationId)
    {
        lobbyistEmployer.LobbyingInterestId = 1;
        lobbyistEmployer.LobbyingInterest = new()
        {
            Id = 1,
            Name = "Testing"
        };

        // Arrange
        var request = new LobbyistEmployerLobbyingInterestsRequest
        {
            FilingInterestsDescription = "Some lobbying interest",
            CheckRequiredFieldsFlag = true,
        };

        _registrationRepositoryMock.FindLobbyistEmployerById(Arg.Any<long>()).Returns(Task.FromResult<LobbyistEmployer?>(lobbyistEmployer));
        _modelMapper.UpdateLobbyistEmployerLobbyingInterests(lobbyistEmployer, request).Returns(lobbyistEmployer);
        _registrationRepositoryMock.Update(Arg.Any<LobbyistEmployer>()).Returns(Task.FromResult<Registration>(lobbyistEmployer));
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistEmployerLobbyingInterests, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsLobbyistEmployerLobbyingInterests>(),
                true).Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors
        _lobbyingInterestRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<LobbyingInterest?>(new LobbyingInterest() { Id = 999, Name = "Testing Old" }));
        _lobbyingInterestRepository.Delete(Arg.Any<LobbyingInterest>()).Returns(Task.FromResult(true));

        // Act
        var result = await _service.UpdateLobbyistEmployerRegistrationPage05(registrationId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
        });
    }

    [TestCaseSource(nameof(UpdateLobbyistEmployeeRegistrationTestCases))]
    public void UpdateLobbyistEmployeeRegistrationPage05_ValidRequest_HasNoInterest_ReturnsKeyNotFound(LobbyistEmployer lobbyistEmployer, long registrationId)
    {
        lobbyistEmployer.LobbyingInterestId = 1;
        lobbyistEmployer.LobbyingInterest = new()
        {
            Id = 1,
            Name = "Testing"
        };

        // Arrange
        var request = new LobbyistEmployerLobbyingInterestsRequest
        {
            FilingInterestsDescription = "Some lobbying interest",
            CheckRequiredFieldsFlag = true,
        };

        _registrationRepositoryMock.FindLobbyistEmployerById(Arg.Any<long>()).Returns(Task.FromResult<LobbyistEmployer?>(lobbyistEmployer));
        _modelMapper.UpdateLobbyistEmployerLobbyingInterests(lobbyistEmployer, request).Returns(lobbyistEmployer);
        _registrationRepositoryMock.Update(Arg.Any<LobbyistEmployer>()).Returns(Task.FromResult<Registration>(lobbyistEmployer));
        _decisionsSvcMock.InitiateWorkflow<DecisionsLobbyistEmployerLobbyingInterests, List<WorkFlowError>>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsLobbyistEmployerLobbyingInterests>(),
                true).Returns(Task.FromResult(new List<WorkFlowError>())); // No validation errors
        _lobbyingInterestRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<LobbyingInterest?>(null));

        // Act
        _ = Assert.ThrowsAsync<KeyNotFoundException>(() => _service.UpdateLobbyistEmployerRegistrationPage05(registrationId, request));
    }

    [Test]
    public async Task GetLobbyistRegistrationByEmployerRegistration_InvalidId_ReturnEmptyData()
    {
        // Arrange
        long id = -1;

        // Act
        var result = await _service.GetLobbyistRegistrationByEmployerRegistration(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<InHouseLobbyistResponseDto>>());
            Assert.That(result, Has.Count.EqualTo(0));
        });
    }

    [Test]
    public async Task GetLobbyistRegistrationByEmployerRegistration_InvalidId_ReturnData()
    {
        // Arrange
        long id = 1;
        var lobbyists = new List<Lobbyist>
        {
                new ()
                {
                    Id = 123,
                    FilerId = 2,
                    StatusId = 3,
                    Name = "Regression Test",
                    FirstName = "Regression",
                    LastName = "Test"
                },
                new ()
                {
                    Id = 321,
                    FilerId = 2,
                    StatusId = 1,
                    Name = "Unit Test",
                    FirstName = "Unit",
                    LastName = "Test"
                }

        };

        _registrationRepositoryMock.FindLobbyistRegistrationByEmployer(Arg.Any<long>()).Returns(Task.FromResult<List<Lobbyist>?>(lobbyists));
        // Act
        var result = await _service.GetLobbyistRegistrationByEmployerRegistration(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<InHouseLobbyistResponseDto>>());
            Assert.That(result, Has.Count.EqualTo(2));
            Assert.That(result[0].Id, Is.EqualTo(lobbyists.FirstOrDefault()!.Id));
        });
    }

    private static IEnumerable<object[]> UpdateLobbyistEmployeeRegistrationTestCases()
    {
        yield return new object[]
        {
            new LobbyistEmployer
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
            },
            0L
        };
        yield return new object[]
        {
            new LobbyistEmployer
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                }
            },
            0L
        };
        yield return new object[]
        {
            new LobbyistEmployer
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                }
            },
            2L
        };
        yield return new object[]
        {
            new LobbyistEmployer
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                    FilerLinks = new List<FilerLink>
                    {
                        new() {
                            Id = 1,
                            FilerId = 1,
                            LinkedEntityId = 2,
                            EffectiveDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                            FilerLinkTypeId = 1,
                            CreatedBy = 1,
                            ModifiedBy = 1,
                        }
                    }
                }
            },
            2L
        };
        yield return new object[]
        {
            new LobbyistEmployer
            {
                Id = 1,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 1,
                FilerId = 1,
                Filer = new Filer
                {
                    Id = 1,
                    FilerLinks = new List<FilerLink>
                    {
                        new() {
                            Id = 1,
                            FilerId = 1,
                            LinkedEntityId = 3,
                            EffectiveDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                            FilerLinkTypeId = 1,
                            CreatedBy = 1,
                            ModifiedBy = 1,
                        }
                    }
                }
            },
            2L
        };
    }

    [Test]
    public async Task SearchLobbyistByIdOrName_ValidQuery_ReturnsExpectedResults()
    {
        // Arrange
        var testQuery = "Sample Lobbyist";
        var expectedFilerId = 123;
        var expectedName = "Sample Lobbyist";
        var expectedEmail = "<EMAIL>";

        var mockFiler = new Filer
        {
            Id = expectedFilerId,
            CurrentRegistration = new LobbyistTermination
            {
                Name = expectedName,
                Email = expectedEmail,
                StatusId = RegistrationStatus.Accepted.Id,
                TerminatedAt = null
            }
        };

        _registrationRepositoryMock.FindLobbyistByIdOrName(testQuery).Returns(new List<Filer> { mockFiler });

        // Act
        var result = await _service.SearchLobbyistByIdOrName(testQuery);

        // Assert
        var dto = result.Single();
        Assert.Multiple(() =>
        {
            Assert.That(dto.Id, Is.EqualTo(expectedFilerId));
            Assert.That(dto.Name, Is.EqualTo(expectedName));
            Assert.That(dto.Email, Is.EqualTo(expectedEmail));
            Assert.That(dto.IsDisabled, Is.True);
        });
    }

    [Test]
    public async Task UnlinkLobbyistFromEmployer_ShouldCallDatabase()
    {
        // Arrange
        long employerId = 1;
        long lobbyistId = 1;

        _filerLinkRepositoryMock.UnlinkLobbyistFromEmployer(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(true));

        // Act
        var result = await _service.UnlinkLobbyistFromEmployer(employerId, lobbyistId);

        // Assert
        Assert.That(result, Is.True);

        await _filerLinkRepositoryMock.Received(1).UnlinkLobbyistFromEmployer(Arg.Any<long>(), Arg.Any<long>());
    }
}
