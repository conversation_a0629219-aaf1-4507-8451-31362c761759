using System.Data;
using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Models;
using OrganizationContactResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models.OrganizationContactResponseDto;
using PaymentMadeToLobbyingFirmsRequestDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.PaymentMadeToLobbyingFirmsRequestDto;

namespace SOS.CalAccess.FilerPortal.ControllerServices;

public class FirmPaymentTransactionCtlSvc(
    IFilerContactSvc filerContactSvc,
    ILobbyingFirmRegistrationSvc lobbyingFirmRegistrationSvc,
    ITransactionSvc transactionSvc,
    IContactsApi contactsApi,
    IStringLocalizer<SharedResources> localizer) : IFirmPaymentTransactionCtlSvc
{
    private static readonly string _businessAddressType = "Business";

    /// <inheritdoc/>
    public async Task<JsonResult> SearchLobbyingFirmsByIdOrName(
       string query,
       long filerId,
       CancellationToken cancellationToken)
    {
        var data = await filerContactSvc.SearchLobbyingFirmsByNameOrId(query, filerId);
        return new JsonResult(data);
    }

    /// <inheritdoc/>
    public async Task<FirmPaymentTransactionViewModel?> Get01ViewModel(
        string reportType,
        long filingId,
        long filerId,
        long? contactId,
        long? registrationFilingId,
        CancellationToken cancellationToken = default)
    {
        if (!contactId.HasValue && !registrationFilingId.HasValue)
        {
            return new FirmPaymentTransactionViewModel
            {
                FilingId = filingId,
                FilerId = filerId,
                ReportType = reportType
            };
        }

        var contact = await GetContactViewModel(contactId, registrationFilingId);
        return new FirmPaymentTransactionViewModel
        {
            FilingId = filingId,
            FilerId = filerId,
            ReportType = reportType,
            RegistrationFilingId = registrationFilingId,
            ContactId = contactId,
            Contact = contact
        };
    }

    /// <inheritdoc/>
    public async Task<FirmPaymentTransactionViewModel?> GetViewModel(
        string reportType,
        long filingId,
        long filerId,
        long? contactId,
        long? registrationFilingId,
        CancellationToken cancellationToken = default)
    {
        var contact = await GetContactViewModel(contactId, registrationFilingId);
        return new FirmPaymentTransactionViewModel()
        {
            FilingId = filingId,
            FilerId = filerId,
            ReportType = reportType,
            RegistrationFilingId = registrationFilingId,
            ContactId = contactId,
            Contact = contact
        };
    }

    /// <inheritdoc />
    /// Refactor to make this reusable accross all Transactions that use the firm selection screen  
    public async Task SaveFilerContact(FirmPaymentTransactionViewModel model, ModelStateDictionary modelState, string contactForm = "PaymentToLobbyingFirm")
    {
        var contactDetail = model.Contact;
        var upsertContactRequest = CreateUpsertContactRequest(contactDetail, contactForm);

        try
        {
            if (model.ContactId != null && model.ContactId.Value != 0)
            {
                await contactsApi.UpdateFilerContact(model.ContactId.Value, upsertContactRequest);
            }
            else
            {
                var newContact = await contactsApi.CreateFilerContact(model.FilerId!.Value, upsertContactRequest);
                if (newContact.ValidationErrors.Count > 0)
                {
                    ApplyFirmContactValidationErrorsToModelState(newContact.ValidationErrors, modelState);
                }
                else
                {
                    model.ContactId = newContact.Id;
                }

            }
        }
        catch (ApiException err) when (err.StatusCode == (HttpStatusCode)422)
        {
            if (err.Content != null)
            {
                var validationErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content) ?? new List<WorkFlowError>();
                ApplyFirmContactValidationErrorsToModelState(validationErrors, modelState);
            }
        }
    }

    /// <inheritdoc />
    public async Task<long?> SaveTransaction(FirmPaymentTransactionViewModel model, ModelStateDictionary modelState)
    {
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            FilingId = model.FilingId!.Value,
            FilerId = model.FilerId!.Value,
            FeesAndRetainersAmount = model.FeesAndRetainersAmount,
            ReimbursementOfExpensesAmount = model.ReimbursementOfExpensesAmount,
            AdvancesOrOtherPaymentsAmount = model.AdvancesOrOtherPaymentsAmount,
            AdvancesOrOtherPaymentsExplanation = model.AdvancesOrOtherPaymentsExplanation,
            RegistrationFilingId = model.RegistrationFilingId,
            FilerContactId = model.ContactId
        };

        var response = await transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request);
        if (!response.Valid)
        {
            ApplyTransactionValidationErrorsToModelState(response.ValidationErrors, modelState);
        }

        return response.Id;
    }

    /// <inheritdoc />
    public async Task<GenericContactViewModel> GetContactViewModel(long? contactId, long? registrationFilingId)
    {
        var contact = new GenericContactViewModel();

        if (contactId.HasValue && contactId != 0)
        {
            var contactDetails = await filerContactSvc.GetFilerContactById(contactId.Value);
            if (contactDetails is null)
            {
                return contact;
            }

            contact.Id = contactDetails.Id;
            contact.TypeId = contactDetails.TypeId;

            var address = contactDetails.Addresses
                .Where(a => a.Type == _businessAddressType)
                .OrderByDescending(e => e.Id)
                .FirstOrDefault();

            if (address != null)
            {
                contact.Country = address.Country;
                contact.Street = address.Street;
                contact.Street2 = address.Street2;
                contact.City = address.City;
                contact.State = address.State;
                contact.ZipCode = address.Zip;
            }

            if (contactDetails is OrganizationContactResponseDto organization)
            {
                contact.OrganizationName = organization.OrganizationName;
            }
        }
        else if (registrationFilingId.HasValue && registrationFilingId != 0)
        {
            var firmDetails = await lobbyingFirmRegistrationSvc.GetLobbyingFirm(registrationFilingId!.Value);
            if (firmDetails is null)
            {
                return contact;
            }

            contact.OrganizationName = firmDetails.Name;

            var address = firmDetails.Addresses
                .Where(a => a.Type == _businessAddressType)
                .OrderByDescending(e => e.Id)
                .FirstOrDefault();

            if (address != null)
            {
                contact.Country = address.Country;
                contact.Street = address.Street;
                contact.Street2 = address.Street2;
                contact.City = address.City;
                contact.State = address.State;
                contact.ZipCode = address.Zip;
            }
        }

        return contact;
    }

    /// <summary>
    /// Creates the UpsertFilerContactRequest from contact details
    /// </summary>
    /// <param name="contactDetail">Contact details from view model</param>
    /// <returns>UpsertFilerContactRequest for API call</returns>
    private static UpsertOrganizationFilerContactRequest CreateUpsertContactRequest(dynamic contactDetail, string contactForm)
    {
        var addressDtoList = BuildAddressDtoList(contactDetail);
        var emailAddressDtoList = new List<EmailAddressDto>();
        var phoneNumberDtoList = BuildPhoneNumberDtoList(contactDetail);

        return new UpsertOrganizationFilerContactRequest(
            addressDtoList,
            emailAddressDtoList,
            contactForm,
            contactDetail.OrganizationName ?? string.Empty,
            phoneNumberDtoList
        );
    }

    /// <summary>
    /// Builds AddressDto list for save contact request.
    /// </summary>
    /// <param name="contactDetail"></param>
    /// <returns></returns>
    private static List<AddressDto> BuildAddressDtoList(dynamic contactDetail)
    {
        return new List<AddressDto>
        {
            new(
                contactDetail.City ?? string.Empty,
                contactDetail.Country ?? string.Empty,
                0,
                string.Empty,
                contactDetail.State ?? string.Empty,
                contactDetail.Street ?? string.Empty,
                contactDetail.Street2 ?? string.Empty,
                _businessAddressType,
                contactDetail.ZipCode ?? string.Empty
            )
        };
    }

    private static List<PhoneNumberDto> BuildPhoneNumberDtoList(dynamic contactDetail)
    {
        return new List<PhoneNumberDto>
        {
            new(
            id: contactDetail?.PhoneNumberId ?? 0
            , countryCode: contactDetail?.PhoneNumberCountryCode ?? string.Empty
            , countryId: null
            , selectedCountry: 0 //contactDetail?.SelectedCountry
            , internationalNumber: false
            , number: contactDetail?.PhoneNumber ?? string.Empty
            , extension: ""//contactDetail?.Extension ?? string.Empty
            , setAsPrimaryPhoneNumber: false
            , type: _businessAddressType)
        };
    }

    private void ApplyFirmContactValidationErrorsToModelState(
        IReadOnlyList<WorkFlowError> errors,
        ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            if (_firmContactFieldValidationMap.TryGetValue(error.FieldName, out var data))
            {
                modelState.AddModelError(data.FieldName, ReplaceFieldName(error.Message, localizer[data.LabelKey].Value));
            }
            else
            {
                modelState.AddModelError("", error.Message);
            }
        }
    }

    private void ApplyTransactionValidationErrorsToModelState(
        IReadOnlyList<CalAccess.Models.Common.WorkFlowError> errors,
        ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            if (_transactionFieldValidationMap.TryGetValue(error.FieldName, out var data))
            {
                modelState.AddModelError(data.FieldName, ReplaceFieldName(error.Message, localizer[data.LabelKey].Value));
            }
            else
            {
                modelState.AddModelError("", error.Message);
            }
        }
    }
    private static string ReplaceFieldName(string template, string replacement)
    {
        return template.Replace("{{Field Name}}", replacement, StringComparison.Ordinal);
    }

    private static readonly Dictionary<string, FieldProperty> _firmContactFieldValidationMap = new()
    {
        { "LobbyingFirmName", new FieldProperty("Contact.OrganizationName", ResourceConstants.FirmPaymentTransaction02NameLabel ) },
        { "PhoneNumber", new FieldProperty("Contact.PhoneNumber", "Common.PhoneNumber" ) },
        { "Country", new FieldProperty("Contact.Country", "Common.Country" ) },
        { "Street", new FieldProperty("Contact.Street", "Common.Street" ) },
        { "Street2", new FieldProperty("Contact.Street2", "FilerPortal.Contact.CommonFields.Street2" ) },
        { "City", new FieldProperty("Contact.City", "Common.City" ) },
        { "State", new FieldProperty("Contact.State", "Common.State" ) },
        { "Zip", new FieldProperty("Contact.ZipCode", "Common.ZipCode" ) },

    };

    private static readonly Dictionary<string, FieldProperty> _transactionFieldValidationMap = new()
    {
        { "FeesRetainers", new FieldProperty("FeesAndRetainersAmount", ResourceConstants.FirmPaymentTransaction03FeesAndRetainersAmount ) },
        { "ExpensesReimbursement", new FieldProperty("ReimbursementOfExpensesAmount", ResourceConstants.FirmPaymentTransaction03ReimbursementOfExpensesAmount) },
        { "AdvancePayment", new FieldProperty("AdvancesOrOtherPaymentsAmount", ResourceConstants.FirmPaymentTransaction03AdvancesOrOtherPaymentsAmount) },
        { "AdvancePaymentExplanation", new FieldProperty("AdvancesOrOtherpaymentsExplanation", ResourceConstants.FirmPaymentTransaction03AdvancesOrOtherPaymentsExplanation) },
    };
}
