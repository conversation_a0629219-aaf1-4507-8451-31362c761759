// <copyright file="LobbyistEmployerController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.WebApi.Shared;

namespace SOS.CalAccess.Services.WebApi.Registrations;

/// <summary>
/// API controller responsible for routing requests related to Lobbyist Employer registrations.
/// </summary>
[ApiController]
[AllowAnonymous]
[Route("/")]
public sealed class LobbyistEmployerController(
    IAuthorizationService authorization,
    ILobbyistEmployerRegistrationSvc lobbyistEmployerRegistrationSvc) : AuthorizationAwareControllerBase(authorization), ILobbyistEmployerRegistrationSvc
{
    /// <summary>
    /// Retrieves a single Lobbyist Employer registration by id.
    /// </summary>
    /// <param name="id">The id of the Lobbyist Employer registration to search for.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(ILobbyistEmployerRegistrationSvc.GetLobbyistEmployerPath, Name = nameof(GetLobbyistEmployer))]
    public async Task<LobbyistEmployerResponseDto?> GetLobbyistEmployer([FromRoute] long id)
    {
        return await lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(id);
    }

    /// <summary>
    /// Retrieves a single Lobbyist Employer registration by Filer id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet(ILobbyistEmployerRegistrationSvc.GetLobbyistEmployerByFilerIdPath, Name = nameof(GetLobbyistEmployerByFilerId))]
    public async Task<LobbyistEmployerResponseDto?> GetLobbyistEmployerByFilerId([FromRoute] long id)
    {
        return await lobbyistEmployerRegistrationSvc.GetLobbyistEmployerByFilerId(id);
    }

    /// <summary>
    /// Retrieves a single Lobbyist Employer registration by name.
    /// </summary>
    /// <param name="id">The id of the Lobbyist Employer registration to search for.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(ILobbyistEmployerRegistrationSvc.GetLobbyistEmployerByNamePath, Name = nameof(GetLobbyistEmployerByName))]
    public async Task<LobbyistEmployerResponseDto?> GetLobbyistEmployerByName([FromQuery] string name)
    {
        var lobbyistEmployer = await lobbyistEmployerRegistrationSvc.GetLobbyistEmployerByName(name);
        return lobbyistEmployer;
    }

    /// <inheritdoc/>
    [HttpPost(ILobbyistEmployerRegistrationSvc.CreateLobbyistEmployerRegistrationPage03Path, Name = nameof(CreateLobbyistEmployerRegistrationPage03))]
    public async Task<RegistrationResponseDto> CreateLobbyistEmployerRegistrationPage03([FromBody] LobbyistEmployerGeneralInfoRequest request)
    {
        return await lobbyistEmployerRegistrationSvc.CreateLobbyistEmployerRegistrationPage03(request);
    }

    [HttpPost(ILobbyistEmployerRegistrationSvc.CancelLobbyistEmployerRegistrationPath, Name = nameof(CancelLobbyistEmployerRegistration))]
    public async Task CancelLobbyistEmployerRegistration(long id)
    {
        await lobbyistEmployerRegistrationSvc.CancelLobbyistEmployerRegistration(id);
    }

    [HttpPut(ILobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage03Path, Name = nameof(UpdateLobbyistEmployerRegistrationPage03))]
    public async Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage03([FromRoute] long id, [FromBody] LobbyistEmployerGeneralInfoRequest request)
    {
        return await lobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage03(id, request);
    }

    [HttpPut(ILobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage04Path, Name = nameof(UpdateLobbyistEmployerRegistrationPage04))]
    public async Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage04([FromRoute] long id, [FromBody] LobbyistEmployerStateAgenciesRequest request)
    {
        return await lobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage04(id, request);
    }


    [HttpPut(ILobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage05Path, Name = nameof(UpdateLobbyistEmployerRegistrationPage05))]
    public async Task<RegistrationResponseDto> UpdateLobbyistEmployerRegistrationPage05([FromRoute] long id, [FromBody] LobbyistEmployerLobbyingInterestsRequest request)
    {
        return await lobbyistEmployerRegistrationSvc.UpdateLobbyistEmployerRegistrationPage05(id, request);
    }

    /// <summary>
    /// Searches for lobbyist by id or name.
    /// </summary>
    /// <param name="q">The search query string containing the lobbyist's Id or name.</param>
    /// <returns>Returns a list of lobbyists matching the search criteria.</returns>
    [HttpGet(ILobbyistEmployerRegistrationSvc.SearchLobbyistByIdOrNamePath, Name = nameof(SearchLobbyistByIdOrName))]
    public async Task<IEnumerable<LobbyistSearchResultDto>> SearchLobbyistByIdOrName(string q)
    {
        return await lobbyistEmployerRegistrationSvc.SearchLobbyistByIdOrName(q);
    }

    [HttpGet(ILobbyistEmployerRegistrationSvc.GetLobbyistRegistrationByEmployerRegistrationPath, Name = nameof(GetLobbyistRegistrationByEmployerRegistration))]
    public async Task<List<InHouseLobbyistResponseDto>> GetLobbyistRegistrationByEmployerRegistration(long employerRegistrationId)
    {
        return await lobbyistEmployerRegistrationSvc.GetLobbyistRegistrationByEmployerRegistration(employerRegistrationId);
    }

    [HttpDelete(ILobbyistEmployerRegistrationSvc.UnlinkLobbyistFromEmployerPath, Name = nameof(UnlinkLobbyistFromEmployer))]
    public async Task<bool> UnlinkLobbyistFromEmployer(long employerRegistrationId, long lobbyistRegistrationId)
    {
        return await lobbyistEmployerRegistrationSvc.UnlinkLobbyistFromEmployer(employerRegistrationId, lobbyistRegistrationId);
    }

    /// <summary>
    /// Get all lobbying firms
    /// </summary>
    /// <returns></returns>
    [HttpGet(ILobbyistEmployerRegistrationSvc.GetAllLobbyingFirmsPath, Name = nameof(GetAllLobbyingFirms))]
    public async Task<IEnumerable<LobbyingFirmSearchDto>> GetAllLobbyingFirms()
    {
        return await lobbyistEmployerRegistrationSvc.GetAllLobbyingFirms();
    }

    /// <summary>
    /// Link lobbying firms to lobbyist employer
    /// </summary>
    /// <param name="lobbyistEmployerId"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPut(ILobbyistEmployerRegistrationSvc.LinkLobbyistEmployerToLobbyingFirmsPath, Name = nameof(LinkLobbyingFirmsToLobbyistEmployer))]
    public async Task<RegistrationResponseDto> LinkLobbyingFirmsToLobbyistEmployer([FromRoute] long id, [FromBody] LobbyistEmployerToLobbyingFirmRequestDto request)
    {
        return await lobbyistEmployerRegistrationSvc.LinkLobbyingFirmsToLobbyistEmployer(id, request);
    }
}
