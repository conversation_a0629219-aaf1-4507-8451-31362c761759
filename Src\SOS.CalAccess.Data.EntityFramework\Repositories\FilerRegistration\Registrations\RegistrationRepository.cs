using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Registrations;
public class RegistrationRepository(DatabaseContext dbContext) : Repository<Registration, long>(dbContext), IRegistrationRepository
{

    /// <summary>
    /// Get all registrations that match the query
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<IEnumerable<Registration>> GetAll(string query = "")
    {
        var registrations = dbContext.Set<Registration>().AsQueryable();

        if (!string.IsNullOrWhiteSpace(query))
        {
            registrations = registrations.Where(x => EF.Functions.Like(x.Name, $"%{query}%"));
        }

        return await registrations.ToListAsync();
    }

    public Task AddAddress(Address address, long registrationId)
    {
        throw new NotImplementedException();
    }

    public Task AddPhoneNumber(PhoneNumber phoneNumber, long registrationId)
    {
        throw new NotImplementedException();
    }
    public Task RemoveAddress(long addressId, long registrationId)
    {
        throw new NotImplementedException();
    }

    public Task RemovePhoneNumber(long phoneNumberId, long registrationId)
    {
        throw new NotImplementedException();
    }

    public Task UpdateAddress(Address address, long registrationId)
    {
        throw new NotImplementedException();
    }

    public Task UpdatePhoneNumber(PhoneNumber phoneNumber, long registrationId)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// Gets the AddressList by Id
    /// </summary>
    /// <param name="id">AddressList Id</param>
    /// <returns></returns>
    public async Task<AddressList?> GetAddressListById(long id)
    {
        var result = await dbContext.Set<AddressList>()
            .Where(r => r.Id == id)
            .Include(l => l!.Addresses)
            .FirstOrDefaultAsync();
        return result;
    }

    /// <summary>
    /// Gets the PhoneNumberList by Id
    /// </summary>
    /// <param name="id">PhoneNumber List</param>
    /// <returns></returns>
    public async Task<PhoneNumberList?> GetPhoneNumberListById(long id)
    {
        var result = await dbContext.Set<PhoneNumberList>()
            .Where(r => r.Id == id)
            .Include(l => l!.PhoneNumbers)
            .FirstOrDefaultAsync();
        return result;
    }

    /// <summary>
    /// Gets the Registration Record type
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<string?> GetRegistrationDiscriminatorById(long id)
    {
        return await dbContext.Set<Registration>()
                    .Where(r => r.Id == id)
                    .Select(r => EF.Property<string>(r, "Discriminator"))
                    .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Check the registration name is unique.
    /// </summary>
    /// <param name="name">Name tied to registration</param>
    /// <param name="parentId">Id of the parent Amendment</param>
    /// <returns>Returns true if the registration name is unique.</returns>
    public async Task<bool> IsRegistrationNameUnique(string name, string discriminator, long filerTypeId, long? parentId = null)
    {
        var result = await dbContext
            .Database
            .SqlQuery<RegistrationNameRankDto>($@"
            WITH Filtered AS (
                SELECT rf.Id, rf.Name, rf.ParentId, 
                       ROW_NUMBER() OVER(PARTITION BY OriginalId ORDER BY Version DESC) AS 'Rank' 
                FROM RegistrationFiling rf
                JOIN Filer f on f.Id = rf.FilerId
                WHERE StatusId = {RegistrationStatus.Accepted.Id}
                    AND Discriminator = {discriminator}
                    AND f.FilerStatusId = {FilerStatus.Active.Id}
                    AND f.FilerTypeId = {filerTypeId}
            )
            SELECT * FROM Filtered
            WHERE Rank = 1
                AND Name = {name}
                AND ({parentId} IS NULL OR Id != {parentId})
        ")
            .ToListAsync();

        return result.Count == 0;
    }

    #region CandidateIntentionStatement

    /// <summary>
    /// Repository method to create a new Candidate Registration
    /// </summary>
    /// <param name="entity">Election PK</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public async Task<CandidateIntentionStatement> Create(CandidateIntentionStatement entity)
    {
        dbContext.CandidateIntentionStatements.Add(entity);
        await dbContext.SaveChangesAsync();

        if (entity.OriginalId == null)
        {
            entity.OriginalId = entity.Id;
            await dbContext.SaveChangesAsync();
        }

        return entity;
    }
    public async Task<CandidateIntentionStatement?> FindCandidateIntentionStatementById(long registrationId)
    {
        try
        {
            return await dbContext.Set<CandidateIntentionStatement>()
                .Where(r => r.Id == registrationId)
                .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
                .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
                .AsSplitQuery()
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            // Log the exception (consider using a logging framework like Serilog, NLog, or ILogger)
            Console.WriteLine($"An error occurred: {ex.Message}");
            return null; // or handle it as needed
        }
    }
    public async Task<CandidateIntentionStatement?> GetRegistrationByFilerId(long filerId)
    {
        return await dbContext.Set<CandidateIntentionStatement>()
            .Where(rf => rf.FilerId == filerId)
            .FirstOrDefaultAsync();
    }

    public async Task<CandidateIntentionStatement?> FindCandidateIntentionStatementWithElectionById(long registrationId)
    {
        return await dbContext.Set<CandidateIntentionStatement>()
             .Where(r => r.Id == registrationId)
             .Include(r => r.Filer)
             .Include(r => r.PoliticalParty)
             .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
             .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
             .Include(r => r.ElectionRace)
                 .ThenInclude(er => er!.Election)
                    .ThenInclude(e => e!.ElectionType)
             .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.Office)
             .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.District)
             .AsSplitQuery()
             .FirstOrDefaultAsync();
    }

    public async Task<Election?> FindElectionByRegistrationId(long registrationId)
    {
        var result = await dbContext.Set<CandidateIntentionStatement>()
            .Where(r => r.Id == registrationId)
            .Select(r => new
            {
                r.ElectionRace!.ElectionId
            })
            .SelectMany(data => dbContext.Set<Election>()
                .Where(e => e.Id == data.ElectionId))
            .FirstOrDefaultAsync();
        return result;
    }

    public async Task<IEnumerable<CandidateIntentionStatement>> FindCandidateRegistrationsWithElectionByIdOrName(string query)
    {
        var isNumeric = long.TryParse(query, out var idToMatch);

        //TODO: This most likely needs to take a limit on the number of records to return as well as verify that query is more than at least a single character
        return await dbContext.Set<CandidateIntentionStatement>()
             .Where(r => r.CandidateId != null &&
                         r.StatusId == RegistrationStatus.Accepted.Id &&
                        (EF.Functions.Like(r.Name, $"%{query}%") ||
                         (isNumeric && r.CandidateId == idToMatch)))
             .Include(r => r.Candidate)
             .Include(r => r.ElectionRace)
                 .ThenInclude(er => er!.Election)
                    .ThenInclude(e => e!.ElectionType)
             .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.Office)
             .OrderBy(r => r.CandidateId)
                .ThenByDescending(r => r.ApprovedAt)
                .ThenByDescending(r => r.ElectionRace!.Election.ElectionDate)
             .AsSplitQuery()
             .Take(5)
             .ToListAsync();
    }

    /// <summary>
    /// Repository method to associate an Election with a Registration
    /// </summary>
    /// <param name="electionId">Election PK</param>
    /// <param name="registrationId">Registration PK</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public async Task<long> LinkElectionToCandidateIntentRegistration(
    long registrationId, string? jurisdiction, long? electionId, long? officeId, long? districtId, long? countyId, long? partyId)
    {

        var foundReg = await dbContext.Set<Registration>().FirstOrDefaultAsync(r => r.Id == registrationId) ?? throw new KeyNotFoundException($"Unable to find registration: {registrationId}");

        var foundParty = partyId > 0 ? await dbContext.Set<PoliticalParty>().FindAsync(partyId) : null;

        ElectionRace? foundElectionRace = null;
        District? foundCounty = null;
        if (electionId > 0 && districtId > 0 && officeId > 0)
        {
            foundElectionRace = await dbContext.Set<ElectionRace>()
                                 .Where(er => er.ElectionId == electionId && er.DistrictId == districtId && er.OfficeId == officeId)
                                 .Include(er => er.District)
                                 .Include(er => er.Office)
                                 .Include(er => er.Election)
                                 .ThenInclude(et => et.ElectionType)
                                 .AsSplitQuery()
                                 .FirstOrDefaultAsync();


            if (foundElectionRace?.Office != null && foundElectionRace.Office.Name.Contains("Superior Court"))
            {
                foundCounty = foundElectionRace.District;
            }
        }

        var districtNumber = (foundElectionRace != null && foundElectionRace.District != null && foundElectionRace.District.Id != 1) ? foundElectionRace.District.DistrictNumber.ToString() : null;

        var candIntent = (CandidateIntentionStatement)foundReg;


        candIntent.ElectionRaceId = foundElectionRace?.Id;
        candIntent.ElectionRace = foundElectionRace;
        candIntent.ElectionJurisdiction = string.IsNullOrWhiteSpace(jurisdiction) ? null : jurisdiction;
        candIntent.ElectionOfficeSought = foundElectionRace?.Office?.Name;
        candIntent.ElectionDistrictNumber = districtNumber;
        candIntent.ElectionCounty = foundCounty?.DistrictNumber.ToString();
        candIntent.PoliticalPartyId = foundParty?.Id;

        await dbContext.SaveChangesAsync();


        return candIntent?.ElectionRaceId ?? 0;
    }
    /// <summary>
    /// Repository method to get expenditure expense ceiling amount by registration id
    /// </summary>
    /// <param name="registrationId">registration id</param>
    /// <returns></returns>
    public async Task<ExpenditureExpenseAmount?> FindExpenditureExpenseAmount(long registrationId)
    {
        var result = await dbContext.Set<CandidateIntentionStatement>()
            .Where(r => r.Id == registrationId)
            .Select(r => new
            {
                r.ElectionRace!.OfficeId,
                ElectionYear = r.ElectionRace.Election.ElectionDate.Year
            })
            .SelectMany(data => dbContext.Set<ExpenditureExpenseAmount>()
                .Where(e => e.OfficeId == data.OfficeId && e.ElectionYear == data.ElectionYear))
            .FirstOrDefaultAsync();

        return result;
    }

    /// <summary>
    /// Repository method to get election type
    /// </summary>
    /// <param name="registrationId">registration id</param>
    /// <returns></returns>
    public async Task<ElectionType?> FindElectionType(long registrationId)
    {
        var result = await dbContext.Set<CandidateIntentionStatement>()
            .Where(r => r.Id == registrationId)
            .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.Election)
                    .ThenInclude(e => e.ElectionType)
            .Select(r => r.ElectionRace!.Election.ElectionType)
            .FirstOrDefaultAsync();
        return result;
    }

    /// <inheritdoc/>
    public async Task<CandidateIntentionStatement?> GetCandidateIntentionStatementWithElectionByFilerId(long filerId)
    {
        return await dbContext.Set<CandidateIntentionStatement>()
            .Where(r => r.FilerId == filerId && r.StatusId != RegistrationStatus.Canceled.Id)
            .OrderByDescending(r => r.Id)
            .Include(r => r.PoliticalParty)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.Election)
                    .ThenInclude(e => e!.ElectionType)
            .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.Office)
            .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.District)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<CandidateIntentionStatement?> GetCisByFilerId(long filerId)
    {
        var result = await dbContext.Set<CandidateIntentionStatement>()
            .Where(r => r.FilerId == filerId && r.StatusId != RegistrationStatus.Canceled.Id && r.CommitteeTypeId == CommitteeType.PrimarilyFormed.Id)
            .OrderByDescending(r => r.Id)
            .Include(r => r.Filer)
                .ThenInclude(f => f!.Users
                    .Where(fu => fu.FilerRole == FilerRole.RecipientCommittee_Treasurer))
                        .ThenInclude(fu => fu.User)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
        return result;
    }

    public async Task<Filer?> GetFilerOfLatestAcceptedCisRegistration(long userId)
    {
        var result = await dbContext.Set<CandidateIntentionStatement>()
            .OrderByDescending(r => r.Id)
            .Include(r => r.Filer)
            .ThenInclude(f => f!.Users)
            .Where(x =>
                x.StatusId == RegistrationStatus.Accepted.Id &&
                x.Filer != null &&
                x.Filer.Users != null &&
                x.Filer.Users.Any(u => u.UserId == userId)
            )
            .AsSplitQuery()
            .FirstOrDefaultAsync();

        return result?.Filer;
    }

    public async Task<CandidateIntentionStatement?> GetLatestAcceptedCisRegistrationByFilerId(long filerId)
    {
        var filer = await dbContext.Set<Filer>()
            .Where(f => f.Id == filerId)
            .FirstOrDefaultAsync();

        var registration = await dbContext.Set<CandidateIntentionStatement>()
            .Where(x => x.Id == filer!.CurrentRegistrationId &&
                        x.StatusId == RegistrationStatus.Accepted.Id)
            .FirstOrDefaultAsync();

        var result = await dbContext.Set<CandidateIntentionStatement>()
            .Include(r => r.Candidate)
                .ThenInclude(c => c!.User)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.Election)
            .Include(r => r.ElectionRace)
                .ThenInclude(er => er!.District)
            .Where(x => x.OriginalId == registration!.OriginalId &&
                        x.StatusId == RegistrationStatus.Accepted.Id)
            .AsSplitQuery()
            .OrderByDescending(x => x.Version)
            .FirstOrDefaultAsync();

        return result;
    }

    #region CisRegistrationWithdrawal
    public async Task<CisWithdrawal?> GetCisRegistrationWithdrawalById(long registrationId)
    {
        return await dbContext.Set<CisWithdrawal>()
            .Where(rf => rf.Id == registrationId)
            .FirstOrDefaultAsync();
    }
    #endregion
    #endregion

    #region SlateMailerOrganization

    /// <summary>
    /// Gets the Slate Mailer Organization Registration Record from the Database
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    public async Task<SlateMailerOrganization?> FindSlateMailerOrganizationById(long id)
    {
        var result = await dbContext.Set<SlateMailerOrganization>()
            .Where(r => r.Id == id)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .Include(r => r.RegistrationRegistrationContacts)
                .ThenInclude(c => c!.RegistrationContact)
            .Include(r => r.Filer)
                .ThenInclude(c => c!.FilerLinks)
            .Include(r => r.Attestations)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
        return result;
    }

    /// <inheritdoc/>
    public async Task<SlateMailerOrganization?> FindSlateMailerOrganizationBasicById(long id)
    {
        return await dbContext.Set<SlateMailerOrganization>()
            .AsNoTracking()
            .Where(r => r.Id == id)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Gets the SMO RegistrationContacts
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    public async Task<IEnumerable<RegistrationRegistrationContact>> FindSmoRegistrationContactsById(long id)
    {
        var result = await dbContext.Set<RegistrationRegistrationContact>()
            .Where(r => r.Active && r.RegistrationId == id)
            .Include(c => c!.RegistrationContact)
                .ThenInclude(r => r!.AddressList)
                    .ThenInclude(l => l!.Addresses)
            .Include(c => c!.RegistrationContact)
                .ThenInclude(r => r!.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .AsSplitQuery()
            .ToListAsync();
        return result;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<RegistrationRegistrationContact>> FindSmoRegistrationContactsByIdNoTracking(long id)
    {
        var result = await dbContext.Set<RegistrationRegistrationContact>()
            .Where(r => r.Active && r.RegistrationId == id)
            .AsNoTracking()
            .Include(c => c!.RegistrationContact)
                .ThenInclude(r => r!.AddressList)
                    .ThenInclude(l => l!.Addresses)
            .Include(c => c!.RegistrationContact)
                .ThenInclude(r => r!.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .AsSplitQuery()
            .ToListAsync();
        return result;
    }

    /// <summary>
    /// Gets Specific SMO RegistrationContacts
    /// </summary>
    /// <param name="id">Id of the Registration filing</param>
    /// <returns></returns>
    public async Task<RegistrationRegistrationContact?> GetSmoRegistrationRegistrationContactById(long registrationId, long contactId)
    {
        var result = await dbContext.Set<RegistrationRegistrationContact>()
            .Where(c => c.Active &&
                        c.RegistrationId == registrationId &&
                        c.Id == contactId)
            .Include(c => c!.Registration)
            .Include(c => c!.RegistrationContact)
            .ThenInclude(r => r!.AddressList)
            .ThenInclude(l => l!.Addresses)
            .Include(c => c!.RegistrationContact)
            .ThenInclude(r => r!.PhoneNumberList)
            .ThenInclude(l => l!.PhoneNumbers)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
        return result;
    }
    /// <inheritdoc/>
    public async Task<SlateMailerOrganization?> FindExistingSmoAmendmentRegistration(long parentId, long statusId)
    {
        return await dbContext.Set<SlateMailerOrganization>()
            .Where(x => x.ParentId == parentId && x.StatusId == statusId)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<List<SlateMailerOrganization>> SearchSmoRegistrationByIdOrName(string query, long statusId, long? userId)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return new List<SlateMailerOrganization>();
        }

        var isNumeric = long.TryParse(query, out long idToMatch);

        var result = await dbContext
            .Database
            .SqlQuery<SmoRegistrationBasicResponseDto>($@"
                WITH Filtered AS (
                    SELECT rf.Id, rf.Name, rf.Email, rf.StatusId, rf.FilerId, ROW_NUMBER() OVER(PARTITION BY OriginalId ORDER BY Version DESC) AS 'Rank' FROM RegistrationFiling rf
                    JOIN Filer f ON f.Id = rf.FilerId
                    JOIN FilerUser fu ON fu.FilerId = f.Id
                    WHERE StatusId = {statusId}
                        AND Discriminator = {nameof(SlateMailerOrganization)}
                        AND ({userId} IS NULL OR fu.UserId = {userId})
                )
                SELECT * FROM Filtered
                WHERE Rank = 1
                    AND (({isNumeric} = 1 AND Id = {idToMatch})
                    OR Name LIKE {'%' + query + '%'})
            ")
            .ToListAsync();

        // Have to remap into entity again, since cannot reference to the DTO
        return [.. result.Select(SmoRegistrationBasicResponseDto.MapToEntity)];
    }

    /// <inheritdoc/>
    public async Task<SlateMailerOrganization?> FindSmoRegistrationLatestAcceptedByFilerId(long filerId)
    {
        return await dbContext.Set<SlateMailerOrganization>()
            .Where(r => r.FilerId == filerId && r.StatusId == RegistrationStatus.Accepted.Id)
            .OrderByDescending(r => r.Id)
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<SlateMailerOrganization?> FindSlateMailerOrganizationWithParentById(long id)
    {
        return await dbContext.Set<SlateMailerOrganization>()
            .Include(x => x.Parent)
            .Where(r => r.Id == id)
            .FirstOrDefaultAsync();
    }
    #endregion

    #region LobbyistEmployer
    /// <inheritdoc/>
    public async Task<LobbyistEmployer?> FindLobbyistEmployerById(long id)
    {
        return await dbContext.Set<LobbyistEmployer>()
            .Where(r => r.Id == id)
            .Include(r => r.AddressList)
            .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
            .ThenInclude(l => l!.PhoneNumbers)
            .Include(r => r.RegistrationAgencies)
            .Include(r => r.LobbyingInterest)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<LobbyistEmployer?> FindLobbyistEmployerByName(string name)
    {
        return await dbContext.Set<LobbyistEmployer>()
            .Where(r => r.Name == name)
            .FirstOrDefaultAsync();
    }

    #endregion

    #region LobbyingFirm
    /// <inheritdoc/>
    public async Task<LobbyingFirm?> FindLobbyingFirmById(long id)
    {
        return await dbContext.Set<LobbyingFirm>()
            .Where(r => r.Id == id)
            .Include(r => r.AddressList)
            .ThenInclude(l => l!.Addresses)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
    }

    /// <inheritdoc/>
    public async Task<List<LobbyingFirm>> GetAllLobbyingFirms()
    {
        List<long> statuses = new()
        {
            RegistrationStatus.Draft.Id,
            RegistrationStatus.Rejected.Id,
            RegistrationStatus.Canceled.Id,
        };
        return await dbContext.Set<LobbyingFirm>().Where(f => !statuses.Contains(f.StatusId)).AsNoTracking().ToListAsync();
    }
    #endregion

    #region Lobbyist Registration
    /// <summary>
    /// Repository method to create a new Lobbyist Registration
    /// </summary>
    /// <param name="entity">Election PK</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public async Task<Lobbyist> CreateLobbyist(Lobbyist entity)
    {
        dbContext.Lobbyists.Add(entity);
        await dbContext.SaveChangesAsync();

        if (entity.OriginalId == null)
        {
            entity.OriginalId = entity.Id;
            await dbContext.SaveChangesAsync();
        }

        return entity;
    }

    /// <summary>
    /// Gets a Lobbyist Registration Record from the Database using the employer Registration Id
    /// </summary>
    /// <param name="employerRegistrationId">Employer ID of the registration</param>
    /// <returns>List of lobbyist registration </returns>
    public async Task<List<Lobbyist>?> FindLobbyistRegistrationByEmployer(long employerRegistrationId)
    {
        var result = await (from l in dbContext.Lobbyists
                            join f in dbContext.Filers on l.FilerId equals f.Id
                            join fl in dbContext.FilerLinks on f.Id equals fl.FilerId
                            where fl.FilerLinkTypeId == FilerLinkType.LobbyistEmployer.Id && fl.LinkedEntityId == employerRegistrationId && fl.Active
                        select l).ToListAsync();

        return result;
    }

    /// <summary>
    /// Gets the Lobbyist Registration Record from the Database
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    public async Task<Lobbyist?> FindLobbyistById(long id)
    {
        var result = await dbContext.Set<Lobbyist>()
            .Where(r => r.Id == id)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .Include(r => r.RegistrationAgencies)
            .Include(r => r.LegislativeSession)
            .Include(r => r.RegistrationRegistrationContacts)
                .ThenInclude(c => c!.RegistrationContact)
            .Include(r => r.Filer)
                .ThenInclude(c => c!.FilerLinks)
            .Include(r => r.Attestations)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
        return result;
    }

    /// <summary>
    /// Gets the Lobbyist Registration Record from the Database as no tracking
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    public async Task<LobbyistWithdrawal?> FindLobbyistWithdrawalById(long id)
    {
        var result = await dbContext.Set<LobbyistWithdrawal>()
            .Where(r => r.Id == id)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .Include(r => r.RegistrationRegistrationContacts)
                .ThenInclude(c => c!.RegistrationContact)
            .Include(r => r.Filer)
                .ThenInclude(c => c!.FilerLinks)
            .Include(r => r.Attestations)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
        return result;
    }

    /// <summary>
    /// Gets the Lobbyist Registration Record from the Database as no tracking
    /// </summary>
    /// <param name="id">Id of the registration filer</param>
    /// <returns></returns>
    public async Task<LobbyistTermination?> FindLobbyistTerminationById(long id)
    {
        var result = await dbContext.Set<LobbyistTermination>()
            .Where(r => r.Id == id)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .Include(r => r.RegistrationRegistrationContacts)
                .ThenInclude(c => c!.RegistrationContact)
            .Include(r => r.Filer)
                .ThenInclude(c => c!.FilerLinks)
            .Include(r => r.Attestations)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
        return result;
    }

    /// <summary>
    /// Gets a Registration Record from the Database using the Filer ID
    /// </summary>
    /// <typeparam name="T">The type of registration to return (e.g., Lobbyist, LobbyistEmployer)</typeparam>
    /// <param name="filerId">Filer ID of the registration</param>
    /// <returns>Registration entity with address and phone number information, or null if not found</returns>
    public async Task<T?> FindLobbyingRegistrationByFilerId<T>(long filerId) where T : Registration
    {
        var result = await dbContext.Set<T>()
           .Where(r => r.FilerId == filerId)
           .Include(r => EF.Property<object>(r, "AddressList"))
           .ThenInclude(a => EF.Property<object>(a, "Addresses"))
           .Include(r => EF.Property<object>(r, "PhoneNumberList"))
           .ThenInclude(p => EF.Property<object>(p, "PhoneNumbers"))
           .AsSplitQuery()
           .FirstOrDefaultAsync();

        return result;
    }

    /// <inheritdoc/>
    public async Task<List<T?>> FindListOfRegistrationsByFilerId<T>(long filerId) where T : Registration
    {
        var query = dbContext.Set<T>()
            .Where(r => r.FilerId == filerId)
            .Include(r => EF.Property<object>(r, "AddressList"))
                .ThenInclude(a => EF.Property<object>(a, "Addresses"))
            .Include(r => EF.Property<object>(r, "PhoneNumberList"))
                .ThenInclude(p => EF.Property<object>(p, "PhoneNumbers"));

        if (typeof(T) == typeof(Lobbyist) ||
            typeof(T) == typeof(LobbyistEmployer) ||
            typeof(T) == typeof(LobbyingFirm))
        {
            query = query.Include(r => EF.Property<object>(r, "LegislativeSession"));
        }

        return (await query!
            .AsSplitQuery()
            .AsNoTracking()
            .ToListAsync())!;
    }
    #endregion

    #region CandidateControlledCommittee
    public async Task<IEnumerable<CandidateControlledCommittee>> FindControlledCommitteesByNameOrId(string q)
    {
        if (string.IsNullOrEmpty(q))
        {
            return new List<CandidateControlledCommittee>();
        }

        bool isNumeric = long.TryParse(q, out long queryNumber);

        var results = await dbContext.Set<CandidateControlledCommittee>()
            .Include(r => r.AddressList)
            .ThenInclude(a => a!.Addresses)
            .Include(r => r.PhoneNumberList)
            .ThenInclude(a => a!.PhoneNumbers)
            .Include(f => f.Filer)
            .Where(r =>
                (
                    EF.Functions.Like(r!.Name, $"%{q}%") ||
                    (isNumeric && r.FilerId == queryNumber)
                ) &&
                r.FilerId != null &&
                r.Filer != null &&
                r.Filer.FilerStatusId == FilerStatus.Active.Id &&
                r.StatusId != RegistrationStatus.Draft.Id &&
                r.StatusId != RegistrationStatus.Rejected.Id &&
                r.StatusId != RegistrationStatus.Canceled.Id &&
                r.CommitteeTypeId == CommitteeType.CandidateControlled.Id)
            .OrderBy(r => r.Id)
            .ThenBy(r => r!.Name)
            .Take(10)
            .AsNoTracking()
            .ToListAsync();

        return results;
    }
    public async Task<CandidateControlledCommittee?> GetControlledCommitteeByFilerId(long filerId)
    {
        return await dbContext.Set<CandidateControlledCommittee>()
            .Where(r => r.FilerId == filerId && r.StatusId != RegistrationStatus.Canceled.Id)
            .OrderByDescending(r => r.Id)
            .Include(r => r.AddressList)
                .ThenInclude(l => l!.Addresses)
            .Include(r => r.PhoneNumberList)
                .ThenInclude(l => l!.PhoneNumbers)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
    }
    #endregion

    #region Committee
    /// <summary>
    /// Finds a Committee by Id (complete) or Name (partial)
    /// </summary>
    /// <param name="query">term to search for</param>
    /// <returns></returns>
    public async Task<IEnumerable<Filer>> FindCommitteeByIdOrName(string query)
    {
        var filerTypes = new[] { FilerType.RecipientCommittee.Id };
        return await FindFilersByIdOrName(query, filerTypes);
    }
    /// <inheritdoc/>
    public async Task<Committee?> GetCommitteeByFilerId(long filerId)
    {
        var baseQuery = GetPrimarilyFormedCommitteeBaseQuery();
        var result = await baseQuery.Where(r => r.FilerId == filerId)
            .AsSplitQuery()
            .FirstOrDefaultAsync();
        return result;
    }

    /// <summary>
    /// Finds a Primarily Formed Committee by Id (complete) or Name (partial)
    /// </summary>
    /// <param name="query">term to search for</param>
    /// <returns></returns>
    public async Task<IEnumerable<Committee>> FindPrimarilyFormedCommitteeByIdOrName(string query)
    {
        if (string.IsNullOrEmpty(query))
        {
            return new List<Committee>();
        }

        bool isNumeric = long.TryParse(query, out long queryNumber);

        var baseQuery = GetPrimarilyFormedCommitteeBaseQuery();
        var result = await baseQuery.Where(r =>
                    EF.Functions.Like(r.Name, $"%{query}%") ||
                    (isNumeric && r.Id == queryNumber)
                )
            .OrderBy(r => r.Id)
            .ThenBy(r => r.Name)
            .Take(10)
            .ToListAsync();

        return result;
    }

    private IQueryable<Committee> GetPrimarilyFormedCommitteeBaseQuery()
    {
        List<long> filter = new()
        {
            RegistrationStatus.Draft.Id,
            RegistrationStatus.Rejected.Id,
            RegistrationStatus.Canceled.Id,
        };

        var query = dbContext.Set<Committee>()
            .Include(r => r.AddressList)
                .ThenInclude(a => a!.Addresses)
            .Include(r => r.Filer)
                .ThenInclude(f => f!.Users
                    .Where(fu => fu.FilerRole == FilerRole.RecipientCommittee_Treasurer))
                        .ThenInclude(fu => fu.User)
            .Where(r =>
                r.Filer != null &&
                r.Filer.FilerStatusId == FilerStatus.Active.Id &&
                r.Filer.FilerTypeId == FilerType.Candidate.Id &&
                r.CommitteeTypeId == CommitteeType.PrimarilyFormed.Id &&
            !filter.Contains(r.StatusId));
        return query;
    }
    #endregion

    #region Lobbyist Registration
    /// <summary>
    /// Finds a  lobbyist employer or lobbying firm by Id (complete) or Name (partial)
    /// </summary>
    /// <param name="query">term to search for</param>
    /// <returns></returns>
    public async Task<IEnumerable<Filer>> FindLobbyistEmployerOrLobbyingFirmByIdOrName(string query)
    {
        var filerTypes = new[] { FilerType.LobbyistEmployer.Id, FilerType.LobbyingFirm.Id };
        return await FindFilersByIdOrName(query, filerTypes);
    }
    #endregion

    #region Lobbyist Employer Registration
    /// <summary>
    /// Finds a  lobbyist by Id (complete) or Name (partial)
    /// </summary>
    /// <param name="query">term to search for</param>
    /// <returns></returns>
    public async Task<IEnumerable<Filer>> FindLobbyistByIdOrName(string query)
    {
        var filerTypes = new[] { FilerType.Lobbyist.Id };
        return await FindFilersCustomByIdOrName(query, filerTypes);
    }

    #endregion

    #region Private
    private async Task<IEnumerable<Filer>> FindFilersByIdOrName(string query, IEnumerable<long> filerTypeIds)
    {
        if (string.IsNullOrEmpty(query))
        {
            return new List<Filer>();
        }

        bool isNumeric = long.TryParse(query, out long queryNumber);
        List<long> filter = new()
    {
        RegistrationStatus.Draft.Id,
        RegistrationStatus.Rejected.Id,
        RegistrationStatus.Canceled.Id,
    };

        var results = await dbContext.Set<Filer>()
            .Include(r => r.CurrentRegistration)
            .Where(r =>
                r.FilerStatusId == FilerStatus.Active.Id &&
                filerTypeIds.Contains(r.FilerTypeId) &&
                !filter.Contains(r.CurrentRegistration!.StatusId) &&
                (
                    EF.Functions.Like(r.CurrentRegistration!.Name, $"%{query}%") ||
                    (isNumeric && r.Id == queryNumber)
                )
            )
            .OrderBy(r => r.Id)
            .ThenBy(r => r.CurrentRegistration!.Name)
            .Take(10)
            .ToListAsync();

        return results;
    }

    private async Task<IEnumerable<Filer>> FindFilersCustomByIdOrName(string query, IEnumerable<long> filerTypeIds)
    {
        if (string.IsNullOrEmpty(query))
        {
            return new List<Filer>();
        }

        bool isNumeric = long.TryParse(query, out long queryNumber);

        var lobbyist = await dbContext.Set<Filer>()
            .Include(r => r.CurrentRegistration)
            .Where(r =>
                filerTypeIds.Contains(r.FilerTypeId) &&
                r.CurrentRegistration!.StatusId != RegistrationStatus.Canceled.Id &&
                r.CurrentRegistration is Lobbyist &&
                (
                    EF.Functions.Like(r.CurrentRegistration!.Name, $"%{query}%") ||
                    (isNumeric && r.Id == queryNumber)
                )
            )
            .OrderBy(r => r.Id)
            .ThenBy(r => r.CurrentRegistration!.Name)
            .Take(10)
            .ToListAsync();

        var lobbyistTermination = await dbContext.Set<Filer>()
            .Include(r => r.CurrentRegistration)
            .Where(r =>
                filerTypeIds.Contains(r.FilerTypeId) &&
                r.CurrentRegistration!.StatusId == RegistrationStatus.Accepted.Id &&
                r.CurrentRegistration!.TerminatedAt != null &&
                EF.Property<string>(r.CurrentRegistration, "Discriminator") == nameof(LobbyistTermination) &&
                (
                    EF.Functions.Like(r.CurrentRegistration!.Name, $"%{query}%") ||
                    (isNumeric && r.Id == queryNumber)
                )
            )
            .OrderBy(r => r.Id)
            .ThenBy(r => r.CurrentRegistration!.Name)
            .Take(10)
            .ToListAsync();

        return lobbyist.Concat(lobbyistTermination);
    }
    #endregion
}
