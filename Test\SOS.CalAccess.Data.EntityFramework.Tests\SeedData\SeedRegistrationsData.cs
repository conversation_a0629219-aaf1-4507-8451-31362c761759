// <copyright file="SeedRegistrationsData.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.SeedData;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Auditing;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Registrations;

/// <summary>
/// Static helper class for context initialization and seeding data during tests.
/// </summary>
public static class SeedRegistrationsData
{

    /// <summary>
    /// Creates a new <see cref="CandidateRegistration"/> instance with
    /// default values.
    /// </summary>
    /// <param name="withName">The name to assign to the registration.</param>
    /// <param name="status">Optional status value to use. Defaults to "Pending".</param>
    /// <returns>A new candidate registration.</returns>
    public static CandidateIntentionStatement Templated(string withName, long? status = default) => new()
    {
        Name = withName,
        StatusId = status ?? RegistrationStatus.Draft.Id,
        ApprovedAt = DateTime.Now,
        AddressList = SeedValues.GetTestAddressList(),
        PhoneNumberList = SeedValues.GetTestPhoneNumberList(),
    };

    /// <summary>
    /// Initializes the context with some data that can be used to test
    /// both lookup and mutation operations on it.
    /// </summary>
    /// <param name="context">The <see cref="DatabaseContext" /> instance being initialized.</param>
    /// <returns>An <see cref="AssertionReferences"/> instance for writing tests against the seeded data.</returns>
    public static async Task<AssertionReferences> PrepareRegistrationData(this DatabaseContext context, IDateTimeSvc dateTimeSvc)
    {
        var pending = Templated(withName: "Pending Registration");

        var approved = Templated(withName: "Approved Registration", status: RegistrationStatus.Accepted.Id);

        var submitted = Templated(withName: "Submitted Registration", status: RegistrationStatus.Submitted.Id);

        context.CandidateIntentionStatements.AddRange(pending, approved, submitted);

        var entry = context.Entry(approved);
        var issuedAt = dateTimeSvc.GetCurrentDateTime();
        var log = new AuditLog(new DataAction(entry, issuedAt), dateTimeSvc, SystemActor.MapLight.Id);

        context.AuditLogs.Add(log);

        await context.SaveChangesAsync();

        var filer = new Filer
        {
            CurrentRegistrationId = approved.Id,
            FilerStatusId = FilerStatus.Active.Id,
        };
        approved.Filer = filer;

        await context.SaveChangesAsync();

        return new()
        {
            PendingRegistrationId = pending.Id,
            ApprovedRegistrationId = approved.Id,
            SubmittedRegistrationId = submitted.Id,
            FilerId = filer.Id,
        };
    }

    /// <summary>
    /// Class for holding the output and reference items from a data seeding process
    /// that can be used for asserting test conditions.
    /// </summary>
    public sealed class AssertionReferences
    {
        /// <summary>
        /// Gets the id of an existing registration item that is in the "Pending" state.
        /// </summary>
        public long PendingRegistrationId { get; init; }

        /// <summary>
        /// Gets the id of an existing registration item that is in the "Submitted" state.
        /// </summary>
        public long SubmittedRegistrationId { get; init; }

        /// <summary>
        /// Gets the id of an existing registration item that is in the "Approved" state.
        /// </summary>
        public long ApprovedRegistrationId { get; init; }

        /// <summary>
        /// Gets the id of an existing filer item that is associated with the "Approved" registration.
        /// </summary>
        public long FilerId { get; init; }
    }

    /// <summary>
    /// Test registration type to represent an unknown registration type.
    /// </summary>
    internal sealed class UnknownRegistrationType : Registration
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UnknownRegistrationType"/> class.
        /// </summary>
        [SetsRequiredMembers]
        internal UnknownRegistrationType()
        {
            Name = "Name";
            StatusId = RegistrationStatus.Terminated.Id;
        }
    }
}
