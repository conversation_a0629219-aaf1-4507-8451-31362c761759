using System.Reflection;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Lookups;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Contacts;

namespace SOS.CalAccess.WebApi.Tests.Contacts;

[TestFixture]
public class ContactsControllerTests
{
    private ContactsController _controller;
    private ICreatePayee _createPayeeCommandMock;
    private ICreateFilerContact _createFilerContactCommandMock;
    private IUpdateFilerContact _updateFilerContactCommandMock;
    private IDecisionsSvc _decisionsSvcMock;
    private IAuthorizationService _authorizationMock;
    private IFilerContactSvc _filerContactSvcMock;
    private IDateTimeSvc _dateTimeSvc;

    [SetUp]
    public void Setup()
    {
        _createPayeeCommandMock = Substitute.For<ICreatePayee>();
        _createFilerContactCommandMock = Substitute.For<ICreateFilerContact>();
        _updateFilerContactCommandMock = Substitute.For<IUpdateFilerContact>();
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _authorizationMock = Substitute.For<IAuthorizationService>();
        _filerContactSvcMock = Substitute.For<IFilerContactSvc>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();

        _authorizationMock
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<IEnumerable<IAuthorizationRequirement>>())
            .Returns(AuthorizationResult.Success());

        var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
        {
            new (ClaimTypes.NameIdentifier, "test-user"),
            new (ClaimTypes.Name, "Test User")
        }, "mock"));

        _controller = new ContactsController(_authorizationMock, _filerContactSvcMock, _decisionsSvcMock, _dateTimeSvc)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            }
        };
    }

    [Test]
    public async Task CreatePayee_ReturnsCreatedAtAction_WhenIndividualPayeeCreatedSuccessfully()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testFirstName = "Tony";
        var testMiddleName = (string?)null;
        var testLastName = "Stark";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertIndividualPayeeRequest
        {
            FirstName = testFirstName,
            MiddleName = testMiddleName,
            LastName = testLastName,
            Country = testCountry,
            Street = testStreet,
            City = testCity,
            State = testState,
            ZipCode = testZipCode
        };

        var createdContact = new IndividualContact
        {
            Id = expectedContactId,
            Employer = null,
            Occupation = null,
            FirstName = testFirstName,
            MiddleName = testMiddleName,
            LastName = testLastName
        };

        var successfulResult = new Success<FilerContact>(createdContact);

        _createPayeeCommandMock
            .Execute(Arg.Any<CreatePayeeCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        _decisionsSvcMock.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(
               DecisionsWorkflow.PayeeRuleset, Arg.Any<DecisionServicePayee>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.CreatePayee(testId, body, _createPayeeCommandMock);

        Assert.That(result, Is.Not.Null, "Expected result to be of type ActionResult<ContactItemResponse>");
        Assert.That(result.Result, Is.InstanceOf<CreatedAtActionResult>(), "Expected CreatedAtActionResult");

        var createdResult = result.Result as CreatedAtActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(createdResult!.ActionName, Is.EqualTo(nameof(ContactsController.GetContact)), "Action name mismatch");
            Assert.That(createdResult.RouteValues, Is.Not.Null, "RouteValues should not be null");
            Assert.That(createdResult.RouteValues!.ContainsKey("id"), Is.True, "RouteValues should contain 'id'");
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(expectedContactId), "Route ID mismatch");
            Assert.That(createdResult.Value, Is.InstanceOf<ContactItemResponse>(), "Expected ContactItemResponse");
            var contactResponse = createdResult.Value as ContactItemResponse;
            Assert.That(contactResponse!.Id, Is.EqualTo(expectedContactId), "Returned contact ID does not match");
        });
    }

    [Test]
    public async Task CreatePayee_ReturnsCreatedAtAction_WhenOrganizationPayeeCreatedSuccessfully()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testOrganizationName = "Lumon";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertOrganizationPayeeRequest
        {
            OrganizationName = testOrganizationName,
            Country = testCountry,
            Street = testStreet,
            City = testCity,
            State = testState,
            ZipCode = testZipCode
        };

        var createdContact = new OrganizationContact
        {
            Id = expectedContactId,
            OrganizationName = testOrganizationName,
        };

        var successfulResult = new Success<FilerContact>(createdContact);

        _createPayeeCommandMock
            .Execute(Arg.Any<CreatePayeeCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        _decisionsSvcMock.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(
               DecisionsWorkflow.PayeeRuleset, Arg.Any<DecisionServicePayee>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.CreatePayee(testId, body, _createPayeeCommandMock);

        Assert.That(result, Is.Not.Null, "Expected result to be of type ActionResult<ContactItemResponse>");
        Assert.That(result.Result, Is.InstanceOf<CreatedAtActionResult>(), "Expected CreatedAtActionResult");

        var createdResult = result.Result as CreatedAtActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(createdResult!.ActionName, Is.EqualTo(nameof(ContactsController.GetContact)), "Action name mismatch");
            Assert.That(createdResult.RouteValues, Is.Not.Null, "RouteValues should not be null");
            Assert.That(createdResult.RouteValues!.ContainsKey("id"), Is.True, "RouteValues should contain 'id'");
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(expectedContactId), "Route ID mismatch");
            Assert.That(createdResult.Value, Is.InstanceOf<ContactItemResponse>(), "Expected ContactItemResponse");
            var contactResponse = createdResult.Value as ContactItemResponse;
            Assert.That(contactResponse!.Id, Is.EqualTo(expectedContactId), "Returned contact ID does not match");
        });
    }

    [Test]
    public async Task SearchLobbyingCoalitionByNameOrId_ReturnsExpectedResults()
    {
        // Arrange
        var query = "TestQuery";
        var filerId = 12345L;
        var expectedResults = new List<ContactSearchResultDto>
        {
            new() {
                AddressLine1 = "123 Main St",
                AddressLine2 = "Apt 4",
                ContactId = 1,
                FilerId = filerId,
                Name = "Test Name",
                RegistrationFilingId = 1001
            },
            new() {
                AddressLine1 = "456 Elm St",
                AddressLine2 = "Suite 5",
                ContactId = 2,
                FilerId = filerId,
                Name = "Another Name",
                RegistrationFilingId = 1002
            }
        };

        var filerContactSvcMock = Substitute.For<IFilerContactSvc>();
        filerContactSvcMock.SearchLobbyingCoalitionsByNameOrId(query, filerId).Returns(expectedResults);

        var controller = new ContactsController(_authorizationMock, _filerContactSvcMock, _decisionsSvcMock, _dateTimeSvc)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
                    {
                        new (ClaimTypes.NameIdentifier, "test-user"),
                        new (ClaimTypes.Name, "Test User")
                    }, "mock"))
                }
            }
        };

        // Act
        var result = await controller.SearchLobbyingCoalitionByNameOrId(filerContactSvcMock, query, filerId);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be of type IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.InstanceOf<IEnumerable<ContactSearchResultDto>>(), "Expected IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.EqualTo(expectedResults), "Returned results do not match expected results");
    }

    [Test]
    public async Task SearchLobbyingFirmsByNameOrId_ReturnsExpectedResults()
    {
        // Arrange
        var query = "TestQuery";
        var filerId = 12345L;
        var expectedResults = new List<ContactSearchResultDto>
    {
        new() {
            AddressLine1 = "123 Main St",
            AddressLine2 = "Sacramento, CA 95814",
            ContactId = 1,
            FilerId = filerId,
            Name = "Test Firm",
            RegistrationFilingId = 1001
        },
        new() {
            AddressLine1 = "456 Elm St",
            AddressLine2 = "San Francisco, CA 94104",
            ContactId = 2,
            FilerId = filerId,
            Name = "Another Firm",
            RegistrationFilingId = 1002
        }
    };

        _ = _filerContactSvcMock.SearchLobbyingFirmsByNameOrId(query, filerId).Returns(expectedResults);

        // Act
        var result = await _controller.SearchLobbyingFirmsByNameOrId(query, filerId);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be of type IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.InstanceOf<IEnumerable<ContactSearchResultDto>>(), "Expected IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.EqualTo(expectedResults), "Returned results do not match expected results");

        _ = await _filerContactSvcMock.Received(1).SearchLobbyingFirmsByNameOrId(query, filerId);
    }

    [Test]
    public async Task SearchLobbyingFirmsByNameOrId_WithNoResults_ReturnsEmptyCollection()
    {
        // Arrange
        var query = "NonexistentFirm";
        var filerId = 12345L;
        var emptyResults = new List<ContactSearchResultDto>();

        _ = _filerContactSvcMock.SearchLobbyingFirmsByNameOrId(query, filerId).Returns(emptyResults);

        // Act
        var result = await _controller.SearchLobbyingFirmsByNameOrId(query, filerId);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be of type IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.Empty, "Expected an empty collection of results");

        _ = await _filerContactSvcMock.Received(1).SearchLobbyingFirmsByNameOrId(query, filerId);
    }

    [Test]
    public async Task SearchLobbyingFirmsByNameOrId_WithEmptyQuery_ReturnsResults()
    {
        // Arrange
        var query = string.Empty;
        var filerId = 12345L;
        var expectedResults = new List<ContactSearchResultDto>
    {
        new() {
            AddressLine1 = "789 Oak St",
            AddressLine2 = "Los Angeles, CA 90001",
            ContactId = 3,
            FilerId = filerId,
            Name = "Default Firm",
            RegistrationFilingId = 1003
        }
    };

        _ = _filerContactSvcMock.SearchLobbyingFirmsByNameOrId(query, filerId).Returns(expectedResults);

        // Act
        var result = await _controller.SearchLobbyingFirmsByNameOrId(query, filerId);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be of type IEnumerable<ContactSearchResultDto>");
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(1), "Expected one result");
            Assert.That(result.First().Name, Is.EqualTo("Default Firm"), "Name mismatch");
        });

        _ = await _filerContactSvcMock.Received(1).SearchLobbyingFirmsByNameOrId(query, filerId);
    }


    [Test]
    public async Task SearchContactsByNameOrId_WithValidModelState_ReturnsSearchResults()
    {
        // Arrange
        var searchText = "test";
        var filerId = 12345L;
        var expectedContactTypes = new List<string> { "Individual", "Organization" };
        var expectedResults = new List<ContactSearchResultDto>
    {
        new() {
            AddressLine1 = "123 Main St",
            AddressLine2 = "Apt 4",
            ContactId = 1,
            FilerId = filerId,
            Name = "Test Name",
            RegistrationFilingId = 1001
        },
        new() {
            AddressLine1 = "456 Elm St",
            AddressLine2 = "Suite 5",
            ContactId = 2,
            FilerId = filerId,
            Name = "Another Name",
            RegistrationFilingId = 1002
        }
    };

        var filerContactSvcMock = Substitute.For<IFilerContactSvc>();
        _ = filerContactSvcMock.SearchFilerContactsByNameOrId(
            filerId,
            searchText,
            Arg.Is<List<string>>(types =>
                types.Count == expectedContactTypes.Count &&
                types.All(t => expectedContactTypes.Contains(t))))
            .Returns(expectedResults);

        // Act
        var result = await _controller.SearchContactsByNameOrId(filerContactSvcMock, filerId, searchText, expectedContactTypes);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be of type IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.InstanceOf<IEnumerable<ContactSearchResultDto>>(), "Expected IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.EqualTo(expectedResults), "Returned results do not match expected results");
    }

    [Test]
    public async Task SearchContactsByNameOrId_WithNoResults_ReturnsEmptyList()
    {
        // Arrange
        var searchText = "nonexistent";
        var filerId = 12345L;
        var emptyResults = new List<ContactSearchResultDto>();

        var filerContactSvcMock = Substitute.For<IFilerContactSvc>();
        _ = filerContactSvcMock.SearchFilerContactsByNameOrId(
            Arg.Any<long>(),
            Arg.Any<string>(),
            Arg.Any<List<string>>())
            .Returns(emptyResults);

        // Act
        var result = await _controller.SearchContactsByNameOrId(filerContactSvcMock, filerId, searchText, new List<string>());

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be of type IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.InstanceOf<IEnumerable<ContactSearchResultDto>>(), "Expected IEnumerable<ContactSearchResultDto>");
        Assert.That(result, Is.Empty, "Expected an empty list of results");
    }

    [Test]
    public async Task CreateFilerContact_ReturnsCreatedAtAction()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testOrganizationName = "Lumon";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = testOrganizationName,
            Addresses = new List<AddressDto>()
            {
                new()
                {
                    Country = testCountry,
                    Street = testStreet,
                    City = testCity,
                    State = testState,
                    Zip = testZipCode
                }
            },
            EmailAddresses = new List<EmailAddressDto>()
            {
                new()
                {
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "test"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>()
            {
                new()
                {
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Phone"
                },
                new()
                {
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Fax"
                }
            }
        };

        var createdContact = new OrganizationContact
        {
            Id = expectedContactId,
            OrganizationName = testOrganizationName,
        };

        var successfulResult = new Success<FilerContact>(createdContact);

        _createFilerContactCommandMock
            .Execute(Arg.Any<CreateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        _decisionsSvcMock.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(
               DecisionsWorkflow.PayeeRuleset, Arg.Any<DecisionServicePayee>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.CreateFilerContact(testId, body, _createFilerContactCommandMock, CancellationToken.None);

        Assert.That(result, Is.Not.Null, "Expected result to be of type ActionResult<ContactItemResponse>");
        Assert.That(result.Result, Is.InstanceOf<CreatedAtActionResult>(), "Expected CreatedAtActionResult");

        var createdResult = result.Result as CreatedAtActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(createdResult!.ActionName, Is.EqualTo(nameof(ContactsController.CreateFilerContact)), "Action name mismatch");
            Assert.That(createdResult.RouteValues, Is.Not.Null, "RouteValues should not be null");
            Assert.That(createdResult.RouteValues!.ContainsKey("id"), Is.True, "RouteValues should contain 'id'");
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(expectedContactId), "Route ID mismatch");
            Assert.That(createdResult.Value, Is.InstanceOf<ContactItemResponse>(), "Expected ContactItemResponse");
            var contactResponse = createdResult.Value as ContactItemResponse;
            Assert.That(contactResponse!.Id, Is.EqualTo(expectedContactId), "Returned contact ID does not match");
        });
    }

    [Test]
    public async Task CreateFilerContact_FilerCommiteeContact_ReturnsCreatedAtAction()
    {
        // Arrange
        var filerId = 1;
        var committeeName = "Committee Name";
        var request = new UpsertFilerCommitteeFilerContactRequest
        {
            CommitteeName = committeeName,
        };

        var createdContact = new FilerCommitteeContact
        {
            Id = 1,
            CommitteeName = committeeName,
            AddressList = GenerateAddressList(),
            PhoneNumberList = GeneratePhoneNumberList(),
            EmailAddressList = GenerateEmailAddressList(),
        };

        var successfulResult = new Success<FilerContact>(createdContact);

        _createFilerContactCommandMock
            .Execute(Arg.Any<CreateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        // Act
        var result = await _controller.CreateFilerContact(filerId, request, _createFilerContactCommandMock, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Expected result to be of type ActionResult<ContactItemResponse>");
            Assert.That(result.Result, Is.InstanceOf<CreatedAtActionResult>(), "Expected CreatedAtActionResult");

            var createdResult = result.Result as CreatedAtActionResult;

            Assert.That(createdResult!.ActionName, Is.EqualTo(nameof(ContactsController.CreateFilerContact)), "Action name mismatch");
            Assert.That(createdResult.RouteValues, Is.Not.Null, "RouteValues should not be null");
            Assert.That(createdResult.RouteValues!.ContainsKey("id"), Is.True, "RouteValues should contain 'id'");
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(createdContact.Id), "Route ID mismatch");
            Assert.That(createdResult.Value, Is.InstanceOf<ContactItemResponse>(), "Expected ContactItemResponse");
            var contactResponse = createdResult.Value as ContactItemResponse;
            Assert.That(contactResponse!.Id, Is.EqualTo(createdContact.Id), "Returned contact ID does not match");
        });
    }

    [Test]
    public async Task CreateFilerContact_CandidateContact_ReturnsCreatedAtAction()
    {
        // Arrange
        var filerId = 1;
        var request = new UpsertCandidateFilerContactRequest
        {
            FirstName = "John1",
            LastName = "Doe1",
            District = "TestDistrict",
            Jurisdiction = "TestJurisdiction",
            OfficeSought = "TestOfficeSought",
        };

        var createdContact = new CandidateContact
        {
            Id = 1,
            FirstName = "John1",
            LastName = "Doe1",
            District = "TestDistrict",
            Jurisdiction = "TestJurisdiction",
            OfficeSought = "TestOfficeSought",
        };

        var successfulResult = new Success<FilerContact>(createdContact);

        _createFilerContactCommandMock
            .Execute(Arg.Any<CreateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        // Act
        var result = await _controller.CreateFilerContact(filerId, request, _createFilerContactCommandMock, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Expected result to be of type ActionResult<ContactItemResponse>");
            Assert.That(result.Result, Is.InstanceOf<CreatedAtActionResult>(), "Expected CreatedAtActionResult");

            var createdResult = result.Result as CreatedAtActionResult;

            Assert.That(createdResult!.ActionName, Is.EqualTo(nameof(ContactsController.CreateFilerContact)), "Action name mismatch");
            Assert.That(createdResult.RouteValues, Is.Not.Null, "RouteValues should not be null");
            Assert.That(createdResult.RouteValues!.ContainsKey("id"), Is.True, "RouteValues should contain 'id'");
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(createdContact.Id), "Route ID mismatch");
            Assert.That(createdResult.Value, Is.InstanceOf<ContactItemResponse>(), "Expected ContactItemResponse");
            var contactResponse = createdResult.Value as ContactItemResponse;
            Assert.That(contactResponse!.Id, Is.EqualTo(createdContact.Id), "Returned contact ID does not match");
        });
    }

    [Test]
    public async Task UpdateFilerContact_ReturnsCreatedAtAction()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testOrganizationName = "Lumon";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = testOrganizationName,
            Addresses = new List<AddressDto>()
            {
                new()
                {
                    Id = 1,
                    Country = testCountry,
                    Street = testStreet,
                    City = testCity,
                    State = testState,
                    Zip = testZipCode
                }
            },
            EmailAddresses = new List<EmailAddressDto>()
            {
                new()
                {
                    Id = 1,
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "test"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>()
            {
                new()
                {
                    Id = 1,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Phone"
                },
                new()
                {
                    Id = 2,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Fax"
                }
            }
        };

        var updatedContact = new OrganizationContact
        {
            Id = expectedContactId,
            OrganizationName = testOrganizationName,
        };

        var successfulResult = new Success<FilerContact>(updatedContact);

        _updateFilerContactCommandMock
            .Execute(Arg.Any<UpdateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        _decisionsSvcMock.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(
               DecisionsWorkflow.PayeeRuleset, Arg.Any<DecisionServicePayee>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.UpdateFilerContact(testId, body, _updateFilerContactCommandMock, CancellationToken.None);

        Assert.That(result, Is.Not.Null, "Expected result to be of type ActionResult<ContactItemResponse>");
        Assert.That(result.Result, Is.InstanceOf<ActionResult>(), "Expected NoContentResult");
    }

    [Test]
    public async Task UpdateFilerContact_Organization_ReturnsDecisionsError()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testOrganizationName = "Lumon";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = testOrganizationName,
            Addresses = new List<AddressDto>()
            {
                new()
                {
                    Id = 1,
                    Country = testCountry,
                    Street = testStreet,
                    City = testCity,
                    State = testState,
                    Zip = testZipCode
                }
            },
            EmailAddresses = new List<EmailAddressDto>()
            {
                new()
                {
                    Id = 1,
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "test"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>()
            {
                new()
                {
                    Id = 1,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Phone"
                },
                new()
                {
                    Id = 2,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Fax"
                }
            },
            FilerContactForm = "UpsertPayee"
        };

        var updatedContact = new OrganizationContact
        {
            Id = expectedContactId,
            OrganizationName = testOrganizationName,
        };

        var successfulResult = new Success<FilerContact>(updatedContact);

        var validationErrors = new List<WorkFlowError>
        {
            new("Some Field", "Err001", "Validation", "Some field is invalid.")
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(
               DecisionsWorkflow.PayeeRuleset, Arg.Any<DecisionServicePayee>(), true)
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _controller.UpdateFilerContact(testId, body, _updateFilerContactCommandMock, CancellationToken.None);

        //Assert
        Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
        var objectResult = (ObjectResult)result.Result;

        Assert.Multiple(() =>
        {
            Assert.That(objectResult.StatusCode, Is.EqualTo(422));
            Assert.That(objectResult.Value, Is.EqualTo(validationErrors));
        });
    }

    [Test]
    public async Task UpdateFilerContactPayor_Organization_ReturnsDecisionsError()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testOrganizationName = "Lumon";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = testOrganizationName,
            Addresses = new List<AddressDto>()
            {
                new()
                {
                    Id = 1,
                    Country = testCountry,
                    Street = testStreet,
                    City = testCity,
                    State = testState,
                    Zip = testZipCode
                }
            },
            EmailAddresses = new List<EmailAddressDto>()
            {
                new()
                {
                    Id = 1,
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "test"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>()
            {
                new()
                {
                    Id = 1,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Phone"
                },
                new()
                {
                    Id = 2,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Fax"
                }
            },
            FilerContactForm = "UpsertPayor"
        };

        var updatedContact = new OrganizationContact
        {
            Id = expectedContactId,
            OrganizationName = testOrganizationName,
        };

        var successfulResult = new Success<FilerContact>(updatedContact);

        var validationErrors = new List<WorkFlowError>
        {
            new("Some Field", "Err001", "Validation", "Some field is invalid.")
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
               DecisionsWorkflow.TransactionPayorInformationRuleSet, Arg.Any<DecisionServiceTransactor>(), true)
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _controller.UpdateFilerContact(testId, body, _updateFilerContactCommandMock, CancellationToken.None);

        //Assert
        Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
        var objectResult = (ObjectResult)result.Result;

        Assert.Multiple(() =>
        {
            Assert.That(objectResult.StatusCode, Is.EqualTo(422));
            Assert.That(objectResult.Value, Is.EqualTo(validationErrors));
        });
    }

    [Test]
    public async Task UpdateFilerContactPayee_Organization_ReturnsDecisionsError()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testOrganizationName = "Lumon";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = testOrganizationName,
            Addresses = new List<AddressDto>()
            {
                new()
                {
                    Id = 1,
                    Country = testCountry,
                    Street = testStreet,
                    City = testCity,
                    State = testState,
                    Zip = testZipCode
                }
            },
            EmailAddresses = new List<EmailAddressDto>()
            {
                new()
                {
                    Id = 1,
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "test"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>()
            {
                new()
                {
                    Id = 1,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Phone"
                },
                new()
                {
                    Id = 2,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Fax"
                }
            },
            FilerContactForm = "PayeeInformation"
        };

        var updatedContact = new OrganizationContact
        {
            Id = expectedContactId,
            OrganizationName = testOrganizationName,
        };

        var successfulResult = new Success<FilerContact>(updatedContact);

        var validationErrors = new List<WorkFlowError>
        {
            new("Some Field", "Err001", "Validation", "Some field is invalid.")
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
               DecisionsWorkflow.TransactionPayeeInformationRuleSet, Arg.Any<DecisionServiceTransactor>(), true)
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _controller.UpdateFilerContact(testId, body, _updateFilerContactCommandMock, CancellationToken.None);

        //Assert
        Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
        var objectResult = (ObjectResult)result.Result;

        Assert.Multiple(() =>
        {
            Assert.That(objectResult.StatusCode, Is.EqualTo(422));
            Assert.That(objectResult.Value, Is.EqualTo(validationErrors));
        });
    }

    [Test]
    public async Task UpdateFilerContact_Individual_ReturnsDecisionsError()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;

        var testFirstName = "Mark";
        var testLastName = "Scount";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";
        var body = new UpsertIndividualFilerContactRequest
        {
            FirstName = testFirstName,
            LastName = testLastName,
            Employer = string.Empty,
            Occupation = string.Empty,
            MiddleName = string.Empty,
            Addresses = new List<AddressDto>()
            {
                new()
                {
                    Id = 1,
                    Country = testCountry,
                    Street = testStreet,
                    City = testCity,
                    State = testState,
                    Zip = testZipCode
                }
            },
            EmailAddresses = new List<EmailAddressDto>()
            {
                new()
                {
                    Id = 1,
                    Email = "<EMAIL>",
                    Status = "test",
                    VerificationCode = "test"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>()
            {
                new()
                {
                    Id = 1,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Phone"
                },
                new()
                {
                    Id = 2,
                    Extension = "test",
                    CountryCode = "USA",
                    InternationalNumber = false,
                    Number = "123123",
                    Type = "Fax"
                }
            },
            FilerContactForm = "UpsertPayee"
        };

        var updatedContact = new IndividualContact
        {
            Id = expectedContactId,
            FirstName = testFirstName,
            LastName = testLastName,
            Employer = string.Empty,
            Occupation = string.Empty,
            MiddleName = string.Empty,
        };

        var successfulResult = new Success<FilerContact>(updatedContact);

        var validationErrors = new List<WorkFlowError>
        {
            new("Some Field", "Err001", "Validation", "Some field is invalid.")
        };

        _decisionsSvcMock.InitiateWorkflow<DecisionServicePayee, List<WorkFlowError>>(
               DecisionsWorkflow.PayeeRuleset, Arg.Any<DecisionServicePayee>(), true)
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _controller.UpdateFilerContact(testId, body, _updateFilerContactCommandMock, CancellationToken.None);

        //Assert
        Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
        var objectResult = (ObjectResult)result.Result;

        Assert.Multiple(() =>
        {
            Assert.That(objectResult.StatusCode, Is.EqualTo(422));
            Assert.That(objectResult.Value, Is.EqualTo(validationErrors));
        });
    }

    [Test]
    public async Task ValidateCreateFilerContact_IndividualPayor_ReturnsValidationErrors()
    {
        var body = new UpsertIndividualFilerContactRequest
        {
            FirstName = "John",
            LastName = "Doe",
            MiddleName = "M",
            Employer = "TestCo",
            FilerContactForm = "UpsertPayor",
            Addresses = new List<AddressDto> {
            new() {
                Street = "123 Main St",
                Street2 = "Apt 4",
                City = "Sacramento",
                State = "CA",
                Zip = "95814",
                Country = "USA",
                Purpose = "Mailing",
                Type = "Business"
            }
        },
            EmailAddresses = new List<EmailAddressDto> {
            new() {
                Email = "<EMAIL>",
                Status = "Active",
                VerificationCode = "ABC123"
            }
        },
            PhoneNumbers = new List<PhoneNumberDto> {
            new() {
                Number = "555-1234",
                CountryCode = "1",
                Type = "Mobile",
                InternationalNumber = false
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
    {
        new("FirstName", "E001", "Validation", "First name required.")
    };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayorInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result[0].FieldName, Is.EqualTo("FirstName"));
        });
    }

    [Test]
    public async Task ValidateCreateFilerContact_CommitteePayor_ReturnsValidationErrors()
    {
        var body = new UpsertFilerCommitteeFilerContactRequest
        {
            CommitteeName = "Save the Trees",
            CommitteeId = 123,
            FilerContactForm = "UpsertPayor",
            Addresses = new List<AddressDto> {
            new() {
                Street = "1 Tree Lane",
                City = "Oakland",
                State = "CA",
                Zip = "94607",
                Country = "USA",
                Type = "Business",
                Purpose = "Mailing"
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
    {
        new("CommitteeId", "E002", "Validation", "Committee ID is missing.")
    };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayorInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.That(result.Any(e => e.FieldName == "CommitteeId"));
    }

    [Test]
    public async Task ValidateCreateFilerContact_CandidatePayor_ReturnsValidationErrors()
    {
        var body = new UpsertCandidateFilerContactRequest
        {
            FirstName = "Alice",
            LastName = "Smith",
            MiddleName = "K",
            OfficeSought = "Mayor",
            Jurisdiction = "Springfield",
            District = "3",
            FilerContactForm = "UpsertPayor",
            Addresses = new List<AddressDto> {
            new() {
                Street = "789 Main St",
                City = "Springfield",
                State = "IL",
                Zip = "62704",
                Country = "USA",
                Type = "Business",
                Purpose = "Mailing"
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
    {
        new("OfficeSought", "E003", "Validation", "Office is required.")
    };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayorInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.That(result.Any(e => e.FieldName == "OfficeSought"));
    }

    [Test]
    public async Task ValidateCreateFilerContact_OrganizationPayor_ReturnsValidationErrors()
    {
        var body = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = "Citizens for Change",
            FilerContactForm = "UpsertPayor",
            Addresses = new List<AddressDto> {
            new() {
                Street = "456 Maple Ave",
                City = "Denver",
                State = "CO",
                Zip = "80202",
                Country = "USA",
                Type = "Business",
                Purpose = "Mailing"
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
        {
            new("OrganizationName", "E004", "Validation", "Organization name required.")
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayorInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.That(result.Any(e => e.FieldName == "OrganizationName"));
    }

    [Test]
    public async Task ValidateCreateFilerContact_IndividualPayee_ReturnsValidationErrors()
    {
        var body = new UpsertIndividualFilerContactRequest
        {
            FirstName = "John",
            LastName = "Doe",
            MiddleName = "M",
            Employer = "TestCo",
            FilerContactForm = "PayeeInformation",
            Addresses = new List<AddressDto> {
            new() {
                Street = "123 Main St",
                Street2 = "Apt 4",
                City = "Sacramento",
                State = "CA",
                Zip = "95814",
                Country = "USA",
                Purpose = "Mailing",
                Type = "Business"
            }
        },
            EmailAddresses = new List<EmailAddressDto> {
            new() {
                Email = "<EMAIL>",
                Status = "Active",
                VerificationCode = "ABC123"
            }
        },
            PhoneNumbers = new List<PhoneNumberDto> {
            new() {
                Number = "555-1234",
                CountryCode = "1",
                Type = "Mobile",
                InternationalNumber = false
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
        {
            new("FirstName", "E001", "Validation", "First name required.")
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayeeInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result[0].FieldName, Is.EqualTo("FirstName"));
        });
    }

    [Test]
    public async Task ValidateCreateFilerContact_CommitteePayee_ReturnsValidationErrors()
    {
        var body = new UpsertFilerCommitteeFilerContactRequest
        {
            CommitteeName = "Save the Trees",
            CommitteeId = 123,
            FilerContactForm = "PayeeInformation",
            Addresses = new List<AddressDto> {
            new() {
                Street = "1 Tree Lane",
                City = "Oakland",
                State = "CA",
                Zip = "94607",
                Country = "USA",
                Type = "Business",
                Purpose = "Mailing"
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
        {
            new("CommitteeId", "E002", "Validation", "Committee ID is missing.")
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayeeInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.That(result.Any(e => e.FieldName == "CommitteeId"));
    }

    [Test]
    public async Task ValidateCreateFilerContact_CandidatePayee_ReturnsValidationErrors()
    {
        var body = new UpsertCandidateFilerContactRequest
        {
            FirstName = "Alice",
            LastName = "Smith",
            MiddleName = "K",
            OfficeSought = "Mayor",
            Jurisdiction = "Springfield",
            District = "3",
            FilerContactForm = "PayeeInformation",
            Addresses = new List<AddressDto> {
            new() {
                Street = "789 Main St",
                City = "Springfield",
                State = "IL",
                Zip = "62704",
                Country = "USA",
                Type = "Business",
                Purpose = "Mailing"
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
        {
            new("OfficeSought", "E003", "Validation", "Office is required.")
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayeeInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.That(result.Any(e => e.FieldName == "OfficeSought"));
    }

    [Test]
    public async Task ValidateCreateFilerContact_OrganizationPayee_ReturnsValidationErrors()
    {
        var body = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = "Citizens for Change",
            FilerContactForm = "PayeeInformation",
            Addresses = new List<AddressDto> {
            new() {
                Street = "456 Maple Ave",
                City = "Denver",
                State = "CO",
                Zip = "80202",
                Country = "USA",
                Type = "Business",
                Purpose = "Mailing"
            }
        }
        };

        var expectedErrors = new List<WorkFlowError>
        {
            new("OrganizationName", "E004", "Validation", "Organization name required.")
        };

        _decisionsSvcMock
            .InitiateWorkflow<DecisionServiceTransactor, List<WorkFlowError>>(
                DecisionsWorkflow.TransactionPayeeInformationRuleSet,
                Arg.Any<DecisionServiceTransactor>(),
                true)
            .Returns(expectedErrors);

        var result = await _controller.ValidateCreateFilerContact(body);

        Assert.That(result.Any(e => e.FieldName == "OrganizationName"));
    }

    [Test]
    public async Task UpdateFilerContact_FilerCommiteeContact_ReturnsCreatedAtAction()
    {
        // Arrange
        var contactId = 1;
        var committeeName = "Committee Name";
        var request = new UpsertFilerCommitteeFilerContactRequest
        {
            CommitteeName = committeeName,
        };

        var updatedContact = new FilerCommitteeContact
        {
            Id = 1,
            CommitteeName = committeeName,
        };

        var successfulResult = new Success<FilerContact>(updatedContact);

        _updateFilerContactCommandMock
            .Execute(Arg.Any<UpdateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        // Act
        var result = await _controller.UpdateFilerContact(contactId, request, _updateFilerContactCommandMock, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<ActionResult>());
        });
    }

    [Test]
    public async Task UpdateFilerContact_CandidateContact_ReturnsCreatedAtAction()
    {
        // Arrange
        var contactId = 1;
        var request = new UpsertCandidateFilerContactRequest
        {
            FirstName = "John1",
            LastName = "Doe1",
            District = "TestDistrict",
            Jurisdiction = "TestJurisdiction",
            OfficeSought = "TestOfficeSought",
        };

        var updatedContact = new CandidateContact
        {
            Id = 1,
            FirstName = "John1",
            LastName = "Doe1",
            District = "TestDistrict",
            Jurisdiction = "TestJurisdiction",
            OfficeSought = "TestOfficeSought",
        };

        var successfulResult = new Success<FilerContact>(updatedContact);

        _updateFilerContactCommandMock
            .Execute(Arg.Any<UpdateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        // Act
        var result = await _controller.UpdateFilerContact(contactId, request, _updateFilerContactCommandMock, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<ActionResult>());
        });
    }

    [Test]
    public async Task UpdateFilerContact_OrganizationContact_ReturnsNotFound()
    {
        // Arrange
        var contactId = 1;
        var request = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = "Test Org 1"
        };

        var failureResult = new Failure<FilerContact>.NotFound("No contact was found with the requested id");

        _updateFilerContactCommandMock
            .Execute(Arg.Any<UpdateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(failureResult);

        // Act
        var result = await _controller.UpdateFilerContact(contactId, request, _updateFilerContactCommandMock, CancellationToken.None);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<NotFoundObjectResult>());
        });
    }

    [Test]
    public async Task GetFilerContactById_ReturnsExpectedResponse()
    {
        // Arrange
        var contactId = 123L;
        var expectedResponse = new IndividualContactResponseDto(
            id: contactId,
            filerId: 456,
            typeId: FilerContactType.Individual.Id,
            addresses: new List<AddressDto>(),
            phoneNumbers: new List<PhoneNumberDto>(),
            emailAddresses: new List<EmailAddress>(),
            firstName: "John",
            lastName: "Doe",
            middleName: "David",
            employer: "Acme Corp",
            occupation: "Developer"
        );

        var filerContactSvcMock = Substitute.For<IFilerContactSvc>();
        _ = filerContactSvcMock.GetFilerContactById(contactId).Returns(expectedResponse);

        var controller = new ContactsController(_authorizationMock, filerContactSvcMock, _decisionsSvcMock, _dateTimeSvc);

        // Act
        var result = await controller.GetFilerContactById(contactId);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be non-null");
        Assert.That(result, Is.InstanceOf<ContactResponseDto>(), "Expected result to be of type ContactResponseDto");
        Assert.That(result, Is.InstanceOf<IndividualContactResponseDto>(), "Expected result to be of type IndividualContactResponseDto");

        var individualResponse = (IndividualContactResponseDto)result!;
        Assert.Multiple(() =>
        {
            Assert.That(individualResponse.Id, Is.EqualTo(contactId), "Contact ID mismatch");
            Assert.That(individualResponse.FilerId, Is.EqualTo(456), "Filer ID mismatch");
            Assert.That(individualResponse.TypeId, Is.EqualTo(FilerContactType.Individual.Id), "Type ID mismatch");
            Assert.That(individualResponse.FirstName, Is.EqualTo("John"), "First name mismatch");
            Assert.That(individualResponse.LastName, Is.EqualTo("Doe"), "Last name mismatch");
        });

        _ = await filerContactSvcMock.Received(1).GetFilerContactById(contactId);
    }

    [Test]
    public async Task GetFilerContactById_WhenContactNotFound_ReturnsNull()
    {
        // Arrange
        var contactId = 999L;
        var filerContactSvcMock = Substitute.For<IFilerContactSvc>();
        _ = filerContactSvcMock.GetFilerContactById(contactId).Returns((ContactResponseDto?)null);

        var controller = new ContactsController(_authorizationMock, filerContactSvcMock, _decisionsSvcMock, _dateTimeSvc);

        // Act
        var result = await controller.GetFilerContactById(contactId);

        // Assert
        Assert.That(result, Is.Null, "Expected result to be null when contact not found");

        _ = await filerContactSvcMock.Received(1).GetFilerContactById(contactId);
    }

    [Test]
    public async Task ValidateCreateFilerContact_Should_Process_IndividualContact()
    {
        var addresses = new List<AddressDto>
        {
            new()
            {
                Id = 12345,
                Street = "123 Main St",
                Street2 = "Apt 4B",
                City = "New York",
                State = "NY",
                Zip = "10001",
                Type = "Residential",
                Country = "USA",
                Purpose = "Billing"
            }
        };
        var emails = new List<EmailAddressDto>
        {
            new ()
            {
                Id = 101,
                Email = "<EMAIL>",
                Status = "Verified",
                VerificationCode = "ABC123"
            }
        };
        var phoneNumbers = new List<PhoneNumberDto>
        {
            new()
            {
                Id = 1001,
                InternationalNumber = true,
                CountryCode = "+1",
                Extension = "212",
                Number = "555-1234",
                Type = "Mobile"
            }
        };

        // Arrange
        var request = new UpsertIndividualFilerContactRequest()
        {
            Addresses = addresses,
            EmailAddresses = emails,
            FilerContactForm = "PaymentReceiveLobbyingCoalition",
            FirstName = "John",
            MiddleName = string.Empty,
            LastName = "Doe",
            Employer = string.Empty,
            Occupation = string.Empty,
            PhoneNumbers = phoneNumbers,
        };

        _decisionsSvcMock.InitiateWorkflow<PaymentReceiveLobbyingCoalitionMemberDs, List<WorkFlowError>>(
               DecisionsWorkflow.PaymentReceiveLobbyingCoalitionMemberRuleset, It.IsAny<PaymentReceiveLobbyingCoalitionMemberDs>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.ValidateCreateFilerContact(request);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task ValidateCreateFilerContact_Should_Process_OrganizationContact()
    {
        // Arrange  
        var addresses = new List<AddressDto>
       {
           new()
           {
               Id = 12345,
               Street = "123 Main St",
               Street2 = "Apt 4B",
               City = "New York",
               State = "NY",
               Zip = "10001",
               Type = "Residential",
               Country = "USA",
               Purpose = "Billing"
           }
       };
        var emails = new List<EmailAddressDto>
       {
           new ()
           {
               Id = 101,
               Email = "<EMAIL>",
               Status = "Verified",
               VerificationCode = "ABC123"
           }
       };
        var phoneNumbers = new List<PhoneNumberDto>
       {
           new()
           {
               Id = 1001,
               InternationalNumber = true,
               CountryCode = "+1",
               Extension = "212",
               Number = "555-1234",
               Type = "Mobile"
           }
       };

        var request = new UpsertOrganizationFilerContactRequest
        {
            FilerContactForm = "PaymentReceiveLobbyingCoalition",
            OrganizationName = "TechCorp",
            Addresses = addresses,
            EmailAddresses = emails,
            PhoneNumbers = phoneNumbers
        };

        _ = _decisionsSvcMock.InitiateWorkflow<PaymentReceiveLobbyingCoalitionMemberDs, List<WorkFlowError>>(
               DecisionsWorkflow.PaymentReceiveLobbyingCoalitionMemberRuleset, It.IsAny<PaymentReceiveLobbyingCoalitionMemberDs>(), true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act  
        var result = await _controller.ValidateCreateFilerContact(request);

        // Assert  
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task ValidateCreateFilerContact_Should_Process_PaymentToLobbyingFirm_OrganizationContact()
    {
        // Arrange
        var addresses = new List<AddressDto>
        {
            new()
            {
                Street = "123 Main St",
                Street2 = "Suite 100",
                City = "Sacramento",
                State = "CA",
                Zip = "95814",
                Country = "USA"
            }
        };

        var request = new UpsertOrganizationFilerContactRequest
        {
            OrganizationName = "TechCorp",
            Addresses = addresses,
            EmailAddresses = new List<EmailAddressDto>(),
            PhoneNumbers = new List<PhoneNumberDto>(),
            FilerContactForm = "PaymentToLobbyingFirm"
        };

        var expectedWorkflowData = new PaymentMadeToLobbyingFirmContactDs
        {
            Address1 = new PaymentMadeToLobbyingFirmContactAddress
            {
                Country = "USA",
                Street = "123 Main St",
                Street2 = "Suite 100",
                City = "Sacramento",
                State = "CA",
                Zip = "95814"
            },
            LobbyingFirmName = "TechCorp"
        };

        _ = _decisionsSvcMock
            .InitiateWorkflow<PaymentMadeToLobbyingFirmContactDs, List<WorkFlowError>>(
                DecisionsWorkflow.PaymentMadeToLobbyingFirmsContactRuleset,
                Arg.Is<PaymentMadeToLobbyingFirmContactDs>(data =>
                    data.Address1!.Country == expectedWorkflowData.Address1.Country &&
                    data.Address1.Street == expectedWorkflowData.Address1.Street &&
                    data.LobbyingFirmName == expectedWorkflowData.LobbyingFirmName),
                true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.ValidateCreateFilerContact(request);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be non-null");
        Assert.That(result, Is.Empty, "Expected no validation errors");
        _ = await _decisionsSvcMock.Received(1).InitiateWorkflow<PaymentMadeToLobbyingFirmContactDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentMadeToLobbyingFirmsContactRuleset,
            Arg.Any<PaymentMadeToLobbyingFirmContactDs>(),
            true);
    }

    [Test]
    public async Task ValidateCreateFilerContact_Should_Return_ValidationErrors_For_PaymentToLobbyingFirm()
    {
        // Arrange
        var addresses = new List<AddressDto>
        {
            new()
            {
                Street = "123 Main St",
                Street2 = "Suite 100",
                City = "Sacramento",
                State = "CA",
                Zip = "95814",
                Country = "USA"
            }
        };

        var emailAddresses = new List<EmailAddressDto>
        {
            new()
            {
                Email = "<EMAIL>",
                Status = "Verified",
                VerificationCode = "12345"
            }
        };

        var phoneNumbers = new List<PhoneNumberDto>
        {
            new()
            {
                Extension = "916",
                CountryCode = "1",
                Number = "5551234",
                Type = "Work"
            }
        };

        var request = new UpsertOrganizationFilerContactRequest
        {
            FilerContactForm = "PaymentToLobbyingFirm",
            OrganizationName = "TechCorp",
            Addresses = addresses,
            EmailAddresses = emailAddresses,
            PhoneNumbers = phoneNumbers
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("Address1.Street", "Err001", "Validation", "Street is required."),
            new("LobbyingFirmName", "Err002", "Validation", "Lobbying firm name is required.")
        };

        _ = _decisionsSvcMock
            .InitiateWorkflow<PaymentMadeToLobbyingFirmContactDs, List<WorkFlowError>>(
                DecisionsWorkflow.PaymentMadeToLobbyingFirmsContactRuleset,
                Arg.Any<PaymentMadeToLobbyingFirmContactDs>(),
                true)
            .Returns(Task.FromResult(validationErrors));

        // Act
        var result = await _controller.ValidateCreateFilerContact(request);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be non-null");
        Assert.That(result, Is.Not.Empty, "Expected validation errors");
        Assert.That(result, Has.Count.EqualTo(validationErrors.Count), "Validation error count mismatch");
        Assert.That(result, Is.EqualTo(validationErrors), "Validation errors do not match expected errors");
    }

    [Test]
    public async Task ValidateCreateFilerContact_ShouldInvokeEndOfSessionWorkflow_WhenFormIsEndOfSession()
    {
        // Arrange
        var request = new UpsertOrganizationFilerContactRequest
        {
            FilerContactForm = "EndOfSession",
            OrganizationName = "Test Org",
            Addresses = new List<AddressDto>
        {
            new() {
                Street = "123 Main St",
                Street2 = "Suite 100",
                City = "Sacramento",
                State = "CA",
                Zip = "95814",
                Country = "USA"
            }
        },
            EmailAddresses = new List<EmailAddressDto>()
        };

        var expectedWorkflowResult = new List<WorkFlowError>();

        _decisionsSvcMock
            .InitiateWorkflow<EndOfSessionLobbyingContactDs, List<WorkFlowError>>(
                DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransaction,
                Arg.Any<EndOfSessionLobbyingContactDs>(),
                true)
            .Returns(expectedWorkflowResult);

        // Act
        var result = await _controller.ValidateCreateFilerContact(request);

        // Assert
        Assert.That(result, Is.EqualTo(expectedWorkflowResult));

        await _decisionsSvcMock.Received(1).InitiateWorkflow<EndOfSessionLobbyingContactDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransaction,
            Arg.Is<EndOfSessionLobbyingContactDs>(ds =>
                ds.Address1 != null &&
                ds.Address1.Street == "123 Main St" &&
                ds.Address1.City == "Sacramento" &&
                ds.Address1.State == "CA" &&
                ds.Address1.Zip == "95814" &&
                ds.Address1.Country == "USA" &&
                ds.LobbyingFirmName == "Test Org"
            ),
            true
        );
    }

    [Test]
    public async Task UpdateFilerContact_EndOfSessionRequest_WithValidationErrors_ReturnsUnprocessableEntity()
    {
        // Arrange
        var id = 1L;
        var request = new UpsertOrganizationFilerContactRequest
        {
            FilerContactForm = "EndOfSession",
            OrganizationName = "Test Org",
            Addresses = new List<AddressDto>
            {
                new()
                {
                    Country = "US", Street = "123 Main St", City = "City", State = "CA", Zip = "90000"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>
            {
                new() { Number = "1234567890", CountryCode = "+1-USA" }
            }
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("Validation error", "Err001", "Validation", "Field is required")
        };

        _decisionsSvcMock.InitiateWorkflow<EndOfSessionLobbyingContactDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransaction,
            Arg.Any<EndOfSessionLobbyingContactDs>(),
            true
        ).Returns(validationErrors);

        // Act
        var result = await _controller.UpdateFilerContact(id, request, _updateFilerContactCommandMock);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<UnprocessableEntityObjectResult>());
        var objectResult = result.Result as UnprocessableEntityObjectResult;
        Assert.That(objectResult!.Value, Is.EqualTo(validationErrors));

        await _updateFilerContactCommandMock.DidNotReceive().Execute(Arg.Any<UpdateFilerContactCommand>(), Arg.Any<CancellationToken>());
    }

    [Test]
    public void UpdateFilerContact_EndOfSessionRequest_WithInvalidRequestType_ThrowsNotSupportedException()
    {
        // Arrange
        var id = 1L;
        var request = new UpsertIndividualFilerContactRequest // Assume this is *not* UpsertOrganizationFilerContactRequest
        {
            FilerContactForm = "EndOfSession",
            FirstName = "John",
            MiddleName = "M.",
            LastName = "Doe",
            Employer = "test"
        };

        // Act & Assert
        var ex = Assert.ThrowsAsync<NotSupportedException>(async () =>
            await _controller.UpdateFilerContact(id, request, _updateFilerContactCommandMock));

        Assert.That(ex!.Message, Does.Contain("Unsupported payee type"));
    }

    [Test]
    public async Task CreateFilerContact_EndOfSession_FormatsPhoneNumber()
    {
        // Arrange
        var testId = 12345L;
        var expectedContactId = 1;
        var expectedFormattedPhone = "15551234567";

        var testOrganizationName = "Lumon";
        var testCountry = "USA";
        var testStreet = "123 Main St";
        var testCity = "Sacramento";
        var testState = "CA";
        var testZipCode = (ZipCode)"95814";

        var phoneNumber = new PhoneNumberDto
        {
            CountryCode = "1-US",
            Number = "5551234567",
            Type = "Phone"
        };

        var body = new UpsertOrganizationFilerContactRequest
        {
            FilerContactForm = "EndOfSession",
            OrganizationName = testOrganizationName,
            Addresses = new List<AddressDto>
        {
            new()
            {
                Country = testCountry,
                Street = testStreet,
                City = testCity,
                State = testState,
                Zip = testZipCode
            }
        },
            EmailAddresses = new List<EmailAddressDto>
        {
            new()
            {
                Email = "<EMAIL>",
                Status = "test",
                VerificationCode = "test"
            }
        },
            PhoneNumbers = new List<PhoneNumberDto> { phoneNumber }
        };

        var createdContact = new OrganizationContact
        {
            Id = expectedContactId,
            OrganizationName = testOrganizationName,
        };

        var successfulResult = new Success<FilerContact>(createdContact);

        _createFilerContactCommandMock
            .Execute(Arg.Any<CreateFilerContactCommand>(), Arg.Any<CancellationToken>())
            .Returns(successfulResult);

        EndOfSessionLobbyingContactDs capturedDs = null!;
        _decisionsSvcMock
            .InitiateWorkflow<EndOfSessionLobbyingContactDs, List<WorkFlowError>>(
                DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransaction,
                Arg.Do<EndOfSessionLobbyingContactDs>(ds => capturedDs = ds),
                true)
            .Returns(Task.FromResult(new List<WorkFlowError>()));

        // Act
        var result = await _controller.CreateFilerContact(testId, body, _createFilerContactCommandMock, CancellationToken.None);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected result to be of type ActionResult<ContactItemResponse>");
        Assert.That(result.Result, Is.InstanceOf<CreatedAtActionResult>(), "Expected CreatedAtActionResult");

        var createdResult = result.Result as CreatedAtActionResult;
        Assert.That(createdResult, Is.Not.Null);

        Assert.Multiple(() =>
        {
            var contactResponse = createdResult.Value as ContactItemResponse;
            Assert.That(contactResponse!.Id, Is.EqualTo(expectedContactId), "Returned contact ID does not match");

            // Additional assertion for phone number formatting
            Assert.That(capturedDs.PhoneNumber, Is.EqualTo(expectedFormattedPhone), "Phone number was not formatted as expected");
        });
    }

    [Test]
    public async Task UpdateFilerContact_PaymentReceiveLobbyingCoalitionRequest_WithValidationErrors_ReturnsUnprocessableEntity()
    {
        // Arrange
        var id = 1L;
        var request = new UpsertOrganizationFilerContactRequest
        {
            FilerContactForm = "PaymentReceiveLobbyingCoalition",
            OrganizationName = "Test Org",
            Addresses = new List<AddressDto>
            {
                new()
                {
                    Country = "US", Street = "123 Main St", City = "City", State = "CA", Zip = "90000"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto>
            {
                new() { Number = "1234567890", CountryCode = "+1-USA" }
            }
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("Validation error", "Err001", "Validation", "Field is required")
        };

        _decisionsSvcMock.InitiateWorkflow<PaymentReceiveLobbyingCoalitionMemberDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentReceiveLobbyingCoalitionMemberRuleset,
            Arg.Any<PaymentReceiveLobbyingCoalitionMemberDs>(),
            true
        ).Returns(validationErrors);

        // Act
        var result = await _controller.UpdateFilerContact(id, request, _updateFilerContactCommandMock);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<UnprocessableEntityObjectResult>());
        var objectResult = result.Result as UnprocessableEntityObjectResult;
        Assert.That(objectResult!.Value, Is.EqualTo(validationErrors));

        await _updateFilerContactCommandMock.DidNotReceive().Execute(Arg.Any<UpdateFilerContactCommand>(), Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task UpdateFilerContact_PaymentReceiveLobbyingCoalitionRequest_WithValidationErrorsIndividual_ReturnsUnprocessableEntity()
    {
        // Arrange
        var id = 1L;
        var request = new UpsertIndividualFilerContactRequest()
        {
            FilerContactForm = "PaymentReceiveLobbyingCoalition",
            FirstName = "John",
            LastName = "Doe",
            MiddleName = "M",
            Employer = "TestCo",
            Addresses = new List<AddressDto> {
                new() {
                    Street = "123 Main St",
                    Street2 = "Apt 4",
                    City = "Sacramento",
                    State = "CA",
                    Zip = "95814",
                    Country = "USA",
                    Purpose = "Mailing",
                    Type = "Business"
                }
            },
            EmailAddresses = new List<EmailAddressDto> {
                new() {
                    Email = "<EMAIL>",
                    Status = "Active",
                    VerificationCode = "ABC123"
                }
            },
            PhoneNumbers = new List<PhoneNumberDto> {
                new() {
                    Number = "555-1234",
                    CountryCode = "1",
                    Type = "Mobile",
                    InternationalNumber = false
                }
            }
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("Validation error", "Err001", "Validation", "Field is required")
        };

        _decisionsSvcMock.InitiateWorkflow<PaymentReceiveLobbyingCoalitionMemberDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentReceiveLobbyingCoalitionMemberRuleset,
            Arg.Any<PaymentReceiveLobbyingCoalitionMemberDs>(),
            true
        ).Returns(validationErrors);

        // Act
        var result = await _controller.UpdateFilerContact(id, request, _updateFilerContactCommandMock);

        // Assert
        Assert.That(result.Result, Is.InstanceOf<UnprocessableEntityObjectResult>());
        var objectResult = result.Result as UnprocessableEntityObjectResult;
        Assert.That(objectResult!.Value, Is.EqualTo(validationErrors));

        await _updateFilerContactCommandMock.DidNotReceive().Execute(Arg.Any<UpdateFilerContactCommand>(), Arg.Any<CancellationToken>());
    }

    [Test]
    [TestCase("1-USA", "5551234567", ExpectedResult = "15551234567")]
    [TestCase("44-UK", "2071234567", ExpectedResult = "442071234567")]
    [TestCase(null, "5551234567", ExpectedResult = "5551234567")]
    [TestCase("1-USA", null, ExpectedResult = "1")]
    [TestCase(null, null, ExpectedResult = "")]
    public string FormatPhoneNumberForValidation_ReturnsExpectedFormat(string? countryCode, string? number)
    {
        // Arrange
        var phoneNumber = new PhoneNumberDto
        {
            CountryCode = countryCode,
            Number = number
        };

        var method = typeof(ContactsController) // Replace with the class that defines FormatPhoneNumberForValidation
            .GetMethod("FormatPhoneNumberForValidation", BindingFlags.NonPublic | BindingFlags.Static);

        // Act
        var result = method?.Invoke(null, new object[] { phoneNumber }) as string;

        // Assert
        Assert.That(result, Is.Not.Null); // Protects against method returning null instead of string.Empty
        return result!;
    }

    #region Private
    private static AddressList GenerateAddressList()
    {
        return new AddressList
        {
            Addresses = new List<Address>
            {
                new() { Id = 1, Street = "123 Business Rd", City = "Sacramento", State = "CA", Zip = "95814", Country = "USA", Type = "Business", Purpose = "Primary" }
            }
        };
    }

    private static PhoneNumberList GeneratePhoneNumberList()
    {
        return new PhoneNumberList
        {
            PhoneNumbers = new List<PhoneNumber>
            {
                new() { Id = 2, Number = "555-9876", Type = "Business" }
            }
        };
    }

    private static EmailAddressList GenerateEmailAddressList()
    {
        return new EmailAddressList
        {
            EmailAddresses = new List<EmailAddress>
            {
                new() { Id = 3, Email = "<EMAIL>" }
            }
        };
    }

    #endregion
}
