using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;
[TestFixture]
public class LobbyistResponseDtoTest
{
    [Test]
    public void Constructor_ShouldInitializeProperties()
    {
        // Arrange
        var lobbyist = new Lobbyist
        {
            Id = 1,
            Name = "Test Lobbyist",
            Email = "<EMAIL>",
            StatusId = 2,
            FilerId = 3,
            EmployerName = "Test Employer",
            StateLegislatureLobbying = true,
            DateQualified = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            AddressListId = 4,
            PhoneNumberListId = 5,
            AddressList = new AddressList
            {
                Addresses =
                [
                    new Address
                    {
                        Street = "123 Test St",
                        City = "Test City",
                        State = "CA",
                        Zip = "12345",
                        Country = "USA",
                        Type = "Home",
                        Purpose = "Mailing"
                    }
                ]
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers =
                [
                    new PhoneNumber
                    {
                        Extension = "123",
                        Number = "4567890",
                        Type = "Mobile",
                        CountryCode = "1",
                        InternationalNumber = false
                    }
                ]
            }
        };

        // Act
        var dto = new LobbyistResponseDto(lobbyist);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(dto.Id, Is.EqualTo(lobbyist.Id));
            Assert.That(dto.Name, Is.EqualTo(lobbyist.Name));
            Assert.That(dto.Email, Is.EqualTo(lobbyist.Email));
            Assert.That(dto.StatusId, Is.EqualTo(lobbyist.StatusId));
            Assert.That(dto.FilerId, Is.EqualTo(lobbyist.FilerId));
            Assert.That(dto.EmployerName, Is.EqualTo(lobbyist.EmployerName));
            Assert.That(dto.StateLegislatureLobbying, Is.EqualTo(lobbyist.StateLegislatureLobbying));
            Assert.That(dto.DateQualified, Is.EqualTo(lobbyist.DateQualified));
            Assert.That(dto.AddressListId, Is.EqualTo(lobbyist.AddressListId));
            Assert.That(dto.PhoneNumberListId, Is.EqualTo(lobbyist.PhoneNumberListId));
            Assert.That(dto.Addresses, Has.Count.EqualTo(1));
            Assert.That(dto.PhoneNumbers, Has.Count.EqualTo(1));
        });
    }

    [Test]
    public void Constructor_ShouldHandleNullValues()
    {
        // Arrange
        var lobbyist = new Lobbyist
        {
            Id = 1,
            Name = "Test Lobbyist",
            Email = null,
            StatusId = 2,
            FilerId = 3,
            EmployerName = "Test Employer",
            StateLegislatureLobbying = true,
            DateQualified = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            AddressListId = null,
            PhoneNumberListId = null,
            AddressList = null,
            PhoneNumberList = null
        };

        // Act
        var dto = new LobbyistResponseDto(lobbyist);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(dto.Id, Is.EqualTo(lobbyist.Id));
            Assert.That(dto.Name, Is.EqualTo(lobbyist.Name));
            Assert.That(dto.Email, Is.EqualTo(string.Empty));
            Assert.That(dto.StatusId, Is.EqualTo(lobbyist.StatusId));
            Assert.That(dto.FilerId, Is.EqualTo(lobbyist.FilerId));
            Assert.That(dto.EmployerName, Is.EqualTo(lobbyist.EmployerName));
            Assert.That(dto.StateLegislatureLobbying, Is.EqualTo(lobbyist.StateLegislatureLobbying));
            Assert.That(dto.DateQualified, Is.EqualTo(lobbyist.DateQualified));
            Assert.That(dto.AddressListId, Is.EqualTo(0));
            Assert.That(dto.PhoneNumberListId, Is.EqualTo(0));
            Assert.That(dto.Addresses, Is.Empty);
            Assert.That(dto.PhoneNumbers, Is.Empty);
        });
    }
}
