using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;

public class DecisionsSubmitLobbyistEmployerReport
{
    public required List<DecisionsTotalPaymentsPucActivity> PUCActivity { get; set; }
    public required List<DecisionsLobbyistEmployerReportActionsLobbiedAgencies> Agencies { get; set; }
    public required List<DecisionsLobbyistEmployerReportActionsLobbied> ActionsLobbied { get; set; }
    public required List<DecisionsMajorDonorSelectionLobbyistEmployerReport> MajorDonorSelection { get; set; }
    public required List<DecisionsSubmitLobbyingCampaignContribution> LobbyingCampaignContributon { get; set; }
    public required List<DecisionsLumpSumPayments> LumpSums { get; set; }
    public required List<DecisionsPaymentsToInHouseLobbyists> PaymentsToInHouseLobbyists { get; set; }
    public required List<PaymentMadeToLobbyingFirmContactDs> AddFirmContact { get; set; }
#pragma warning disable CA1707
    public required List<PaymentMadeToLobbyingFirmsDs> PaymentMadeToLobbyingFirm_SaveTransaction { get; set; }
#pragma warning restore CA1707
    public required List<DecisionsSubmitActivityExpenseTransaction> ActivityExpenseTransaction { get; set; }
    public required List<DecisionsSubmitActivityExpensePayee> ActivityExpensePayee { get; set; }
    public required List<DecisionsSubmitActivityExpenseReportablePerson> ActivityExpenseReportablePerson { get; set; }
    public required List<DecisionServicePayee> AddPayee { get; set; }
#pragma warning disable CA1707
    public required List<OtherPaymentsToInfluenceDs> OtherPayments_SaveTransaction { get; set; }
#pragma warning restore CA1707
    public required List<PaymentReceiveLobbyingCoalitionPaymentAmountDs> CoalitionPaymentAmount { get; set; }
    public required List<PaymentReceiveLobbyingCoalitionMemberDs> CoalitionMemberInformation { get; set; }
    public required List<PaymentReceiveLobbyingCoalitionDs> PaymentReceivedByLobbyingCoalitions { get; set; }
    public required List<PaymentMadeToLobbyingCoalitionDs> PaymentToLobbyingCoalition { get; set; }
    public string? AmendmentExplanation { get; set; }
}
