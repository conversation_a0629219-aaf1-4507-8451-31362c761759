@model GenericContactViewModel
@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.UI.Common.Localization
@using SOS.CalAccess.UI.Common.Services;
@using SOS.CalAccess.Models.FilerDisclosure.Contacts;
@inject IHtmlLocalizer<SharedResources> Localizer
@using Microsoft.AspNetCore.Html

@{
    ViewData["Title"] = Model.Action;
    var returnUrl = Model.ReturnUrl ?? Url.Action("Index", "Filer", new { id = Model.FilerId });
    var individualContactId = FilerContactType.Individual.Id;
    var organizationContactId = FilerContactType.Organization.Id;
    var buttonBarModel = new ButtonBarModel
    {
        RightButtons =
        [
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                CssClass = "btn btn-flat-primary btn-sm ms-auto",
                HtmlContent = new HtmlString(
                   "<a asp-controller='Disclosure' asp-action='Index' asp-route-viewName='' class='btn btn-flat-primary btn-sm ms-auto' " +
                   "data-bs-toggle='modal' " +
                   "data-bs-target='#cancelConfirmModal'>" +
                   @SharedLocalizer[CommonResourceConstants.Cancel].Value +
                   "</a>"
                )
            },
        ],
        LeftButtons =
        [
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                HtmlContent = new HtmlString(
                    "<button type='button' class='btn btn-primary btn-sm ms-2 text-white' onclick='history.go(-1);'>" +
                    Localizer["Common.Previous"].Value +
                    "</button>"
                )
            },
            new ButtonConfig
            {
                Type = ButtonType.Button,
                CssClass = "btn btn-warning btn-sm text-white",
                Action = SOS.CalAccess.UI.Common.Enums.FormAction.Continue,
                InnerTextKey=Localizer["Common.Continue"].Value
            }
        ]
    };
}

@{
    var cancelModal = new CancelConfirmModal(
        Title: "Confirm Cancellation",
        Body: "Are you sure you want to cancel? Any unsaved changes will be lost.",
        CloseButtonText: "No, Stay Here",
        SubmitButtonText: "Yes, Cancel",
        ActionUrl: returnUrl!
    );
}
<partial name="_CancelConfirmModal" model="cancelModal" />

<div class="p-5 mt-4 d-flex flex-column border border-gray">
    <div class="d-flex flex-column">
        <h1 class="card-title mb-4">@Localizer["FilerPortal.Contact.ContactForm.Title"]</h1>
        <p class="card-subtitle mb-3">@Localizer["FilerPortal.Contact.ContactForm.Body"]</p>
    </div>

    @using (Html.BeginForm(ViewBag.IsEditing ? "Edit" : "Create", "Contact", FormMethod.Post))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.Id)
        @Html.HiddenFor(m => m.FilerId)
        @Html.HiddenFor(m => m.FilingId)
        @Html.HiddenFor(m => m.AddressId)
        @Html.HiddenFor(m => m.ReturnUrl)
        @Html.HiddenFor(m => m.ReportType)
        @Html.HiddenFor(m => m.Context)
        @Html.HiddenFor(m => m.TransactionId)

        <div class="mb-3">
            <div class="d-flex">
                <p class="form-label">@Localizer["FilerPortal.Contact.ContactForm.PayeeType"]</p>
                <i class="bi bi-info-circle ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Cumque quis tempora aliquid iusto nostrum ad."></i>
            </div>
            <div>
                @Html.RadioButtonFor(m => m.TypeId, individualContactId, new { @id = "individual" }) @Localizer["FilerPortal.Contact.ContactForm.Individual"]
            </div>
            <div>
                @Html.RadioButtonFor(m => m.TypeId, organizationContactId, new { @id = "organization" }) @Localizer["FilerPortal.Contact.ContactForm.Organization"]
            </div>
        </div>

        <div class="mb-3 col-lg-4" id="firstNameContainer" style="display: none;">
            @Html.LabelFor(m => m.FirstName, Localizer["Common.FirstName"].Value, new { @class = "form-label" })
            @Html.ValidationMessageFor(m => m.FirstName, null, new { @class = "text-danger" })
            @Html.TextBoxFor(m => m.FirstName, new { @class = "form-control" })
            @if (Model.Messages.Validations != null && Model.Messages.Validations.Any() && Model.Messages.Validations.ContainsKey("FirstName"))
            {
                @UtilityService.DecisionsErrorDisplay(Model.Messages.Validations["FirstName"].Message, Localizer["Common.FirstName"].Value)
            }
        </div>

        <div class="mb-3 col-lg-4" id="middleNameContainer" style="display: none;">
            @Html.LabelFor(m => m.MiddleName, Localizer["Common.MiddleName"].Value, new { @class = "form-label" })
            @Html.ValidationMessageFor(m => m.MiddleName, null, new { @class = "text-danger" })
            @Html.TextBoxFor(m => m.MiddleName, new { @class = "form-control" })
        </div>

        <div class="mb-3 col-lg-4" id="lastNameContainer" style="display: none;">
            @Html.LabelFor(m => m.LastName, Localizer["Common.LastName"].Value, new { @class = "form-label" })
            @Html.ValidationMessageFor(m => m.LastName, null, new { @class = "text-danger" })
            @Html.TextBoxFor(m => m.LastName, new { @class = "form-control" })
            @if (Model.Messages.Validations != null && Model.Messages.Validations.Any() && Model.Messages.Validations.ContainsKey("LastName"))
            {
                @UtilityService.DecisionsErrorDisplay(Model.Messages.Validations["LastName"].Message, Localizer["Common.LastName"].Value)
            }
        </div>

        <div class="mb-3 col-lg-4" id="organizationNameContainer" style="display: none;">
            @Html.LabelFor(m => m.OrganizationName, Localizer["FilerPortal.Contact.ContactForm.OrganizationName"].Value, new { @class = "form-label" })
            @Html.ValidationMessageFor(m => m.OrganizationName, null, new { @class = "text-danger" })
            @Html.TextBoxFor(m => m.OrganizationName, new { @class = "form-control" })
            @if (Model.Messages.Validations != null && Model.Messages.Validations.Any() && Model.Messages.Validations.ContainsKey("OrganizationName"))
            {
                @UtilityService.DecisionsErrorDisplay(Model.Messages.Validations["OrganizationName"].Message, Localizer["FilerPortal.Contact.ContactForm.OrganizationName"].Value)
            }
        </div>

        <partial name="_CommonFields" model="Model" />

        <hr />

        <partial name="_ButtonBar" model="buttonBarModel" />

    }
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const individual = document.getElementById("individual");
        const organization = document.getElementById("organization");
        const firstName = document.getElementById("firstNameContainer");
        const middleName = document.getElementById("middleNameContainer");
        const lastName = document.getElementById("lastNameContainer");
        const organizationName = document.getElementById("organizationNameContainer");

        function toggleFields() {

            if (individual.checked) {
                firstName.style.display = "block";
                middleName.style.display = "block";
                lastName.style.display = "block";
                organizationName.style.display = "none";
            } else if (organization.checked) {
                firstName.style.display = "none";
                middleName.style.display = "none";
                lastName.style.display = "none";
                organizationName.style.display = "block";
            }
        }
        toggleFields();

        individual.addEventListener("change", toggleFields);
        organization.addEventListener("change", toggleFields);

        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTrigger) {
            return new bootstrap.Tooltip(tooltipTrigger);
        });
    });
</script>
