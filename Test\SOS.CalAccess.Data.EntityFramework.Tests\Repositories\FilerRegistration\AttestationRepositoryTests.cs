using SOS.CalAccess.Data.EntityFramework.Repositories;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration;

[TestFixture]
[TestOf(typeof(AttestationRepository))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class AttestationRepositoryTests
{
    private AttestationRepository _repository;

    [SetUp]
    public async Task Setup()
    {
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new AttestationRepository(context);
    }

    [Test]
    public async Task GetFilerUserByFilerId_Found_ShouldReturnResult()
    {
        // Arrange
        // Get seed data
        var data = GetData();

        // Add seed data into the database
        await _repository.Create(data);

        // Act
        var result = await _repository.FindByRegistrationId(1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<Attestation>());
            Assert.That(result?.Id, Is.EqualTo(data.Id));
        });
    }

    [Test]
    public async Task FindByRegistrationId_NotFound_ShouldReturnNull()
    {
        // Arrange

        // Act
        var result = await _repository.FindByRegistrationId(1);

        // Assert
        Assert.That(result, Is.Null);
    }

    #region Private
    private static Attestation GetData()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        return new Attestation
        {
            Id = 1,
            RegistrationId = 1,
            ExecutedAt = date,

        };
    }
    #endregion
}
