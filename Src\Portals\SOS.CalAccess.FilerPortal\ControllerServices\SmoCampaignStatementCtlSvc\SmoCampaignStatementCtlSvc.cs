using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.Constants;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.Mapper;
using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Mapper;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Constants;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using Syncfusion.EJ2.Grids;
using CandidateOrMeasureNotListedPaymentReceivedResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.CandidateOrMeasureNotListedPaymentReceivedResponseDto;
using FilerContactType = SOS.CalAccess.Models.FilerDisclosure.Contacts.FilerContactType;
using FilingPeriodResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.FilingPeriodResponseDto;
using FilingSummaryResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.FilingSummaryResponseDto;
using FilingSummaryStatus = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryStatus;
using FilingSummaryType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using PaymentMadeByAgentOrIndependentContractorResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.PaymentMadeByAgentOrIndependentContractorResponseDto;
using PaymentMadeResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.PaymentMadeResponseDto;
using PaymentReceivedResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.PaymentReceivedResponseDto;
using PersonReceiving1000OrMoreResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.PersonReceiving1000OrMoreResponseDto;
using ResourceConstants = SOS.CalAccess.FilerPortal.Models.Localization.ResourceConstants;
using SmoCampaignStatementAttestationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoCampaignStatementAttestationResponseDto;
using SmoCampaignStatementFilingSummaryRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.SmoCampaignStatementFilingSummaryRequest;
using SmoCampaignStatementRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.SmoCampaignStatementRequest;
using SmoRegistrationSendForAttestationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationSendForAttestationRequest;
using TransactionDetailRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.TransactionDetailRequest;
using WorkFlowError = SOS.CalAccess.Models.Common.WorkFlowError;

namespace SOS.CalAccess.FilerPortal.ControllerServices.SmoCampaignStatementCtlSvc;

[SuppressMessage("SonarQube", "S107:Methods should not have too many parameters", Justification = "Will refactor later")]
public class SmoCampaignStatementCtlSvc(
    IStringLocalizer<SharedResources> localizer,
    ISmoCampaignStatementSvc smoCampaignStatementSvc,
    ITransactionSvc transactionSvc,
    IReferenceDataSvc referenceDataSvc,
    ISmoRegistrationSvc smoRegistrationSvc,
    IDecisionsValidationMapService decisionsValidationMapService,
    IDateTimeSvc dateTimeSvc,
    Generated.IUsersApi usersApi,
    Generated.IContactsApi contactsApi,
    Generated.IFilingsApi filingsApi
    ) : ISmoCampaignStatementCtlSvc
{
    #region Selection Page
    public async Task<long> CreateSmoCampaignStatement(long? registrationId)
    {
        // Create a SmoCampaignStatement record under DisclosureFiling
        SmoCampaignStatementRequest request = new()
        {
            RegistrationId = registrationId,
        };
        long filingId = await smoCampaignStatementSvc.CreateSmoCampaignStatementAsync(request);

        return filingId;
    }

    #endregion

    #region Review Page
    /// <inheritdoc />
    public async Task<List<SelectListItem>> GetUnreportedFilingPeriods(long filingId)
    {
        List<FilingPeriodResponseDto> filingPeriodResponse = await smoCampaignStatementSvc.GetUnreportedFilingPeriodsByFilerAsync(filingId);

        var formattedFilingPeriods = filingPeriodResponse.Select(filingPeriod => new SelectListItem
        {
            Value = filingPeriod.Id.ToString(CultureInfo.InvariantCulture),
            Text = $"{filingPeriod.Name} ({filingPeriod.StartDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)} - {filingPeriod.EndDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)})"
        }).ToList();

        return formattedFilingPeriods;
    }

    /// <inheritdoc />
    public async Task<SmoCampaignStatementReviewViewModel?> GetCompleteReviewPageViewModel(long filingId)
    {
        // Retrieve list of unreported filing periods
        var unreportedFilingPeriods = await GetUnreportedFilingPeriods(filingId);

        var generalInformation = new SummaryItem
        {
            Title = localizer[ResourceConstants.SmoCampaignStatementReviewGeneralInformation].Value,
            ShowStatus = false,
            Url = "GeneralInformation",
            UrlQuery = new { filingId },
            Action = localizer[ResourceConstants.View].Value
        };

        var filingSummary = new SummaryItem
        {
            Title = localizer[ResourceConstants.SmoCampaignStatementReviewFilingSummary].Value,
            ShowStatus = false,
            Url = "FilingSummary",
            UrlQuery = new { filingId },
            Action = localizer[ResourceConstants.View].Value
        };

        // Retrieve overview information for the filing summary sections and their statuses
        // - Payments received
        // - Payments made
        // - Payments made by an agent or independent contractor
        // - Payments receiving $1000 or more
        // - Candidates and measures not listed on Payment received
        var disclosureOverviewResponse = await smoCampaignStatementSvc.GetSmoCampaignStatementOverviewAsync(filingId);
        var transactionSummaryUrl = "TransactionSummary";
        var filerId = disclosureOverviewResponse.FilerId;

        // add the current filing period to the dropdown
        if (disclosureOverviewResponse.FilingPeriod != null)
        {
            var currentlySelectedFilingPeriod = new SelectListItem
            {
                Value = disclosureOverviewResponse.FilingPeriod.Id.ToString(CultureInfo.InvariantCulture),
                Text = $"{disclosureOverviewResponse.FilingPeriod.Name} ({disclosureOverviewResponse.FilingPeriod.StartDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)} - {disclosureOverviewResponse.FilingPeriod.EndDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)})"
            };

            unreportedFilingPeriods.Add(currentlySelectedFilingPeriod);
        }

        var amendmentExplanation = disclosureOverviewResponse.FilingSummaries
            .Where(summary => summary.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id)
            .Select(summary => new SummaryItem
            {
                FilingId = filingId,
                SummaryId = summary.Id,
                Title = localizer[ResourceConstants.SmoCampaignStatementAmendmentExplanationTitle].Value,
                ShowStatus = true,
                Status = summary.FilingSummaryStatusName,
                Url = "AmendmentExplanation",
                UrlQuery = new { filingId },
                Action = SetFilingSummaryActionText(summary.FilingSummaryStatusId)
            })
            .FirstOrDefault();

        var paymentsReceived = disclosureOverviewResponse.FilingSummaries
            .Where(summary => summary.FilingSummaryTypeId == FilingSummaryType.PaymentReceivedSummary.Id)
            .Select(summary => new SummaryItem
            {
                FilingId = filingId,
                SummaryId = summary.Id,
                Title = localizer[ResourceConstants.SmoCampaignStatementReviewPaymentsReceived].Value,
                ShowStatus = true,
                Status = summary.FilingSummaryStatusName,
                Url = transactionSummaryUrl,
                UrlQuery = new { filingId, filerId, summaryType = FilingSummaryType.PaymentReceivedSummary.Name, filingSummaryId = summary.Id },
                Action = SetFilingSummaryActionText(summary.FilingSummaryStatusId)
            })
            .FirstOrDefault();

        var paymentsMade = disclosureOverviewResponse.FilingSummaries
            .Where(summary => summary.FilingSummaryTypeId == FilingSummaryType.PaymentMadeSummary.Id)
            .Select(summary => new SummaryItem
            {
                FilingId = filingId,
                SummaryId = summary.Id,
                Title = localizer[ResourceConstants.SmoCampaignStatementReviewPaymentsMade].Value,
                ShowStatus = true,
                Status = summary.FilingSummaryStatusName,
                Url = transactionSummaryUrl,
                UrlQuery = new { filingId, filerId, summaryType = FilingSummaryType.PaymentMadeSummary.Name, filingSummaryId = summary.Id },
                Action = SetFilingSummaryActionText(summary.FilingSummaryStatusId)
            })
            .FirstOrDefault();

        var paymentsMadeByAgentOrContractor = disclosureOverviewResponse.FilingSummaries
            .Where(summary => summary.FilingSummaryTypeId == FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id)
            .Select(summary => new SummaryItem
            {
                FilingId = filingId,
                SummaryId = summary.Id,
                Title = localizer[ResourceConstants.SmoCampaignStatementReviewPaymentsMadeByAgentOrContractor].Value,
                ShowStatus = true,
                Status = summary.FilingSummaryStatusName,
                Url = transactionSummaryUrl,
                UrlQuery = new { filingId, filerId, summaryType = FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Name, filingSummaryId = summary.Id },
                Action = SetFilingSummaryActionText(summary.FilingSummaryStatusId)
            })
            .FirstOrDefault();

        var personsReceiving1000OrMore = disclosureOverviewResponse.FilingSummaries
            .Where(summary => summary.FilingSummaryTypeId == FilingSummaryType.PersonReceiving1000OrMoreSummary.Id)
            .Select(summary => new SummaryItem
            {
                FilingId = filingId,
                SummaryId = summary.Id,
                Title = localizer[ResourceConstants.SmoCampaignStatementReviewPersonsReceiving1000OrMore].Value,
                ShowStatus = true,
                Status = summary.FilingSummaryStatusName,
                Url = transactionSummaryUrl,
                UrlQuery = new { filingId, filerId, summaryType = FilingSummaryType.PersonReceiving1000OrMoreSummary.Name, filingSummaryId = summary.Id },
                Action = SetFilingSummaryActionText(summary.FilingSummaryStatusId)
            })
            .FirstOrDefault();

        var candidatesMeasuresNotListed = disclosureOverviewResponse.FilingSummaries
            .Where(summary => summary.FilingSummaryTypeId == FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Id)
            .Select(summary => new SummaryItem
            {
                FilingId = filingId,
                SummaryId = summary.Id,
                Title = localizer[ResourceConstants.SmoCampaignStatementReviewPaymentsCandidatesMeasuresNotListed].Value,
                ShowStatus = true,
                Status = summary.FilingSummaryStatusName,
                Url = transactionSummaryUrl,
                UrlQuery = new { filingId, summaryType = FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name },
                Action = SetFilingSummaryActionText(summary.FilingSummaryStatusId)
            })
            .FirstOrDefault();

        bool isReadyToAttestOrSendForAttestation = true;

        foreach (var filingSummaryEntry in disclosureOverviewResponse.FilingSummaries)
        {
            // if there is a filing summary that has not been started, mark the "send" / "send for attestation" button as read-only
            if (filingSummaryEntry.FilingSummaryStatusId == FilingSummaryStatus.NotStarted.Id)
            {
                isReadyToAttestOrSendForAttestation = false;
                break;
            }
        }

        bool isAmending = await IsSmoCampaignStatementAmendment(filingId);

        return new SmoCampaignStatementReviewViewModel()
        {
            Id = filingId,
            FilerId = disclosureOverviewResponse.FilerId,
            FilingPeriodOptions = unreportedFilingPeriods,
            SelectedFilingPeriodId = disclosureOverviewResponse.FilingPeriodId,
            FilingSummary = filingSummary,
            GeneralInformation = generalInformation,
            AmendmentExplanation = amendmentExplanation,
            PaymentsReceived = paymentsReceived,
            PaymentsMade = paymentsMade,
            PaymentsMadeByAgentOrContractor = paymentsMadeByAgentOrContractor,
            PaymentsReceiving1000OrMore = personsReceiving1000OrMore,
            CandidatesMeasuresNotListed = candidatesMeasuresNotListed,
            IsReadyToAttestOrSendForAttestation = isReadyToAttestOrSendForAttestation,
            IsAmending = isAmending
        };
    }

    public async Task UpdateSmoCampaignStatement(long? filingId, long? filingPeriodId)
    {
        SmoCampaignStatementRequest request = new()
        {
            FilingPeriodId = filingPeriodId,
        };
        await smoCampaignStatementSvc.UpdateSmoCampaignStatementAsync(filingId!.Value, request);
    }

    public async Task MarkFilingSummaryAsNothingToReport(long? filingId, long? filingSummaryId)
    {
        await smoCampaignStatementSvc.MarkFilingSummaryAsNothingToReportAsync(filingId!.Value, filingSummaryId!.Value);
    }

    public async Task CancelSmoCampaignStatementDraft(long? filingId)
    {
        await filingsApi.CancelFiling(filingId!.Value);
    }
    #endregion

    #region General Information
    public async Task<SmoCampaignStatementGeneralInfoViewModel?> GetGeneralInformationPageViewModel(long filingId)
    {
        try
        {
            var generalInformation = await smoCampaignStatementSvc.GetSmoGeneralInformationById(filingId);

            if (generalInformation == null)
            {
                return new SmoCampaignStatementGeneralInfoViewModel();
            }

            return new SmoCampaignStatementGeneralInfoViewModel(filingId, generalInformation);
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error de-serializing the response: " + ex.Message);
            return new SmoCampaignStatementGeneralInfoViewModel();
        }
    }
    #endregion

    #region Filing Summary
    public async Task<SmoCampaignStatementFilingSummaryViewModel?> GetFilingSummaryPageViewModel(long filingId)
    {
        try
        {
            var filingSummary = await smoCampaignStatementSvc.GetSmoFilingSummaryByFilingId(filingId);

            if (filingSummary.Count == 0)
            {
                return new SmoCampaignStatementFilingSummaryViewModel();
            }

            return new SmoCampaignStatementFilingSummaryViewModel(filingId, filingSummary);
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error de-serializing the response: " + ex.Message);
            return new SmoCampaignStatementFilingSummaryViewModel();
        }
    }
    #endregion

    #region  Transaction Summary - Payments Received
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionSummaryViewModel> GetPaymentsReceived(long filingId)
    {
        var response = await smoCampaignStatementSvc.GetTransactionSummaryAsync(filingId, FilingSummaryType.PaymentReceivedSummary.Id);
        string deleteMessage = $"{localizer[ResourceConstants.SmoCampaignStatementTransactionSummaryDeletePaymentConfirmation].Value}";
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = filingId,
            FilingSummaryId = response.SummaryResponseDto.Id,
            FilerId = response.FilerId,
            SummaryType = FilingSummaryType.PaymentReceivedSummary.Name,
            PaymentsReceivedGridModel = new SmallDataGridModel
            {
                PrimaryKey = nameof(PaymentReceivedResponseDto.Id),
                GridId = "PaymentsReceivedGrid",
                GridType = nameof(PaymentReceivedResponseDto),
                AllowPaging = true,
                AllowTextWrap = false,
                AllowAdding = false,
                AllowDeleting = true,
                DeleteConfirmationField = nameof(PaymentReceivedResponseDto.Id),
                DeleteConfirmationMessage = deleteMessage,
                EnableExport = false,
                DataSource = response.TransactionResponseDtos,
                Columns = new List<DataGridColumn>
                {
                    new() { Field = nameof(PaymentReceivedResponseDto.TransactionDate), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridDateHeader, IsDateTime = true },
                    new() { Field = nameof(PaymentReceivedResponseDto.Name), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridNameHeader },
                    new() { Field = nameof(PaymentReceivedResponseDto.CandidateOrMeasure), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridCandidateOrMeasureHeader },
                    new() { Field = nameof(PaymentReceivedResponseDto.Position), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridSupportOpposeHeader },
                    new() { Field = nameof(PaymentReceivedResponseDto.Amount), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridAmountHeader, IsCurrency = true, TextAlign = TextAlign.Right },
                    new() { Field = nameof(PaymentReceivedResponseDto.CumulativeAmount), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridCumulativeAmountHeader, IsCurrency = true, TextAlign = TextAlign.Right },
                },
                ActionItems = new List<GridActionItem>
                {
                    new() { Label = CommonResourceConstants.Edit, Action = CommonConstants.Grid.EditAction, ControllerName= $"SmoCampaignStatement/{filingId}/{TransactionType.PaymentReceived.Name}", ActionName = EditTransactionEntryActionName },
                    new() { Label = CommonResourceConstants.Delete, Action = CommonConstants.Grid.DeleteAction, ControllerName = $"SmoCampaignStatement/{filingId}", ActionName = DeleteTransactionEntryActionName }
                }
            },
            TransactionSubtotal = Math.Max(response.SummaryResponseDto.PeriodAmount - response.SummaryResponseDto.UnitemizedAmount ?? 0m, 0),
            AddNewTranscationUrl = "PaymentReceived01",
            AddNewTranscationUrlQuery = new { filingId, filerId = response.FilerId },
            // UnitemizedPaymentSubtotal is equal to UnitemizedPaymentLessThan100 since it is currently the only value that contributes to the subtotal.
            // This may change in the future.
            UnitemizedPaymentLessThan100 = response.SummaryResponseDto.UnitemizedAmount,
            UnitemizedPaymentSubtotal = response.SummaryResponseDto.UnitemizedAmount,
        };

        return model;
    }
    #endregion

    #region  Transaction Summary - Payments Made
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionSummaryViewModel> GetPaymentsMade(long filingId)
    {
        var response = await smoCampaignStatementSvc.GetTransactionSummaryAsync(filingId, FilingSummaryType.PaymentMadeSummary.Id);
        string deleteMessage = $"{localizer[ResourceConstants.SmoCampaignStatementTransactionSummaryDeletePaymentConfirmation].Value}";
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = filingId,
            FilingSummaryId = response.SummaryResponseDto.Id,
            FilerId = response.FilerId,
            SummaryType = FilingSummaryType.PaymentMadeSummary.Name,
            PaymentsMadeGridModel = new SmallDataGridModel
            {
                PrimaryKey = nameof(PaymentMadeResponseDto.Id),
                GridId = "PaymentsMadeGrid",
                GridType = nameof(PaymentMadeResponseDto),
                AllowPaging = true,
                AllowTextWrap = false,
                AllowAdding = false,
                AllowDeleting = true,
                DeleteConfirmationField = nameof(PaymentMadeResponseDto.Id),
                DeleteConfirmationMessage = deleteMessage,
                EnableExport = false,
                DataSource = response.TransactionResponseDtos,
                Columns = new List<DataGridColumn>
                {
                    new() { Field = nameof(PaymentMadeResponseDto.TransactionDate), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridDateHeader, IsDateTime = true },
                    new() { Field = nameof(PaymentMadeResponseDto.Name), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridNameHeader },
                    new() { Field = nameof(PaymentMadeResponseDto.Description), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridDescriptionHeader },
                    new() { Field = nameof(PaymentMadeResponseDto.Amount), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridAmountHeader, IsCurrency = true, TextAlign = TextAlign.Right },
                },
                ActionItems = new List<GridActionItem>
                {
                    new() { Label = CommonResourceConstants.Edit, Action = CommonConstants.Grid.EditAction, ControllerName= $"SmoCampaignStatement/{filingId}/{TransactionType.PaymentMade.Name}", ActionName = EditTransactionEntryActionName },
                    new() { Label = CommonResourceConstants.Delete, Action = CommonConstants.Grid.DeleteAction, ControllerName = $"SmoCampaignStatement/{filingId}", ActionName = DeleteTransactionEntryActionName }
                }
            },
            TransactionSubtotal = Math.Max(response.SummaryResponseDto.PeriodAmount - response.SummaryResponseDto.UnitemizedAmount ?? 0m, 0),
            AddNewTranscationUrl = "PaymentMade01",
            AddNewTranscationUrlQuery = new { filingId, filerId = response.FilerId },
            // This may change in the future.
            UnitemizedPaymentLessThan100 = response.SummaryResponseDto.UnitemizedAmount,
            UnitemizedPaymentSubtotal = response.SummaryResponseDto.UnitemizedAmount,
        };

        return model;
    }
    #endregion

    #region  Transaction Summary - Payments Made by an Agent or Indepdendent Contractor
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionSummaryViewModel> GetPaymentsMadeByAnAgentOrIndependentContractor(long filingId)
    {
        var response = await smoCampaignStatementSvc.GetTransactionSummaryAsync(filingId, FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id);
        string deleteMessage = $"{localizer[ResourceConstants.SmoCampaignStatementTransactionSummaryDeletePaymentConfirmation].Value}";
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = filingId,
            FilingSummaryId = response.SummaryResponseDto.Id,
            FilerId = response.FilerId,
            SummaryType = FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Name,
            PaymentsMadeByAgentOrContractorGridModel = new SmallDataGridModel
            {
                PrimaryKey = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto.Id),
                GridId = "PaymentsMadeByAgentOrContractor",
                GridType = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto),
                AllowPaging = true,
                AllowTextWrap = false,
                AllowAdding = false,
                AllowDeleting = true,
                DeleteConfirmationField = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto.Id),
                DeleteConfirmationMessage = deleteMessage,
                EnableExport = false,
                DataSource = response.TransactionResponseDtos,
                Columns = new List<DataGridColumn>
                {
                    new() { Field = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto.TransactionDate), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridDateHeader, IsDateTime = true },
                    new() { Field = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto.Name), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridNameHeader },
                    new() { Field = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto.AgentOrIndependentContractorName), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridAgentOrContractorHeader },
                    new() { Field = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto.Description), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridDescriptionHeader },
                    new() { Field = nameof(PaymentMadeByAgentOrIndependentContractorResponseDto.Amount), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridAmountHeader, IsCurrency = true, TextAlign = TextAlign.Right },
                },
                ActionItems = new List<GridActionItem>
                {
                    new() { Label = CommonResourceConstants.Edit, Action = CommonConstants.Grid.EditAction, ControllerName= $"SmoCampaignStatement/{filingId}/{TransactionType.PaymentMade.Name}ByAgentOrIndependentContractor", ActionName = EditTransactionEntryActionName },
                    new() { Label = CommonResourceConstants.Delete, Action = CommonConstants.Grid.DeleteAction, ControllerName = $"SmoCampaignStatement/{filingId}", ActionName = DeleteTransactionEntryActionName }
                }
            },
            TransactionSubtotal = response.SummaryResponseDto.PeriodAmount,
            AddNewTranscationUrl = "PaymentMade01",
            AddNewTranscationUrlQuery = new { filingId, filerId = response.FilerId, isPaymentMadeByAgent = true },
        };

        return model;
    }
    #endregion

    #region  Transaction Summary - Persons Receiving $1000 or More
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionSummaryViewModel> GetPersonsReceiving1000OrMore(long filingId)
    {
        var response = await smoCampaignStatementSvc.GetTransactionSummaryAsync(filingId, FilingSummaryType.PersonReceiving1000OrMoreSummary.Id);
        string deleteMessage = $"{localizer[ResourceConstants.SmoCampaignStatementTransactionSummaryDeletePaymentConfirmation].Value}";
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = filingId,
            FilingSummaryId = response.SummaryResponseDto.Id,
            FilerId = response.FilerId,
            SummaryType = FilingSummaryType.PersonReceiving1000OrMoreSummary.Name,
            PersonsReceiving1000OrMoreGridModel = new SmallDataGridModel
            {
                PrimaryKey = nameof(PersonReceiving1000OrMoreResponseDto.Id),
                GridId = "PersonsReceiving1000OrMore",
                GridType = nameof(PersonReceiving1000OrMoreResponseDto),
                AllowPaging = true,
                AllowTextWrap = false,
                AllowAdding = false,
                AllowDeleting = true,
                DeleteConfirmationField = nameof(PersonReceiving1000OrMoreResponseDto.Id),
                DeleteConfirmationMessage = deleteMessage,
                EnableExport = false,
                DataSource = response.TransactionResponseDtos,
                Columns = new List<DataGridColumn>
                {
                    new() { Field = nameof(PersonReceiving1000OrMoreResponseDto.Name), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridNameHeader },
                    new() { Field = nameof(PersonReceiving1000OrMoreResponseDto.Amount), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridAmountHeader, IsCurrency = true, TextAlign = TextAlign.Right },
                    new() { Field = nameof(PersonReceiving1000OrMoreResponseDto.CumulativeAmount), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridCumulativeAmountHeader, IsCurrency = true, TextAlign = TextAlign.Right },
                },
                ActionItems = new List<GridActionItem>
                {
                    new() { Label = CommonResourceConstants.Edit, Action = CommonConstants.Grid.EditAction, ControllerName= $"SmoCampaignStatement/{filingId}/{TransactionType.PersonReceiving1000OrMore.Name}", ActionName = EditTransactionEntryActionName },
                    new() { Label = CommonResourceConstants.Delete, Action = CommonConstants.Grid.DeleteAction, ControllerName = $"SmoCampaignStatement/{filingId}", ActionName = DeleteTransactionEntryActionName }
                }
            },
            TransactionSubtotal = response.SummaryResponseDto.PeriodAmount,
            AddNewTranscationUrl = "PersonReceiving01",
            AddNewTranscationUrlQuery = new { filingId, filerId = response.FilerId },
        };

        return model;
    }
    #endregion

    #region  Transaction Summary - Candidates and Measures Not Listed on Payments Received
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionSummaryViewModel> GetCandidatesAndMeasureOnPaymentsReceived(long filingId)
    {
        var response = await smoCampaignStatementSvc.GetCandidatesOrMeasuresNotListedOnPaymentReceivedAsync(filingId);
        string deleteMessage = $"{localizer[ResourceConstants.SmoCampaignStatementTransactionSummaryDeletePaymentConfirmation].Value}";
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = filingId,
            SummaryType = FilingSummaryType.CandidateOrMeasureSupportedOrOpposedSummary.Name,
            CandidatesMeasuresNotListedGridModel = new SmallDataGridModel
            {
                PrimaryKey = nameof(CandidateOrMeasureNotListedPaymentReceivedResponseDto.Id),
                GridId = "CandidatesMeasuresNotListed",
                GridType = nameof(CandidateOrMeasureNotListedPaymentReceivedResponseDto),
                AllowPaging = true,
                AllowTextWrap = false,
                AllowAdding = false,
                AllowDeleting = true,
                EnableExport = false,
                DeleteConfirmationField = nameof(CandidateOrMeasureNotListedPaymentReceivedResponseDto.Id),
                DeleteConfirmationMessage = deleteMessage,
                DataSource = response,
                Columns = new List<DataGridColumn>
                {
                    new() { Field = nameof(CandidateOrMeasureNotListedPaymentReceivedResponseDto.CandidateOrMeasure), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridCandidateOrMeasureHeader },
                    new() { Field = nameof(CandidateOrMeasureNotListedPaymentReceivedResponseDto.Position), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridSupportOpposeHeader },
                    new() { Field = nameof(CandidateOrMeasureNotListedPaymentReceivedResponseDto.OfficeOrMeasure), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridOfficeOrMeasureHeader },
                    new() { Field = nameof(CandidateOrMeasureNotListedPaymentReceivedResponseDto.Jurisdiction), HeaderText = ResourceConstants.SmoCampaignStatementTransactionSummaryGridJurisdictionHeader },
                },
                ActionItems = new List<GridActionItem>
                {
                    new() { Label = CommonResourceConstants.Edit, Action = CommonConstants.Grid.EditAction, ControllerName= $"SmoCampaignStatement/{filingId}", ActionName = "EditDisclosureWithoutPaymentReceived" },
                    new() { Label = CommonResourceConstants.Delete, Action = CommonConstants.Grid.DeleteAction, ControllerName = $"SmoCampaignStatement/{filingId}", ActionName = "DeleteDisclosureWithoutPaymentReceived" }
                }
            },
            AddNewTranscationUrl = "CandidatesMeasuresNotListed01",
            AddNewTranscationUrlQuery = new { filingId },
        };

        return model;
    }
    #endregion

    #region Verification Page
    public async Task<SmoCampaignStatementVerificationViewModel?> GetVerificationPageViewModel(long filingId, bool isAuthorizedToAttest)
    {
        try
        {
            var smoCampaignStatement = await smoCampaignStatementSvc.GetSmoCampaignStatementOverviewAsync(filingId);

            // Get current user & filer user
            var userResponse = await usersApi.GetSelf();

            // Get responsible officer contacts
            var contacts = await smoCampaignStatementSvc.GetSmoCampaignStatementResponsibleOfficerContactsAsync(filingId);

            // Find the contact that match with current user
            var matchedContact = contacts.FirstOrDefault(x => x.UserId == userResponse.Id);

            // If current user is authorized to attest, check if the registration already attested or not

            var attestationResponse = new SmoCampaignStatementAttestationResponseDto();
            if (isAuthorizedToAttest)
            {
                attestationResponse = await smoCampaignStatementSvc.GetSmoCampaignStatementAttestationAsync(filingId);
            }

            return new SmoCampaignStatementVerificationViewModel
            {
                Id = smoCampaignStatement.Id,
                FilerId = smoCampaignStatement.FilerId,
                FirstName = attestationResponse.FirstName ?? matchedContact?.FirstName,
                LastName = attestationResponse.LastName ?? matchedContact?.LastName,
                Title = attestationResponse.Title ?? matchedContact?.Title,
                ExecutedOn = attestationResponse.ExecutedAt ?? dateTimeSvc.GetCurrentDateTime(),
                IsVerificationCertified = attestationResponse.ExecutedAt is not null,
                IsUserAuthorizedToAttest = isAuthorizedToAttest,
                ResponsibleOfficers = [.. contacts.Select(x => new ResponsibleOfficerSharedViewModel()
                {
                    ContactId = x.Id,
                    Title = x.Title,
                    FirstName = x.FirstName,
                    LastName = x.LastName
                })],
            };

        }
        catch (Exception ex)
        {
            Console.WriteLine("Error deserializing the response: " + ex.Message);
            return new SmoCampaignStatementVerificationViewModel();
        }
    }
    #endregion

    #region Confirmation Page
    /// <inheritdoc />
    public async Task<SmoCampaignStatementConfirmationViewModel?> GetConfirmationPageViewModel(long filingId)
    {
        try
        {
            var response = await smoCampaignStatementSvc.GetSmoCampaignStatementPendingItemsAsync(filingId);

            return new SmoCampaignStatementConfirmationViewModel
            {
                Id = filingId,
                ExecutedOn = dateTimeSvc.GetCurrentDateTime(),
                IsSubmission = true,
                PendingItems = [.. response.Select(x => new PendingItemSharedViewModel
                {
                    Item = x.Item,
                    Status = x.Status,
                })],
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error deserializing the response: " + ex.Message);
            return new SmoCampaignStatementConfirmationViewModel();
        }
    }

    /// <inheritdoc />
    public async Task AttestSmoCampaignStatement(SmoCampaignStatementVerificationViewModel model, ModelStateDictionary modelState)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        // Attest the registration
        var response = await smoCampaignStatementSvc.AttestStatementAsync(model.Id.GetValueOrDefault());

        // Add error to model state
        if (!response.Valid)
        {
            VerificationApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    /// <inheritdoc />
    public async Task SendForAttestation(SmoCampaignStatementVerificationViewModel model, ModelStateDictionary modelState)
    {
        if (model.Id is null)
        {
            throw new KeyNotFoundException($"No registration id exists for registration");
        }

        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = model.SelectedResponsibleOfficersContactIds ?? new List<long> { }
        };

        // Send notification for attestation
        var response = await smoCampaignStatementSvc.SendForAttestationAsync(model.Id.GetValueOrDefault(), request);

        // Add error to model state
        if (!response.Valid)
        {
            VerificationApplyErrorsToModelState(response.ValidationErrors, modelState);
        }
    }

    #endregion

    #region Filer Contact

    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetFilerContactViewModelAsync(long filingId, long? filerId, long? contactId, long? transactionId, string? transactionType)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel(filingId, filerId, contactId, transactionId);

        // Set the default address
        SmoCampaignStatementTransactionEntryViewModel.MapDefaultAddressToModel(model, transactionType ?? string.Empty);

        // If both of the IDs are null, it's a new transaction
        if (contactId is null && transactionId is null)
        {
            return model;
        }

        // If the contactId is null (means the transactionId is not null), it navigates from the Summary List view
        // Since the smallGrid only has a primary but not a secondary key, we need to retrieve the contactId from the transaction
        var effectiveContactId = contactId;
        if (effectiveContactId is null)
        {
            var transaction = await smoCampaignStatementSvc.GetSmoCampaignStatementTransactionAsync(
                filingId,
                transactionId!.Value,
                transactionType ?? string.Empty);
            effectiveContactId = transaction.ContactId;
        }

        // Fetch contact info
        var contactInfo = await contactsApi.GetFilerContactById(effectiveContactId.GetValueOrDefault());

        // Remap filerId in case filerId is missing from the query
        model.FilerId = contactInfo?.FilerId;
        model.ContactId = effectiveContactId;

        // Determine the type of contact (e.g., IndividualContactResponseDto, CommitteeContactResponseDto, etc.)
        return contactInfo switch
        {
            Generated.IndividualContactResponseDto individual => SmoCampaignStatementTransactionEntryViewModel.MapIndividualContactToModel(model, individual),
            Generated.FilerCommitteeContactResponseDto committee => SmoCampaignStatementTransactionEntryViewModel.MapFilerCommitteeContactToModel(model, committee),
            Generated.OrganizationContactResponseDto organization => SmoCampaignStatementTransactionEntryViewModel.MapOrganizationContactToModel(model, organization),
            Generated.CandidateContactResponseDto candidate => SmoCampaignStatementTransactionEntryViewModel.MapCandidateContactToModel(model, candidate),
            _ => model
        };
    }

    public async Task SaveOrUpdateContactAsync(
        SmoCampaignStatementTransactionEntryViewModel model,
        string filerContactForm,
        ModelStateDictionary modelState,
        CancellationToken cancellationToken = default)
    {
        if (model.ContactId is null)
        {
            await CreateContactAsync(model, filerContactForm, modelState, cancellationToken);
        }
        else
        {
            await UpdateContactAsync(model, filerContactForm, modelState, cancellationToken);
        }
    }

    private async Task CreateContactAsync(
        SmoCampaignStatementTransactionEntryViewModel model,
        string filerContactForm,
        ModelStateDictionary modelState,
        CancellationToken cancellationToken)
    {
        var payload = FilerContactMapper.BuildFilerContactPayload(model, filerContactForm);
        var response = await contactsApi.CreateFilerContact(model.FilerId.GetValueOrDefault(), payload, cancellationToken);

        ApplyContactValidationErrorsToModelState(response?.ValidationErrors, model, modelState);

        if (response?.ValidationErrors.Count == 0)
        {
            model.ContactId = response.Id;
        }
    }

    private async Task UpdateContactAsync(
        SmoCampaignStatementTransactionEntryViewModel model,
        string filerContactForm,
        ModelStateDictionary modelState,
        CancellationToken cancellationToken)
    {
        if (model.ContactId is null)
        {
            throw new InvalidOperationException("Contact ID is required for updating.");
        }

        var payload = FilerContactMapper.BuildFilerContactPayload(model, filerContactForm);

        // TD: Temporary handling error due to the current design of the API
        // Need to refactor it later
        try
        {
            await contactsApi.UpdateFilerContact(model.ContactId.Value, payload, cancellationToken);
        }
        catch (ApiException apiEx)
        {
            if (apiEx.Content is not null)
            {
                // De-serialize workflow errors from API response
                var workflowErrors = JsonConvert.DeserializeObject<IReadOnlyList<Generated.WorkFlowError>>(apiEx.Content);

                ApplyContactValidationErrorsToModelState(workflowErrors, model, modelState);
            }
        }
        catch (Exception)
        {
            // Just try to avoid the exception to throw
        }
    }

    #endregion

    #region Transaction Entry
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionEntryViewModel> UpdateModelWithTransactionAsync(SmoCampaignStatementTransactionEntryViewModel model, string transactionType)
    {
        var transaction = await smoCampaignStatementSvc.GetSmoCampaignStatementTransactionAsync(
            model.Id,
            model.TransactionId.GetValueOrDefault(), transactionType
            );

        return transaction switch
        {
            PaymentReceivedResponseDto response => SmoCampaignStatementTransactionEntryViewModel.MapPaymentReceivedResponseToModel(response),
            PaymentMadeByAgentOrIndependentContractorResponseDto response => SmoCampaignStatementTransactionEntryViewModel.MapPaymentMadeByAgentResponseToModel(response),
            PaymentMadeResponseDto response => SmoCampaignStatementTransactionEntryViewModel.MapPaymentMadeResponseToModel(response),
            PersonReceiving1000OrMoreResponseDto response => SmoCampaignStatementTransactionEntryViewModel.MapPersonReceivingResponseToModel(response),
            _ => model
        };
    }

    /// <inheritdoc />
    public async Task SaveTransactionAsync(SmoCampaignStatementTransactionEntryViewModel model, ModelStateDictionary modelState, string transactionType)
    {
        TransactionDetailRequest request = transactionType switch
        {
            var type when type == TransactionType.PaymentReceived.Name => DisclosureTransactionMapper.MapModelToPaymentReceivedRequest(model),
            var type when type == TransactionType.PaymentMade.Name => DisclosureTransactionMapper.MapModelToPaymentMadeRequest(model),
            var type when type == TransactionType.PersonReceiving1000OrMore.Name => DisclosureTransactionMapper.MapModelToPersonReceivingRequest(model),
            _ => throw new InvalidOperationException($"Unsupported transaction type: {transactionType}"),
        };

        // If there is no transaction ID, means create new transaction
        // Otherwise, update the existing one
        TransactionResponseDto transactionResponse;
        if (model.TransactionId.GetValueOrDefault() == 0)
        {
            transactionResponse = await smoCampaignStatementSvc.CreateSmoCampaignStatementTransactionAsync(model.Id, request);
        }
        else
        {
            transactionResponse = await smoCampaignStatementSvc.UpdateSmoCampaignStatementTransactionAsync(model.Id, model.TransactionId.GetValueOrDefault(), request);
        }

        // Add error to model state
        ApplyDecisionsToTransactionModelState(transactionResponse, modelState);
    }
    #endregion

    #region Transaction Entry - Payment Received
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetPaymentReceived03ViewModelAsync(PaymentReceivedParameters parameters)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel(
            parameters.Id,
            parameters.ContactId,
            parameters.TransactionId);

        // If transactionId has value, it's editing
        if (model.TransactionId.HasValue)
        {
            model = await UpdateModelWithTransactionAsync(model, TransactionType.PaymentReceived.Name);
        }

        // TD: Need a better way to cover this
        if (!string.IsNullOrWhiteSpace(parameters.Jurisdiction) &&
            !string.IsNullOrWhiteSpace(parameters.PertainsTo) &&
            !string.IsNullOrWhiteSpace(parameters.Position))
        {
            // Override by the selection if this is previous of PaymentReceived04
            OverrideDataToViewModelForPaymentReceived(model, parameters);
        }

        return model;
    }

    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetPaymentReceived04ViewModelAsync(PaymentReceivedParameters parameters)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel(
            parameters.Id,
            parameters.ContactId,
            parameters.TransactionId);

        // If transactionId has value, it's editing
        if (model.TransactionId.HasValue)
        {
            model = await UpdateModelWithTransactionAsync(model, TransactionType.PaymentReceived.Name);
        }

        // Override by the selection from previous step
        OverrideDataToViewModelForPaymentReceived(model, parameters);
        await PopulateUnitemizedAndCumulativeAmounts(model);

        return model;
    }

    public async Task PopulateUnitemizedAndCumulativeAmounts(SmoCampaignStatementTransactionEntryViewModel model)
    {
        var request = new PaymentReceivedRequest
        {
            Jurisdiction = model.Jurisdiction ?? string.Empty,
            Position = model.Position ?? string.Empty,
            ContactId = model.ContactId ?? 0,
            StanceOnCandidate = new StanceOnCandidateDto { CandidateId = model.CandidateId },
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto { BallotMeasureId = model.BallotMeasureId },
        };

        var response = await smoCampaignStatementSvc.GetPaymentReceivedCumulativeAmountAsync(model.Id, request);

        model.UnitemizedAmount = response.PreviouslyUnitemizedAmount ?? 0m;
        model.CumulativeAmountToDate = (response.CumulativeAmount ?? 0m) + model.TransactionAmount.GetValueOrDefault();
    }
    #endregion

    #region Candidate or Measurer Without Payment Received
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetCandidateOrMeasureWithoutPaymentReceivedAsync(long filingId, long disclosureWithoutPaymentId)
    {
        var response = await smoCampaignStatementSvc.GetDisclosureWithoutPaymentReceivedByIdAsync(filingId, disclosureWithoutPaymentId);

        return SmoCampaignStatementTransactionEntryViewModel.MapDisclosureWithoutPaymentReceivedResponseToModel(response);
    }

    /// <inheritdoc />
    public async Task SaveCandidateOrMeasureWithoutPaymentReceivedAsync(SmoCampaignStatementTransactionEntryViewModel model, ModelStateDictionary modelState)
    {
        var request = DisclosureTransactionMapper.MapModelToDisclosureWithoutPaymentReceivedDto(model);

        // If there is no DisclosureWithoutPaymentReceived ID, means create new record
        // Otherwise, update the existing one
        // Reuse response as this is not an actual transaction
        TransactionResponseDto transactionResponse;
        if (model.DisclosureWithoutPaymentId.GetValueOrDefault() == 0)
        {
            transactionResponse = await smoCampaignStatementSvc.CreateDisclosureWithoutPaymentReceivedAsync(model.Id, request);
        }
        else
        {
            transactionResponse = await smoCampaignStatementSvc.UpdateDisclosureWithoutPaymentReceivedAsync(model.Id, model.DisclosureWithoutPaymentId.GetValueOrDefault(), request);
        }

        // Add error to model state
        ApplyDecisionsToTransactionModelState(transactionResponse, modelState);
    }

    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetCandidatesMeasuresNotListedViewModel(CandidatesMeasuresNotListedParameters parameters)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel(parameters.Id);

        // If disclosureWithoutPaymentId has value, it's editing
        if (parameters.DisclosureWithoutPaymentId.HasValue)
        {
            model = await GetCandidateOrMeasureWithoutPaymentReceivedAsync(parameters.Id, parameters.DisclosureWithoutPaymentId.GetValueOrDefault());
        }

        // TD: Need a better way to cover this
        if (!string.IsNullOrWhiteSpace(parameters.Jurisdiction) &&
            !string.IsNullOrWhiteSpace(parameters.PertainsTo) &&
            !string.IsNullOrWhiteSpace(parameters.Position))
        {
            // Override by the selection if this is previous page
            // CandidatesMeasuresNotListed01 -> CandidatesMeasuresNotListed02
            // CandidatesMeasuresNotListed01 <- CandidatesMeasuresNotListed02
            OverrideDataToViewModelForNotListed(model, parameters);
        }

        return model;
    }
    #endregion

    #region Transaction Entry - Payment Made
    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetPaymentMade03ViewModelAsync(long filingId, long contactId, long? transactionId)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel(filingId, contactId, transactionId);

        // If transactionId has value, it's editing
        if (model.TransactionId.HasValue)
        {
            model = await UpdateModelWithTransactionAsync(model, TransactionType.PaymentMade.Name);
        }

        // Get Expenditure Code to map to View (Payment Code)
        model.CodeOptions = await GetPaymentCodeListDropdown();

        return model;
    }
    #endregion

    #region Transaction Entry - Person Receiving
    /// <inheritdoc/>
    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetPersonReceivingFilerContactViewModelAsync(long filingId, long? contactId, long? transactionId, long? officerId, long? registrationId, long? filerId)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel(filingId, filerId, contactId, transactionId);

        // If officerId presents, it's adding new transaction or modifying the payee
        if (officerId is not null)
        {
            // Get payee information from officer information
            // If the officer already link to a contact, return the information from the contact
            await GetOfficerInformationAndMapToModelAsync(model, registrationId.GetValueOrDefault(), officerId.GetValueOrDefault(), filerId.GetValueOrDefault());

            return model;
        }

        // If both of the IDs are null, it's a new transaction
        if (contactId is null && transactionId is null)
        {
            return model;
        }

        // If the contactId is null (means the transactionId is not null), it navigates from the Summary List view
        // Since the smallGrid only has a primary but not a secondary key, we need to retrieve the contactId from the transaction
        var effectiveContactId = contactId;
        if (effectiveContactId is null)
        {
            var transaction = await smoCampaignStatementSvc.GetSmoCampaignStatementTransactionAsync(
                filingId,
                transactionId!.Value,
                TransactionType.PersonReceiving1000OrMore);
            effectiveContactId = transaction.ContactId.GetValueOrDefault();
        }

        // Fetch contact info
        await PopulateModelFromIndividualContactAsync(model, effectiveContactId.Value);

        return model;
    }

    /// <inheritdoc />
    public async Task<SmoCampaignStatementTransactionEntryViewModel> GetPersonReceiving03ViewModelAsync(long filingId, long contactId, long? transactionId)
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel(filingId, contactId, transactionId);

        // If transactionId has value, it's editing
        if (model.TransactionId.HasValue)
        {
            model = await UpdateModelWithTransactionAsync(model, TransactionType.PersonReceiving1000OrMore.Name);
        }

        return model;
    }
    #endregion

    #region Amendment
    public async Task<long> InitializeSmoCampaignStatementAmendment(long filingId)
    {
        long amendFilingId = await smoCampaignStatementSvc.InitializeSmoCampaignStatementAmendmentAsync(filingId);

        return amendFilingId;
    }
    #endregion

    #region Amendment Explanation
    /// <inheritdoc />
    public async Task<AmendSmoCampaignStatementAmendmentExplanationViewModel> GetAmendmentExplanation(long filingId)
    {
        var response = await smoCampaignStatementSvc.GetSmoCampaignStatementOverviewAsync(filingId);
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel()
        {
            Id = filingId,
            AmendmentExplanation = response.AmendmentExplanation ?? "",
        };

        return model;
    }

    public async Task<FilingSummaryResponseDto?> UpdateAmendmentExplanation(AmendSmoCampaignStatementAmendmentExplanationViewModel model, ModelStateDictionary modelState)
    {
        try
        {
            if (!model.Id.HasValue)
            {
                throw new InvalidOperationException("Id is null.");
            }
            var request = new FilingSummaryAmendmentExplanationRequest()
            {
                AmendmentExplanation = model.AmendmentExplanation ?? ""
            };

            var response = await smoCampaignStatementSvc.UpdateAmendmentExplanationAsync(model.Id.Value, request);

            ApplyDecisionsToAmendmentExplanation(response, modelState);

            return response;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error de-serializing the response: " + ex.Message);
            return null;
        }
    }
    #endregion

    #region Shared
    /// <inheritdoc />
    public async Task<FilingSummaryResponseDto?> SubmitTransactionSummaryUnitemizedPaymentForm(SmoCampaignStatementTransactionSummaryViewModel model, ModelStateDictionary modelState)
    {
        try
        {
            if (!model.Id.HasValue || !model.FilingSummaryId.HasValue)
            {
                throw new InvalidOperationException("Id or FilingSummaryId is null.");
            }
            var request = new SmoCampaignStatementFilingSummaryRequest()
            {
                UnitemizedAmount = model.UnitemizedPaymentLessThan100.GetValueOrDefault()
            };

            var response = await smoCampaignStatementSvc.UpdateFilingSummaryAsync(model.Id.Value, model.FilingSummaryId.Value, request);

            ApplyDecisionsToTransactionSummaryModelState(response, modelState);

            return response;

        }
        catch (Exception ex)
        {
            Console.WriteLine("Error de-serializing the response: " + ex.Message);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task DeleteTransactionEntry(long filingId, long transactionId)
    {
        try
        {
            await transactionSvc.DeleteTransactionAsync(filingId, transactionId);
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error in deleting the transaction: " + ex.Message);
        }
    }

    /// <inheritdoc />
    public async Task DeleteDisclosureWithoutPaymentReceived(long filingId, long disclosureWithoutPaymentId)
    {
        try
        {
            await smoCampaignStatementSvc.DeleteDisclosureWithoutPaymentReceivedAsync(filingId, disclosureWithoutPaymentId);
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error in deleting the record: " + ex.Message);
        }
    }

    /// <inheritdoc />
    public async Task<List<SmoRegistrationContactDto>> SearchSmoOfficersAsync(long filerId, long filingId, string search)
    {
        // Get officer from the latest Accepted registration
        var officers = await smoRegistrationSvc.SearchLatestAcceptedSmoRegistrationOfficerByNameAsync(filerId, search);

        // Get the officer that already has transactions
        var summaries = await smoCampaignStatementSvc.GetTransactionSummaryAsync(filingId, FilingSummaryType.PersonReceiving1000OrMoreSummary.Id);
        var existingOfficerNames = summaries.TransactionResponseDtos
            .Select(x => x as PersonReceiving1000OrMoreResponseDto)
            .Where(x => !string.IsNullOrWhiteSpace(x?.Name))
            .Select(x => x!.Name).ToList();

        // Filter officer who already have the transaction by Name
        var filteredOfficers = officers.Where(x => !existingOfficerNames.Contains($"{x.FirstName} {x.LastName}")).ToList();

        return filteredOfficers;
    }

    /// <inheritdoc />
    public async Task<List<SelectListItem>> GetPaymentCodeListDropdown()
    {
        var expenditureCodes = await referenceDataSvc.GetAllExpenditureCodesAsync();

        return [.. expenditureCodes.Select(x => new SelectListItem
        {
            Value = x.Id.ToString(CultureInfo.InvariantCulture),
            Text = $"{x.Abbrev} - {x.Description}",
        })];
    }

    /// <inheritdoc />
    public async Task<bool> IsSmoCampaignStatementAmendment(long filingId)
    {
        return await smoCampaignStatementSvc.IsSmoCampaignStatementAmendmentAsync(filingId);
    }

    /// <inheritdoc />
    public async Task ValidatePersonReceivingOfficerRequestAsync(SmoCampaignStatementTransactionEntryViewModel model, ModelStateDictionary modelState)
    {
        var request = new PersonReceiving1000ValidationRequest
        {
            OfficerId = model.OfficerId
        };

        var response = await smoCampaignStatementSvc.ValidatePersonReceivingOfficerAsync(model.Id, request);

        ApplyDecisionsToTransactionModelState(response, modelState);
    }

    /// <inheritdoc />
    public async Task ValidatePaymentReceivedRequestAsync(SmoCampaignStatementTransactionEntryViewModel model, ModelStateDictionary modelState)
    {
        var request = new PaymentReceivedValidationRequestDto
        {
            CandidateOrMeasure = model.PertainsTo,
            Jurisdiction = model.Jurisdiction,
            Position = model.Position ?? string.Empty,
            Amount = model.TransactionAmount.GetValueOrDefault(),
            TransactionDate = model.TransactionDate,
            Notes = model.Notes,
        };

        var response = await smoCampaignStatementSvc.ValidatePaymentReceivedAsync(model.Id, request);

        ApplyDecisionsToTransactionModelState(response, modelState);
    }

    /// <inheritdoc />
    public async Task ValidateCandidateOrMeasureNotListedAsync(SmoCampaignStatementTransactionEntryViewModel model, ModelStateDictionary modelState)
    {
        var request = new CandidateOrMeasureNotListedValidationRequest
        {
            CandidateOrMeasure = model.PertainsTo ?? string.Empty,
            Jurisdiction = model.Jurisdiction ?? string.Empty,
            Position = model.Position ?? string.Empty,
        };

        var response = await smoCampaignStatementSvc.ValidateCandidateOrMeasureNotListedP1Async(model.Id, request);

        ApplyDecisionsToTransactionModelState(response, modelState);
    }
    #endregion

    #region Private
    private const string EditTransactionEntryActionName = "EditTransactionEntry";
    private const string DeleteTransactionEntryActionName = "DeleteTransactionEntry";

    private static Dictionary<string, FieldProperty> BuildTransactorValidationMap(string addressPrefix)
    {
        return new Dictionary<string, FieldProperty>
        {
            // ParticipantType
            ["PayorType"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.ParticipantType),
                ResourceConstants.ContactFormPayorType),

            // Individual fields
            ["FirstName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.FirstName),
                ResourceConstants.SmoCampaignStatementTransactorsFirstName),
            ["MiddleName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.MiddleName),
                ResourceConstants.SmoCampaignStatementTransactorsMiddleName),
            ["LastName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.LastName),
                ResourceConstants.SmoCampaignStatementTransactorsLastName),
            ["Employer"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.Employer),
                ResourceConstants.SmoCampaignStatementTransactorsEmployer),

            // Address fields — now using prefix
            ["Address.Street"] = new FieldProperty($"{addressPrefix}.Street", CommonResourceConstants.Street),
            ["Address.Street2"] = new FieldProperty($"{addressPrefix}.Street2", CommonResourceConstants.Street2),
            ["Address.City"] = new FieldProperty($"{addressPrefix}.City", CommonResourceConstants.City),
            ["Address.State"] = new FieldProperty($"{addressPrefix}.State", CommonResourceConstants.State),
            ["Address.Zip"] = new FieldProperty($"{addressPrefix}.Zip", CommonResourceConstants.ZipCode),
            ["Address.Country"] = new FieldProperty($"{addressPrefix}.Country", CommonResourceConstants.Country),

            // Committee fields
            ["CommitteeId"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.CommitteeId),
                ResourceConstants.SmoCampaignStatementTransactorsSearchForCommittee),
            ["CommitteeName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.CommitteeName),
                ResourceConstants.SmoCampaignStatementTransactorsSearchForCommittee),

            // Candidate fields
            ["CandidateFirstName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.CandidateFirstName),
                ResourceConstants.CandidateBallotMeasureCandidateFirstName),
            ["CandidateMiddleName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.CandidateMiddleName),
                ResourceConstants.CandidateBallotMeasureCandidateMiddleName),
            ["CandidateLastName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.CandidateLastName),
                ResourceConstants.CandidateBallotMeasureCandidateLastName),
            ["OfficeSought"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.OfficeSought),
                ResourceConstants.CandidateBallotMeasureOfficeSought),
            ["CandidateJurisdictionName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.JurisdictionName),
                ResourceConstants.CandidateBallotMeasureJurisdictionName),
            ["District"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.District),
                ResourceConstants.CandidateBallotMeasureDistrict),

            // Organization fields
            ["OrganizationName"] = new FieldProperty(
                nameof(SmoCampaignStatementTransactionEntryViewModel.OrganizationName),
                ResourceConstants.SmoCampaignStatementTransactorsOrganizationName)
        };
    }

    private static Dictionary<string, FieldProperty> BuildTransactionValidationMap()
    {
        return new Dictionary<string, FieldProperty>
        {
            // Payment Received
            { "PayorType", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.PertainsTo), ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentPaymentPertain) },
            { "Jurisdiction", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.Jurisdiction), ResourceConstants.CandidateBallotMeasureJurisdiction) },
            { "Position", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.Position), ResourceConstants.CandidateBallotMeasurePosition) },
            { "AmountReceived", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.TransactionAmount), ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentAmountReceived) },
            { "DateReceived", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.TransactionDate), ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentDateReceived) },
            { "CandidateId", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.CandidateId), ResourceConstants.CandidateBallotMeasureAddCandidate) },
            { "FirstName", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.CandidateFirstName), ResourceConstants.CandidateBallotMeasureCandidateFirstName) },
            { "MiddleName", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.CandidateMiddleName), ResourceConstants.CandidateBallotMeasureCandidateMiddleName) },
            { "LastName", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.CandidateLastName), ResourceConstants.CandidateBallotMeasureCandidateLastName) },
            { "OfficeSought", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.OfficeSought), ResourceConstants.CandidateBallotMeasureOfficeSought) },
            { "JurisdictionName", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.JurisdictionName), ResourceConstants.CandidateBallotMeasureJurisdictionName) },
            { "District", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.District), ResourceConstants.CandidateBallotMeasureDistrict) },
            { "BallotId", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.BallotMeasureId), ResourceConstants.CandidateBallotMeasureAddMeasure) },
            { "BallotLetter", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.BallotLetter), ResourceConstants.CandidateBallotMeasureBallotLetter) },
            { "FullBallotTitle", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.BallotMeasureTitle), ResourceConstants.CandidateBallotMeasureFullTitleBallotMeasure) },

            // Person Receiving 1000
            { "OfficerId", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.OfficerId), ResourceConstants.ChooseOfficer) },

            // Payment Made/By Agent
            { "CodeId", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.CodeId), ResourceConstants.PaymentCode) },
            { "AgentOrIndependentContractorName", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.AgentOrIndependentContractorName ), ResourceConstants.SmoCampaignStatementPaymentMade03TextboxNameOf) },
            { "Description", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.Description), ResourceConstants.Description) },
            { "DatePaid", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.TransactionDate), ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentDatePaid) },
            { "AmountPaid", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.TransactionAmount), ResourceConstants.TransactionAboutThePaymentAmountPaid) },
            { "FD_CF_Filing_SMO_401_PaymentReceivedInformation.UnitemizedAmount", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.UnitemizedAmount), ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentPreviouslyUnitemizedAmount) },

            // Share
            { "Amount", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.TransactionAmount), ResourceConstants.Amount) },
            { "Notes", new FieldProperty(nameof(SmoCampaignStatementTransactionEntryViewModel.Notes), ResourceConstants.SmoCampaignStatementTransactorsAboutThePaymentNotes) },
        };
    }

    private void ApplyDecisionsToTransactionSummaryModelState(FilingSummaryResponseDto response, ModelStateDictionary modelState)
    {
        if (!response.Valid.GetValueOrDefault() && response.ValidationErrors?.Count > 0)
        {
            var smoUnitemizedAmountFieldValidationMap = new Dictionary<string, FieldProperty>
            {
                { "UnitemizedPaymentsReceivedTotal", new FieldProperty("UnitemizedPaymentLessThan100", ResourceConstants.SmoCampaignStatementTransactionSummaryUnitemizedPaymentsLessThan100) },
                { "UnitemizedPaymentsMadeTotal", new FieldProperty("UnitemizedPaymentLessThan100", ResourceConstants.SmoCampaignStatementTransactionSummaryUnitemizedPaymentsLessThan100) }
            };

            decisionsValidationMapService.ApplyErrorsToModelState(smoUnitemizedAmountFieldValidationMap, response.ValidationErrors, modelState);
        }
    }

    private void ApplyContactValidationErrorsToModelState(
        IReadOnlyList<Generated.WorkFlowError>? validationErrors,
        SmoCampaignStatementTransactionEntryViewModel model,
        ModelStateDictionary modelState
        )
    {
        if (validationErrors is null || validationErrors.Count == 0)
        {
            return;
        }

        var remappedErrors = validationErrors.Select(error =>
        {
            var fieldName = error.FieldName;

            if (model.ParticipantType == FilerContactType.Candidate.Name && fieldName.Contains("Name", StringComparison.Ordinal))
            {
                fieldName = $"{DisclosureConstants.CandidateOrMeasure.Candidate}{fieldName}";
            }

            return new Generated.WorkFlowError(
                fieldName: fieldName,
                errorCode: error.ErrorCode,
                errorType: error.ErrorType,
                message: error.Message
            );
        }).ToList();


        var convertedErrors = MapValidationErrors(remappedErrors);
        foreach (var err in convertedErrors)
        {
            Console.WriteLine($"Final Error: {err.FieldName} → {err.Message}");
        }

        string addressPrefix;

        switch (model.ParticipantType)
        {
            case var pt when pt == FilerContactType.Organization.Name:
                // Organization → use the organization address
                addressPrefix = nameof(model.OrganizationTransactorAddress);
                break;

            case var pt when pt == FilerContactType.Individual.Name:
                // Individual → use the individual address
                addressPrefix = nameof(model.IndividualTransactorAddress);
                break;

            default:
                // any future/new type → fallback to individual address
                addressPrefix = nameof(model.IndividualTransactorAddress);
                break;
        }

        var validationMap = BuildTransactorValidationMap(addressPrefix);

        decisionsValidationMapService.ApplyErrorsToModelState(validationMap, convertedErrors, modelState);
    }

    private static List<WorkFlowError> MapValidationErrors(List<Generated.WorkFlowError> apiErrors)
    {
        return [.. apiErrors
            .Select(e => new WorkFlowError(
                e.FieldName ?? string.Empty,
                e.ErrorCode ?? string.Empty,
                e.ErrorType ?? string.Empty,
                e.Message ?? string.Empty
            ))];
    }

    private void ApplyDecisionsToAmendmentExplanation(FilingSummaryResponseDto response, ModelStateDictionary modelState)
    {
        if (!(response!.Valid ?? false) && response.ValidationErrors != null)
        {
            var smoAmendmentExplanationValidationMap = new Dictionary<string, FieldProperty>
            {
                { "AmendmentExplanation", new FieldProperty("AmendmentExplanation", ResourceConstants.SmoCampaignStatementAmendmentExplanationTitle) },
            };

            decisionsValidationMapService.ApplyErrorsToModelState(smoAmendmentExplanationValidationMap, response!.ValidationErrors, modelState);
        }
    }

    private string SetFilingSummaryActionText(long? filingSumaryStatusId)
    {
        return filingSumaryStatusId == FilingSummaryStatus.NothingToReport.Id ?
        localizer[ResourceConstants.Start].Value
        : localizer[ResourceConstants.Edit].Value;
    }

    private static void OverrideDataToViewModelForPaymentReceived(SmoCampaignStatementTransactionEntryViewModel model, PaymentReceivedParameters parameters)
    {
        model.Jurisdiction = parameters.Jurisdiction;
        model.PertainsTo = parameters.PertainsTo;
        model.Position = parameters.Position;

        model.TransactionAmount = parameters.TransactionAmount.GetValueOrDefault();
        model.TransactionDate = parameters.TransactionDate;
        model.Notes = parameters.Notes;
        model.AttachedFileGuidsJson = parameters.AttachedFileGuidsJson;
    }

    private static void OverrideDataToViewModelForNotListed(SmoCampaignStatementTransactionEntryViewModel model, CandidatesMeasuresNotListedParameters parameters)
    {
        model.Jurisdiction = parameters.Jurisdiction;
        model.PertainsTo = parameters.PertainsTo;
        model.Position = parameters.Position;
    }

    private async Task GetOfficerInformationAndMapToModelAsync(SmoCampaignStatementTransactionEntryViewModel model, long registrationId, long officerId, long filerId)
    {
        // Find officer by selection
        var officer = await smoRegistrationSvc.GetSmoOfficer(registrationId, officerId);
        if (officer is null)
        {
            return;
        }

        // Get list current filer contact that linked to a filer
        var contacts = await contactsApi.GetContacts(filerId);
        var foundContact = contacts.Where(x => x is Generated.IndividualContactResponse).OfType<Generated.IndividualContactResponse>().FirstOrDefault(x => x.FirstName == officer.FirstName && x.LastName == officer.LastName);

        // If cannot found any matched contact, populate officer information into model
        if (foundContact is null)
        {
            SmoCampaignStatementTransactionEntryViewModel.MapOfficerContactToModel(model, officer);
            return;
        }

        // Fetch contact info
        await PopulateModelFromIndividualContactAsync(model, foundContact.Id);
    }

    private async Task PopulateModelFromIndividualContactAsync(SmoCampaignStatementTransactionEntryViewModel model, long contactId)
    {
        var contactInfo = await contactsApi.GetFilerContactById(contactId);
        if (contactInfo is not null)
        {
            // Remap filerId in case filerId is missing from the query
            model.ContactId = contactId;
            model.FilerId = contactInfo.FilerId;

            SmoCampaignStatementTransactionEntryViewModel.MapIndividualContactToModel(model, (contactInfo as Generated.IndividualContactResponseDto)!);
        }
    }

    private static void VerificationApplyErrorsToModelState(List<WorkFlowError> errors, ModelStateDictionary modelState)
    {
        foreach (var error in errors)
        {
            // Errors to show at the bottom using Html.ValidationSummary
            string fieldName = "{{Field Name}}";
            if (error.Message.Contains(fieldName, StringComparison.Ordinal))
            {
                modelState.AddModelError("", error.Message.Replace(fieldName, error.FieldName, StringComparison.Ordinal));
            }
            else
            {
                modelState.AddModelError("", error.FieldName + ": " + error.Message);

            }
        }
    }

    private void ApplyDecisionsToTransactionModelState(TransactionResponseDto response, ModelStateDictionary modelState)
    {
        if (!response.Valid && response.ValidationErrors.Count > 0)
        {
            decisionsValidationMapService.ApplyErrorsToModelState(BuildTransactionValidationMap(), response!.ValidationErrors, modelState);
        }
    }
    #endregion
}
