@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Registrations.AmendSmoRegistration
@using SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration
@using SOS.CalAccess.Foundation.Utils

@inject IHtmlLocalizer<SharedResources> Localizer
@inject IDateTimeSvc DateTimeSvc

@model SOS.CalAccess.FilerPortal.Models.Registrations.SmoRegistration.SmoRegistrationDetailsStep01ViewModel
@{
    var organizationLevel = new Dictionary<string, string>
    {
        { "state",  Localizer[ResourceConstants.State].Value },
        { "county", Localizer[ResourceConstants.County].Value },
        { "city", Localizer[ResourceConstants.City].Value },
    };

    var yesNoOptions = new Dictionary<string, string>
    {
        { "true", Localizer[ResourceConstants.Yes].Value },
        { "false", Localizer[ResourceConstants.No].Value }
    };

    var isAmending = Model is AmendSmoRegistrationDetailsStep01ViewModel;

    var committeeModelData = new CommitteeSearchPartialViewModel
    {
        ControllerName = isAmending ? RegistrationConstants.RoutingPath.SlateMailerOrganizationAmendment : RegistrationConstants.RoutingPath.SlateMailerOrganization,
        CommitteeId = Model.CommitteeId,
        CommitteeName = Model.CommitteeName,
        Label = ResourceConstants.SmoRegistration04CommitteeIDOrName,
        Placeholder = ResourceConstants.SmoRegistration04CommitteeIDOrNameMessage,
        Required = true,
    };
}

@Html.AntiForgeryToken()
@Html.HiddenFor(m => m.Id)

<div class="mb-3 cursor-pointer">
    @Html.Radio(SharedLocalizer, nameof(Model.OrganizationLevelOfActivity), Localizer[ResourceConstants.SmoRegistration04OrganizationLevelOfActivity].Value, organizationLevel, Model.OrganizationLevelOfActivity ?? "", false, false)
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.OrganizationLevelOfActivity)
</div>

<div class="mb-3 cursor-pointer">
    <div class="d-flex align-items-center gap-2">
        @Html.LabelFor(m => m.IsOrganizationQualified, Localizer[ResourceConstants.SmoRegistration04IsOrganizationQualified].Value, new { @class = "form-label" })
        @await Html.PartialAsync("_TooltipButton", @Localizer[ResourceConstants.SmoRegistration04IsOrganizationQualifiedTooltip].Value)
    </div>

    @Html.Radio(SharedLocalizer, nameof(Model.IsOrganizationQualified), "", yesNoOptions, Model.IsOrganizationQualified?.ToString().ToLower() ?? "", false, false)
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.IsOrganizationQualified)
</div>

<div id="dateQualifiedField" class="col-md-12 mb-3">
    @Html.DatePickerFor(SharedLocalizer, m => m.DateQualifiedAsSMO, Localizer[ResourceConstants.SmoRegistration04DateQualifiedAsSMO].Value, minDate: null, maxDate: DateTimeSvc.GetCurrentDateTime(), format: "MM/dd/yyyy", isRequired: true, isReadOnly: false, cssClass: "datepicker-container", placeholderResourceKey: "MM/DD/YYYY")
</div>

<div class="mb-3 cursor-pointer">
    <div class="d-flex align-items-center gap-2">
        @Html.LabelFor(m => m.IsOrganizationCampaignCommittee, Localizer[ResourceConstants.SmoRegistration04IsOrganizationCampaignCommittee].Value, new { @class = "form-label" })
        @await Html.PartialAsync("_TooltipButton", @Localizer[ResourceConstants.SmoRegistration04IsOrganizationCampaignCommitteeTooltip].Value)
    </div>

    @Html.Radio(SharedLocalizer, nameof(Model.IsOrganizationCampaignCommittee), "", yesNoOptions, Model.IsOrganizationCampaignCommittee?.ToString().ToLower() ?? "", false, false, captionKey: ResourceConstants.SmoRegistration04IsOrganizationCampaignCommitteeCaption)
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.IsOrganizationCampaignCommittee)
</div>

<div id="committee-search-container">
    <div class="col-sm-6 mb-3">
        <partial name="_CommitteeSearch" Model="committeeModelData" />
    </div>
</div>

<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
        function toggleFieldsAndValidate() {
            var isQualified = document.querySelector('input[name="IsOrganizationQualified"]:checked');
            var dateQualifiedPicker = document.getElementById("DateQualifiedAsSMO");

            if (isQualified && isQualified.value === "true") {
                if (dateQualifiedPicker && dateQualifiedPicker.ej2_instances) {
                    dateQualifiedPicker.ej2_instances[0].enabled = true;
                }
            } else {
                if (dateQualifiedPicker && dateQualifiedPicker.ej2_instances) {
                    dateQualifiedPicker.ej2_instances[0].enabled = false;
                    dateQualifiedPicker.ej2_instances[0].value = null;
                }
            }

            // Get OrganizationCampaignCommittee Radio Element
            var isOrganizationCampaignCommitteeTrue = document.querySelector("input#IsOrganizationCampaignCommittee_true");

            // Get Committee Search Element
            var committeeSearchInput = document.getElementById('@Html.IdFor(m => m.CommitteeName)');

            // If OrganizationCampaignCommittee clicked Yes then enable the Search field.
            // Otherwise, disable it.
            if (isOrganizationCampaignCommitteeTrue.checked) {
                committeeSearchInput.disabled = false;
            } else {
                committeeSearchInput.disabled = true;
                committeeSearchInput.value = "";
            }
        }

        document.querySelectorAll('input[name="IsOrganizationQualified"], input[name="IsOrganizationCampaignCommittee"]').forEach(function (radio) {
            radio.addEventListener("change", toggleFieldsAndValidate);
        });

        //Ensure correct state on page load
        toggleFieldsAndValidate();
    });
</script>
