@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.FilerPortal.ViewHelpers;
@using SOS.CalAccess.UI.Common.Enums;
@using SOS.CalAccess.UI.Common.Localization;

@inject IHtmlLocalizer<SharedResources> Localizer
@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.PayLobbyistFeeViewModel;
@{
    var progressBar = LobbyistRegistrationProgressBarHelper.BuildProgressBar(
        version: Model.Version,
        id: Model.Id,
        type: RegistrationConstants.RegistrationType.LobbyistEmployer,
        currentStep: 2,
        ViewData
    );

    var buttonConfig = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            ButtonBarModel.DefaultPrevious,
            new ButtonConfig()
            {
                Type = ButtonType.Button,
                Action = FormAction.Submit,
                CssClass = "btn btn-primary me-2",
                InnerTextKey = CommonResourceConstants.Save
            },
        },
        RightButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = Html.Raw(@"<a href='#'
                                       class='btn btn-outline-primary'
                                       data-bs-toggle='modal'
                                       data-bs-target='#cancelConfirmModal'>
                                       Cancel
                                    </a>"),
            },        
        }
    };

    var payerOptions = new Dictionary<string, string>
    {
        { "Employer", SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationPayLobbyistFeeByEmployer].Value },
        { "Lobbyist", SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationPayLobbyistFeeByLobbist].Value }
    };

    var cancelModal = new CancelConfirmModal(
        Title: SharedLocalizer["Common.CancelConfirmationTitle"].Value,
        Body: SharedLocalizer["Common.CancelConfirmationBody"].Value,
        CloseButtonText: SharedLocalizer["Common.CancelConfirmationClose"].Value,
        SubmitButtonText: SharedLocalizer["Common.CancelConfirmationSubmit"].Value,
        ActionUrl: Url.Action("Step02LobbyistList", "LobbyistEmployerRegistration", new { id = Model.Id }) ?? "",
        Method: "GET"
    );
}


@Html.HiddenFor(m => m.Id)
@Html.HiddenFor(m => m.Version)
@Html.HiddenFor(m => m.LobbyistEmployerId)
@Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationTitle2)
<h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationTitle]</h3>

<partial name="_LayoutProgressbar" model="progressBar" />
<div class="form-container">
    <h3> @SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationPayLobbyistFeeTitle]</h3>

    @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationPayLobbyistFeeSummaryText)

    @using (Html.BeginForm("Step02PayLobbyistFee", "LobbyistEmployerRegistration", FormMethod.Post))
    {
        <div class="mb-3">
            @Html.Radio(SharedLocalizer, "Payer", ResourceConstants.LobbyistEmployerRegistrationPayLobbyistFeeQuestion, payerOptions, "", true, false)
            @Html.SosValidationMessageFor(SharedLocalizer, m => m.Payer)
        </div>

        <a asp-action="UnderConstruction"
           asp-controller="Home"
           class="btn btn-flat-primary btn-sm mt-3 mb-5">
            <i class="fas fa-file-pdf mr-2"></i> @Localizer["FilerPortal.Filing.Verification.PreviewPDF"]
        </a>

        <partial name="_ButtonBar" model="buttonConfig" />
    }
</div>

<partial name="_CancelConfirmModal" model="cancelModal" />
<style>
    .form-container {
        width: 90%;
        margin: auto;
        padding: 20px 0px;
    }
</style>
