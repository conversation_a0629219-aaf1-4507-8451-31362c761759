// <copyright file="UpdateTransactionCommandTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>


using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Transactions;

/// <summary>
/// Tests for the <see cref="UpdateTransactionCommand"/> and its variants.
/// </summary>
[TestFixture]
[TestOf(typeof(UpdateTransactionCommand))]
[FixtureLifeCycle(LifeCycle.SingleInstance)]
[Parallelizable(ParallelScope.All)]
public sealed class UpdateTransactionCommandTests
{
    private static readonly ITransactionLike UpdateData =
        TransactionLike.Dummy with { Notes = "Updated" };

    private static readonly UpdateExpenditureCommand ForExpenditure =
        new(default, UpdateData, "Testing");

    private static readonly Expenditure Expenditure = new()
    {
        Amount = (Currency)100.0m,
        Purpose = "Testing",
        TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        Notes = "Not updated",
    };

    private static readonly UpdateInKindContributionCommand ForInKind =
        new(default, UpdateData);

    private static readonly InKindContribution InKindContribution = new()
    {
        Amount = (Currency)100.0m,
        TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        Notes = "Not updated",
    };

    private static readonly UpdateMonetaryContributionCommand ForMonetary =
        new(default, UpdateData, MonetaryType.Cash);

    private static readonly MonetaryContribution MonetaryContribution = new(MonetaryType.Cash)
    {
        Amount = (Currency)100.0m,
        TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        Notes = "Not updated",
    };

    private static readonly UnexpectedTransaction Unexpected = new();

    private static readonly FilerContact Contact = new OrganizationContact
    {
        // Common = SeedData.DummyContact,
        OrganizationName = "ACME, Inc.",
    };

    private static readonly UpdateTransactionCommand[] Commands =
        [ForExpenditure, ForInKind, ForMonetary];

    private static readonly (UpdateTransactionCommand Command, Transaction Transaction)[] CommandsWithInputs =
    [
        (ForExpenditure, Expenditure),
        (ForInKind, InKindContribution),
        (ForMonetary, MonetaryContribution),
    ];

    /// <summary>
    /// Asserts that we are covering all known concrete implementations of commands.
    /// </summary>
    [Test]
    public void AllConcreteCommandClasses_AreUnderTest()
    {
        var commands = typeof(UpdateTransactionCommand).Assembly.GetTypes()
            .Where(t => !t.IsAbstract)
            .Where(t => t.IsAssignableTo(typeof(UpdateTransactionCommand)))
            .ToList();

        var commandsTestedForFailure = Commands.Select(c => c.GetType());
        var commandsTestedForSuccess = CommandsWithInputs.Select(t => t.Command.GetType());

        Assert.Multiple(() =>
        {
            Assert.That(commandsTestedForFailure, Is.EquivalentTo(commandsTestedForSuccess));
            Assert.That(commands, Is.EquivalentTo(commandsTestedForFailure));
        });
    }

    /// <summary>
    /// Asserts that update command variants fail when the passed transaction is not of the
    /// appropriate type.
    /// </summary>
    /// <param name="command">The command variant under test.</param>
    [TestCaseSource(nameof(Commands))]
    public void UpdateVariants_ShouldFail_OnMismatchedTransactionType(UpdateTransactionCommand command)
    {
        var result = command.Apply(Unexpected, Contact);

        Assert.That(result, Is.AssignableTo<Failure<Transaction>.InvalidState>());
    }

    /// <summary>
    /// Asserts that update command variants correctly set the data on their target transaction
    /// type.
    /// </summary>
    /// <param name="testCase">The command variant under test with its input.</param>
    [TestCaseSource(nameof(CommandsWithInputs))]
    public void UpdateVariants_ShouldSucceedAndApplyUpdate_ForCompatibleTransactions(
        (UpdateTransactionCommand Command, Transaction Transaction) testCase)
    {
        var (command, transaction) = testCase;

        var result = command.Apply(transaction, Contact);

        Assert.That(result, Is.AssignableTo<Success<Transaction>>());

        if (result.Unwrap(out var success, out _))
        {
            Assert.Fail("Success results should not fail to unwrap");
        }

        Assert.That(success, Has.Property(nameof(Transaction.Notes)).EqualTo(UpdateData.Notes));
    }
}
