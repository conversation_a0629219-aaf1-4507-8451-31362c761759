using Microsoft.AspNetCore.Authorization;
using Moq;
using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerRegistration;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.FileSystem;
using SOS.CalAccess.Services.WebApi.Registrations;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.WebApi.Tests.Registrations;
public class LobbyistControllerTests
{
    //dependancies
    private IAuthorizationService _authorizationMock;
    private IRegistrationRegistrationContactRepository _registrationRegistrationContactRepositoryMock;
    private IRegistrationRepository _registrationRepositoryMock;
    private IDecisionsSvc _decisionsSvcMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private IFilerLinkRepository _filerLinkRepositoryMock;
    private IReferenceDataSvc _referenceDataSvcMock;
    private ILinkageSvc _linkageSvcMock;
    private IAttestationRepository _attestationRepositoryMock;
    private IUploadFileSvc _uploadFileSvcMock;
    private IDateTimeSvc _dateTimeSvc;
    private DateTime _dateNow;

    private Mock<IAuthorizationService> _authorizationServiceMock;
    private Mock<ILobbyistRegistrationSvc> _lobbyistRegistrationSvcMock;
    private LobbyistController _controller;
    private LobbyistRegistrationSvcDependencies _dependencies;
    private ModelMapper<LobbyistRegistrationRequestDto, Lobbyist> _modelMapperMock;

    [SetUp]
    public void Setup()
    {
        _authorizationMock = Substitute.For<IAuthorizationService>();
        _referenceDataSvcMock = Substitute.For<IReferenceDataSvc>();
        _authorizationServiceMock = new Mock<IAuthorizationService>();
        _lobbyistRegistrationSvcMock = new Mock<ILobbyistRegistrationSvc>();
        _controller = new LobbyistController(_authorizationServiceMock.Object, _lobbyistRegistrationSvcMock.Object);

        //dependencies
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _registrationRegistrationContactRepositoryMock = Substitute.For<IRegistrationRegistrationContactRepository>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _modelMapperMock = Substitute.For<ModelMapper<LobbyistRegistrationRequestDto, Lobbyist>>();
        _filerLinkRepositoryMock = Substitute.For<IFilerLinkRepository>();
        _linkageSvcMock = Substitute.For<ILinkageSvc>();
        _attestationRepositoryMock = Substitute.For<IAttestationRepository>();
        _uploadFileSvcMock = Substitute.For<IUploadFileSvc>();
        _dependencies = new LobbyistRegistrationSvcDependencies
        (
            _decisionsSvcMock,
            _authorizationSvcMock,
            _filerSvcMock,
            _notificationSvcMock,
            _userMaintenanceSvcMock,
            _registrationRepositoryMock,
            _registrationRegistrationContactRepositoryMock,
            _filerLinkRepositoryMock,
            _linkageSvcMock,
            _attestationRepositoryMock,
            _uploadFileSvcMock
        );
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    [Test]
    public async Task GetLobbyistByFilerId_ReturnsLobbyistResponseDto()
    {
        // Arrange
        var id = 1L;
        var expectedResponse = new LobbyistResponseDto();
        _lobbyistRegistrationSvcMock.Setup(svc => svc.GetLobbyistByFilerId(id)).ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.GetLobbyistByFilerId(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(expectedResponse));
    }

    [Test]
    public async Task GetLobbyistByFilerId_ReturnsNull_WhenLobbyistNotFound()
    {
        // Arrange
        var id = 1L;
        _lobbyistRegistrationSvcMock.Setup(svc => svc.GetLobbyistByFilerId(id)).ReturnsAsync((LobbyistResponseDto?)null);

        // Act
        var result = await _controller.GetLobbyistByFilerId(id);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task SearchLobbyistEmployerOrLobbyingFirmByIdOrName_DoesNotExist_ReturnException()
    {
        {
            // Arrange
            _ = _registrationRepositoryMock.FindLobbyistEmployerOrLobbyingFirmByIdOrName(Arg.Any<string>()).ReturnsForAnyArgs([]);

            var lobbyistRegistrationSvc = new LobbyistRegistrationSvc(_dependencies, new RegistrationModelMapper(_referenceDataSvcMock), _dateTimeSvc);
            var controller = new LobbyistController(_authorizationServiceMock.Object, lobbyistRegistrationSvc);

            // Act
            var result = await controller.SearchLobbyistEmployerOrLobbyingFirmByIdOrName("NotExist");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(0));
        }
    }

    [Test]
    public async Task SearchLobbyistEmployerOrLobbyingFirmByIdOrName_Found_ReturnList()
    {
        {
            // Arrange
            List<Filer> list = new()
            {
                new()
                {
                    Id = 1,
                    FilerStatusId = FilerStatus.Active.Id,
                    CurrentRegistration = new LobbyistEmployer()
                    {
                        StatusId = RegistrationStatus.Accepted.Id,
                        Name = "Test Lobbyist Employer Alpha",
                        Email = "<EMAIL>",
                        EmployerName = "Employer Name",
                        EmployerType = "Employer Type",
                        BusinessActivity = "Activity",
                        BusinessDescription = "Description",
                        InterestType = "Interest Type",
                        NumberOfMembers = 10
                    },
                },
                new()
                {
                    Id = 2,
                    FilerStatusId = FilerStatus.Active.Id,
                    CurrentRegistration = new LobbyingFirm()
                    {
                        StatusId = RegistrationStatus.Accepted.Id,
                        Name = "Test Lobbying Firm Alpha",
                        Email = "<EMAIL>",
                        LegislativeSessionId = 1,
                        ResponsibleOfficerTitle = "Officer Title"
                    },
                },
            };
            _ = _registrationRepositoryMock.FindLobbyistEmployerOrLobbyingFirmByIdOrName(Arg.Any<string>()).ReturnsForAnyArgs(list);

            var lobbyistRegistrationSvc = new LobbyistRegistrationSvc(_dependencies, new RegistrationModelMapper(_referenceDataSvcMock), _dateTimeSvc);
            var controller = new LobbyistController(_authorizationServiceMock.Object, lobbyistRegistrationSvc);
            // Act
            var result = await controller.SearchLobbyistEmployerOrLobbyingFirmByIdOrName("Test");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(list.Count));
        }
    }

    [Test]
    public async Task CreateRegistrationPage03_Create_NoErrors()
    {
        {

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            LobbyistRegistrationSvcDependencies dependencies = new
            (
                _decisionsSvcMock,
                _authorizationSvcMock,
                _filerSvcMock,
                _notificationSvcMock,
                _userMaintenanceSvcMock,
                _registrationRepositoryMock,
               _registrationRegistrationContactRepositoryMock,
                _filerLinkRepositoryMock,
                _linkageSvcMock,
                _attestationRepositoryMock,
                _uploadFileSvcMock
            );

            var lobbyistRegistrationSvc = new LobbyistRegistrationSvc(dependencies, new RegistrationModelMapper(_referenceDataSvcMock), _dateTimeSvc);

            LobbyistRegistrationRequestDto request = new()
            {
                SelfRegister = true,
                FirstName = "FirstName",
                MiddleName = "MiddleName",
                LastName = "LastName",
                PhoneNumber = new PhoneNumberDto(),
                FaxNumber = new PhoneNumberDto(),
                Email = "<EMAIL>",
                FilerId = 1,
                LobbyistEmployerOrLobbyingFirmId = 1,
                BusinessAddress = new()
                {
                    Type = "Business",
                    Purpose = "Sample purpose",
                    Country = "United States",
                    Street = "1585 KAPIOLANI BLVD STE 1800",
                    Street2 = null,
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96814",
                },
                MailingAddress = new()
                {
                    Type = "Mailing",
                    Purpose = "Sample purpose",
                    Country = "United States",
                    Street = "711 KAPIOLANI BLVD STE 1800",
                    Street2 = null,
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96814",
                },
                LegislativeSessionId = 2,
                DateOfQualification = _dateNow,
                IsPlacementAgent = true,
                CompletedEthicsCourse = true,
                IsNewCertification = null,
                CompletedCourseDate = _dateNow,
                Agencies = new()
                {
                    new() { AgencyId = 1, AgencyName = "dummy agency", RegistrationId = 1 },
                    new() { AgencyId = 2, AgencyName = "dummy agency 2", RegistrationId = 1 }
                },
                IsLobbyingStateLegislature = true,
                Photo = "Testing photo",
                DiligenceVerificationStatement = null,
            };

            var controller = new LobbyistController(_authorizationMock, lobbyistRegistrationSvc);

            _ = _registrationRepositoryMock
                .IsLobbyistNameUnique(Arg.Any<string>()).Returns(true);

            _ = _decisionsSvcMock
                .InitiateWorkflow<DecisionsLobbyistRegistrationGeneralInfo, List<WorkFlowError>>(Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsLobbyistRegistrationGeneralInfo>(), Arg.Any<bool>())
                .Returns(new List<WorkFlowError>());

            // Act
            var result = await controller.CreateLobbyistRegistrationPage03(request);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public void CancelLobbyistRegistration_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;

        _ = _lobbyistRegistrationSvcMock.Setup(svc => svc.CancelLobbyistRegistration(id)).Returns(Task.CompletedTask);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _controller.CancelLobbyistRegistration(id));
    }

    [Test]
    public async Task CancelLobbyistRegistration_CallsServiceWithCorrectId()
    {
        // Arrange
        var id = 123L;

        // Act
        await _controller.CancelLobbyistRegistration(id);

        // Assert
        _lobbyistRegistrationSvcMock.Verify(svc => svc.CancelLobbyistRegistration(id), Times.Once);
    }

    [Test]
    public async Task SearchLobbyistEmployerOrLobbyingFirmByIdOrName_ReturnsExpectedResults()
    {
        // Arrange
        var query = "Alpha";
        var expectedResults = new List<LobbyistEmployerOrLobbyistFirmSearchResultDto>
    {
        new() {
            Id = 1,
            Name = "Test Lobbyist Employer Alpha"
        },
        new() {
            Id = 2,
            Name = "Test Lobbying Firm Alpha"
        }
    };

        _lobbyistRegistrationSvcMock
            .Setup(svc => svc.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(query))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.SearchLobbyistEmployerOrLobbyingFirmByIdOrName(query);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(expectedResults.Count));
            Assert.That(result.Select(r => r.Id), Is.EquivalentTo(expectedResults.Select(e => e.Id)));
            Assert.That(result.Select(r => r.Name), Is.EquivalentTo(expectedResults.Select(e => e.Name)));
        });
    }

    [Test]
    public async Task CreateLobbyistAmendmentRegistrationAsync_ShouldReturnLobbyistResponseDto()
    {
        // Arrange
        var id = 123L;

        var lobbyist = new Lobbyist
        {
            Id = id,
            Name = "Test Lobbyist",
            StatusId = RegistrationStatus.Accepted.Id,
            Version = 1,
            AddressList = new AddressList
            {
                Id = 1,
                Addresses = new List<Address>
            {
                new()
                {
                    Id = 1,
                    City = "Honolulu",
                    State = "HI",
                    Country = "USA",
                    Zip = "96814",
                    Street = "123 Example St",
                    Street2 = null,
                    Purpose = "Business",
                    Type = "Work"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                Id = 1,
                PhoneNumbers = new List<PhoneNumber>
            {
                new()
                {
                    Id = 1,
                    CountryCode = "+1",
                    CountryId = 1,
                    Number = "8081234567",
                    Type = "Work"
                }
            }
            }
        };

        _registrationRepositoryMock.FindLobbyistById(Arg.Any<long>())
            .Returns(lobbyist);

        _authorizationSvcMock.GetInitiatingUserId()
            .Returns(1L);

        var svc = new LobbyistRegistrationSvc(_dependencies, new RegistrationModelMapper(_referenceDataSvcMock), _dateTimeSvc);
        var controller = new LobbyistController(_authorizationServiceMock.Object, svc);

        // Act
        var result = await controller.CreateLobbyistAmendmentRegistrationAsync(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<LobbyistResponseDto>());
        Assert.That(result.Name, Is.EqualTo("Test Lobbyist"));
    }

    [Test]
    public async Task WithdrawLobbyistRegistration_ShouldReturnValue()
    {
        // Arrange
        var id = 123;
        var withdrawnAt = DateTime.UtcNow;
        var payload = new WithdrawLobbyistRegistrationRequestDto()
        {
            StatusId = 1,
            WithdrawnAt = withdrawnAt,
        };
        var data = new RegistrationResponseDto()
        {
            Id = id
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.WithdrawLobbyistRegistration(It.IsAny<long>(), It.IsAny<WithdrawLobbyistRegistrationRequestDto>()))
                                    .Returns(Task.FromResult(data));

        // Act
        var result = await _controller.WithdrawLobbyistRegistration(id, payload);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result?.Id, Is.EqualTo(id));
    }

    [Test]
    public async Task GetRegistrationById_ShouldReturnValue()
    {
        // Arrange
        var id = 123;
        var withdrawnAt = DateTime.UtcNow;
        var data = new LobbyistResponseDto()
        {
            Id = id,
            WithdrawnAt = withdrawnAt
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.GetRegistrationById(It.IsAny<long>()))
                                    .Returns(Task.FromResult<LobbyistResponseDto?>(data));

        // Act
        var result = await _controller.GetRegistrationById(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.Id, Is.EqualTo(id));
            Assert.That(result?.WithdrawnAt, Is.EqualTo(withdrawnAt));
        });
    }
    [Test]
    public async Task SendLobbyistRegistrationTermination_ShouldReturnValue()
    {
        // Arrange
        var id = 123;
        var date = DateTime.UtcNow;
        var payload = new LobbyistRegistrationTerminationRequestDto()
        {
            StatusId = 1,
            TerminatedAt = date,
        };
        var data = new RegistrationResponseDto()
        {
            Id = id
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.SendLobbyistRegistrationTermination(It.IsAny<long>(), It.IsAny<LobbyistRegistrationTerminationRequestDto>()))
                                    .Returns(Task.FromResult(data));

        // Act
        var result = await _controller.SendLobbyistRegistrationTermination(id, payload);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result?.Id, Is.EqualTo(id));
    }
    [Test]
    public async Task SaveLobbyistRegistrationTermination_ShouldReturnValue()
    {
        // Arrange
        var id = 123;
        var date = DateTime.UtcNow;
        var payload = new LobbyistRegistrationTerminationRequestDto()
        {
            StatusId = 3,
            TerminatedAt = date,
        };
        var data = new RegistrationResponseDto()
        {
            Id = id
        };

        _lobbyistRegistrationSvcMock.Setup(c => c.SaveLobbyistRegistrationTermination(It.IsAny<long>(), It.IsAny<LobbyistRegistrationTerminationRequestDto>()))
                                    .Returns(Task.FromResult(data));

        // Act
        var result = await _controller.SaveLobbyistRegistrationTermination(id, payload);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result?.Id, Is.EqualTo(id));
    }

    [Test]
    public async Task SendLobbyistRegistrationWithdrawal_ReturnsExpectedResponse()
    {
        var id = 123L;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = 1,
            WithdrawnAt = DateTime.UtcNow
        };
        var expected = new RegistrationResponseDto { Id = id };

        _lobbyistRegistrationSvcMock
            .Setup(svc => svc.SendLobbyistRegistrationWithdrawal(id, request))
            .ReturnsAsync(expected);

        var result = await _controller.SendLobbyistRegistrationWithdrawal(id, request);

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(id));
    }

    [Test]
    public async Task SendLobbyistRegistrationWithdrawal_ReturnsNull_WhenServiceReturnsNull()
    {
        var id = 456L;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = 2,
            WithdrawnAt = DateTime.UtcNow
        };

        _lobbyistRegistrationSvcMock
            .Setup(svc => svc.SendLobbyistRegistrationWithdrawal(id, request))!
            .ReturnsAsync((RegistrationResponseDto?)null);

        var result = await _controller.SendLobbyistRegistrationWithdrawal(id, request);

        Assert.That(result, Is.Null);
    }

    [Test]
    public void SendLobbyistRegistrationWithdrawal_ThrowsException_WhenServiceThrows()
    {
        var id = 789L;
        var request = new WithdrawLobbyistRegistrationRequestDto
        {
            StatusId = 3,
            WithdrawnAt = DateTime.UtcNow
        };

        _lobbyistRegistrationSvcMock
            .Setup(svc => svc.SendLobbyistRegistrationWithdrawal(id, request))
            .ThrowsAsync(new InvalidOperationException("Service error"));

        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _controller.SendLobbyistRegistrationWithdrawal(id, request));

        Assert.That(ex!.Message, Is.EqualTo("Service error"));
    }
}
