using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;

namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Controller for payments made to lobbying firms transaction-related actions.
/// </summary>
public class FirmPaymentTransactionController : Controller
{
    private const string SelectContactViewName = "~/Views/Contact/SelectLobbyingFirm.cshtml";
    private const string EnterContactViewName = "~/Views/Contact/LobbyingFirmForm.cshtml";
    private const string EnterAmountViewName = "EnterAmount";

    private const string ContinueAction = "Continue";
    private const string PreviousAction = "Previous";
    private const string SaveAction = "Save";

    private readonly IToastService _toastService;
    private readonly IStringLocalizer<SharedResources> _localizer;
    private readonly IFirmPaymentTransactionCtlSvc _firmPaymentTransactionCtlSvc;
    private readonly IFilingsApi _filingsApi;

    public FirmPaymentTransactionController(
        IToastService toastService,
        IStringLocalizer<SharedResources> localizer,
        IFirmPaymentTransactionCtlSvc firmPaymentTransactionCtlSvc,
        IFilingsApi filingsApi)
    {
        _toastService = toastService;
        _localizer = localizer;
        _firmPaymentTransactionCtlSvc = firmPaymentTransactionCtlSvc;
        _filingsApi = filingsApi;
    }

    #region Step 01 - Select Lobbying Firm Contact

    /// <summary>
    /// Loader for 01_SelectContact view.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> SelectContact(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long? contactId,
        [FromQuery] long? registrationFilingId,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        var model = await _firmPaymentTransactionCtlSvc.Get01ViewModel(reportType, filingId, filerId, contactId, registrationFilingId, cancellationToken);
        return View(SelectContactViewName, model);
    }

    /// <summary>
    /// Handles redirects based on action selected in step 1.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> SelectContact(FirmPaymentTransactionViewModel model, string action, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        return action == ContinueAction
            ? RedirectToAction(nameof(EnterContact),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId,
                    registrationFilingId = model.RegistrationFilingId
                })
            : await HandleCancelTransaction(model);
    }

    /// <summary>
    /// Search lobbying firm for select component.
    /// </summary>
    /// <param name="search"></param>
    /// <param name="filerId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<JsonResult> SearchLobbyingFirmsByIdOrName(
        [FromQuery] string search,
        [FromQuery] long filerId,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        return await _firmPaymentTransactionCtlSvc.SearchLobbyingFirmsByIdOrName(search, filerId, cancellationToken);
    }
    #endregion

    #region Step 02 - Enter Lobbying Firm Contact

    /// <summary>
    /// Loader for 02_EnterContact view.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="contactId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> EnterContact(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long? contactId,
        [FromQuery] long? registrationFilingId,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        var model = await _firmPaymentTransactionCtlSvc.GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, cancellationToken);
        return View(EnterContactViewName, model);
    }

    /// <summary>
    /// Handles redirects or save contact operation based on action selected in step 2.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> EnterContact(FirmPaymentTransactionViewModel model, string action, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        return action switch
        {
            PreviousAction => RedirectToAction(nameof(SelectContact),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId,
                    registrationFilingId = model.RegistrationFilingId
                }),
            ContinueAction => await SaveContactAsync(model),
            _ => await HandleCancelTransaction(model)
        };
    }

    /// <summary>
    /// Save contact async operation.
    /// </summary>
    /// <param name="model">The transaction view model containing contact information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>IActionResult for the next view or error handling</returns>
    private async Task<IActionResult> SaveContactAsync(FirmPaymentTransactionViewModel model)
    {
        if (model.FilingId == null || model.FilerId == null)
        {
            return HandleInvalidModelState();
        }

        // Skip save operation if RegistrationFilingId is provided
        if (model.RegistrationFilingId != null)
        {
            return RedirectToEnterAmount(model);
        }

        await _firmPaymentTransactionCtlSvc.SaveFilerContact(model, ModelState);

        if (ModelState.IsValid)
        {
            return RedirectToEnterAmount(model);
        }

        return View(EnterContactViewName, model);
    }

    /// <summary>
    /// Redirects to the Enter Amount step with required parameters
    /// </summary>
    /// <param name="model">The transaction view model</param>
    /// <returns>RedirectToActionResult to the next step</returns>
    private RedirectToActionResult RedirectToEnterAmount(FirmPaymentTransactionViewModel model)
    {
        return RedirectToAction(nameof(EnterAmount),
            new
            {
                reportType = model.ReportType,
                filingId = model.FilingId,
                filerId = model.FilerId,
                contactId = model.ContactId,
                registrationFilingId = model.RegistrationFilingId
            });
    }

    #endregion

    #region Step 03 - Enter Payment Amounts to Lobbying Firms

    /// <summary>
    /// Loader for 03_EnterAmount view.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="contactId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> EnterAmount(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long? contactId,
        [FromQuery] long? registrationFilingId,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        var model = await _firmPaymentTransactionCtlSvc.GetViewModel(reportType, filingId, filerId, contactId, registrationFilingId, cancellationToken);
        return View(EnterAmountViewName, model);
    }

    /// <summary>
    /// Handles redirects or save transaction operation based on action selected in step 3.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> EnterAmount(FirmPaymentTransactionViewModel model, string action, CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidModelState();
        }

        return action switch
        {
            PreviousAction => RedirectToAction(nameof(EnterContact),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    contactId = model.ContactId,
                    registrationFilingId = model.RegistrationFilingId
                }),
            SaveAction => await SaveTransactionAsync(model),
            _ => await HandleCancelTransaction(model)
        };
    }

    /// <summary>
    /// Handles save payment transaction async operation.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<IActionResult> SaveTransactionAsync(FirmPaymentTransactionViewModel model)
    {
        if (model.FilingId == null || model.FilerId == null)
        {
            return HandleInvalidModelState();
        }

        _ = await _firmPaymentTransactionCtlSvc.SaveTransaction(model, ModelState);

        if (ModelState.IsValid)
        {
            _toastService.Success(_localizer[ResourceConstants.CreateTransactionSuccessMessage]);
            return await RedirectToTransactionSummary(model);
        }

        model.Contact = await _firmPaymentTransactionCtlSvc.GetContactViewModel(model.ContactId, model.RegistrationFilingId);
        return View(EnterAmountViewName, model);
    }
    #endregion

    #region Helper Methods

    /// <summary>
    /// Handles invalid model state by returning a NotFound result.
    /// </summary>
    /// <returns></returns>
    private NotFoundResult HandleInvalidModelState() => NotFound();

    /// <summary>
    /// Handles the cancel transaction action by displaying a success message and redirecting to the transaction summary.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<RedirectToActionResult> HandleCancelTransaction(FirmPaymentTransactionViewModel model)
    {
        _toastService.Success(_localizer[ResourceConstants.CancelTransactionSuccessMessage]);
        return await RedirectToTransactionSummary(model);
    }

    /// <summary>
    /// Redirects to the transaction summary page.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private async Task<RedirectToActionResult> RedirectToTransactionSummary(FirmPaymentTransactionViewModel model)
    {
        var isAmendment = await GetIsAmendment((long)model.FilingId!);
        var controller = isAmendment ? "AmendDisclosure" : "Disclosure";

        return RedirectToAction("Index", controller, new
        {
            viewName = FilingSummaryTypeModel.MadeToLobbyingFirmsSummary.Name,
            reportType = model.ReportType,
            filingId = model.FilingId,
            filerId = model.FilerId
        });
    }

    private async Task<bool> GetIsAmendment(long filingId)
    {
        var filing = await _filingsApi.GetFiling(filingId);
        return filing?.Version > 0;
    }
    #endregion
}
