using Microsoft.EntityFrameworkCore;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.SeedData;
using SOS.CalAccess.Data.SeedData;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.SeedData;

[TestFixture]
public class SeedRegistrationRepositoryTest
{
    private DatabaseContext _dbContext;
    private ISeedCommonRepository _seedCommonRepository;
    private SeedRegistrationRepository _repository;
    private const long UserId = 1;

    [SetUp]
    public void Setup()
    {
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        _dbContext = new DatabaseContext(options);
        _seedCommonRepository = Substitute.For<ISeedCommonRepository>();
        _repository = new SeedRegistrationRepository(_dbContext, _seedCommonRepository);

        SeedDatabase();
    }

    [TearDown]
    public void TearDown()
    {
        _dbContext?.Dispose();
    }

    private void SeedDatabase()
    {
        _dbContext.RegistrationStatuses.Add(RegistrationStatus.Accepted);
        _dbContext.FilerStatuses.Add(new FilerStatus { Id = 100, Name = FilerStatus.Active.Name });
        _dbContext.FilingTypes.Add(FilingType.AdHocFiling);
        _dbContext.FilingStatuses.Add(FilingStatus.Submitted);

        _dbContext.Filertypes.AddRange(
            FilerType.Candidate,
            FilerType.BusinessEntity,
            FilerType.Lobbyist,
            FilerType.LobbyingFirm,
            FilerType.LobbyistEmployer,
            FilerType.RecipientCommittee
        );

        _dbContext.SaveChanges();
    }

    [Test]
    public async Task CreateTestCandidateIntentionStatement_Works()
    {
        // Act
        var result = await _repository.CreateTestCandidateIntentionStatement(UserId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Test Candidate"));
        });
    }

    [Test]
    public async Task CreateTestLobbyistEmployerRegistration_Works()
    {
        // Act
        var result = await _repository.CreateTestLobbyistEmployerRegistration(UserId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Test Lobbyist Employer"));
        });
    }

    [Test]
    public async Task CreateTestLobbyistRegistration_Works()
    {
        // Act
        var result = await _repository.CreateTestLobbyistRegistration(UserId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Test Lobbyist"));
        });
    }

    [Test]
    public void CreateFilerForRegistration_ThrowsIfRegistrationNotFound()
    {
        // Act & Assert
        Assert.ThrowsAsync<ArgumentException>(() => _repository.CreateFilerForRegistration(UserId, 999));
    }

    [Test]
    public async Task CreateTestFiling_CreatesNewFiling()
    {
        // Arrange
        var filingPeriod = new FilingPeriod
        {
            Id = 99,
            StartDate = SeedValues.StartOfPeriod,
            EndDate = SeedValues.EndOfPeriod
        };
        var filer = new Filer
        {
            Id = 888,
            CreatedBy = UserId,
            FilerStatusId = 100,
            FilerTypeId = 2,
            CurrentRegistrationId = 123
        };
        _dbContext.FilingPeriods.Add(filingPeriod);
        _dbContext.Filers.Add(filer);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.CreateTestFiling(UserId, filer.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FilerId, Is.EqualTo(filer.Id));
        });
    }

    [Test]
    public async Task CreateTestPrimarilyFormedCommittee_Works()
    {
        // Act
        var result = await _repository.CreateTestPrimarilyFormedCommittee(UserId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Test Primarily Formed Committee"));
        });
    }

    [Test]
    public async Task CreateTestCandidateControlledCommittee_Works()
    {
        var election = new Models.FilerRegistration.Elections.Election
        {
            ElectionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            Name = "name",
            Id = 1
        };
        _seedCommonRepository.EnsureTestElection().Returns(election);
        // Act
        var result = await _repository.CreateTestCandidateControlledCommittee(UserId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Name, Is.EqualTo("Test Candidate Controlled Committee"));
        });
    }

}
