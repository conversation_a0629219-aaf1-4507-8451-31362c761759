using System.Diagnostics;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Refit;
using SOS.CalAccess.FilerPortal.Models;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.FilerPortal.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IConfiguration _configuration;
    private readonly IDateTimeSvc _dateTimeSvc;

    public HomeController(
        ILogger<HomeController> logger,
        IConfiguration? configuration,
        IDateTimeSvc dateTimeSvc)
    {
        _logger = logger;
        _configuration = configuration!;
        _dateTimeSvc = dateTimeSvc;
    }

    [AllowAnonymous]
    public IActionResult Index()
    {
        var username = User.Identity?.IsAuthenticated == true ? User.Identity.Name : "Guest";
        var messageCount = 0;

        ViewData["Username"] = username;
        ViewData["MessageCount"] = messageCount;

        ViewData["Environment"] = _configuration["Application:Environment"];
        ViewData["Version"] = _configuration["Application:Version"];

        return View();
    }

    [AllowAnonymous]
    public IActionResult ReferencePage()
    {
        var model = new ReferenceViewModel()
        {
            RegistrationDate = DateTime.Today,
            RegistrationName = "TestRegistrationName",
            NotificationTypeList = new List<SelectListItem> {
                new() { Text = "info", Value = "1", Selected = true },
                new() { Text = "success", Value = "2" },
                new() { Text = "Warning", Value = "3" },
                new() { Text = "error", Value = "4" }
                },
            NotificationType = "1",
            ReceiveSms = true,
            FilerName = "Test Filer Name",
            DescriptiveMessage = "Descrption",
            NotificationPreference = "Phone",
            PhoneNumber1 = new() { CountryCode = "+1", Extension = "1234", Number = "5551231234", SelectedCountry = 2 },
            PhoneNumber2 = new() { CountryCode = "+1", Extension = "", Number = "5559879876" },
            PhoneNumber3 = new() { Extension = "1234", Number = "5551231234" },
            ExpenditureExpenseAmount = 10000
        };
        //await _authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.ReferencePage_View, User));
        return View(model);
    }

    [AllowAnonymous]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        var exceptionHandlerPathFeature =
            HttpContext.Features.Get<IExceptionHandlerPathFeature>();
        var exception = exceptionHandlerPathFeature?.Error;

        if (exception is ApiException ae && ae.StatusCode == HttpStatusCode.Unauthorized)
        {
            _logger.LogError(1, exception, "Failed Authorization error handler");
        }

        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }

    [AllowAnonymous]
    public IActionResult ChangeLanguage(string lang)
    {
        if (!string.IsNullOrEmpty(lang))
        {
            Response.Cookies.Append(
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(lang)),
                new CookieOptions { Expires = DateTime.UtcNow.AddYears(1), Secure = true, HttpOnly = true }
            );
        }
        return Redirect(Request.Headers.Referer.ToString());
    }

    public IActionResult UnderConstruction()
    {
        return View("/Views/Shared/_UnderConstruction.cshtml");
    }
}
