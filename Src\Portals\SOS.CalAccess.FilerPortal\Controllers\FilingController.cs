// <copyright file="FilingController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Filings;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Extensions;
using SOS.CalAccess.UI.Common.Services;
using AlertType = SOS.CalAccess.FilerPortal.Extensions.AlertType;
using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
using IPortalAlertsContainer = SOS.CalAccess.UI.Common.Extensions.IPortalAlertsContainer;
using ValidationMessage = SOS.CalAccess.UI.Common.Extensions.ValidationMessage;

namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Controller for handling filing-related actions.
/// </summary>
public class FilingController : Controller
{
    /// <summary>
    /// The name of the view for creating or editing a filing.
    /// </summary>
    public const string FilingFormView = "FilingForm";

    private const string AlertError = "There was an error with the form. Please correct the errors and try again.";
    private const string Index = "Index";
    private const string Disclosure = "Disclosure";
    private const string Filing = "Filing";
    private const string AmendDisclosure = "AmendDisclosure";
    private const string TemporaryDashboard = "TemporaryDashboard";
    private const string SubmittedView = "Submitted";
    private const string VerificationView = "Verification";
    private const string ApiRequestErrorKey = "Common.APIRequestError";
    private const string SendForAttestationView = "SendForAttestation";
    private const string SubmitReportSuccessMessage = "FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage";
    private const string SubmitReportErrorMessage = "FilerPortal.Disclosure.Dashboard.SubmitReportErrorMessage";

    private readonly ILogger<FilingController> _logger;
    private readonly IToastService _toastService;
    private readonly IStringLocalizer<SharedResources> _localizer;
    private readonly IFilingSvc _filingSvc;
    private readonly IDateTimeSvc _dateTimeSvc;
    private readonly IUsersApi _usersApi;

    public FilingController(IToastService toastService, IStringLocalizer<SharedResources> localizer, ILogger<FilingController> logger, IFilingSvc filingSvc, IUsersApi usersApi, IDateTimeSvc dateTimeSvc)
    {
        _toastService = toastService;
        _localizer = localizer;
        _logger = logger;
        _filingSvc = filingSvc;
        _usersApi = usersApi;
        _dateTimeSvc = dateTimeSvc;
    }
    /// <summary>
    /// Amends an existing filing.
    /// </summary>
    /// <param name="id">The id of the filing to amend.</param>
    /// <param name="api">The API client for interacting with the filings API.</param>
    /// <returns>A redirect to the edit page for the amended filing.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<ActionResult> Amend(
        [Required] long? id,
        [FromServices] IFilingsApi api)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filing = await api.CreateFilingAmendment(id!.Value);

        this.SetGlobalAlert(AlertType.Success, "Filing amended successfully.");

        return RedirectToAction("Edit", new { id = filing.Id });
    }

    /// <summary>
    /// Displays the create filing page.
    /// </summary>
    /// <param name="filerId">The filer id this filing is created for.</param>
    /// <returns>The Create view.</returns>
    public IActionResult Create(
        [Required, FromQuery] long? filerId)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return View(FilingFormView, new FilingViewModel { FilerId = filerId });
    }

    /// <summary>
    /// Creates a new filing.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="api">The API client for interacting with the filings API.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(
        FilingViewModel model,
        [FromServices] IFilingsApi api)
    {
        if (!ModelState.IsValid)
        {
            this.SetGlobalAlert(AlertType.Danger, AlertError);

            return View(FilingFormView, model);
        }

        var filing = await api.CreateFiling(model.FilerId!.Value, model.ToRequest());

        this.SetGlobalAlert(AlertType.Success, "Filing created successfully.");

        return RedirectToAction("Summary", new { filing.Id });
    }

    /// <summary>
    /// Displays the edit filing page.
    /// </summary>
    /// <param name="id">The id of the filing to edit.</param>
    /// <param name="api">The API client for interacting with the filings API.</param>
    /// <returns>The Edit view.</returns>
    public async Task<IActionResult> Edit(
        [Required] long? id,
        [FromServices] IFilingsApi api)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filing = await api.GetFiling(id!.Value);

        return View(FilingFormView, new FilingViewModel(filing));
    }

    /// <summary>
    /// Edits an existing filing.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="api">The API client for interacting with the filings API.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(
        FilingViewModel model,
        [FromServices] IFilingsApi api)
    {
        if (!ModelState.IsValid)
        {
            this.SetGlobalAlert(AlertType.Danger, AlertError);

            return View(FilingFormView, model);
        }

        await api.UpdateFiling(model.Id!.Value, model.ToRequest());

        this.SetGlobalAlert(AlertType.Success, "Filing updated successfully.");

        return RedirectToAction("Summary", new { model.Id });
    }

    /// <summary>
    /// Displays the committee information page for a filing.
    /// </summary>
    /// <param name="id">The id of the filing to display.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="filersApi">The API client for interacting with the filers API.</param>
    /// <returns>The CommitteeInformation view.</returns>
    public async Task<IActionResult> CommitteeInformation(
        [Required] long? id,
        [FromServices] IFilingsApi filingsApi,
        [FromServices] IFilersApi filersApi)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filing = await filingsApi.GetFiling(id!.Value);

        var registration = await filersApi.GetCurrentRegistration(filing.FilerId);

        return View(new CommitteeInformationViewModel { Filing = filing, Registration = registration });
    }

    /// <summary>
    /// Displays the contributions page for a filing.
    /// </summary>
    /// <param name="id">The id of the filing to display.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="transactionsApi">The API client for interacting with the transactions API.</param>
    /// <returns>The Contributions view.</returns>
    public async Task<IActionResult> Contributions(
        [Required] long? id,
        [FromServices] IFilingsApi filingsApi,
        [FromServices] ITransactionsApi transactionsApi)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filingTask = filingsApi.GetFiling(id!.Value);

        var inKindTask = transactionsApi.GetAllTransactionsByFiling(
            id.Value,
            page: null,
            pageSize: 100,
            type: "Contribution",
            kind: "InKind");

        var monetaryTask = transactionsApi.GetAllTransactionsByFiling(
            id.Value,
            page: null,
            pageSize: 100,
            type: "Contribution",
            kind: "Monetary");

        await Task.WhenAll(filingTask, inKindTask, monetaryTask);

        var filing = await filingTask;
        var inKind = await inKindTask;
        var monetary = await monetaryTask;

        return View(new ContributionsViewModel { Filing = filing, InKind = inKind, Monetary = monetary });
    }

    /// <summary>
    /// Displays the expenditures page for a filing.
    /// </summary>
    /// <param name="id">The id of the filing to display.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="transactionsApi">The API client for interacting with the transactions API.</param>
    /// <returns>The Expenditures view.</returns>
    public async Task<IActionResult> Expenditures(
        [Required] long? id,
        [FromServices] IFilingsApi filingsApi,
        [FromServices] ITransactionsApi transactionsApi)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filingTask = filingsApi.GetFiling(id!.Value);

        var expendituresTask = transactionsApi.GetAllTransactionsByFiling(
            id.Value,
            page: null,
            pageSize: 100,
            type: "Expenditure",
            kind: string.Empty);

        await Task.WhenAll(filingTask, expendituresTask);

        var filing = await filingTask;
        var expenditures = await expendituresTask;

        return View(new ExpendituresViewModel { Filing = filing, Expenditures = expenditures });
    }

    /// <summary>
    /// Displays the summary page for a filing.
    /// </summary>
    /// <param name="id">The target filing id.</param>
    /// <param name="filingsApi">Filings API wrapper.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>The Summary view.</returns>
    public async Task<IActionResult> Summary(
        [Required] long? id,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filing = await filingsApi.GetFiling(id!.Value, cancellationToken);

        return View(new SummaryViewModel(filing.Id, filing.FilerId, filing.Status));
    }

    /// <summary>
    /// Submits a filing for review.
    /// </summary>
    /// <param name="id">The target filing id.</param>
    /// <param name="filingsApi">Filings API wrapper.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the filer dashboard view on success.</returns>
    [HttpPost]
    public async Task<IActionResult> Submit(
        [Required] long? id,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filing = await filingsApi.SubmitFiling(id!.Value, cancellationToken);

        this.SetGlobalAlert(AlertType.Success, "Filing submitted successfully.");
        return RedirectToAction(nameof(FilerController.Index), "Filer", new { Id = filing.FilerId });
    }

    public IActionResult LobbyistEmployer(
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new LobbyistEmployerReportViewModel
        {
            IsStarted = false
        };


        return View(model);
    }

    /// <summary>
    /// Creates a new filing of type lobbyist employer coalition report.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> LobbyistEmployer(
        LobbyistEmployerReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        if (!model.IsStarted)
        {
            var userResponse = await _usersApi.GetSelf(cancellationToken);

            var filers = await filingsApi.GetFilersAssociatedUser(userResponse.Id, FilingTypeModel.LobbyistEmployerReport.Id, cancellationToken);
            var filersOption = filers.Select(f => new SelectListItem
            {
                Text = $"#{f.FilerId} - {f.FilerName}",
                Value = f.FilerId.ToString(CultureInfo.InvariantCulture)
            }).ToList();

            // Update properties instead of creating new object
            model.IsStarted = true;
            model.FilerId = filers.Count > 0 ? filers[0].FilerId : model.FilerId;
            model.Filers = filersOption;

            return View(model);
        }

        CreateLobbyistEmployerReportDto request = new(filerId: model.FilerId, filingPeriodId: model.FilingPeriodId, id: null);
        var filing = await filingsApi.CreateLobbyistEmployerReportFiling(model.FilerId, request, cancellationToken);

        return RedirectToAction(Index, Disclosure, new { filerId = model.FilerId, filingId = filing.Id, reportType = FilingTypeModel.LobbyistEmployerReport.Name });
    }

    /// <summary>
    /// Creates a new filing of type lobbyist employer report.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> AmendLobbyistEmployerReport(
        LobbyistEmployerReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            _toastService.Error(_localizer[ApiRequestErrorKey]);
            return RedirectToAction(nameof(Index), Disclosure);
        }

        CreateLobbyistEmployerReportDto request = new(model.FilerId, model.FilingPeriodId, null);
        var filing = await filingsApi.CreateLobbyistEmployerReportFiling(model.FilerId, request, cancellationToken);

        return RedirectToAction(Index, AmendDisclosure, new { filerId = model.FilerId, filingId = filing.Id, reportType = FilingTypeModel.LobbyistEmployerReport.Name });
    }

    public IActionResult Lobbyist(
    [FromServices] IRegistrationsApi registrationsApi,
    [FromServices] IFilingsApi filingsApi,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        TempData.Remove("ParentId");


        var model = new LobbyistReportViewModel()
        {
            IsStarted = false
        };


        return View(model);
    }

    /// <summary>
    /// Creates a new filing of type lobbyist report.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> Lobbyist(
        LobbyistReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        if (!model.IsStarted)
        {
            var userResponse = await _usersApi.GetSelf(cancellationToken);
            // get filers associated with the user
            var filers = await filingsApi.GetFilersAssociatedUser(userResponse.Id, FilingTypeModel.LobbyistReport.Id, cancellationToken);
            // Create a list of SelectListItem for the filers
            var filersOption = filers.Select(f => new SelectListItem
            {
                Text = $"#{f.FilerId} - {f.FilerName}",
                Value = f.FilerId.ToString(CultureInfo.InvariantCulture)
            }).ToList();

            // Update properties instead of creating new object
            model.Filers = filersOption;
            model.IsStarted = true;
            model.FilerId = filers.Count > 0 ? filers[0].FilerId : model.FilerId;

            return View(model);
        }

        CreateLobbyistReportDto request = new(filerId: model.FilerId, filingPeriodId: model.FilingPeriodId, id: null);
        var filing = await filingsApi.CreateLobbyistReportFiling(model.FilerId, request, cancellationToken);

        return RedirectToAction(Index, Disclosure, new { filerId = model.FilerId, filingId = filing.Id, reportType = FilingTypeModel.LobbyistReport.Name, filingStatus = filing.Status });
    }

    /// <summary>
    /// Creates a new filing of type lobbyist report.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> AmendLobbyistReport(
        LobbyistReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            _toastService.Error(_localizer[ApiRequestErrorKey]);
            return RedirectToAction(nameof(Index), Disclosure);
        }

        CreateLobbyistReportDto request = new(model.FilerId, model.FilingPeriodId, null);
        var filing = await filingsApi.CreateLobbyistReportFiling(model.FilerId, request, cancellationToken);

        return RedirectToAction(Index, AmendDisclosure, new { filerId = model.FilerId, filingId = filing.Id, reportType = FilingTypeModel.LobbyistReport.Name });
    }

    /// <summary>
    /// Cancel a new filing of type lobbyist employer coalition report.
    /// </summary>
    /// <param name="id">The Id of the filing to cancel.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> CancelReport(
        [FromRoute][Required] long id,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        try
        {
            await filingsApi.CancelFiling(id, cancellationToken);
            _toastService.Success(_localizer["FilerPortal.Disclosure.Dashboard.CancelReportSuccessMessage"].Value);
            return RedirectToAction(TemporaryDashboard, Filing);
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer[ApiRequestErrorKey].Value);
            _logger.LogError(ex, "Cancel filing report failed.");
            return RedirectToAction(nameof(Index), Disclosure);
        }
    }

    /// <summary>
    /// Update lobbyist employer filing status to submited if all data is valid.
    /// </summary>
    /// <param name="model">The view model containing the updated payment information.</param>
    /// <param name="filingsApi">The API service for filing operations.</param>
    /// <param name="cancellationToken">Token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
    [HttpPost]
    public async Task<IActionResult> SubmitLobbyistReport(
        string reportType,
        LobbyingReportVerificationViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        SubmitLobbyistReportDto request = new(model.DiligenceStatementVerification);
        try
        {
            var response = await filingsApi.SubmitLobbyistReport(model.Id, request, cancellationToken);
            if (response.Valid)
            {
                ViewBag.SubmittedDate = response.SubmittedDate;
                ViewBag.ReportType = reportType;
                _toastService.Success(_localizer[SubmitReportSuccessMessage]);
                return View(SubmittedView);
            }
            else
            {
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                _toastService.Error(_localizer[SubmitReportErrorMessage]);
                return View(VerificationView, model);
            }
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer[ApiRequestErrorKey]);
            _logger.LogError(ex, "Submit lobbyist report failed");
            return View(VerificationView, model);
        }
    }

    [HttpGet]
    [Route("CreateFilingAmendment")]
    public async Task<IActionResult> CreateFilingAmendment(
    string parentId,
    string reportType,
    [FromServices] IFilingsApi filingsApi,
    CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        var response = await filingsApi.CreateFilingAmendment(long.Parse(parentId, CultureInfo.InvariantCulture), cancellationToken);
        return RedirectToAction(Index, AmendDisclosure, new { filerId = response.FilerId, filingId = response.Id, reportType });
    }

    /// <summary>
    /// Update lobbyist employer filing status to submited if all data is valid.
    /// </summary>
    /// <param name="model">The view model containing the updated payment information.</param>
    /// <param name="filingsApi">The API service for filing operations.</param>
    /// <param name="cancellationToken">Token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
    [HttpPost]
    public async Task<IActionResult> SubmitLobbyistEmployerReport(
        string reportType,
        LobbyingReportVerificationViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        SubmitLobbyistEmployerReportDto request = new(model.DiligenceStatementVerification);
        try
        {
            var response = await filingsApi.SubmitLobbyistEmployerReport(model.Id, request, cancellationToken);
            if (response.Valid)
            {
                ViewBag.SubmittedDate = response.SubmittedDate;
                ViewBag.ReportType = reportType;
                _toastService.Success(_localizer[SubmitReportSuccessMessage]);
                return View(SubmittedView);
            }
            else
            {
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                ViewBag.ReportType = reportType;
                _toastService.Error(_localizer[SubmitReportErrorMessage]);
                return View(VerificationView, model);
            }
        }
        catch (Exception ex)
        {
            ViewBag.ReportType = reportType;
            _toastService.Error(_localizer[ApiRequestErrorKey]);
            _logger.LogError(ex, "Submit lobbyist employer report failed");
            return View(VerificationView, model);
        }
    }

    /// <summary>
    /// Update 48H report filing status to submited if all data is valid.
    /// </summary>
    /// <param name="model">The view model containing the updated payment information.</param>
    /// <param name="filingsApi">The API service for filing operations.</param>
    /// <param name="cancellationToken">Token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
    [HttpPost]
    public async Task<IActionResult> SubmitReport48H(
        string reportType,
        LobbyingReportVerificationViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        SubmitLobbyistEmployerReportDto request = new(model.DiligenceStatementVerification);
        try
        {
            var response = await filingsApi.SubmitReport48H(model.Id, request, cancellationToken);
            if (response.Valid)
            {
                ViewBag.SubmittedDate = response.SubmittedDate;
                ViewBag.ReportType = reportType;
                _toastService.Success(_localizer[SubmitReportSuccessMessage]);
                return View(SubmittedView);
            }
            else
            {
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                _toastService.Error(_localizer[SubmitReportErrorMessage]);
                ViewBag.ReportType = reportType;
                return View(VerificationView, model);
            }
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer[ApiRequestErrorKey]);
            _logger.LogError(ex, "Submit 48H report failed");
            ViewBag.ReportType = reportType;
            return View(VerificationView, model);
        }
    }

    [HttpGet]
    public async Task<IActionResult> Verification(
        [FromServices] IFilersApi filersApi,
        [FromQuery] long filerId,
        [FromQuery] long filingId,
        [FromQuery] string reportType,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var currentFiler = await filersApi.GetCurrentRegistration(filerId, cancellationToken);
        ViewBag.ReportType = reportType;

        // Verification and Submitted screens currently only exist for the Lobbyist report.
        // Different return values will be implemented later for different report types.
        return View("Verification", new LobbyingReportVerificationViewModel { Id = filingId, FilerName = currentFiler.Name, FilerId = filerId, });
    }

    [HttpGet]
    public async Task<IActionResult> SendForAttestation(
    [FromServices] IFilersApi filersApi,
    [FromQuery] long filerId,
    [FromQuery] long filingId,
    [FromQuery] string reportType,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        ViewBag.ReportType = reportType;
        var currentFiler = await filersApi.GetCurrentRegistration(filerId, cancellationToken);

        var responsibleOfficers = await _filingSvc.GetResponsibleOfficers(filingId);

        return View(SendForAttestationView, new VerificationFilingReportViewModel { FilingId = filingId, FilerId = filerId, FilerName = currentFiler.Name, ResponsibleOfficers = responsibleOfficers });
    }

    [HttpPost]
    public async Task<IActionResult> SendLobbyistReportForAttestation(
        VerificationFilingReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        [FromServices] IFilersApi filersApi,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        ViewBag.ReportType = model.ReportType;
        var currentFiler = await filersApi.GetCurrentRegistration(model.FilerId, cancellationToken);
        model.FilerName = currentFiler.Name;

        try
        {
            var response = await filingsApi.SendForAttestationLobbyistReport((long)model.FilingId!, cancellationToken);

            if (response.Valid)
            {
                _toastService.Success(_localizer["FilerPortal.Filing.SendForAttestationLobbyistReport.SendSuccess"]);
                return RedirectToAction(TemporaryDashboard, Filing);
            }
            else
            {
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                _toastService.Error(_localizer["FilerPortal.Filing.SendForAttestationLobbyistReport.SendError"]);
                return View("SendForAttestation", model);
            }
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer["FilerPortal.Filing.SendForAttestation.Exception"]);
            _logger.LogError(ex, "Failed sending a lobbyist report for attestation.");
            return View("SendForAttestation", model);
        }
    }

    /// <summary>
    /// Send for Attestation - Lobbyist Employer report.
    /// </summary>
    /// <param name="model">VerificationFilingReportViewModel</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> SendForAttestationReportLobbyistEmployer(
        VerificationFilingReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid || model.SelectedResponsibleOfficerIds is null || model.SelectedResponsibleOfficerIds.Count == 0)
        {
            return NotFound();
        }

        try
        {
            var response = await filingsApi.SendForAttestationReportLobbyistEmployer(model.FilingId, model.SelectedResponsibleOfficerIds, cancellationToken);
            if (response.Valid)
            {
                _toastService.Success(_localizer[ResourceConstants.SendForAttestationSuccessMessage]);

                return RedirectToAction(Index, "DisclosureTemporaryDashboard");
            }
            else
            {
                ViewBag.ReportType = model.ReportType;
                var responsibleOfficers = await _filingSvc.GetResponsibleOfficers(model.FilingId);
                model.ResponsibleOfficers = responsibleOfficers;
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                _toastService.Error(_localizer[ResourceConstants.SendForAttestationFailMessage]);
                return View(SendForAttestationView, model);
            }
        }
        catch (Exception ex)
        {
            ViewBag.ReportType = model.ReportType;
            var responsibleOfficers = await _filingSvc.GetResponsibleOfficers(model.FilingId);
            model.ResponsibleOfficers = responsibleOfficers;
            _logger.LogError(ex, "{Msg}", new { Msg = _localizer[ResourceConstants.SendForAttestationFailMessage] });
            _toastService.Error(_localizer[ResourceConstants.SendForAttestationFailMessage]);
            return View(SendForAttestationView, model);
        }
    }

    #region 72h Report
    public async Task<IActionResult> Report72H(
    [FromServices] IRegistrationsApi registrationsApi,
    [FromServices] IFilingsApi filingsApi,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        var lobbyistEmployerRegistration = await registrationsApi.GetLobbyistEmployerEntityRegistration(cancellationToken);

        if (lobbyistEmployerRegistration is not null && lobbyistEmployerRegistration.FilerId.HasValue)
        {
            var model = new Report72HViewModel
            {
                Id = lobbyistEmployerRegistration.Id,
                FilerId = (long)lobbyistEmployerRegistration.FilerId,
            };
            return View(model);
        }
        return View(null);

    }

    /// <summary>
    /// Creates a new filing of type 72h report.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> CreateReport72H(
        Report72HViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filing = await filingsApi.CreateReport72H((long)model.FilerId!, cancellationToken);

        return RedirectToAction(Index, Disclosure, new { filerId = model.FilerId, filingId = filing.Id, reportType = FilingTypeModel.Report72h.Name });
    }

    /// <summary>
    /// Send for Attestation - 72h report.
    /// </summary>
    /// <param name="model">VerificationFilingReportViewModel</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> SendForAttestationReport72H(
        VerificationFilingReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid || model.SelectedResponsibleOfficerIds is null || model.SelectedResponsibleOfficerIds.Count == 0)
        {
            return NotFound();
        }

        try
        {
            var response = await filingsApi.SendForAttestationReport72H(model.FilingId, model.SelectedResponsibleOfficerIds, cancellationToken);
            if (response.Valid)
            {
                _toastService.Success(_localizer[ResourceConstants.SendForAttestationSuccessMessage]);

                return RedirectToAction(Index, "DisclosureTemporaryDashboard");
            }
            else
            {
                _toastService.Error(_localizer[ResourceConstants.SendForAttestationFailMessage]);
                var responsibleOfficers = await _filingSvc.GetResponsibleOfficers(model.FilingId);
                model.ResponsibleOfficers = responsibleOfficers;
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                ViewBag.ReportType = model.ReportType;
                return View(SendForAttestationView, model);
            }
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer[ResourceConstants.SendForAttestationFailMessage]);
            _logger.LogError(ex, "{Msg}", new { Msg = _localizer[ResourceConstants.SendForAttestationFailMessage] });
            var responsibleOfficers = await _filingSvc.GetResponsibleOfficers(model.FilingId);
            model.ResponsibleOfficers = responsibleOfficers;
            ViewBag.ReportType = model.ReportType;
            return View(SendForAttestationView, model);
        }
    }

    /// <summary>
    /// Attest a filing of type 72h report.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> Submit72hReport(
        Report72HViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        ViewBag.ReportType = FilingTypeModel.Report72h.Name;
        SubmitFilingReportDto request = new(model.DiligenceStatementVerification);
        try
        {
            var response = await filingsApi.AttestReport72H(model.Id, request, cancellationToken);
            if (response.Valid)
            {
                ViewBag.SubmittedDate = _dateTimeSvc.GetCurrentDateTime();
                _toastService.Success(_localizer[SubmitReportSuccessMessage]);
                return View(SubmittedView);
            }
            else
            {
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                _toastService.Error(_localizer[SubmitReportErrorMessage]);
                return View(VerificationView, model);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Submit 72H report failed");
            _toastService.Error(_localizer[ApiRequestErrorKey]);
            return View(VerificationView, model);
        }
    }
    #endregion

    #region 48h Report
    public async Task<IActionResult> Report48H(
    [FromServices] IRegistrationsApi registrationsApi,
    [FromServices] IFilingsApi filingsApi,
    CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }
        var lobbyistEmployerRegistration = await registrationsApi.GetLobbyistEmployerEntityRegistration(cancellationToken);

        if (lobbyistEmployerRegistration is not null && lobbyistEmployerRegistration.FilerId.HasValue)
        {
            var model = new Report48HViewModel
            {
                FilerId = (long)lobbyistEmployerRegistration.FilerId,
            };
            return View(model);
        }
        return View(null);

    }

    /// <summary>
    /// Creates a new filing of type 72h report.
    /// </summary>
    /// <param name="model">The model containing the filing information.</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> CreateReport48H(
        Report48HViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var filing = await filingsApi.CreateReport48H(model.FilerId, cancellationToken);

        return RedirectToAction(Index, Disclosure, new { filerId = model.FilerId, filingId = filing.Id, reportType = FilingTypeModel.Report48h.Name });
    }

    /// <summary>
    /// Send for Attestation - 48h report.
    /// </summary>
    /// <param name="id">Filing Id</param>
    /// <param name="filerId">Filer Id</param>
    /// <param name="reportType">Report Type</param>
    /// <param name="filingsApi">The API client for interacting with the filings API.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A redirect to the home page.</returns>
    [HttpPost]
    public async Task<IActionResult> SendForAttestationReport48H(
        VerificationFilingReportViewModel model,
        [FromServices] IFilingsApi filingsApi,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        try
        {
            var request = new Report48HSendForAttestationRequest(model.SelectedResponsibleOfficerIds ?? new List<long>());
            var response = await filingsApi.SendForAttestationReport48H(model.FilingId, request, cancellationToken);
            if (response.Valid)
            {
                _toastService.Success(_localizer[ResourceConstants.SendForAttestationSuccessMessage]);

                return RedirectToAction(Index, "DisclosureTemporaryDashboard");
            }
            else
            {
                SyncDecisionsValidationsWithViewModel([.. response.ValidationErrors], model);
                _toastService.Error(_localizer[ResourceConstants.SendForAttestationFailMessage]);
                var responsibleOfficers = await _filingSvc.GetResponsibleOfficers(model.FilingId);
                model.ResponsibleOfficers = responsibleOfficers;
                ViewBag.ReportType = model.ReportType;
                return View(SendForAttestationView, model);
            }
        }
        catch (Exception ex)
        {
            _toastService.Error(_localizer[ResourceConstants.SendForAttestationFailMessage]);
            _logger.LogError(ex, "{Msg}", new { Msg = _localizer[ResourceConstants.SendForAttestationFailMessage] });
            var responsibleOfficers = await _filingSvc.GetResponsibleOfficers(model.FilingId);
            model.ResponsibleOfficers = responsibleOfficers;
            ViewBag.ReportType = model.ReportType;
            return View(SendForAttestationView, model);
        }
    }

    #endregion

    /// <summary>
    /// Display the confirmation page for the report submission.
    /// </summary>
    /// <returns>The confirmation page for the submitted report.</returns>
    [HttpGet]
    public IActionResult Submitted()
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return View(SubmittedView);
    }

    private void SyncDecisionsValidationsWithViewModel(
        IList<WorkFlowError> decisions,
        IPortalAlertsContainer viewModel
        )
    {
        if (decisions.Any())
        {
            for (int i = 0; i < decisions.Count; i++)
            {
                var dsmNew = decisions[i];
                this.AddDecisionValidationToViewModel(viewModel, new ValidationMessage { ErrorCode = dsmNew.ErrorCode, FieldName = dsmNew.FieldName, Message = dsmNew.Message, Type = dsmNew.ErrorType });
            }
        }
    }

}
