using System.Net;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Moq;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Refit;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.Efile.Model;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using static SOS.CalAccess.FilerPortal.Controllers.TransactionController;
using ActivityExpenseDto = SOS.CalAccess.FilerPortal.Generated.ActivityExpenseDto;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
using PaymentMadeToLobbyingCoalitionResponse = SOS.CalAccess.FilerPortal.Generated.PaymentMadeToLobbyingCoalitionResponse;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class TransactionControllerTests
{

    #region Setup

    private Mock<IRegistrationsApi> _registrationsApiMock;
    private Mock<IFirmPaymentTransactionCtlSvc> _firmPaymentTransactionCtlSvcMock;
    private Mock<ITempDataDictionaryFactory> _tempDataFactoryMock;
    private Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private Mock<IToastService> _toastService;
    private Mock<IStringLocalizer<SharedResources>> _localizerMock;
    private Mock<ITransactionsApi> _transactionsApiMock;
    private Mock<IContactsApi> _contactsApiMock;
    private Mock<IActivityExpenseApi> _activityExpensesApiMock;
    private Mock<ITransactionCtlSvc> _transactionCtlSvc;
    private Mock<IReferenceDataApi> _referenceDataApiMock;
    private Mock<IFilingsApi> _filingsApiMock;
    private Mock<ILobbyistEmployerCoalitionApi> _lobbyistEmployerCoalitionApiMock;
    private Mock<IFilingsApi> _filingsApi;
    private TransactionController _controller;
    private IDateTimeSvc _dateTimeSvc;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _registrationsApiMock = new Mock<IRegistrationsApi>();
        _referenceDataApiMock = new Mock<IReferenceDataApi>();
        _firmPaymentTransactionCtlSvcMock = new Mock<IFirmPaymentTransactionCtlSvc>();

        _tempDataFactoryMock = new Mock<ITempDataDictionaryFactory>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        var tempDataMock = new Mock<ITempDataDictionary>();
        var httpContextMock = new Mock<HttpContext>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _tempDataFactoryMock.Setup(x => x.GetTempData(httpContextMock.Object)).Returns(tempDataMock.Object);
        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

        // Mock the keys for Toast success/danger/warning
        tempDataMock.Setup(td => td["ToastType"]).Returns("");
        tempDataMock.Setup(td => td["ToastMessage"]).Returns("");
        tempDataMock.Setup(td => td["ToastShowCloseButton"]).Returns("");
        tempDataMock.Setup(td => td["ToastX"]).Returns("");
        tempDataMock.Setup(td => td["ToastY"]).Returns("");
        tempDataMock.Setup(td => td["ToastTimeOut"]).Returns("");

        _toastService = new Mock<IToastService>();

        _localizerMock = new Mock<IStringLocalizer<SharedResources>>();
        _transactionsApiMock = new Mock<ITransactionsApi>();
        _contactsApiMock = new Mock<IContactsApi>();
        _activityExpensesApiMock = new Mock<IActivityExpenseApi>();
        _transactionCtlSvc = new Mock<ITransactionCtlSvc>();
        _referenceDataApiMock = new Mock<IReferenceDataApi>();
        _filingsApiMock = new Mock<IFilingsApi>();
        _lobbyistEmployerCoalitionApiMock = new Mock<ILobbyistEmployerCoalitionApi>();
        _filingsApi = new Mock<IFilingsApi>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();

        _controller = new TransactionController(
            _toastService.Object,
            _localizerMock.Object,
            _transactionCtlSvc.Object,
            _contactsApiMock.Object,
            _referenceDataApiMock.Object,
            _dateTimeSvc,
            _filingsApi.Object,
            _transactionsApiMock.Object)

        {
            TempData = tempDataMock.Object
        };
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #endregion

    #region ActivityExpense

#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.

    [Test]
    public void PassActivityExpenseData_InvalidModel_ReturnsViewWithModel()
    {
        // Arrange
        var model = new ActivityExpenseViewModel();
        _controller.ModelState.AddModelError("SomeProperty", "Some error");

        // Act
        var result = _controller.PassActivityExpenseData(
            _activityExpensesApiMock.Object,
            null,
            null,
            null,
            null,
            null,
            model,
            null);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Console.WriteLine(viewResult.ViewName);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.ViewName, Is.EqualTo("ActivityExpense"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });
    }


    [Test]
    public async Task NewActivityExpense_InitializesReportablePeopleGridCorrectly()
    {
        // Arrange
        var filerId = 1L;
        var filingId = 2L;
        var returnUrl = "http://example.com";
        var contactId = 1L;
        var cancellationToken = CancellationToken.None;
        var contactDetailMock = new ContactItemResponse(1
            , "Test City"
            , "United States"
            , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
            , filerId
            , contactId
            , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
            , "HI"
            , "123 Sample St."
            , "APT A"
            , 2
            , true
            , new List<WorkFlowError>()
            , returnUrl
            , "12345");
        var aeType1 = new ActivityExpenseTypeResponse(0, "Item 1", 1, 0, "Travel");
        var aeType2 = new ActivityExpenseTypeResponse(0, "Item 2", 2, 0, "Event");
        var activityExpenseTypes = new List<ActivityExpenseTypeResponse>();
        activityExpenseTypes.Add(aeType1);
        activityExpenseTypes.Add(aeType2);

        _activityExpensesApiMock.Setup(x => x.GetActivityExpenseTypes(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activityExpenseTypes);

        _contactsApiMock.Setup(x => x.GetContact(contactId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(contactDetailMock);

        var sessionMock = new Mock<ISession>();
        var controllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };
        _controller.ControllerContext = controllerContext;

        // Act
        var result = await _controller.NewActivityExpense(
            _activityExpensesApiMock.Object,
            filerId,
            filingId,
            contactId,
            "Sample Report Type",
            returnUrl,
            CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult.Model as ActivityExpenseViewModel;
        Assert.That(model, Is.Not.Null);

        var gridModel = model.ReportablePeopleGridModel;
        Assert.That(gridModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(gridModel.GridId, Is.EqualTo("ReportablePeopleGrid"));
            Assert.That(gridModel.GridType, Is.EqualTo(nameof(TransactionReportablePerson)));
            Assert.That(gridModel.AllowPaging, Is.False);
            Assert.That(gridModel.AllowAdding, Is.False);
            Assert.That(gridModel.AllowDeleting, Is.False);
            Assert.That(gridModel.EnableExport, Is.False);
            Assert.That(gridModel.PageSize, Is.EqualTo(10));
            Assert.That(gridModel.Columns, Has.Count.EqualTo(3));
            Assert.That(gridModel.PrimaryKey, Is.EqualTo(nameof(TransactionReportablePerson.Id)));
            Assert.That(gridModel.DeleteConfirmationMessage, Is.EqualTo("Are you sure that you want to delete reportable person?"));
            Assert.That(gridModel.Columns[0].Field, Is.EqualTo(nameof(TransactionReportablePerson.Name)));
            Assert.That(gridModel.Columns[1].Field, Is.EqualTo(nameof(TransactionReportablePerson.OfficialPosition)));
            Assert.That(gridModel.Columns[2].Field, Is.EqualTo(nameof(TransactionReportablePerson.Amount)));
            Assert.That(gridModel.Columns[2].IsCurrency, Is.True);
        });

        Assert.Multiple(() =>
        {
            Assert.That(model.ContactId, Is.Not.Null);
            Assert.That(model.Contacts, Is.Not.Null);
        });

        Assert.Multiple(() =>
        {
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.Contacts, Has.Count.EqualTo(1));
            Assert.That(model.Contacts[0].Id, Is.EqualTo(contactId));
        });
    }

    [Test]
    public void PassActivityExpenseData_WithExistingSessionData_RedirectsToNewActivityExpenseReportablePerson()
    {
        // Arrange
        var model = new ActivityExpenseViewModel();
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();

        var key = "ActivityExpenseData";
        var existingData = JsonConvert.SerializeObject(new ActivityExpenseViewModel());
        var byteData = Encoding.UTF8.GetBytes(existingData);

        sessionMock.Setup(s => s.TryGetValue(key, out It.Ref<byte[]>.IsAny!))
            .Returns((string _, out byte[] value) =>
            {
                value = byteData;
                return true;
            });

        httpContextMock.Setup(c => c.Session)
            .Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = httpContextMock.Object
        };

        // Act
        var result = _controller.PassActivityExpenseData(
            _activityExpensesApiMock.Object,
            null,
            null,
            null,
            null,
            null,
            model,
            null);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("NewActivityExpenseReportablePerson"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Transaction"));
        });
    }

    [Test]
    public void PassActivityExpenseData_WithNoSessionData_SavesToSessionAndRedirects()
    {
        // Arrange
        var model = new ActivityExpenseViewModel();
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();

        var key = "ActivityExpenseData";
        var existingData = JsonConvert.SerializeObject(new ActivityExpenseViewModel());
        var byteData = Encoding.UTF8.GetBytes(existingData);
        byte[] savedData = [];

        sessionMock.Setup(s => s.TryGetValue(key, out It.Ref<byte[]>.IsAny!))
            .Returns(false);

        sessionMock.Setup(s => s.Set(key, It.IsAny<byte[]>()))
            .Callback<string, byte[]>((k, v) => savedData = v);

        httpContextMock.Setup(c => c.Session)
            .Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = httpContextMock.Object
        };

        // Act
        var result = _controller.PassActivityExpenseData(
            _activityExpensesApiMock.Object,
            null,
            null,
            null,
            null,
            null,
            model,
            "someReturnUrl");

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("NewActivityExpenseReportablePerson"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Transaction"));
        });

        Assert.That(savedData, Is.Not.Null);
        var deserialized = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(Encoding.UTF8.GetString(savedData));
        Assert.That(deserialized, Is.Not.Null);
    }

    [Test]
    public void PassActivityExpenseData_WithSessionDataAndParameters_RedirectsToCreateReportablePerson()
    {
        // Arrange
        var model = new ActivityExpenseViewModel { FilerId = 123 };
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();

        var key = "ActivityExpenseData";
        var existingData = JsonConvert.SerializeObject(model);
        var byteData = Encoding.UTF8.GetBytes(existingData);

        // Mock session to return existing data
        sessionMock.Setup(s => s.TryGetValue(key, out byteData))
            .Returns(true);

        httpContextMock.Setup(c => c.Session)
            .Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = httpContextMock.Object
        };

        // Test with at least one parameter having value
        var testName = "John Doe";
        var testAgency = "State Department";
        var returnUrl = "someReturnUrl";

        // Act
        var result = _controller.PassActivityExpenseData(
            _activityExpensesApiMock.Object,
            name: testName,
            officialPosition: null,
            officialPositionDescription: null,
            agency: testAgency,
            agencyDescription: null,
            model: model,
            fromTransaction: false,
            returnUrl: returnUrl);

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("CreateReportablePerson"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Contact"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null, "RouteValues should not be null.");
            Assert.That(redirectResult.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues!["name"], Is.EqualTo(testName));
            Assert.That(redirectResult.RouteValues!["agency"], Is.EqualTo(testAgency));
            Assert.That(redirectResult.RouteValues!["returnUrl"], Is.EqualTo(returnUrl));
        });
    }


    [Test]
    public void GetActivityExpenseTypesDictionary_ValidInput_ReturnsCorrectDictionary()
    {
        // Arrange
        var aeType1 = new ActivityExpenseTypeResponse(0, "Item 1", 1, 0, "Travel");
        var aeType2 = new ActivityExpenseTypeResponse(0, "Item 2", 2, 0, "Event");
        var activityExpenseTypes = new List<ActivityExpenseTypeResponse>();
        activityExpenseTypes.Add(aeType1);
        activityExpenseTypes.Add(aeType2);

        // Act
        var result = TransactionController.GetActivityExpenseTypesDictionary(activityExpenseTypes);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(2));

        Assert.That(result.Keys, Is.EqualTo(new HashSet<string> { "1", "2" }));
    }

    [Test]
    public async Task NewActivityExpense_InvalidModelState_ShouldStayOnCreate()
    {
        // Arrange
        var model = new ActivityExpenseViewModel
        {
            FilerId = 123,
            FilingId = 123,
            ContactId = 1,
            ReturnUrl = "/return",
        };

        _controller.ModelState.AddModelError("Error", "Invalid model state");
        _ = _activityExpensesApiMock.Setup(api => api.CreateActivityExpense(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<ActivityExpenseDto>(), It.IsAny<CancellationToken>()));

        // Act
        var result = await _controller.HandleActivityExpense(_activityExpensesApiMock.Object, model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected ViewResult when ModelState is invalid.");
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ActivityExpense"));
            Assert.That(result.Model, Is.EqualTo(model));
        });
    }

    [Test]
    public async Task NewActivityExpense_ApiCallFail_ShouldStayOnCreate()
    {
        // Arrange
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        _ = httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);
        var activityExpenseData = /*lang=json,strict*/ """{"DisplayContactAs":"Payee","ActivityExpenseTypes":{},"IsCreditCard":true,"CreditCardCompanyName":"Chase","ActivityExpenseTypeId":9,"ActivityDescription":"activity description","ReturnUrl":"/Disclosure?filerId=1&filingId=1&reportType=Lobbyist&viewName=ActivityExpenses","ReportType":"LobbyistReport","TransactionReportablePeople":[{"Id":0,"TransactionId":0,"Transaction":null,"FilerContactId":null,"FilerContact":null,"RegistrationId":null,"Registration":null,"Amount":{"Value":20000.0},"Name":"Sample reportable person","OfficialPosition":"Other","OfficialPositionDescription":"sample description","Agency":"Other","AgencyDescription":"Sample agency description","CreatedBy":0,"ModifiedBy":0}],"ReportablePeople":{},"AreReportablePeopleDisabled":null,"ReportablePeopleGridModel":null,"PayeeInfoGridModel":null,"Id":null,"Contacts":null,"Amount":1000.0,"TransactionDate":"2025-03-28T00:00:00-10:00","ContactId":4,"FilerId":1,"FilingId":1,"Notes":"additional information","Readonly":false,"Action":"New Activity Expense","Messages":{"GlobalAlerts":[],"Validations":{}}}""";
        var byteArray = Encoding.UTF8.GetBytes(activityExpenseData);
        _ = sessionMock.Setup(s => s.TryGetValue("ActivityExpenseData", out byteArray)).Returns(true);

        var aeType1 = new ActivityExpenseTypeResponse(0, "Item 1", 1, 0, "Travel");
        var aeType2 = new ActivityExpenseTypeResponse(0, "Item 2", 2, 0, "Event");
        var activityExpenseTypes = new List<ActivityExpenseTypeResponse>();
        activityExpenseTypes.Add(aeType1);
        activityExpenseTypes.Add(aeType2);

        _activityExpensesApiMock.Setup(x => x.GetActivityExpenseTypes(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activityExpenseTypes);

        var model = new ActivityExpenseViewModel
        {
            Amount = null,
            FilerId = 123,
            FilingId = 123,
            ContactId = 1,
            ReturnUrl = "/return",
        };

        var fakeResponse = new HttpResponseMessage(HttpStatusCode.InternalServerError)
        {
            Content = new StringContent("Api error!")
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            fakeResponse,
            new RefitSettings());

        _ = _activityExpensesApiMock
            .Setup(api => api.CreateActivityExpense(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<ActivityExpenseDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Act
        var result = await _controller.HandleActivityExpense(_activityExpensesApiMock.Object, model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ActivityExpense"));
            Assert.That(result.Model, Is.EqualTo(model));

            var typedModel = result.Model as ActivityExpenseViewModel;
            Assert.That(typedModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task NewActivityExpense_ApiCallFail_ShouldStayOnCreate_ZeroAmount()
    {
        // Arrange
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        _ = httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);
        var activityExpenseData = /*lang=json,strict*/ """{"DisplayContactAs":"Payee","ActivityExpenseTypes":{},"IsCreditCard":true,"CreditCardCompanyName":"Chase","ActivityExpenseTypeId":9,"ActivityDescription":"activity description","ReturnUrl":"/Disclosure?filerId=1&filingId=1&reportType=Lobbyist&viewName=ActivityExpenses","ReportType":"LobbyistReport","TransactionReportablePeople":[{"Id":0,"TransactionId":0,"Transaction":null,"FilerContactId":null,"FilerContact":null,"RegistrationId":null,"Registration":null,"Amount":{"Value":20000.0},"Name":"Sample reportable person","OfficialPosition":"Other","OfficialPositionDescription":"sample description","Agency":"Other","AgencyDescription":"Sample agency description","CreatedBy":0,"ModifiedBy":0}],"ReportablePeople":{},"AreReportablePeopleDisabled":null,"ReportablePeopleGridModel":null,"PayeeInfoGridModel":null,"Id":null,"Contacts":null,"Amount":1000.0,"TransactionDate":"2025-03-28T00:00:00-10:00","ContactId":4,"FilerId":1,"FilingId":1,"Notes":"additional information","Readonly":false,"Action":"New Activity Expense","Messages":{"GlobalAlerts":[],"Validations":{}}}""";
        var byteArray = Encoding.UTF8.GetBytes(activityExpenseData);
        _ = sessionMock.Setup(s => s.TryGetValue("ActivityExpenseData", out byteArray)).Returns(true);

        var aeType1 = new ActivityExpenseTypeResponse(0, "Item 1", 1, 0, "Travel");
        var aeType2 = new ActivityExpenseTypeResponse(0, "Item 2", 2, 0, "Event");
        var activityExpenseTypes = new List<ActivityExpenseTypeResponse>();
        activityExpenseTypes.Add(aeType1);
        activityExpenseTypes.Add(aeType2);

        _activityExpensesApiMock.Setup(x => x.GetActivityExpenseTypes(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activityExpenseTypes);

        var model = new ActivityExpenseViewModel
        {
            Amount = 0,
            FilerId = 123,
            FilingId = 123,
            ContactId = 1,
            ReturnUrl = "/return",
        };

        var fakeResponse = new HttpResponseMessage(HttpStatusCode.InternalServerError)
        {
            Content = new StringContent("Api error!")
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            fakeResponse,
            new RefitSettings());

        _ = _activityExpensesApiMock
            .Setup(api => api.CreateActivityExpense(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<ActivityExpenseDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Act
        var result = await _controller.HandleActivityExpense(_activityExpensesApiMock.Object, model) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("ActivityExpense"));
            Assert.That(result.Model, Is.EqualTo(model));

            var typedModel = result.Model as ActivityExpenseViewModel;
            Assert.That(typedModel, Is.Not.Null);
        });
    }

    [Test]
    public async Task NewActivityExpenseReportablePerson_ShouldReturnViewWithReportablePeople()
    {
        // Arrange
        var filerId = 12345;
        var model = new ActivityExpenseViewModel();

        var reportablePeople = new List<TransactionReportablePersonDto>
        {
            new (
                agency: "California Energy Commission",
                agencyDescription: "Responsible for energy policy and planning",
                amount: 1500.50,
                contactDisplay: "John Doe (Lobbyist)",
                contactType: FilingTypeModel.LobbyistReport.Name,
                createdDate: _dateNow.AddDays(-10),
                filerContactId: 301,
                id: 101,
                name: "John Doe",
                officialPosition: "Senior Advisor",
                officialPositionDescription: "Advises on renewable energy policies",
                registrationDisplay: "Reg-2025-001",
                registrationId: 501,
                registrationType: "Lobbying",
                transactionId: 201
            ),
            new (
                agency: "State Water Resources Board",
                agencyDescription: "Oversees state water usage and quality",
                amount: 2200.00,
                contactDisplay: "N/A",
                contactType: "Other",
                createdDate: _dateNow.AddDays(-30),
                filerContactId: null,
                id: 102,
                name: "Jane Public",
                officialPosition: "Consultant",
                officialPositionDescription: "Independent policy consultant",
                registrationDisplay: "N/A",
                registrationId: null,
                registrationType: "N/A",
                transactionId: 202
            )
        };

        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        _ = httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);
        var activityExpenseData = /*lang=json,strict*/ """{"DisplayContactAs":"Payee","ActivityExpenseTypes":{},"IsCreditCard":true,"CreditCardCompanyName":"Chase","ActivityExpenseTypeId":9,"ActivityDescription":"activity description","ReturnUrl":"/Disclosure?filerId=12345&filingId=1&reportType=Lobbyist&viewName=ActivityExpenses","ReportType":"LobbyistReport","TransactionReportablePeople":[],"ReportablePeople":{},"AreReportablePeopleDisabled":null,"ReportablePeopleGridModel":null,"PayeeInfoGridModel":null,"Id":null,"Contacts":null,"Amount":1000.0,"TransactionDate":"2025-03-28T00:00:00-10:00","ContactId":4,"FilerId":12345,"FilingId":1,"Notes":"additional information","Readonly":false,"Action":"New Activity Expense","Messages":{"GlobalAlerts":[],"Validations":{}}}""";
        var byteArray = Encoding.UTF8.GetBytes(activityExpenseData);
        _ = sessionMock.Setup(s => s.TryGetValue("ActivityExpenseData", out byteArray)).Returns(true);

        _transactionsApiMock
            .Setup(api => api.GetAllReportablePersonsByFiler(filerId, It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(reportablePeople);

        // Act
        var result = await _controller.NewActivityExpenseReportablePerson(
            _contactsApiMock.Object,
            _transactionsApiMock.Object,
            model,
            filerId,
            CancellationToken.None
        );

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var updatedModel = viewResult?.Model as ActivityExpenseViewModel;
        Assert.Multiple(() =>
        {
            Assert.That(updatedModel, Is.Not.Null);
            Assert.That(updatedModel!.FilerId, Is.EqualTo(filerId));
        });
    }

    [Test]
    public void DeleteReportablePerson_ValidId_RemovesPersonAndReturnsSuccess()
    {
        // Arrange
        var testData = new ActivityExpenseViewModel
        {
            FilerId = 123,
            TransactionReportablePeople = new()
            {
                new() { Id = 1, Name = "Person1", Amount = (Currency)100 },
                new() { Id = 2, Name = "Person2", Amount = (Currency)200 }
            }
        };

        var serializedData = JsonConvert.SerializeObject(testData);
        var byteData = Encoding.UTF8.GetBytes(serializedData);
        var key = "ActivityExpenseData";
        byte[] savedData = null!;

        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.TryGetValue(key, out byteData))
                  .Returns(true);

        sessionMock.Setup(s => s.Set(key, It.IsAny<byte[]>()))
                  .Callback<string, byte[]>((key, data) => savedData = data);

        var httpContextMock = new Mock<HttpContext>();
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };

        // Act
        var result = _controller.DeleteReportablePerson(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Value, Is.Not.Null);

        var updatedModel = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(
            Encoding.UTF8.GetString(savedData));
        Assert.That(updatedModel, Is.Not.Null);

        dynamic responseValue = result.Value;
        Assert.That(responseValue, Is.Not.Null);

        var dataSource = responseValue.DataSource as IEnumerable<dynamic>;
        Assert.That(dataSource, Is.Not.Null);

        var columns = responseValue.Columns as IEnumerable<dynamic>;
        Assert.That(columns, Is.Not.Null);

        Assert.Multiple(() =>
        {
            // Verify session was updated correctly
            Assert.That(savedData, Is.Not.Null);
            Assert.That(updatedModel.TransactionReportablePeople, Has.Count.EqualTo(1));

            // Verify DataSource
            Assert.That(dataSource.Count(), Is.EqualTo(1));
            Assert.That(dataSource.First().Id, Is.EqualTo(2));
            Assert.That(dataSource.First().Name, Is.EqualTo("Person2"));
            Assert.That(dataSource.First().Amount, Is.EqualTo(200.0));

            // Verify grid configuration
            Assert.That(responseValue.GridId, Is.EqualTo("ReportablePeopleGrid"));
            Assert.That(responseValue.GridType, Is.EqualTo("TransactionReportablePerson"));
            Assert.That(responseValue.AllowPaging, Is.False);
            Assert.That(responseValue.AllowAdding, Is.False);
            Assert.That(responseValue.AllowDeleting, Is.False);

            // Verify columns configuration
            Assert.That(columns, Is.Not.Null);
            Assert.That(columns.Count(), Is.EqualTo(3));
            Assert.That(columns.First(c => c.Field == "Name").HeaderText, Is.EqualTo("Name of person"));
            Assert.That(columns.First(c => c.Field == "Amount").IsCurrency, Is.True);
        });
    }

    [Test]
    public void DeleteReportablePerson_InvalidModelState_Returns404()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Test error");

        // Act
        var result = _controller.DeleteReportablePerson(1);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result.StatusCode, Is.EqualTo(404));
    }

    [Test]
    public void DeleteReportablePerson_PersonNotFound_Returns404WithError()
    {
        // Arrange
        var testData = new ActivityExpenseViewModel
        {
            FilerId = 123,
            TransactionReportablePeople = new()
            {
                new() { Id = 1, Name = "Person1", Amount = (Currency)100 },
                new() { Id = 2, Name = "Person2", Amount = (Currency)200 }
            }
        };

        var serializedData = JsonConvert.SerializeObject(testData);
        var byteData = Encoding.UTF8.GetBytes(serializedData);
        var key = "ActivityExpenseData";
        byte[] savedData = null!;

        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.TryGetValue(key, out byteData))
                  .Returns(true);

        sessionMock.Setup(s => s.Set(key, It.IsAny<byte[]>()))
                  .Callback<string, byte[]>((key, data) => savedData = data);

        var httpContextMock = new Mock<HttpContext>();
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };

        // Act
        var result = _controller.DeleteReportablePerson(999);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusCode, Is.EqualTo(404));
        });
    }

    [Test]
    public void EditReportablePerson_ValidId_RedirectsWithCorrectParameters()
    {
        // Arrange
        var testData = new ActivityExpenseViewModel
        {
            FilerId = 123,
            TransactionReportablePeople = new()
            {
                new() { Id = 1, Name = "Person1", Amount = (Currency)100, Agency = "AGY", AgencyDescription = "Agency Desc", OfficialPosition = "POS", OfficialPositionDescription = "Position Desc"},
                new() { Id = 2, Name = "Person2", Amount = (Currency)200 }
            },
            ReturnUrl = "/return"
        };

        var serializedData = JsonConvert.SerializeObject(testData);
        var byteData = Encoding.UTF8.GetBytes(serializedData);
        var key = "ActivityExpenseData";
        byte[] savedData = null!;

        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.TryGetValue(key, out byteData))
                  .Returns(true);

        sessionMock.Setup(s => s.Set(key, It.IsAny<byte[]>()))
                  .Callback<string, byte[]>((key, data) => savedData = data);

        var httpContextMock = new Mock<HttpContext>();
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };

        // Act
        var result = _controller.EditReportablePerson(1);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = (RedirectToActionResult)result;
        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("EditReportablePerson"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Contact"));

            // Verify all route values
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(123));
            Assert.That(redirectResult.RouteValues["id"], Is.EqualTo(1));
            Assert.That(redirectResult.RouteValues["name"], Is.EqualTo("Person1"));
            Assert.That(redirectResult.RouteValues["agency"], Is.EqualTo("AGY"));
            Assert.That(redirectResult.RouteValues["agencyDescription"], Is.EqualTo("Agency Desc"));
            Assert.That(redirectResult.RouteValues["officialPosition"], Is.EqualTo("POS"));
            Assert.That(redirectResult.RouteValues["officialPositionDescription"], Is.EqualTo("Position Desc"));
            Assert.That(redirectResult.RouteValues["amount"], Is.EqualTo(100m));
            Assert.That(redirectResult.RouteValues["returnUrl"], Is.EqualTo("/return"));
        });
    }

    [Test]
    public void EditReportablePerson_InvalidModelState_Returns404()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Test error");

        // Act
        var result = _controller.EditReportablePerson(1) as JsonResult;

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusCode, Is.EqualTo(404));
        });
    }

    [Test]
    public void EditReportablePerson_InvalidModelData_Returns400WithError()
    {
        var serializedData = JsonConvert.SerializeObject(null);
        var byteData = Encoding.UTF8.GetBytes(serializedData);
        var key = "ActivityExpenseData";
        byte[] savedData = null!;

        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.TryGetValue(key, out byteData))
                  .Returns(false);

        sessionMock.Setup(s => s.Set(key, It.IsAny<byte[]>()))
                  .Callback<string, byte[]>((key, data) => savedData = data);

        var httpContextMock = new Mock<HttpContext>();
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };

        // Act
        var result = _controller.EditReportablePerson(1) as JsonResult;

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result, Is.Not.Null);

        dynamic response = result.Value!;
        var expectedError = /*lang=json,strict*/ "{\"error\":\"Invalid model data\"}";
        var serializedResponse = JsonConvert.SerializeObject(response);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusCode, Is.EqualTo(400));
            Assert.That(serializedResponse, Is.EqualTo(expectedError));
        });
    }

    [Test]
    public void EditReportablePerson_PersonNotFound_Returns404WithError()
    {
        // Arrange
        var testData = new ActivityExpenseViewModel
        {
            FilerId = 123,
            TransactionReportablePeople = new()
            {
                new() { Id = 1, Name = "Person1", Amount = (Currency)100 },
                new() { Id = 2, Name = "Person2", Amount = (Currency)200 }
            }
        };

        var serializedData = JsonConvert.SerializeObject(testData);
        var byteData = Encoding.UTF8.GetBytes(serializedData);
        var key = "ActivityExpenseData";
        byte[] savedData = null!;

        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.TryGetValue(key, out byteData))
                  .Returns(true);

        sessionMock.Setup(s => s.Set(key, It.IsAny<byte[]>()))
                  .Callback<string, byte[]>((key, data) => savedData = data);

        var httpContextMock = new Mock<HttpContext>();
        httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };

        // Act
        var result = _controller.EditReportablePerson(999) as JsonResult;

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.That(result, Is.Not.Null);

        dynamic response = result.Value!;
        var expectedError = /*lang=json,strict*/ "{\"error\":\"Person not found\"}";
        var serializedResponse = JsonConvert.SerializeObject(response);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusCode, Is.EqualTo(404));
            Assert.That(serializedResponse, Is.EqualTo(expectedError));
        });
    }

    [Test]
    public async Task PreEditActivityExpense_InvalidModelState_Returns404()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Test error");

        // Act
        var result = await _controller.PreEditActivityExpense("", 1, _activityExpensesApiMock.Object, CancellationToken.None) as JsonResult;

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusCode, Is.EqualTo(404));
        });
    }

    [Test]
    public async Task EditActivityExpense_InvalidModelState_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.EditActivityExpense(_activityExpensesApiMock.Object, 1, 1, 1, 1, "", CancellationToken.None) as NotFoundResult;

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PreEditActivityExpense_SuccessfulApiCall_RedirectsToEditActivityExpense()
    {
        // Arrange
        var reportablePeople = new List<TransactionReportablePersonDto>();

        TransactionReportablePersonDto newPerson = new(
            agency: string.Empty,
            agencyDescription: string.Empty,
            amount: 100,
            contactDisplay: "",
            contactType: "",
            createdDate: _dateNow,
            filerContactId: 1,
            id: 1,
            name: string.Empty,
            officialPosition: string.Empty,
            officialPositionDescription: string.Empty,
            registrationDisplay: "",
            registrationId: null,
            registrationType: "",
            transactionId: null
        );
        reportablePeople.Add(newPerson);

        var mockResult = new ActivityExpenseDto(
            activityDescription: "Test Activity",
            activityExpenseTypeId: 1,
            amount: 100,
            contactId: 789,
            creditCardCompanyName: "Visa",
            filerId: 123,
            filingId: 456,
            id: 1,
            isCreditCard: true,
            monetaryTypeId: 1,
            notes: "Some notes",
            transactionDate: _dateNow,
            transactionReportablePersons: reportablePeople
        );

        _ = _activityExpensesApiMock.Setup(api => api.GetActivityExpenseForUpdate(1, It.IsAny<CancellationToken>()))
                              .ReturnsAsync(mockResult);

        var mockUrlHelper = new Mock<IUrlHelper>();

        mockUrlHelper
            .Setup(x => x.Action(It.IsAny<UrlActionContext>()))
            .Returns("/Disclosure/Index");

        _controller.Url = mockUrlHelper.Object;

        // Act
        var result = await _controller.PreEditActivityExpense("", 1, _activityExpensesApiMock.Object, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Edit"));
            Assert.That(result.RouteValues!["context"], Is.EqualTo($"EditActivityExpense/{mockResult.Id}"));
            Assert.That(result.RouteValues["filerId"], Is.EqualTo(mockResult.FilerId));
            Assert.That(result.RouteValues["filingId"], Is.EqualTo(mockResult.FilingId));
            Assert.That(result.RouteValues["contactId"], Is.EqualTo(mockResult.ContactId));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(""));
        });
    }

    [Test]
    public async Task PreEditActivityExpense_ApiCallThrowsException_ReturnsNotFound()
    {
        // Arrange
        var activityExpenseApiMock = new Mock<IActivityExpenseApi>();

        var fakeResponse = new HttpResponseMessage(HttpStatusCode.InternalServerError)
        {
            Content = new StringContent("Api error!")
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            fakeResponse,
            new RefitSettings());

        _ = activityExpenseApiMock.Setup(api => api.GetActivityExpenseForUpdate(It.IsAny<long>(), It.IsAny<CancellationToken>()))
                              .ThrowsAsync(apiException);

        // Act
        var result = await _controller.PreEditActivityExpense("", 1, activityExpenseApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EditActivityExpense_InitializesCorrectly()
    {
        // Arrange
        var expenseId = 1;
        var filerId = 1L;
        var filingId = 2L;
        var returnUrl = "http://example.com";
        var contactId = 1L;
        var cancellationToken = CancellationToken.None;
        var contactDetailMock = new ContactItemResponse(1
            , "Test City"
            , "United States"
            , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
            , filerId
            , contactId
            , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
            , "HI"
            , "123 Sample St."
            , "APT A"
            , 2
            , true
            , new List<WorkFlowError>()
            , returnUrl
            , "12345");
        var aeType1 = new ActivityExpenseTypeResponse(0, "Item 1", 1, 0, "Travel");
        var aeType2 = new ActivityExpenseTypeResponse(0, "Item 2", 2, 0, "Event");
        var activityExpenseTypes = new List<ActivityExpenseTypeResponse>();
        activityExpenseTypes.Add(aeType1);
        activityExpenseTypes.Add(aeType2);

        var reportablePeople = new List<TransactionReportablePersonDto>();
        TransactionReportablePersonDto newPerson = new(
            agency: string.Empty,
            agencyDescription: string.Empty,
            amount: 100,
            contactDisplay: "",
            contactType: "",
            createdDate: _dateNow,
            filerContactId: 1,
            id: 1,
            name: string.Empty,
            officialPosition: string.Empty,
            officialPositionDescription: string.Empty,
            registrationDisplay: "",
            registrationId: null,
            registrationType: "",
            transactionId: 1
        );
        reportablePeople.Add(newPerson);

        var mockResult = new ActivityExpenseDto(
            activityDescription: "Test Activity",
            activityExpenseTypeId: 1,
            amount: 100,
            contactId: 789,
            creditCardCompanyName: "Visa",
            filerId: 123,
            filingId: 456,
            id: 1,
            isCreditCard: true,
            monetaryTypeId: 1,
            notes: "Some notes",
            transactionDate: _dateNow,
            transactionReportablePersons: reportablePeople
        );

        _ = _activityExpensesApiMock.Setup(x => x.GetActivityExpenseTypes(It.IsAny<CancellationToken>()))
            .ReturnsAsync(activityExpenseTypes);

        _ = _activityExpensesApiMock.Setup(x => x.GetActivityExpenseForUpdate(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResult);

        _ = _contactsApiMock.Setup(x => x.GetContact(contactId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(contactDetailMock);

        var sessionMock = new Mock<ISession>();
        var controllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };
        _controller.ControllerContext = controllerContext;

        // Act
        var result = await _controller.EditActivityExpense(
            _activityExpensesApiMock.Object,
            expenseId,
            filerId,
            filingId,
            contactId,
            "Sample Report Type",
            cancellationToken);

        // Assert
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult, Is.Not.Null);
            Assert.That(viewResult!.ViewName, Is.EqualTo("ActivityExpense"));
        });

        var model = viewResult.Model as ActivityExpenseViewModel;
        Assert.That(model, Is.Not.Null);

        var gridModel = model.ReportablePeopleGridModel;
        Assert.That(gridModel, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(gridModel.GridId, Is.EqualTo("ReportablePeopleGrid"));
            Assert.That(gridModel.GridType, Is.EqualTo(nameof(TransactionReportablePerson)));
        });

        Assert.Multiple(() =>
        {
            Assert.That(model.ContactId, Is.Not.Null);
            Assert.That(model.Contacts, Is.Not.Null);
        });

        Assert.Multiple(() =>
        {
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.Contacts, Has.Count.EqualTo(1));
            Assert.That(model.Contacts[0].Id, Is.EqualTo(contactId));
        });
    }

    [Test]
    public async Task EditActivityExpense_Success_RedirectsToDisclosureIndex()
    {
        // Arrange
        var httpContextMock = new Mock<HttpContext>();
        var sessionMock = new Mock<ISession>();
        _controller.ControllerContext.HttpContext = httpContextMock.Object;
        _ = httpContextMock.Setup(c => c.Session).Returns(sessionMock.Object);
        var activityExpenseData = /*lang=json,strict*/ """{"DisplayContactAs":"Payee","ActivityExpenseTypes":{},"IsCreditCard":true,"CreditCardCompanyName":"Chase","ActivityExpenseTypeId":9,"ActivityDescription":"activity description","ReturnUrl":"/Disclosure?filerId=1&filingId=1&reportType=Lobbyist&viewName=ActivityExpenses","ReportType":"LobbyistReport","TransactionReportablePeople":[{"Id":0,"TransactionId":0,"Transaction":null,"FilerContactId":null,"FilerContact":null,"RegistrationId":null,"Registration":null,"Amount":{"Value":20000.0},"Name":"Sample reportable person","OfficialPosition":"Other","OfficialPositionDescription":"sample description","Agency":"Other","AgencyDescription":"Sample agency description","CreatedBy":0,"ModifiedBy":0}],"ReportablePeople":{},"AreReportablePeopleDisabled":null,"ReportablePeopleGridModel":null,"PayeeInfoGridModel":null,"Id":null,"Contacts":null,"Amount":1000.0,"TransactionDate":"2025-03-28T00:00:00-10:00","ContactId":4,"FilerId":1,"FilingId":1,"Notes":"additional information","Readonly":false,"Action":"New Activity Expense","Messages":{"GlobalAlerts":[],"Validations":{}}}""";
        var byteArray = Encoding.UTF8.GetBytes(activityExpenseData);
        _ = sessionMock.Setup(s => s.TryGetValue("ActivityExpenseData", out byteArray)).Returns(true);

        var cancellationToken = CancellationToken.None;

        ActivityExpenseViewModel model = new()
        {
            ActivityDescription = "sample description",
            ActivityExpenseTypeId = 1,
            Amount = 100,
            ContactId = 1,
            CreditCardCompanyName = "Chase",
            FilerId = 1,
            FilingId = 1,
            Id = 1,
            IsCreditCard = true,
            Notes = "Sample notes",
            TransactionDate = _dateNow
        };

        var controllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { Session = sessionMock.Object }
        };
        _controller.ControllerContext = controllerContext;

        // Act
        var result = await _controller.HandleActivityExpense(
            _activityExpensesApiMock.Object,
            model,
            "/returnUrl",
            cancellationToken);

        // Assert
        var viewResult = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ActionName, Is.EqualTo("Index"), "Should redirect to 'Index' action.");
            Assert.That(viewResult.ControllerName, Is.EqualTo("Disclosure"), "Should redirect to 'Disclosure' controller.");
        });
    }

    #endregion

    #region CampaignContribution

    [Test]
    public async Task CreateLobbyistEmployerCoalitionLobbyingCampaignContribution_ShouldSetCommitteeDropdownOptions()
    {
        // Arrange
        var filerId = 12345;
        var model = new LobbyingCampaignContributionViewModel
        {
            FilingId = 98765,
            Amount = 100,
            IsRecipientCommittee = true,
        };

        var committees = new List<RegistrationItemResponse>
        {
            new (approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
            new (approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
            new (approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3") // Should not be included
        };

        // Mocking the RegistrationsApi to return the committees
        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        // Act
        var result = await _controller.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
            filerId,
            _transactionsApiMock.Object,
            _registrationsApiMock.Object,
            model
        );

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var updatedModel = viewResult?.Model as LobbyingCampaignContributionViewModel;
        Assert.That(updatedModel, Is.Not.Null);

        var committeeAutoCompleteModel = updatedModel.RecipientCommitteeFilerAutoCompleteModel;
        var optionA = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 12345);
        var optionB = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 67890);
        Assert.Multiple(() =>
            {
                Assert.That(committeeAutoCompleteModel, Is.Not.Null, "CommitteeDropdownOptions should not be null");
                Assert.That(committeeAutoCompleteModel.Options, Is.TypeOf<List<RegistrationItemResponse>>(), "Expected options to be a list of RegistrationItemResponse");
                Assert.That(committeeAutoCompleteModel.Options, Has.Count.EqualTo(2), "Expected CommitteeDropdownOptions to have 2 items");
                Assert.That(committeeAutoCompleteModel.Name, Is.EqualTo("RecipientCommitteeFilerId"), "Expected committee auto complete to use name RecipientCommitteeFilerId");
                Assert.That(committeeAutoCompleteModel.TextKey, Is.EqualTo("Name"), "Expected committee auto complete to use Name for option text");
                Assert.That(committeeAutoCompleteModel.ValueKey, Is.EqualTo("FilerId"), "Expected committee auto complete to use Id for option value");
                Assert.That(committeeAutoCompleteModel.Placeholder, Is.Not.Null, "Expected committee auto complete to have a placeholder");
                Assert.That(optionA, Is.Not.Null);
                Assert.That(optionB, Is.Not.Null);
                Assert.That(optionA?.Name, Is.EqualTo("Committee A"), "Expected dropdown option for 12345");
                Assert.That(optionB?.Name, Is.EqualTo("Committee B"), "Expected dropdown option for 67890");
            }
        );
    }

    [Test]
    public async Task CreateLobbyistCampaignContribution_ShouldSetCommitteeDropdownOptions()
    {
        // Arrange
        var filerId = 12345;
        var model = new LobbyistCampaignContributionViewModel
        {
            FilingId = 98765,
            Amount = 100,
            IsRecipientCommittee = true,
        };

        var committees = new List<RegistrationItemResponse>
        {
            new (approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
            new (approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
            new (approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3") // Should not be included
        };

        // Mocking the RegistrationsApi to return the committees
        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        // Act
        var result = await _controller.CreateLobbyistCampaignContribution(
            filerId,
            _transactionsApiMock.Object,
            _registrationsApiMock.Object,
            model
        );

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var updatedModel = viewResult?.Model as LobbyingCampaignContributionViewModel;
        Assert.That(updatedModel, Is.Not.Null);

        var committeeAutoCompleteModel = updatedModel.RecipientCommitteeFilerAutoCompleteModel;
        var optionA = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 12345);
        var optionB = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 67890);
        Assert.Multiple(() =>
        {
            Assert.That(committeeAutoCompleteModel, Is.Not.Null, "CommitteeDropdownOptions should not be null");
            Assert.That(committeeAutoCompleteModel.Options, Is.TypeOf<List<RegistrationItemResponse>>(), "Expected options to be a list of RegistrationItemResponse");
            Assert.That(committeeAutoCompleteModel.Options, Has.Count.EqualTo(2), "Expected CommitteeDropdownOptions to have 2 items");
            Assert.That(committeeAutoCompleteModel.Name, Is.EqualTo("RecipientCommitteeFilerId"), "Expected committee auto complete to use name RecipientCommitteeFilerId");
            Assert.That(committeeAutoCompleteModel.TextKey, Is.EqualTo("Name"), "Expected committee auto complete to use Name for option text");
            Assert.That(committeeAutoCompleteModel.ValueKey, Is.EqualTo("FilerId"), "Expected committee auto complete to use Id for option value");
            Assert.That(committeeAutoCompleteModel.Placeholder, Is.Not.Null, "Expected committee auto complete to have a placeholder");
            Assert.That(optionA, Is.Not.Null);
            Assert.That(optionB, Is.Not.Null);
            Assert.That(optionA?.Name, Is.EqualTo("Committee A"), "Expected dropdown option for 12345");
            Assert.That(optionB?.Name, Is.EqualTo("Committee B"), "Expected dropdown option for 67890");
        }
        );
    }

    [Test]
    public async Task CreateLobbyistEmployerCoalitionLobbyingCampaignContribution_ShouldRedirectToDisclosureIndext()
    {
        // Arrange
        var filerId = 12345;
        var reportType = FilingTypeModel.LobbyistEmployerReport.Name;
        var returnUrl = "http://example.com";
        var model = new LobbyingCampaignContributionViewModel
        {
            FilingId = 98765,
            Amount = 100,
            IsRecipientCommittee = true,
        };

        var committees = new List<RegistrationItemResponse>
        {
            new (approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
            new (approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
            new (approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3") // Should not be included
        };

        // Mocking the RegistrationsApi to return the committees
        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        _transactionsApiMock
            .Setup(api => api.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(It.IsAny<long>(), It.IsAny<LobbyingCampaignContributionRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new TransactionItemResponse(10, "Display Name", null, null, 1, null, "note", new DateTime(), TransactionType.LobbyingCampaignContribution.Id));

        // Act
        var result = await _controller.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
            filerId,
            _transactionsApiMock.Object,
            _registrationsApiMock.Object,
            model,
            returnUrl
        ) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Expected RedirectToActionResult for a valid request.");
            Assert.That(result!.ActionName, Is.EqualTo("Index"), "Should redirect to 'Index' action.");
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"), "Should redirect to 'Disclosure' controller.");

            Assert.That(result.RouteValues, Contains.Key("filerId"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));

            Assert.That(result.RouteValues, Contains.Key("filingId"));
            Assert.That(result.RouteValues["filingId"], Is.EqualTo(model.FilingId));

            Assert.That(result.RouteValues, Contains.Key("viewName"));
            Assert.That(result.RouteValues["viewName"], Is.EqualTo(FilingSummaryTypeModel.CampaignContributionSummary.Name));

            Assert.That(result.RouteValues, Contains.Key("reportType"));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(reportType));
        });
    }

    [Test]
    public async Task CreateLobbyistCampaignContribution_ShouldRedirectToDisclosureIndex()
    {
        // Arrange
        var filerId = 12345;
        var reportType = FilingTypeModel.LobbyistReport.Name;
        var returnUrl = "http://example.com";
        var model = new LobbyistCampaignContributionViewModel
        {
            FilingId = 98765,
            Amount = 100,
            IsRecipientCommittee = true,
        };

        var committees = new List<RegistrationItemResponse>
        {
            new (approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
            new (approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
            new (approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3") // Should not be included
        };

        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        _transactionsApiMock
            .Setup(api => api.CreateLobbyingCampaignContribution(It.IsAny<long>(), It.IsAny<LobbyistCampaignContributionRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new TransactionItemResponse(10, "Display Name", null, null, 1, null, "note", new DateTime(), TransactionType.LobbyingCampaignContribution.Id));

        // Act
        var result = await _controller.CreateLobbyistCampaignContribution(
            filerId,
            _transactionsApiMock.Object,
            _registrationsApiMock.Object,
            model,
            returnUrl
        ) as RedirectToActionResult;

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Expected RedirectToActionResult for a valid request.");
            Assert.That(result!.ActionName, Is.EqualTo("Index"), "Should redirect to 'Index' action.");
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"), "Should redirect to 'Disclosure' controller.");

            Assert.That(result.RouteValues, Contains.Key("filerId"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));

            Assert.That(result.RouteValues, Contains.Key("filingId"));
            Assert.That(result.RouteValues["filingId"], Is.EqualTo(model.FilingId));

            Assert.That(result.RouteValues, Contains.Key("viewName"));
            Assert.That(result.RouteValues["viewName"], Is.EqualTo(FilingSummaryTypeModel.CampaignContributionSummary.Name));

            Assert.That(result.RouteValues, Contains.Key("reportType"));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(reportType));
        });
    }

    [Test]
    public async Task CreateLobbyingCampaignContribution_ShouldReturnViewWithModel()
    {
        // Arrange
        var filerId = 12345L;
        var filingId = 67890L;
        var returnUrl = "foo";

        var committees = new List<RegistrationItemResponse>
    {
        new(approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
        new(approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
        new(approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3")
    };

        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        // Act
        var result = await _controller.CreateLobbyingCampaignContribution(
            filerId,
            filingId,
            _registrationsApiMock.Object,
            returnUrl
        );

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult!.ViewName, Is.EqualTo("LobbyingCampaignContribution"));

        var model = viewResult.Model as LobbyingCampaignContributionViewModel;
        Assert.That(model, Is.Not.Null);

        var committeeAutoCompleteModel = model.RecipientCommitteeFilerAutoCompleteModel;
        Assert.Multiple(() =>
        {
            Assert.That(model!.FilerId, Is.EqualTo(filerId));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(committeeAutoCompleteModel, Is.Not.Null);
            Assert.That(committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 12345), Is.Not.Null);
            Assert.That(committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 67890), Is.Not.Null);
        });
    }

    [Test]
    public async Task CreateLobbyistCampaignContribution_ShouldReturnViewWithModel()
    {
        // Arrange
        var filerId = 12345L;
        var filingId = 67890L;
        var returnUrl = "foo";

        var committees = new List<RegistrationItemResponse>
        {
            new(approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
            new(approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
            new(approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3")
        };

        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        // Act
        var result = await _controller.CreateLobbyistCampaignContribution(
            filerId,
            filingId,
            _registrationsApiMock.Object,
            returnUrl
        );

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult!.ViewName, Is.EqualTo("LobbyistCampaignContribution"));

        var model = viewResult.Model as LobbyistCampaignContributionViewModel;
        Assert.That(model, Is.Not.Null);

        var committeeAutoCompleteModel = model.RecipientCommitteeFilerAutoCompleteModel;
        Assert.Multiple(() =>
        {
            Assert.That(model!.FilerId, Is.EqualTo(filerId));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(committeeAutoCompleteModel, Is.Not.Null);
            Assert.That(committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 12345), Is.Not.Null);
            Assert.That(committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 67890), Is.Not.Null);
        });
    }

    [Test]
    public async Task CreateLobbyistEmployerCoalitionLobbyingCampaignContribution_ShouldHandleApiException()
    {
        // Arrange
        var filerId = 12345;
        var model = new LobbyingCampaignContributionViewModel
        {
            FilingId = 98765,
            Amount = 100,
            IsRecipientCommittee = true,
        };

        var committees = new List<RegistrationItemResponse>
        {
            new (approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
            new (approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
            new (approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3")
        };

        // Mocking the RegistrationsApi to return the committees
        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        var fakeResponse = new HttpResponseMessage(HttpStatusCode.UnprocessableEntity)
        {
            Content = new StringContent(JsonConvert.SerializeObject(new[]
            {
                new
                {
                    FieldName = "Amount",
                    Message = "Invalid amount",
                    ErrorType = "Validation"
                }
            }))
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            fakeResponse,
            new RefitSettings());

        _transactionsApiMock
            .Setup(api => api.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(It.IsAny<long>(), It.IsAny<LobbyingCampaignContributionRequestDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Act
        var result = await _controller.CreateLobbyistEmployerCoalitionLobbyingCampaignContribution(
            filerId,
            _transactionsApiMock.Object,
            _registrationsApiMock.Object,
            model
        );

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var updatedModel = viewResult?.Model as LobbyingCampaignContributionViewModel;
        Assert.That(updatedModel, Is.Not.Null);

        var committeeAutoCompleteModel = updatedModel.RecipientCommitteeFilerAutoCompleteModel;
        var optionA = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 12345);
        var optionB = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 67890);
        Assert.Multiple(() =>
        {
            Assert.That(committeeAutoCompleteModel, Is.Not.Null, "CommitteeDropdownOptions should not be null");
            Assert.That(committeeAutoCompleteModel.Options, Is.TypeOf<List<RegistrationItemResponse>>(), "Expected options to be a list of RegistrationItemResponse");
            Assert.That(committeeAutoCompleteModel.Options, Has.Count.EqualTo(2), "Expected CommitteeDropdownOptions to have 2 items");
            Assert.That(committeeAutoCompleteModel.Name, Is.EqualTo("RecipientCommitteeFilerId"), "Expected committee auto complete to use name RecipientCommitteeFilerId");
            Assert.That(committeeAutoCompleteModel.TextKey, Is.EqualTo("Name"), "Expected committee auto complete to use Name for option text");
            Assert.That(committeeAutoCompleteModel.ValueKey, Is.EqualTo("FilerId"), "Expected committee auto complete to use Id for option value");
            Assert.That(committeeAutoCompleteModel.Placeholder, Is.Not.Null, "Expected committee auto complete to have a placeholder");
            Assert.That(optionA, Is.Not.Null);
            Assert.That(optionB, Is.Not.Null);
            Assert.That(optionA?.Name, Is.EqualTo("Committee A"), "Expected dropdown option for 12345");
            Assert.That(optionB?.Name, Is.EqualTo("Committee B"), "Expected dropdown option for 67890");
        }
        );
    }

    [Test]
    public async Task SearchFilersByTypeAndQuery_ReturnsJsonResult_WhenModelStateIsValid()
    {
        // Arrange
        var query = "Committee";
        var filerTypeId = 1L;
        var cancellationToken = CancellationToken.None;
        var expectedData = new List<FilerSearchDto>
        {
            new(id: 1, name: "Committee A", filerType: "Recipient Committee", filerTypeId: 1, filerId: 1, displayText: "Committee A (ID: 1)"),
            new(id: 2, name: "Committee B", filerType: "Recipient Committee", filerTypeId: 1, filerId: 2, displayText: "Committee B (ID: 2")

        };

        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock
            .Setup(api => api.SearchFilers(query, filerTypeId, cancellationToken))
            .ReturnsAsync(expectedData);

        // Act
        var result = await _controller.SearchFilersByTypeAndQuery(
            query, filerTypeId, filingsApiMock.Object, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;

        Assert.That(jsonResult.Value, Is.EqualTo(expectedData));

        filingsApiMock.Verify(api =>
            api.SearchFilers(query, filerTypeId, cancellationToken),
            Times.Once);
    }

    [Test]
    public async Task SearchFilersByTypeAndQuery_ReturnsEmptyJsonResult_WhenModelStateIsInvalid()
    {
        // Arrange
        var query = "";
        var filerTypeId = 1L;
        var cancellationToken = CancellationToken.None;
        var filingsApiMock = new Mock<IFilingsApi>();

        _controller.ModelState.AddModelError("query", "Query is required");

        // Act
        var result = await _controller.SearchFilersByTypeAndQuery(
            query, filerTypeId, filingsApiMock.Object, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;
        Assert.That(jsonResult.Value, Is.EqualTo(new List<object>()));

        filingsApiMock.Verify(api =>
            api.SearchFilers(It.IsAny<string>(), It.IsAny<long>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
    #endregion

    #region Transaction Cancellation

    [Test]
    public async Task CancelTransaction_Invalid_ShouldReturnNotFound()
    {
        // Arrange
        var model = new LobbyingCampaignContributionViewModel();
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.CancelTransaction(model, FilingTypeModel.LobbyistEmployerReport.Name) as NotFoundResult;

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public async Task CancelTransaction_ValidModelState_ShouldRedirectToDisclosureIndex()
    {
        // Arrange
        var model = new LobbyingCampaignContributionViewModel
        {
            FilerId = 123,
            FilingId = 456
        };
        var reportType = FilingTypeModel.LobbyistEmployerReport.Name;

        // Act
        var result = await _controller.CancelTransaction(model, reportType) as RedirectToActionResult;

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Expected RedirectToActionResult for a valid request.");
            Assert.That(result!.ActionName, Is.EqualTo("Index"), "Should redirect to 'Index' action.");
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"), "Should redirect to 'Disclosure' controller.");

            Assert.That(result.RouteValues, Contains.Key("filerId"));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(model.FilerId));

            Assert.That(result.RouteValues, Contains.Key("filingId"));
            Assert.That(result.RouteValues["filingId"], Is.EqualTo(model.FilingId));

            Assert.That(result.RouteValues, Contains.Key("viewName"));
            Assert.That(result.RouteValues["viewName"], Is.EqualTo(FilingSummaryTypeModel.CampaignContributionSummary.Name));

            Assert.That(result.RouteValues, Contains.Key("reportType"));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(reportType));
        });
    }

    #endregion

    #region Search

    [Test]
    public async Task SearchFilerContactsByIdOrName_ReturnsJsonResult_WhenModelStateIsValid()
    {
        // Arrange
        var search = "J";
        var filerId = 1L;
        var cancellationToken = CancellationToken.None;
        var contactTypes = new List<string> { "Individual", "Organization" };
        var expectedData = new List<ContactSearchResultDto>
        {
            new("123 Main St", "City, State, 12345", 1L, 2L, "John Doe", 3L)
        };
        _contactsApiMock
            .Setup(api => api.SearchContactsByNameOrId(filerId, search, contactTypes, cancellationToken))
            .ReturnsAsync(expectedData);

        // Act
        var result = await _controller.SearchFilerContactsByIdOrName(
            _contactsApiMock.Object, search, filerId, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;

        Assert.That(jsonResult.Value, Is.EqualTo(expectedData));
    }

    [Test]
    public async Task SearchAllAgencies_ReturnsJsonResult_WhenModelStateIsValid()
    {
        // Arrange
        var search = "me";
        var cancellationToken = CancellationToken.None;

        var expectedAgencies = new List<Agency>
        {
            new(createdBy: 0, id: 1, modifiedBy: 0, name: "Department of Education", registrationAgencies: [] ),
            new(createdBy: 0, id: 2, modifiedBy: 0, name: "Environmental Protection Agency", registrationAgencies: [] )
        };

        _referenceDataApiMock
            .Setup(api => api.SearchAllAgencies(search, cancellationToken))
            .ReturnsAsync(expectedAgencies);

        // Act
        var result = await _controller.SearchAllAgencies(
            _referenceDataApiMock.Object, search, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;

        var expected = expectedAgencies
            .Select(a => new Dictionary<string, object?>
            {
                ["Id"] = a.Id,
                ["Name"] = a.Name
            }).ToList();

        var actual = ((IEnumerable<object>)jsonResult.Value!)
            .Select(x =>
            {
                var dict = new Dictionary<string, object?>();
                foreach (var prop in x.GetType().GetProperties())
                {
                    dict[prop.Name] = prop.GetValue(x);
                }
                return dict;
            }).ToList();

        Assert.That(actual, Is.EquivalentTo(expected));
    }

    [Test]
    public async Task SearchAllAgencies_ReturnsJsonResult_WhenModelStateIsInvalid()
    {
        // Arrange
        var search = "Invalid";

        _controller.ModelState.AddModelError("search", "Search term is required");

        // Act
        var result = await _controller.SearchAllAgencies(
            _referenceDataApiMock.Object, search, It.IsAny<CancellationToken>());

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;
        Assert.That(jsonResult?.Value?.ToString(), Is.EqualTo(new { }.ToString()));
    }

    [Test]
    public async Task SearchAllBills_ReturnsJsonResult_WhenModelStateIsValid()
    {
        // Arrange
        var search = "me";
        var cancellationToken = CancellationToken.None;
        Generated.BillHouse billhouse1 = new(1, "billhouse 1");
        Generated.BillHouse billhouse2 = new(2, "billhouse 2");

        var expectedBills = new List<Bill>
        {
            new(createdBy: 0, id: 1, modifiedBy: 0, billHouseId: 1, billHouse: billhouse1, title: "Department of Education", number: "ABC-123" ),
            new(createdBy: 0, id: 2, modifiedBy: 0, billHouseId: 2, billHouse: billhouse2, title: "Environmental Protection Agency", number: "ABC-123" )
        };

        _ = _referenceDataApiMock
            .Setup(api => api.SearchAllBills(search, cancellationToken))
            .ReturnsAsync(expectedBills);

        // Act
        var result = await _controller.SearchAllBills(
            _referenceDataApiMock.Object, search, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;

        var expected = expectedBills
            .Select(a => new Dictionary<string, object?>
            {
                ["Id"] = a.Id,
                ["BillHouseId"] = a.BillHouseId,
                ["Title"] = a.Title,
                ["Number"] = a.Number
            }).ToList();

        var actual = ((IEnumerable<object>)jsonResult.Value!)
            .Select(x =>
            {
                var dict = new Dictionary<string, object?>();
                foreach (var prop in x.GetType().GetProperties())
                {
                    dict[prop.Name] = prop.GetValue(x);
                }
                return dict;
            }).ToList();

        Assert.That(actual, Is.EquivalentTo(expected));
    }

    [Test]
    public async Task SearchAllBills_ReturnsJsonResult_WhenModelStateIsInvalid()
    {
        // Arrange
        var search = "Invalid";

        _controller.ModelState.AddModelError("search", "Search term is required");

        // Act
        var result = await _controller.SearchAllBills(
            _referenceDataApiMock.Object, search, It.IsAny<CancellationToken>());

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;
        Assert.That(jsonResult?.Value?.ToString(), Is.EqualTo(new { }.ToString()));
    }

    [Test]
    public async Task SearchFilerContactsByIdOrName_ReturnsEmptyJsonResult_WhenModelStateIsInvalid()
    {
        // Arrange
        var search = "InvalidSearch";
        var filerId = 0L;
        var cancellationToken = CancellationToken.None;

        // Simulate an invalid model state
        _controller.ModelState.AddModelError("search", "Search term is required");

        // Act
        var result = await _controller.SearchFilerContactsByIdOrName(
            _contactsApiMock.Object, search, filerId, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;
        Assert.That(jsonResult?.Value?.ToString(), Is.EqualTo(new { }.ToString()));
    }

    [Test]
    public async Task SearchReportablePersonsByIdOrName_ReturnsJsonResult_WhenModelStateIsValid()
    {
        // Arrange
        var search = "J";
        var filerId = 1L;
        var contactId = 21L;
        var mockReportablePerson = new TransactionReportablePerson(
            agency: "Agency 1",
            agencyDescription: "Description for Agency 1",
            amount: 100.0,
            createdBy: 1,
            filerContact: null!,
            filerContactId: contactId,
            id: 1,
            modifiedBy: 2,
            name: "John Doe",
            officialPosition: "Treasurer",
            officialPositionDescription: "Treasurer Description",
            registration: null!,
            registrationId: 5,
            transactionId: 1,
            transaction: null!
        );

        var expectedData = new List<TransactionReportablePerson>
        {
            mockReportablePerson
        };

        _activityExpensesApiMock
            .Setup(api => api.SearchReportablePersonsByName(filerId, contactId, search, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedData);

        // Act
        var result = await _controller.SearchReportablePersonsByName(
            _activityExpensesApiMock.Object, search, filerId, contactId, It.IsAny<CancellationToken>());

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;

        Assert.That(jsonResult.Value, Is.EqualTo(expectedData));
    }

    [Test]
    public async Task SearchReportablePersonsByIdOrName_ReturnsEmptyJsonResult_WhenModelStateIsInvalid()
    {
        // Arrange
        var search = "Invalid";
        var filerId = 0L;
        var contactId = 21L;

        _controller.ModelState.AddModelError("search", "Search term is required");

        // Act
        var result = await _controller.SearchReportablePersonsByName(
            _activityExpensesApiMock.Object, search, filerId, contactId, It.IsAny<CancellationToken>());

        // Assert
        Assert.That(result, Is.InstanceOf<JsonResult>());
        var jsonResult = result;
        Assert.That(jsonResult?.Value?.ToString(), Is.EqualTo(new { }.ToString()));
    }

    #endregion

    #region PaymentToLobbyingCoalition

    [Test]
    public void PaymentToLobbyingCoalitionPage01_ShouldReturnViewWithModel()
    {
        // Arrange
        var filerId = 12345;
        var filingId = 1;
        var returnUrl = "http://example.com";

        // Act
        var result = _controller.PaymentToLobbyingCoalitionPage01(filerId, filingId, returnUrl);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult?.Model as PaymentMadeToLobbyingCoalitionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.FilerId, Is.EqualTo(filerId), "Expected FilerId to match the input filerId");
            Assert.That(model.ReturnUrl, Is.EqualTo(returnUrl), "Expected ReturnUrl to match the input returnUrl");
        });
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage02_ShouldReturnViewWithModelWithContactIdAsync()
    {
        // Arrange
        var filerId = 12345;
        var filingId = 1;
        var returnUrl = "http://example.com";
        var contactId = 1;
        var registrationFilingId = 0;
        var cancellationToken = CancellationToken.None;
        var contactDetailMock = new ContactItemResponse(1
            , "Test City"
            , "United States"
            , new List<EmailAddress>() { new("<EMAIL>", 1, 1, "test", "test") }
            , filerId
            , contactId
            , new List<PhoneNumber>() { new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Phone"), new(null, "+1", 1, 1, "123", 1, false, false, 1, "1234567890", 1, "Fax") }
            , "HI"
            , "123 Sample St."
            , "APT A"
            , 2
            , true
            , new List<WorkFlowError>()
            , returnUrl
            , "12345");
        var faxNumber = contactDetailMock.PhoneNumbers.First(x => x.Type == "Fax");
        var phoneNumber = contactDetailMock.PhoneNumbers.First(x => x.Type == "Phone");
        var expectedModel = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = filerId,
            ReturnUrl = returnUrl,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            Contact = new GenericContactViewModel()
            {
                PhoneNumber = phoneNumber.Number,
                PhoneNumberCountryCode = phoneNumber.CountryCode,
                FaxNumber = faxNumber.Number,
                FaxNumberCountryCode = faxNumber.CountryCode,
                EmailAddress = contactDetailMock.EmailAddresses[0].Email,
                Country = contactDetailMock.Country,
                Street = contactDetailMock.Street,
                Street2 = contactDetailMock.Street2,
                City = contactDetailMock.City,
                State = contactDetailMock.State,
                ZipCode = contactDetailMock.ZipCode,
            },
            LobbyingCoalitionName = contactDetailMock.DisplayName(),
        };
        _transactionCtlSvc
            .Setup(api => api.GetContactDetailForViewModel(It.IsAny<PaymentMadeToLobbyingCoalitionViewModel>(), cancellationToken))
            .ReturnsAsync(expectedModel);

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage02Async(filerId
            , contactId
            , registrationFilingId
            , filingId
            , returnUrl
            , cancellationToken);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult?.Model as PaymentMadeToLobbyingCoalitionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.FilerId, Is.EqualTo(filerId), "Expected FilerId to match the input filerId");
            Assert.That(model.ReturnUrl, Is.EqualTo(returnUrl), "Expected ReturnUrl to match the input returnUrl");
            Assert.That(model.ContactId, Is.EqualTo(contactId), "Expected ContactId to match the input contactId");
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId), "Expected RegistrationFilingId to match the input registrationFilingId");
            Assert.That(model.LobbyingCoalitionName, Is.EqualTo(expectedModel.LobbyingCoalitionName), "Expected LobbyingCoalitionName to match the returned LobbyingCoalitionName");
            Assert.That(model.Contact!.PhoneNumber, Is.EqualTo(expectedModel.Contact.PhoneNumber), "Expected Phone Number to match the returned Phone Number");
            Assert.That(model.Contact!.PhoneNumberCountryCode, Is.EqualTo(expectedModel.Contact.PhoneNumberCountryCode), "Expected Phone Number Country Code to match the returned Phone Number Country Code");
            Assert.That(model.Contact!.FaxNumber, Is.EqualTo(expectedModel.Contact.FaxNumber), "Expected Fax Number to match the returned Fax Number");
            Assert.That(model.Contact!.FaxNumberCountryCode, Is.EqualTo(expectedModel.Contact.FaxNumberCountryCode), "Expected Fax Number Country Code to match the returned Fax Number Country Code");
            Assert.That(model.Contact!.EmailAddress, Is.EqualTo(expectedModel.Contact.EmailAddress), "Expected Email Address to match the returned Email Address");
            Assert.That(model.Contact!.Country, Is.EqualTo(expectedModel.Contact.Country), "Expected Country to match the returned Country");
            Assert.That(model.Contact!.Street, Is.EqualTo(expectedModel.Contact.Street), "Expected Street to match the returned Street");
            Assert.That(model.Contact!.Street2, Is.EqualTo(expectedModel.Contact.Street2), "Expected Street2 to match the returned Street2");
            Assert.That(model.Contact!.State, Is.EqualTo(expectedModel.Contact.State), "Expected State to match the returned State");
            Assert.That(model.Contact!.City, Is.EqualTo(expectedModel.Contact.City), "Expected City to match the returned City");
            Assert.That(model.Contact!.ZipCode, Is.EqualTo(expectedModel.Contact.ZipCode), "Expected ZipCode to match the returned ZipCode");
        });
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage02_ShouldReturnViewWithModelWithRegistrationFilingIdAsync()
    {
        // Arrange
        var filerId = 12345;
        var filingId = 1;
        var returnUrl = "http://example.com";
        var contactId = 0;
        var registrationFilingId = 1;
        var cancellationToken = CancellationToken.None;
        var addressList = new List<AddressDtoModel>
        {
            new("dummy city", "dummy country", "home","dummy state","123 dummy street", "APT 500", "work", "96183")
        };
        var phoneNumberList = new List<PhoneNumberDto>
        {
            new("+1", null, "987", 1, false, "1011231234", 1, false, "Phone"),
            new("+1", null, "678", 1, false, "2029871234", 1, false, "Fax"),
        };
        var lobbyistEmployerMock = new LobbyistEmployerResponseDto(addressList, 1, new List<RegistrationAgencyDto>(), "test", "test dsc", _dateNow, "<EMAIL>", "dummy employee", "developer", filerId, registrationFilingId, "test", "class", "portion", "type", false, 0, "interest", new List<LobbyingEmployerGroupMemberDto>(), "dummy name", "purpose", 1, 1, phoneNumberList, true, 1, 1);
        var phoneNumber = lobbyistEmployerMock.PhoneNumbers[0];
        var faxNumber = lobbyistEmployerMock.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax");
        var address = lobbyistEmployerMock.Addresses[0];
        var expectedModel = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = filerId,
            ReturnUrl = returnUrl,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            Contact = new GenericContactViewModel()
            {
                PhoneNumber = phoneNumber?.Number ?? string.Empty,
                PhoneNumberCountryCode = phoneNumber?.CountryCode ?? string.Empty,
                FaxNumber = faxNumber?.Number ?? string.Empty,
                FaxNumberCountryCode = faxNumber?.CountryCode ?? string.Empty,
                EmailAddress = lobbyistEmployerMock.Email,
                Country = address?.Country ?? string.Empty,
                Street = address?.Street ?? string.Empty,
                Street2 = address?.Street2 ?? string.Empty,
                City = address?.City ?? string.Empty,
                State = address?.State ?? string.Empty,
                ZipCode = address?.Zip ?? string.Empty,
            },
            LobbyingCoalitionName = lobbyistEmployerMock.Name,
        };
        _transactionCtlSvc
            .Setup(api => api.GetContactDetailForViewModel(It.IsAny<PaymentMadeToLobbyingCoalitionViewModel>(), cancellationToken))
            .ReturnsAsync(expectedModel);

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage02Async(filerId
            , contactId
            , registrationFilingId
            , filingId
            , returnUrl
            , cancellationToken);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult?.Model as PaymentMadeToLobbyingCoalitionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.FilerId, Is.EqualTo(filerId), "Expected FilerId to match the input filerId");
            Assert.That(model.ReturnUrl, Is.EqualTo(returnUrl), "Expected ReturnUrl to match the input returnUrl");
            Assert.That(model.ContactId, Is.EqualTo(contactId), "Expected ContactId to match the input contactId");
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId), "Expected RegistrationFilingId to match the input registrationFilingId");
            Assert.That(model.LobbyingCoalitionName, Is.EqualTo(expectedModel.LobbyingCoalitionName), "Expected LobbyingCoalitionName to match the returned LobbyingCoalitionName");
            Assert.That(model.Contact!.PhoneNumber, Is.EqualTo(expectedModel.Contact.PhoneNumber), "Expected Phone Number to match the returned Phone Number");
            Assert.That(model.Contact!.PhoneNumberCountryCode, Is.EqualTo(expectedModel.Contact.PhoneNumberCountryCode), "Expected Phone Number Country Code to match the returned Phone Number Country Code");
            Assert.That(model.Contact!.FaxNumber, Is.EqualTo(expectedModel.Contact.FaxNumber), "Expected Fax Number to match the returned Fax Number");
            Assert.That(model.Contact!.FaxNumberCountryCode, Is.EqualTo(expectedModel.Contact.FaxNumberCountryCode), "Expected Fax Number Country Code to match the returned Fax Number Country Code");
            Assert.That(model.Contact!.EmailAddress, Is.EqualTo(expectedModel.Contact.EmailAddress), "Expected Email Address to match the returned Email Address");
            Assert.That(model.Contact!.Country, Is.EqualTo(expectedModel.Contact.Country), "Expected Country to match the returned Country");
            Assert.That(model.Contact!.Street, Is.EqualTo(expectedModel.Contact.Street), "Expected Street to match the returned Street");
            Assert.That(model.Contact!.Street2, Is.EqualTo(expectedModel.Contact.Street2), "Expected Street2 to match the returned Street2");
            Assert.That(model.Contact!.State, Is.EqualTo(expectedModel.Contact.State), "Expected State to match the returned State");
            Assert.That(model.Contact!.City, Is.EqualTo(expectedModel.Contact.City), "Expected City to match the returned City");
            Assert.That(model.Contact!.ZipCode, Is.EqualTo(expectedModel.Contact.ZipCode), "Expected ZipCode to match the returned ZipCode");
        });
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage02Submit_ShouldReturnPaymentMadeView_WhenFormActionCancel()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            FilingId = 123,
            FilerId = 456
        };

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage02Async(model, FormAction.Cancel, cancellationToken) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.RouteValues!["viewName"], Is.EqualTo(FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name));
            Assert.That(result.RouteValues!["reportType"], Is.EqualTo(FilingTypeModel.LobbyistEmployerReport.Name));
        });
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage02Submit_ShouldThrowNotFoundError_WhenModelStateInvalid()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;

        _controller.ModelState.AddModelError("key", "Invalid model state");

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage02Async(new PaymentMadeToLobbyingCoalitionViewModel(), null, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage02Submit_ShouldRedirectToActionPage03_WhenRegistrationFilingIdValid()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var registrationFilingId = 1;

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage02Async(new PaymentMadeToLobbyingCoalitionViewModel()
        {
            RegistrationFilingId = registrationFilingId,
        }, null, cancellationToken) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("PaymentToLobbyingCoalitionPage03"));
        });
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage02Submit_ShouldCreateNewContactAndRedirectToActionPage03_WhenContactIdEqualZero()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var registrationFilingId = 0;
        var contactId = 0;
        var filerId = 1;
        var addressList = new List<AddressDtoModel>
        {
            new("dummy city", "dummy country", "home","dummy state","123 dummy street", "APT 500", "work", "96183")
        };
        var phoneNumberList = new List<PhoneNumberDto>
        {
            new("+1", null, "987", 1, false, "1011231234", 1, false, "Phone"),
            new("+1", null, "678", 1, false, "2029871234", 1, false, "Fax"),
        };
        var lobbyistEmployerMock = new LobbyistEmployerResponseDto(addressList, 1, new List<RegistrationAgencyDto>(), "test", "test dsc", _dateNow, "<EMAIL>", "dummy employee", "developer", filerId, registrationFilingId, "test", "class", "portion", "type", false, 0, "interest", new List<LobbyingEmployerGroupMemberDto>(), "dummy name", "purpose", 1, 1, phoneNumberList, true, 1, 1);
        var phoneNumber = lobbyistEmployerMock.PhoneNumbers[0];
        var faxNumber = lobbyistEmployerMock.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax");
        var address = lobbyistEmployerMock.Addresses[0];
        var model = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            Contact = new GenericContactViewModel()
            {
                PhoneNumber = phoneNumber?.Number ?? string.Empty,
                PhoneNumberCountryCode = phoneNumber?.CountryCode ?? string.Empty,
                FaxNumber = faxNumber?.Number ?? string.Empty,
                FaxNumberCountryCode = faxNumber?.CountryCode ?? string.Empty,
                EmailAddress = lobbyistEmployerMock.Email,
                Country = address?.Country ?? string.Empty,
                Street = address?.Street ?? string.Empty,
                Street2 = address?.Street2 ?? string.Empty,
                City = address?.City ?? string.Empty,
                State = address?.State ?? string.Empty,
                ZipCode = address?.Zip ?? string.Empty,
            },
            LobbyingCoalitionName = lobbyistEmployerMock.Name,
        };
        var newContactId = 1;


        _transactionCtlSvc
            .Setup(api => api.HandlePaymentToLobbyingCoalitionPage02Submit(model, cancellationToken))
            .ReturnsAsync(newContactId);

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage02Async(model, null, cancellationToken) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("PaymentToLobbyingCoalitionPage03"));
            Assert.That(result?.RouteValues?.First(x => x.Key == "ContactId").Value, Is.EqualTo(newContactId));
        });
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage02Submit_ShouldUpdateContactAndRedirectToActionPage03_WhenContactIdValid()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var registrationFilingId = 0;
        var contactId = 1;
        var filerId = 1;
        var addressList = new List<AddressDtoModel>
        {
            new("dummy city", "dummy country", "home","dummy state","123 dummy street", "APT 500", "work", "96183")
        };
        var phoneNumberList = new List<PhoneNumberDto>
        {
            new("+1", null, "987", 1, false, "1011231234", 1, false, "Phone"),
            new("+1", null, "678", 1, false, "2029871234", 1, false, "Fax"),
        };
        var lobbyistEmployerMock = new LobbyistEmployerResponseDto(addressList, 1, new List<RegistrationAgencyDto>(), "test", "test dsc", _dateNow, "<EMAIL>", "dummy employee", "developer", filerId, registrationFilingId, "test", "class", "portion", "type", false, 0, "interest", new List<LobbyingEmployerGroupMemberDto>(), "dummy name", "purpose", 1, 1, phoneNumberList, true, 1, 1);
        var phoneNumber = lobbyistEmployerMock.PhoneNumbers[0];
        var faxNumber = lobbyistEmployerMock.PhoneNumbers.FirstOrDefault(x => x.Type == "Fax");
        var address = lobbyistEmployerMock.Addresses[0];
        var model = new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = filerId,
            ContactId = contactId,
            RegistrationFilingId = registrationFilingId,
            Contact = new GenericContactViewModel()
            {
                PhoneNumber = phoneNumber?.Number ?? string.Empty,
                PhoneNumberCountryCode = phoneNumber?.CountryCode ?? string.Empty,
                FaxNumber = faxNumber?.Number ?? string.Empty,
                FaxNumberCountryCode = faxNumber?.CountryCode ?? string.Empty,
                EmailAddress = lobbyistEmployerMock.Email,
                Country = address?.Country ?? string.Empty,
                Street = address?.Street ?? string.Empty,
                Street2 = address?.Street2 ?? string.Empty,
                City = address?.City ?? string.Empty,
                State = address?.State ?? string.Empty,
                ZipCode = address?.Zip ?? string.Empty,
            },
            LobbyingCoalitionName = lobbyistEmployerMock.Name,
        };


        _transactionCtlSvc
            .Setup(api => api.HandlePaymentToLobbyingCoalitionPage02Submit(model, cancellationToken))
            .ReturnsAsync(contactId);

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage02Async(model, null, cancellationToken) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        Assert.Multiple(() =>
        {
            Assert.That(result?.ActionName, Is.EqualTo("PaymentToLobbyingCoalitionPage03"));
            Assert.That(result?.RouteValues?.First(x => x.Key == "ContactId").Value, Is.EqualTo(contactId));
        });
    }

    [Test]
    public void PaymentToLobbyingCoalitionPage03_ShouldThrowNotFoundError_WhenModelStateInvalid()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;

        _controller.ModelState.AddModelError("key", "Invalid model state");

        // Act
        var result = _controller.PaymentToLobbyingCoalitionPage03(0, 0, 0, 0, null);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public void PaymentToLobbyingCoalitionPage03_ShouldReturnViewOfPage03_WhenModelStateValid()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;

        // Act
        var result = _controller.PaymentToLobbyingCoalitionPage03(1, 1, 1, 1, "https://example.com");

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var model = viewResult?.Model as PaymentMadeToLobbyingCoalitionViewModel;
        Assert.That(model, Is.Not.Null);
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage03_ValidModelWithId_CallsEditAndRedirects()
    {
        // Arrange
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            Id = 1,
            Amount = 123.45m,
            ContactId = 10,
            FilerId = 20,
            FilingId = 30,
            RegistrationFilingId = 40,
            ReturnUrl = "/return-url"
        };

        _controller.ModelState.Clear(); // Valid model

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage03(model, _lobbyistEmployerCoalitionApiMock.Object);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage03Submit_ShouldReturnPaymentMadeView_WhenFormActionCancel()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            FilingId = 123,
            FilerId = 456
        };

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage03(model, _lobbyistEmployerCoalitionApiMock.Object, FormAction.Cancel, cancellationToken) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.RouteValues!["viewName"], Is.EqualTo(FilingSummaryTypeModel.ToLobbyingCoalitionSummary.Name));
            Assert.That(result.RouteValues!["reportType"], Is.EqualTo(FilingTypeModel.LobbyistEmployerReport.Name));
        });
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage03Submit_ShouldThrowNotFoundError_WhenModelStateInvalid()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;

        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            FilingId = 123,
            FilerId = 456
        };

        _controller.ModelState.AddModelError("key", "Invalid model state");

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage03(model, _lobbyistEmployerCoalitionApiMock.Object, null, cancellationToken);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage03Submit_ShouldRedirectToReturnUrl_WhenCreatingTransactionSucceed_WithContactId()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var model = new PaymentMadeToLobbyingCoalitionRequestDto(Convert.ToDouble(10)
                , 1
                , 1
                , 1
                , null
                , null);
        var modelState = new ModelStateDictionary();
        var responseMock = new TransactionResponseDto(1, true, new List<WorkFlowError>());

        _transactionCtlSvc
            .Setup(api => api.HandlePaymentToLobbyingCoalitionPage03Submit(model, modelState, cancellationToken))
            .ReturnsAsync(responseMock);

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage03(new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = 1,
            ReturnUrl = "https://example.com",
            FilingId = 1,
            Amount = 100,
            RegistrationFilingId = 0,
            ContactId = 1,
        }, _lobbyistEmployerCoalitionApiMock.Object, null, cancellationToken);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage03Submit_ShouldShowError_WhenAmountIsNull()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var responseMock = new TransactionResponseDto(1, false, new List<WorkFlowError>() { new("1", "1", "Period Amount", "Period Amount is Invalid") });

        _transactionCtlSvc
            .Setup(api => api.HandlePaymentToLobbyingCoalitionPage03Submit(It.IsAny<PaymentMadeToLobbyingCoalitionRequestDto>(), It.IsAny<ModelStateDictionary>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(responseMock);
        _localizerMock.Setup(l => l[ResourceConstants.FieldIsRequired])
            .Returns(new LocalizedString(ResourceConstants.FieldIsRequired, "{0} is required."));

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage03(new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            Amount = null,
            RegistrationFilingId = 1,
            ContactId = 0,
            ReturnUrl = "https://example.com"
        }, _lobbyistEmployerCoalitionApiMock.Object, null, cancellationToken);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var viewModel = viewResult?.Model as PaymentMadeToLobbyingCoalitionViewModel;
        Assert.That(viewModel, Is.Not.Null);
    }

    [Test]
    public async Task PaymentToLobbyingCoalitionPage03Submit_ShouldRedirectToReturnUrl_WhenCreatingTransactionSucceed_WithRegistrationId()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var responseMock = new TransactionResponseDto(1, true, new List<WorkFlowError>());

        _transactionCtlSvc
            .Setup(api => api.HandlePaymentToLobbyingCoalitionPage03Submit(It.IsAny<PaymentMadeToLobbyingCoalitionRequestDto>(), It.IsAny<ModelStateDictionary>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(responseMock);

        // Act
        var result = await _controller.PaymentToLobbyingCoalitionPage03(new PaymentMadeToLobbyingCoalitionViewModel()
        {
            FilerId = 1,
            ReturnUrl = "https://example.com",
            FilingId = 1,
            Amount = 100,
            RegistrationFilingId = 1,
            ContactId = 0,
        }, _lobbyistEmployerCoalitionApiMock.Object, null, cancellationToken);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task InitializePaymentToLobbyingCoalition_ValidRequest_RedirectsToEditAction()
    {
        // Arrange
        var transactionId = 123;
        var cancellationToken = CancellationToken.None;
        var reportType = "test";
        var filingId = 1;
        var filingSummaryId = 1;

        var mockResponse = new PaymentMadeToLobbyingCoalitionResponse(100, "John", 1, 100, 1, filingId, transactionId, null);

        _lobbyistEmployerCoalitionApiMock
            .Setup(api => api.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse);

        // Act
        var result = await _controller.InitializePaymentToLobbyingCoalition(
            reportType,
            filingId,
            transactionId,
            filingSummaryId,
            _lobbyistEmployerCoalitionApiMock.Object,
            cancellationToken);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("EditPaymentToLobbyingCoalition"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Transaction"));
        });
    }

    [Test]
    public async Task InitializePaymentToLobbyingCoalition_ApiThrowsException_ReturnsNotFound()
    {
        // Arrange
        var transactionId = 123L;
        var cancellationToken = CancellationToken.None;
        var reportType = "type";
        var filingId = 1;
        var filingSummaryId = 1;

        var notFoundResponse = new HttpResponseMessage(HttpStatusCode.NotFound);
        var apiException = await ApiException.Create(
            new HttpRequestMessage(HttpMethod.Get, "http://test.com/api"),
            HttpMethod.Get,
            notFoundResponse,
            new RefitSettings());

        _ = _lobbyistEmployerCoalitionApiMock
                .Setup(api => api.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(It.IsAny<long>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(apiException);

        // Act
        var result = await _controller.InitializePaymentToLobbyingCoalition(
            reportType,
            filingId,
            transactionId,
            filingSummaryId,
            _lobbyistEmployerCoalitionApiMock.Object,
            cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EditPaymentToLobbyingCoalition_Get_ValidModel_ReturnsView()
    {
        // Arrange
        var contactId = 10;
        var regFilingId = 100;
        var id = 1001;
        var filerId = 2;
        var filingId = 3;
        var filingSummaryId = 1;
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            Id = id,
            FilerId = filerId,
            FilingId = filingId,
            FilingSummaryId = filingSummaryId
        };

        _transactionCtlSvc
            .Setup(api => api.GetContactDetailForViewModel(model, It.IsAny<CancellationToken>()))
            .ReturnsAsync(model);

        // Act
        var result = await _controller.EditPaymentToLobbyingCoalition(
            id: id,
            filerId: filerId,
            contactId: contactId,
            registrationFilingId: regFilingId,
            filingId: filingId,
            filingSummaryId: filingSummaryId
        );

        // Assert
        var view = result as ViewResult;
        Assert.That(view, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(view.ViewName, Is.EqualTo("PaymentToLobbyingCoalition/Page02"));
        });
    }

    [Test]
    public async Task EditPaymentToLobbyingCoalition_Get_InvalidModel_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid");

        // Act
        var result = await _controller.EditPaymentToLobbyingCoalition(1, 2, 3, 4, 5, 1);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EditPaymentToLobbyingCoalition_Post_WithRegistrationFilingId_RedirectsToPage03()
    {
        // Arrange
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            Id = 1,
            FilerId = 2,
            RegistrationFilingId = 3,
            ContactId = 4,
            FilingId = 5,
            ReturnUrl = "url"
        };

        // Act
        var result = await _controller.EditPaymentToLobbyingCoalition(model);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("EditPaymentToLobbyingCoalitionPage03"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Transaction"));
        });
    }

    [Test]
    public async Task EditPaymentToLobbyingCoalition_Post_NoRegistrationFilingId_SuccessfulRedirect()
    {
        // Arrange
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            Id = 1,
            FilerId = 2,
            RegistrationFilingId = 0,
            ContactId = 0,
            FilingId = 3,
            ReturnUrl = "url"
        };

        _transactionCtlSvc
            .Setup(api => api.HandlePaymentToLobbyingCoalitionPage02Submit(model, It.IsAny<CancellationToken>()))
            .ReturnsAsync(77);

        // Act
        var result = await _controller.EditPaymentToLobbyingCoalition(model);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.That(redirect.ActionName, Is.EqualTo("EditPaymentToLobbyingCoalitionPage03"));
    }

    [Test]
    public async Task EditPaymentToLobbyingCoalition_Post_Exception_ReturnsViewWithError()
    {
        // Arrange
        var model = new PaymentMadeToLobbyingCoalitionViewModel
        {
            Id = 1,
            FilerId = 2,
            RegistrationFilingId = 0,
            ContactId = 4,
            FilingId = 5,
            ReturnUrl = "url"
        };

        _ = _transactionCtlSvc
            .Setup(api => api.HandlePaymentToLobbyingCoalitionPage02Submit(model, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidProgramException("something went wrong"));

        // Act
        var result = await _controller.EditPaymentToLobbyingCoalition(model);

        // Assert
        var view = result as ViewResult;
        Assert.That(view, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(view.ViewName, Is.EqualTo("EditPaymentToLobbyingCoalition"));
        });
    }

    [Test]
    public async Task EditPaymentToLobbyingCoalitionPage03_ValidModel_ReturnsViewWithModel()
    {
        // Arrange
        var id = 1;
        var filerId = 2;
        var contactId = 3;
        var registrationFilingId = 4;
        var filingId = 5;
        var returnUrl = "/return";

        var mockResponse = new PaymentMadeToLobbyingCoalitionResponse(123, "John", 1, 100, 1, filingId, id, null);

        _lobbyistEmployerCoalitionApiMock
            .Setup(api => api.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse);

        // Ensure valid model state
        _controller.ModelState.Clear();

        // Act
        var result = await _controller.EditPaymentToLobbyingCoalitionPage03(
            id,
            filerId,
            contactId,
            registrationFilingId,
            filingId,
            _lobbyistEmployerCoalitionApiMock.Object,
            returnUrl
        );

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("PaymentToLobbyingCoalition/Page03"));

        var model = viewResult.Model as PaymentMadeToLobbyingCoalitionViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(id));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(model.ReturnUrl, Is.EqualTo(returnUrl));
            Assert.That(model.Amount, Is.EqualTo(mockResponse.AmountThisPeriod));
        });
    }


    [Test]
    public async Task EditPaymentReceivedByLobbyingCoalition_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "Invalid model state");
        string reportType = "TestReport";
        string filingId = "123";
        string filerId = "456";
        long transactionId = 789;

        // Act
        var result = await _controller.EditPaymentReceivedByLobbyingCoalition(
            reportType, filingId, filerId, transactionId, _lobbyistEmployerCoalitionApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EditPaymentReceivedByLobbyingCoalition_ValidInput_RedirectsToEditTransactionContact()
    {
        // Arrange
        string reportType = "TestReport";
        string filingId = "123";
        string filerId = "456";
        long transactionId = 789;
        var contact = new ContactResponseDto(new List<AddressDto>(), new List<EmailAddress>(), 1, 1, new List<PhoneNumberDto>(), 1);
        var transaction = new PaymentReceiveLobbyingCoalitionResponse(10, "", contact, 1, 1);

        _lobbyistEmployerCoalitionApiMock.Setup(api => api.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId, CancellationToken.None))
            .ReturnsAsync(transaction);

        // Act
        var redirectResult = await _controller.EditPaymentReceivedByLobbyingCoalition(
            reportType, filingId, filerId, transactionId, _lobbyistEmployerCoalitionApiMock.Object, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("EnterContact"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("CoalitionReceivedTransaction"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
        });
    }

    [Test]
    public async Task EditPaymentReceivedByLobbyingCoalition_ApiThrowsException_ReturnsNotFound()
    {
        // Arrange
        string reportType = "TestReport";
        string filingId = "123";
        string filerId = "456";
        long transactionId = 789;
        _lobbyistEmployerCoalitionApiMock.Setup(api => api.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId, CancellationToken.None))
            .ThrowsAsync(new KeyNotFoundException("API error"));

        // Act
        var result = await _controller.EditPaymentReceivedByLobbyingCoalition(
            reportType, filingId, filerId, transactionId, _lobbyistEmployerCoalitionApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    #endregion

    #region OtherPayment
    [Test]
    public async Task EditOtherPaymentToInfluenceTransaction_InvalidModelState_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("error", "Invalid model state");
        string filingId = "123";
        string filerId = "456";
        long transactionId = 789;

        // Act
        var result = await _controller.EditOtherPaymentToInfluenceTransaction(filingId, filerId, transactionId, _transactionsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EditOtherPaymentToInfluenceTransaction_ValidInput_RedirectsToEditTransactionContact()
    {
        // Arrange
        string filingId = "123";
        string filerId = "456";
        long transactionId = 789;
        var contactResponse = new ContactItemResponse
            (
                1,
                "City",
                "USA",
                null,
                1,
                1,
                   null,
                "HI",
                "Street1",
                "Street2",
                2,
                true,
                null,
                "www.test.com",
                "12345"
            );
        var transactionResponse = new OtherInfluencePaymentDto
            (
                new List<ActionsLobbiedDto>()
                {
                    new(string.Empty, string.Empty, 1, 1)
                },
                true,
                true,
                true,
                100,
                [],
                1,
                1,
                1,
                1,
                string.Empty,
                string.Empty,
                1,
                [],
                DateTime.Now
            );

        _transactionsApiMock.Setup(api => api.GetOtherPaymentsToInfluenceTransactionById(transactionId, CancellationToken.None))
            .ReturnsAsync(transactionResponse);

        // Act
        var redirectResult = await _controller.EditOtherPaymentToInfluenceTransaction(
            filingId, filerId, transactionId, _transactionsApiMock.Object, CancellationToken.None) as RedirectToActionResult;

        // Assert
        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Edit"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Contact"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
        });
    }

    [Test]
    public async Task EditOtherPaymentToInfluenceTransaction_ApiThrowsException_ReturnsNotFound()
    {
        // Arrange
        string filingId = "123";
        string filerId = "456";
        long transactionId = 789;
        _transactionsApiMock.Setup(api => api.GetOtherPaymentsToInfluenceTransactionById(transactionId, CancellationToken.None))
            .ThrowsAsync(new KeyNotFoundException("API error"));

        // Act
        var result = await _controller.EditOtherPaymentToInfluenceTransaction(
            filingId, filerId, transactionId, _transactionsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }
    [Test]
    public async Task OtherPaymentPayee_Invalid_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.NewOtherPaymentPayee(1, 1, "/return", "Sample report type", _contactsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task OtherPaymentPayee_ShouldReturnViewWithContacts()
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;
        var returnUrl = "/return";
        var reportType = "Sample report type";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var contacts = new List<ContactItemResponse> { new(1, "Test City", "US", emails, filerId, 1, phoneNumbers, "HI", "Street", "Street 2", 2, true, workflowErrors, "", "12345") };

        var model = new OtherPaymentViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            Contacts = contacts,
            ReturnUrl = returnUrl,
        };

        _ = _contactsApiMock.Setup(x => x.GetContacts(It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(contacts);

        // Act
        var result = await _controller.NewOtherPaymentPayee(1, 1, "/return", reportType, _contactsApiMock.Object);

        // Assert
        var viewResult = result as ViewResult;
        var resultModel = viewResult!.Model as OtherPaymentViewModel;

        Assert.That(viewResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(viewResult.ViewName, Is.EqualTo("NewPayee"));
            Assert.That(viewResult.Model, Is.InstanceOf<OtherPaymentViewModel>());
            Assert.That(resultModel!.FilerId, Is.EqualTo(filerId));
            Assert.That(resultModel!.FilingId, Is.EqualTo(filingId));
            Assert.That(resultModel!.Contacts, Is.EqualTo(contacts));
            Assert.That(resultModel!.ReturnUrl, Is.EqualTo(returnUrl));
            Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(reportType));
        });
    }

    [Test]
    public async Task OtherPayment_Invalid_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.NewOtherPayment(1, 1, 1, _transactionsApiMock.Object, "sample report type", "/return");

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task OtherPayment_ShouldReturnNewOtherPaymentView()
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;
        var contactId = 1;
        var reportType = "sample report type";
        var returnUrl = "/return";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var transactionId = 1;

        var administrativeActions = new List<AdministrativeActionViewModel>
        {
            new() { Id = 101, Name = "Agency 1", AdministrativeAction = "Action 1", AgencyId = 1 },
            new() { Id = 102, Name = "Agency 2", AdministrativeAction = "Action 2", AgencyId = 2 }
        };

        var serializedActions = JsonConvert.SerializeObject(administrativeActions);
        _controller.TempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
    {
        { "AdministrativeActionTempDataKey", serializedActions }
    };

        var paymentCodes = new List<Generated.PaymentCodeRefResponse>
    {
        new ("Advertising (A)", 1, "Advertising"),
        new ("Research (R)", 2, "Research"),
        new ("Other (O)", 3, "Other")
    };
        var paymentCodeDictionary = new Dictionary<string, string>
    {
        { "1", "Advertising (A)" },
        { "2", "Research (R)" },
        { "3", "Other (O)" }
    };

        var cumulativeAmount = new Generated.CumulativeAmountResponse(filingId, 3000);

        _ = _referenceDataApiMock.Setup(api => api.GetAllPaymentCodes(It.IsAny<CancellationToken>())).ReturnsAsync(paymentCodes);

        _ = _transactionsApiMock.Setup(api => api.GetOtherPaymentsCumulativeAmountForFilingAndContact(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(cumulativeAmount);

        var contactResponse = new ContactItemResponse
        (
            1,
            "City",
            "USA",
            emails,
            1,
            1,
            phoneNumbers,
            "HI",
            "Street1",
            "Street2",
            2,
            true,
            workflowErrors,
            "www.test.com",
            "12345"
        );
        var transactionResponse = new OtherInfluencePaymentDto
            (
                new List<ActionsLobbiedDto>()
                {
                    new(string.Empty, string.Empty, 1, 1)
                },
                true,
                true,
                true,
                100,
                [],
                1,
                1,
                1,
                1,
                string.Empty,
                string.Empty,
                1,
                [],
                DateTime.Now
            );

        _contactsApiMock.Setup(x => x.GetContact(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(contactResponse);

        _transactionsApiMock.Setup(x => x.GetOtherPaymentsToInfluenceTransactionById(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(transactionResponse);

        // Act
        var result = await _controller.NewOtherPayment(filerId, filingId, contactId, _transactionsApiMock.Object, reportType, returnUrl, transactionId) as ViewResult;
        var resultModel = result!.Model as OtherPaymentViewModel;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("NewOtherPayment"));
            Assert.That(result.Model, Is.InstanceOf<OtherPaymentViewModel>());
            Assert.That(resultModel!.CumulativeAmount, Is.EqualTo(3000m));
            Assert.That(resultModel!.FilerId, Is.EqualTo(filerId));
            Assert.That(resultModel!.FilingId, Is.EqualTo(filingId));
            Assert.That(resultModel!.ReturnUrl, Is.EqualTo(returnUrl));
            Assert.That(resultModel!.PaymentCodes, Is.EqualTo(paymentCodeDictionary));
            Assert.That(resultModel!.Contacts![0], Is.EqualTo(contactResponse));
            Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(reportType));

            // NEW: Validate AdministrativeActionGridModel
            Assert.That(resultModel!.AdministrativeActionGridModel, Is.Not.Null);
            Assert.That(resultModel.AdministrativeActionGridModel?.GridId, Is.EqualTo("AdministrativeAction"));
        });
    }

    [Test]
    public async Task OtherPayment_ShouldReturnNewOtherPaymentView_CumulativeNotFound()
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;
        var contactId = 1;
        var reportType = "sample report type";
        var returnUrl = "/return";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();

        var administrativeActions = new List<AdministrativeActionViewModel>
        {
            new() { Id = 101, Name = "Agency 1", AdministrativeAction = "Action 1", AgencyId = 1 },
            new() { Id = 102, Name = "Agency 2", AdministrativeAction = "Action 2", AgencyId = 2 }
        };

        var serializedActions = JsonConvert.SerializeObject(administrativeActions);
        _controller.TempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            { "AdministrativeActionTempDataKey", serializedActions }
        };

        var paymentCodes = new List<Generated.PaymentCodeRefResponse>
        {
            new ("Advertising (A)", 1, "Advertising"),
            new ("Research (R)", 2, "Research"),
            new ("Other (O)", 3, "Other")
        };
        var paymentCodeDictionary = new Dictionary<string, string>
        {
            { "1", "Advertising (A)" },
            { "2", "Research (R)" },
            { "3", "Other (O)" }
        };

        var notFoundResponse = new HttpResponseMessage(HttpStatusCode.NotFound);
        var apiException = await ApiException.Create(
            new HttpRequestMessage(HttpMethod.Get, "http://test.com/api"),
            HttpMethod.Get,
            notFoundResponse,
            new RefitSettings());

        _transactionsApiMock
            .Setup(api => api.GetOtherPaymentsCumulativeAmountForFilingAndContact(
                filingId,
                contactId,
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        _ = _referenceDataApiMock.Setup(api => api.GetAllPaymentCodes(It.IsAny<CancellationToken>())).ReturnsAsync(paymentCodes);

        var contactResponse = new ContactItemResponse
        (
            1,
            "City",
            "USA",
            emails,
            1,
            1,
            phoneNumbers,
            "HI",
            "Street1",
            "Street2",
            2,
            true,
            workflowErrors,
            "www.test.com",
            "12345"
        );

        _contactsApiMock.Setup(x => x.GetContact(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(contactResponse);

        // Act
        var result = await _controller.NewOtherPayment(filerId, filingId, contactId, _transactionsApiMock.Object, reportType, returnUrl) as ViewResult;
        var resultModel = result!.Model as OtherPaymentViewModel;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("NewOtherPayment"));
            Assert.That(result.Model, Is.InstanceOf<OtherPaymentViewModel>());
            Assert.That(resultModel!.CumulativeAmount, Is.EqualTo(0m));
            Assert.That(resultModel!.FilerId, Is.EqualTo(filerId));
            Assert.That(resultModel!.FilingId, Is.EqualTo(filingId));
            Assert.That(resultModel!.ReturnUrl, Is.EqualTo(returnUrl));
            Assert.That(resultModel!.PaymentCodes, Is.EqualTo(paymentCodeDictionary));
            Assert.That(resultModel!.Contacts![0], Is.EqualTo(contactResponse));
            Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(reportType));

            // NEW: Validate AdministrativeActionGridModel
            Assert.That(resultModel!.AdministrativeActionGridModel, Is.Not.Null);
            Assert.That(resultModel.AdministrativeActionGridModel?.GridId, Is.EqualTo("AdministrativeAction"));
        });

        // Assert for nested private grid function
        Assert.Multiple(() =>
        {
            Assert.That(resultModel!.LegislationAssyBillGridModel!.GridId, Is.EqualTo("AssemblyBill"));
            Assert.That(resultModel!.LegislationSenateBillGridModel!.GridId, Is.EqualTo("SenateBill"));
            Assert.That(resultModel!.LegislationAssyBillGridModel.Columns.Any(c => c.Field == "Number"));
            Assert.That(resultModel!.LegislationSenateBillGridModel.Columns.Any(c => c.Field == "Number"));
            Assert.That(resultModel!.LegislationAssyBillGridModel.Columns.Any(c => c.Field == "Title"));
            Assert.That(resultModel!.LegislationSenateBillGridModel.Columns.Any(c => c.Field == "Title"));
        });
    }

    [Test]
    public void AddAdministrativeAction_ShouldAppendAndAssignIds()
    {
        // Arrange
        var emptySerializedList = JsonConvert.SerializeObject(new List<AdministrativeActionViewModel>());

        var newActions = new List<AdministrativeActionViewModel>
        {
            new() { Name = "New Agency 1", AdministrativeAction = "New Action 1", AgencyId = 1 },
            new() { Name = "New Agency 2", AdministrativeAction = "New Action 2", AgencyId = 2 }
        };

        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            { "AdministrativeActionTempDataKey", emptySerializedList }
        };

        _controller.TempData = tempData;

        // Act
        var result = _controller.AddAdministrativeAction(newActions);
        var tempDataJson = tempData["AdministrativeActionTempDataKey"] as string;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(2));

        Assert.Multiple(() =>
        {
            Assert.That(result[0].Id, Is.EqualTo(1));
            Assert.That(result[0].Name, Is.EqualTo("New Agency 1"));
            Assert.That(result[1].Id, Is.EqualTo(2));
            Assert.That(result[1].Name, Is.EqualTo("New Agency 2"));
        });
    }

    [Test]
    public void DeleteAdministrativeAction_ShouldRemoveItemAndReturnUpdatedGrid()
    {
        // Arrange
        var existingActions = new List<AdministrativeActionViewModel>
        {
            new() { Id = 1, Name = "Agency 1", AdministrativeAction = "Action 1", AgencyId = 1 },
            new() { Id = 2, Name = "Agency 2", AdministrativeAction = "Action 2", AgencyId = 2 }
        };

        var serialized = JsonConvert.SerializeObject(existingActions);
        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            { "AdministrativeActionTempDataKey", serialized }
        };

        _controller.TempData = tempData;

        // Act
        var result = _controller.DeleteAdministrativeAction(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.InstanceOf<SmallDataGridModel>());
    }

    [Test]
    public void AddAssemblyBill_ShouldAppendAndAssignIds()
    {
        // Arrange
        var emptySerializedList = JsonConvert.SerializeObject(new List<LegislativeBillViewModel>());

        var newBills = new List<LegislativeBillViewModel>
        {
            new() { Title = "Assembly Bill 1", Number = "ABC-123", Id = 1 },
            new() { Title = "Assembly Bill 2", Number = "XYZ-123", Id = 2 }
        };

        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            { "LegislationAssyBillTempDataKey", emptySerializedList }
        };

        _controller.TempData = tempData;

        // Act
        var result = _controller.AddAssemblyBill(newBills);
        _ = tempData["LegislationAssyBillTempDataKey"] as string;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(2));

        Assert.Multiple(() =>
        {
            Assert.That(result[0].Id, Is.EqualTo(1));
            Assert.That(result[0].Title, Is.EqualTo("Assembly Bill 1"));
            Assert.That(result[1].Id, Is.EqualTo(2));
            Assert.That(result[1].Title, Is.EqualTo("Assembly Bill 2"));
        });
    }

    [Test]
    public void DeleteAssemblyBill_ShouldRemoveItemAndReturnUpdatedGrid()
    {
        // Arrange
        var existingBills = new List<LegislativeBillViewModel>
        {
            new() { Title = "Assembly Bill 1", Number = "ABC-123", Id = 1 },
            new() { Title = "Assembly Bill 2", Number = "XYZ-123", Id = 2 }
        };

        var serialized = JsonConvert.SerializeObject(existingBills);
        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            { "LegislationAssyBillTempDataKey", serialized }
        };

        _controller.TempData = tempData;

        // Act
        var result = _controller.DeleteAssemblyBill(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.InstanceOf<SmallDataGridModel>());
    }

    [Test]
    public void AddSenateBill_ShouldAppendAndAssignIds()
    {
        // Arrange
        var emptySerializedList = JsonConvert.SerializeObject(new List<LegislativeBillViewModel>());

        var newBills = new List<LegislativeBillViewModel>
        {
            new() { Title = "Senate Bill 1", Number = "ABC-123", Id = 1 },
            new() { Title = "Senate Bill 2", Number = "XYZ-123", Id = 2 }
        };

        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            { "LegislationSenateBillTempDataKey", emptySerializedList }
        };

        _controller.TempData = tempData;

        // Act
        var result = _controller.AddSenateBill(newBills);
        _ = tempData["LegislationSenateBillTempDataKey"] as string;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(2));

        Assert.Multiple(() =>
        {
            Assert.That(result[0].Id, Is.EqualTo(1));
            Assert.That(result[0].Title, Is.EqualTo("Senate Bill 1"));
            Assert.That(result[1].Id, Is.EqualTo(2));
            Assert.That(result[1].Title, Is.EqualTo("Senate Bill 2"));
        });
    }

    [Test]
    public void DeleteSenateBill_ShouldRemoveItemAndReturnUpdatedGrid()
    {
        // Arrange
        var existingBills = new List<LegislativeBillViewModel>
        {
            new() { Title = "Senate Bill 1", Number = "ABC-123", Id = 1 },
            new() { Title = "Senate Bill 2", Number = "XYZ-123", Id = 2 }
        };

        var serialized = JsonConvert.SerializeObject(existingBills);
        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>())
        {
            { "LegislationSenateBillTempDataKey", serialized }
        };

        _controller.TempData = tempData;

        // Act
        var result = _controller.DeleteSenateBill(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result!.Value, Is.InstanceOf<SmallDataGridModel>());
    }

    [Test]
    public async Task NewOtherPayment_Post_InvalidModel_ReturnsViewWithError()
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;
        var returnUrl = "/return";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var contacts = new List<ContactItemResponse> { new(1, "Test City", "US", emails, filerId, 1, phoneNumbers, "HI", "Street", "Street 2", 2, true, workflowErrors, "", "12345") };

        var model = new OtherPaymentViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            Contacts = contacts,
            ContactId = 1,
            ReturnUrl = returnUrl,
        };

        _controller.ModelState.AddModelError("Error", "Test error");

        var paymentCodes = new List<Generated.PaymentCodeRefResponse>
        {
            new ("Advertising (A)", 1, "Advertising"),
            new ("Research (R)", 2, "Research"),
            new ("Other (O)", 3, "Other")
        };

        var cumulativeAmount = new Generated.CumulativeAmountResponse(filingId, 3000);

        _ = _referenceDataApiMock.Setup(api => api.GetAllPaymentCodes(It.IsAny<CancellationToken>())).ReturnsAsync(paymentCodes);

        _ = _transactionsApiMock.Setup(api => api.GetOtherPaymentsCumulativeAmountForFilingAndContact(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(cumulativeAmount);

        // Act
        var result = await _controller.NewOtherPayment(
            _transactionsApiMock.Object,
            model,
            returnUrl: null,
            cancellationToken: CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("NewOtherPayment"));
            Assert.That(result.Model, Is.EqualTo(model));
        });
    }

    [Test]
    [TestCase(true, TestName = "Null amount")]
    [TestCase(false, TestName = "Negative amount")]
    public async Task NewOtherPayment_Post_ApiCallFail_ShouldStayOnCreate(bool useNullAmount)
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;
        var returnUrl = "/return";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var contacts = new List<ContactItemResponse> { new(1, "Test City", "US", emails, filerId, 1, phoneNumbers, "HI", "Street", "Street 2", 2, true, workflowErrors, "", "12345") };

        var model = new OtherPaymentViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            Contacts = contacts,
            ContactId = 1,
            ReturnUrl = returnUrl,
            Amount = useNullAmount ? null : -1
        };

        var paymentCodes = new List<Generated.PaymentCodeRefResponse>
        {
            new ("Advertising (A)", 1, "Advertising"),
            new ("Research (R)", 2, "Research"),
            new ("Other (O)", 3, "Other")
        };

        var cumulativeAmount = new Generated.CumulativeAmountResponse(filingId, 3000);

        _ = _referenceDataApiMock.Setup(api => api.GetAllPaymentCodes(It.IsAny<CancellationToken>())).ReturnsAsync(paymentCodes);

        _ = _transactionsApiMock.Setup(api => api.GetOtherPaymentsCumulativeAmountForFilingAndContact(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CancellationToken>())).ReturnsAsync(cumulativeAmount);

        var validationErrors = new List<WorkFlowError>
        {
            new("Some error", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            new("Some error", "ErrGlobal0001", null!, "{{Field Name}} is required")
        };

        var fakeResponse = new HttpResponseMessage(HttpStatusCode.UnprocessableEntity)
        {
            Content = new StringContent(JsonConvert.SerializeObject(validationErrors))
        };

        var apiException = await ApiException.Create(
            new HttpRequestMessage(),
            HttpMethod.Post,
            fakeResponse,
            new RefitSettings());

        _ = _transactionsApiMock
            .Setup(api => api.CreateOtherInfluencePayment(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<OtherInfluencePaymentDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(apiException);

        // Act
        var result = await _controller.NewOtherPayment(
            _transactionsApiMock.Object,
            model,
            returnUrl: null,
            cancellationToken: CancellationToken.None) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ViewName, Is.EqualTo("NewOtherPayment"));
            Assert.That(result.Model, Is.EqualTo(model));
        });
    }

    [Test]
    [TestCase(true)]
    [TestCase(false)]
    public async Task NewOtherPayment_Post_ApiCallSuccess_ShouldRedirectToIndex(bool isCreate)
    {
        // Arrange
        var transactionId = 1;
        var filerId = 1;
        var filingId = 1;
        var returnUrl = "/return";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var contacts = new List<ContactItemResponse> { new(1, "Test City", "US", emails, filerId, 1, phoneNumbers, "HI", "Street", "Street 2", 2, true, workflowErrors, "", "12345") };

        var model = new OtherPaymentViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            Contacts = contacts,
            ContactId = 1,
            ReturnUrl = returnUrl,
            Amount = 1,
            TransactionId = isCreate ? null : transactionId
        };

        var paymentCodes = new List<Generated.PaymentCodeRefResponse>
        {
            new ("Advertising (A)", 1, "Advertising"),
            new ("Research (R)", 2, "Research"),
            new ("Other (O)", 3, "Other")
        };

        _ = _referenceDataApiMock.Setup(api => api.GetAllPaymentCodes(It.IsAny<CancellationToken>())).ReturnsAsync(paymentCodes);

        _ = _transactionsApiMock
            .Setup(api => api.CreateOtherInfluencePayment(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<OtherInfluencePaymentDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<TransactionItemResponse>());

        _ = _transactionsApiMock
           .Setup(api => api.EditOtherInfluencePayment(It.IsAny<long>(), It.IsAny<OtherInfluencePaymentDto>(), It.IsAny<CancellationToken>()))
           .ReturnsAsync(It.IsAny<TransactionItemResponse>());

        // Act
        var result = await _controller.NewOtherPayment(
            _transactionsApiMock.Object,
            model,
            returnUrl: null,
            cancellationToken: CancellationToken.None);

        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());

        var redirectResult = (RedirectToActionResult)result;
        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        // Assert
        Assert.Multiple(() =>
        {
            // Verify the redirect target
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Disclosure"));

            // Verify RouteValues
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(1));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(1));
            Assert.That(redirectResult.RouteValues["viewName"], Is.EqualTo("OtherPaymentsToInfluenceSummary"));
        });
    }

    [Test]
    [TestCase(true, TestName = "Null actions lobbied")]
    [TestCase(false, TestName = "Non null actions lobbied")]
    public async Task NewOtherPayment_Post_ApiCallSuccess_ShouldRedirectToIndex_WithActionsLobbied(bool nullActionsLobbied)
    {
        // Arrange
        var filerId = 1;
        var filingId = 1;
        var returnUrl = "/return";
        var emails = new List<EmailAddress>();
        var phoneNumbers = new List<PhoneNumber>();
        var workflowErrors = new List<WorkFlowError>();
        var contacts = new List<ContactItemResponse> { new(1, "Test City", "US", emails, filerId, 1, phoneNumbers, "HI", "Street", "Street 2", 2, true, workflowErrors, "", "12345") };

        var model = new OtherPaymentViewModel()
        {
            FilerId = filerId,
            FilingId = filingId,
            Contacts = contacts,
            ContactId = 1,
            ReturnUrl = returnUrl,
        };

        var paymentCodes = new List<Generated.PaymentCodeRefResponse>
        {
            new ("Advertising (A)", 1, "Advertising"),
            new ("Research (R)", 2, "Research"),
            new ("Other (O)", 3, "Other")
        };

        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>());

        var senateBillActionsLobbied = nullActionsLobbied ? null : new List<LegislativeBillViewModel>
        {
            new() { Id = 1 }
        };

        var assyBillActionsLobbied = nullActionsLobbied ? null : new List<LegislativeBillViewModel>
        {
            new() { Id = 1 }
        };

        var adminActionActionsLobbied = nullActionsLobbied ? null : new List<AdministrativeActionViewModel>
        {
            new() {Name = "Yes", AgencyId = 1, AgencyDescription = "Test Agency", AdministrativeAction = "Test Action" },
            new() {Name = "Yes", AgencyId = 1, AgencyDescription = null, AdministrativeAction = null }
        };

        var legislationSenateBillKey = "SenateBill";
        var legislationAssyBillKey = "AssemblyBill";
        var adminActionsKey = "AdministrativeAction";

        tempData[legislationSenateBillKey] = senateBillActionsLobbied;
        tempData[legislationAssyBillKey] = assyBillActionsLobbied;
        tempData[adminActionsKey] = adminActionActionsLobbied;

        _controller.TempData = tempData;
        _controller.ControllerContext = new ControllerContext()
        {
            HttpContext = new DefaultHttpContext()
        };

        _ = _referenceDataApiMock.Setup(api => api.GetAllPaymentCodes(It.IsAny<CancellationToken>())).ReturnsAsync(paymentCodes);

        _ = _transactionsApiMock
            .Setup(api => api.CreateOtherInfluencePayment(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<OtherInfluencePaymentDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(It.IsAny<TransactionItemResponse>());

        // Act
        var result = await _controller.NewOtherPayment(
            _transactionsApiMock.Object,
            model,
            returnUrl: null,
            cancellationToken: CancellationToken.None);

        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());

        var redirectResult = (RedirectToActionResult)result;
        Assert.That(redirectResult.RouteValues, Is.Not.Null);

        // Assert
        Assert.Multiple(() =>
        {
            // Verify the redirect target
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Disclosure"));

            // Verify RouteValues
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(1));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(1));
            Assert.That(redirectResult.RouteValues["viewName"], Is.EqualTo("OtherPaymentsToInfluenceSummary"));

            Assert.That(tempData[adminActionsKey], Is.Null);
        });
    }

    #endregion

    #region 48 Hour Report End-of-Session Lobbying Transaction

    [Test]
    public async Task Report48HEosTransaction_Get_WithValidParameters_ReturnsViewWithModel()
    {
        // Arrange
        string reportType = FilingTypeModel.Report48h.Name;
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;
        string orgName = "Test Lobbying Firm";
        var contact = new GenericContactViewModel
        {
            OrganizationName = orgName
        };

        _ = _firmPaymentTransactionCtlSvcMock
            .Setup(api => api.GetContactViewModel(It.IsAny<long>(), It.IsAny<long>()))
            .ReturnsAsync(contact);

        // Act
        var result = await _controller.Report48HEosTransaction(reportType, filingId, filerId, contactId, registrationFilingId, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;

        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Report48HEosTransaction"));

        var model = viewResult.Model as Report48HTransactionViewModel;
        Assert.That(model, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(model.ReportType, Is.EqualTo(reportType));
            Assert.That(model.FilingId, Is.EqualTo(filingId));
            Assert.That(model.FilerId, Is.EqualTo(filerId));
            Assert.That(model.ContactId, Is.EqualTo(contactId));
            Assert.That(model.RegistrationFilingId, Is.EqualTo(registrationFilingId));
            Assert.That(model.LegislationAssyBillGridModel, Is.Not.Null);
            Assert.That(model.LegislationSenateBillGridModel, Is.Not.Null);
            Assert.That(model.Contact.OrganizationName, Is.EqualTo(orgName));
        });

        _firmPaymentTransactionCtlSvcMock.Verify(api =>
            api.GetContactViewModel(It.IsAny<long>(), It.IsAny<long>()),
            Times.Once);
    }

    [Test]
    public async Task Report48HEosTransaction_Get_WithInvalidModelState_ReturnsNotFound()
    {
        // Arrange
        string reportType = FilingTypeModel.Report48h.Name;
        long filingId = 123;
        long filerId = 456;
        long? contactId = 789;
        long? registrationFilingId = 101112;

        _controller.ModelState.AddModelError("error", "test error");

        // Act
        var result = await _controller.Report48HEosTransaction(reportType, filingId, filerId, contactId, registrationFilingId, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());

        _firmPaymentTransactionCtlSvcMock.Verify(api =>
            api.GetContactViewModel(It.IsAny<long>(), It.IsAny<long>()),
            Times.Never);
    }

    [Test]
    public async Task Report48HEosTransaction_Post_WithPreviousAction_RedirectsToEnterContact()
    {
        // Arrange
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456,
            ContactId = 789,
            RegistrationFilingId = 101112
        };

        string action = "Previous";

        // Act
        var result = await _controller.Report48HEosTransaction(model, action, _transactionsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("CreateEditLobbyingFirm"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Contact"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirectResult.RouteValues["contactId"], Is.EqualTo(model.ContactId));
            Assert.That(redirectResult.RouteValues["registrationFilingId"], Is.EqualTo(model.RegistrationFilingId));
        });
    }

    [Test]
    public async Task Report48HEosTransaction_Post_WithCancelAction_RedirectsToTransactionSummary()
    {
        // Arrange
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 123,
            FilerId = 456
        };

        string action = "Cancel";

        // Act
        var result = await _controller.Report48HEosTransaction(model, action, _transactionsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.RouteValues, Is.Not.Null);
            Assert.That(redirectResult.RouteValues!["viewName"], Is.EqualTo(FilingSummaryTypeModel.EndOfSessionLobbyingSummary.Name));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(model.ReportType));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(model.FilingId));
            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(model.FilerId));
        });
    }

    [Test]
    public async Task Report48HEosTransaction_ActionIsSave_ValidApiResponse_RedirectsToSummary()
    {
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 10,
            FilerId = 20,
            AmountPaidToFirm = 100,
            ContactId = 30,
            DateLobbyingFirmHired = DateTime.Today,
            RegistrationFilingId = 40
        };


        _transactionsApiMock.Setup(api => api.CreateEndOfSessionLobbying(It.IsAny<EndOfSessionLobbyingRequestDto>(), CancellationToken.None))
            .ReturnsAsync(new TransactionResponseDto(1, true, new List<WorkFlowError>()));

        var result = await _controller.Report48HEosTransaction(model, "Save", _transactionsApiMock.Object);

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect!.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task Report48HEosTransaction_ActionIsSave_InvalidApiResponse_ReturnsViewWithModel()
    {
        var model = new Report48HTransactionViewModel
        {
            ReportType = FilingTypeModel.Report48h.Name,
            FilingId = 10,
            FilerId = 20,
            Amount = 100,
            ContactId = 30,
            DateLobbyingFirmHired = DateTime.Today,
            RegistrationFilingId = 40
        };

        _transactionsApiMock.Setup(api => api.CreateEndOfSessionLobbying(It.IsAny<EndOfSessionLobbyingRequestDto>(), CancellationToken.None))
            .ReturnsAsync(new TransactionResponseDto(1, true, new List<WorkFlowError>()));

        _transactionsApiMock
            .Setup(api => api.EditEndOfSessionLobbying(It.IsAny<long>(), It.IsAny<EndOfSessionLobbyingRequestDto>(), CancellationToken.None))
            .ReturnsAsync(new TransactionResponseDto(1, true, new List<WorkFlowError>()));

        var result = await _controller.Report48HEosTransaction(model, "Save", _transactionsApiMock.Object, CancellationToken.None);

        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var viewResult = result as RedirectToActionResult;
        Assert.That(viewResult?.ActionName, Is.EqualTo("Index"));
    }

    [Test]
    public async Task EditReport48HEosTransaction_ShouldReturnViewWithExpectedModel()
    {
        // Arrange
        string reportType = FilingTypeModel.Report48h.Name;
        long transactionId = 123;
        long filingId = 456;
        long filerId = 789;
        long registrationFilingId = 2020;

        var dto = new Generated.EndOfSessionLobbyingDto
        (
            actionsLobbied: [],
            amount: 15000.00,
            contact: null,
            dateLobbyingFirmHired: new DateTime(2025, 4, 15),
            filerId,
            filingId,
            firmName: "Capitol Strategies LLC",
            id: transactionId,
            registrationId: registrationFilingId
        );

        _transactionsApiMock
            .Setup(api => api.GetEndOfSessionLobbyingTransactionById(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(dto);

        _controller.TempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>());

        var parameters = new EditReport48HEosTransactionParameters
        {
            ReportType = reportType,
            FilingId = filingId,
            FilerId = filerId,
            TransactionId = transactionId,
            RegistrationFilingId = registrationFilingId
        };

        // Act
        var result = await _controller.EditReport48HEosTransaction(parameters, _firmPaymentTransactionCtlSvcMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = result as ViewResult;
        Assert.That(viewResult!.ViewName, Is.EqualTo("Report48HEosTransaction"));

        var model = viewResult.Model as Report48HTransactionViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model!.Id, Is.EqualTo(transactionId));
            Assert.That(model!.FilingId, Is.EqualTo(filingId));
            Assert.That(model!.FilerId, Is.EqualTo(filerId));
            Assert.That(model!.ContactId, Is.EqualTo(null));
            Assert.That(model!.RegistrationFilingId, Is.EqualTo(registrationFilingId));
            Assert.That(model!.Amount, Is.EqualTo(15000.00m));
        });
    }

    [Test]
    public async Task EditReport48HEosTransaction_ShouldReturnNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("SomeField", "Error");

        var parameters = new EditReport48HEosTransactionParameters
        {
            ReportType = "Report48H",
            FilingId = 1L,
            FilerId = 1L,
            TransactionId = 1L,
            RegistrationFilingId = 1L,
            ContactId = 1L
        };

        // Act
        var result = await _controller.EditReport48HEosTransaction(parameters, Substitute.For<IFirmPaymentTransactionCtlSvc>());

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task EditEndOfSessionLobbyingContact_WithValidId_RedirectsToCreateEditLobbyingFirm()
    {
        // Arrange
        long transactionId = 999;
        long expectedFilingId = 123;
        long expectedFilerId = 456;
        long expectedContactId = 789;
        long expectedRegistrationFilingId = 321;

        var contact = new CandidateContact(
            addressList: null,
            addressListId: 1,
            committeeName: "Test Committee",
            contactFiler: null,
            contactFilerId: 1,
            createdBy: 1,
            district: "District 1",
            emailAddressList: null,
            emailAddressListId: 1,
            externalId: "EXT123",
            filer: null,
            filerContactType: new FilerContactType(1, 1, 1, "Lobbyist"),
            filerContactTypeId: 1,
            filerId: 2,
            filingContactSummary: null,
            firstName: "John",
            id: expectedContactId,
            jurisdiction: "CA",
            lastName: "Doe",
            middleName: "Q",
            modifiedBy: 1,
            officeSought: "Senator",
            phoneNumberList: null,
            phoneNumberListId: 1
        );

        var dto = new Generated.EndOfSessionLobbyingDto(
            actionsLobbied: new List<ActionsLobbied>(),
            amount: 1000.0,
            contact: contact,
            dateLobbyingFirmHired: DateTime.UtcNow,
            filerId: expectedFilerId,
            filingId: expectedFilingId,
            firmName: "Test Lobbying Firm",
            id: transactionId,
            registrationId: expectedRegistrationFilingId
        );


        var transactionsApiMock = new Mock<ITransactionsApi>();
        transactionsApiMock
            .Setup(api => api.GetEndOfSessionLobbyingTransactionById(transactionId, CancellationToken.None))
            .ReturnsAsync(dto);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };

        // Act
        var result = await _controller.EditEndOfSessionLobbyingContact(transactionId, transactionsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = result as RedirectToActionResult;
        Assert.Multiple(() =>
        {
            Assert.That(redirect?.ActionName, Is.EqualTo("CreateEditLobbyingFirm"));
            Assert.That(redirect?.ControllerName, Is.EqualTo("Contact"));
            Assert.That(redirect?.RouteValues, Is.Not.Null);
        });
        Assert.Multiple(() =>
        {
            Assert.That(redirect, Is.Not.Null);
            Assert.That(redirect.RouteValues, Is.Not.Null);

            Assert.That(redirect.RouteValues["reportType"], Is.EqualTo(FilingTypeModel.Report48h.Name));
            Assert.That(redirect.RouteValues["filingId"], Is.EqualTo(expectedFilingId));
            Assert.That(redirect.RouteValues["filerId"], Is.EqualTo(expectedFilerId));
            Assert.That(redirect.RouteValues["contactId"], Is.EqualTo(expectedContactId));
            Assert.That(redirect.RouteValues["transactionId"], Is.EqualTo(transactionId));
            Assert.That(redirect.RouteValues["registrationFilingId"], Is.EqualTo(expectedRegistrationFilingId));
        });


        transactionsApiMock.Verify(api => api.GetEndOfSessionLobbyingTransactionById(transactionId, CancellationToken.None), Times.Once);
    }


    [Test]
    public async Task EditEndOfSessionLobbyingContact_ShouldReturnNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("id", "Invalid");

        // Act
        var result = await _controller.EditEndOfSessionLobbyingContact(0, _transactionsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    #endregion

    #region Other

    [Test]
    public async Task EditMonetaryContribution_ShouldRedirectToFilerIndex()
    {
        // Arrange
        var id = 12345L;
        var filerId = 67890L;
        var model = new MonetaryContributionViewModel
        {
            FilerId = filerId,
            Amount = 100,
            TransactionDate = _dateNow,
            ContactId = 1
        };

        _transactionsApiMock
            .Setup(api => api.UpdateTransaction(id, It.IsAny<UpsertTransactionRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.EditMonetaryContribution(
            id,
            _contactsApiMock.Object,
            _transactionsApiMock.Object,
            model
        );

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null, "Expected RedirectToActionResult for a valid request.");

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"), "Should redirect to 'Index' action.");
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Filer"), "Should redirect to 'Filer' controller.");
            Assert.That(redirectResult.RouteValues, Is.Not.Null, "RouteValues should not be null.");
            Assert.That(redirectResult.RouteValues!["id"], Is.EqualTo(filerId), "Expected FilerId in route values.");
        });
    }

    [Test]
    public async Task EditInKindContribution_ShouldRedirectToFilerIndex()
    {
        // Arrange
        var id = 12345L;
        var filerId = 67890L;
        var contactId = 54321L;
        var model = new InKindContributionViewModel
        {
            FilerId = filerId,
            ContactId = contactId,
            Amount = 100,
            TransactionDate = _dateNow,
        };

        _transactionsApiMock
            .Setup(api => api.UpdateTransaction(id, It.IsAny<UpsertTransactionRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.EditInKindContribution(
            id,
            _contactsApiMock.Object,
            _transactionsApiMock.Object,
            model
        );

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Filer"));
            Assert.That(redirectResult.RouteValues!["id"], Is.EqualTo(filerId));
        });
    }

    [Test]
    public async Task EditExpenditure_ShouldRedirectToFilerIndex()
    {
        // Arrange
        var id = 12345L;
        var filerId = 67890L;
        var contactId = 54321L;
        var model = new ExpenditureViewModel
        {
            FilerId = filerId,
            ContactId = contactId,
            Amount = 100,
            TransactionDate = _dateNow,
        };

        _transactionsApiMock
            .Setup(api => api.UpdateTransaction(id, It.IsAny<UpsertTransactionRequest>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.EditExpenditure(
            id,
            _contactsApiMock.Object,
            _transactionsApiMock.Object,
            model
        );

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Filer"));
            Assert.That(redirectResult.RouteValues!["id"], Is.EqualTo(filerId));
        });
    }

    [Test]
    public async Task DeleteTransaction_ShouldRedirectToFilerIndex()
    {
        // Arrange
        var id = 12345L;
        var filerId = 67890L;

        _transactionsApiMock
            .Setup(api => api.DeleteTransaction(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new TransactionItemResponse(10, "Display Name", null, null, 1, null, "note", new DateTime(), TransactionType.LobbyingCampaignContribution.Id));

        // Act
        var result = await _controller.DeleteTransaction(
            id,
            filerId,
            _transactionsApiMock.Object
        );

        // Assert
        var redirectResult = result as RedirectToActionResult;
        Assert.That(redirectResult, Is.Not.Null);

        Assert.Multiple(() =>
        {
            Assert.That(redirectResult!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("Filer"));
            Assert.That(redirectResult.RouteValues!["id"], Is.EqualTo(filerId));
        });
    }

    [Test]
    public void Redirect_ShouldLocalRedirect_WhenReturnUrlIsValid()
    {
        // Arrange
        var returnUrl = "/valid/local/url";

        var urlHelperMock = new Mock<IUrlHelper>();
        urlHelperMock
            .Setup(url => url.IsLocalUrl(returnUrl))
            .Returns(true);

        _controller.Url = urlHelperMock.Object;

        // Act
        var result = _controller.Redirect(returnUrl);

        // Assert
        var localRedirectResult = result;
        Assert.That(localRedirectResult, Is.Not.Null);
        Assert.That(localRedirectResult!.Url, Is.EqualTo(returnUrl));
    }

    [Test]
    public async Task EditLobbyistCampaignContribution_ReturnsViewResult_WithExpectedModel()
    {
        // Arrange
        var id = 123L;

        var reg = new RegistrationItemResponse(_dateNow, 1, 1, "registration", 1, "draft");

        var filerItemResponse = new FilerItemResponse(reg, 1);

        var transactionResponse = new LobbyistCampaignContributionItemResponse
            (100, null, filerItemResponse, null, 1, 1, id, false, false, "Contributor",
            "notes", filerItemResponse, 12, "Recipient", "Non committee", "Separate Account", _dateNow);

        var committees = new List<RegistrationItemResponse>
        {
            new (approvedAt: _dateNow, filerId: 12345, id: 1, name: "Committee A", statusId: 1, type: "Type1"),
            new (approvedAt: _dateNow, filerId: 67890, id: 2, name: "Committee B", statusId: 2, type: "Type2"),
            new (approvedAt: _dateNow, filerId: null, id: 3, name: "Invalid Committee", statusId: 3, type: "Type3") // Should not be included
        };

        _transactionsApiMock
            .Setup(api => api.GetLobbyistCampaignContributionTransactionById(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(transactionResponse);

        _registrationsApiMock
            .Setup(api => api.GetAllCommittees(It.IsAny<CancellationToken>()))
            .ReturnsAsync(committees);

        // Act
        var result = await _controller.EditLobbyistCampaignContribution(id, _registrationsApiMock.Object, _transactionsApiMock.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);

        var updatedModel = viewResult?.Model as LobbyingCampaignContributionViewModel;
        Assert.That(updatedModel, Is.Not.Null);

        var committeeAutoCompleteModel = updatedModel.RecipientCommitteeFilerAutoCompleteModel;
        var optionA = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 12345);
        var optionB = committeeAutoCompleteModel.Options.FirstOrDefault(x => x.FilerId == 67890);
        Assert.Multiple(() =>
        {
            Assert.That(committeeAutoCompleteModel, Is.Not.Null, "CommitteeDropdownOptions should not be null");
            Assert.That(committeeAutoCompleteModel.Options, Is.TypeOf<List<RegistrationItemResponse>>(), "Expected options to be a list of RegistrationItemResponse");
            Assert.That(committeeAutoCompleteModel.Options, Has.Count.EqualTo(2), "Expected CommitteeDropdownOptions to have 2 items");
            Assert.That(committeeAutoCompleteModel.Name, Is.EqualTo("RecipientCommitteeFilerId"), "Expected committee auto complete to use name RecipientCommitteeFilerId");
            Assert.That(committeeAutoCompleteModel.TextKey, Is.EqualTo("Name"), "Expected committee auto complete to use Name for option text");
            Assert.That(committeeAutoCompleteModel.ValueKey, Is.EqualTo("FilerId"), "Expected committee auto complete to use Id for option value");
            Assert.That(committeeAutoCompleteModel.Placeholder, Is.Not.Null, "Expected committee auto complete to have a placeholder");
            Assert.That(optionA, Is.Not.Null);
            Assert.That(optionB, Is.Not.Null);
            Assert.That(optionA?.Name, Is.EqualTo("Committee A"), "Expected dropdown option for 12345");
            Assert.That(optionB?.Name, Is.EqualTo("Committee B"), "Expected dropdown option for 67890");
        }
        );
    }

    [Test]
    public async Task EditLobbyistCampaignContribution_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Id", "Required");

        var id = 123L;

        // Act
        var result = await _controller.EditLobbyistCampaignContribution(id, _registrationsApiMock.Object, _transactionsApiMock.Object);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    #endregion

#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.

}
