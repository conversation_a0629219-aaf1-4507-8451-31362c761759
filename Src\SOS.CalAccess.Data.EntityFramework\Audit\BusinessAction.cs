// <copyright file="BusinessAction.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Data.EntityFramework.Audit;

/// <summary>
/// A class for representing logs of high-level actions in the system.
/// </summary>
/// <param name="type">A key to identify the type of action that was logged.</param>
/// <param name="subject">The scope of the action.</param>
/// <param name="resourceId">An identifier for the affected resource(s).</param>
/// <param name="details">Optional additional description for the action.</param>
public sealed class BusinessAction(
    string type, string subject, string resourceId, DateTime issuedAt, string? details = null) : IAuditableAction
{
    /// <summary>
    /// Identifier for the action of reading a resource.
    /// This is required because low-level audit logs do not track reads.
    /// </summary>
    public const string ReadAction = nameof(ReadAction);

    /// <summary>
    /// Identifier for the action of reading a set of resources.
    /// This is required because low-level audit logs do not track reads.
    /// </summary>
    public const string ReadAllAction = nameof(ReadAllAction);

    /// <summary>
    /// Identifier for the action of changing the status of a resource that admits doing so.
    /// e.g., changing a registration from draft to approved.
    /// </summary>
    public const string ChangeStatusAction = nameof(ChangeStatusAction);

    /// <summary>
    /// Identifier for the action of amending a resource that admits doing so.
    /// </summary>
    public const string AmendAction = nameof(AmendAction);

    /// <summary>
    /// Stand-in for a resource identifier for a read-all action that scans the full resource.
    /// </summary>
    public const string Root = nameof(Root);

    /// <summary>
    /// Stand-in for a resource identifier for a read-all action that scans the full resource for
    /// all items that the issuer has access to.
    /// </summary>
    public const string WithGrantedAccess = nameof(WithGrantedAccess);

    /// <inheritdoc/>
    public string Type { get; } = type;

    /// <inheritdoc/>
    public string Subject { get; } = subject;

    /// <inheritdoc/>
    public string ResourceId { get; } = resourceId;

    /// <inheritdoc/>
    public string? Diff { get; } = details;

    /// <inheritdoc/>
    public DateTime IssuedAt { get; } = issuedAt;

    /// <summary>
    /// Convenience method for generating a new action that represents
    /// updating the status of a specific resource instance.
    /// </summary>
    /// <typeparam name="TResource">The resource type. Used to source subject.</typeparam>
    /// <param name="resourceId">The resource id (as a string).</param>
    /// <param name="details">Additional details about the action.</param>
    /// <returns>A new action that can be logged.</returns>
    public static BusinessAction ChangeStatus<TResource>(string resourceId, IDateTimeSvc dateTimeSvc, string? details = null) =>
        new(ChangeStatusAction, typeof(TResource).Name, resourceId, dateTimeSvc.GetCurrentDateTime(), details);

    /// <summary>
    /// Convenience method for generating a new action that represents
    /// reading one specific resource.
    /// </summary>
    /// <typeparam name="TResource">The resource type. Used to source subject.</typeparam>
    /// <param name="resourceId">The resource id (as a string).</param>
    /// <param name="details">Additional details about the action.</param>
    /// <returns>A new action that can be logged.</returns>
    public static BusinessAction Read<TResource>(string resourceId, IDateTimeSvc dateTimeSvc, string? details = null) =>
        new(ReadAction, typeof(TResource).Name, resourceId, dateTimeSvc.GetCurrentDateTime(), details);

    /// <summary>
    /// Convenience method for generating a new action that represents
    /// reading all (or some) of the resources under the specified parent resource.
    /// </summary>
    /// <typeparam name="TParent">The parent resource type. Used to source subject.</typeparam>
    /// <typeparam name="TResource">The resource type. Used to source subject.</typeparam>
    /// <param name="resourceId">The resource id (as a string).</param>
    /// <param name="details">Additional details about the action.</param>
    /// <returns>A new action that can be logged.</returns>
    public static BusinessAction ReadAll<TParent, TResource>(string resourceId, IDateTimeSvc dateTimeSvc, string? details = null) =>
        new(ReadAction, $"{typeof(TParent).Name}:{typeof(TResource).Name}", resourceId, dateTimeSvc.GetCurrentDateTime(), details);

    /// <summary>
    /// Convenience method for generating a new action that represents
    /// reading all of the resources of a given type.
    /// </summary>
    /// <typeparam name="TResource">The resource type. Used to source subject.</typeparam>
    /// <param name="details">Additional details about the action.</param>
    /// <returns>A new action that can be logged.</returns>
    public static BusinessAction ReadAll<TResource>(IDateTimeSvc dateTimeSvc, string? details = null) =>
        new(ReadAction, typeof(TResource).Name, Root, dateTimeSvc.GetCurrentDateTime(), details);

    /// <summary>
    /// Convenience method for generating a new action that represents
    /// reading all of the resources of a given type for which the issuer has access.
    /// </summary>
    /// <typeparam name="TResource">The resource type. Used to source subject.</typeparam>
    /// <param name="details">Additional details about the action.</param>
    /// <returns>A new action that can be logged.</returns>
    public static BusinessAction ReadAllWithAccess<TResource>(IDateTimeSvc dateTimeSvc, string? details = null) =>
        new(ReadAction, typeof(TResource).Name, WithGrantedAccess, dateTimeSvc.GetCurrentDateTime(), details);
}
