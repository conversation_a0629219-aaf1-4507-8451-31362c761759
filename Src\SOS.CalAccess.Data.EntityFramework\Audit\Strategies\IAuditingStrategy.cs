// <copyright file="IAuditingStrategy.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore.ChangeTracking;
using SOS.CalAccess.Data.EntityFramework.Audit.Diffing;

namespace SOS.CalAccess.Data.EntityFramework.Audit.Strategies;

/// <summary>
/// Interface for an implementation providing a way to generate a set
/// of auditable actions from changes sourced from EF's change tracking mechanisms.
/// </summary>
public interface IAuditingStrategy
{
    /// <summary>
    /// Generate a list of actions to be audited based on the set of changes being tracked by EF.
    /// </summary>
    /// <remarks>
    /// An audit strategy implementation may define varying forms of generating auditable actions
    /// from entity changes: it might coalesce all changes into a single action, generate several
    /// entries per change or skip some, it is up to the implementor.
    /// </remarks>
    /// <param name="entries">The change tracking entries to be processed.</param>
    /// <param name="diffSerializer">
    /// The implementation to use for generating diffs, if supported by the strategy implementation.
    /// </param>
    /// <param name="token">Cancellation source.</param>
    /// <returns>A readonly list of auditable actions to be made into audit logs.</returns>
    ValueTask<IReadOnlyList<DataAction>> Audit(
        IEnumerable<EntityEntry> entries,
        IAuditDiffSerializer diffSerializer,
        DateTime issuedAt,
        CancellationToken token = default);
}
