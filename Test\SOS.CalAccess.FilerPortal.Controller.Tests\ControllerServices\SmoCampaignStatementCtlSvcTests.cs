using System.Reflection;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using SOS.CalAccess.FilerPortal.Constants;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices.SmoCampaignStatementCtlSvc;
using SOS.CalAccess.FilerPortal.Mapper;
using SOS.CalAccess.FilerPortal.Models.Disclosure.SmoCampaignStatement;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;
using static SOS.CalAccess.FilerPortal.Constants.DisclosureConstants;
using FilerRole = SOS.CalAccess.Models.Authorization.FilerRole;
using FilingOverviewResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.FilingOverviewResponseDto;
using FilingPeriodResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.FilingPeriodResponseDto;
using FilingSummaryResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.FilingSummaryResponseDto;
using SmoCampaignStatementAttestationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoCampaignStatementAttestationResponseDto;
using SmoCampaignStatementFilingSummaryRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.SmoCampaignStatementFilingSummaryRequest;
using SmoCampaignStatementRequest = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.SmoCampaignStatementRequest;
using SmoGeneralInformationResponseDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoGeneralInformationResponseDto;
using SmoRegistrationContactDto = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationContactDto;
using SmoRegistrationSendForAttestationRequest = SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models.SmoRegistrationSendForAttestationRequest;
using TransactionDetailResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.TransactionDetailResponseDto;
using TransactionSummaryResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models.TransactionSummaryResponseDto;
using ValidatedSmoCampaignStatementResponseDto = SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models.ValidatedSmoCampaignStatementResponseDto;
using WorkFlowError = SOS.CalAccess.Models.Common.WorkFlowError;

namespace SOS.CalAccess.FilerPortal.Tests.ControllerServices;

[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[TestFixture]
public class SmoCampaignStatementCtlSvcTests
{
    private SmoCampaignStatementCtlSvc _service;
    private IStringLocalizer<SharedResources> _localizerMock;
    private ISmoCampaignStatementSvc _smoCampaignStatementSvcMock;
    private ITransactionSvc _transactionSvcMock;
    private IReferenceDataSvc _referenceDataSvcMock;
    private ISmoRegistrationSvc _smoRegistrationSvcMock;
    private IDecisionsValidationMapService _decisionsValidationMapServiceMock;
    private Generated.IContactsApi _contactsApiMock;
    private Generated.IFilingsApi _filingsApiMock;
    private Generated.IUsersApi _usersApiMock;
    private IDateTimeSvc _dateTimeSvcMock;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _localizerMock = Substitute.For<IStringLocalizer<SharedResources>>();
        _smoCampaignStatementSvcMock = Substitute.For<ISmoCampaignStatementSvc>();
        _contactsApiMock = Substitute.For<Generated.IContactsApi>();
        _transactionSvcMock = Substitute.For<ITransactionSvc>();
        _referenceDataSvcMock = Substitute.For<IReferenceDataSvc>();
        _smoRegistrationSvcMock = Substitute.For<ISmoRegistrationSvc>();
        _decisionsValidationMapServiceMock = Substitute.For<IDecisionsValidationMapService>();
        _contactsApiMock = Substitute.For<Generated.IContactsApi>();
        _filingsApiMock = Substitute.For<Generated.IFilingsApi>();
        _usersApiMock = Substitute.For<Generated.IUsersApi>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();

        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _localizerMock[Arg.Any<string>()].Returns(call =>
        {
            var key = call.Arg<string>();
            return new LocalizedString(key, key);
        });

        _service = new SmoCampaignStatementCtlSvc(
            _localizerMock,
            _smoCampaignStatementSvcMock,
            _transactionSvcMock,
            _referenceDataSvcMock,
            _smoRegistrationSvcMock,
            _decisionsValidationMapServiceMock,
            _dateTimeSvcMock,
            _usersApiMock,
            _contactsApiMock,
            _filingsApiMock);
    }

    [Test]
    public async Task GetUnreportedFilingPeriods_ReturnsSelectListItem()
    {
        // Arrange
        var id = 1;
        var filingPeriodResponse = new List<FilingPeriodResponseDto>()
        {
            new()
            {
                Id = 1,
                Name = "Test",
                Description = "Test Filing Period",
                StartDate = _dateNow,
                EndDate = _dateNow
            }
        };
        _smoCampaignStatementSvcMock
            .GetUnreportedFilingPeriodsByFilerAsync(id)
            .Returns(filingPeriodResponse);

        // Act
        var result = await _service.GetUnreportedFilingPeriods(id);

        // Assert
        Assert.That(result, Is.InstanceOf<List<SelectListItem>>());
    }

    [Test]
    public async Task CreateSmoCampaignStatement_ReturnsFilingId()
    {
        // Arrange
        long registrationId = 1;
        long filingId = 2;
        _smoCampaignStatementSvcMock
            .CreateSmoCampaignStatementAsync(Arg.Any<SmoCampaignStatementRequest>())
            .Returns(filingId);

        // Act
        var result = await _service.CreateSmoCampaignStatement(registrationId);

        // Assert
        Assert.That(result, Is.EqualTo(filingId));
    }

    [Test]
    public async Task GetCompleteReviewPageViewModel_ReturnsViewModel()
    {
        // Arrange
        var id = 1;
        var filingPeriodResponse = new List<FilingPeriodResponseDto>()
        {
            new()
            {
                Id = 1,
                Name = "Test",
                Description = "Test Filing Period",
                StartDate = _dateNow,
                EndDate = _dateNow
            }
        };
        _smoCampaignStatementSvcMock
            .GetUnreportedFilingPeriodsByFilerAsync(id)
            .Returns(filingPeriodResponse);

        var filingSummaries = new FilingOverviewResponseDto()
        {
            Id = 13,
            FilingPeriodId = 1,
            FilingPeriod = new FilingPeriodResponseDto()
            {
                Id = 5,
                Name = "1st Pre-Election",
                Description = null,
                StartDate = _dateNow,
                EndDate = _dateNow
            },
            FilerId = 65,
            StatusId = 1,
            StatusName = "Draft",
            FilingSummaries =
            {
                new()
                {
                    Id = 1,
                    FilingId = 1,
                    FilingSummaryTypeId = 13,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PaymentReceivedSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 2,
                    FilingId = 1,
                    FilingSummaryTypeId = 14,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PaymentMadeSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 3,
                    FilingId = 1,
                    FilingSummaryTypeId = 15,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PaymentMadeByAgentOrIndependentContractorSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 4,
                    FilingId = 1,
                    FilingSummaryTypeId = 16,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PersonReceiving1000OrMoreSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 5,
                    FilingId = 1,
                    FilingSummaryTypeId = 17,
                    FilingSummaryStatusId = 2,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "CandidateOrMeasureSupportedOrOpposedSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 6,
                    FilingId = 1,
                    FilingSummaryTypeId = 19,
                    FilingSummaryStatusId = 2,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "AmendmentExplanationSummary",
                    FilingSummaryStatusName = "Not Started"
                }
            }
        };
        _smoCampaignStatementSvcMock
            .GetSmoCampaignStatementOverviewAsync(1)
            .Returns(filingSummaries);

        _smoCampaignStatementSvcMock
            .IsSmoCampaignStatementAmendmentAsync(1)
            .Returns(false);

        // Act
        var result = await _service.GetCompleteReviewPageViewModel(1);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementReviewViewModel>());
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task UpdateSmoCampaignStatement_AsyncCallIsMade()
    {
        // Act
        await _service.UpdateSmoCampaignStatement(1, 1);

        // Assert
        await _smoCampaignStatementSvcMock.Received(1).UpdateSmoCampaignStatementAsync(1, Arg.Any<SmoCampaignStatementRequest>());
    }

    [Test]
    public async Task MarkFilingSummaryAsNothingToReport_AsyncCallIsMade()
    {
        // Act
        await _service.MarkFilingSummaryAsNothingToReport(1, 1);

        // Assert
        await _smoCampaignStatementSvcMock.Received(1).MarkFilingSummaryAsNothingToReportAsync(1, 1);
    }

    [Test]
    public async Task CancelSmoCampaignStatementDraft_AsyncCallIsMade()
    {
        // Act
        await _service.CancelSmoCampaignStatementDraft(1);

        // Assert
        await _filingsApiMock.Received(1).CancelFiling(1);
    }

    [Test]
    public async Task GetGeneralInformationPageViewModel_GeneralInformationExists_ReturnsViewModel()
    {
        // Arrange
        var filingId = 1;
        var smoGeneralInformationResponseDto = new SmoGeneralInformationResponseDto();

        _smoCampaignStatementSvcMock
            .GetSmoGeneralInformationById(Arg.Any<long>())
            .Returns(Task.FromResult(smoGeneralInformationResponseDto));

        // Act
        var result = await _service.GetGeneralInformationPageViewModel(filingId);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementGeneralInfoViewModel>());
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetGeneralInformationPageViewModel_ExceptionThrown_ReturnsNull()
    {
        // Arrange
        var filingId = 1;
        _ = _smoCampaignStatementSvcMock.GetSmoGeneralInformationById(filingId)
            .Returns(Task.FromException<SmoGeneralInformationResponseDto>(new InvalidOperationException("Test Exception"))); // Mock exception

        // Act
        var result = await _service.GetGeneralInformationPageViewModel(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementGeneralInfoViewModel>());
    }

    [Test]
    public async Task GetFilingSummaryPageViewModel_FilingSummaryExists_ReturnsViewModel()
    {
        // Arrange
        var filingId = 1;
        var smoFilingSummaryDto = new FilingSummaryResponseDto();
        _smoCampaignStatementSvcMock
            .GetSmoFilingSummaryByFilingId(filingId)
            .Returns(Task.FromResult(new List<FilingSummaryResponseDto> { smoFilingSummaryDto }));

        // Act
        var result = await _service.GetFilingSummaryPageViewModel(filingId);

        // Assert
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementFilingSummaryViewModel>());
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetFilingSummaryPageViewModel_ExceptionThrown_ReturnsNull()
    {
        // Arrange
        var filingId = 1;
        _smoCampaignStatementSvcMock
            .GetSmoFilingSummaryByFilingId(filingId)
            .Returns<Task<List<FilingSummaryResponseDto>>>(_ => throw new InvalidOperationException("Test Exception"));



        // Act
        var result = await _service.GetFilingSummaryPageViewModel(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementFilingSummaryViewModel>());
    }

    [Test]
    public async Task GetPaymentsReceived_ReturnsViewModel()
    {
        // Arrange
        var filingId = 1;
        var response = new TransactionSummaryResponseDto
        {
            FilerId = 1,
            TransactionResponseDtos = new List<TransactionDetailResponseDto>
            {
                new()
                {
                    Id = 1,
                    Amount = 1,
                    TransactionDate = _dateNow,
                }
            },
            SummaryResponseDto = new FilingSummaryResponseDto
            {
                Id = 1,
            }
        };
        _smoCampaignStatementSvcMock.GetTransactionSummaryAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(response);

        // Act
        var result = await _service.GetPaymentsReceived(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task GetPaymentsMade_ReturnsViewModel()
    {
        // Arrange
        var filingId = 1;
        var response = new TransactionSummaryResponseDto
        {
            FilerId = 1,
            TransactionResponseDtos = new List<TransactionDetailResponseDto>
            {
                new()
                {
                    Id = 1,
                    Amount = 1,
                    TransactionDate = _dateNow,
                }
            },
            SummaryResponseDto = new FilingSummaryResponseDto
            {
                Id = 1,
            }
        };
        _smoCampaignStatementSvcMock.GetTransactionSummaryAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(response);

        // Act
        var result = await _service.GetPaymentsMade(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task GetPaymentsMadeByAnAgentOrIndependentContractor_ReturnsViewModel()
    {
        // Arrange
        var filingId = 1;
        var response = new TransactionSummaryResponseDto
        {
            FilerId = 1,
            TransactionResponseDtos = new List<TransactionDetailResponseDto>
            {
                new()
                {
                    Id = 1,
                    Amount = 1,
                    TransactionDate = _dateNow,
                }
            },
            SummaryResponseDto = new FilingSummaryResponseDto
            {
                Id = 1,
            }
        };
        _smoCampaignStatementSvcMock.GetTransactionSummaryAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(response);

        // Act
        var result = await _service.GetPaymentsMadeByAnAgentOrIndependentContractor(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    [Test]
    public async Task GetPersonsReceiving1000OrMore_ReturnsViewModel()
    {
        // Arrange
        var filingId = 1;
        var response = new TransactionSummaryResponseDto
        {
            FilerId = 1,
            TransactionResponseDtos = new List<TransactionDetailResponseDto>
            {
                new()
                {
                    Id = 1,
                    Amount = 1,
                    TransactionDate = _dateNow,
                }
            },
            SummaryResponseDto = new FilingSummaryResponseDto
            {
                Id = 1,
            }
        };
        _smoCampaignStatementSvcMock.GetTransactionSummaryAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(response);

        // Act
        var result = await _service.GetPersonsReceiving1000OrMore(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }


    [Test]
    public async Task GetCandidatesAndMeasureOnPaymentsReceived_ReturnsViewModel()
    {
        // Arrange
        var filingId = 1;
        var response = new TransactionSummaryResponseDto
        {
            FilerId = 1,
            TransactionResponseDtos = new List<TransactionDetailResponseDto>
            {
                new()
                {
                    Id = 1,
                    Amount = 1,
                    TransactionDate = _dateNow,
                }
            },
            SummaryResponseDto = new FilingSummaryResponseDto
            {
                Id = 1,
            }
        };
        _smoCampaignStatementSvcMock.GetTransactionSummaryAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(response);

        // Act
        var result = await _service.GetCandidatesAndMeasureOnPaymentsReceived(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionSummaryViewModel>());
    }

    #region Filer Contact
    [Test]
    public async Task GetFilerContactViewModelAsync_ContactIdNull_ReturnsModelWithFilingIdOnly()
    {
        var filingId = 123;
        var result = await _service.GetFilerContactViewModelAsync(filingId, null, null, null, null);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.ContactId, Is.Null);
        });
    }

    [Test]
    public async Task GetFilerContactViewModelAsync_WithIndividualContact_MapsCorrectly()
    {
        // Arrange
        var contact = new Generated.IndividualContactResponseDto(
            addresses: new List<Generated.AddressDto> { new("123 St", "", 0, "City", "State", "Zip", "Type", "Mailing", "US") },
            emailAddresses: new List<Generated.EmailAddress> { new("<EMAIL>", 1L, 0L, "", "abc123") },
            employer: "Test Inc",
            id: 1L,
            filerId: 1L,
            occupation: "SWE",
            firstName: "John",
            middleName: "Q",
            lastName: "Public",
            phoneNumbers: new List<Generated.PhoneNumberDto> { new("+1", null, "987", 1, false, "1011231234", 1, false, "Work"), },
            typeId: 99L
        );

        _contactsApiMock.GetFilerContactById(Arg.Any<long>()).Returns(contact);

        // Act
        var result = await _service.GetFilerContactViewModelAsync(123, 1, 1, null, null);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.ParticipantType, Is.EqualTo("Individual"));
            Assert.That(result.FirstName, Is.EqualTo("John"));
            Assert.That(result.Email, Is.EqualTo("<EMAIL>"));
        });
    }

    [Test]
    public async Task GetFilerContactViewModelAsync_WithCommitteeContact_MapsCorrectly()
    {
        // Arrange
        var contact = new Generated.FilerCommitteeContactResponseDto(
            addresses: new List<Generated.AddressDto> { new("456 Rd", "", 0, "City", "State", "Zip", "Type", "Mailing", "US") },
            committeeName: "Test Committee",
            emailAddresses: new List<Generated.EmailAddress>(),
            id: 2,
            filerId: 1,
            phoneNumbers: new List<Generated.PhoneNumberDto>(),
            typeId: 99
        );

        _contactsApiMock.GetFilerContactById(2).Returns(contact);

        // Act
        var result = await _service.GetFilerContactViewModelAsync(123, 1, 2, null, null);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.ParticipantType, Is.EqualTo(FilerContactType.Filer.Name));
            Assert.That(result.CommitteeName, Is.EqualTo("Test Committee"));
        });
    }

    [Test]
    public async Task GetFilerContactViewModelAsync_WithOrganizationContact_MapsCorrectly()
    {
        // Arrange
        var contact = new Generated.OrganizationContactResponseDto(
            addresses: new List<Generated.AddressDto> { new("789 Ave", "", 0, "City", "State", "Zip", "Type", "Mailing", "US") },
            emailAddresses: new List<Generated.EmailAddress>(),
            filerId: 3,
            id: 3,
            organizationName: "Test Org",
            phoneNumbers: new List<Generated.PhoneNumberDto>(),
            typeId: 99
        );

        _contactsApiMock.GetFilerContactById(3).Returns(contact);

        // Act
        var result = await _service.GetFilerContactViewModelAsync(123, 3, 3, null, null);

        // Arrange
        Assert.Multiple(() =>
        {
            Assert.That(result.ParticipantType, Is.EqualTo(FilerContactType.Organization.Name));
            Assert.That(result.OrganizationName, Is.EqualTo("Test Org"));
        });
    }

    [Test]
    public async Task GetFilerContactViewModelAsync_WithCandidateContact_MapsCorrectly()
    {
        var contact = new Generated.CandidateContactResponseDto(
            addresses: new List<Generated.AddressDto> { new("321 Ln", "", 0, "City", "State", "Zip", "Type", "Mailing", "US") },
            district: "1",
            emailAddresses: new List<Generated.EmailAddress>(),
            filerId: 4,
            firstName: "Jane",
            id: 4,
            jurisdiction: "Test City",
            lastName: "Doe",
            middleName: "R",
            officeSought: "Mayor",
            phoneNumbers: new List<Generated.PhoneNumberDto>(),
            typeId: 99
        );

        _contactsApiMock.GetFilerContactById(4).Returns(contact);

        var result = await _service.GetFilerContactViewModelAsync(123, 4L, 4L, null, null);

        Assert.Multiple(() =>
        {
            Assert.That(result.ParticipantType, Is.EqualTo("Candidate"));
            Assert.That(result.CandidateFirstName, Is.EqualTo("Jane"));
            Assert.That(result.OfficeSought, Is.EqualTo("Mayor"));
        });
    }

    [Test]
    public async Task SaveOrUpdateContact_UpdatesContact_WhenContactIdExists()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 100,
            ContactId = 22,
            ParticipantType = "Individual",
            IndividualTransactorAddress = new AddressViewModel(),
        };

        _contactsApiMock
            .UpdateFilerContact(22, Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(new Generated.ContactItemResponse(22, "", "", new List<Generated.EmailAddress>(), 100, 22, new List<Generated.PhoneNumber>(), "", "", "", 1, true, [], "", ""));

        var modelState = new ModelStateDictionary();
        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        Assert.That(model.ContactId, Is.EqualTo(22));
    }

    [Test]
    public void BuildFilerContactPayload_ThrowsIfUnsupportedType()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            ParticipantType = "Alien",
            IndividualTransactorAddress = new AddressViewModel()
        };

        // Act & Assert
        var ex = Assert.Throws<InvalidOperationException>(() => FilerContactMapper.BuildFilerContactPayload(model, "UpsertPayor"));
        Assert.That(ex.Message, Does.Contain("Unsupported transactor type"));
    }

    [TestCase("Individual")]
    [TestCase("Filer")]
    [TestCase("Candidate")]
    [TestCase("Organization")]
    public async Task SaveOrUpdateContact_CreatesContact_ForEachParticipantType(string participantType)
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 100,
            ContactId = null,
            ParticipantType = participantType,
            FirstName = "A",
            LastName = "B",
            MiddleName = "C",
            CommitteeName = "ComX",
            CandidateFirstName = "Jane",
            CandidateLastName = "Doe",
            CandidateMiddleName = "Z",
            OfficeSought = "Mayor",
            JurisdictionName = "Test City",
            District = "5",
            OrganizationName = "OrgX",
            Employer = "ACME",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                Number = "123-456",
            },
            OrganizationTransactorAddress = new()
            {
                Street = "1 St",
                City = "City",
                State = "CA",
                Zip = "99999",
                Country = "US",
                Purpose = "Mailing"
            }
        };

        _contactsApiMock
            .CreateFilerContact(Arg.Any<long>(), Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(new Generated.ContactItemResponse(1, "City", "US", new List<Generated.EmailAddress>(), 55, 1, new List<Generated.PhoneNumber>(), "CA", "1 St", "", 1, true, [], "", "99999"));

        var modelState = new ModelStateDictionary();

        // Act
        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        // Assert
        Assert.That(model.ContactId, Is.EqualTo(1));
    }

    [Test]
    public void SaveOrUpdateContact_ThrowsOnUnsupportedType()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 100,
            ParticipantType = "Alien",
            IndividualTransactorAddress = new()
        };

        _smoCampaignStatementSvcMock
            .GetSmoGeneralInformationById(100)
            .Returns(new SmoGeneralInformationResponseDto
            {
                RegistrationDetail = new() { FilerId = 55 }
            });

        var modelState = new ModelStateDictionary();

        var ex = Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState)
        );

        Assert.That(ex!.Message, Is.EqualTo("Unsupported transactor type: Alien"));
    }


    [Test]
    public async Task GetPaymentReceived02PageViewModel_FallbacksToFirstAddress_WhenNoMailingAddress()
    {
        // Arrange
        var filingId = 123;
        long? contactId = 456;

        var mockContact = new Generated.IndividualContactResponseDto(
            addresses: new List<Generated.AddressDto>
            {
                new("123 Billing St", "", 0, "City", "State", "Zip", "Type", "Billing", "US"),
                new("456 Home St", "", 0, "City", "State", "Zip", "Type", "Home", "US")
            },
            emailAddresses: new List<Generated.EmailAddress> { new("<EMAIL>", 1L, 0L, "", "abc123") }, employer: "Test Inc",
            id: 1L,
            filerId: 1L,
            occupation: "SWE",
            firstName: "John",
            middleName: "Q",
            lastName: "Public",
            phoneNumbers: new List<Generated.PhoneNumberDto> { new("+1", null, "987", 1, false, "1011231234", 1, false, "Work"), },
            typeId: 99L
        );
        _contactsApiMock.GetFilerContactById(contactId.Value).Returns(mockContact);

        // Act
        var result = await _service.GetFilerContactViewModelAsync(filingId, 1L, contactId, null, TransactionType.PaymentReceived.Name);

        // Assert
        Assert.That(result.IndividualTransactorAddress.Street, Is.EqualTo("Zip"));
    }

    [Test]
    public async Task SaveOrUpdateContact_WithExistingContact_CallsUpdate()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 123,
            ContactId = 77,
            ParticipantType = "Individual",
            FirstName = "Jane",
            LastName = "Doe",
            IndividualTransactorAddress = new AddressViewModel()
        };

        var modelState = new ModelStateDictionary();

        _contactsApiMock
            .UpdateFilerContact(77, Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(new Generated.ContactItemResponse(77, "", "", [], 1, 77, [], "", "", "", 0, true, [], "", ""));

        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        await _contactsApiMock.Received(1).UpdateFilerContact(77, Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>());
    }

    [Test]
    public async Task SaveOrUpdateContact_WhenInvalidContact_AddsModelStateErrors()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 100,
            ParticipantType = "Individual",
            ContactId = null,
            IndividualTransactorAddress = new AddressViewModel()
        };

        var response = new Generated.ContactItemResponse(
            id: 1, city: "", country: "", emailAddresses: [],
            filerId: 55, addressId: 1, phoneNumbers: [], state: "", street: "", street2: "",
            typeId: 1, valid: false,
            validationErrors: new List<Generated.WorkFlowError>
            {
                new("FirstName", "ERR01", "Validation", "First is required"),
                new("Address.Street", "ERR02", "Validation", "Street is required")
            },
            website: "", zipCode: ""
        );

        _contactsApiMock
            .CreateFilerContact(Arg.Any<long>(), Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(response);

        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                Arg.Any<ModelStateDictionary>()))
            .Do(call =>
            {
                var modelState = call.ArgAt<ModelStateDictionary>(2);
                modelState.AddModelError("FirstName", "First is required");
                modelState.AddModelError("TransactorAddress.Street", "Street is required");
            });

        var modelState = new ModelStateDictionary();

        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        Assert.Multiple(() =>
        {
            Assert.That(modelState.ContainsKey("FirstName"), Is.True);
            Assert.That(modelState.ContainsKey("TransactorAddress.Street"), Is.True);
        });
    }

    [Test]
    public async Task ApplyContactValidationErrorsToModelState_Individual_AddsModelErrors()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 999,
            ParticipantType = "Individual"
        };

        var response = new Generated.ContactItemResponse(
            id: 1, city: "", country: "", emailAddresses: [], filerId: 1, addressId: 1,
            phoneNumbers: [], state: "", street: "", street2: "", typeId: 1, valid: false,
            validationErrors: new List<Generated.WorkFlowError>
            {
            new("FirstName", "ERR01", "Validation", "First is required"),
            new("Address.City", "ERR02", "Validation", "City is required")
            },
            website: "", zipCode: ""
        );

        var modelState = new ModelStateDictionary();

        _contactsApiMock
            .CreateFilerContact(Arg.Any<long>(), Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(response);

        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                modelState))
            .Do(_ =>
            {
                modelState.AddModelError("FirstName", "First is required");
                modelState.AddModelError("TransactorAddress.City", "City is required");
            });

        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        Assert.Multiple(() =>
        {
            Assert.That(modelState.ContainsKey("FirstName"));
            Assert.That(modelState.ContainsKey("TransactorAddress.City"));
        });
    }

    [Test]
    public async Task ApplyContactValidationErrorsToModelState_Candidate_RemapsFieldNames()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 999,
            ParticipantType = "Candidate"
        };

        var response = new Generated.ContactItemResponse(
            id: 2, city: "", country: "", emailAddresses: [], filerId: 2, addressId: 2,
            phoneNumbers: [], state: "", street: "", street2: "", typeId: 1, valid: false,
            validationErrors: new List<Generated.WorkFlowError>
            {
            new("FirstName", "ERR01", "Validation", "First is required"),
            new("LastName", "ERR02", "Validation", "Last is required")
            },
            website: "", zipCode: ""
        );

        var modelState = new ModelStateDictionary();

        _contactsApiMock
            .CreateFilerContact(Arg.Any<long>(), Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(response);

        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                modelState))
            .Do(_ =>
            {
                modelState.AddModelError("CandidateFirstName", "First is required");
                modelState.AddModelError("CandidateLastName", "Last is required");
            });

        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        Assert.Multiple(() =>
        {
            Assert.That(modelState.ContainsKey("CandidateFirstName"));
            Assert.That(modelState.ContainsKey("CandidateLastName"));
        });
    }

    [Test]
    public async Task ApplyContactValidationErrorsToModelState_Candidate_MapsAllNameFields()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 999,
            ParticipantType = "Candidate"
        };

        var response = new Generated.ContactItemResponse(
            id: 99,
            city: "",
            country: "",
            emailAddresses: [],
            filerId: 88,
            addressId: 1,
            phoneNumbers: [],
            state: "",
            street: "",
            street2: "",
            typeId: 1,
            valid: false,
            validationErrors: new List<Generated.WorkFlowError>
            {
                new("FirstName", "ERR01", "Validation", "First is required"),
                new("MiddleName", "ERR02", "Validation", "Middle is required"),
                new("LastName", "ERR03", "Validation", "Last is required")
            },
            website: "",
            zipCode: ""
        );

        var modelState = new ModelStateDictionary();

        // Mock CreateFilerContact returning our fake response
        _contactsApiMock
            .CreateFilerContact(Arg.Any<long>(), Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(response);

        // Simulate field mapping applied
        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                modelState))
            .Do(_ =>
            {
                modelState.AddModelError("CandidateFirstName", "First is required");
                modelState.AddModelError("CandidateMiddleName", "Middle is required");
                modelState.AddModelError("CandidateLastName", "Last is required");
            });

        // Act
        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        // Assert that all 3 mapped candidate fields exist
        Assert.Multiple(() =>
        {
            Assert.That(modelState.ContainsKey("CandidateFirstName"), "CandidateFirstName was not added to ModelState");
            Assert.That(modelState.ContainsKey("CandidateMiddleName"), "CandidateMiddleName was not added to ModelState");
            Assert.That(modelState.ContainsKey("CandidateLastName"), "CandidateLastName was not added to ModelState");
        });
    }

    [Test]
    public async Task ApplyContactValidationErrorsToModelState_Committee_AddsCommitteeErrors()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 999,
            ParticipantType = FilerContactType.Filer.Name
        };

        var response = new Generated.ContactItemResponse(
            id: 3, city: "", country: "", emailAddresses: [], filerId: 3, addressId: 3,
            phoneNumbers: [], state: "", street: "", street2: "", typeId: 1, valid: false,
            validationErrors: new List<Generated.WorkFlowError>
            {
                new("CommitteeId", "ERR01", "Validation", "Missing committee"),
            },
            website: "", zipCode: ""
        );

        var modelState = new ModelStateDictionary();

        _contactsApiMock
            .CreateFilerContact(Arg.Any<long>(), Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(response);

        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                modelState))
            .Do(_ =>
            {
                modelState.AddModelError("CommitteeName", "Missing committee");
            });

        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        Assert.That(modelState.ContainsKey("CommitteeName"));
    }

    [Test]
    public async Task ApplyContactValidationErrorsToModelState_Organization_AddsOrgErrors()
    {
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 999,
            ParticipantType = FilerContactType.Organization.Name,
        };

        var response = new Generated.ContactItemResponse(
            id: 4, city: "", country: "", emailAddresses: [], filerId: 4, addressId: 4,
            phoneNumbers: [], state: "", street: "", street2: "", typeId: 1, valid: false,
            validationErrors: new List<Generated.WorkFlowError>
            {
                new("OrganizationName", "ERR01", "Validation", "Org required")
            },
            website: "",
            zipCode: ""
        );

        var modelState = new ModelStateDictionary();

        _contactsApiMock
            .CreateFilerContact(Arg.Any<long>(), Arg.Any<Generated.UpsertFilerContactRequest>(), Arg.Any<CancellationToken>())
            .Returns(response);

        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                modelState))
            .Do(_ =>
            {
                modelState.AddModelError("OrganizationName", "Org required");
            });

        await _service.SaveOrUpdateContactAsync(model, DisclosureConstants.Transaction.PayorFilerContactForm, modelState);

        Assert.That(modelState.ContainsKey("OrganizationName"));
    }
    #endregion

    #region Payment Made
    #endregion

    #region GetVerificationPageViewModel

    [Test]
    public async Task GetVerificationPageViewModel_NotAttest_ReturnViewModel()
    {
        // Arrange
        var filingId = 1;
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", 1, "Test");
        var filerRole = new Generated.FilerRole(true, 0, "", default!, 1, 1, false, false, 0, FilerRole.SlateMailerOrg_PrincipalOfficer.Name, default!);
        var filerUserResponse = new Generated.FilerUserDto(1, "", filerRole, 1, 1, default!, 1);
        var smoOfficer = new List<SmoRegistrationContactDto>
        {
            new ()
            {
                CanAuthorize = true,
                Id = 1,
                Title = "President",
                Email = "<EMAIL>",
                StartDate = _dateNow,
            }
        };
        var smoResponse = new FilingOverviewResponseDto
        {
            Id = filingId,
            FilerId = 1,
        };


        _smoCampaignStatementSvcMock.GetSmoCampaignStatementOverviewAsync(filingId).Returns(smoResponse);
        _usersApiMock.GetSelf().Returns(userResponse);
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementResponsibleOfficerContactsAsync(filingId).Returns(smoOfficer);

        // Act
        var result = await _service.GetVerificationPageViewModel(filingId, false);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementVerificationViewModel>());
            Assert.That(result!.Id, Is.EqualTo(filingId));
        });
    }
    [Test]
    public async Task GetVerificationPageViewModel_Attest_ReturnViewModel()
    {
        // Arrange
        var filingId = 1;
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", 1, "Test");
        var filerRole = new Generated.FilerRole(true, 0, "", default!, 1, 1, false, false, 0, FilerRole.SlateMailerOrg_PrincipalOfficer.Name, default!);
        var filerUserResponse = new Generated.FilerUserDto(1, "", filerRole, 1, 1, default!, 1);
        var smoOfficer = new List<SmoRegistrationContactDto>
        {
            new ()
            {
                CanAuthorize = true,
                Id = 1,
                Title = "President",
                Email = "<EMAIL>",
                StartDate = _dateNow,
            }
        };
        var smoResponse = new FilingOverviewResponseDto
        {
            Id = filingId,
            FilerId = 1,
        };

        var attestationResponse = new SmoCampaignStatementAttestationResponseDto
        {
            FirstName = "First",
            LastName = "Last",
            Title = "Title",
            ExecutedAt = _dateNow,
        };

        _smoCampaignStatementSvcMock.GetSmoCampaignStatementOverviewAsync(filingId).Returns(smoResponse);
        _usersApiMock.GetSelf().Returns(userResponse);
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementResponsibleOfficerContactsAsync(filingId).Returns(smoOfficer);
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementAttestationAsync(filingId).Returns(attestationResponse);
        // Act
        var result = await _service.GetVerificationPageViewModel(filingId, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            _smoCampaignStatementSvcMock.Received(1).GetSmoCampaignStatementAttestationAsync(filingId);
        });
    }
    [Test]
    public async Task GetVerificationPageViewModel_Exception_ReturnViewModel()
    {
        // Arrange
        var filingId = 1;
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementOverviewAsync(filingId).Throws(new KeyNotFoundException());

        // Act
        var result = await _service.GetVerificationPageViewModel(filingId, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementVerificationViewModel>());
            Assert.That(result!.Id, Is.Null);
        });
    }
    #endregion

    #region GetConfirmationPageViewModel
    [Test]
    public async Task GetConfirmationPageViewModel_ReturnViewModel()
    {
        // Arrange
        var filingId = 1;

        _smoCampaignStatementSvcMock.GetSmoCampaignStatementPendingItemsAsync(filingId).Returns(new List<Services.Business.FilerRegistration.Registrations.Models.PendingItemDto>());

        // Act
        var result = await _service.GetConfirmationPageViewModel(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementConfirmationViewModel>());
            Assert.That(result!.Id, Is.EqualTo(filingId));
        });
    }
    [Test]
    public async Task GetConfirmationPageViewModel_Exception_ReturnViewModel()
    {
        // Arrange
        var filingId = 1;
        var userResponse = new Generated.UserItemResponse("<EMAIL>", "Test", filingId, "Test");
        var smoResponse = new FilingOverviewResponseDto
        {
            Id = filingId,
            FilerId = 1,
        };

        _smoCampaignStatementSvcMock.GetSmoCampaignStatementPendingItemsAsync(filingId).Throws(new KeyNotFoundException());

        // Act
        var result = await _service.GetConfirmationPageViewModel(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementConfirmationViewModel>());
            Assert.That(result!.Id, Is.Null);
        });
    }
    #endregion

    #region AttestAsync
    [Test]
    public async Task AttestAsync_Success()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var model = new SmoCampaignStatementVerificationViewModel() { Id = 1 };
        var response = new ValidatedSmoCampaignStatementResponseDto
        {
            ApprovedAt = _dateNow,
            Valid = true,
        };
        _smoCampaignStatementSvcMock.AttestStatementAsync(1).Returns(response);

        // Act
        await _service.AttestSmoCampaignStatement(model, modelState);

        // Assert
        await _smoCampaignStatementSvcMock.Received(1).AttestStatementAsync(model.Id.Value);
    }

    [Test]
    public async Task AttestAsync_InvalidResponse_ThrowsError()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var model = new SmoCampaignStatementVerificationViewModel() { Id = 1 };
        var response = new ValidatedSmoCampaignStatementResponseDto
        {
            ApprovedAt = _dateNow,
            Valid = false,
            ValidationErrors = new()
            {
                new("FirstName", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            }
        };
        _smoCampaignStatementSvcMock.AttestStatementAsync(1).Returns(response);

        // Act
        await _service.AttestSmoCampaignStatement(model, modelState);

        // Assert
        await _smoCampaignStatementSvcMock.Received(1).AttestStatementAsync(model.Id.Value);

    }
    #endregion

    #region SendForAttestation
    [Test]
    public async Task SendForAttestation_Success()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            SelectedResponsibleOfficersContactIds = new List<long> { }
        };
        var response = new ValidatedSmoCampaignStatementResponseDto
        {
            ApprovedAt = _dateNow,
            Valid = true,
        };
        _smoCampaignStatementSvcMock.SendForAttestationAsync(1, Arg.Any<SmoRegistrationSendForAttestationRequest>()).Returns(response);
        await _service.SendForAttestation(model, modelState);
        // Assert
        await _smoCampaignStatementSvcMock.Received(1).SendForAttestationAsync(
            model.Id.Value,
            Arg.Is<SmoRegistrationSendForAttestationRequest>(req =>
                req.RegistrationRegistrationContactIds.SequenceEqual(model.SelectedResponsibleOfficersContactIds))
        );
    }
    [Test]
    public async Task SendForAttestation_InvalidResponse_ThrowsError()
    {
        // Arrange
        var modelState = new ModelStateDictionary();
        var model = new SmoCampaignStatementVerificationViewModel
        {
            Id = 1,
            SelectedResponsibleOfficersContactIds = new List<long> { }
        };
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long> { }
        };
        var response = new ValidatedSmoCampaignStatementResponseDto
        {
            ApprovedAt = _dateNow,
            Valid = false,
            ValidationErrors = new()
            {
                new("FirstName", "ErrGlobal0001", "Validation", "{{Field Name}} is required"),
            }
        };

        _smoCampaignStatementSvcMock.SendForAttestationAsync(1, Arg.Any<SmoRegistrationSendForAttestationRequest>()).Returns(response);

        // Act
        await _service.SendForAttestation(model, modelState);

        // Assert
        await _smoCampaignStatementSvcMock.Received(1).SendForAttestationAsync(
            model.Id.Value,
            Arg.Is<SmoRegistrationSendForAttestationRequest>(req =>
                req.RegistrationRegistrationContactIds.SequenceEqual(model.SelectedResponsibleOfficersContactIds))
    );
    }
    #endregion

    #region Shared
    [Test]
    public async Task SubmitTransactionSummaryUnitemizedPaymentForm_ShouldUpdateFilingSummary()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            FilingSummaryId = 1,
            UnitemizedPaymentLessThan100 = 100,
        };
        var modelState = new ModelStateDictionary();
        var response = new FilingSummaryResponseDto()
        {
            Id = 1,
            FilingId = 1,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        _smoCampaignStatementSvcMock
            .UpdateFilingSummaryAsync(1, 1, Arg.Any<SmoCampaignStatementFilingSummaryRequest>())
            .Returns(response);

        // Act
        var result = await _service.SubmitTransactionSummaryUnitemizedPaymentForm(model, modelState);

        // Assert
        Assert.That(result, Is.EqualTo(response));
    }

    [Test]
    public async Task SubmitTransactionSummaryUnitemizedPaymentForm_ShouldCatchError()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionSummaryViewModel()
        {
            Id = 1,
            FilingSummaryId = 1,
            UnitemizedPaymentLessThan100 = 100,
        };
        var modelState = new ModelStateDictionary();

        // Act
        var result = await _service.SubmitTransactionSummaryUnitemizedPaymentForm(model, modelState);

        // Assert
        Assert.That(result, Is.EqualTo(null));
    }

    [Test]
    public async Task DeleteTransactionHistory_ShouldCallSvc()
    {
        // Arrange
        var filingId = 1;
        var transactionId = 2;

        // Act
        await _service.DeleteTransactionEntry(filingId, transactionId);

        // Assert
        await _transactionSvcMock.Received(1).DeleteTransactionAsync(filingId, transactionId);
    }


    [Test]
    public async Task DeleteDisclosureWithoutPaymentReceived_ShouldCallSvc()
    {
        // Arrange
        var filingId = 1;
        var disclosureWithoutPaymentId = 2;

        // Act
        await _service.DeleteDisclosureWithoutPaymentReceived(filingId, disclosureWithoutPaymentId);

        // Assert
        await _smoCampaignStatementSvcMock.Received(1).DeleteDisclosureWithoutPaymentReceivedAsync(filingId, disclosureWithoutPaymentId);
    }

    [Test]
    public async Task SearchSmoOfficersAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;
        var query = "Search";
        var officers = new List<SmoRegistrationContactDto>
        {
            new()
            {
                Id = 1L,
                FirstName = "Test",
                LastName = "Test",
                RegistrationId = 1,
            }
        };
        var summaries = new TransactionSummaryResponseDto
        {
            FilerId = filerId,
            SummaryResponseDto = new FilingSummaryResponseDto(),
            TransactionResponseDtos = new List<TransactionDetailResponseDto>
            {
                new PersonReceiving1000OrMoreResponseDto()
                {
                    Name = "Test Test1",
                },
                new PersonReceiving1000OrMoreResponseDto()
                {
                    Name = "",
                }
            },
        };
        _smoRegistrationSvcMock.SearchLatestAcceptedSmoRegistrationOfficerByNameAsync(Arg.Any<long>(), Arg.Any<string>()).Returns(Task.FromResult(officers));
        _smoCampaignStatementSvcMock.GetTransactionSummaryAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(summaries));

        // Act
        var result = await _service.SearchSmoOfficersAsync(filerId, filingId, query);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<SmoRegistrationContactDto>>());
            Assert.That(result.Any(x => x.FirstName == "Test"), Is.True);
        });
    }

    [Test]
    public void ApplyDecisionsToTransactionSummaryModelState_ValidRequest_ShouldApplyErrorToModelState()
    {
        // Arrange
        var response = new FilingSummaryResponseDto
        {
            Valid = false,
            ValidationErrors = new List<WorkFlowError>
            {
                new("ErrorKey", "errorCode", "Validation", "message")
            }
        };
        var modelState = new ModelStateDictionary();

        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                Arg.Any<ModelStateDictionary>()))
            .Do(call =>
            {
                var modelState = call.Arg<ModelStateDictionary>();
                modelState.AddModelError("UnitemizedPaymentLessThan100", "UnitemizedPaymentLessThan100 is required");
            });

        var methodInfo = typeof(SmoCampaignStatementCtlSvc).GetMethod("ApplyDecisionsToTransactionSummaryModelState", BindingFlags.Instance | BindingFlags.NonPublic)!;

        // Act
        methodInfo.Invoke(_service, [response, modelState]);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(modelState, Is.Not.Null);
            Assert.That(modelState.ContainsKey("UnitemizedPaymentLessThan100"), Is.True);
        });
    }
    #endregion

    #region Transaction Entry
    [Test]
    public async Task UpdateModelWithTransactionAsync_WithPaymentReceivedResponse_MapsCorrectly()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1,
        };
        var response = new PaymentReceivedResponseDto
        {
            FilingId = 1,
            CandidateOrMeasure = "Candidate",
            Name = "Name",
            Position = "Position",
            StanceOnCandidateDto = new StanceOnCandidateDto
            {
                Id = 1,
                CandidateId = 1,
                CandidateName = "Name",
                FirstName = "Name",
                LastName = "Name",
                MiddleName = "Name",
                District = "District",
                Jurisdiction = "Jurisdiction",
                OfficeSought = "OfficeSought"
            },
            StanceOnBallotMeasureDto = new StanceOnBallotMeasureDto
            {
                Id = 1,
                BallotMeasureId = 1,
                BallotNumberOrLetter = "Code",
                Jurisdiction = "Jurisdiction",
                BallotLetter = "BallotLetter",
                Title = "Title",
            }
        };
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), TransactionType.PaymentReceived.Name)
            .Returns(Task.FromResult<TransactionDetailResponseDto>(response));

        // Act
        var result = await _service.UpdateModelWithTransactionAsync(model, TransactionType.PaymentReceived.Name);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
            Assert.That(result.Id, Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task UpdateModelWithTransactionAsync_PaymentMadeByAgentOrIndependentContractorResponse_MapsCorrectly()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1,
        };
        var response = new PaymentMadeByAgentOrIndependentContractorResponseDto
        {
            Id = 1,
            FilingId = 1,
            Name = "Name",
            Description = "Description",
            AgentOrIndependentContractorName = "Agent",
            ContactId = 1,
            TransactionDate = _dateNow,
            Notes = "notes",
            Amount = 1m,
        };
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), TransactionType.PaymentMade.Name)
            .Returns(Task.FromResult<TransactionDetailResponseDto>(response));

        // Act
        var result = await _service.UpdateModelWithTransactionAsync(model, TransactionType.PaymentMade.Name);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
            Assert.That(result.Id, Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task UpdateModelWithTransactionAsync_PaymentMadeResponse_MapsCorrectly()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1,
        };
        var response = new PaymentMadeResponseDto
        {
            Id = 1,
            FilingId = 1,
            Name = "Name",
            Description = "Description",
            ContactId = 1,
            TransactionDate = _dateNow,
            Notes = "notes",
            Amount = 1m,
        };
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), TransactionType.PaymentMade.Name)
            .Returns(Task.FromResult<TransactionDetailResponseDto>(response));

        // Act
        var result = await _service.UpdateModelWithTransactionAsync(model, TransactionType.PaymentMade.Name);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
            Assert.That(result.Id, Is.EqualTo(model.Id));
        });
    }

    [Test]
    public async Task UpdateModelWithTransactionAsync_PersonReceiving1000OrMoreResponseDto_MapsCorrectly()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = 1,
        };
        var response = new PersonReceiving1000OrMoreResponseDto
        {
            Id = 1,
            FilingId = 1,
            Name = "Name",
            ContactId = 1,
            TransactionDate = _dateNow,
            Notes = "notes",
            Amount = 1m,
        };
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), TransactionType.PersonReceiving1000OrMore.Name)
            .Returns(Task.FromResult<TransactionDetailResponseDto>(response));

        // Act
        var result = await _service.UpdateModelWithTransactionAsync(model, TransactionType.PersonReceiving1000OrMore.Name);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatementTransactionEntryViewModel>());
            Assert.That(result.Id, Is.EqualTo(model.Id));
        });
    }
    #endregion

    #region Transaction Entry - Payment Received
    [Test]
    public async Task GetPaymentReceived03ViewModelAsync_WithTransactionIdAndOverrideData_ReturnsExpectedModel()
    {
        // Arrange
        var parameters = new PaymentReceivedParameters
        {
            Id = 1,
            ContactId = 2,
            TransactionId = 3,
            Jurisdiction = "OverriddenJurisdiction",
            PertainsTo = "BallotMeasure",
            Position = "OverriddenPosition",
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
            Notes = "Notes",
        };

        var response = new PaymentReceivedResponseDto
        {
            Id = 3,
            FilingId = 1,
            CandidateOrMeasure = "Candidate",
            Name = "Name",
            Position = "Position",
        };
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), TransactionType.PaymentReceived.Name)
            .Returns(Task.FromResult<TransactionDetailResponseDto>(response));

        // Act
        var result = await _service.GetPaymentReceived03ViewModelAsync(parameters);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(parameters.Id));
            Assert.That(result.TransactionId, Is.EqualTo(parameters.TransactionId));
            Assert.That(result.Position, Is.EqualTo("OverriddenPosition")); // overridden
            Assert.That(result.Jurisdiction, Is.EqualTo("OverriddenJurisdiction")); // overridden
        });
    }

    [Test]
    public async Task GetPaymentReceived04ViewModelAsync_WithTransactionIdAndOverrideData_ReturnsExpectedModel()
    {
        // Arrange
        var parameters = new PaymentReceivedParameters
        {
            Id = 1,
            ContactId = 2,
            TransactionId = 3,
            Jurisdiction = "OverriddenJurisdiction",
            PertainsTo = "BallotMeasure",
            Position = "OverriddenPosition",
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
            Notes = "Notes",
        };

        var response = new PaymentReceivedResponseDto
        {
            Id = 3,
            FilingId = 1,
            CandidateOrMeasure = "Candidate",
            Name = "Name",
            Position = "Position",
        };
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), TransactionType.PaymentReceived.Name)
            .Returns(Task.FromResult<TransactionDetailResponseDto>(response));

        var validateResponse = new PaymentsReceivedCumulativeAmountDto(
            id: null,
            previouslyUnitemizedAmount: 0m,
            cumulativeAmount: 0m
        );
        _smoCampaignStatementSvcMock
            .GetPaymentReceivedCumulativeAmountAsync(
                Arg.Any<long>(),
                Arg.Any<PaymentReceivedRequest>())
            .Returns(Task.FromResult(validateResponse));
        // Act
        var result = await _service.GetPaymentReceived04ViewModelAsync(parameters);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(parameters.Id));
            Assert.That(result.TransactionId, Is.EqualTo(parameters.TransactionId));
            Assert.That(result.Position, Is.EqualTo("OverriddenPosition")); // overridden
            Assert.That(result.Jurisdiction, Is.EqualTo("OverriddenJurisdiction")); // overridden
        });
    }

    [Test]
    public async Task SaveTransactionAsync_PaymentReceived_WithId_ShouldUpdateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            TransactionId = transactionId,
            ContactId = 1L,
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
            Notes = "notes",
            PertainsTo = CandidateOrMeasure.BallotMeasure,
            BallotMeasureId = 1,
            BallotNumberOrLetter = "Code",
            Jurisdiction = "Jurisdiction",
            BallotLetter = "BallotLetter",
            BallotMeasureTitle = "Title",
            AttachedFileGuidsJson = JsonConvert.SerializeObject(new List<string> { Guid.NewGuid().ToString() }),
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        _ = _smoCampaignStatementSvcMock.UpdateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<PaymentReceivedRequest>())
            .Returns(Task.FromResult(response));

        // Act
        await _service.SaveTransactionAsync(model, modelState, TransactionType.PaymentReceived.Name);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .UpdateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<PaymentReceivedRequest>());
    }

    [Test]
    public async Task SaveTransactionAsync_PaymentMade_WithoutId_ShouldCreateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            ContactId = 1L,
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
            Notes = "Notes",
            CodeId = 1,
            Description = "Description",
            AttachedFileGuidsJson = JsonConvert.SerializeObject(new List<string> { Guid.NewGuid().ToString() }),
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        _smoCampaignStatementSvcMock.CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeRequest>()).Returns(Task.FromResult(response));

        // Act
        await _service.SaveTransactionAsync(model, modelState, TransactionType.PaymentMade.Name);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeRequest>());
    }

    [Test]
    public async Task SaveTransactionAsync_PaymentMade_WithAttachedFileGuidsJson_ShouldCreateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            ContactId = 1L,
            TransactionAmount = 1m,
            TransactionDate = DateTime.Now,
            Notes = "Notes",
            CodeId = 1,
            Description = "Description",
            AttachedFileGuidsJson = "[\"file-guid-1\", \"file-guid-2\", \"file-guid-3\"]",
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        _smoCampaignStatementSvcMock.CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeRequest>()).Returns(Task.FromResult(response));

        // Act
        await _service.SaveTransactionAsync(model, modelState, TransactionType.PaymentMade.Name);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeRequest>());
    }

    [Test]
    public async Task SaveTransactionAsync_PaymentMade_WithoutAttachedFileGuidsJson_ShouldCreateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            ContactId = 1L,
            TransactionAmount = 1m,
            TransactionDate = DateTime.Now,
            Notes = "Notes",
            CodeId = 1,
            Description = "Description",
            AttachedFileGuidsJson = "null",
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        _smoCampaignStatementSvcMock.CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeRequest>()).Returns(Task.FromResult(response));

        // Act
        await _service.SaveTransactionAsync(model, modelState, TransactionType.PaymentMade.Name);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeRequest>());
    }

    [Test]
    public async Task SaveTransactionAsync_PaymentMadeByAgent_WithoutId_ShouldCreateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            ContactId = 1L,
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
            Notes = "Notes",
            CodeId = 1,
            Description = "Description",
            AgentOrIndependentContractorName = "Agent",
            IsPaidByAgentOrContractor = true,
            AttachedFileGuidsJson = JsonConvert.SerializeObject(new List<string> { Guid.NewGuid().ToString() }),
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        _smoCampaignStatementSvcMock.CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeByAgentOrIndependentContractorRequest>())
            .Returns(Task.FromResult(response));

        // Act
        await _service.SaveTransactionAsync(model, modelState, TransactionType.PaymentMade.Name);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .CreateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<PaymentMadeByAgentOrIndependentContractorRequest>());
    }

    [Test]
    public async Task SaveTransactionAsync_PersonReceiving_WithId_ShouldUpdateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            TransactionId = transactionId,
            ContactId = 1L,
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
            Notes = "notes",
            AttachedFileGuidsJson = JsonConvert.SerializeObject(new List<string> { Guid.NewGuid().ToString() }),
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        _ = _smoCampaignStatementSvcMock.UpdateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<PersonReceiving1000OrMoreRequest>()).Returns(Task.FromResult(response));

        // Act
        await _service.SaveTransactionAsync(model, modelState, TransactionType.PersonReceiving1000OrMore.Name);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .UpdateSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<PersonReceiving1000OrMoreRequest>());
    }

    [Test]
    public async Task PopulateUnitemizedAndCumulativeAmounts_ValidResponse_SetsModelAmountsCorrectly()
    {
        // Arrange
        var filingId = 1L;
        var transactionId = 10L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            TransactionId = transactionId,
            ContactId = 5L,
            TransactionAmount = 100m,
            Jurisdiction = "State",
            Position = "Support",
            CandidateId = 2,
            BallotMeasureId = null
        };

        var responseDto = new PaymentsReceivedCumulativeAmountDto(
            id: filingId,
            previouslyUnitemizedAmount: 50m,
            cumulativeAmount: 200m
        );

        _smoCampaignStatementSvcMock
            .GetPaymentReceivedCumulativeAmountAsync(
                Arg.Is(filingId),
                Arg.Any<PaymentReceivedRequest>())
            .Returns(Task.FromResult(responseDto));

        // Act
        await _service.PopulateUnitemizedAndCumulativeAmounts(model);

        // Assert
        Assert.Multiple(() =>
        {
            // UnitemizedAmount should be set to response.PreviouslyUnitemizedAmount (50m)
            Assert.That(model.UnitemizedAmount, Is.EqualTo(50m));

            // CumulativeAmountToDate should be response.CumulativeAmount (200m) + model.TransactionAmount (100m)
            Assert.That(model.CumulativeAmountToDate, Is.EqualTo(200m + 100m));
        });
    }

    [Test]
    public async Task PopulateUnitemizedAndCumulativeAmounts_NullValues_DefaultsToZeroPlusTransactionAmount()
    {
        // Arrange
        var filingId = 2L;
        var transactionId = 20L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            TransactionId = transactionId,
            ContactId = 7L,
            TransactionAmount = 150m,
            Jurisdiction = "State",
            Position = "Oppose",
            CandidateId = null,
            BallotMeasureId = 3
        };

        var responseDto = new PaymentsReceivedCumulativeAmountDto(
            id: filingId,
            previouslyUnitemizedAmount: null,
            cumulativeAmount: null
        );

        _smoCampaignStatementSvcMock
            .GetPaymentReceivedCumulativeAmountAsync(
                Arg.Is(filingId),
                Arg.Any<PaymentReceivedRequest>())
            .Returns(Task.FromResult(responseDto));

        // Act
        await _service.PopulateUnitemizedAndCumulativeAmounts(model);

        // Assert
        Assert.Multiple(() =>
        {
            // PreviouslyUnitemizedAmount was null → model.UnitemizedAmount defaults to 0
            Assert.That(model.UnitemizedAmount, Is.EqualTo(0m));

            // CumulativeAmount was null → (0) + model.TransactionAmount (150m)
            Assert.That(model.CumulativeAmountToDate, Is.EqualTo(0m + 150m));
        });
    }
    #endregion

    #region Candidate or Measurer Without Payment Received
    [Test]
    public async Task GetCandidateOrMeasureWithoutPaymentReceivedAsync_WithValidRequest_ReturnsExpectedModel()
    {
        // Arrange
        var filingId = 1L;
        var disclosureWithoutPaymentId = 1L;
        var response = new DisclosureWithoutPaymentReceivedDto
        {
            Id = 1,
            FilingId = 1,
            Position = "Position",
            StanceOnCandidate = new StanceOnCandidateDto
            {
                Id = 1,
                CandidateId = 1,
                CandidateName = "Name",
                FirstName = "Name",
                LastName = "Name",
                MiddleName = "Name",
                District = "District",
                Jurisdiction = "Jurisdiction",
                OfficeSought = "OfficeSought"
            },
            StanceOnBallotMeasure = new StanceOnBallotMeasureDto
            {
                Id = 1,
                BallotMeasureId = 1,
                BallotNumberOrLetter = "Code",
                Jurisdiction = "Jurisdiction",
                BallotLetter = "BallotLetter",
                Title = "Title",
            }
        };
        _smoCampaignStatementSvcMock.GetDisclosureWithoutPaymentReceivedByIdAsync(Arg.Any<long>(), Arg.Any<long>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _service.GetCandidateOrMeasureWithoutPaymentReceivedAsync(filingId, disclosureWithoutPaymentId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.DisclosureWithoutPaymentId, Is.EqualTo(disclosureWithoutPaymentId));
        });
    }

    [Test]
    public async Task SaveCandidateOrMeasureWithoutPaymentReceivedAsync_WithoutId_ShouldCreateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            PertainsTo = CandidateOrMeasure.Candidate,
            CandidateId = 1,
            CandidateName = "Name",
            FirstName = "Name",
            LastName = "Name",
            MiddleName = "Name",
            District = "District",
            Jurisdiction = "Jurisdiction",
            OfficeSought = "OfficeSought"
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = null,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>(),
        };

        _smoCampaignStatementSvcMock.CreateDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<DisclosureWithoutPaymentReceivedDto>()).Returns(Task.FromResult(response));

        // Act
        await _service.SaveCandidateOrMeasureWithoutPaymentReceivedAsync(model, modelState);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .CreateDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<DisclosureWithoutPaymentReceivedDto>());
    }

    [Test]
    public async Task SaveCandidateOrMeasureWithoutPaymentReceivedAsync_WithId_ShouldUpdateSuccessfully()
    {
        // Arrange
        var filingId = 1L;
        var disclosureWithoutPaymentId = 1L;
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            Id = filingId,
            DisclosureWithoutPaymentId = disclosureWithoutPaymentId,
            PertainsTo = CandidateOrMeasure.BallotMeasure,
            BallotMeasureId = 1,
            BallotNumberOrLetter = "Code",
            Jurisdiction = "Jurisdiction",
            BallotLetter = "BallotLetter",
            BallotMeasureTitle = "Title",
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = null,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>(),
        };

        _smoCampaignStatementSvcMock.UpdateDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<DisclosureWithoutPaymentReceivedDto>()).Returns(Task.FromResult(response));

        // Act
        await _service.SaveCandidateOrMeasureWithoutPaymentReceivedAsync(model, modelState);

        // Assert
        _ = _smoCampaignStatementSvcMock
            .Received(1)
            .UpdateDisclosureWithoutPaymentReceivedAsync(Arg.Any<long>(), Arg.Any<long>(), Arg.Any<DisclosureWithoutPaymentReceivedDto>());
    }

    [Test]
    public async Task GetPaymentReceived04ViewModelAsync_WithoutIdAndOverrideData_ReturnsExpectedModel()
    {
        // Arrange
        var parameters = new CandidatesMeasuresNotListedParameters
        {
            Id = 1,
            Jurisdiction = "OverriddenJurisdiction",
            PertainsTo = "BallotMeasure",
            Position = "OverriddenPosition",
        };

        // Act
        var result = await _service.GetCandidatesMeasuresNotListedViewModel(parameters);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(parameters.Id));
            Assert.That(result.Position, Is.EqualTo("OverriddenPosition")); // overridden
            Assert.That(result.Jurisdiction, Is.EqualTo("OverriddenJurisdiction")); // overridden
        });
    }
    #endregion

    #region Transaction Entry - Payment Made
    [Test]
    public async Task GetPaymentMade03ViewModelAsync_WithTransactionId_ReturnsExpectedModel()
    {
        // Arrange
        var filingId = 1L;
        var contactId = 1L;
        var transactionId = 1L;
        var response = new PaymentMadeResponseDto
        {
            Id = transactionId,
            FilingId = filingId,
            ContactId = contactId,
            Name = "Name",
            Description = "Description"
        };
        var expenditureCodes = new List<ExpenditureCode>
        {
            new ()
            {
                Id = 1,
                Abbrev = "FEE",
                Description = "Election filing, ballot, and annual fees",
            }
        };
        _smoCampaignStatementSvcMock.GetSmoCampaignStatementTransactionAsync(Arg.Any<long>(), Arg.Any<long>(), TransactionType.PaymentMade.Name)
            .Returns(Task.FromResult<TransactionDetailResponseDto>(response));
        _referenceDataSvcMock.GetAllExpenditureCodesAsync()
            .Returns(Task.FromResult<IEnumerable<ExpenditureCode>>(expenditureCodes));

        // Act
        var result = await _service.GetPaymentMade03ViewModelAsync(filingId, contactId, transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.TransactionId, Is.EqualTo(transactionId));
            Assert.That(result.ContactId, Is.EqualTo(contactId));
        });
    }
    #endregion

    #region Transaction Entry - Person Receiving
    [Test]
    public async Task GetPersonReceivingFilerContactViewModelAsync_WithOfficerId_ReturnsExpectedModel()
    {
        // Arrange
        var filingId = 1L;
        var officerId = 1L;
        var registrationId = 1L;
        var filerId = 1L;
        var response = new SmoRegistrationContactDto
        {
            Id = 1,
            RegistrationId = registrationId,
            FirstName = "Test",
            LastName = "Test",
            MiddleName = "",
            Email = "<EMAIL>",
            PhoneNumber = new PhoneNumberDto
            {
                Id = 1,
                Number = "12314124",
                CountryCode = "+1"
            },
            Address = new AddressDto
            {
                Id = 1,
                City = "city",
                Country = "country",
                Purpose = "purpose",
                State = "state",
                Type = "type",
                Street = "street",
            }
        };
        _smoRegistrationSvcMock.GetSmoOfficer(Arg.Any<long>(), Arg.Any<long>())
            .Returns(Task.FromResult(response));

        // Act
        var result = await _service.GetPersonReceivingFilerContactViewModelAsync(filingId, null, null, officerId, registrationId, filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(filingId));
        });
    }

    [Test]
    public async Task GetPersonReceivingFilerContactViewModelAsync_NewTransaction_ReturnsEmptyModel()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 1L;

        // Act
        var result = await _service.GetPersonReceivingFilerContactViewModelAsync(filingId, null, null, null, null, filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(filingId));
        });
    }

    [Test]
    public async Task GetPersonReceivingFilerContactViewModelAsync_WithIndividualContact_MapsCorrectly()
    {
        // Arrange
        var contact = new Generated.IndividualContactResponseDto(
            addresses: new List<Generated.AddressDto> { new("123 St", "", 0, "City", "State", "Zip", "Type", "Mailing", "US") },
            emailAddresses: new List<Generated.EmailAddress> { new("<EMAIL>", 1L, 0L, "", "abc123") },
            employer: "Test Inc",
            id: 1L,
            filerId: 1L,
            occupation: "SWE",
            firstName: "John",
            middleName: "Q",
            lastName: "Public",
            phoneNumbers: new List<Generated.PhoneNumberDto> { new("+1", null, "987", 1, false, "1011231234", 1, false, "Home"), },
            typeId: 99L
        );

        _contactsApiMock.GetFilerContactById(Arg.Any<long>()).Returns(contact);

        // Act
        var result = await _service.GetPersonReceivingFilerContactViewModelAsync(123, 1, 1, null, null, 1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.ParticipantType, Is.EqualTo("Individual"));
            Assert.That(result.FirstName, Is.EqualTo("John"));
            Assert.That(result.Email, Is.EqualTo("<EMAIL>"));
        });
    }
    #endregion

    #region Validation
    [Test]
    public async Task ValidatePersonReceivingOfficerRequestAsync_InvalidRequest_ShouldAddErrorToModelState()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            OfficerId = 1L
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = false,
            ValidationErrors = new List<WorkFlowError>
            {
                new("OfficerId", "errorCode", "Validation", "message")
            }
        };
        _smoCampaignStatementSvcMock.ValidatePersonReceivingOfficerAsync(Arg.Any<long>(), Arg.Any<PersonReceiving1000ValidationRequest>())
            .Returns(Task.FromResult(response));
        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                Arg.Any<ModelStateDictionary>()))
            .Do(call =>
            {
                var modelState = call.ArgAt<ModelStateDictionary>(2);
                modelState.AddModelError("OfficerId", "OfficerId is required");
            });

        // Act
        await _service.ValidatePersonReceivingOfficerRequestAsync(model, modelState);

        // Assert
        Assert.That(modelState.ContainsKey("OfficerId"), Is.True);
    }

    [Test]
    public async Task ValidatePaymentReceivedAsync_InvalidRequest_ShouldAddErrorToModelState()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            PertainsTo = "",
            Jurisdiction = "",
            Position = "",
            TransactionAmount = 1m,
            TransactionDate = _dateNow,
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = false,
            ValidationErrors = new List<WorkFlowError>
            {
                new("PertainsTo", "errorCode", "Validation", "message")
            }
        };
        _smoCampaignStatementSvcMock.ValidatePaymentReceivedAsync(Arg.Any<long>(), Arg.Any<PaymentReceivedValidationRequestDto>())
            .Returns(Task.FromResult(response));
        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                Arg.Any<ModelStateDictionary>()))
            .Do(call =>
            {
                var modelState = call.ArgAt<ModelStateDictionary>(2);
                modelState.AddModelError("PertainsTo", "PertainsTo is required");
            });

        // Act
        await _service.ValidatePaymentReceivedRequestAsync(model, modelState);

        // Assert
        Assert.That(modelState.ContainsKey("PertainsTo"), Is.True);
    }

    [Test]
    public async Task ValidateCandidateOrMeasureNotListedP1Async_InvalidRequest_ShouldAddErrorToModelState()
    {
        // Arrange
        var model = new SmoCampaignStatementTransactionEntryViewModel
        {
            PertainsTo = "",
            Jurisdiction = "",
            Position = "",
        };
        var modelState = new ModelStateDictionary();
        var response = new TransactionResponseDto
        {
            Id = 1L,
            Valid = false,
            ValidationErrors = new List<WorkFlowError>
            {
                new("Jurisdiction", "errorCode", "Validation", "message")
            }
        };
        _smoCampaignStatementSvcMock.ValidateCandidateOrMeasureNotListedP1Async(Arg.Any<long>(), Arg.Any<CandidateOrMeasureNotListedValidationRequest>())
            .Returns(Task.FromResult(response));
        _decisionsValidationMapServiceMock
            .When(x => x.ApplyErrorsToModelState(
                Arg.Any<Dictionary<string, FieldProperty>>(),
                Arg.Any<List<WorkFlowError>>(),
                Arg.Any<ModelStateDictionary>()))
            .Do(call =>
            {
                var modelState = call.ArgAt<ModelStateDictionary>(2);
                modelState.AddModelError("Jurisdiction", "Jurisdiction is required");
            });

        // Act
        await _service.ValidateCandidateOrMeasureNotListedAsync(model, modelState);

        // Assert
        Assert.That(modelState.ContainsKey("Jurisdiction"), Is.True);
    }
    #endregion

    #region Amendment
    [Test]
    public async Task InitializeSmoCampaignStatementAmendment_ShouldReturnNewFilingId()
    {
        // Arrange
        var filingId = 1;
        var amendFilingIdMock = _smoCampaignStatementSvcMock
            .InitializeSmoCampaignStatementAmendmentAsync(filingId)
            .Returns(2);

        // Act
        var response = await _service.InitializeSmoCampaignStatementAmendment(filingId);

        // Assert
        Assert.That(response, Is.EqualTo(2));
    }
    #endregion

    #region Amendment Explanation
    [Test]
    public async Task GetAmendmentExplanation_ShouldReturnModel()
    {
        // Arrange
        var filingId = 1;
        var smoCampaignStatementOverviewMock = new FilingOverviewResponseDto()
        {
            Id = 123,
            FilingPeriodId = 1,
            FilingPeriod = new FilingPeriodResponseDto()
            {
                Id = 5,
                Name = "1st Pre-Election",
                Description = null,
                StartDate = _dateNow,
                EndDate = _dateNow
            },
            FilerId = 65,
            StatusId = 1,
            StatusName = "Draft",
            FilingSummaries =
            {
                new()
                {
                    Id = 1,
                    FilingId = 1,
                    FilingSummaryTypeId = 13,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PaymentReceivedSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 2,
                    FilingId = 1,
                    FilingSummaryTypeId = 14,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PaymentMadeSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 3,
                    FilingId = 1,
                    FilingSummaryTypeId = 15,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PaymentMadeByAgentOrIndependentContractorSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 4,
                    FilingId = 1,
                    FilingSummaryTypeId = 16,
                    FilingSummaryStatusId = 1,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "PersonReceiving1000OrMoreSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 5,
                    FilingId = 1,
                    FilingSummaryTypeId = 17,
                    FilingSummaryStatusId = 2,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "CandidateOrMeasureSupportedOrOpposedSummary",
                    FilingSummaryStatusName = "Not Started"
                },
                new()
                {
                    Id = 6,
                    FilingId = 1,
                    FilingSummaryTypeId = 19,
                    FilingSummaryStatusId = 2,
                    NoActivityToReport = null,
                    PeriodAmount = 0,
                    ToDateAmount = 0,
                    CreatedBy = 0,
                    ModifiedBy = 0,
                    FilingSummaryTypeName = "AmendmentExplanationSummary",
                    FilingSummaryStatusName = "Not Started"
                }
            },
            AmendmentExplanation = "Amendment Explanation"
        };
        _smoCampaignStatementSvcMock
            .GetSmoCampaignStatementOverviewAsync(filingId)
            .Returns(smoCampaignStatementOverviewMock);

        // Act
        var response = await _service.GetAmendmentExplanation(filingId);

        // Arrange
        Assert.That(response, Is.Not.Null);
    }

    [Test]
    public async Task UpdateAmendmentExplanation_ShouldApplyDecisionsResponse()
    {
        // Arrange
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel()
        {
            Id = 1,
            AmendmentExplanation = "Amendment Explanation"
        };
        var modelState = new ModelStateDictionary();
        var requestMock = new FilingSummaryAmendmentExplanationRequest()
        {
            AmendmentExplanation = "Amendment Explanation"
        };
        var filingSummaryResponseMock = new FilingSummaryResponseDto()
        {
            ValidationErrors = new List<WorkFlowError>
            {
                new("AmendmentExplanation", "ErrGlobal0001", "Validation", "AmendmentExplanation is required"),
            }
        };
        _smoCampaignStatementSvcMock
            .UpdateAmendmentExplanationAsync(Arg.Any<long>(), Arg.Any<FilingSummaryAmendmentExplanationRequest>())
            .Returns(filingSummaryResponseMock);

        // Act
        var response = await _service.UpdateAmendmentExplanation(model, modelState);

        // Arrange
        Assert.That(response, Is.Not.Null);
    }

    [Test]
    public async Task UpdateAmendmentExplanation_ShouldThrowError()
    {
        // Arrange
        var model = new AmendSmoCampaignStatementAmendmentExplanationViewModel()
        {
            AmendmentExplanation = "Amendment Explanation"
        };
        var modelState = new ModelStateDictionary();
        var requestMock = new FilingSummaryAmendmentExplanationRequest()
        {
            AmendmentExplanation = "Amendment Explanation"
        };
        var filingSummaryResponseMock = new FilingSummaryResponseDto()
        {
            ValidationErrors = new List<WorkFlowError>
            {
                new("AmendmentExplanation", "ErrGlobal0001", "Validation", "AmendmentExplanation is required"),
            }
        };
        _smoCampaignStatementSvcMock
            .UpdateAmendmentExplanationAsync(Arg.Any<long>(), Arg.Any<FilingSummaryAmendmentExplanationRequest>())
            .Returns(filingSummaryResponseMock);

        // Act
        var response = await _service.UpdateAmendmentExplanation(model, modelState);

        // Arrange
        Assert.That(response, Is.Null);
    }
    #endregion
}
