using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
public sealed class Report72HSvc(
    Report72HDependencies report72HDependencies,
    FilingSharedServicesDependencies filingSharedDependencies,
    IDateTimeSvc dateTimeSvc
    ) : IReport72HSvc
{
    public async Task<ValidateReport72HSubmissionResponseDto> AttestReport72H(long filingId, bool? diligenceStatementVerified)
    {
        var report72h = await report72HDependencies.FilingSvc.GetFilingById(filingId) ?? throw new KeyNotFoundException($"Filing not found. Id={filingId}");

        // Check if the report already attested
        // If the report is attested, return
        if (report72h.StatusId != FilingStatus.Accepted.Id)
        {
            // Get Filer User of the current user to send notifications.
            var userId = await filingSharedDependencies.AuthorizationSvc.GetInitiatingUserId();
            var filerUser = await filingSharedDependencies.FilerSvc.GetFilerUserByUserIdAsync(report72h.FilerId, userId.GetValueOrDefault())
                ?? throw new InvalidOperationException($"No filer user found with ID {report72h.FilerId}.");

            ActionsLobbiedSummaryResponse actionsLobbieds = await report72HDependencies.FilingSvc.GetActionsLobbiedSummaryForFiling(filingId);
            int billCount = (actionsLobbieds.SenateBillActions?.Count() ?? default) + (actionsLobbieds.AssemblyBillActions?.Count() ?? default);

            List<string> lobbyingAdvertisementSubjects = new();

            if (actionsLobbieds.AdministrativeActions?.Any() ?? false)
            {
                lobbyingAdvertisementSubjects.Add("Administrative actions");
            }
            if (billCount > 0)
            {
                lobbyingAdvertisementSubjects.Add("Legislation");
            }
            if (!string.IsNullOrEmpty(actionsLobbieds.OtherActionsLobbied))
            {
                lobbyingAdvertisementSubjects.Add("Other");
            }

            List<DecisionsActionsLobbiedAgency>? actionLobbiedAgencies = actionsLobbieds.AdministrativeActions?.Select(x => new DecisionsActionsLobbiedAgency
            {
                AgencyName = x.AgencyName,
                AdministrativeActionDescription = x.AdministrativeAction,
                OfficialPosition = x.OfficialPositionName,
                OfficialPositionDescription = x.OfficialPositionName
            }).ToList();

            var actionsLobbiedBills = actionsLobbieds.SenateBillActions?.Concat(actionsLobbieds.AssemblyBillActions!).Select(x => new DecisionsActionsLobbiedBill
            {
                OfficialPosition = x.OfficialPositionName,
                OfficialPositionDescription = x.OfficialPositionName
            }).ToList();

            CalAccess.Models.FilerDisclosure.Transactions.LobbyingAdvertisement? lobbyingAdvertisement = await report72HDependencies.TransactionSvc.GetLobbyingAdvertisementTransactionByFilingId(filingId);

            Decisions72HReportSubmission decisionsAttestationInput = new()
            {
                ActionsLobbied = lobbyingAdvertisementSubjects.Count > 0
                    ?
                    [
                        new()
                        {
                            AdministrativeActionCount = actionLobbiedAgencies?.Count,
                            BillCount = billCount,
                            LobbyingAdvertisementSubjects = lobbyingAdvertisementSubjects,
                            OtherActionDescription = actionsLobbieds.OtherActionsLobbied,
                        }
                    ]
                    : [],
                ActionsLobbiedAgencies = actionLobbiedAgencies ?? [],
                ActionsLobbiedBills = actionsLobbiedBills ?? [],
                LobbyingAdvertisement = lobbyingAdvertisement == null
                    ? []
                    : new List<DecisionsLobbyingAdvertisement>()
                    {
                        new()
                        {
                            Amount = (long?)(lobbyingAdvertisement.Amount),
                            Description = lobbyingAdvertisement.DistributionMethodDescription,
                            DistributionMethod = lobbyingAdvertisement.DistributionMethod!.Name,
                            PublicationDate = lobbyingAdvertisement.PublicationDate ?? DateTime.MinValue,
                        },
                    }
            };

            // Call Decisions to validate business rule - Run post-submission workflow validation
            var decisionResponse = await ValidateReport72HAttestation(filerUser, decisionsAttestationInput, report72h.Version > 0);

            if (decisionResponse is not null && decisionResponse.Result.Count > 0)
            {
                return new ValidateReport72HSubmissionResponseDto
                {
                    Id = report72h.Id,
                    Valid = false,
                    ValidationErrors = decisionResponse.Result,
                    StatusId = report72h.StatusId,
                    SubmittedDate = report72h.SubmittedDate
                };
            }

            report72h.StatusId = FilingStatus.Accepted.Id;
            report72h.SubmittedDate = dateTimeSvc.GetCurrentDateTime();
            report72h.ApprovedAt = dateTimeSvc.GetCurrentDateTime();
            report72h.DiligenceStatementVerified = diligenceStatementVerified;

            await report72HDependencies.FilingSvc.UpdateFiling(report72h);
        }

        return new ValidateReport72HSubmissionResponseDto
        {
            Id = report72h.Id,
            Valid = true,
            StatusId = report72h.StatusId,
            SubmittedDate = report72h.SubmittedDate
        };
    }

    #region Private Methods
    private async Task<Decisions72HReportAttestationResponse?> ValidateReport72HAttestation(FilerUserDto filerUser, Decisions72HReportSubmission input, bool isAmendment)
    {
        // Should call Decisions to validate business rule - Run post-submission workflow validation
        var workflow = isAmendment ? DecisionsWorkflow.FDLOBFiling72HourReportSubmitReportAmendment : DecisionsWorkflow.SubmitReport72HRuleSet;

        var decisionResponse = await filingSharedDependencies.DecisionsSvc.InitiateWorkflow<Decisions72HReportSubmission, Decisions72HReportAttestationResponse>(workflow, input, checkRequiredFields: true);

        // Only send notifications to filer if it's attestation flow
        if (decisionResponse?.Result?.Count == 0)
        {
            // Send Notifications
            IDictionary<string, string>? notificationData = new Dictionary<string, string>
            {
                ["Status"] = FilingStatus.Accepted.Name
            };
            await SendDecisionsNotifications(decisionResponse.Notifications, filerUser, notificationData);

        }
        return decisionResponse!;
    }

    private async Task SendDecisionsNotifications(List<NotificationTrigger>? notifications, FilerUserDto filer, IDictionary<string, string>? notificationData = null)
    {
        if (notifications != null && notifications.Count > 0)
        {
            foreach (NotificationTrigger notification in notifications)
            {
                if (notification.SendNotification && notification.NotificationTemplateId is not null)
                {
                    try
                    {
                        await filingSharedDependencies.NotificationSvc.SendUserNotification(
                            new SendUserNotificationRequest((long)notification.NotificationTemplateId, filer.UserId,
                                filer.FilerId, notification.DueDate, notificationData));
                    }
                    catch (Exception)
                    {
                        // Ignored - If notification fails.
                    }
                }
            }
        }
    }
    #endregion
}
