using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.WebApi.Filings;

namespace SOS.CalAccess.WebApi.Tests.Filings;

[TestFixture]
public class Form470SControllerTests
{
    private IForm470SSvc _form470SSvc;
    private Form470SController _controller;
    private IAuthorizationService _authorizationService;
    private IAuthorizationSvc _authorizationSvc;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _form470SSvc = Substitute.For<IForm470SSvc>();
        _authorizationService = Substitute.For<IAuthorizationService>();
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _controller = new Form470SController(_authorizationService, _form470SSvc);
    }

    [Test]
    public async Task CreateOfficeHolderCandidateSupplementFormFiling_ReturnsValidatedResponse()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            FilerId = 123,
            IsSubmission = true,
            ContributionsOrExpenditureOverOn = _dateNow
        };

        var expectedResponse = new ValidatedForm470SResponseDto
        (
            10, true, new List<WorkFlowError>(), 5
        );

        _form470SSvc
            .CreateOfficeHolderCandidateSupplementFormFiling(request)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.CreateOfficeHolderCandidateSupplementFormFiling(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.StatusId, Is.EqualTo(5));
        });
    }

    [Test]
    public async Task UpdateOfficeHolderCandidateSupplementFormFiling_ReturnsValidatedResponse()
    {
        // Arrange
        var request = new Form470SFilingRequest
        {
            FilerId = 123,
            IsSubmission = true,
            ContributionsOrExpenditureOverOn = _dateNow
        };

        var expectedResponse = new ValidatedForm470SResponseDto(10, true, new List<WorkFlowError>(), 5);

        _form470SSvc
            .UpdateOfficeHolderCandidateSupplementFormFiling(request)
            .Returns(expectedResponse);

        // Act
        var result = await _controller.UpdateOfficeHolderCandidateSupplementFormFiling(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(10));
            Assert.That(result.StatusId, Is.EqualTo(5));
        });
    }
    [Test]
    public async Task GetFilerIdOfLatestAcceptedCis_ReturnsId_WhenValidInput()
    {
        _form470SSvc.GetFilerIdOfLatestAcceptedCis().Returns(1);

        // Act
        var result = await _controller.GetFilerIdOfLatestAcceptedCis();

        // Assert
        Assert.That(result, Is.EqualTo(1));
    }
    [Test]
    public async Task GetLatestRegistrationIdByFilerId_ReturnsId_WhenValidInput()
    {
        _form470SSvc.GetLatestRegistrationIdByFilerId(1).Returns(1);

        // Act
        var result = await _controller.GetLatestRegistrationIdByFilerId(1);

        // Assert
        Assert.That(result, Is.EqualTo(1));
    }
}
