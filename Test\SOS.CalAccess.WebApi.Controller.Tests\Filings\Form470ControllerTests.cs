using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.WebApi.Filings;

namespace SOS.CalAccess.WebApi.Tests.Filings;
[TestFixture]
public class Form470ControllerTests
{
    private IForm470Svc _form470Svc;
    private Form470Controller _controller;
    private IAuthorizationService _authorizationService;
    private IAuthorizationSvc _authorizationSvc;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _form470Svc = Substitute.For<IForm470Svc>();
        _authorizationService = Substitute.For<IAuthorizationService>();
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _controller = new Form470Controller(_authorizationService, _form470Svc);
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    [Test]
    public async Task CreateOfficeHolderCandidateShortFormFiling_ReturnsFiling_WhenValidInput()
    {
        // Arrange
        long filerId = 1;
        long filingPeriodId = 2;

        var expectedFilingId = 1L;

        _form470Svc
            .CreateOfficeHolderCandidateShortFormFiling(filerId, filingPeriodId)
            .Returns(expectedFilingId);

        // Act
        var result = await _controller.CreateOfficeHolderCandidateShortFormFiling(filerId, filingPeriodId);

        // Assert
        Assert.That(result, Is.EqualTo(expectedFilingId));
    }

    [Test]
    public async Task GetForm470ById_ReturnsFiling_WhenFilingExists()
    {
        // Arrange
        long id = 123;
        var expectedFiling = new OfficeHolderCandidateShortForm
        {
            Id = id,
            FilerId = 1,
            FilingPeriodId = 2,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
            StatusId = FilingStatus.Pending.Id
        };

        _form470Svc.GetForm470ById(id).Returns(expectedFiling);

        // Act
        var result = await _controller.GetForm470ById(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.Id, Is.EqualTo(id));
            Assert.That(result?.FilingTypeId, Is.EqualTo(FilingType.OfficeHolderCandidateShortForm.Id));
        });
    }

    [Test]
    public async Task GetForm470ById_ReturnsNull_WhenFilingDoesNotExist()
    {
        // Arrange
        long id = 999;
        _form470Svc.GetForm470ById(id).Returns((OfficeHolderCandidateShortForm?)null);

        // Act
        var result = await _controller.GetForm470ById(id);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetForm470Overview_ReturnsResponse_WhenValidId()
    {
        // Arrange
        long id = 1;
        var expectedResponse = new Form470OverviewResponse
        {

            CandidateIntentionStatement470 = new CandidateIntentionStatement470 { RegistrationId = 10, FilerId = 100 },
            Form470Filing = new Services.Business.FilerDisclosure.Filings.Models.Form470Filing
            {
                Id = id,
                FilerId = 100,
                CurrentRegistrationId = 10,
                Filer = new Models.FilerRegistration.Filers.Filer { Id = 100 },
            }
        };

        _form470Svc.GetForm470Overview(id).Returns(expectedResponse);

        // Act
        var result = await _controller.GetForm470Overview(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.CandidateIntentionStatement470?.RegistrationId, Is.EqualTo(10));
            Assert.That(result?.Form470Filing?.Id, Is.EqualTo(id));
            Assert.That(result?.Form470Filing?.CurrentRegistrationId, Is.EqualTo(10));
            Assert.That(result?.Form470Filing?.Filer, !Is.Null);
        });
    }

    [Test]
    public async Task GetForm470Overview_ReturnsNull_WhenNotFound()
    {
        // Arrange
        long id = 999;
        _form470Svc.GetForm470Overview(id).Returns((Form470OverviewResponse?)null);

        // Act
        var result = await _controller.GetForm470Overview(id);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsStatement_WhenExists()
    {
        // Arrange
        long filerId = 101;
        var expectedStatement = new CandidateIntentionStatement470
        {
            RegistrationId = 1,
            FilerId = filerId,
            Name = "Sri S",
            EmailAddress = "<EMAIL>"
        };

        _form470Svc.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
                   .Returns(expectedStatement);

        // Act
        var result = await _controller.GetCandidateIntentionStatementWithElectionByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.FilerId, Is.EqualTo(filerId));
            Assert.That(result?.Name, Is.EqualTo("Sri S"));
            Assert.That(result?.EmailAddress, Is.EqualTo("<EMAIL>"));
        });
    }

    [Test]
    public async Task GetCandidateIntentionStatementWithElectionByFilerId_ReturnsNull_WhenNotFound()
    {
        // Arrange
        long filerId = 999;
        _form470Svc.GetCandidateIntentionStatementWithElectionByFilerId(filerId)
                   .Returns((CandidateIntentionStatement470?)null);

        // Act
        var result = await _controller.GetCandidateIntentionStatementWithElectionByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task CreateFilingRelatedFiler_ReturnsFilingRelatedFiler_WhenSuccessful()
    {
        // Arrange
        var filingId = 1L;
        var filerId = 2L;

        var expected = new FilingRelatedFiler
        {
            Id = 100,
            FilingId = filingId,
            FilerId = filerId,
            Active = true
        };

        _form470Svc.CreateFilingRelatedFiler(filingId, filerId).Returns(expected);

        // Act
        var result = await _controller.CreateFilingRelatedFiler(filingId, filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(100));
            Assert.That(result.FilingId, Is.EqualTo(filingId));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.Active, Is.True);
        });
    }

    [Test]
    public async Task CancelFilingRelatedFiler_CallsService_WithCorrectId()
    {
        // Arrange
        var id = 1L;

        // Act
        await _controller.CancelFilingRelatedFiler(id);

        // Assert
        await _form470Svc.Received(1).CancelFilingRelatedFiler(id);
    }

    #region FindPrimarilyFormedCommitteeByIdOrName

    [Test]
    public async Task FindPrimarilyFormedCommitteeByIdOrName_ReturnsCommittees()
    {
        // Arrange
        var query = "TestCommittee";
        var expectedResponse = new List<Form470SearchCommitteeResponseDto>
        {
            new(),
        };

        var form470Svc = Substitute.For<IForm470Svc>();
        _form470Svc.FindPrimarilyFormedCommitteeByIdOrName(query)
            .Returns(Task.FromResult<IEnumerable<Form470SearchCommitteeResponseDto>>(expectedResponse));


        // Act
        var result = await _controller.FindPrimarilyFormedCommitteeByIdOrName(query);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResponse));
    }
    #endregion

    #region

    [Test]
    public async Task GetFilingRelatedFiler_ReturnsForm470Committees_WhenDataExists()
    {
        // Arrange
        long filingId = 1;
        var expectedCommittees = GetSampleCommittees();

        _form470Svc.GetFilingRelatedFiler(filingId).Returns(expectedCommittees);

        // Act
        var result = await _controller.GetFilingRelatedFiler(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(expectedCommittees.Count));
        Assert.That(result[0].CommitteeName, Is.EqualTo("Committee A"));
    }

    [Test]
    public async Task GetFilingRelatedFiler_ReturnsEmptyList_WhenNoCommitteesExist()
    {
        // Arrange
        long filingId = 2;
        _form470Svc.GetFilingRelatedFiler(filingId).Returns(new List<Form470Committee>());

        // Act
        var result = await _controller.GetFilingRelatedFiler(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Empty);
    }

    private static List<Form470Committee> GetSampleCommittees()
    {
        return new List<Form470Committee>
        {
            new() {
                Id = 1,
                CommitteeName = "Committee A",
                CommitteeAddress = new AddressDto
                {
                  Street = "123 Main",
                        City = "City",
                        State = "CA",
                        Zip = "90001",
                        Purpose = "Candidate",
                },
                Treasurer = new User
                {
                    FirstName = "Sri",
                    LastName = "s",
                    EmailAddress = "<EMAIL>",
                    EntraOid = "TestOid"
                }
            }
        };
    }

    #endregion

    #region UpdateOfficeHolderCandidateShortFormFiling

    [Test]
    public async Task UpdateOfficeHolderCandidateShortFormFiling_ReturnsUpdatedFiling_WhenValidInput()
    {
        // Arrange
        var request = new Form470FilingRequest
        {
            FilingId = 100,
            FilingPeriodId = 300
        };

        // Act
        try
        {
            await _controller.UpdateOfficeHolderCandidateShortFormFiling(request);
        }
        catch (Exception e)
        {
            Assert.Fail(e.Message);
        }

        // Assert
        Assert.Pass();
    }

    #endregion

    #region Attestation

    [Test]
    public async Task AttestOfficeholderAndCandidateCampaignStatement_CallsService_WithCorrectId()
    {
        // Arrange
        var id = 1L;
        var request = new Form470AttestRequest
        {
            IsAgreedTerm = true
        };

        var expectedResponse = new ValidatedForm470ResponseDto(id, true, new List<WorkFlowError>(), FilingStatus.Accepted.Id)
        {
            Id = id,
            StatusId = FilingStatus.Accepted.Id,
            SubmittedDate = _dateNow,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };

        _ = _form470Svc.AttestOfficeholderAndCandidateCampaignStatement(id, request)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await _controller.AttestOfficeholderAndCandidateCampaignStatement(id, request);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResponse));
    }


    [Test]
    public async Task AttestOfficeholderAndNonCandidateCampaignStatement_CallsService_WithCorrectId()
    {
        // Arrange
        var id = 1L;

        var expectedResponse = new ValidatedForm470ResponseDto(id, true, new List<WorkFlowError>(), FilingStatus.Accepted.Id)
        {
            Id = id,
            StatusId = FilingStatus.Pending.Id,
            SubmittedDate = _dateNow,
            Valid = true,
            ValidationErrors = new List<WorkFlowError>()
        };
        var request = new Form470AttestRequest
        {
            CheckRequiredFieldsFlag = true
        };
        _ = _form470Svc.AttestOfficeholderAndNonCandidateCampaignStatement(id, request)
            .Returns(Task.FromResult(expectedResponse));

        // Act
        var result = await _controller.AttestOfficeholderAndNonCandidateCampaignStatement(id, request);

        // Assert
        Assert.That(result, Is.EqualTo(expectedResponse));
    }

    #endregion
}
