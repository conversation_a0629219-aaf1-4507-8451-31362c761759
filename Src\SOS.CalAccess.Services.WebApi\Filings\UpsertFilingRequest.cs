// <copyright file="UpsertFilingRequest.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Services.WebApi.Filings;

/// <summary>
/// Request object to upsert a filing in the system.
/// </summary>
public class UpsertFilingRequest
{
    /// <summary>
    /// Gets or sets the end date for the filing.
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Gets or sets the start date for the filing.
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Gets or sets the Amendment Explanation for the filing.
    /// </summary>
    public string? AmendmentExplanation { get; set; }

    /// <summary>
    /// Gets or sets the Legislative session ID for the filing.
    /// </summary>
    public long? LegislativeSessionId { get; set; }

    /// <summary>
    /// Converts this request object into a command object for creating a new filing.
    /// </summary>
    /// <param name="filerId">The id of the filer to create the filing for.</param>
    /// <param name="dateTimeSvc">Datetime service to get the urrent datetime stamp.</param>
    /// <returns>A <see cref="CreateFilingCommand"/> instance.</returns>
    public virtual CreateFilingCommand IntoCreateCommand(long filerId, IDateTimeSvc dateTimeSvc)
    {
        return new CreateFilingCommand
        {
            EndDate = EndDate ?? dateTimeSvc.GetCurrentDateTime(),
            FilerId = filerId,
            StartDate = StartDate ?? dateTimeSvc.GetCurrentDateTime(),
        };
    }

    /// <summary>
    /// Converts this request object into a command object for updating an existing filing.
    /// </summary>
    /// <param name="filingId">The id of the filing to update.</param>
    /// <returns>A <see cref="UpdateFilingCommand"/> instance.</returns>
    public virtual UpdateFilingCommand IntoUpdateCommand(long filingId)
    {
        var filingRequest = new UpdateFilingCommand()
        {
            Id = filingId
        };
        if (EndDate.HasValue)
        {
            filingRequest.EndDate = EndDate.Value;
        }
        if (StartDate.HasValue)
        {
            filingRequest.StartDate = StartDate.Value;
        }

        filingRequest.AmendmentExplanation = AmendmentExplanation;
        filingRequest.LegislativeSessionId = LegislativeSessionId;
        return filingRequest;
    }
}
