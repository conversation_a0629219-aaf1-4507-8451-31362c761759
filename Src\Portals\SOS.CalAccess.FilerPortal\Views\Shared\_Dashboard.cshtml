@model SOS.CalAccess.FilerPortal.Models.Disclosure.DisclosureSummaryViewModel
@using Microsoft.AspNetCore.Html
@using SOS.CalAccess.FilerPortal.Models.Disclosure
@using SOS.CalAccess.FilerPortal.Models.Localization
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using SOS.CalAccess.Models.FilerDisclosure.Filings
@using SOS.CalAccess.UI.Common.Enums
@using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
@inject IHtmlLocalizer<SharedResources> SharedLocalizer

@functions
{
    public string GetTooltip(string? type)
    {
        switch (type)
        {
            case var _ when type == FilingSummaryTypeModel.LobbyingAdvertisementSummary.Name:
                return SharedLocalizer["FilePortal.Disclosure.Dashboard.LobbyingAdvertisment.InstructionsTooltip"].Value;
            default:
                return "";
        }
    }
}

@{
    var controller = ViewContext.RouteData.Values["controller"]?.ToString();
    List<DisclosureReport> transactionEntries = Model.TransactionEntries;

    CancelConfirmModal cancelConfirmModalEntity;

    var isLobbyist = ViewBag.ReportType == FilingType.LobbyistReport.Name;
    var isEmployer = ViewBag.ReportType == FilingType.LobbyistEmployerReport.Name;

    cancelConfirmModalEntity = new CancelConfirmModal(
        "Cancel and Delete Draft Report",
        "Are you sure you want to cancel and delete your draft?",
        "Close with no changes",
        "Cancel and Delete Draft",
        $"/Filing/CancelReport/{Model.Id}");

    var actionLobbiedEntry = transactionEntries.FirstOrDefault(e => e.ViewName == FilingSummaryType.ActionsLobbiedSummary);
    string actionLobbiedStatusColor = HtmlHelpers.GetFilingSummaryStatusColorByStatusName(actionLobbiedEntry?.Status);

    var isDisabled = transactionEntries.Any(e => e.Status == FilingSummaryStatus.NotStarted.Name);

    var leftButtons = new List<ButtonConfig>
    {
        new ButtonConfig
        {
            Type = ButtonType.Custom,
            HtmlContent = new HtmlString(
                isDisabled
                    ? "<button class='btn btn-primary btn-sm me-2' disabled>" + SharedLocalizer["FilerPortal.Disclosure.Dashboard.Submit"].Value + "</button>"
                    : "<a href='"
                      + Url.Action("Verification", "Filing", new
                      {
                          filerId = Model.FilerId,
                          filingId = Model.Id,
                          reportType = ViewBag.ReportType
                      })
                      + "' class='btn btn-primary btn-sm me-2'>"
                      + SharedLocalizer["FilerPortal.Disclosure.Dashboard.Submit"].Value
                      + "</a>"
            )
        },
        new ButtonConfig
        {
            Type = ButtonType.Custom,
            HtmlContent = new HtmlString(
                isDisabled
                    ? "<button class='btn btn-outline-secondary btn-sm me-2' disabled>" + SharedLocalizer[ResourceConstants.SendForAttestation].Value + "</button>"
                    : "<a href='"
                      + Url.Action("SendForAttestation", "Filing", new
                      {
                          filerId = Model.FilerId,
                          filingId = Model.Id,
                          reportType = ViewBag.ReportType
                      })
                      + "' class='btn btn-secondary btn-sm me-2'>"
                      + SharedLocalizer[ResourceConstants.SendForAttestation].Value
                      + "</a>"
            )
        }
    };

    var buttonBarModel = new ButtonBarModel
    {
        LeftButtons = leftButtons,
        RightButtons =
        [
            new ButtonConfig
            {
                Type = ButtonType.Custom,
                CssClass = "btn btn-secondary btn-sm ms-auto",
                HtmlContent = new HtmlString(
                    "<a asp-controller='{controller}' asp-action='Index' asp-route-viewName='' class='btn btn-flat-primary btn-sm ms-auto' style='border: none;' " + "data-bs-toggle='modal' " + "data-bs-target='#cancelConfirmModal'>" + @SharedLocalizer["FilerPortal.Disclosure.Dashboard.Cancel"].Value + "</a>"
                )
            },
            new ButtonConfig
            {
                Type = ButtonType.Link,
                InnerTextKey = SharedLocalizer["Common.SaveAndClose"].Value,
                CssClass = "btn btn-secondary btn-sm ms-2",
                Url = Url.Action("SaveAndClose")
            }
        ]
    };

    var transactionItemsRendered = isEmployer
    ? transactionEntries
        .Where(t => t.FilingSummaryTypeId != FilingSummaryType.ActionsLobbiedSummary.Id &&
                    t.FilingSummaryTypeId != FilingSummaryType.AmendmentExplanation.Id)
        .ToList()
    : transactionEntries
        .Where(t => t.FilingSummaryTypeId != FilingSummaryType.AmendmentExplanation.Id)
        .ToList();
}

<h2 class="card-title mb-4">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.Title"]</h2>
<p class="card-subtitle mb-5">
    @(ViewBag.ReportType == FilingType.LobbyistReport.Name
        ? SharedLocalizer["FilerPortal.Disclosure.Dashboard.Body01"]
        : SharedLocalizer["FilerPortal.Disclosure.Dashboard.Body02"])
</p>

@if (isLobbyist || isEmployer)
{
    <div class="row mb-4">
        @Html.HiddenFor(m => m.FilingPeriodId)
        @Html.Label("filingPeriod", SharedLocalizer["FilerPortal.Disclosure.Dashboard.FilingPeriod"].Value, new { @class = "col-sm-2 fw-bold" })

        <div class="filing-period-selection d-flex">
            <select asp-for="@Model.FilingPeriodId" class="form-select w-50 flex-fill col-sm-2" id="filingPeriodDropdown" name="FilingPeriodId">
                @* <option value="0">Select a period</option> *@
                @foreach (var period in Model.FilingPeriods)
                {
                    <option value="@period.Id" disabled="@(period.HasFiling ? "disabled" : null)" selected="@(period.Id == Model.FilingPeriodId ? "selected" : null)">
                        @period.Name
                    </option>
                }
            </select>

            @* @Html.DropDownListFor(m => m.FilingPeriodId, new SelectList(Model.FilingPeriods, dataValueField: "Id", dataTextField: "Name", selectedValue: Model.FilingPeriodId), optionLabel: "", new { @class = "form-select" }) *@

            <div class="custom-filing-period flex-fill">
                <div id="customDatePickers" class="custom-period-dates" style="@(Model.FilingPeriodId != null && Model.FilingPeriodId != 0 ? "display: none" : "")">
                    @Html.DatePickerFor(SharedLocalizer, m => m.StartDate, "", format: "MM/dd/yyyy", isRequired: false, isReadOnly: false, cssClass: "custom-start-date")
                    @Html.DatePickerFor(SharedLocalizer, m => m.EndDate, "", format: "MM/dd/yyyy", isRequired: false, isReadOnly: false, cssClass: "custom-end-date")
                </div>

            </div>
        </div>
        @Html.SosValidationMessageFor(SharedLocalizer, m => m.FilingPeriodId)

    </div>
}

@if ((ViewBag.ReportType == FilingType.LobbyistReport.Name || ViewBag.ReportType == FilingType.LobbyistEmployerReport.Name || ViewBag.ReportType == FilingType.Report72h.Name || ViewBag.ReportType == FilingType.Report48h.Name) && controller == "AmendDisclosure")
{
    <partial name="_AmendmentExplanationSummary" model="@Model" />
}

<ul class="list-group-flush w-100">
    <li class="list-group-item">
        <div class="d-flex flex-row justify-content-between py-3">
            <div class="d-flex">
                <span style="display: inline-block; width: 4px; height: 65px; border-radius: 25px; background-color: #5E9967;"></span>
                <div class="ms-3 py-2">
                    <p class="fw-bold mb-0">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.GeneralInformation"]</p>
                </div>
            </div>
            <div class="d-flex flex-column">
                <div class="d-flex">
                    @Html.LinkButton(
                        localizer: SharedLocalizer,
                        textKey: "FilerPortal.Disclosure.Dashboard.View",
                        controller: controller,
                        action: "Index",
                        routeValues: new
                        {
                            viewName = DisclosureConstants.Controller.GeneralInfo,
                            filerId = Model.FilerId,
                            filingId = Model.Id,
                            reportType = ViewBag.ReportType
                        },
                        cssClass: "btn btn-outline-primary btn-sm ms-auto"
                    )
                </div>
            </div>
        </div>
    </li>

    @if (isEmployer)
    {
        <li class="list-group-item">
            <div class="d-flex flex-row justify-content-between py-3">
                <div class="d-flex">
                    <span style="display: inline-block; width: 4px; height: 65px; border-radius: 25px; background-color: @actionLobbiedStatusColor;"></span>
                    <div class="d-flex flex-column align-items-start mb-3 ms-3">
                        @if (!string.IsNullOrEmpty(actionLobbiedEntry?.Status))
                        {
                            <p class="mb-0">
                                <span style="color: @actionLobbiedStatusColor;">@actionLobbiedEntry.Status</span>
                            </p>
                        }
                        <p class="fw-bold mb-0">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.ActionLobbied"]</p>
                    </div>
                </div>
                <div class="d-flex flex-column align-items-end">
                    <div class="d-flex flex-column align-items-end">
                        @Html.LinkButton(
                            localizer: SharedLocalizer,
                            textKey: HtmlHelpers.GetFilingSummaryButtonTextByStatusName(actionLobbiedEntry?.Status, SharedLocalizer),
                            controller: controller,
                            action: "Index",
                            routeValues: new
                            {
                                viewName = actionLobbiedEntry!.ViewName,
                                filerId = Model.FilerId,
                                filingId = Model.Id,
                                reportType = ViewBag.ReportType,
                                filingSummaryId = actionLobbiedEntry?.Id
                            },
                            cssClass: "btn btn-outline-primary btn-sm ms-auto"
                        )
                    </div>
                    <div class="d-flex">
                        @if (actionLobbiedEntry?.Status == FilingSummaryStatus.NotStarted.Name)
                        {
                            @Html.LinkButton(
                                localizer: SharedLocalizer,
                                textKey: "FilerPortal.Disclosure.Dashboard.DontHaveAnything",
                                controller: controller,
                                action: "HandleUpdateStatusFilingSummary",
                                routeValues: new
                                {
                                    filerId = Model.FilerId,
                                    filingId = Model.Id,
                                    reportType = ViewBag.ReportType,
                                    filingSummaryId = actionLobbiedEntry?.Id,
                                    filingSummaryStatusName = FilingSummaryStatus.NothingToReport.Name
                                },
                                cssClass: "link-primary"
                            )
                        }
                    </div>
                </div>
            </div>
        </li>
    }
</ul>
@if (isEmployer || isLobbyist)
{
    <h3 class="fw-bold">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.FilingSummary01"]</h3>
    <ul class="list-group-flush w-100">
        <li class="list-group-item">
            <div class="d-flex flex-row justify-content-between py-3">
                <div class="d-flex">
                    <span style="display: inline-block; width: 4px; height: 65px; border-radius: 25px; background-color: #5E9967;"></span>
                    <div class="ms-3 py-2">
                        <p class="fw-bold mb-0">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.FilingSummary02"]</p>
                    </div>
                </div>
                <div class="d-flex flex-column">
                    <div class="d-flex">
                        @{
                            if (ViewBag.ReportType == FilingType.LobbyistReport.Name)
                            {
                                @Html.Button(
                                    localizer: SharedLocalizer,
                                    text: "FilerPortal.Disclosure.Dashboard.View",
                                    id: null,
                                    type: "button",
                                    onClick: "location.href='"
                                             + Url.Action(controller + "FilingSummary", "Lobbyist", new
                                             {
                                                 filerId = Model.FilerId,
                                                 filingId = Model.Id,
                                                 reportType = ViewBag.ReportType
                                             })
                                             + "'",
                                    ariaLabel: SharedLocalizer["FilerPortal.Disclosure.Dashboard.View"].Value,
                                    cssClass: "btn btn-outline-primary btn-sm ms-auto"
                                )
                            }
                            else
                            {
                                @Html.Button(
                                    localizer: SharedLocalizer,
                                    text: "FilerPortal.Disclosure.Dashboard.View",
                                    id: null,
                                    type: "button",
                                    onClick: "location.href='"
                                             + Url.Action(controller + "FilingSummary", "LobbyistEmployerCoalition", new
                                             {
                                                 filerId = Model.FilerId,
                                                 filingId = Model.Id,
                                                 reportType = ViewBag.ReportType
                                             })
                                             + "'",
                                    ariaLabel: SharedLocalizer["FilerPortal.Disclosure.Dashboard.View"].Value,
                                    cssClass: "btn btn-outline-primary btn-sm ms-auto"
                                )
                            }
                        }
                    </div>
                </div>
            </div>

        </li>
    </ul>
}
<h3 class="fw-bold">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.TransactionEntry"]</h3>
<ul class="list-group-flush w-100">
    @foreach (DisclosureReport entry in transactionItemsRendered)
    {
        var statusColor = HtmlHelpers.GetFilingSummaryStatusColorByStatusName(entry.Status);
        <li class="list-group-item">
            <div class="d-flex flex-row justify-content-between mt-3">
                <div class="d-flex flex-row align-items-start mb-3">
                    <span style="display: inline-block; width: 4px; height: 65px; border-radius: 25px; background-color: @statusColor;"></span>
                    <div class="d-flex flex-column align-items-start mb-3 ms-3">
                        @if (!string.IsNullOrEmpty(entry?.Status))
                        {
                            <p class="mb-0">
                                <span style="color: @statusColor;">@entry.Status</span>
                            </p>
                        }
                        @if (!string.IsNullOrEmpty(GetTooltip(entry?.ViewName)))
                        {
                            <div class="fw-bold mb-0 d-flex">
                                <span>@entry?.Name</span>
                                <div class="mt-1">
                                    <partial name="_TooltipButton" model="@GetTooltip(entry?.ViewName)" />
                                </div>
                            </div>
                        }
                        else
                        {
                            <p class="fw-bold mb-0">@entry?.Name</p>
                        }
                    </div>
                </div>
                <div class="d-flex flex-column align-items-end">
                    <div class="d-flex flex-column align-items-end">
                        @Html.Button(
                            localizer: SharedLocalizer,
                            text: HtmlHelpers.GetFilingSummaryButtonTextByStatusName(entry?.Status, SharedLocalizer),
                            id: null,
                            type: "button",
                            onClick: "location.href='"
                                     + Url.Action("Index", controller, new
                                     {
                                         viewName = entry?.ViewName,
                                         filerId = Model.FilerId,
                                         filingId = Model.Id,
                                         reportType = ViewBag.ReportType,
                                         filingSummaryId = entry?.Id
                                     })
                                     + "'",
                            ariaLabel: HtmlHelpers.GetFilingSummaryButtonTextByStatusName(entry?.Status, SharedLocalizer),
                            cssClass: "btn btn-outline-primary btn-sm ms-auto"
                        )
                    </div>
                    <div class="d-flex">
                        @if (entry?.Status == FilingSummaryStatus.NotStarted.Name)
                        {
                            @Html.LinkButton(
                                localizer: SharedLocalizer,
                                controller: controller,
                                action: "HandleUpdateStatusFilingSummary",
                                routeValues: new
                                {
                                    filerId = Model.FilerId,
                                    filingId = Model.Id,
                                    reportType = ViewBag.ReportType,
                                    filingSummaryId = (long?)entry.Id,
                                    filingSummaryStatusName = FilingSummaryStatus.NothingToReport.Name
                                },
                                textKey: "FilerPortal.Disclosure.Dashboard.DontHaveAnything",
                                cssClass: "link-primary"
                            )
                        }
                    </div>
                </div>
            </div>
        </li>
    }
</ul>

<p class="my-5">
    <a href="#" class="link-primary">@SharedLocalizer["FilerPortal.Disclosure.Dashboard.PreviewPdf"].Value</a>
</p>

<div class="my-4"></div>
<partial name="_ButtonBar" model="buttonBarModel"/>

<partial name="_CancelConfirmModal" for="@cancelConfirmModalEntity"/>

<script>
    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Get reference to the filing period dropdown
        const filingPeriodDropdown = document.getElementById('filingPeriodDropdown');
        const customStartDate = document.getElementById('StartDate');
        const customEndDate = document.getElementById('EndDate');

        // Debounced handler
        const debouncedUpdate = debounce(function () {
            updateFilingCustomPeriod(filingPeriodDropdown ? filingPeriodDropdown.value : "");
        }, 700);

        // If dropdown exists, add change event listener
        if (filingPeriodDropdown) {
            filingPeriodDropdown.addEventListener('change', function (e) {
                // Show date pickers only when no period is selected (Other type)
                if (this.value === "") {
                    customDatePickers.style.display = "flex";
                } else {
                    customDatePickers.style.display = "none";
                }
                // Trigger the update function when dropdown value changes
                updateFilingCustomPeriod(this.value);
            });

            // Listen for changes on custom start and end date pickers
            // Track whether input was through typing
            let isTyping = false;

            if (customStartDate) {
                customStartDate.addEventListener('blur', function() {
                    // When input loses focus, check if the value actually changed
                    updateFilingCustomPeriod(filingPeriodDropdown ? filingPeriodDropdown.value : "");
                });
            }

            // Same for end date
            if (customEndDate) {
                customEndDate.addEventListener('blur', function() {
                    updateFilingCustomPeriod(filingPeriodDropdown ? filingPeriodDropdown.value : "");
                });
            }
        }

        let startDateLabel = document.querySelector('label[for="StartDate"]');
        if (startDateLabel) {
            startDateLabel.style.display = "none";
        }
        let endDateLabel = document.querySelector('label[for="EndDate"]');
        if (endDateLabel) {
            endDateLabel.style.display = "none";
        }
    });

    function updateFilingCustomPeriod(dropdownValue) {
        // Get form values
        const filingId = @(Model.Id);
        const filerId = @(Model.FilerId);
        const filingPeriodId = document.getElementById('filingPeriodDropdown').value;
        const startDate = document.querySelector('input[name="StartDate"]').value;
        const endDate = document.querySelector('input[name="EndDate"]').value;

        console.log(startDate === "")
        console.log(endDate === "")
        console.log(!filingId)
        if (!filingPeriodId && (!startDate || !endDate)) {
            console.log("not")
            return;
        }

        fetch('@Url.Action("UpdateFilingCustomPeriod", controller)', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                Id: filingId,
                StartDate: startDate,
                EndDate: endDate,
                FilingPeriodId: parseInt(filingPeriodId) || null,
            })
        })
            .then(response => {
                return response.json();
            })
            .then(result => {
                if (result.success) {
                    showToast('@(SharedLocalizer["FilerPortal.Disclosure.Dashboard.UpdateFilingPeriodSuccessMessage"].Value)', 'e-toast-success', true, 'Right', 'Bottom');

                    console.log("success", result)
                } else {
                    // Display error message for FilingPeriodId if present
                    if (result.error) {
                        let errorObj = JSON.parse(result.error);

                        let validationSpan = document.querySelector('span[data-valmsg-for="FilingPeriodId"]');
                        if (validationSpan) {
                            validationSpan.classList.remove('field-validation-valid');
                            validationSpan.classList.add('field-validation-error');
                            validationSpan.classList.add('text-danger');
                            validationSpan.textContent = errorObj.Error;
                        }
                    } else {
                        let validationSpan = document.querySelector('span[data-valmsg-for="FilingPeriodId"]');
                        if (validationSpan) {
                            validationSpan.classList.remove('field-validation-error');
                            validationSpan.classList.remove('text-danger');
                            validationSpan.classList.add('field-validation-valid');
                            validationSpan.textContent = '';
                        }
                    }
                }
            })
    }
</script>

<style>
    .custom-period-dates {
        display: flex;
        gap: 1rem;
        margin-left: 1rem;
    }
    .custom-period-dates .e-input-group {
        padding: .375rem;
    }
</style>
