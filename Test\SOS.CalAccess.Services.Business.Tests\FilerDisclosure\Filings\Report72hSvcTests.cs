using NSubstitute;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Filings;
public class Report72hSvcTests
{
    private IDecisionsSvc _decisionsSvcMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private ITransactionSvc _transactionSvcMock;
    private IFilingSvc _filingSvcMock;
    private Report72HDependencies _dependenices;
    private FilingSharedServicesDependencies _sharedDependencies;
    private Report72HSvc _service;
    private IReferenceDataSvc _referenceDataSvcMock;
    private ILinkageSvc _linkageSvcMock;
    private IDateTimeSvc _dateTimeSvcMock;
    private DateTime _dateNow;

    [SetUp]
    public void Setup()
    {
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _filingSvcMock = Substitute.For<IFilingSvc>();
        _transactionSvcMock = Substitute.For<ITransactionSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _referenceDataSvcMock = Substitute.For<IReferenceDataSvc>();
        _linkageSvcMock = Substitute.For<ILinkageSvc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _dependenices = new Report72HDependencies
        (
            _filingSvcMock,
            _transactionSvcMock
        );

        _sharedDependencies = new FilingSharedServicesDependencies
        (
            _filerSvcMock,
            _decisionsSvcMock,
            _authorizationSvcMock,
            _userMaintenanceSvcMock,
            _notificationSvcMock,
            _referenceDataSvcMock,
            _linkageSvcMock,
            _dateTimeSvcMock
        );

        _service = new Report72HSvc(_dependenices, _sharedDependencies, _dateTimeSvcMock);
    }

    [Test]
    [TestCase(1, null)]
    [TestCase(3, "2025-01-01")]
    public async Task AttestReport72H_FilingIsValid_UpdatesFilingAndReturnsValidResponse(long filingStatus, DateTime? submittedDate)
    {
        // Arrange
        long filingId = 123;
        long filerId = 456;
        long userId = 789;

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = filingStatus,
            SubmittedDate = submittedDate
        };

        var filerUser = new FilerUserDto
        {
            FilerId = filerId,
            UserId = userId
        };

        var actionsSummary = new ActionsLobbiedSummaryResponse
        {
            FilingSummaryId = 1,
            FilingId = filingId,
            OtherActionsLobbied = "Other action",
            AdministrativeActions = new List<ActionsLobbiedResponseDto>
            {
                new() { Id = 1, AdministrativeAction = "Admin Action 1", OfficialPositionName = "Position test" },
            },
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>()
            {
                new() { Id = 2, BillId = 2, OfficialPositionName = "Position 2 test" },
            },
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
            {
                new() { Id = 3, BillId = 3, OfficialPositionName = "Position 3 test" },
            },
        };

        var lobbyingAd = new LobbyingAdvertisement
        {
            Amount = (Currency)1000,
            DistributionMethodDescription = "Social Media",
            DistributionMethod = AdvertisementDistributionMethod.SocialMedia,
            DistributionMethodId = AdvertisementDistributionMethod.SocialMedia.Id,
            PublicationDate = _dateNow
        };

        var decisionsResponse = new Decisions72HReportAttestationResponse()
        {
            Result = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>()
            {
                new (true, 1, _dateNow)
            }
        };

        _filingSvcMock.GetFilingById(filingId).Returns(filing);
        _authorizationSvcMock.GetInitiatingUserId().Returns(userId);
        _filerSvcMock.GetFilerUserByUserIdAsync(filerId, userId).Returns(filerUser);
        _filingSvcMock.GetActionsLobbiedSummaryForFiling(filingId).Returns(actionsSummary);
        _transactionSvcMock.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(lobbyingAd);
        _decisionsSvcMock
            .InitiateWorkflow<Decisions72HReportSubmission, Decisions72HReportAttestationResponse>(
                DecisionsWorkflow.SubmitReport72HRuleSet, Arg.Any<Decisions72HReportSubmission>(), true)
            .Returns(decisionsResponse);

        // Act
        var result = await _service.AttestReport72H(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });

        if (filingStatus == FilingStatus.Draft.Id)
        {
            await _filingSvcMock.Received(1).UpdateFiling(filing);
            await _notificationSvcMock.Received(1).Received(1).SendUserNotification(Arg.Is<SendUserNotificationRequest>(n =>
                n.NotificationTemplateId == 1 &&
                n.UserId == userId &&
                n.FilerId == filerId &&
                n.DueDate.HasValue
            ));

            await _decisionsSvcMock.Received(1).InitiateWorkflow<Decisions72HReportSubmission, Decisions72HReportAttestationResponse>(
                DecisionsWorkflow.SubmitReport72HRuleSet,
                Arg.Any<Decisions72HReportSubmission>(),
                true
            );
        }
    }

    [Test]
    public async Task AttestReport72H_FilingIsNotValid_UpdatesFilingAndReturnsResponse_WithValidationErrors()
    {
        // Arrange
        long filingId = 123;
        long filerId = 456;
        long userId = 789;

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            SubmittedDate = null
        };

        var filerUser = new FilerUserDto
        {
            FilerId = filerId,
            UserId = userId
        };

        var actionsSummary = new ActionsLobbiedSummaryResponse
        {
            FilingSummaryId = 1,
            FilingId = filingId,
            OtherActionsLobbied = "Other action",
            AdministrativeActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>(),
        };

        var decisionsResponse = new Decisions72HReportAttestationResponse(
            new List<WorkFlowError>()
        {
            new ("PublicationDate", "Err123", "Validation", "{{FieldName}} is required"),
        });

        _filingSvcMock.GetFilingById(filingId).Returns(filing);
        _authorizationSvcMock.GetInitiatingUserId().Returns(userId);
        _filerSvcMock.GetFilerUserByUserIdAsync(filerId, userId).Returns(filerUser);
        _filingSvcMock.GetActionsLobbiedSummaryForFiling(filingId).Returns(actionsSummary);
        _transactionSvcMock.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(Task.FromResult<LobbyingAdvertisement?>(null));
        _decisionsSvcMock
            .InitiateWorkflow<Decisions72HReportSubmission, Decisions72HReportAttestationResponse>(
                DecisionsWorkflow.SubmitReport72HRuleSet, Arg.Any<Decisions72HReportSubmission>(), true)
            .Returns(decisionsResponse);

        // Act
        var result = await _service.AttestReport72H(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
            Assert.That(result.SubmittedDate, Is.Null);
        });
    }

    [Test]
    public async Task AttestReport72H_AmendmentFiling_UsesAmendmentWorkflow()
    {
        // Arrange
        long filingId = 999;
        long filerId = 456;
        long userId = 789;

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            SubmittedDate = null,
            OriginalId = 111,
            Version = 1 // Indicates amendment
        };

        var filerUser = new FilerUserDto
        {
            FilerId = filerId,
            UserId = userId
        };

        var actionsSummary = new ActionsLobbiedSummaryResponse
        {
            FilingSummaryId = 1,
            FilingId = filingId,
            OtherActionsLobbied = "Other",
            AdministrativeActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>(),
        };

        var lobbyingAd = new LobbyingAdvertisement
        {
            Amount = (Currency)500,
            DistributionMethod = AdvertisementDistributionMethod.SocialMedia,
            DistributionMethodDescription = "Social",
            DistributionMethodId = AdvertisementDistributionMethod.SocialMedia.Id,
            PublicationDate = DateTime.Today
        };

        var decisionsResponse = new Decisions72HReportAttestationResponse()
        {
            Result = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>
        {
            new(true, 2, _dateNow)
        }
        };

        _filingSvcMock.GetFilingById(filingId).Returns(filing);
        _authorizationSvcMock.GetInitiatingUserId().Returns(userId);
        _filerSvcMock.GetFilerUserByUserIdAsync(filerId, userId).Returns(filerUser);
        _filingSvcMock.GetActionsLobbiedSummaryForFiling(filingId).Returns(actionsSummary);
        _transactionSvcMock.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(lobbyingAd);
        _decisionsSvcMock
            .InitiateWorkflow<Decisions72HReportSubmission, Decisions72HReportAttestationResponse>(
                DecisionsWorkflow.FDLOBFiling72HourReportSubmitReportAmendment,
                Arg.Any<Decisions72HReportSubmission>(),
                true)
            .Returns(decisionsResponse);

        // Act
        var result = await _service.AttestReport72H(filingId, true);

        // Assert
        Assert.That(result.Valid, Is.True);
        await _decisionsSvcMock.Received(1).InitiateWorkflow<Decisions72HReportSubmission, Decisions72HReportAttestationResponse>(
            DecisionsWorkflow.FDLOBFiling72HourReportSubmitReportAmendment,
            Arg.Any<Decisions72HReportSubmission>(),
            true
        );
    }

}
