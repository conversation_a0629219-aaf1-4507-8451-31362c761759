// <copyright file="TerminateFilerRegistrationTests.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filers;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Messaging;
using SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Filers;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Commands.Filers;

/// <summary>
/// Tests for the <see cref="TerminateFilerRegistration"/> command handler.
/// </summary>
[TestFixture]
[Parallelizable(ParallelScope.All)]
[TestOf(typeof(TerminateFilerRegistration))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class TerminateFilerRegistrationTests
{
    private readonly ServiceProvider _services = new ServiceCollection()
        .AddLogging()
        .BuildServiceProvider();

    private readonly IMessagingHub _messaging = Substitute.For<IMessagingHub>();
    private readonly IDateTimeSvc _dateTimeSvc = Substitute.For<IDateTimeSvc>();

    /// <summary>
    /// Asserts that our implementation of the <see cref="TerminateFilerRegistration" /> command
    /// terminates a registration that is in the "Approved" state.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task TerminateFilerRegistration_ReturnsSuccess_WhenFilerRegistrationIsTerminated()
    {
        var logger = _services.GetRequiredService<ILogger<TerminateFilerRegistration>>();

        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilersData();

        var command = new TerminateFilerRegistration(
            context, _messaging, _dateTimeSvc, logger);

        var success = await command.Execute(data.FilerId)
            as Success<Registration>;
        Assert.That(success, Is.Not.Null);
        Assert.That(success.Value, Has.Property(nameof(Registration.StatusId)).EqualTo(RegistrationStatus.Terminated.Id));
    }

    /// <summary>
    /// Asserts that our implementation of the <see cref="TerminateFilerRegistration" /> command
    /// terminates a registration that is in the "Approved" state and updates TerminationAt.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task TerminateFilerRegistration_ReturnsSuccessAndSetsTerminatedDate_WhenFilerRegistrationIsTerminated()
    {
        var logger = _services.GetRequiredService<ILogger<TerminateFilerRegistration>>();

        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilersData();

        var command = new TerminateFilerRegistration(
            context, _messaging, _dateTimeSvc, logger);

        var success = await command.Execute(data.FilerId)
            as Success<Registration>;

        Assert.That(success, Is.Not.Null);
    }

    /// <summary>
    /// Asserts that our implementation of the <see cref="TerminateFilerRegistration" /> command
    /// returns a <see cref="Failure{TResult}.NotFound"/> instance when the referenced
    /// target is not present in the database.
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task TerminateFilerRegistration_ReturnsNotFoundFailure_WhenNoFilerRegistrationExists()
    {
        var logger = _services.GetRequiredService<ILogger<TerminateFilerRegistration>>();

        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        await context.PrepareFilersData();

        var command = new TerminateFilerRegistration(
            context, _messaging, _dateTimeSvc, logger);

        var result = await command.Execute(default);

        Assert.That(result, Is.AssignableFrom(typeof(Failure<Registration>.NotFound)));
    }

    /// <summary>
    /// Asserts that our implementation of the <see cref="TerminateFilerRegistration" /> command
    /// returns a <see cref="Failure{TResult}.InvalidState"/> instance when the referenced
    /// target is in any state other than "Pending".
    /// </summary>
    /// <returns>A task that represents the asynchronous execution of the test.</returns>
    [Test]
    public async Task TerminateFilerRegistration_ReturnsInvalidStateFailure_WhenTheRegistrationIsNotApproved()
    {
        var logger = _services.GetRequiredService<ILogger<TerminateFilerRegistration>>();

        await using var factory = new DatabaseContextFactory();
        await using var context = await factory.CreateContext();

        var data = await context.PrepareFilersData(RegistrationStatus.Draft.Id);

        var command = new TerminateFilerRegistration(
            context, _messaging, _dateTimeSvc, logger);

        var result = await command.Execute(data.FilerId);

        Assert.That(result, Is.AssignableFrom(typeof(Failure<Registration>.InvalidState)));
    }
}
