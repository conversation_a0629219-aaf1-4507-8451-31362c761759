@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.ViewHelpers
@using SOS.CalAccess.UI.Common.Enums
@using SOS.CalAccess.UI.Common.Localization
@inject IHtmlLocalizer<SharedResources> Localizer
@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.AddLobbyingFirmsViewModel


@{
    var progressBar = LobbyistRegistrationProgressBarHelper.BuildProgressBar(
        version: 0,
        id: Model.Id,
        type: RegistrationConstants.RegistrationType.LobbyistEmployer,
        currentStep: 3,
        ViewData
    );

    var buttonConfig = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = Html.Raw(@"<a href='#'
                                       class='btn btn-outline-primary'
                                       data-bs-toggle='modal'
                                       data-bs-target='#cancelConfirmModal'>
                                       Cancel
                                    </a>"),
            },
            new ButtonConfig()
            {
                Type = ButtonType.Button,
                Action = FormAction.Submit,
                CssClass = "btn btn-primary me-2",
                InnerTextKey = CommonResourceConstants.Save
            },
        },
        RightButtons = new List<ButtonConfig>
        {
        }
    };

    var lobbyingFirmOptions = (Model?.LobbyingFirms ?? new Dictionary<long, string>())
        .Select(a => new { Id = a.Key, Text = a.Value })
        .ToList();

    var cancelModal = new CancelConfirmModal(
        Title: SharedLocalizer["Common.CancelConfirmationTitle"].Value,
        Body: SharedLocalizer["Common.CancelConfirmationBody"].Value,
        CloseButtonText: SharedLocalizer["Common.CancelConfirmationClose"].Value,
        SubmitButtonText: SharedLocalizer["Common.CancelConfirmationSubmit"].Value,
        ActionUrl: Url.Action("Step03LobbyingFirms", "LobbyistEmployerRegistration", new { id = Model!.Id }) ?? "",
        Method: "GET"
    );
}
@Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationTitle2)
<h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationTitle]</h3>
<partial name="_LayoutProgressbar" model="progressBar" />

@using (Html.BeginForm("Step03AddLobbyingFirms", "LobbyistEmployerRegistration", FormMethod.Post))
{
    @Html.HiddenFor(m => m.Id)
    <div class="d-flex flex-column gap-1 main-form">
        <h3 class="mb-3"> @SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmDetailsTitle]</h3>

        @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmDetailsDescription)

        <div class="mb-3" id="Multiselect_ResponsibleOfficers">
            <p class="field-label">
                @Localizer[ResourceConstants.LobbyistEmployerRegistrationStep03LobbyingFirmDetailsSearch].Value
            </p>
            @(Html.EJS().MultiSelect("SelectedLobbyingFirms")
                .Placeholder(@Localizer[ResourceConstants.LobbyistEmployerRegistrationLobbyingFirms].Value)
                .DataSource(lobbyingFirmOptions)
                .Fields(f => f.Value("Id").Text("Text"))
                .Mode(Syncfusion.EJ2.DropDowns.VisualMode.Box)
                .AllowFiltering(true)
                .Value(Model.SelectedLobbyingFirms)
                .AllowCustomValue(false)
                .CssClass("w-full multi-select rounded custom-border")
                .Render()
                )
            @Html.SosValidationMessageFor(Localizer, m => m.SelectedLobbyingFirms)
        </div>

        <div class="main-button">
            <partial name="_ButtonBar" model="buttonConfig" />
        </div>
    </div>

}
<partial name="_CancelConfirmModal" model="cancelModal" />
<style>
    .main-form {
        width: 90%;
        margin: auto;
        margin-top: 40px;
        padding: 40px;
    }

    .field-label {
        margin-bottom: 5px;
    }

    .main-button {
        margin-top: 20px;
    }
</style>
