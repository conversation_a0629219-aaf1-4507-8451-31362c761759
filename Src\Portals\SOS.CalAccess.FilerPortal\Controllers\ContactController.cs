// <copyright file="ContactController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Refit;
using SOS.CalAccess.FilerPortal.Alerts;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Extensions;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Contacts;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;
using FilerContactType = SOS.CalAccess.Models.FilerDisclosure.Contacts.FilerContactType;
using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
using TransactionReportablePerson = SOS.CalAccess.Models.FilerDisclosure.Contacts.TransactionReportablePerson;

namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Controller for handling contact-related actions.
/// </summary>
[Route("[controller]")]
public class ContactController : Controller
{
    /// <summary>
    /// The name of the view for creating or editing a contact.
    /// </summary>
    public const string ContactFormView = "ContactForm";

    /// <summary>
    /// The name of the view for viewing a contact.
    /// </summary>
    public const string ContactDetailsView = "ContactDetailsView";

    /// <summary>
    /// The name of the view for creating or editing a reportable person.
    /// </summary>
    public const string ReportablePersonFormView = "ReportablePersonForm";

    /// <summary>
    /// Reference for the transaction controller.
    /// </summary>
    public const string TransactionController = "Transaction";

    /// <summary>
    /// Reference for the creating a new activity expense - transaction.
    /// </summary>
    public const string NewActivityExpense = "NewActivityExpense";

    /// <summary>
    /// Reference for the creating a new activity expense - transaction.
    /// </summary>
    public const string EditActivityExpense = "EditActivityExpense";

    /// <summary>
    /// Reference for the activity expense temp data constant
    /// </summary>
    public const string ActivityExpenseData = "ActivityExpenseData";

    /// <summary>
    ///  The name of the view for selecting a lobbying firm.
    /// </summary>
    public const string SelectLobbyingFirmView = "SelectLobbyingFirm";

    /// <summary>
    ///  The name of the view for creating a lobbying firm.
    /// </summary>
    public const string LobbyingFirmView = "LobbyingFirmForm";

    /// <summary>
    ///  Previous form action
    /// </summary>
    private const string FormActionPrevious = "Previous";

    /// <summary>
    ///  Cancel confirm form action
    /// </summary>
    private const string FormActionCancelConfirm = "SelectLobbyingFirm";

    /// <summary>
    ///  Continue form action
    /// </summary>
    private const string FormActionContinue = "Continue";

    private readonly IToastService _toastService;

    private readonly IStringLocalizer<SharedResources> _localizer;

    private readonly IContactsApi _contactsApi;

    public ContactController(IToastService toastService, IStringLocalizer<SharedResources> localizer, IContactsApi contactsApi)
    {
        _toastService = toastService;
        _localizer = localizer;
        _contactsApi = contactsApi;
    }

    #region Main Contact Actions

    /// <summary>
    /// Renders the view for creating a new contact. The type of contact to create is determined by the type parameter.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer to create the contact for.</param>
    /// <param name="type">The type of contact to create.</param>
    /// <param name="returnUrl">The URL to return to if creation cancelled or completed.</param>
    /// <returns>The CreateContact view.</returns>
    [HttpGet]
    [Route("Create")]
    public IActionResult Create(
        [Required, FromQuery] long? filerId,
        [FromQuery] long? filingId,
        [FromQuery] long? typeId = null,
        [FromQuery] string? returnUrl = null,
        [FromQuery] string? context = "")
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        ViewBag.IsEditing = false;
        return View(ContactFormView, new GenericContactViewModel { FilerId = filerId, Id = filingId, TypeId = typeId ?? FilerContactType.Individual.Id, ReturnUrl = returnUrl, Context = context });
    }

    /// <summary>
    /// Handles the creation of a new contact. The method is smart enough to determine the type of contact to create
    /// based on the instance of the <see cref="ContactViewModel"/> provided.
    /// </summary>
    /// <param name="api">The API client for interacting with the contacts API.</param>
    /// <param name="model">The model containing the contact information.</param>
    /// <param name="returnUrl">The URL to return to after the contact is created.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The Activity Expense payee selection screen if the model is invalid.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    [Route("Create")]
    public async Task<IActionResult> Create(
        GenericContactViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            _toastService.Error(_localizer["FilerPortal.Contact.ContactForm.CreateErrorMessage"]);
            ViewBag.IsEditing = false;
            return View(ContactFormView, model);
        }

        ContactViewModel typedModel = model.ToTypedModel();
        UpsertPayeeRequest request = typedModel.ToPayeeRequest();

        try
        {
            var createdPayee = await _contactsApi.CreatePayee(typedModel.FilerId!.Value, request, cancellationToken);
            _toastService.Success(_localizer["FilerPortal.Contact.ContactForm.CreateSuccessMessage"]);

            if (model.Context == "OtherPayment")
            {
                return RedirectToAction("NewOtherPayment", TransactionController, new { filerId = model.FilerId, filingId = model.FilingId, returnUrl = model.ReturnUrl, contactId = createdPayee.Id, reportType = model.ReportType });
            }
            else
            {
                return RedirectToAction(NewActivityExpense, TransactionController, new { filerId = model.FilerId, filingId = model.FilingId, returnUrl = model.ReturnUrl, contactId = createdPayee.Id, reportType = model.ReportType });
            }
        }
        catch (ApiException err)
        {
            if (err.StatusCode == (HttpStatusCode)422)
            {
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content!);
                foreach (var error in workflowErrors!)
                {
                    SyncDecisionsValidationsWithViewModel([error], model);
                }
            }
            ViewBag.IsEditing = false;
            return View(ContactFormView, model);
        }
    }

    /// <summary>
    /// Renders the view for viewing details of an existing contact.
    /// </summary>
    /// <param name="id">The unique identifier of the contact to edit.</param>
    /// <param name="api">The API client for interacting with the contacts API.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The ViewContact view.</returns>
    public async Task<IActionResult> View(
        long id,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var response = await _contactsApi.GetContact(id, cancellationToken);

        return response switch
        {
            CommitteeContactResponse committeeResponse => View(ContactDetailsView, new CommitteeContactViewModel(committeeResponse) { Readonly = true, }),
            IndividualContactResponse individualResponse => View(ContactDetailsView, new IndividualContactViewModel(individualResponse) { Readonly = true, }),
            OrganizationContactResponse organizationResponse => View(ContactDetailsView, new OrganizationContactViewModel(organizationResponse) { Readonly = true, }),
            _ => NotFound(),
        };
    }

    /// <summary>
    /// Renders the view for editing an existing contact.
    /// </summary>
    /// <param name="id">The unique identifier of the contact to edit.</param>
    /// <param name="api">The API client for interacting with the contacts API.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The EditContact view.</returns>
    [HttpGet]
    [Route("Edit")]
    public async Task<IActionResult> Edit(
        [FromQuery] string contactId,
        [FromQuery] string? returnUrl = null,
        [FromQuery] string? context = "",
        [FromQuery] long? transactionId = null,
        [FromQuery] string? reportType = null,
        [FromQuery] long? filingId = null,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var response = await _contactsApi.GetContact(long.Parse(contactId, CultureInfo.InvariantCulture), cancellationToken);
        ViewBag.IsEditing = true;

        return response switch
        {
            CommitteeContactResponse committeeResponse => View(ContactFormView, new CommitteeContactViewModel(committeeResponse)),
            IndividualContactResponse individualResponse => View(ContactFormView, new GenericContactViewModel(individualResponse, context, returnUrl, filingId, reportType, transactionId)),
            OrganizationContactResponse organizationResponse => View(ContactFormView, new GenericContactViewModel(organizationResponse, context, returnUrl, filingId, reportType, transactionId)),
            _ => NotFound(),
        };
    }

    /// <summary>
    /// Handles the editing of an existing contact.
    /// </summary>
    /// <param name="contactId">The unique identifier of the contact to edit.</param>
    /// <param name="api">The API client for interacting with the contacts API.</param>
    /// <param name="model">The model containing the contact information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The NewActivityExpense transaction screen, NotFound if invalid, or the same Contact form if Decisions catches anything.</returns>
    [HttpPost]
    [ValidateAntiForgeryToken]
    [Route("Edit")]
    public async Task<IActionResult> Edit(
        GenericContactViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var emailList = new List<EmailAddressDto>();
        var phoneNumberList = new List<PhoneNumberDto>();
        var addressList = new List<AddressDto>
        {
            new(
                model.City ?? string.Empty,
                model.Country ?? string.Empty,
                (long)model.AddressId!,
                string.Empty,
                model.State ?? string.Empty,
                model.Street ?? string.Empty,
                model.Street2 ?? string.Empty,
                string.Empty,
                model.ZipCode ?? string.Empty)
        };

        UpsertFilerContactRequest upsertContactRequest = model.TypeId switch
        {
            var type when type == FilerContactType.Individual.Id =>
                new UpsertIndividualFilerContactRequest(
                    addressList,
                    emailList,
                    string.Empty,
                    filerContactForm: "UpsertPayee",
                    model.FirstName ?? string.Empty,
                    model.LastName ?? string.Empty,
                    model.MiddleName ?? string.Empty,
                    string.Empty,
                    phoneNumberList),
            var type when type == FilerContactType.Organization.Id =>
                new UpsertOrganizationFilerContactRequest(
                    addressList,
                    emailList,
                    filerContactForm: "UpsertPayee",
                    model.OrganizationName ?? string.Empty,
                    phoneNumberList),
            _ => throw new ArgumentException("The contact TypeId must be of Individual or Organization.")
        };

        try
        {
            await HandleUpdateFilerContact(model, upsertContactRequest, cancellationToken);

            // Validation check if errors occur from changing contact types
            if (model.Messages.Validations.Count > 0)
            {
                ViewBag.IsEditing = true;
                return View(ContactFormView, model);
            }

            if (model.Context == "OtherPayment")
            {
                return RedirectToAction("NewOtherPayment", TransactionController, new { filerId = model.FilerId, filingId = model.FilingId, returnUrl = model.ReturnUrl, contactId = model.Id, reportType = model.ReportType, transactionId = model.TransactionId });
            }

            if (model.Context?.StartsWith("EditActivityExpense/", StringComparison.Ordinal) == true)
            {
                var idFromContext = model.Context.Split('/').Last();
                return RedirectToAction("EditActivityExpense", TransactionController, new { id = idFromContext, filerId = model.FilerId, filingId = model.FilingId, contactId = model.Id, reportType = model.ReportType });
            }
            else
            {
                return RedirectToAction(NewActivityExpense, TransactionController, new { filerId = model.FilerId, filingId = model.FilingId, returnUrl = model.ReturnUrl, contactId = model.Id, reportType = model.ReportType });
            }
        }
        catch (ApiException err)
        {
            if (err.StatusCode == (HttpStatusCode)422)
            {
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content!);
                Console.WriteLine();
                Console.WriteLine(workflowErrors);
                Console.WriteLine();
                foreach (var error in workflowErrors!)
                {
                    SyncDecisionsValidationsWithViewModel([error], model);
                }
            }
            ViewBag.IsEditing = true;
            return View(ContactFormView, model);
        }
    }

    #endregion

    #region Reportable Person (Activity Expense)

    /// <summary>
    /// Renders the view for creating a new reportable person.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer to create the contact for.</param>
    /// <param name="type">The type of contact to create.</param>
    /// <param name="returnUrl">The URL to return to if creation cancelled or completed.</param>
    /// <returns>The CreateContact view.</returns>
    [HttpGet]
    [Route("CreateReportablePerson")]
    public async Task<IActionResult> CreateReportablePerson(
        [FromQuery] string? name,
        [FromQuery] string? officialPosition,
        [FromQuery] string? officialPositionDescription,
        [FromQuery] string? agency,
        [FromQuery] string? agencyDescription,
        [FromServices] IReferenceDataApi referenceDataApi)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        // Grab reference data from activity expense session
        var savedSerializedData = HttpContext.Session.GetString(ActivityExpenseData);
        var activityExpenseModel = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(savedSerializedData!);
        var reportablePersonViewModel = new ReportablePersonViewModel { FilerId = activityExpenseModel!.FilerId, ReturnUrl = activityExpenseModel.ReturnUrl };
        await PopulateReferenceData(reportablePersonViewModel, referenceDataApi);
        reportablePersonViewModel.Name = name;
        reportablePersonViewModel.OfficialPosition = officialPosition;
        reportablePersonViewModel.OfficialPositionDescription = officialPositionDescription;
        reportablePersonViewModel.Agency = agency;
        reportablePersonViewModel.AgencyDescription = agencyDescription;
        ViewBag.IsEditingReportablePerson = false;
        return View(ReportablePersonFormView, reportablePersonViewModel);
    }

    /// <summary>
    /// Handles the validation of a new activity expense reportable person.
    /// </summary>
    /// <returns>The view for completing the new activity expense submission.</returns>
    [HttpPost]
    [Route("CreateReportablePerson")]
    public async Task<IActionResult> CreateReportablePerson(
        [FromServices] IActivityExpenseApi activityExpenseApi,
        [FromServices] IReferenceDataApi referenceDataApi,
        ReportablePersonViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            _toastService.Error("There was an issue creating the reportable person. Please try again.");
            ViewBag.IsEditingReportablePerson = false;
            return View(ReportablePersonFormView, model);
        }

        double reqAmount = (double?)model.Amount <= 0 ? -1 : (double?)model.Amount ?? -1;

        // Format the reportable person data for validation
        List<ValidateReportablePersonDs> reportablePersons = new();
        ValidateReportablePersonDs personDs = new(
            model.Agency ?? string.Empty,
            model.AgencyDescription ?? string.Empty,
            reqAmount,
            model.Name ?? string.Empty,
            model.OfficialPosition ?? string.Empty,
            model.OfficialPositionDescription ?? string.Empty
        );
        reportablePersons.Add(personDs);

        try
        {
            await activityExpenseApi.ValidateReportablePersons((long)model.FilerId!, reportablePersons, cancellationToken);
            var serializedModel = HttpContext.Session.GetString(ActivityExpenseData);
            var activityExpenseModel = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(serializedModel ?? "") ?? new ActivityExpenseViewModel();

            // Format the reportable person data for activity expense
            TransactionReportablePerson person = new()
            {
                Agency = model.Agency!,
                AgencyDescription = model.AgencyDescription ?? string.Empty,
                Amount = (Currency)model.Amount!.Value,
                Name = model.Name!,
                OfficialPosition = model.OfficialPosition!,
                OfficialPositionDescription = model.OfficialPositionDescription ?? string.Empty
            };

            // Save the new reportable person to the activity expense session
            activityExpenseModel.TransactionReportablePeople ??= new List<TransactionReportablePerson>();
            person.Id = activityExpenseModel.TransactionReportablePeople.Select(p => p?.Id ?? 0).DefaultIfEmpty(0).Max() + 1;
            activityExpenseModel.TransactionReportablePeople.Add(person);

            HttpContext.Session.SetString(ActivityExpenseData, JsonConvert.SerializeObject(activityExpenseModel));
            _toastService.Success("Reportable person was successfully added!");
            return RedirectToAction(NewActivityExpense, TransactionController, activityExpenseModel);
        }
        catch (ApiException err)
        {
            if (err.StatusCode == (HttpStatusCode)422)
            {
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content!);
                foreach (var error in workflowErrors!)
                {
                    SyncDecisionsValidationsWithViewModel([error], model);
                }
            }
            await PopulateReferenceData(model, referenceDataApi);
            ViewBag.IsEditingReportablePerson = false;
            return View(ReportablePersonFormView, model);
        }
    }

    /// <summary>
    /// Renders the view for editing a reportable person.
    /// </summary>
    /// <param name="filerId">The unique identifier of the filer to create the contact for.</param>
    /// <param name="type">The type of contact to create.</param>
    /// <param name="returnUrl">The URL to return to if creation cancelled or completed.</param>
    /// <returns>The CreateContact view.</returns>
    [HttpGet]
    [Route("EditReportablePerson")]
    public async Task<IActionResult> EditReportablePerson(
        [FromQuery] string? name,
        [FromQuery] string? officialPosition,
        [FromQuery] string? officialPositionDescription,
        [FromQuery] string? agency,
        [FromQuery] string? agencyDescription,
        [FromQuery] long? id,
        [FromQuery] decimal? amount,
        [FromServices] IReferenceDataApi referenceDataApi)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        // Grab reference data from activity expense session
        var savedSerializedData = HttpContext.Session.GetString(ActivityExpenseData);
        var activityExpenseModel = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(savedSerializedData!);
        var reportablePersonViewModel = new ReportablePersonViewModel
        {
            FilerId = activityExpenseModel!.FilerId,
            ReturnUrl = activityExpenseModel.ReturnUrl,
            Id = id,
            Name = name,
            OfficialPosition = officialPosition,
            OfficialPositionDescription = officialPositionDescription,
            Agency = agency,
            AgencyDescription = agencyDescription,
            Amount = amount
        };
        await PopulateReferenceData(reportablePersonViewModel, referenceDataApi);
        ViewBag.IsEditingReportablePerson = true;
        return View(ReportablePersonFormView, reportablePersonViewModel);
    }

    /// <summary>
    /// Handles the validation of editing an activity expense reportable person.
    /// </summary>
    /// <returns>The view for completing the new activity expense submission.</returns>
    [HttpPost]
    [Route("EditReportablePerson")]
    public async Task<IActionResult> EditReportablePerson(
        [FromServices] IActivityExpenseApi activityExpenseApi,
        [FromServices] IReferenceDataApi referenceDataApi,
        ReportablePersonViewModel model,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            _toastService.Error("There was an issue creating the reportable person. Please try again.");
            ViewBag.IsEditingReportablePerson = true;
            return View(ReportablePersonFormView, model);
        }

        double reqAmount = (double?)model.Amount <= 0 ? -1 : (double?)model.Amount ?? -1;

        // Format the reportable person data for validation
        List<ValidateReportablePersonDs> reportablePersons = new();
        ValidateReportablePersonDs personDs = new(
            model.Agency ?? string.Empty,
            model.AgencyDescription ?? string.Empty,
            reqAmount,
            model.Name ?? string.Empty,
            model.OfficialPosition ?? string.Empty,
            model.OfficialPositionDescription ?? string.Empty
        );
        reportablePersons.Add(personDs);

        try
        {
            await activityExpenseApi.ValidateReportablePersons((long)model.FilerId!, reportablePersons, cancellationToken);
            var serializedModel = HttpContext.Session.GetString(ActivityExpenseData);
            var activityExpenseModel = JsonConvert.DeserializeObject<ActivityExpenseViewModel>(serializedModel ?? "") ?? new ActivityExpenseViewModel();

            // Update the reportable person for ID in activity expense model
            activityExpenseModel.TransactionReportablePeople ??= new List<TransactionReportablePerson>();
            var existingPerson = activityExpenseModel.TransactionReportablePeople.FirstOrDefault(p => p.Id == model.Id);
            if (existingPerson != null)
            {
                // Update existing person
                existingPerson.Agency = model.Agency!;
                existingPerson.AgencyDescription = model.AgencyDescription ?? string.Empty;
                existingPerson.Amount = (Currency)model.Amount!.Value;
                existingPerson.Name = model.Name!;
                existingPerson.OfficialPosition = model.OfficialPosition!;
                existingPerson.OfficialPositionDescription = model.OfficialPositionDescription ?? string.Empty;
            }


            HttpContext.Session.SetString(ActivityExpenseData, JsonConvert.SerializeObject(activityExpenseModel));
            _toastService.Success("Reportable person was successfully updated!");
            return RedirectToAction(EditActivityExpense, TransactionController, activityExpenseModel);
        }
        catch (ApiException err)
        {
            if (err.StatusCode == (HttpStatusCode)422)
            {
                var workflowErrors = JsonConvert.DeserializeObject<List<WorkFlowError>>(err.Content!);
                foreach (var error in workflowErrors!)
                {
                    SyncDecisionsValidationsWithViewModel([error], model);
                }
            }
            await PopulateReferenceData(model, referenceDataApi);
            ViewBag.IsEditingReportablePerson = true;
            return View(ReportablePersonFormView, model);
        }
    }

    #endregion

    #region Lobbying Firm (48 Hour End-of-session Lobbying Report Transaction)

    /// <summary>
    /// The GET action for retrieving lobbying firms by name or ID
    /// </summary>
    /// <param name="contactsApi"></param>
    /// <param name="search"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>JsonResult needed to be displayed on _LobbyingEntitySearch.cshtml</returns>
    [HttpGet("SearchLobbyingFirmsByIdOrName")]
    public async Task<JsonResult> SearchLobbyingFirmsByIdOrName(
        [FromQuery] string search,
        [FromQuery] long filerId,
        CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return new JsonResult(new { });
        }

        var data = await _contactsApi.SearchLobbyingFirmsByNameOrId(search, filerId, cancellationToken);
        return new JsonResult(data);
    }

    /// <summary>
    /// Handles the cancel transaction action by displaying a success message and redirecting to the transaction summary.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private RedirectToActionResult HandleCancelLobbyingFirmSelect(FirmPaymentTransactionViewModel model)
    {
        _toastService.Success(_localizer[ResourceConstants.CancelTransactionSuccessMessage]);
        return RedirectToTransactionSummary(model);
    }

    /// <summary>
    /// Redirects to the 48 Hour end-of-session lobbying transaction summary page.
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private RedirectToActionResult RedirectToTransactionSummary(FirmPaymentTransactionViewModel model) =>
        RedirectToAction("Index", "Disclosure", new
        {
            viewName = FilingSummaryTypeModel.EndOfSessionLobbyingSummary.Name,
            reportType = model.ReportType,
            filingId = model.FilingId,
            filerId = model.FilerId
        });

    /// <summary>
    /// Lobbying Firm selection screen for the 48 Hour end-of-session lobbying report transaction, the first page in the process.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet("SelectLobbyingFirm")]
    public IActionResult SelectLobbyingFirm(
        [FromQuery] string reportType,
        [FromQuery] long filingId,
        [FromQuery] long filerId,
        [FromQuery] long? contactId,
        [FromQuery] long? registrationFilingId,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = new Report48HTransactionViewModel { ReportType = reportType, FilingId = filingId, FilerId = filerId, ContactId = contactId, RegistrationFilingId = registrationFilingId };
        return View(SelectLobbyingFirmView, model);
    }

    /// <summary>
    /// Handles redirection if existing Lobbying Firm is selected
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost("SelectLobbyingFirm")]
    public IActionResult SelectLobbyingFirm(Report48HTransactionViewModel model, string? action = "", CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return action switch
        {
            FormActionPrevious => RedirectToTransactionSummary(model),
            FormActionCancelConfirm => RedirectToTransactionSummary(model),
            _ => RedirectToAction(nameof(CreateEditLobbyingFirm), new { reportType = model.ReportType, filingId = model.FilingId, filerId = model.FilerId, contactId = model.ContactId, registrationFilingId = model.RegistrationFilingId })
        };
    }

    /// <summary>
    /// Lobbying Firm create/edit screen for the 48 Hour end-of-session lobbying report transaction.
    /// </summary>
    /// <param name="reportType"></param>
    /// <param name="filingId"></param>
    /// <param name="filerId"></param>
    /// <param name="contactId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet("CreateEditLobbyingFirm")]
    public async Task<IActionResult> CreateEditLobbyingFirm(
        [FromQuery] CreateEditLobbyingFirmParamaeters parameters,
        [FromServices] IFirmPaymentTransactionCtlSvc firmPaymentTransactionCtlSvc,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var model = await firmPaymentTransactionCtlSvc.GetViewModel(parameters.ReportType, parameters.FilingId, parameters.FilerId, parameters.ContactId, parameters.RegistrationFilingId, cancellationToken);

        if (model is not null)
        {
            model.Id = parameters.TransactionId;
        }

        return View(LobbyingFirmView, model);
    }

    /// <summary>
    /// Handles redirects or saving for the Lobbying Firm page.
    /// </summary>
    /// <param name="model"></param>
    /// <param name="action"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost("CreateEditLobbyingFirm")]
    public async Task<IActionResult> CreateEditLobbyingFirm(
        FirmPaymentTransactionViewModel model,
        string action,
        [FromServices] IFirmPaymentTransactionCtlSvc firmPaymentTransactionCtlSvc,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        return action switch
        {
            FormActionPrevious => RedirectToAction(nameof(SelectLobbyingFirm),
                new
                {
                    reportType = model.ReportType,
                    filingId = model.FilingId,
                    filerId = model.FilerId,
                    registrationFilingId = model.RegistrationFilingId
                }),
            FormActionContinue => await SaveLobbyingFirmAsync(model, firmPaymentTransactionCtlSvc),
            _ => HandleCancelLobbyingFirmSelect(model)
        };
    }

    /// <summary>
    /// Save Lobbying Firm async operation.
    /// </summary>
    /// <param name="model">The transaction view model containing the Lobbying Firm information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>IActionResult for the next view or error handling</returns>
    private async Task<IActionResult> SaveLobbyingFirmAsync(FirmPaymentTransactionViewModel model, IFirmPaymentTransactionCtlSvc firmPaymentTransactionCtlSvc)
    {
        if (model.FilingId == null || model.FilerId == null)
        {
            return NotFound();
        }

        // Skip save operation for FilerContact if RegistrationFilingId is provided
        if (model.RegistrationFilingId != null)
        {
            return RedirectTo48HEosTransaction(model);
        }

        // Preserve the model state before it is mutated.
        bool isUpdated = model.ContactId.HasValue;
        await firmPaymentTransactionCtlSvc.SaveFilerContact(model, ModelState, "EndOfSession");

        if (ModelState.IsValid)
        {
            if (isUpdated)
            {
                _toastService.Success(_localizer["FilerPortal.Contact.LobbyingFirm.UpdateSuccessMessage"]);
            }
            else
            {
                _toastService.Success(_localizer["FilerPortal.Contact.LobbyingFirm.CreateSuccessMessage"]);
            }
            return RedirectTo48HEosTransaction(model);
        }

        return View(LobbyingFirmView, model);
    }

    /// <summary>
    /// Redirects to the main 48 Hour Report transaction form.
    /// </summary>
    /// <param name="model">The transaction view model</param>
    /// <returns>RedirectToActionResult to the 48 Hour Report transaction form</returns>
    private RedirectToActionResult RedirectTo48HEosTransaction(FirmPaymentTransactionViewModel model)
    {
        var action = model.Id.HasValue ? "EditReport48HEosTransaction" : "Report48HEosTransaction";
        return RedirectToAction(action, TransactionController,
            new
            {
                reportType = model.ReportType,
                transactionId = model.Id,
                filingId = model.FilingId,
                filerId = model.FilerId,
                contactId = model.ContactId,
                registrationFilingId = model.RegistrationFilingId
            });
    }

    #endregion

    #region Helpers

    private void SyncDecisionsValidationsWithViewModel(
    IList<WorkFlowError> decisions,
    IPortalAlertsContainer viewModel
    )
    {
        if (decisions.Any())
        {
            for (int i = 0; i < decisions.Count; i++)
            {
                var dsmNew = decisions[i];
                this.AddDecisionValidationToViewModel(viewModel, new ValidationMessage { ErrorCode = dsmNew.ErrorCode, FieldName = dsmNew.FieldName, Message = dsmNew.Message, Type = dsmNew.ErrorType });
            }
        }
    }

    private static async Task<ReportablePersonViewModel> PopulateReferenceData(ReportablePersonViewModel model, IReferenceDataApi referenceDataApi)
    {
        var officialPositions = await referenceDataApi.GetAllOfficialPositions();
        var agencies = await referenceDataApi.GetAllAgencies();
        model.OfficialPositions = officialPositions!.ToDictionary(x => x.Name, x => x.Name);
        model.Agencies = agencies!.ToDictionary(x => x.Id.ToString(CultureInfo.InvariantCulture), x => x.Name);
        return model;
    }

    #endregion

    private async Task HandleUpdateFilerContact(GenericContactViewModel model, UpsertFilerContactRequest upsertContactRequest, CancellationToken cancellationToken)
    {
        var filerContact = await _contactsApi.GetFilerContactById((long)model.Id!, cancellationToken);

        // If editting Filer Contact Type, create a new Filer Contact record
        if (filerContact.TypeId != model.TypeId)
        {
            var newFilerContact = await _contactsApi.CreateFilerContact((long)model.FilerId!, upsertContactRequest, cancellationToken);

            if (newFilerContact.ValidationErrors.Count > 0)
            {
                foreach (var error in newFilerContact.ValidationErrors!)
                {
                    SyncDecisionsValidationsWithViewModel([error], model);
                }
            }
            else
            {
                model.Id = newFilerContact.Id;
                _toastService.Success("Contact updated successfully.");
            }
        }
        else
        {
            _ = await _contactsApi.UpdateFilerContact((long)model.Id!, upsertContactRequest, cancellationToken);
            _toastService.Success("Contact updated successfully.");
        }
    }

    public class CreateEditLobbyingFirmParamaeters
    {
        [FromQuery(Name = "reportType")]
        [JsonRequired]
        public required string ReportType { get; set; }

        [FromQuery(Name = "filingId")]
        [JsonRequired]
        public required long FilingId { get; set; }

        [FromQuery(Name = "filerId")]
        [JsonRequired]
        public required long FilerId { get; set; }

        [FromQuery(Name = "contactId")]
        public long? ContactId { get; set; }

        [FromQuery(Name = "transactionId")]
        public long? TransactionId { get; set; }

        [FromQuery(Name = "registrationFilingId")]
        public long? RegistrationFilingId { get; set; }
    }
}
