using NSubstitute;
using SOS.CalAccess.Data.Efile;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Services.Business.Efile;

namespace SOS.CalAccess.Services.Business.Tests.Efile;

/// <summary>
/// Unit tests for the <see cref="ApiRequestSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[TestFixture]
[TestOf(typeof(ApiRequestSvc))]
public sealed class ApiRequestSvcTest
{
    private IApiRequestRepository _apiRequestRepository;
    private ApiRequestSvc _apiRequestSvc;
    private IDateTimeSvc _dateTimeSvc;
    private DateTime _dateNow;

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _apiRequestRepository = Substitute.For<IApiRequestRepository>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        _apiRequestSvc = new ApiRequestSvc(_apiRequestRepository, _dateTimeSvc);
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    [Test]
    public async Task PersistApiRequests_ValidInput()
    {
        string path = "/new/path";
        string userId = "1000";
        string filerId = "5050";
        string filingId = "1500";
        int apiRequestStatusId = 1;
        DateTime recievedAt = _dateNow;
        int fileSize = 1000;
        int transactionCount = 4;

        var newApiRequest = new ApiRequest
        {
            Path = path,
            UserId = userId,
            FilerId = filerId,
            FilingId = filingId,
            ApiRequestStatusId = apiRequestStatusId,
            ReceivedAt = recievedAt,
            FileSize = fileSize,
            TransactionCount = transactionCount
        };

        _ = _apiRequestRepository.Create(newApiRequest).Returns(Task.FromResult(newApiRequest));
        _dateTimeSvc.GetCurrentDateTime().Returns(_dateNow);
        var result = await _apiRequestSvc.CreateNewApiRequest(newApiRequest);

        Assert.That(result, Is.EqualTo(newApiRequest));
        _ = _apiRequestRepository.Received().Create(newApiRequest);
    }

    [Test]
    public async Task UpdateStatusOfApiRequests_ValidInput_ShouldUpdateStatusAndAddErrors()
    {
        int apiRequestId = 2;
        int apiRequestStatusId = 8;
        var errors = new List<ApiError>
        {
            new() { ApiErrorCode = "ERR001", ApiErrorDescription = "Invalid schema", ApiErrorField = "Filer Name" },
            new() { ApiErrorCode = "ERR002", ApiErrorDescription = "Missing required field", ApiErrorField = "Country" }
        };

        // Act
        await _apiRequestSvc.InvalidateRequest(apiRequestId, errors);

        // Verify status update
        _ = _apiRequestRepository.Received().UpdateStatusOfApiRequests(2, apiRequestStatusId, errors);
    }

    [Test]
    public async Task AcceptRequest_ShouldUpdateStatusAndFilingId_WhenValidInputProvided()
    {
        int apiRequestId = 2;
        string filingId = "1500";
        int apiRequestStatusId = 7;

        // Act
        await _apiRequestSvc.AcceptRequest(apiRequestId, filingId);

        // Verify status update
        _ = _apiRequestRepository.Received().UpdateFilingIdOfApiRequests(2, "1500");
        _ = _apiRequestRepository.Received().UpdateStatusOfApiRequests(2, apiRequestStatusId, Arg.Any<List<ApiError>>());
    }

    [Test]
    public async Task RejectRequestForSystemError_ShouldUpdateStatusAndErrors_WhenValidInputProvided()
    {
        int apiRequestId = 2;
        var errors = new List<ApiError>
        {
            new() { ApiErrorCode = "ERR001", ApiErrorDescription = "Invalid schema", ApiErrorField = "Filer Name" },
            new() { ApiErrorCode = "ERR002", ApiErrorDescription = "Missing required field", ApiErrorField = "Country" }
        };

        int apiRequestStatusId = 5;
        // Act
        await _apiRequestSvc.RejectRequestForSystemError(apiRequestId, errors);

        // Verify status update
        _ = _apiRequestRepository.Received().UpdateStatusOfApiRequests(2, apiRequestStatusId, errors);
    }

    [Test]
    public async Task RejectRequestForBusinessRuleViolation_ShouldUpdateStatusAndErrors_WhenValidInputProvided()
    {
        int apiRequestId = 2;
        var errors = new List<ApiError>
        {
            new() { ApiErrorCode = "ERR001", ApiErrorDescription = "limit exceeded"},
            new() { ApiErrorCode = "ERR002", ApiErrorDescription = "Another forms required" }
        };

        int apiRequestStatusId = 6;
        // Act
        await _apiRequestSvc.RejectRequestForBusinessRuleViolation(apiRequestId, errors);

        // Verify status update
        _ = _apiRequestRepository.Received().UpdateStatusOfApiRequests(2, apiRequestStatusId, errors);
    }

    [Test]
    public async Task HoldRequestForReview_ShouldUpdateStatusAndErrors_WhenValidInputProvided()
    {
        int apiRequestId = 2;
        var errors = new List<ApiError>
        {
            new() { ApiErrorCode = "ERR001", ApiErrorDescription = "Incomplete information"},
            new() { ApiErrorCode = "ERR002", ApiErrorDescription = "Required supporting documents" }
        };

        int apiRequestStatusId = 4;
        // Act
        await _apiRequestSvc.HoldRequestForReview(apiRequestId, errors);

        // Verify status update
        _ = _apiRequestRepository.Received().UpdateStatusOfApiRequests(2, apiRequestStatusId, errors);
    }

    [Test]
    public async Task ProcessRequest_ShouldUpdateStatus_WhenValidInputProvided()
    {
        int apiRequestId = 2;
        int apiRequestStatusId = 3;

        // Act
        await _apiRequestSvc.ProcessRequest(apiRequestId);

        // Verify status update
        _ = _apiRequestRepository.Received().UpdateStatusOfApiRequests(2, apiRequestStatusId, Arg.Any<List<ApiError>>());
    }

    [Test]
    public async Task QueueApiRequestForProcessing_ShouldUpdateStatus_WhenValidInputProvided()
    {
        int apiRequestId = 2;
        int apiRequestStatusId = 2;

        // Act
        await _apiRequestSvc.QueueApiRequestForProcessing(apiRequestId);

        // Verify status update
        _ = _apiRequestRepository.Received().UpdateStatusOfApiRequests(2, apiRequestStatusId, Arg.Any<List<ApiError>>());
    }

}
