using SOS.CalAccess.Data.Authorization;
using SOS.CalAccess.Data.Common;
using SOS.CalAccess.Data.Contracts.Authorization;
using SOS.CalAccess.Data.Contracts.Common;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Elections;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.FilerRegistration;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;

namespace SOS.CalAccess.Services.Business;

#pragma warning disable S107 // Methods should not have too many parameters
public class ReferenceDataSvc(
    IFilerTypeRepository filerTypeRepository,
    INotificationTypeRepository notificationTypeRepository,
    IOfficeRepository officeRepository,
    IPoliticalPartyRepository politicalPartyRepository,
    IOfficialPositionRepository officialPositionRepository,
    IAgencyRepository agencyRepository,
    IPermissionRepository permissionRepository,
    IPaymentCodeRepository paymentCodeRepository,
    IBillRepository billRepository,
    ICountryRepository countryRepository,
    IExpenditureCodeRepository expenditureCodeRepository,
    IAdvertisementDistributionMethodRepository advertisementDistributionMethodRepository,
    IFilerRoleRepository filerRoleRepository,
    INatureAndInterestTypeRepository natureAndInterestTypeRepository,
    IIndustryGroupClassificationTypeRepository industryGroupClassificationTypeRepository,
    IBusinessSubcategoryRepository businessSubcategoryRepository
    ) : IReferenceDataSvc
#pragma warning disable S107 // Methods should not have too many parameters
{
    /// <summary>
    /// Get all filer types
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<FilerType>> GetAllFilerTypes()
    {
        return await filerTypeRepository.GetAll();
    }

    /// <summary>
    /// Get all filer roles
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<FilerRoleDto>> GetAllFilerRoles()
    {
        var roles = await filerRoleRepository.GetAll();
        return roles.Select(x => new FilerRoleDto
        {
            Id = x.Id,
            Name = x.Name,
            Description = x.Description,
            FilerTypeId = x.FilerTypeId,
            IsDefaultFilerTypeRole = x.IsDefaultFilerTypeRole,
            IsRequestable = x.IsRequestable,
        });
    }

    /// <summary>
    /// Get all notification types
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<NotificationType>> GetAllNotificationTypes()
    {
        return await notificationTypeRepository.GetAll();
    }

    /// <summary>
    /// Get Office by Id
    /// </summary>
    /// <param name="officeId"></param>
    /// <returns></returns>
    public async Task<Office?> GetOffice(long officeId)
    {
        return await officeRepository.FindById(officeId);
    }

    /// <summary>
    /// Get All political parties
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<PoliticalParty>> GetAllPoliticalParties()
    {
        return await politicalPartyRepository.GetAll();
    }


    /// <summary>
    /// List permissions
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<MaintainPermissionsDto>> ListPermissions()
    {

        var result = await permissionRepository.GetAll();
        return result.Select(ConvertPermissionToDto).ToList();
    }

    private static MaintainPermissionsDto ConvertPermissionToDto(Permission model)
    {
        return new MaintainPermissionsDto()
        {
            Id = model.Id,
            Name = model.Name ?? "",
            Description = model.Description ?? "",
            Granted = false
        };
    }

    /// <summary>
    /// Get All offical positions
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<OfficialPosition>> GetAllOfficialPositions()
    {
        return await officialPositionRepository.GetAll();
    }

    /// <summary>
    /// Get All agencies
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<Agency>> GetAllAgencies()
    {
        return await agencyRepository.GetAll();
    }

    /// <summary>
    /// Search all agencies
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<Agency>> SearchAllAgencies(string query)
    {
        return await agencyRepository.GetAll(query);
    }

    /// <summary>
    /// Get All payment codes
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<PaymentCodeRefResponse>> GetAllPaymentCodes()
    {
        var paymentCodes = await paymentCodeRepository.GetAll();
        return paymentCodes.Select(pc => new PaymentCodeRefResponse
        {
            Id = pc.Id,
            Name = pc.Name,
            DisplayName = pc.GetDisplayName()
        });
    }

    /// <summary>
    /// Get All advertisement distribution methods
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<AdvertisementDistributionMethodRefResponse>> GetAllAdvertisementDistributionMethods()
    {
        var distributionMethods = await advertisementDistributionMethodRepository.GetAll();
        return distributionMethods.Select(pc => new AdvertisementDistributionMethodRefResponse
        {
            Id = pc.Id,
            Name = pc.Name,
            DisplayName = pc.GetDisplayName()
        });
    }

    /// <summary>
    /// Search all bills
    /// </summary>
    /// <returns></returns>
    /// 
    public async Task<IEnumerable<Bill>> SearchAllBills(string query)
    {
        return await billRepository.GetAll(query);
    }

    /// <summary>
    /// Get all country codes
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<Country>> GetAllCountryCodes()
    {
        return await countryRepository.GetAll();
    }

    /// <summary>
    /// Get a country record by id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<Country?> GetCountryById(long id)
    {
        return await countryRepository.FindById(id);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ExpenditureCode>> GetAllExpenditureCodesAsync()
    {
        return await expenditureCodeRepository.GetAll();
    }

    public async Task<IEnumerable<NatureAndInterestType>> GetAllNatureAndInterestTypesAsync()
    {
        return await natureAndInterestTypeRepository.GetAll();
    }

    public async Task<IEnumerable<IndustryGroupClassificationType>> GetAllIndustryGroupClassificationTypesAsync()
    {
        return await industryGroupClassificationTypeRepository.GetAll();
    }

    public async Task<IEnumerable<BusinessSubcategory>> GetAllBusinessSubcategoriesAsync()
    {
        return await businessSubcategoryRepository.GetAll();
    }
}
