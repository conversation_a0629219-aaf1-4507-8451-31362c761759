using System.Globalization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using NSubstitute;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Filings;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Services;
using FilingStatus = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingStatus;
using FilingType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
using ReponsibleOfficerDto = SOS.CalAccess.Models.FilerDisclosure.Filings.ReponsibleOfficerDto;
using WorkFlowError = SOS.CalAccess.FilerPortal.Generated.WorkFlowError;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class FilingControllerTests
{
    #region Setup

    private Mock<ITempDataDictionaryFactory> _tempDataFactoryMock;
    private Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private Mock<IStringLocalizer<SharedResources>> _localizerMock;
    private Mock<ILogger<FilingController>> _loggerMock;
    private Mock<IToastService> _toastService;
    private Mock<IFilingsApi> _filingsApiMock;
    private Mock<IFilingSvc> _filingSvc;
    private Mock<IUsersApi> _userApiMock;
    private FilingController _controller;
    private IDateTimeSvc _dateTimeSvc;
    private DateTime DateNow;

    [SetUp]
    public void Setup()
    {
        _loggerMock = new Mock<ILogger<FilingController>>();

        _tempDataFactoryMock = new Mock<ITempDataDictionaryFactory>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        _filingsApiMock = new Mock<IFilingsApi>();
        _userApiMock = new Mock<IUsersApi>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();
        DateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        var tempDataMock = new Mock<ITempDataDictionary>();
        var httpContextMock = new Mock<HttpContext>();

        _tempDataFactoryMock.Setup(x => x.GetTempData(httpContextMock.Object)).Returns(tempDataMock.Object);
        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContextMock.Object);

        // Mock the keys for Toast success/danger/warning
        tempDataMock.Setup(td => td["ToastType"]).Returns("");
        tempDataMock.Setup(td => td["ToastMessage"]).Returns("");
        tempDataMock.Setup(td => td["ToastShowCloseButton"]).Returns("");
        tempDataMock.Setup(td => td["ToastX"]).Returns("");
        tempDataMock.Setup(td => td["ToastY"]).Returns("");
        tempDataMock.Setup(td => td["ToastTimeOut"]).Returns("");

        _toastService = new Mock<IToastService>();
        _filingSvc = new Mock<IFilingSvc>();
        _localizerMock = new Mock<IStringLocalizer<SharedResources>>();

        _controller = new FilingController(_toastService.Object, _localizerMock.Object, _loggerMock.Object, _filingSvc.Object, _userApiMock.Object, _dateTimeSvc)
        {
            TempData = tempDataMock.Object
        };
    }

    [TearDown]
    public void TearDown()
    {
        _controller?.Dispose();
    }

    #endregion

    [Test]
    public async Task CancelReport_ShouldRedirectToDashboard_WhenCancellationIsSuccessful()
    {
        // Arrange
        var mockFilingApi = new Mock<IFilingsApi>();

        long reportId = 123;

        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.CancelReportSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.CancelReportSuccessMessage", "Cancelled successfully"));
        // Act
        var result = await _controller.CancelReport(reportId, mockFilingApi.Object, CancellationToken.None);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("TemporaryDashboard"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Filing"));
        });
    }

    [Test]
    public async Task CancelReport_ShouldReturnNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        _controller.ModelState.AddModelError("id", "Required");

        // Act
        var result = await _controller.CancelReport(0, Mock.Of<IFilingsApi>(), CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Verification_InvalidGet_ShouldReturnNotFound()
    {
        // Arrange
        var mockFilersApi = new Mock<IFilersApi>();
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.Verification(mockFilersApi.Object, 1, 1, "SampleReport");

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public async Task Verification_ShouldReturnVerificationPage()
    {
        // Arrange
        var mockFilersApi = new Mock<IFilersApi>();
        var expectedFilerId = 1;
        var expectedFilingId = 2;
        var expectedFilerName = "Test Filer";
        var approvedAt = DateNow;
        var reportType = FilingTypeModel.LobbyistReport.Name;

        _ = mockFilersApi.Setup(api => api.GetCurrentRegistration(expectedFilerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RegistrationResponse(approvedAt, expectedFilerId, expectedFilingId, expectedFilerName, 3, reportType));

        // Act
        var result = await _controller.Verification(mockFilersApi.Object, expectedFilerId, expectedFilingId, reportType);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());

        var viewResult = result as ViewResult;
        Assert.That(viewResult!.ViewName, Is.EqualTo("Verification"));

        var model = viewResult.Model as LobbyingReportVerificationViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(expectedFilingId));
            Assert.That(model.FilerId, Is.EqualTo(expectedFilerId));
            Assert.That(model.FilerName, Is.EqualTo(expectedFilerName));
        });
    }

    [Test]
    public async Task SendForAttestation_InvalidGet_ShouldReturnNotFound()
    {
        // Arrange
        var mockFilersApi = new Mock<IFilersApi>();
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.SendForAttestation(mockFilersApi.Object, 1, 1, "SampleReport");

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public async Task SendForAttestation_ShouldReturnCorrectPage()
    {
        // Arrange
        var mockFilersApi = new Mock<IFilersApi>();
        var expectedFilerId = 1;
        var expectedFilingId = 2;
        var expectedFilerName = "Test Filer";
        var approvedAt = DateNow;
        var reportType = FilingTypeModel.LobbyistReport.Name;

        _ = mockFilersApi.Setup(api => api.GetCurrentRegistration(expectedFilerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RegistrationResponse(approvedAt, expectedFilerId, expectedFilingId, expectedFilerName, 3, reportType));

        // Act
        var result = await _controller.SendForAttestation(mockFilersApi.Object, expectedFilerId, expectedFilingId, reportType);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());

        var viewResult = result as ViewResult;
        Assert.That(viewResult!.ViewName, Is.EqualTo("SendForAttestation"));

        var model = viewResult.Model as VerificationFilingReportViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model.FilingId, Is.EqualTo(expectedFilingId));
            Assert.That(model.FilerId, Is.EqualTo(expectedFilerId));
            Assert.That(model.FilerName, Is.EqualTo(expectedFilerName));
        });
    }

    [Test]
    public void Submitted_InvalidGet_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = _controller.Submitted();

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public void Submitted_ShouldReturnSubmittedPage()
    {
        // Arrange
        _controller.ViewBag.SubmittedDate = DateNow;
        _controller.ViewBag.ReportType = "Sample Report";

        // Act
        var result = _controller.Submitted();

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());

        var viewResult = result as ViewResult;
        Assert.That(viewResult!.ViewName, Is.EqualTo("Submitted"));
        Assert.That(_controller.ViewBag.SubmittedDate, Is.InstanceOf<DateTime>());
        Assert.That(_controller.ViewBag.ReportType, Is.InstanceOf<string>());
    }

    [Test]
    public async Task SubmitLobbyistReport_ShouldRedirectToSubmittedPage()
    {
        // Arrange
        var reportType = "LobbyistReport";
        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            DiligenceStatementVerification = true
        };

        var mockFilingsApi = new Mock<IFilingsApi>();
        var workflowErrors = new List<WorkFlowError>();
        var expectedResponse = new SubmitLobbyingReportResponse(true, 1, 1, DateNow, true, workflowErrors);

        _ = mockFilingsApi.Setup(f => f.SubmitLobbyistReport(
                model.Id,
                It.IsAny<SubmitLobbyistReportDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Mock the success message localization
        _ = _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage", "Success message"));

        // Act
        var result = await _controller.SubmitLobbyistReport(reportType, model, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());

        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("Submitted"));
            Assert.That(_controller.ViewBag.SubmittedDate, Is.EqualTo(expectedResponse.SubmittedDate));
            Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(reportType));
        });

        // Verify the toast service was called with success
        _toastService.Verify(ts => ts.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }

    [Test]
    public async Task SubmitLobbyistReport_ShouldReturnVerificationView_WhenResponseIsInvalid()
    {
        // Arrange
        var reportType = "LobbyistReport";
        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            DiligenceStatementVerification = true
        };

        var mockFilingsApi = new Mock<IFilingsApi>();

        var validationErrors = new List<WorkFlowError>
        {
            new ("code", "Validation", "field", "field is required")
        };

        var expectedResponse = new SubmitLobbyingReportResponse(
            valid: false,
            id: 1,
            submittedDate: null,
            validationErrors: validationErrors,
            diligenceStatementVerified: true,
            statusId: 1
        );

        mockFilingsApi.Setup(f => f.SubmitLobbyistReport(
                model.Id,
                It.IsAny<SubmitLobbyistReportDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Mock the error toast localization
        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.SubmitReportErrorMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.SubmitReportErrorMessage", "Error message"));

        // Act
        var result = await _controller.SubmitLobbyistReport(reportType, model, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());

        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("Verification"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
            Assert.That(model.Messages.Validations, Is.Not.Null.And.Not.Empty);
            Assert.That(model.Messages.Validations["field"].Message, Is.EqualTo("field is required"));
        });

        // Verify the toast service was called with error
        _toastService.Verify(ts => ts.Error("Error message", true, "Right", "Bottom", 0), Times.Once);
    }

    [Test]
    public async Task Amend_InvalidModelState_ReturnsNotFound()
    {
        // Arrange: Make the model state invalid.
        _controller.ModelState.AddModelError("id", "Required");
        var mockFilingsApi = new Mock<IFilingsApi>();

        // Act
        var result = await _controller.Amend(1, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>(), "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public void UpdateLobbyistEmployerReportDto_ShouldSetCurrencyProperties()
    {
        // Arrange
        var dto = new UpdateLobbyistEmployerReportDto(
            id: 1,
            totalOverheadExpense: 123.45,
            totalPaymentsToInHouseLobbyists: null,
            totalUnderThresholdPayments: 678.90
        );

        // Act & Assert
        Assert.Multiple(() =>
        {
            Assert.That(dto.Id, Is.EqualTo(1));
            Assert.That(dto.TotalOverheadExpense, Is.EqualTo(123.45));
            Assert.That(dto.TotalUnderThresholdPayments, Is.EqualTo(678.90));
        });
    }

    [Test]
    public async Task LobbyistEmployer_Post_IsStartedFalse_ReturnsViewWithUpdatedModel()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var userId = 42L;
        var filerId = 1001L;
        var userResponse = new UserItemResponse("<EMAIL>", "John", userId, "Doe");
        var filers = new List<FilerUserDto>();
        var model = new LobbyistEmployerReportViewModel
        {
            IsStarted = false,
            FilerId = filerId,
            Filers = new List<SelectListItem>()
        };

        _userApiMock.Setup(x => x.GetSelf(cancellationToken)).ReturnsAsync(userResponse);
        _filingsApiMock.Setup(x => x.GetFilersAssociatedUser(userId, FilingTypeModel.LobbyistEmployerReport.Id, cancellationToken))
            .ReturnsAsync(filers);

        // Act
        var result = await _controller.LobbyistEmployer(model, _filingsApiMock.Object, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = (ViewResult)result;
        Assert.That(viewResult.Model, Is.InstanceOf<LobbyistEmployerReportViewModel>());
        var returnedModel = (LobbyistEmployerReportViewModel)viewResult.Model;

        Assert.Multiple(() =>
        {
            Assert.That(returnedModel.IsStarted, Is.True);
            Assert.That(returnedModel.FilerId, Is.EqualTo(filerId));
            Assert.That(returnedModel.Filers, Is.Not.Null);
        });
    }

    [Test]
    public async Task Lobbyist_Post_IsStartedFalse_NoFilers_ReturnsViewWithModelUnchangedFilerId()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var userId = 42L;
        var userResponse = new UserItemResponse("<EMAIL>", "John", userId, "Doe");
        var filers = new List<FilerUserDto>();
        var model = new LobbyistReportViewModel
        {
            IsStarted = false,
            FilerId = 555,
            Filers = null!
        };

        _userApiMock.Setup(x => x.GetSelf(cancellationToken)).ReturnsAsync(userResponse);
        _filingsApiMock.Setup(x => x.GetFilersAssociatedUser(userId, FilingTypeModel.LobbyistReport.Id, cancellationToken))
            .ReturnsAsync(filers);

        // Act
        var result = _controller.Lobbyist(model, _filingsApiMock.Object, cancellationToken);

        // Assert
        var viewResult = (ViewResult)(await result);
        Assert.That(viewResult.Model, Is.InstanceOf<LobbyistReportViewModel>());
        var returnedModel = (LobbyistReportViewModel)viewResult.Model;

        Assert.Multiple(() =>
        {
            Assert.That(returnedModel.FilerId, Is.EqualTo(555));
            Assert.That(returnedModel.Filers, Is.Not.Null);
        });
    }

    [Test]
    public async Task Lobbyist_Post_IsStartedFalse_ReturnsViewWithUpdatedModel()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var userId = 42L;
        var filerId = 1;
        var userResponse = new UserItemResponse("<EMAIL>", "John", userId, "Doe");
        var filers = new List<FilerUserDto>()
        {
            new(filerId: 1, filerName: "", filerRole: It.IsAny<FilerRole>(), filerRoleId: 1, id: 1, user: It.IsAny<User>(),
                userId: 1)
        };
        var model = new LobbyistReportViewModel
        {
            IsStarted = false,
            FilerId = filerId,
            Filers = new List<SelectListItem>()
            {
                new SelectListItem("foo", "bar")
            }
        };

        _userApiMock.Setup(x => x.GetSelf(cancellationToken)).ReturnsAsync(userResponse);
        _filingsApiMock.Setup(x => x.GetFilersAssociatedUser(It.IsAny<long>(), It.IsAny<long>(), cancellationToken))
            .ReturnsAsync(filers);

        // Act
        var result = await _controller.Lobbyist(model, _filingsApiMock.Object, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = (ViewResult)result;
        Assert.That(viewResult.Model, Is.InstanceOf<LobbyistReportViewModel>());
        var returnedModel = (LobbyistReportViewModel)viewResult.Model;

        Assert.Multiple(() =>
        {
            Assert.That(returnedModel.IsStarted, Is.True);
            Assert.That(returnedModel.FilerId, Is.EqualTo(filerId));
            Assert.That(returnedModel.Filers, Is.Not.Null);
        });
    }

    [Test]
    public async Task LobbyistEmployer_Post_IsStartedFalse_NoFilers_ReturnsViewWithModelUnchangedFilerId()
    {
        // Arrange
        var cancellationToken = CancellationToken.None;
        var userId = 42L;
        var userResponse = new UserItemResponse("<EMAIL>", "John", userId, "Doe");
        var filers = new List<FilerUserDto>();
        var model = new LobbyistEmployerReportViewModel
        {
            IsStarted = false,
            FilerId = 555,
            Filers = new List<SelectListItem>()
        };

        _userApiMock.Setup(x => x.GetSelf(cancellationToken)).ReturnsAsync(userResponse);
        _filingsApiMock.Setup(x => x.GetFilersAssociatedUser(userId, FilingTypeModel.LobbyistEmployerReport.Id, cancellationToken))
            .ReturnsAsync(filers);

        // Act
        var result = await _controller.LobbyistEmployer(model, _filingsApiMock.Object, cancellationToken);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var viewResult = (ViewResult)result;
        Assert.That(viewResult.Model, Is.InstanceOf<LobbyistEmployerReportViewModel>());
        var returnedModel = (LobbyistEmployerReportViewModel)viewResult.Model;

        Assert.Multiple(() =>
        {
            Assert.That(returnedModel.IsStarted, Is.True);
            Assert.That(returnedModel.FilerId, Is.EqualTo(555));
            Assert.That(returnedModel.Filers, Is.Not.Null);
        });
    }

    [Test]
    public async Task Report72H_ValidModel_ReturnsViewWithModel()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        var registration = new RegistrationItemResponse(
            approvedAt: DateNow,
            filerId: 12345,
            id: 67890,
            name: "Test Registration",
            statusId: 1,
            type: "LobbyistEmployer"
        );

        mockRegistrationsApi
            .Setup(x => x.GetLobbyistEmployerEntityRegistration(It.IsAny<CancellationToken>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Report72H(mockRegistrationsApi.Object, mockFilingsApi.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var model = result!.Model as Report72HViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model!.FilerId, Is.EqualTo(12345));
    }

    [Test]
    public async Task CreateReport72H_ValidModel_RedirectsToIndex()
    {
        // Arrange
        var model = new Report72HViewModel
        {
            Id = 1,
            FilerId = 456
        };

        var mockFilingsApi = new Mock<IFilingsApi>();

        var response = new FilingResponseDto(
            endDate: DateNow,
            filerId: 456,
            filerName: "Jane Lobbyist",
            filingStatus: "Pending",
            filingType: "Report72H",
            id: 67890,
            parentId: null,
            startDate: DateNow.AddMonths(-1),
            status: 1,
            submittedDate: null,
            version: null
        );

        mockFilingsApi
            .Setup(f => f.CreateReport72H((long)model.FilerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateReport72H(model, mockFilingsApi.Object) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.RouteValues!["filingId"], Is.EqualTo(67890));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(456));
        });
    }

    [Test]
    public async Task Report72H_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        _controller.ModelState.AddModelError("SomeKey", "Some error");

        // Act
        var result = await _controller.Report72H(mockRegistrationsApi.Object, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CreateReport72H_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new Report72HViewModel
        {
            Id = 1,
            FilerId = 456
        };

        _controller.ModelState.AddModelError("SomeKey", "Some error");

        // Act
        var result = await _controller.CreateReport72H(model, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_ShouldReturnSubmittedView_WhenResponseIsValid()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            FilerId = 100,
            DiligenceStatementVerification = true
        };

        var response = new ValidatedLobbyistEmployerReport(
            true,
            1,
            1,
            submittedDate: new DateTime(2024, 1, 1, 12, 0, 0, 0),
            true,
            validationErrors: []);

        mockFilingsApi.Setup(api => api.SubmitLobbyistEmployerReport(
                model.Id,
                It.IsAny<SubmitLobbyistEmployerReportDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.SubmitLobbyistEmployerReport("LobbyistEmployerReport", model, mockFilingsApi.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Submitted"));


        // Check ViewBag values
        Assert.That(_controller.ViewBag.SubmittedDate, Is.EqualTo(response.SubmittedDate));
        Assert.That(_controller.ViewBag.ReportType, Is.EqualTo("LobbyistEmployerReport"));
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_ShouldReturnSubmittedView_WhenResponseIsInvalid()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            FilerId = 100,
            DiligenceStatementVerification = true
        };

        var response = new ValidatedLobbyistEmployerReport(
            true,
            1,
            1,
            submittedDate: DateTime.Parse("2024-01-01", CultureInfo.InvariantCulture),
            false,
            validationErrors: []);

        mockFilingsApi.Setup(api => api.SubmitLobbyistEmployerReport(
                model.Id,
                It.IsAny<SubmitLobbyistEmployerReportDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.SubmitLobbyistEmployerReport("LobbyistEmployerReport", model, mockFilingsApi.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Verification"));


        // Check ViewBag values
        Assert.That(_controller.ViewBag.ReportType, Is.EqualTo("LobbyistEmployerReport"));
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_ReturnsNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        _controller.ModelState.AddModelError("error", "Invalid model");

        var model = new LobbyingReportVerificationViewModel
        {
            DiligenceStatementVerification = true,
            Id = 1,
            FilerId = 1
        };

        // Act
        var result = await _controller.SubmitLobbyistEmployerReport("LobbyistEmployerReport", model, mockFilingsApi.Object, CancellationToken.None);

        // Assert
        var notFoundResult = result as NotFoundResult;
        Assert.That(notFoundResult, Is.Not.Null);
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_CatchesException_WhenApiCallFails()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            FilerId = 456,
            DiligenceStatementVerification = true,
        };

        var exception = new ArgumentException("Test exception");

        _localizerMock.Setup(l => l[ResourceConstants.UpdateFailMessage]).Returns(new LocalizedString("key", "Update failed"));

        mockFilingsApi.Setup(api => api.SubmitLobbyistEmployerReport(It.IsAny<long>(), It.IsAny<SubmitLobbyistEmployerReportDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _controller.SubmitLobbyistEmployerReport("LobbyistEmployerReport", model, mockFilingsApi.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("Verification"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });

        _loggerMock.Verify(log => log.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((obj, _) => obj.ToString()!.Contains("Submit lobbyist employer report failed")),
            It.Is<Exception>(ex => ex == exception),
            It.Is<Func<It.IsAnyType, Exception?, string>>((func, _) => func != null)
        ), Times.Once);
    }

    [Test]
    public async Task SubmitReport48H_ShouldReturnSubmittedView_WhenResponseIsValid()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            FilerId = 100,
            DiligenceStatementVerification = true
        };

        var response = new SubmitLobbyingReportResponse(
            true,
            1,
            1,
            submittedDate: new DateTime(2024, 1, 1, 12, 0, 0, 0),
            true,
            validationErrors: []);

        mockFilingsApi.Setup(api => api.SubmitReport48H(
                model.Id,
                It.IsAny<SubmitLobbyistEmployerReportDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.SubmitReport48H("Report48H", model, mockFilingsApi.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Submitted"));


        // Check ViewBag values
        Assert.That(_controller.ViewBag.SubmittedDate, Is.EqualTo(response.SubmittedDate));
        Assert.That(_controller.ViewBag.ReportType, Is.EqualTo("Report48H"));
    }


    [Test]
    public async Task SubmitReport48H_ReturnsNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        _controller.ModelState.AddModelError("error", "Invalid model");

        var model = new LobbyingReportVerificationViewModel
        {
            DiligenceStatementVerification = true,
            Id = 1,
            FilerId = 1
        };

        // Act
        var result = await _controller.SubmitReport48H("Report48H", model, mockFilingsApi.Object, CancellationToken.None);

        // Assert
        var notFoundResult = result as NotFoundResult;
        Assert.That(notFoundResult, Is.Not.Null);
    }

    [Test]
    public async Task SubmitReport48H_SyncsValidationErrors_WhenResponseIsInvalid()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            FilerId = 123,
            DiligenceStatementVerification = true
        };

        var validationErrors = new List<WorkFlowError>();

        var response = new SubmitLobbyingReportResponse(
            valid: false,
            id: 1,
            submittedDate: null,
            validationErrors: validationErrors,
            diligenceStatementVerified: true,
            statusId: 1
        );

        mockFilingsApi.Setup(api => api.SubmitReport48H(model.Id, It.IsAny<SubmitLobbyistEmployerReportDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.SubmitReport48H("Report48H", model, mockFilingsApi.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult, Is.Not.Null);
            Assert.That(viewResult!.ViewName, Is.EqualTo("Verification"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });

        Assert.Multiple(() =>
        {
            Assert.That(_controller.ModelState.ErrorCount, Is.EqualTo(validationErrors.Count));
        });
    }


    [Test]
    public async Task SubmitReport48H_CatchesException_WhenApiCallFails()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new LobbyingReportVerificationViewModel
        {
            Id = 1,
            FilerId = 456,
            DiligenceStatementVerification = true,
        };

        var exception = new ArgumentException("Test exception");

        _localizerMock.Setup(l => l[ResourceConstants.UpdateFailMessage]).Returns(new LocalizedString("key", "Update failed"));

        mockFilingsApi.Setup(api => api.SubmitReport48H(It.IsAny<long>(), It.IsAny<SubmitLobbyistEmployerReportDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act
        var result = await _controller.SubmitReport48H("Report48H", model, mockFilingsApi.Object, CancellationToken.None);

        // Assert
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("Verification"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });

        _loggerMock.Verify(log => log.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((obj, _) => obj.ToString()!.Contains("Submit 48H report failed")),
            It.Is<Exception>(ex => ex == exception),
            It.Is<Func<It.IsAnyType, Exception?, string>>((func, _) => func != null)
        ), Times.Once);
    }

    [Test]
    public async Task CreateLobbyistEmployerReport_ModelStateInvalid_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("FilerId", "Required field");
        var model = new LobbyistEmployerReportViewModel
        {
            FilerId = 0,
            FilingPeriodId = 0
        };
        var mockFilingsApi = new Mock<IFilingsApi>();

        // Act
        var result = await _controller.LobbyistEmployer(model, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CreateLobbyistReport_ModelStateInvalid_ShouldReturnNotFound()
    {
        // Arrange
        _controller.ModelState.AddModelError("FilerId", "Required field");
        var model = new LobbyistReportViewModel
        {
            FilerId = 0,
            FilingPeriodId = 0
        };
        var mockFilingsApi = new Mock<IFilingsApi>();

        // Act
        var result = await _controller.Lobbyist(model, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task Lobbyist_ShouldReturnViewWithModel_WhenLobbyistRegistrationExists()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        var registration = new RegistrationItemResponse(
            approvedAt: DateNow,
            filerId: 12345,
            id: 67890,
            name: "Test Lobbyist",
            statusId: 1,
            type: "Lobbyist"
        );

        var filingPeriods = new List<FilingPeriodDto>
        {
            new(id: 1, startDate: DateNow.AddMonths(-3), endDate: DateNow, hasFiling: true, isRemoved: false),
            new(id: 2, startDate: DateNow, endDate: DateNow.AddMonths(3), hasFiling: false, isRemoved: true)
        };

        _ = mockRegistrationsApi
            .Setup(x => x.GetLobbyistEntityRegistration(It.IsAny<CancellationToken>()))
            .ReturnsAsync(registration);

        _ = mockFilingsApi
            .Setup(x => x.GetAllFilingPeriodsForFiler(
                12345,
                FilingTypeModel.LobbyistReport.Id,
                1,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(filingPeriods);

        // Act
        var result = _controller.Lobbyist(mockRegistrationsApi.Object, mockFilingsApi.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var model = result!.Model as LobbyistReportViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(model!.FilerId, Is.EqualTo(0));
        });
    }

    [Test]
    public async Task Lobbyist_ShouldReturnNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = _controller.Lobbyist(mockRegistrationsApi.Object, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task LobbyistEmployer_ShouldReturnViewWithModel_WhenLobbyistEmployerRegistrationExists()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        var registration = new RegistrationItemResponse(
            approvedAt: DateNow,
            filerId: 12345,
            id: 67890,
            name: "Test Lobbyist Employer",
            statusId: 1,
            type: "LobbyistEmployer"
        );

        var filingPeriods = new List<FilingPeriodDto>
        {
            new(id: 1, startDate: DateNow.AddMonths(-3), endDate: DateNow, hasFiling: true, isRemoved: false),
            new(id: 2, startDate: DateNow, endDate: DateNow.AddMonths(3), hasFiling: false, isRemoved: false)
        };
        _ = mockRegistrationsApi
            .Setup(x => x.GetLobbyistEmployerEntityRegistration(It.IsAny<CancellationToken>()))
            .ReturnsAsync(registration);

        _ = mockFilingsApi
            .Setup(x => x.GetAllFilingPeriodsForFiler(
                12345,
                FilingTypeModel.LobbyistEmployerReport.Id,
                1,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(filingPeriods);

        // Act
        var result = _controller.LobbyistEmployer() as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var model = result!.Model as LobbyistEmployerReportViewModel;
        Assert.That(model, Is.Not.Null);
    }

    [Test]
    public void LobbyistEmployer_ShouldReturnNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = _controller.LobbyistEmployer();

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SendLobbyistReportForAttestation_InvalidGet_ShouldReturnNotFound()
    {
        // Arrange
        var reportType = "LobbyistReport";
        var mockFilingsApi = new Mock<IFilingsApi>();
        var mockFilersApi = new Mock<IFilersApi>();
        var model = new VerificationFilingReportViewModel()
        {
            FilerId = 123,
            FilingId = 456,
            FilerName = "Sample Name",
        };

        _controller.ModelState.AddModelError("Error", "Invalid model state");

        // Act
        var result = await _controller.SendLobbyistReportForAttestation(model, mockFilingsApi.Object, mockFilersApi.Object);

        // Assert
        Assert.That(result, Is.Not.Null, "Expected NotFoundResult when ModelState is invalid.");
    }

    [Test]
    public async Task SendLobbyistReportForAttestation_ShouldRedirectToDashboard()
    {
        // Arrange
        var reportType = "LobbyistReport";
        var filerName = "Sample Name";
        var model = new VerificationFilingReportViewModel()
        {
            FilerId = 123,
            FilingId = 456,
            FilerName = filerName
        };

        var mockFilersApi = new Mock<IFilersApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();
        var workflowErrors = new List<WorkFlowError>();
        var expectedResponse = new SubmitLobbyingReportResponse(true, 1, 1, DateNow, true, workflowErrors);
        var registrationResponse = new RegistrationResponse(DateNow, 1, 1, filerName, 1, "type");

        _ = mockFilingsApi.Setup(api => api.SendForAttestationLobbyistReport(
                It.IsAny<long>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _ = mockFilersApi.Setup(api => api.GetCurrentRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(registrationResponse);

        // Mock the success message localization
        _ = _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage", "Success message"));

        // Act
        var result = await _controller.SendLobbyistReportForAttestation(model, mockFilingsApi.Object, mockFilersApi.Object);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("TemporaryDashboard"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Filing"));
        });

        // Verify the toast service was called with success
        _toastService.Verify(ts => ts.Success(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }

    [Test]
    public async Task SendLobbyistReportForAttestation_CatchesException_WhenApiCallFails()
    {
        // Arrange
        var reportType = "LobbyistReport";
        var filerName = "Sample Name";
        var model = new VerificationFilingReportViewModel()
        {
            FilerId = 123,
            FilingId = 456,
            FilerName = filerName
        };

        var registrationResponse = new RegistrationResponse(DateNow, 1, 1, filerName, 1, "type");
        var mockFilersApi = new Mock<IFilersApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        var exception = new ArgumentException("Test exception");

        _ = mockFilingsApi.Setup(api => api.SendForAttestationLobbyistReport(
                It.IsAny<long>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        _ = mockFilersApi.Setup(api => api.GetCurrentRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(registrationResponse);

        // Act
        var result =
            await _controller.SendLobbyistReportForAttestation(model, mockFilingsApi.Object, mockFilersApi.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(viewResult!.ViewName, Is.EqualTo("SendForAttestation"));
            Assert.That(viewResult.Model, Is.EqualTo(model));
        });

        _loggerMock.Verify(log => log.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((obj, _) =>
                obj.ToString()!.Contains("Failed sending a lobbyist report for attestation.")),
            It.Is<Exception>(ex => ex == exception),
            It.Is<Func<It.IsAnyType, Exception?, string>>((func, _) => func != null)
        ), Times.Once);

        // Verify the toast service was called with success
        _toastService.Verify(ts => ts.Error(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }

    [Test]
    public async Task SendLobbyistReportForAttestation_DecisionBuild_WhenApiReturnDecisions()
    {
        // Arrange
        var filerName = "Sample Name";
        var model = new VerificationFilingReportViewModel()
        {
            FilerId = 123,
            FilingId = 456,
            FilerName = filerName
        };

        var registrationResponse = new RegistrationResponse(DateNow, 1, 1, filerName, 1, "type");
        var mockFilersApi = new Mock<IFilersApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        var exception = new ArgumentException("Test exception");
        var workflowErrors = new List<WorkFlowError>()
        {
            new WorkFlowError(errorCode: "test", errorType: "test", fieldName: "foo", message: "bar")
        };

        var expectedResponse = new SubmitLobbyingReportResponse(true, 1, 1, DateNow, false, workflowErrors);

        _ = mockFilingsApi.Setup(api => api.SendForAttestationLobbyistReport(
                It.IsAny<long>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _ = mockFilersApi.Setup(api => api.GetCurrentRegistration(It.IsAny<long>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(registrationResponse);

        // Act
        var result =
            await _controller.SendLobbyistReportForAttestation(model, mockFilingsApi.Object, mockFilersApi.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
    }

    [Test]
    public async Task Report48H_ValidModel_ReturnsViewWithModel()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        var registration = new RegistrationItemResponse(
            approvedAt: DateNow,
            filerId: 12345,
            id: 67890,
            name: "Test Registration",
            statusId: 1,
            type: "LobbyistEmployer"
        );

        mockRegistrationsApi
            .Setup(x => x.GetLobbyistEmployerEntityRegistration(It.IsAny<CancellationToken>()))
            .ReturnsAsync(registration);

        // Act
        var result = await _controller.Report48H(mockRegistrationsApi.Object, mockFilingsApi.Object) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        var model = result!.Model as Report48HViewModel;
        Assert.That(model, Is.Not.Null);
        Assert.That(model!.FilerId, Is.EqualTo(12345));
    }

    [Test]
    public async Task CreateReport48H_ValidModel_RedirectsToIndex()
    {
        // Arrange
        var model = new Report48HViewModel
        {
            Id = 1,
            FilerId = 456
        };

        var mockFilingsApi = new Mock<IFilingsApi>();

        var response = new FilingResponseDto(
            endDate: DateNow,
            filerId: 456,
            filerName: "Jane Lobbyist",
            filingStatus: "Pending",
            filingType: "Report48H",
            id: 67890,
            parentId: null,
            startDate: DateNow.AddMonths(-1),
            status: 1,
            submittedDate: null,
            version: null
        );

        mockFilingsApi
            .Setup(f => f.CreateReport48H(model.FilerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateReport48H(model, mockFilingsApi.Object) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.RouteValues!["filingId"], Is.EqualTo(67890));
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(456));
        });
    }

    [Test]
    public async Task Report48H_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        var mockRegistrationsApi = new Mock<IRegistrationsApi>();
        var mockFilingsApi = new Mock<IFilingsApi>();

        _controller.ModelState.AddModelError("SomeKey", "Some error");

        // Act
        var result = await _controller.Report48H(mockRegistrationsApi.Object, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CreateReport48H_ModelStateInvalid_ReturnsNotFound()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();

        var model = new Report48HViewModel
        {
            Id = 1,
            FilerId = 456
        };

        _controller.ModelState.AddModelError("SomeKey", "Some error");

        // Act
        var result = await _controller.CreateReport48H(model, mockFilingsApi.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task CreateFilingAmendment_WithValidParameters_ReturnsRedirectToActionResult()
    {
        // Arrange
        var parentId = 12345;
        var expectedFilerId = 67890;
        var expectedFilingId = 54321;
        var reportType = FilingType.LobbyistReport.Name;

        var response = new FilingResponseDto
        (
            endDate: DateNow,
            filerId: expectedFilerId,
            filerName: "Tom",
            filingStatus: FilingStatus.Draft,
            filingType: reportType,
            id: expectedFilingId,
            parentId: parentId,
            startDate: DateNow,
            status: 1,
            submittedDate: DateNow,
            version: 1
        );

        _filingsApiMock.Setup(x => x.CreateFilingAmendment(
                parentId,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        // Act
        var result = await _controller.CreateFilingAmendment(parentId.ToString(CultureInfo.InvariantCulture), reportType, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirectResult = result as RedirectToActionResult;

        Assert.That(redirectResult, Is.Not.Null);
        Assert.That(redirectResult.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirectResult.ActionName, Is.EqualTo("Index"));
            Assert.That(redirectResult.ControllerName, Is.EqualTo("AmendDisclosure"));

            Assert.That(redirectResult.RouteValues["filerId"], Is.EqualTo(expectedFilerId));
            Assert.That(redirectResult.RouteValues["filingId"], Is.EqualTo(expectedFilingId));
            Assert.That(redirectResult.RouteValues["reportType"], Is.EqualTo(reportType));
        });

        _filingsApiMock.Verify(x => x.CreateFilingAmendment(
            parentId,
            It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Test]
    public async Task SendForAttestationReport72H_WhenResponseIsValid_ShouldRedirectToIndex()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReport72H(It.IsAny<long>(), It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidatedReport72H(false, 1, 1, DateNow, true, []));
        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationSuccessMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationSuccessMessage, "Success"));

        // Act
        var result = await _controller.SendForAttestationReport72H(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
        });
    }

    [Test]
    public async Task SendForAttestationReport72H_InvalidResponse_ReturnsViewResult()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReport72H(It.IsAny<long>(), It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidatedReport72H(false, 1, 1, DateNow, false, new List<WorkFlowError> { new("123", "123", "123", "Invalid") }));

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationFailMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationFailMessage, "Fail"));

        _ = _filingSvc.Setup(x => x.GetResponsibleOfficers(1)).ReturnsAsync(new List<ReponsibleOfficerDto> {
                new()
                {
                    LastName = "LN",
                    FirstName = "FN",
                    Id = 1,
                    Title = "Admin",
                    Role = "Admin"
                }
            });

        // Act
        var result = await _controller.SendForAttestationReport72H(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var view = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(view?.Model, Is.InstanceOf<VerificationFilingReportViewModel>());
            Assert.That(view?.ViewName, Is.EqualTo("SendForAttestation"));
        });
    }

    [Test]
    public async Task SendForAttestationReport72H_ThrowsException_ReturnsViewResult()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReport72H(It.IsAny<long>(), It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Unexpected error"));

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationFailMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationFailMessage, "Fail"));

        _ = _filingSvc.Setup(x => x.GetResponsibleOfficers(1)).ReturnsAsync(new List<ReponsibleOfficerDto> {
                new()
                {
                    LastName = "LN",
                    FirstName = "FN",
                    Id = 1,
                    Title = "Admin",
                    Role = "Admin"
                }
            });

        // Act
        var result = await _controller.SendForAttestationReport72H(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var view = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(view?.Model, Is.InstanceOf<VerificationFilingReportViewModel>());
            Assert.That(view?.ViewName, Is.EqualTo("SendForAttestation"));
        });
    }

    [Test]
    public async Task SendForAttestationReport72H_ShouldRedirect_WhenResponseIsValid()
    {
        // Arrange
        var filingId = 123L;
        var selectedOfficerIds = new List<long> { 1, 2 };

        var model = new VerificationFilingReportViewModel
        {
            FilerId = 1,
            FilingId = filingId,
            SelectedResponsibleOfficerIds = selectedOfficerIds,
            ReportType = "TestReport"
        };

        var apiResponse = new ValidatedReport72H(false, 1, 1, DateNow, true, []);

        _filingsApiMock.Setup(x =>
            x.SendForAttestationReport72H(filingId, selectedOfficerIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationSuccessMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationSuccessMessage, "Sent successfully"));

        // Act
        var result = await _controller.SendForAttestationReport72H(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
        });
    }

    [Test]
    public async Task SendForAttestationReport72H_ReturnsNotFound_WhenModelStateIsInvalidOrOfficersNotSelected()
    {
        // Arrange
        var mockFilingsApi = Substitute.For<IFilingsApi>();
        var model = new VerificationFilingReportViewModel
        {
            FilerId = 1,
            FilingId = 123,
            SelectedResponsibleOfficerIds = null
        };

        // Simulate invalid model state
        _controller.ModelState.AddModelError("SomeField", "Some error");

        // Act
        var result = await _controller.SendForAttestationReport72H(model, mockFilingsApi);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task SendForAttestationReport48H_WhenResponseIsValid_ShouldRedirectToIndex()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReport48H(It.IsAny<long>(), It.IsAny<Report48HSendForAttestationRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new SubmitLobbyingReportResponse(false, 1, 1, DateNow, true, []));
        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationSuccessMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationSuccessMessage, "Success"));

        // Act
        var result = await _controller.SendForAttestationReport48H(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
        });
    }

    [Test]
    public async Task SendForAttestationReport48H_InvalidResponse_ReturnsViewResult_WithErrorMessageAndResponsibleOfficers()
    {
        // Arrange
        var filingId = 1L;
        var responsibleOfficerId = 1L;
        var reportType = "48H";
        var validationErrors = new List<WorkFlowError>
        {
            new("123", "123", "123", "Invalid")
        };

        var apiResponse = new SubmitLobbyingReportResponse(false, filingId, 1, DateNow, false, validationErrors);


        var mockRequest = new Report48HSendForAttestationRequest(new List<long> { responsibleOfficerId });

        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock
            .Setup(f => f.SendForAttestationReport48H(filingId, It.Is<Report48HSendForAttestationRequest>(r =>
                r.SelectedResponsibleOfficerIds.SequenceEqual(mockRequest.SelectedResponsibleOfficerIds)), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationFailMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationFailMessage, "Fail"));

        var mockOfficers = new List<ReponsibleOfficerDto>
    {
        new()
        {
            LastName = "LN",
            FirstName = "FN",
            Id = responsibleOfficerId,
            Title = "Admin",
            Role = "Admin"
        }
    };

        _filingSvc.Setup(x => x.GetResponsibleOfficers(filingId)).ReturnsAsync(mockOfficers);

        var model = new VerificationFilingReportViewModel
        {
            FilerId = 1,
            FilingId = filingId,
            SelectedResponsibleOfficerIds = new List<long> { responsibleOfficerId },
            ReportType = reportType
        };

        // Act
        var result = await _controller.SendForAttestationReport48H(model, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var view = result as ViewResult;

        Assert.Multiple(() =>
        {
            Assert.That(view?.ViewName, Is.EqualTo("SendForAttestation"));
            Assert.That(view?.Model, Is.InstanceOf<VerificationFilingReportViewModel>());

            var returnedModel = view?.Model as VerificationFilingReportViewModel;
            Assert.That(returnedModel?.ResponsibleOfficers, Is.Not.Null);
            Assert.That(returnedModel?.ResponsibleOfficers?.Count(), Is.EqualTo(mockOfficers.Count));
            Assert.That(returnedModel?.ResponsibleOfficers?.First().LastName, Is.EqualTo("LN"));
            Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(reportType));

            Assert.That(returnedModel?.Messages.Validations.Count, Is.EqualTo(1));
        });

        _toastService.Verify(ts => ts.Error(It.IsAny<string>(), true, "Right", "Bottom", 0), Times.AtLeastOnce);
    }


    [Test]
    public async Task SendForAttestationReport48H_ThrowsException_ReturnsViewResult()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReport48H(It.IsAny<long>(), new Report48HSendForAttestationRequest(new List<long>()), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Unexpected error"));

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationFailMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationFailMessage, "Fail"));

        _ = _filingSvc.Setup(x => x.GetResponsibleOfficers(1)).ReturnsAsync(new List<ReponsibleOfficerDto> {
                new()
                {
                    LastName = "LN",
                    FirstName = "FN",
                    Id = 1,
                    Title = "Admin",
                    Role = "Admin"
                }
            });

        // Act
        var result = await _controller.SendForAttestationReport48H(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var view = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(view?.Model, Is.InstanceOf<VerificationFilingReportViewModel>());
            Assert.That(view?.ViewName, Is.EqualTo("SendForAttestation"));
        });
    }

    [Test]
    public async Task SendForAttestationReport48H_ShouldRedirect_WhenResponseIsValid()
    {
        // Arrange
        var filingId = 123L;
        var selectedOfficerIds = new List<long> { 1, 2 };

        var model = new VerificationFilingReportViewModel
        {
            FilerId = 1,
            FilingId = filingId,
            SelectedResponsibleOfficerIds = selectedOfficerIds,
            ReportType = "TestReport"
        };

        var apiResponse = new SubmitLobbyingReportResponse(false, 1, 1, DateNow, true, []);

        _filingsApiMock.Setup(x =>
            x.SendForAttestationReport48H(It.IsAny<long>(), It.IsAny<Report48HSendForAttestationRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationSuccessMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationSuccessMessage, "Sent successfully"));

        // Act
        var result = await _controller.SendForAttestationReport48H(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
        });
    }

    [Test]
    public async Task SendForAttestationReport48H_ReturnsNotFound_WhenModelStateIsInvalid()
    {
        // Arrange
        var mockFilingsApi = Substitute.For<IFilingsApi>();
        var model = new VerificationFilingReportViewModel
        {
            FilerId = 1,
            FilingId = 123,
            SelectedResponsibleOfficerIds = null
        };

        // Simulate invalid model state
        _controller.ModelState.AddModelError("SomeField", "Some error");

        // Act
        var result = await _controller.SendForAttestationReport48H(model, mockFilingsApi);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task Submit72hReport_WhenResponseIsValid_ShouldReturnSummaryPage()
    {
        // Arrange
        _filingsApiMock.Setup(f => f.AttestReport72H(It.IsAny<long>(), It.IsAny<SubmitFilingReportDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidateReport72HSubmissionResponseDto(1, 1, DateNow, true, new List<WorkFlowError>()));

        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.SubmitReportSuccessMessage", "Success message"));

        Report72HViewModel model = new() { Id = 1, DiligenceStatementVerification = true };

        // Act
        var result = await _controller.Submit72hReport(model, _filingsApiMock.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Submitted"));

        // Check ViewBag values
        Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(FilingTypeModel.Report72h.Name));
    }

    [Test]
    public async Task Submit72hReport_WhenResponseIsValid_False_ShouldReturnVerificationPage()
    {
        // Arrange
        var mockResponse = new ValidateReport72HSubmissionResponseDto(
            1,
            1,
            DateNow,
            false,
            new List<WorkFlowError>()
            {
                new ( "Amount", "Err001", "Validation", "Amount is required" )
            });

        _filingsApiMock.Setup(f => f.AttestReport72H(It.IsAny<long>(), It.IsAny<SubmitFilingReportDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse);

        _localizerMock.Setup(l => l["FilerPortal.Disclosure.Dashboard.SubmitReportErrorMessage"])
            .Returns(new LocalizedString("FilerPortal.Disclosure.Dashboard.SubmitReportErrorMessage", "Success message"));

        Report72HViewModel model = new() { Id = 1, DiligenceStatementVerification = true };

        // Act
        var result = await _controller.Submit72hReport(model, _filingsApiMock.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Verification"));

        // Check ViewBag values
        Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(FilingTypeModel.Report72h.Name));
    }

    [Test]
    public async Task Submit72hReport_WhenThrowException_ShouldReturnVerificationPage()
    {
        // Arrange
        var exception = new ArgumentException("Test exception");

        _filingsApiMock.Setup(f => f.AttestReport72H(It.IsAny<long>(), It.IsAny<SubmitFilingReportDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        _localizerMock.Setup(l => l["Common.APIRequestError"])
            .Returns(new LocalizedString("Common.APIRequestError", "Fail message"));

        Report72HViewModel model = new() { Id = 1, DiligenceStatementVerification = true };

        // Act
        var result = await _controller.Submit72hReport(model, _filingsApiMock.Object);

        // Assert
        var viewResult = result as ViewResult;
        Assert.That(viewResult, Is.Not.Null);
        Assert.That(viewResult.ViewName, Is.EqualTo("Verification"));

        // Check ViewBag values
        Assert.That(_controller.ViewBag.ReportType, Is.EqualTo(FilingTypeModel.Report72h.Name));
    }

    [Test]
    public async Task Submit72hReport_WhenModalStateIsInvalid_ShouldReturnNotFound()
    {
        // Arrange
        var mockModel = new Report72HViewModel { Id = 1, DiligenceStatementVerification = true };
        _controller.ModelState.AddModelError("Id", "Required");

        // Act
        var result = await _controller.Submit72hReport(mockModel, _filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<NotFoundResult>());
    }

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_WhenResponseIsValid_ShouldRedirectToIndex()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReportLobbyistEmployer(It.IsAny<long>(), It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidatedLobbyistEmployerReport(false, 1, 1, DateNow, true, []));
        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationSuccessMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationSuccessMessage, "Success"));

        // Act
        var result = await _controller.SendForAttestationReportLobbyistEmployer(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<RedirectToActionResult>());
        var redirect = (RedirectToActionResult)result;
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
        });
    }

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_InvalidResponse_ReturnsViewResult()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReportLobbyistEmployer(It.IsAny<long>(), It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidatedLobbyistEmployerReport(false, 1, 1, DateNow, false, new List<WorkFlowError> { new("123", "123", "123", "Invalid") }));

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationFailMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationFailMessage, "Fail"));

        _ = _filingSvc.Setup(x => x.GetResponsibleOfficers(1)).ReturnsAsync(new List<ReponsibleOfficerDto> {
                new()
                {
                    LastName = "LN",
                    FirstName = "FN",
                    Id = 1,
                    Title = "Admin",
                    Role = "Admin"
                }
            });

        // Act
        var result = await _controller.SendForAttestationReportLobbyistEmployer(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var view = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(view?.Model, Is.InstanceOf<VerificationFilingReportViewModel>());
            Assert.That(view?.ViewName, Is.EqualTo("SendForAttestation"));
        });
    }

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_ThrowsException_ReturnsViewResult()
    {
        // Arrange
        var filingsApiMock = new Mock<IFilingsApi>();
        filingsApiMock.Setup(f => f.SendForAttestationReportLobbyistEmployer(It.IsAny<long>(), It.IsAny<List<long>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Unexpected error"));

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationFailMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationFailMessage, "Fail"));

        _ = _filingSvc.Setup(x => x.GetResponsibleOfficers(1)).ReturnsAsync(new List<ReponsibleOfficerDto> {
                new()
                {
                    LastName = "LN",
                    FirstName = "FN",
                    Id = 1,
                    Title = "Admin",
                    Role = "Admin"
                }
            });

        // Act
        var result = await _controller.SendForAttestationReportLobbyistEmployer(new VerificationFilingReportViewModel()
        {
            FilerId = 1,
            FilingId = 1,
            SelectedResponsibleOfficerIds = new List<long>() { 1 }
        }, filingsApiMock.Object);

        // Assert
        Assert.That(result, Is.InstanceOf<ViewResult>());
        var view = result as ViewResult;
        Assert.Multiple(() =>
        {
            Assert.That(view?.Model, Is.InstanceOf<VerificationFilingReportViewModel>());
            Assert.That(view?.ViewName, Is.EqualTo("SendForAttestation"));
        });
    }

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_ShouldRedirect_WhenResponseIsValid()
    {
        // Arrange
        var filingId = 123L;
        var selectedOfficerIds = new List<long> { 1, 2 };

        var model = new VerificationFilingReportViewModel
        {
            FilerId = 1,
            FilingId = filingId,
            SelectedResponsibleOfficerIds = selectedOfficerIds,
            ReportType = "TestReport"
        };

        var apiResponse = new ValidatedLobbyistEmployerReport(false, 1, 1, DateNow, true, []);

        _filingsApiMock.Setup(x =>
            x.SendForAttestationReportLobbyistEmployer(filingId, selectedOfficerIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(apiResponse);

        _localizerMock.Setup(l => l[ResourceConstants.SendForAttestationSuccessMessage])
            .Returns(new LocalizedString(ResourceConstants.SendForAttestationSuccessMessage, "Sent successfully"));

        // Act
        var result = await _controller.SendForAttestationReportLobbyistEmployer(model, _filingsApiMock.Object, CancellationToken.None);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("DisclosureTemporaryDashboard"));
        });
    }

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_ReturnsNotFound_WhenModelStateIsInvalidOrOfficersNotSelected()
    {
        // Arrange
        var mockFilingsApi = Substitute.For<IFilingsApi>();
        var model = new VerificationFilingReportViewModel
        {
            FilerId = 1,
            FilingId = 123,
            SelectedResponsibleOfficerIds = null
        };

        // Simulate invalid model state
        _controller.ModelState.AddModelError("SomeField", "Some error");

        // Act
        var result = await _controller.SendForAttestationReportLobbyistEmployer(model, mockFilingsApi);

        // Assert
        Assert.That(result, Is.TypeOf<NotFoundResult>());
    }

    [Test]
    public async Task AmendLobbyistEmployerReport_ModelStateInvalid_ReturnsRedirectWithError()
    {
        // Arrange
        _controller.ModelState.AddModelError("FilerId", "Required");
        var model = new LobbyistEmployerReportViewModel();
        var mockFilingsApi = new Mock<IFilingsApi>();

        _localizerMock.Setup(l => l["Common.APIRequestError"])
            .Returns(new LocalizedString("Common.APIRequestError", "Something went wrong"));

        // Act
        var result = await _controller.AmendLobbyistEmployerReport(model, mockFilingsApi.Object);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Disclosure"));
        });

        _toastService.Verify(t => t.Error("Something went wrong", true, "Right", "Bottom", 0), Times.Once);
    }

    [Test]
    public async Task AmendLobbyistEmployerReport_ValidModel_ShouldRedirectToAmendDisclosure()
    {
        // Arrange
        var mockFilingsApi = new Mock<IFilingsApi>();
        var model = new LobbyistEmployerReportViewModel
        {
            FilerId = 123,
            FilingPeriodId = 456
        };

        var now = DateTime.Now;
        var expectedFiling = new FilingItemResponse(
            amendmentExplanation: "",
            endDate: now,
            filerId: model.FilerId,
            filerName: "Test Filer",
            filingPeriodId: model.FilingPeriodId,
            filingType: "LobbyistEmployerReport",
            filingStatus: "Draft",
            filingTypeId: 1,
            id: 999,
            legislativeSessionId: null,
            parentId: null,
            startDate: now,
            status: 1,
            submittedDate: null,
            totalPaymentsPucActivity: null,
            version: 1
        );

        mockFilingsApi
            .Setup(x => x.CreateLobbyistEmployerReportFiling(
                model.FilerId,
                It.IsAny<CreateLobbyistEmployerReportDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedFiling);

        // Act
        var result = await _controller.AmendLobbyistEmployerReport(model, mockFilingsApi.Object);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect!.ActionName, Is.EqualTo("Index"));
            Assert.That(redirect.ControllerName, Is.EqualTo("AmendDisclosure"));
            Assert.That(redirect.RouteValues, Is.Not.Null);

            Assert.That(redirect.RouteValues!["filerId"], Is.EqualTo(model.FilerId));
            Assert.That(redirect.RouteValues!["filingId"], Is.EqualTo(expectedFiling.Id));
            Assert.That(redirect.RouteValues!["reportType"], Is.EqualTo(FilingTypeModel.LobbyistEmployerReport.Name));
        });
    }
}
