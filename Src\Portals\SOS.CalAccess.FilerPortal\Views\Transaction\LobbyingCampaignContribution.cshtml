@model LobbyingCampaignContributionViewModel
@using Microsoft.AspNetCore.Html
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.UI.Common.Services
@inject IHtmlLocalizer<SharedResources> Localizer
@using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
@using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;

@{
    ViewData[LayoutConstants.Title] = SharedLocalizer["FilerPortal.Transaction.LobbyingCampaignContribution.Title"].Value;
}

<partial name="_CampaignContributionsHeader" model='new {Title = @Localizer[ResourceConstants.CampaignContributionsAddTitle].Value}' />

<div class="col-md-6">
    @using (Html.BeginForm("CreateLobbyistEmployerCoalitionLobbyingCampaignContribution", "Transaction", FormMethod.Post))
    {
        @Html.HiddenFor(m => m.FilerId, Model.FilerId)
        @Html.HiddenFor(m => m.FilingId, Model.FilingId)

        <div class="mb-3 lobbying-form-wrapper">
            @HtmlHelpers.CampaignContributionsTransactionDateInput(Html, Localizer, Localizer[ResourceConstants.CcNewTransactionDate].Value)
            @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "TransactionDate", Localizer[ResourceConstants.CcNewTransactionDate].Value)
        </div>

        <div class="mb-3 lobbying-form-wrapper">
            @Html.Label("IsCommittee", Localizer[ResourceConstants.CcNewIsRecipientCommittee].Value, new { @class = "form-label" })
            <div class="lobbying-radio-wrapper">
                @Html.RadioButtonFor(m => m.IsRecipientCommittee, true, new { @id = "committeeYes" }) Yes
                @Html.RadioButtonFor(m => m.IsRecipientCommittee, false, new { @id = "committeeNo" }) No
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "IsRecipientCommittee", Localizer[ResourceConstants.CcNewIsRecipientCommittee].Value)
            </div>
        </div>

        <div class="mb-3 lobbying-form-wrapper" id="recipientNameContainer" style="display: none;">
            @HtmlHelpers.CampaignContributionsTextBox(Html, Localizer, ResourceConstants.CcNewNonCommitteeRecipentName, "NonCommitteeRecipientName")
            @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "NonCommitteeRecipientName", Localizer[ResourceConstants.CcNewNonCommitteeRecipentName].Value)
        </div>

        <div class="mb-3 lobbying-form-wrapper" id="committeeDropdownContainer" style="display: none;">
            @Html.LabelFor(m => m.RecipientCommitteeFilerId, Localizer[ResourceConstants.CcNewRecipientCommitteeFilerId].Value, new { @class = "form-label" })
            <partial name="_AutoComplete" model="@Model.RecipientCommitteeFilerAutoCompleteModel" />
            @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "CommitteeId", Localizer[ResourceConstants.CcNewRecipientCommitteeFilerId].Value)
        </div>

        <div class="mb-3 lobbying-form-wrapper">
            @Html.LabelFor(m => m.Amount, Localizer[ResourceConstants.CcNewTransactionAmount].Value, new { @class = "form-label" })
            @Html.ValidationMessageFor(m => m.Amount, null, new { @class = "text-danger" })
            @Html.EJS().NumericTextBox("Amount").Format("c2").Value(Model.Amount).FloatLabelType(Syncfusion.EJ2.Inputs.FloatLabelType.Auto).ShowSpinButton(false).Render()
            @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "Amount", Localizer[ResourceConstants.CcNewTransactionAmount].Value)
        </div>

        <partial name="_CampaignContributionsButtons" />
    }
</div>

@{
    var cancelModal = new CancelConfirmModal(
        Title: "Confirm Cancellation",
        Body: "Are you sure you want to cancel? Any unsaved changes will be lost.",
        CloseButtonText: "No, Stay Here",
        SubmitButtonText: "Yes, Cancel",
        ActionUrl: Url.Action("CancelTransaction", "Transaction", new { filerId = Model.FilerId, filingId = Model.FilingId, viewName = FilingSummaryTypeModel.CampaignContributionSummary.Name, reportType = FilingTypeModel.LobbyistEmployerReport.Name })!
    );
}
<partial name="_CancelConfirmModal" model="cancelModal" />

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const committeeYes = document.getElementById("committeeYes");
        const committeeNo = document.getElementById("committeeNo");
        const recipientNameContainer = document.getElementById("recipientNameContainer");
        const committeeDropdownContainer = document.getElementById("committeeDropdownContainer");

        function toggleFields() {
            recipientNameContainer.style.display = committeeNo.checked ? "block" : "none";
            committeeDropdownContainer.style.display = committeeYes.checked ? "block" : "none";
        }

        toggleFields();
        committeeYes.addEventListener("change", toggleFields);
        committeeNo.addEventListener("change", toggleFields);
    });
</script>
