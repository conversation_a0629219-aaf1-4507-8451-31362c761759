using Microsoft.EntityFrameworkCore;
using NSubstitute;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Filings;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Filings;

[TestFixture]
[TestOf(typeof(FilingRepository))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class FilingRepositoryTests
{
    private FilingRepository _repository;
    private DatabaseContext _context;
    private IDateTimeSvc _dateTimeSvc;

    [SetUp]
    public async Task SetUp()
    {
        var factory = new DatabaseContextFactory();
        _context = await factory.CreateContext();
        _repository = new FilingRepository(_context);
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();

        // Clear any existing data first
        await ClearTestDataAsync();
    }

    [TearDown]
    public async Task TearDown()
    {
        await ClearTestDataAsync();
        await _context.DisposeAsync();
    }

    private async Task ClearTestDataAsync()
    {
        _context.FilingsTransactions.RemoveRange(_context.FilingsTransactions);
        _context.Filings.RemoveRange(_context.Filings);
        _context.FilingTypes.RemoveRange(_context.FilingTypes);
        _context.Users.RemoveRange(_context.Users);
        await _context.SaveChangesAsync();
    }

    private async Task SeedTestDataAsync()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var filingTypes = new List<FilingType>()
        {
            FilingType.OfficeHolderCandidateSupplement,
            FilingType.OfficeHolderCandidateShortForm,
            FilingType.SlateMailerOrganization,
        };

        // Create User
        var user = new User()
        {
            Id = 1,
            FirstName = "John",
            LastName = "Wick",
            EmailAddress = "<EMAIL>",
            AddressList = new(),
            PhoneNumberList = new(),
            EmailAddressList = new(),
            EntraOid = "TestOid"
        };

        var filing = new Filing()
        {
            Id = 1,
            StatusId = 1,
            FilerId = 1,
            FilingPeriodId = 1,
            FilingPeriod = new FilingPeriod
            {
                Id = 1,
                StartDate = date.AddDays(-30),
                EndDate = date,
            },
            FilingTypeId = 10,
            OriginalId = 10,
            Original = new Filing
            {
                Id = 10,
                StatusId = 2,
                FilerId = 2,
                FilingPeriodId = 3,
                FilingPeriod = new FilingPeriod
                {
                    Id = 3,
                    StartDate = date.AddDays(-30),
                    EndDate = date,
                },
                FilingTypeId = 10,
            },
            StartDate = date,
            EndDate = date.AddDays(30),
            //FilingRelatedFilers = new List<FilingRelatedFiler>{new() { FilerId = 400, Active = true, Filer = new Filer { Id = 400, FilerStatusId = FilerStatus.Active.Id } } },
            //Filer = new Filer{Id = 401, FilerStatusId = FilerStatus.Active.Id, CurrentRegistrationId = 500, CurrentRegistration = new CandidateIntentionStatement{ Id = 500, Name ="Sri", StatusId = RegistrationStatus.Submitted.Id } },
        };

        var formCandidateCampaign470S = new OfficeHolderCandidateSupplementForm()
        {
            Id = 100,
            StatusId = FilingStatus.Pending.Id,
            FilerId = 26,
            Filer = new Filer { Id = 26 },
            FilingPeriodId = 12,
            FilingPeriod = new FilingPeriod
            {
                Id = 12,
                StartDate = date.AddDays(-35),
                EndDate = date,
            },
            FilingTypeId = FilingType.OfficeHolderCandidateSupplement.Id,
            StartDate = date,
            EndDate = date.AddDays(30),
        };

        var formCandidateCampaign470 = new OfficeHolderCandidateShortForm()
        {
            Id = 111,
            StatusId = FilingStatus.Pending.Id,
            FilerId = 1,
            Filer = new Filer { Id = 1 },
            FilingPeriodId = 15,
            FilingPeriod = new FilingPeriod
            {
                Id = 15,
                StartDate = date.AddDays(-35),
                EndDate = date,
            },
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id,
            StartDate = date,
            EndDate = date.AddDays(30),
        };

        var smoCampaignStatements = new List<SmoCampaignStatement>
        {
            new ()
            {
                Id = 2,
                StatusId = FilingStatus.Accepted.Id,
                FilerId = 2,
                FilingPeriodId = 10,
                FilingPeriod = new FilingPeriod
                {
                    Id = 10,
                    StartDate = date.AddDays(-30),
                    EndDate = date.AddDays(-25),
                },
                FilingTypeId = FilingType.SlateMailerOrganization.Id,
                FilingSummaries = new List<FilingSummary>(),
                StartDate = date.AddDays(-30),
                EndDate = date,
            },
            new ()
            {
                Id = 3,
                StatusId = FilingStatus.Incomplete.Id,
                FilerId = 2,
                FilingPeriodId = 11,
                FilingPeriod = new FilingPeriod
                {
                    Id = 11,
                    StartDate = date.AddDays(-25),
                    EndDate = date.AddDays(-20),
                },
                FilingTypeId = FilingType.SlateMailerOrganization.Id,
                FilingSummaries = new List<FilingSummary>(),
                StartDate = date.AddDays(-25),
                EndDate = date.AddDays(-20),
            },
            new ()
            {
                Id = 4,
                StatusId = FilingStatus.Draft.Id,
                ParentId = 2,
                FilerId = 2,
                FilingPeriodId = 12,
                FilingPeriod = new FilingPeriod
                {
                    Id = 12,
                    StartDate = date.AddDays(-25),
                    EndDate = date.AddDays(-20),
                },
                FilingTypeId = FilingType.SlateMailerOrganization.Id,
                FilingSummaries = new List<FilingSummary>(),
                StartDate = date.AddDays(-25),
                EndDate = date.AddDays(-20),
            }
        };

        _dateTimeSvc.GetCurrentDateTime().Returns(date);

        // filings.Add(FilingWithRelatedFilers());
        await _context.Users.AddAsync(user);
        await _context.FilingTypes.AddRangeAsync(filingTypes);
        await _context.Filings.AddRangeAsync(smoCampaignStatements);
        //await _context.Filings.AddAsync(formCandidateCampaign470S);
        //await _context.Filings.AddAsync(formCandidateCampaign470);

        var entry = await _context.Filings.AddAsync(filing);

        // add AuditLog Entry
        var entryAction = new DataAction(entry, date);
        _context.AuditLogs.Add(
                new AuditLog(entryAction, _dateTimeSvc, user.Id)
            );

        await _context.SaveChangesAsync();
    }

    [Test]
    public async Task GetAll_ReturnsAllFilings()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var result = await _repository.GetAll();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count, Is.AtLeast(1));
    }

    [Test]
    public async Task GetCandidateCommitteesByFilingId_Returns_RelatedFilers()
    {
        DatabaseContext filingContext;
        FilingRepository filingRepository;
        DatabaseContextFactory filingFactory;

        filingFactory = new DatabaseContextFactory();
        filingContext = await filingFactory.CreateContext();
        filingRepository = new FilingRepository(filingContext);

        var filings = SeedFilingsTestDataAsync();

        await filingContext.Filings.AddRangeAsync(filings);
        await filingContext.SaveChangesAsync();

        var filing = await filingContext.Filings.FirstOrDefaultAsync();
        // Act
        var result = await filingRepository.GetCandidateCommitteesByFilingId(filing!.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.Not.Empty);

            var filingResult = result.SingleOrDefault();
            Assert.That(filingResult, Is.Not.Null);
            Assert.That(filingResult!.Id, Is.EqualTo(filing.Id));
            Assert.That(filingResult.FilingRelatedFilers, Is.Not.Null);
            Assert.That(filingResult.Filer, Is.Not.Null);
        });

        await filingContext.DisposeAsync();
    }

    private static List<Filing> SeedFilingsTestDataAsync()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var filingTypes = new List<FilingType>()
        {
            FilingType.OfficeHolderCandidateShortForm,
        };

        return new List<Filing>()
        {
            new ()
            {
                Id = 10,
                StatusId = 1,
                //FilerId = 1,
                FilingPeriodId = 11,
                FilingPeriod = new FilingPeriod
                {
                    Id = 11,
                    StartDate = date.AddDays(-30),
                    EndDate = date,
                },
                FilingRelatedFilers = new List<FilingRelatedFiler>{new() { FilerId = 400, Active = true, Filer = new Filer { Id = 400, FilerStatusId = FilerStatus.Active.Id } } },
                Filer = new Filer{Id = 401, FilerStatusId = FilerStatus.Active.Id, CurrentRegistrationId = 500, CurrentRegistration = new CandidateIntentionStatement{ Id = 500, Name ="Sri", StatusId = RegistrationStatus.Submitted.Id } },
            }
        };
    }

    [Test]
    public async Task GetAllByFilerId_ReturnsOnlyFilingsForGivenFilerId()
    {
        // Arrange
        await ClearTestDataAsync();
        long targetFilerId = 1;
        long otherFilerId = 2;

        // Add filings with targetFilerId (one normal, one cancelled)
        await _context.Filings.AddRangeAsync(new List<Filing>
        {
            new()
            {
                Id = 2,
                FilerId = targetFilerId,
                StatusId = 1, // Not cancelled
                FilingTypeId = 1,
                FilingSummaries = new List<FilingSummary>
                {
                    new() { Id = 1, FilingId = 2, PeriodAmount = 0, ToDateAmount = 0 }
                }
            },
            new()
            {
                Id = 3,
                FilerId = targetFilerId,
                StatusId = FilingStatus.Cancelled.Id, // Cancelled status
                FilingTypeId = 1
            }
        });

        // Add filing with different filer ID
        await _context.Filings.AddRangeAsync(new List<Filing>
        {
            new()
            {
                Id = 4,
                FilerId = otherFilerId,
                StatusId = 1,
                FilingTypeId = 1
            }
        });

        _ = await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAllByFilerId(targetFilerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(1));
            Assert.That(result.All(f => f.FilerId == targetFilerId), Is.True);
            Assert.That(result.All(f => f.StatusId != FilingStatus.Cancelled.Id), Is.True);
        });

        // Verify FilingSummaries were included
        var filing = result.First();
        Assert.That(filing.FilingSummaries, Is.Not.Null);
        Assert.That(filing.FilingSummaries, Has.Count.EqualTo(1));
    }

    [Test]
    public async Task FindSmoCampaignStatementById_Found_ReturnResult()
    {
        // Assign
        var id = 2;
        await SeedTestDataAsync();

        // Act
        var result = await _repository.FindSmoCampaignStatementById(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatement>());
            Assert.That(result?.Id, Is.EqualTo(id));
        });
    }

    [Test]
    public async Task FindSmoCampaignStatementById_NotFound_ReturnNull()
    {
        // Assign
        var id = 999;

        // Act
        var result = await _repository.FindSmoCampaignStatementById(id);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task IsExistingActiveSmoCampaignStatement_Exists_ReturnTrue()
    {
        // Assign
        var filerId = 2;
        var filingPeriodId = 10;
        await SeedTestDataAsync();

        // Act
        var result = await _repository.IsExistingActiveSmoCampaignStatement(filerId, filingPeriodId);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public async Task IsExistingActiveSmoCampaignStatement_NotExists_ReturnFalse()
    {
        // Assign
        var filerId = 2;
        var filingPeriodId = 3;

        // Act
        var result = await _repository.IsExistingActiveSmoCampaignStatement(filerId, filingPeriodId);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task FindPreviousSmoCampaignStatementsInCalendarYear_Found_ShouldReturnResult()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        // Assign
        var filerId = 2;
        var dateToCompare = date;

        // Seed data
        await SeedTestDataAsync();

        // Act
        var result = await _repository.FindPreviousSmoCampaignStatementsInCalendarYear(filerId, dateToCompare);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<SmoCampaignStatement>>());
        });
    }

    [Test]
    public async Task FindPreviousSmoCampaignStatementsInCalendarYear_NotFound_ShouldReturnEmpty()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        // Assign
        var filerId = 1;
        var dateToCompare = date;

        // Seed data
        await SeedTestDataAsync();

        // Act
        var result = await _repository.FindPreviousSmoCampaignStatementsInCalendarYear(filerId, dateToCompare);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Empty);
            Assert.That(result, Is.InstanceOf<List<SmoCampaignStatement>>());
        });
    }

    [Test]
    public async Task GetFilingReportsByFilerIds_ReturnsResult()
    {
        // Arrange
        await SeedTestDataAsync();
        var filerIds = new List<long> { 1, 2 };

        // Act
        var result = await _repository.GetFilingReportsByFilerIds(filerIds);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count, Is.AtLeast(1));
    }

    [Test]
    [Ignore("Need to revisit")]
    public async Task GetForm470SById_Returns_OfficeHolderCandidateSupplementForm()
    {
        await ClearTestDataAsync();
        await SeedTestDataAsync();

        var filingId = 100;
        // Act
        var result = await _repository.GetForm470SById(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);

    }

    [Test]
    [Ignore("Need to revisit")]
    public async Task GetForm470ById_Returns_OfficeHolderCandidateShortForm()
    {
        await ClearTestDataAsync();
        await SeedTestDataAsync();

        var filingId = 111;
        // Act
        var result = await _repository.GetForm470ById(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);

    }

    [Test]
    public async Task CreateForm470Amendment()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        var data = new Filing
        {
            StatusId = FilingStatus.Accepted.Id,
            Version = 0,
            SubmittedDate = date,
        };
        var original = await _repository.Create(data);
        original.OriginalId = original.Id;
        _ = await _repository.Update(original);

        var clone = await _repository.CreateForm470Amendment(original.Id);

        Assert.Multiple(() =>
        {
            Assert.That(clone.Id, Is.Not.EqualTo(original.Id));
            Assert.That(clone.OriginalId, Is.EqualTo(original.Id));
            Assert.That(clone.ParentId, Is.EqualTo(original.Id));
            Assert.That(clone.Version, Is.Not.Null);
            Assert.That(clone.Version, Is.EqualTo(original.Version + 1));
            Assert.That(clone.SubmittedDate, Is.Null);
        });
    }

    [Test]
    public async Task CreateForm470SAmendment()
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        // Arrange
        var original = new Filing
        {
            Id = 1,
            StatusId = FilingStatus.Accepted.Id,
            Version = 0,
            SubmittedDate = date,
        };
        var new470SAmend = new Filing
        {
            Id = 2,
            OriginalId = original.Id,
            ParentId = original.Id,
            Version = original.Version + 1,
            SubmittedDate = null,
            StatusId = FilingStatus.Draft.Id,
        };
        _ = await _repository.Create(original);
        original.OriginalId = original.Id;
        _ = await _repository.Update(original);

        // Act
        var clone = await _repository.CreateForm470SAmendment(original.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(clone.Id, Is.EqualTo(new470SAmend.Id));
            Assert.That(clone.OriginalId, Is.EqualTo(original.Id));
            Assert.That(clone.ParentId, Is.EqualTo(original.Id));
            Assert.That(clone.Version, Is.EqualTo(original.Version + 1));
            Assert.That(clone.SubmittedDate, Is.Null);
        });
    }

    [Test]
    public async Task FindExistingFilingAmendment_ReturnsFiling_WhenMatchExists()
    {
        // Arrange
        var filing = new Filing
        {
            Id = 1,
            ParentId = 100,
            StatusId = 200
        };
        _context.Filings.Add(filing);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindExistingFilingAmendment(100, 200);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(1));
    }

    [Test]
    public async Task FindEntireFilingById_ReturnsFilingWithIncludes()
    {
        // Arrange
        var contact = new IndividualContact { Id = 1, FirstName = "Tom", LastName = "Hanks" };
        var transaction = new ActivityExpense
        {
            Id = 1,
            Contact = contact,
            TransactionReportablePersons = new List<TransactionReportablePerson>(),
            ActionsLobbied = new List<ActionsLobbied?>(),
            Amount = (Currency)20
        };

        var filingSummary = new FilingSummary
        {
            Id = 1,
            PeriodAmount = 1000,
            ToDateAmount = 500,
            NonRegisteredLobbyists = new List<DisclosureFilingNonRegisteredLobbyist>
            {
                new() { Id = 1, FirstName = "Test", LastName = "Lobbyist", FilingSummaryId = 1 }
            }
        };

        var filing = new Filing
        {
            Id = 1,
            StatusId = FilingStatus.Draft.Id,
            FilingSummaries = new List<FilingSummary> { filingSummary },
            ActionsLobbied = new List<ActionsLobbied?>
            {
                new() { Id = 1, FilingId = 1 }
            },
            FilingPeriod = new FilingPeriod(),
            FilingRelatedFilers = new List<FilingRelatedFiler>
            {
                new() { Id = 1, FilingId = 1, FilerId = 100, Active = true, Filer = new Filer { Id = 100 } }
            },
            FilingTransactions = new List<FilingTransaction>
            {
                new()
                {
                    FilingId = 1,
                    Transaction = transaction
                }
            }
        };

        _context.Filings.Add(filing);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindEntireFilingById(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilingSummaries, Is.Not.Null);
            Assert.That(result.FilingSummaries, Is.Not.Empty);
            Assert.That(result.FilingSummaries[0].NonRegisteredLobbyists, Is.Not.Null);
            Assert.That(result.FilingSummaries[0].NonRegisteredLobbyists, Is.Not.Empty);

            Assert.That(result.FilingRelatedFilers, Is.Not.Null);
            Assert.That(result.FilingRelatedFilers, Is.Not.Empty);
            Assert.That(result.FilingRelatedFilers[0].FilerId, Is.EqualTo(100));

            Assert.That(result.ActionsLobbied, Is.Not.Null);
            Assert.That(result.ActionsLobbied, Is.Not.Empty);

            Assert.That(result.FilingPeriod, Is.Not.Null);

            Assert.That(result.FilingTransactions, Is.Not.Empty);
            Assert.That(result.FilingTransactions[0].Transaction!.Contact, Is.Not.Null);
            Assert.That(result.FilingTransactions[0].Transaction!.TransactionReportablePersons, Is.Not.Null);
            Assert.That(result.FilingTransactions[0].Transaction!.ActionsLobbied, Is.Not.Null);
        });
    }

    [Test]
    public async Task GetDraftFilingByFilerAndPeriodAndType_ShouldReturnDraftFiling_WhenMatchExists()
    {
        // Arrange
        long filerId = 101;
        long typeId = FilingType.OfficeHolderCandidateShortForm.Id;
        long filingPeriodId = 55;

        var draftFiling = new Filing
        {
            Id = 1001,
            FilerId = filerId,
            FilingTypeId = typeId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id,
            FilingPeriod = new FilingPeriod
            {
                Id = filingPeriodId,
                StartDate = DateTime.UtcNow.AddDays(-10),
                EndDate = DateTime.UtcNow.AddDays(10)
            }
        };

        await _context.Filings.AddAsync(draftFiling);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDraftFilingByFilerAndPeriodAndType(filerId, filingPeriodId, typeId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Id, Is.EqualTo(draftFiling.Id));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
        });
    }
    [Test]
    public async Task GetDraftFilingByFilerAndPeriodAndType_ShouldReturnNull_WhenNoMatch()
    {
        // Arrange
        long filerId = 999;
        long typeId = FilingType.SlateMailerOrganization.Id;
        long filingPeriodId = 55;

        // Make sure no matching filing exists
        await ClearTestDataAsync();

        // Act
        var result = await _repository.GetDraftFilingByFilerAndPeriodAndType(filerId, filingPeriodId, typeId);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public void IsAmendment_FlagsTrueWhenIdDiffersFromOriginalId()
    {
        var f = new Filing { Id = 100, OriginalId = 99, StatusId = FilingStatus.Draft.Id };
        Assert.That(f.IsAmendment, Is.True);
    }

    [Test]
    public void IsAmendment_FlagsFalseWhenIdEqualsOriginalId()
    {
        var f = new Filing { Id = 100, OriginalId = 100, StatusId = FilingStatus.Draft.Id };
        Assert.That(f.IsAmendment, Is.False);
    }

    [Test]
    public void IsAmendment_FlagsFalseWhenOriginalIdNull()
    {
        var f = new Filing { Id = 42, OriginalId = null, StatusId = FilingStatus.Draft.Id };
        Assert.That(f.IsAmendment, Is.False);
    }
    #region FindActiveSmoCampaignStatementAmendment
    [Test]
    public async Task FindActiveSmoCampaignStatementAmendment_Found_ReturnResult()
    {
        // Assign
        var id = 2;
        await SeedTestDataAsync();

        // Act
        var result = await _repository.FindActiveSmoCampaignStatementAmendment(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatement>());
            Assert.That(result?.ParentId, Is.EqualTo(id));
        });
    }

    [Test]
    public async Task FindActiveSmoCampaignStatementAmendment_NotFound_ReturnNull()
    {
        // Assign
        var id = 999;

        // Act
        var result = await _repository.FindActiveSmoCampaignStatementAmendment(id);

        // Assert
        Assert.That(result, Is.Null);
    }
    #endregion

    #region FindSmoCampaignStatementByIdAsNoTracking
    [Test]
    public async Task FindSmoCampaignStatementByIdAsNoTracking_Found_ReturnResult()
    {
        // Assign
        var id = 2;
        await SeedTestDataAsync();

        // Act
        var result = await _repository.FindSmoCampaignStatementByIdAsNoTracking(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoCampaignStatement>());
            Assert.That(result?.Id, Is.EqualTo(id));
        });
    }

    [Test]
    public async Task FindSmoCampaignStatementByIdAsNoTracking_NotFound_ReturnNull()
    {
        // Assign
        var id = 999;

        // Act
        var result = await _repository.FindSmoCampaignStatementByIdAsNoTracking(id);

        // Assert
        Assert.That(result, Is.Null);
    }
    #endregion
}
