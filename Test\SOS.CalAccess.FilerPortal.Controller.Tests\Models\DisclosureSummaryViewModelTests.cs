using SOS.CalAccess.FilerPortal.Models.Disclosure;

namespace SOS.CalAccess.FilerPortal.Tests.Models
{
    [TestFixture]
    [TestOf(nameof(DisclosureSummaryViewModel))]
    internal sealed class DisclosureSummaryViewModelTests : IDisposable
    {
        private DateTime _dateNow;
        private DisclosureSummaryViewModel? _viewModel;

        [SetUp]
        public void SetUp()
        {
            _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
            _viewModel = new DisclosureSummaryViewModel
            {
                StartDate = _dateNow,
                EndDate = _dateNow.AddDays(30),
                Name = "John Doe",
                Value = "Value",
            };
        }

        [TearDown]
        public void TearDown()
        {
            _viewModel = null;
        }

        [Test]
        public void DisclosureSummaryViewModel_Should_SetAndGet_StartDate()
        {
            DateTime expectedDate = _dateNow.AddDays(-10);
            _viewModel!.StartDate = expectedDate;
            Assert.That(_viewModel.StartDate, Is.EqualTo(expectedDate));
        }

        [Test]
        public void DisclosureSummaryViewModel_Should_SetAndGet_EndDate()
        {
            DateTime expectedDate = _dateNow.AddDays(10);
            _viewModel!.EndDate = expectedDate;
            Assert.That(_viewModel.EndDate, Is.EqualTo(expectedDate));
        }

        [Test]
        public void DisclosureSummaryViewModel_Should_SetAndGet_Name()
        {
            string expectedName = "Sample Name";
            _viewModel!.Name = expectedName;
            Assert.That(_viewModel.Name, Is.EqualTo(expectedName));
        }

        [Test]
        public void DisclosureSummaryViewModel_Should_Handle_Null_Name()
        {
            _viewModel!.Name = null;
            Assert.That(_viewModel.Name, Is.Null);
        }

        [Test]
        public void DisclosureSummaryViewModel_Should_SetAndGet_Value()
        {
            string expectedValue = "Sample Value";
            _viewModel!.Value = expectedValue;
            Assert.That(_viewModel.Value, Is.EqualTo(expectedValue));
        }

        [Test]
        public void DisclosureSummaryViewModel_Should_Handle_Null_Value()
        {
            _viewModel!.Value = null;
            Assert.That(_viewModel.Value, Is.Null);
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
