using System.Globalization;
using SOS.CalAccess.FilerPortal.Generated;
using SOS.CalAccess.FilerPortal.Models.Disclosure;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
public sealed class DisclosureLobbyingGeneralInfoViewModelTest
{
    [Test]
    public void DefaultConstructor_CreatesEmptyViewModel()
    {
        // Arrange & Act
        var viewModel = new DisclosureLobbyingGeneralInfoViewModel();
        Assert.Multiple(() =>
        {
            // Assert
            {
                Assert.That(viewModel.Id, Is.EqualTo(0));
                Assert.That(viewModel.FilerId, Is.Null);
                Assert.That(viewModel.Name, Is.Null);
            }
        });
    }

    [Test]
    public void ParameterizedConstructor_WithNullInputs_InitializesMinimalViewModel()
    {
        // Arrange
        LobbyistResponseDto? lobbyist = null;
        LobbyistReportResponse? filing = null;

        // Act
        var viewModel = new DisclosureLobbyingGeneralInfoViewModel(lobbyist, filing);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(viewModel.Id, Is.EqualTo(0));
            Assert.That(viewModel.FilerId, Is.EqualTo(0));
            Assert.That(viewModel.Name, Is.Null);
        });
    }

    [Test]
    public void ParameterizedConstructor_WithValidInputs_PopulatesProperties()
    {
        // Arrange
        var filing = new LobbyistReportResponse(
            id: 123,
            filerId: 456,
            startDate: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            endDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            diligenceStatementVerified: false,
            filerName: "Foo",
            parentId: 123,
            status: 1,
            submittedDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            version: 1,
            filingTypeId: 16
        );

        var lobbyist = new LobbyistResponseDto(
            addresses: new List<AddressDtoModel>
            {
                new AddressDtoModel(
                    city: "Test City",
                    country: "USA",
                    purpose: "Mailing",
                    state: "CA",
                    street: "123 Test St",
                    street2: "",
                    type: "Home",
                    zip: "12345"
                )
            },
            addressListId: 789,
            dateQualified: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            email: "<EMAIL>",
            employerName: "Test Employer",
            filerId: 456,
            id: 789,
            name: "Test Lobbyist",
            phoneNumberListId: 101,
            phoneNumbers:
            new List<PhoneNumberDto>
            {
                new("+1", null, "987", 1, false, "1011231234", 1, false, "Work"),
            },
            stateLegislatureLobbying: true,
            statusId: 1,
            version: 1,
            agencies: [],
            completedCourseDate: new(),
            completedEthicsCourse: true,
            completedEthicsCourseWithinPastYear: true,
            dateOfQualification: new(),
            diligenceVerificationStatement: true,
            isNewCertification: true,
            firstName: "Walter",
            middleName: string.Empty,
            lastName: "White",
            isLobbyingStateLegislature: true,
            isPlacementAgent: true,
            legislativeSessionId: 1,
            lobbyistEmployerOrLobbyingFirmId: 1,
            lobbyOnlySpecifiedAgencies: false,
            photo: "sample.jpg",
            selfRegister: true,
            isSameAsCandidateAddress: true,
            lobbyistEmployerOrLobbyingFirmName: "test",
            withdrawnAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            terminatedAt: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            effectiveDateOfChanges: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            type: "Lobbyist"
        );

        // Act
        var viewModel = new DisclosureLobbyingGeneralInfoViewModel(lobbyist, filing);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(viewModel.Id, Is.EqualTo(123));
            Assert.That(viewModel.FilerId, Is.EqualTo(456));
            Assert.That(viewModel.StartDate, Is.EqualTo(new DateTime(2023, 1, 1)));
            Assert.That(viewModel.EndDate, Is.EqualTo(new DateTime(2023, 3, 31)));
            Assert.That(viewModel.Name, Is.EqualTo("Test Lobbyist"));
            Assert.That(viewModel.EmployerName, Is.EqualTo("Test Employer"));
            Assert.That(viewModel.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(viewModel.FilerIdNumber, Is.EqualTo("456"));
        });
    }

    [Test]
    public void ParameterizedConstructor_WithFilingButNoLobbyist_PopulatesBasicProperties()
    {
        // Arrange
        var filing = new LobbyistReportResponse(
            id: 123,
            filerId: 456,
            startDate: DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            endDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            diligenceStatementVerified: false,
            filerName: "Foo",
            parentId: 123,
            status: 1,
            submittedDate: DateTime.Parse("2023-03-31", CultureInfo.InvariantCulture),
            version: 1,
            filingTypeId: 16
        );

        // Act
        var viewModel = new DisclosureLobbyingGeneralInfoViewModel(null, filing);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(viewModel.Id, Is.EqualTo(123));
            Assert.That(viewModel.FilerId, Is.EqualTo(456));
            Assert.That(viewModel.StartDate, Is.EqualTo(new DateTime(2023, 1, 1)));
            Assert.That(viewModel.EndDate, Is.EqualTo(new DateTime(2023, 3, 31)));
            Assert.That(viewModel.FilerIdNumber, Is.EqualTo("456"));
            Assert.That(viewModel.Name, Is.Null);
        });
    }
}
