// <copyright file="DataAction.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using SOS.CalAccess.Data.EntityFramework.Audit.Diffing;

namespace SOS.CalAccess.Data.EntityFramework.Audit;

/// <summary>
/// Class for describing low level auditable data actions built from EF Core entity change
/// tracking entries.
/// </summary>
/// <param name="entry">The change tracking entry used to create the action description.</param>
/// <param name="auditDiffSerializer">Optional implementation to use to calculate state diff.</param>
public sealed class DataAction(
    EntityEntry entry,
    DateTime issuedAt,
    IAuditDiffSerializer? auditDiffSerializer = default) : IAuditableAction
{
    /// <summary>
    /// Action tag for operations that created a resource.
    /// </summary>
    public const string Create = "Create";

    /// <summary>
    /// Action tag for operations that updated a resource.
    /// </summary>
    public const string Update = "Update";

    /// <summary>
    /// Action tag for operations that deleted a resource.
    /// </summary>
    public const string Delete = "Delete";

    /// <inheritdoc />
    public string Type { get; } = entry.State switch
    {
        EntityState.Added => Create,
        EntityState.Modified => Update,
        EntityState.Deleted => Delete,
        EntityState.Detached => throw new NotImplementedException(),
        EntityState.Unchanged => throw new NotImplementedException(),
        _ => throw new ArgumentOutOfRangeException(nameof(entry)),
    };

    /// <inheritdoc />
    public string Subject { get; } = entry.Entity.GetType().Name;

    /// <inheritdoc />
    public string ResourceId { get; } = GetOrCreateResourceId(entry);

    /// <inheritdoc />
    public string? Diff { get; } = auditDiffSerializer?.CalculateDiff(entry);

    /// <inheritdoc />
    public DateTime IssuedAt { get; } = issuedAt;

    private static string GetOrCreateResourceId(EntityEntry entityEntry)
    {
        var property = entityEntry.Property(AuditLog.ResourceTag);

        if (property.CurrentValue is string id)
        {
            return id;
        }

        string tag = Guid.NewGuid().ToString();
        property.CurrentValue = tag;

        return tag;
    }
}
