using SOS.CalAccess.Models.FilerRegistration.Filers;
namespace SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;

public interface IFilerLinkRepository : IRepository<FilerLink, long>
{
    /// <summary>
    /// Find FilerLink of a SMO registration application by FilerId and FilerLinkType Id
    /// </summary>
    /// <param name="filerId">ID of filer</param>
    /// <param name="filerLinkTypeId">ID of filer type link</param>
    /// <returns></returns>
    Task<FilerLink?> FindByFilerIdAndLinkType(long filerId, long filerLinkTypeId);

    /// <summary>
    /// Repository method to associate an Controlled Committee to Candidate Registration
    /// </summary>
    /// <param name="filerId">filer Id FK</param>
    /// <param name="controlledCommitteeFilerId">Controlled Committee filerId FK</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    Task<FilerLink?> LinkControlledCommitteeToCandidateIntentionStatement(long filerId, long controlledCommitteeFilerId, long? userId);

    /// <summary>
    /// Repository method to associate an any link type to filer
    /// </summary>
    /// <param name="filerId">filer Id FK</param>
    /// <param name="linkedEntityId">linked type ID FK based on link type</param>
    /// <param name="linkedType">defined linked type</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    Task<FilerLink?> LinkEntityTypeToFiler(long filerId, long linkedEntityId, FilerLinkType linkType, long? userId, List<FilerLinkType>? entityInLinkType = null);

    /// <summary>
    /// Un-link lobbyist from employer
    /// </summary>
    /// <param name="employerRegistrationId">Employer registration id</param>
    /// <param name="lobbyistRegistrationId">Lobbyist registration id</param>
    /// <returns></returns>
    Task<bool> UnlinkLobbyistFromEmployer(long employerRegistrationId, long lobbyistRegistrationId);

    /// <summary>
    /// Link lobbyist employer registration to lobbying firm
    /// </summary>
    /// <param name="filterId"></param>
    /// <param name="lobbyingFirmFilerIds">Filer Ids of lobbying firms</param>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task<List<FilerLink>> LinkLobbyistEmployerRegistrationToLobbyingFirms(long filerId, List<long> lobbyingFirmFilerIds, long userId);
}
