
using System.Globalization;
using System.Reflection;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using SendGrid.Helpers.Errors.Model;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Contacts;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.OtherInfluencePayments;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Common.BusinessRules;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Transactions;

/// <summary>
/// Unit tests for the <see cref="TransactionSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(TransactionSvc))]
public sealed class TransactionSvcTest
{
    private ITransactionRepository _transactionRepository;
    private IFilerContactRepository _filerContactRepositoryMock;
    private IDecisionsSvc _decisionsSvc;
    private ILobbyistEmployerRegistrationSvc _lobbyistEmployerRegistrationSvc;
    private ILobbyingFirmRegistrationSvc _lobbyingFirmRegistrationSvc;
    private IFilerContactSvc _filerContactSvc;
    private IFilingSvc _filingSvc;
    private TransactionSvc _transactionSvc;
    private IActionsLobbiedSvc _actionsLobbiedSvc;
    private IFilingSummaryRepository _filingSummaryRepository;
    private ITransactionHelperSvc _transactionHelperSvc;
    private IFilingContactSummaryRepository _filingContactSummaryRepository;
    private IFilingRepository _filingRepository;
    private IFilingPeriodRepository _filingPeriodRepository;

    private DateTime _dateNow;

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _transactionRepository = Substitute.For<ITransactionRepository>();
        _filerContactRepositoryMock = Substitute.For<IFilerContactRepository>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _lobbyistEmployerRegistrationSvc = Substitute.For<ILobbyistEmployerRegistrationSvc>();
        _lobbyingFirmRegistrationSvc = Substitute.For<ILobbyingFirmRegistrationSvc>();
        _filerContactSvc = Substitute.For<IFilerContactSvc>();
        _filingSvc = Substitute.For<IFilingSvc>();
        _actionsLobbiedSvc = Substitute.For<IActionsLobbiedSvc>();
        _filingSummaryRepository = Substitute.For<IFilingSummaryRepository>();
        _transactionHelperSvc = Substitute.For<ITransactionHelperSvc>();
        _filingContactSummaryRepository = Substitute.For<IFilingContactSummaryRepository>();
        _filingRepository = Substitute.For<IFilingRepository>();
        _filingPeriodRepository = Substitute.For<IFilingPeriodRepository>();

        _transactionSvc = new TransactionSvc(
            _transactionRepository,
            _filerContactRepositoryMock,
            _filingContactSummaryRepository,
            _filingRepository,
            _filingPeriodRepository,
            _decisionsSvc,
            _lobbyistEmployerRegistrationSvc,
            _lobbyingFirmRegistrationSvc,
            _filerContactSvc,
            _filingSvc,
            _actionsLobbiedSvc,
            _filingSummaryRepository,
            _transactionHelperSvc
        );
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
    }

    /// <summary>
    /// Test to ensure GetAllTransactionsForFiler returns a list of transactions.
    /// </summary>
    [Test]
    public async Task GetAllTransactionsForFiler_ReturnsTransactions_WhenTransactionsExist()
    {
        // Arrange
        long filerId = 123L;
        Expenditure transaction1 = new()
        {
            Id = 1,
            TransactionDate = _dateNow,
            Amount = (Currency)100,
            ContactId = 2,
            Purpose = "Test Purpose"
        };
        Expenditure transaction2 = new()
        {
            Id = 2,
            TransactionDate = _dateNow,
            Amount = (Currency)200,
            ContactId = 3,
            Purpose = "Another Purpose"
        };

        _transactionRepository.GetAllByFilerId(filerId).Returns([transaction1, transaction2]);

        // Act
        IEnumerable<Transaction> result = await _transactionSvc.GetAllTransactionsForFiler(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.ToList(), Does.Contain(transaction1));
            Assert.That(result.ToList(), Does.Contain(transaction2));
        });
    }

    /// <summary>
    /// Test to ensure CreateTransaction should return an Id of the created transaction.
    /// </summary>
    [Test]
    public async Task CreateTransaction_ShouldReturnTransactionId_WhenTransactionIsCreatedSuccessfully()
    {
        // Arrange
        Expenditure transaction = new()
        {
            Id = 0,
            TransactionDate = _dateNow,
            Amount = (Currency)100,
            CreatedBy = 1,
            ModifiedBy = 1,
            Purpose = "Test expenditure"
        };

        Expenditure createdTransaction = new()
        {
            Id = 12345,
            TransactionDate = transaction.TransactionDate,
            Amount = transaction.Amount,
            Purpose = transaction.Purpose
        };

        _transactionRepository.Create(transaction).Returns(Task.FromResult<Transaction>(createdTransaction));

        // Act
        long result = await _transactionSvc.CreateTransaction(transaction);

        // Assert
        Assert.That(result, Is.EqualTo(12345));
        await _transactionRepository.Received(1).Create(transaction);
    }

    /// <summary>
    /// Test to ensure GetTransaction should return a valid transaction.
    /// </summary>
    [Test]
    public async Task GetTransaction_ShouldReturnTransaction_WhenTransactionIsFound()
    {
        // Arrange
        int transactionId = 123;
        Expenditure expectedTransaction = new()
        {
            Id = transactionId,
            TransactionDate = _dateNow,
            Amount = (Currency)100,
            Purpose = "Test expenditure",
            CreatedBy = 1,
            ModifiedBy = 1
        };

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(expectedTransaction));

        // Act
        Transaction? result = await _transactionSvc.GetTransaction(transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(expectedTransaction.Id));
        });
        await _transactionRepository.Received(1).FindById(transactionId);
    }

    /// <summary>
    /// Test to ensure GetAllTransactionsForFiling should return a valid transactions for a specific filing.
    /// </summary>
    [Test]
    public async Task GetAllTransactionsForFiling_ShouldReturnTransactions_WhenTransactionsExist()
    {
        // Arrange
        long filingId = 123;
        List<Transaction> expectedTransactions =
        [
            new Expenditure
            {
                Id = 1,
                TransactionDate = _dateNow,
                Amount = (Currency)100,
                Purpose = "Test expenditure 1",
                CreatedBy = 1,
                ModifiedBy = 1
            },
            new Expenditure
            {
                Id = 2,
                TransactionDate = _dateNow,
                Amount = (Currency)200,
                Purpose = "Test expenditure 2",
                CreatedBy = 1,
                ModifiedBy = 1
            }
        ];

        _transactionRepository.GetAllByFilingId(filingId).Returns(Task.FromResult<IEnumerable<Transaction>>(expectedTransactions));

        // Act
        IEnumerable<Transaction> result = await _transactionSvc.GetAllTransactionsForFiling(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.ToList(), Is.EqualTo(expectedTransactions));
        });
        await _transactionRepository.Received(1).GetAllByFilingId(filingId);
    }

    /// <summary>
    /// Test to ensure CreateLobbyingCampaignContribution should return an Id of the created transaction.
    /// </summary>
    [Test]
    public async Task CreateLobbyingCampaignContribution_ShouldReturnTransactionId_WhenTransactionIsCreatedSuccessfully()
    {
        // Arrange
        LobbyingCampaignContribution transaction = new()
        {
            Id = 1,
            Amount = (Currency)150,
            TransactionDate = _dateNow,
            CreatedBy = 1,
            ModifiedBy = 1,
            NonCommitteeRecipientName = "Joe"
        };

        LobbyingCampaignContribution createdTransaction = new()
        {
            Id = 12345,
            TransactionDate = transaction.TransactionDate,
            Amount = transaction.Amount,
            NonCommitteeRecipientName = transaction.NonCommitteeRecipientName
        };

        _transactionRepository.Create(transaction).Returns(Task.FromResult<Transaction>(createdTransaction));

        // Act
        long result = await _transactionSvc.CreateTransaction(transaction);

        // Assert
        Assert.That(result, Is.EqualTo(12345));
        await _transactionRepository.Received(1).Create(transaction);
    }

    /// <summary>
    /// Test to ensure GetAllTransactionsForFiling should return a valid transactions for a specific filing.
    /// </summary>
    [Test]
    public async Task GetAllLobbyistEmployerCampaignContributionTransactionsForFiling_ShouldReturnTransactions_WhenTransactionsExist()
    {
        // Arrange
        long filingId = 123;
        List<LobbyingCampaignContribution> expectedTransactions =
        [
            new() {
                Id = 1,
                Amount = (Currency)150,
                TransactionDate = _dateNow,
                CreatedBy = 1,
                ModifiedBy = 1,
                NonCommitteeRecipientName = "John"
            },
            new() {
                Id = 2,
                Amount = (Currency)200,
                TransactionDate = _dateNow,
                CreatedBy = 1,
                ModifiedBy = 1,
                NonCommitteeRecipientName = "Doe"
            }
        ];

        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId).Returns(Task.FromResult<IEnumerable<LobbyingCampaignContribution>>(expectedTransactions));

        // Act
        IEnumerable<LobbyingCampaignContribution> result = await _transactionSvc.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.ToList(), Is.EqualTo(expectedTransactions));
        });
        await _transactionRepository.Received(1).GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId);
    }

    [Test]
    public void TestPopulateLobbyistCampaignContributionDs_WithReflection()
    {
        // Arrange
        LobbyistCampaignContributionRequestDto requestDto = new()
        {
            TransactionDate = _dateNow,
            Amount = 100,
            IsRecipientCommittee = true,
            RecipientCommitteeFilerId = 12345,
            NonCommitteeRecipientName = "Test Recipient",
            FilerId = 67890,
            ContributorFilerId = 11111,
            IsContributorFiler = true,
            NonFilerContributorName = "Test Contributor",
            SeparateAccountName = "Test Account"
        };

        // Act
        MethodInfo? methodInfo = typeof(WebApi.Transactions.TransactionsController).GetMethod("PopulateLobbyistCampaignContributionDs", BindingFlags.NonPublic | BindingFlags.Static);
        object? result = methodInfo?.Invoke(null, new object[] { requestDto });

        // Assert
        LobbyistCampaignContributionDs? lobbyistCampaignContributionDs = result as LobbyistCampaignContributionDs;
        Assert.That(lobbyistCampaignContributionDs, Is.Not.Null, "Result is not of type LobbyistCampaignContributionDs.");

        Assert.Multiple(() =>
        {
            Assert.That(lobbyistCampaignContributionDs.Amount, Is.EqualTo(requestDto.Amount));
            Assert.That(lobbyistCampaignContributionDs.NonCommitteeRecipentName, Is.EqualTo(requestDto.NonCommitteeRecipientName));
            Assert.That(lobbyistCampaignContributionDs.TransactionDate, Is.EqualTo(requestDto.TransactionDate));
            Assert.That(lobbyistCampaignContributionDs.IsRecipientCommittee, Is.EqualTo(requestDto.IsRecipientCommittee));
            Assert.That(lobbyistCampaignContributionDs.CommitteeId, Is.EqualTo(requestDto.RecipientCommitteeFilerId));
            Assert.That(lobbyistCampaignContributionDs.FilerId, Is.EqualTo(requestDto.FilerId));
            Assert.That(lobbyistCampaignContributionDs.ContributorFilerId, Is.EqualTo(requestDto.ContributorFilerId));
            Assert.That(lobbyistCampaignContributionDs.IsContributorFiler, Is.EqualTo(requestDto.IsContributorFiler));
            Assert.That(lobbyistCampaignContributionDs.NonFilerContributorName, Is.EqualTo(requestDto.NonFilerContributorName));
            Assert.That(lobbyistCampaignContributionDs.SeparateAccountName, Is.EqualTo(requestDto.SeparateAccountName));
        });
    }

    [Test]
    public void TestPopulateLobbyistEmployerCoalitionCampaignContributionDs_WithReflection()
    {
        // Arrange
        LobbyingCampaignContributionRequestDto requestDto = new()
        {
            TransactionDate = _dateNow,
            Amount = 200,
            IsRecipientCommittee = false,
            RecipientCommitteeFilerId = 54321,
            NonCommitteeRecipientName = "Coalition Recipient",
            FilerId = 98765
        };

        // Act
        MethodInfo? methodInfo = typeof(WebApi.Transactions.TransactionsController)
            .GetMethod("PopulateLobbyistEmployerCoalitionCampaignContributionDs", BindingFlags.NonPublic | BindingFlags.Static);

        // Ensure methodInfo isn't null
        Assert.That(methodInfo, Is.Not.Null, "Method 'PopulateLobbyistEmployerCoalitionCampaignContributionDs' not found.");

        object? result = methodInfo.Invoke(null, new object[] { requestDto });

        // Assert
        LobbyistEmployerCoalitionCampaignContributionDs? lobbyistEmployerCoalitionCampaignContributionDs = result as LobbyistEmployerCoalitionCampaignContributionDs;
        Assert.That(lobbyistEmployerCoalitionCampaignContributionDs, Is.Not.Null, "Result is not of type LobbyistEmployerCoalitionCampaignContributionDs.");

        Assert.Multiple(() =>
        {
            Assert.That(lobbyistEmployerCoalitionCampaignContributionDs.Amount, Is.EqualTo(requestDto.Amount));
            Assert.That(lobbyistEmployerCoalitionCampaignContributionDs.NonCommitteeRecipentName, Is.EqualTo(requestDto.NonCommitteeRecipientName));
            Assert.That(lobbyistEmployerCoalitionCampaignContributionDs.TransactionDate, Is.EqualTo(requestDto.TransactionDate));
            Assert.That(lobbyistEmployerCoalitionCampaignContributionDs.IsRecipientCommittee, Is.EqualTo(requestDto.IsRecipientCommittee));
            Assert.That(lobbyistEmployerCoalitionCampaignContributionDs.CommitteeId, Is.EqualTo(requestDto.RecipientCommitteeFilerId));
            Assert.That(lobbyistEmployerCoalitionCampaignContributionDs.FilerId, Is.EqualTo(requestDto.FilerId));
        });
    }
    /// <summary>
    /// Test to ensure GetAllLobbyistEmployerCoalitionPaymentsTransactionsForFiling should return a valid transactions for a specific filing.
    /// </summary>
    [Test]
    public async Task GetAllLobbyistEmployerCoalitionPaymentsTransactionsForFiling_ShouldReturnTransactions_WhenTransactionsExist()
    {
        // Arrange
        long filingId = 123;
        List<PaymentMadeToLobbyingCoalitionResponse> expectedTransactions =
        [
            new() {
                CoalitionName = "John",
                FilerId = 1,
                AmountThisPeriod = (Currency)200,
                CumulativeAmount = (Currency)200
            },
            new() {
                CoalitionName = "Doe",
                FilerId = 1,
                AmountThisPeriod = (Currency)300,
                CumulativeAmount = (Currency)400
            }
        ];

        DateTime legislativeStartDate = _dateNow;

        _transactionRepository.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, legislativeStartDate).Returns(Task.FromResult<IEnumerable<PaymentMadeToLobbyingCoalitionResponse>>(expectedTransactions));

        // Act
        IEnumerable<PaymentMadeToLobbyingCoalitionResponse> result = await _transactionSvc.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, legislativeStartDate);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.ToList(), Is.EqualTo(expectedTransactions));
        });
        await _transactionRepository.Received(1).GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, legislativeStartDate);
    }

    /// <summary>
    /// Test to ensure GetAllLobbyistEmployerCoalitionPaymentsTransactionsForFiling should return a valid transactions for a specific filing.
    /// </summary>
    [Test]
    public async Task GetAllOtherPaymentsToInfluenceTransactionsForFiling_ShouldReturnTransactions_WhenTransactionsExist()
    {
        // Arrange
        long filingId = 123;
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);

        List<OtherPaymentsToInfluenceResponse> expectedTransactions =
        [
        new() {
            PaymentCodeName = "1",
            PayeeName = "Test",
            Amount = (Currency)200,
            CumulativeAmount = (Currency)200
        },
        new()
        {
            PaymentCodeName = "2",
            PayeeName = "Fake",
            Amount = (Currency)300,
            CumulativeAmount = (Currency)400
        }
    ];

        _transactionRepository
            .GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, legislativeStartDate)
            .Returns(Task.FromResult<IEnumerable<OtherPaymentsToInfluenceResponse>>(expectedTransactions));

        // Act
        IEnumerable<OtherPaymentsToInfluenceResponse> result = await _transactionSvc.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, legislativeStartDate);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedTransactions));
        });

        await _transactionRepository
            .Received(1)
            .GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, legislativeStartDate);
    }

    [Test]
    public async Task GetOtherPaymentsToInfluenceTransactionById_ReturnsExpectedResults()
    {
        // Arrange
        var transactionId = 1;
        long contactId = 456;
        var transaction = new OtherPaymentsToInfluence()
        {
            Id = transactionId,
            ContactId = contactId,
            Amount = (Currency)10,
            Contact = new IndividualContact() { Id = contactId },
            ActionsLobbied = new List<Models.FilerDisclosure.ActionsLobbied?>()
            {
                new()
                {
                    Id = 1,
                    AdministrativeAction = "test"
                }
            },
            FilingTransactions = new List<FilingTransaction>
            {
                new() { TransactionId = transactionId, Filing = new Filing { StatusId = 1 } }
            }
        };

        _transactionRepository.GetOtherPaymentsToInfluenceTransactionById(transactionId)
            .Returns(Task.FromResult(transaction));

        // Act
        var result = await _transactionSvc.GetOtherPaymentsToInfluenceTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.ContactId, Is.EqualTo(contactId));
            Assert.That(result.AdministrativeActions, Is.Not.Empty);
        });
    }

    /// <summary>
    /// Test to ensure GetAllEndOfSessionLobbyingTransactionsForFiling should return valid transactions for a specific filing.
    /// </summary>
    [Test]
    public async Task GetAllEndOfSessionLobbyingTransactionsForFiling_ShouldReturnTransactions_WhenTransactionsExist()
    {
        // Arrange
        long filingId = 123;
        List<EndOfSessionLobbyingDto> expectedTransactions =
        [
            new()
            {
                FilerId = 1001,
                FirmName = "Lobbying Firm A",
                DateLobbyingFirmHired = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Local),
                Amount = (Currency)500
            },
            new()
            {
                FilerId = 1002,
                FirmName = "Lobbying Firm B",
                DateLobbyingFirmHired = new DateTime(2023, 2, 15, 0, 0, 0, DateTimeKind.Local),
                Amount = (Currency)300
            }
        ];

        _ = _transactionRepository
            .GetAllEndOfSessionLobbyingTransactionsForFiling(filingId)
            .Returns(Task.FromResult<IEnumerable<EndOfSessionLobbyingDto>>(expectedTransactions));

        // Act
        IEnumerable<EndOfSessionLobbyingDto> result = await _transactionSvc.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId);
        List<EndOfSessionLobbyingDto> resultList = result.ToList();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(resultList, Has.Count.EqualTo(2));

            // Verify first transaction data
            Assert.That(resultList[0].FilerId, Is.EqualTo(1001));
            Assert.That(resultList[0].FirmName, Is.EqualTo("Lobbying Firm A"));
            Assert.That(resultList[0].DateLobbyingFirmHired, Is.EqualTo(new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(resultList[0].Amount, Is.EqualTo(500));

            // Verify second transaction data
            Assert.That(resultList[1].FilerId, Is.EqualTo(1002));
            Assert.That(resultList[1].FirmName, Is.EqualTo("Lobbying Firm B"));
            Assert.That(resultList[1].DateLobbyingFirmHired, Is.EqualTo(new DateTime(2023, 2, 15, 0, 0, 0, DateTimeKind.Local)));
            Assert.That(resultList[1].Amount, Is.EqualTo(300));
        });

        _ = await _transactionRepository.Received(1).GetAllEndOfSessionLobbyingTransactionsForFiling(filingId);
    }

    /// <summary>
    /// Test to ensure GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling should return a valid transactions for a specific filing.
    /// </summary>
    [Test]
    public async Task GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling_ShouldReturnTransactions_WhenTransactionsExist()
    {
        // Arrange
        long filingId = 123;
        List<PaymentMadeToLobbyingFirmsResponse> expectedTransactions =
        [
            new() {
                FirmName = "John",
                FilerId = 1,
                AmountThisPeriod = (Currency)200,
                CumulativeAmount = (Currency)200
            },
            new() {
                FirmName = "Doe",
                FilerId = 1,
                AmountThisPeriod = (Currency)300,
                CumulativeAmount = (Currency)400
            }
        ];

        DateTime legislativeStartDate = _dateNow;

        _transactionHelperSvc.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filingId, legislativeStartDate).Returns(Task.FromResult<IEnumerable<PaymentMadeToLobbyingFirmsResponse>>(expectedTransactions));

        // Act
        IEnumerable<PaymentMadeToLobbyingFirmsResponse> result = await _transactionSvc.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filingId, legislativeStartDate);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.ToList(), Is.EqualTo(expectedTransactions));
        });
        await _transactionHelperSvc.Received(1).GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filingId, legislativeStartDate);
    }

    /// <summary>
    /// Test to verify that CreatePaymentMadeToLobbyingCoalition creates a transaction with just the amount and filer ID
    /// when no coalition or contact information is provided.
    /// </summary>
    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithoutRegistrationOrContact_ShouldCreateBasicTransaction()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = 1000m;
        long filerId = 123L;
        long expectedTransactionId = 789L;

        PaymentMadeToLobbyingCoalition transaction = new()
        {
            Id = expectedTransactionId,
            Amount = (Currency)amount,
            FilerId = filerId
        };

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingCoalition>())
            .Returns(transaction);

        // When validating, return no errors
        _ = _decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingCoalitionDs, WorkFlowError>(
            Arg.Is(DecisionsWorkflow.PaymentMadeToLobbyingCoalitionRuleset),
            Arg.Any<PaymentMadeToLobbyingCoalitionDs>(),
            Arg.Any<bool>())
            .Returns(Task.FromResult<WorkFlowError>(null!));

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingCoalition(disclosureFilingId, amount, filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingCoalition>(t =>
            t.Amount == (Currency)amount &&
            t.FilerId == filerId &&
            t.ContactId == null));
    }

    /// <summary>
    /// Test to verify that CreatePaymentMadeToLobbyingCoalition sets ContactId correctly 
    /// when an existing filer contact ID is provided.
    /// </summary>
    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithExistingFilerContact_ShouldUseProvidedContactId()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = 2000m;
        long filerId = 123L;
        long filerContactId = 456L;
        long expectedTransactionId = 789L;

        PaymentMadeToLobbyingCoalition transaction = new()
        {
            Id = expectedTransactionId,
            Amount = (Currency)amount,
            FilerId = filerId,
            ContactId = filerContactId
        };

        _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingCoalition>())
            .Returns(transaction);

        // When validating, return no errors
        _ = _decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingCoalitionDs, WorkFlowError>(
            Arg.Is(DecisionsWorkflow.PaymentMadeToLobbyingCoalitionRuleset),
            Arg.Any<PaymentMadeToLobbyingCoalitionDs>(),
            Arg.Any<bool>())
            .Returns(Task.FromResult<WorkFlowError>(null!));

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingCoalition(
            disclosureFilingId, amount, filerId, filerContactId: filerContactId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingCoalition>(t =>
            t.Amount == (Currency)amount &&
            t.FilerId == filerId &&
            t.ContactId == filerContactId));
    }

    /// <summary>
    /// Test to verify that CreatePaymentMadeToLobbyingCoalition uses an existing contact relationship 
    /// when one exists between the filer and the coalition.
    /// </summary>
    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithExistingContactRelationship_ShouldUseExistingContact()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = 3000m;
        long filerId = 123L;
        long registrationFilingId = 456L;
        long coalitionFilerId = 555L;
        long existingFilerContactId = 666L;
        long expectedTransactionId = 789L;

        LobbyistEmployer selectedCoalition = new()
        {
            FilerId = coalitionFilerId,
            Name = "Test Coalition",
            StatusId = 1,
            Email = "<EMAIL>",
            EmployerName = "Test Employer",
            EmployerType = "Type A",
            BusinessActivity = "Activity A",
            BusinessDescription = "Description A",
            InterestType = "Interest A",
            NumberOfMembers = 10,
            LegislativeSessionId = 1
        };

        OrganizationContact existingFilerContact = new()
        {
            Id = existingFilerContactId,
            FilerId = filerId,
            ContactFilerId = coalitionFilerId,
            OrganizationName = "Existing Organization"
        };

        PaymentMadeToLobbyingCoalition transaction = new()
        {
            Id = expectedTransactionId,
            Amount = (Currency)amount,
            FilerId = filerId,
            ContactId = existingFilerContactId
        };

        _ = _lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(registrationFilingId)
            .Returns(Task.FromResult<LobbyistEmployerResponseDto?>(new LobbyistEmployerResponseDto(selectedCoalition)));

        _ = _filerContactSvc.GetFilerContactByFilerIdAndContactFilerId(filerId, coalitionFilerId)
            .Returns(existingFilerContact);

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingCoalition>())
            .Returns(transaction);

        // When validating, return no errors
        _ = _decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingCoalitionDs, WorkFlowError>(
            Arg.Is(DecisionsWorkflow.PaymentMadeToLobbyingCoalitionRuleset),
            Arg.Any<PaymentMadeToLobbyingCoalitionDs>(),
            Arg.Any<bool>())
            .Returns(Task.FromResult<WorkFlowError>(null!));

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingCoalition(
            disclosureFilingId, amount, filerId, registrationFilingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _lobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployer(registrationFilingId);
        _ = await _filerContactSvc.Received(1).GetFilerContactByFilerIdAndContactFilerId(filerId, coalitionFilerId);
        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingCoalition>(t =>
            t.Amount == (Currency)amount &&
            t.FilerId == filerId &&
            t.ContactId == existingFilerContactId));
    }

    /// <summary>
    /// Test to verify that CreatePaymentMadeToLobbyingCoalition creates a new contact relationship 
    /// when none exists between the filer and the coalition.
    /// </summary>
    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithNoExistingContactRelationship_ShouldCreateNewContact()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = 4000m;
        long filerId = 123L;
        long registrationFilingId = 456L;
        long coalitionFilerId = 555L;
        long newFilerContactId = 777L;
        long expectedTransactionId = 789L;
        long addressListId = 101L;
        long phoneNumberListId = 102L;

        LobbyistEmployer selectedCoalition = new()
        {
            FilerId = coalitionFilerId,
            Name = "Test Coalition",
            StatusId = 1,
            Email = "<EMAIL>",
            EmployerName = "Test Employer",
            EmployerType = "Type A",
            BusinessActivity = "Activity A",
            BusinessDescription = "Description A",
            InterestType = "Interest A",
            NumberOfMembers = 10,
            LegislativeSessionId = 1,
            AddressListId = addressListId,
            PhoneNumberListId = phoneNumberListId
        };

        PaymentMadeToLobbyingCoalition transaction = new()
        {
            Id = expectedTransactionId,
            Amount = (Currency)amount,
            FilerId = filerId,
            ContactId = newFilerContactId
        };

        _ = _lobbyistEmployerRegistrationSvc.GetLobbyistEmployer(registrationFilingId)
            .Returns(new LobbyistEmployerResponseDto(selectedCoalition));

        _ = _filerContactSvc.GetFilerContactByFilerIdAndContactFilerId(filerId, coalitionFilerId)
            .Returns((FilerContact?)null);

        _ = _filerContactSvc.CreateFilerContact(Arg.Any<OrganizationContact>())
            .Returns(newFilerContactId);

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingCoalition>())
            .Returns(transaction);

        // When validating, return no errors
        _ = _decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingCoalitionDs, WorkFlowError>(
            Arg.Is(DecisionsWorkflow.PaymentMadeToLobbyingCoalitionRuleset),
            Arg.Any<PaymentMadeToLobbyingCoalitionDs>(),
            Arg.Any<bool>())
            .Returns(Task.FromResult<WorkFlowError>(null!));

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingCoalition(
            disclosureFilingId, amount, filerId, registrationFilingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _lobbyistEmployerRegistrationSvc.Received(1).GetLobbyistEmployer(registrationFilingId);
        _ = await _filerContactSvc.Received(1).GetFilerContactByFilerIdAndContactFilerId(filerId, coalitionFilerId);
        _ = await _filerContactSvc.Received(1).CreateFilerContact(Arg.Is<OrganizationContact>(c =>
            c.FilerId == filerId &&
            c.ContactFilerId == coalitionFilerId &&
            c.OrganizationName == selectedCoalition.Name &&
            c.AddressListId == selectedCoalition.AddressListId &&
            c.PhoneNumberListId == selectedCoalition.PhoneNumberListId));
        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingCoalition>(t =>
            t.Amount == (Currency)amount &&
            t.FilerId == filerId &&
            t.ContactId == newFilerContactId));
    }

    /// <summary>
    /// Test to verify that AddTransactionToFiling is called after creating a payment made to lobbying coalition.
    /// </summary>
    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_ShouldAddTransactionToFiling_AfterCreatingTransaction()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = 1000m;
        long filerId = 456L;
        long createdTransactionId = 789L;

        PaymentMadeToLobbyingCoalition createdTransaction = new()
        {
            Id = createdTransactionId,
            Amount = (Currency)amount,
            FilerId = filerId
        };

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingCoalition>())
            .Returns(createdTransaction);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingCoalition(
            disclosureFilingId, amount, filerId);

        // Assert
        Assert.That(result.Id, Is.EqualTo(createdTransaction.Id));

        // Verify the transaction was created
        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingCoalition>(t =>
            t.Amount == (Currency)amount &&
            t.FilerId == filerId));

        // Verify the transaction was added to the filing
        await _transactionRepository.Received(1).AddTransactionToFiling(createdTransactionId, disclosureFilingId);

        // Verify the correct order of calls
        Received.InOrder(() =>
        {
            _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingCoalition>());
            _ = _transactionRepository.AddTransactionToFiling(createdTransactionId, disclosureFilingId);
        });
    }

    /// <summary>
    /// Test to verify validation failures are correctly returned from the Decisions service
    /// </summary>
    [Test]
    public async Task CreatePaymentMadeToLobbyingCoalition_WithValidationError_ShouldReturnErrorDetails()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = -10m;
        long filerId = 123L;

        WorkFlowError validationError = new("PeriodAmount", "ErrGlobal0002", "Validation", "{{Field Name}} is invalid");

        // Configure the decisions service to return a validation error
        _ = _decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingCoalitionDs, WorkFlowError>(
            Arg.Is(DecisionsWorkflow.PaymentMadeToLobbyingCoalitionRuleset),
            Arg.Any<PaymentMadeToLobbyingCoalitionDs>(),
            Arg.Any<bool>())
            .Returns(validationError);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingCoalition(disclosureFilingId, amount, filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.Null);
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
            Assert.That(result.ValidationErrors[0].Message, Is.EqualTo("{{Field Name}} is invalid"));
        });

        // Verify the transaction repository was never called to create a transaction
        _ = await _transactionRepository.DidNotReceive().Create(Arg.Any<PaymentMadeToLobbyingCoalition>());
    }

    [Test]
    public async Task GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling_ReturnsExpectedResults()
    {
        // Arrange
        long filingId = 123;
        DateTime legislativeStartDate = new(2024, 1, 1, 0, 0, 0, 0);
        List<PaymentReceiveLobbyingCoalitionResponse> expectedTransactions =
        [
            new()
            {
                AmountThisPeriod = 500.00m,
                CoalitionName = "Coalition A",
                CumulativeAmount = 1500.00m,
                Id = 1,
            },
            new()
            {
                AmountThisPeriod = 250.00m,
                CoalitionName = "John Doe",
                CumulativeAmount = 750.00m,
                Id = 2
            },
        ];

        _filingSvc.GetLegislativeStartDateForFiling(filingId).Returns(Task.FromResult(legislativeStartDate));
        _transactionHelperSvc.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId, legislativeStartDate)
            .Returns(Task.FromResult<IEnumerable<PaymentReceiveLobbyingCoalitionResponse>>(expectedTransactions));

        // Act
        IEnumerable<PaymentReceiveLobbyingCoalitionResponse> result = await _transactionSvc.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(expectedTransactions.Count));
            Assert.That(result, Is.EqualTo(expectedTransactions));
        });
    }

    [Test]
    public async Task GetPaymentReceivedLobbyingCoalitionTransactionById_ReturnsExpectedResults()
    {
        // Arrange
        var transactionId = 1;
        var paymentResponse = new PaymentReceiveLobbyingCoalition()
        {
            Amount = (Currency)10,
            Id = transactionId,
            Contact = new IndividualContact()
            {
                Id = transactionId,
            }
        };

        _transactionRepository.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId)
            .Returns(Task.FromResult<PaymentReceiveLobbyingCoalition?>(paymentResponse));

        // Act
        var result = await _transactionSvc.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.Contact!.Id, Is.EqualTo(paymentResponse.Contact.Id));
        });
    }

    [Test]
    public void GetPaymentReceivedLobbyingCoalitionTransactionById_ThrowError()
    {
        // Arrange
        long transactionId = 789;
        _transactionRepository.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId)
            .Returns(Task.FromResult<PaymentReceiveLobbyingCoalition?>(null));

        // Act & Assert
        var exception = Assert.ThrowsAsync<KeyNotFoundException>(
            async () => await _transactionSvc.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId));
        Assert.That(exception.Message, Is.EqualTo($"Payment received lobbying Coalition transaction {transactionId}"));
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_ShouldReturnValidResponse_WhenNoErrors()
    {
        // Arrange
        long filingId = 123;
        List<WorkFlowError> decisionsResponse = [];
        DateTime mockLegislativeStartDate = new(2024, 1, 1, 0, 0, 0, 0);
        List<PaymentReceiveLobbyingCoalitionResponse> mockTransactions =
        [
            new() { CoalitionName = "Test Coalition", AmountThisPeriod = 100, CumulativeAmount = 100 },
            new() { CoalitionName = "John Doe", AmountThisPeriod = 200, CumulativeAmount = 200 }
        ];

        _filingSvc.GetLegislativeStartDateForFiling(filingId).Returns(Task.FromResult(mockLegislativeStartDate));
        _transactionHelperSvc.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId, mockLegislativeStartDate)
            .Returns(Task.FromResult(mockTransactions.AsEnumerable()));

        _ = _decisionsSvc
            .InitiateWorkflow<PaymentReceiveLobbyingCoalitionDs, WorkFlowError>(
                DecisionsWorkflow.PaymentReceiveLobbyingCoalitionRuleset,
                Arg.Any<PaymentReceiveLobbyingCoalitionDs>(),
                true)
            .Returns(Task.FromResult<WorkFlowError>(null!)); // No errors

        // Act
        ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse result = await _transactionSvc.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(filingId, Is.EqualTo(result.Id));
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling_ShouldReturnInvalidResponse_WhenErrorsExist()
    {
        // Arrange
        long filingId = 123;
        WorkFlowError validationError = new("PeriodAmount", "ErrGlobal0002", "Validation", "{{Field Name}} is invalid");
        DateTime mockLegislativeStartDate = new(2024, 1, 1, 0, 0, 0, 0);
        List<PaymentReceiveLobbyingCoalitionResponse> mockTransactions =
        [
            new() { CoalitionName = "Test Coalition", AmountThisPeriod = 100, CumulativeAmount = 100 },
            new() { CoalitionName = "John Doe", AmountThisPeriod = 200, CumulativeAmount = 200 }
        ];

        _ = _filingSvc.GetLegislativeStartDateForFiling(filingId).Returns(Task.FromResult(mockLegislativeStartDate));
        _ = _transactionHelperSvc.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId, mockLegislativeStartDate)
            .Returns(Task.FromResult(mockTransactions.AsEnumerable()));

        _ = _decisionsSvc
            .InitiateWorkflow<PaymentReceiveLobbyingCoalitionDs, WorkFlowError>(
                DecisionsWorkflow.PaymentReceiveLobbyingCoalitionRuleset,
                Arg.Any<PaymentReceiveLobbyingCoalitionDs>(),
                true)
            .Returns(validationError);

        // Act
        ValidatedPaymentsReceivedLobbyingCoalitionTransactionResponse result = await _transactionSvc.ValidatePaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(filingId, Is.EqualTo(result.Id));
        });
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
    }

    [Test]
    public async Task CreatePaymentReceivedByLobbyingCoalition_ShouldReturnTransactionId_WhenTransactionIsCreatedSuccessfully()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = 1000m;
        long filerId = 456L;
        long filerContactId = 789L;
        long expectedTransactionId = 101112L;

        PaymentReceiveLobbyingCoalition transaction = new()
        {
            Id = expectedTransactionId,
            Amount = (Currency)amount,
            FilerId = filerId,
            ContactId = filerContactId
        };

        _ = _decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionPaymentAmountDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentReceiveLobbyingCoalitionPaymentAmountRuleset, Arg.Any<PaymentReceiveLobbyingCoalitionPaymentAmountDs>(), Arg.Any<bool>())
        .Returns([]);

        _ = _transactionRepository.Create(Arg.Any<PaymentReceiveLobbyingCoalition>())
            .Returns(transaction);

        var request = new PaymentReceiveLobbyingCoalitionRequestDto
        {
            Amount = (Currency)amount,
            FilerId = filerId,
            DisclosureFilingId = disclosureFilingId,
            ContactId = filerContactId
        };

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentReceivedByLobbyingCoalition(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentReceiveLobbyingCoalition>(t =>
            t.Amount == (Currency)amount &&
            t.FilerId == filerId &&
            t.ContactId == filerContactId));

        await _transactionRepository.Received(1).AddTransactionToFiling(expectedTransactionId, disclosureFilingId);
    }

    [Test]
    public async Task CreatePaymentReceivedByLobbyingCoalition_ShouldReturnInvalidResponse_WhenValidationFails()
    {
        // Arrange
        long disclosureFilingId = 123L;
        decimal amount = 1000m;
        long filerId = 456L;
        long filerContactId = 789L;

        List<WorkFlowError> errors =
        [
            new("", "", "", "")
        ];

        _ = _decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionPaymentAmountDs, List<WorkFlowError>>(
                DecisionsWorkflow.PaymentReceiveLobbyingCoalitionPaymentAmountRuleset, Arg.Any<PaymentReceiveLobbyingCoalitionPaymentAmountDs>(), Arg.Any<bool>())
            .Returns(errors);

        var request = new PaymentReceiveLobbyingCoalitionRequestDto
        {
            Amount = (Currency)amount,
            FilerId = filerId,
            DisclosureFilingId = disclosureFilingId,
            ContactId = filerContactId
        };

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentReceivedByLobbyingCoalition(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.Null);
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EquivalentTo(errors));
        });

        // Ensure Create/AddTransactionToFiling are not called
        await _transactionRepository.DidNotReceive().Create(Arg.Any<PaymentReceiveLobbyingCoalition>());
        await _transactionRepository.DidNotReceive().AddTransactionToFiling(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task EditPaymentReceivedByLobbyingCoalition_ShouldReturnTransactionId_WhenTransactionIsUpdatedSuccessfully()
    {
        // Arrange
        long transactionId = 101112L;
        decimal amount = 2000m;
        long filerId = 456L;
        long contactId = 789L;

        var request = new PaymentReceiveLobbyingCoalitionRequestDto
        {
            Amount = (Currency)amount,
            FilerId = filerId,
            DisclosureFilingId = null,
            ContactId = contactId
        };

        PaymentReceiveLobbyingCoalition existingTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)1000m,
            FilerId = filerId,
            ContactId = 555L
        };

        _ = _decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionPaymentAmountDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentReceiveLobbyingCoalitionPaymentAmountRuleset,
            Arg.Any<PaymentReceiveLobbyingCoalitionPaymentAmountDs>(),
            Arg.Any<bool>())
        .Returns([]);

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(existingTransaction));

        PaymentReceiveLobbyingCoalition updatedTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)amount,
            FilerId = filerId,
            ContactId = contactId
        };
        _transactionRepository.Update(Arg.Any<PaymentReceiveLobbyingCoalition>()).Returns(updatedTransaction);

        // Act
        TransactionResponseDto result = await _transactionSvc.EditPaymentReceivedByLobbyingCoalition(transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        await _transactionRepository.Received(1).Update(Arg.Is<PaymentReceiveLobbyingCoalition>(t =>
            t.Id == transactionId &&
            t.Amount == (Currency)amount &&
            t.FilerId == filerId &&
            t.ContactId == contactId));
    }

    [Test]
    public async Task EditPaymentReceivedByLobbyingCoalition_ShouldReturnInvalidResponse_WhenValidationFails()
    {
        // Arrange
        long transactionId = 101112L;
        decimal amount = -500m; // Invalid amount
        long filerId = 456L;
        long contactId = 789L;

        var request = new PaymentReceiveLobbyingCoalitionRequestDto
        {
            Amount = (Currency)amount,
            FilerId = filerId,
            DisclosureFilingId = null,
            ContactId = contactId
        };

        PaymentReceiveLobbyingCoalition existingTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)1000m,
            FilerId = filerId,
            ContactId = 555L
        };

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(existingTransaction));

        List<WorkFlowError> errors =
        [
            new("Amount", "ERR001", "Validation", "Amount must be positive")
        ];

        _ = _decisionsSvc.InitiateWorkflow<PaymentReceiveLobbyingCoalitionPaymentAmountDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentReceiveLobbyingCoalitionPaymentAmountRuleset,
            Arg.Any<PaymentReceiveLobbyingCoalitionPaymentAmountDs>(),
            Arg.Any<bool>())
        .Returns(errors);

        // Act
        TransactionResponseDto result = await _transactionSvc.EditPaymentReceivedByLobbyingCoalition(transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId)); // ID should still be set
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EquivalentTo(errors));
        });

        await _transactionRepository.DidNotReceive().Update(Arg.Any<PaymentReceiveLobbyingCoalition>());
    }

    [Test]
    public void EditPaymentReceivedByLobbyingCoalition_ShouldThrowNotFoundException_WhenTransactionNotFound()
    {
        // Arrange
        long transactionId = 999L; // Non-existent ID
        var request = new PaymentReceiveLobbyingCoalitionRequestDto
        {
            Amount = (Currency)1000m,
            FilerId = 456L,
            ContactId = 789L
        };

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(null));

        // Act & Assert
        var exception = Assert.ThrowsAsync<NotFoundException>(async () =>
            await _transactionSvc.EditPaymentReceivedByLobbyingCoalition(transactionId, request));

        Assert.That(exception!.Message, Is.EqualTo($"Transaction {transactionId} not found."));
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_ShouldReturnInvalidResponse_WhenValidationFails()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            FilingId = 123L,
            FilerId = 456L,
            FeesAndRetainersAmount = 1000m,
            ReimbursementOfExpensesAmount = 500m,
            AdvancesOrOtherPaymentsAmount = 250m,
            AdvancesOrOtherPaymentsExplanation = "Test explanation"
        };

        List<WorkFlowError> validationErrors =
        [
            new("FeesRetainers", "Err001", "Validation", "Fees and Retainers amount is invalid.")
        ];

        _ = _decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingFirmsDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentMadeToLobbyingFirmsRuleset,
            Arg.Any<PaymentMadeToLobbyingFirmsDs>(),
            true)
            .Returns(validationErrors);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.Null);
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EquivalentTo(validationErrors));
        });

        await _transactionRepository.DidNotReceive().Create(Arg.Any<PaymentMadeToLobbyingFirms>());
        await _transactionRepository.DidNotReceive().AddTransactionToFiling(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_ShouldReturnValidResponse_WhenValidationPasses()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            FilingId = 123L,
            FilerId = 456L,
            FeesAndRetainersAmount = 1000m,
            ReimbursementOfExpensesAmount = 500m,
            AdvancesOrOtherPaymentsAmount = 250m,
            AdvancesOrOtherPaymentsExplanation = "Test explanation"
        };

        PaymentMadeToLobbyingFirms transaction = new()
        {
            Id = 789L,
            FilerId = request.FilerId,
            FeesAndRetainersAmount = (Currency)request.FeesAndRetainersAmount,
            ReimbursementOfExpensesAmount = (Currency)request.ReimbursementOfExpensesAmount,
            AdvancesOrOtherPaymentsAmount = (Currency)request.AdvancesOrOtherPaymentsAmount,
            AdvancesOrOtherPaymentsExplanation = request.AdvancesOrOtherPaymentsExplanation,
            Amount = (Currency)(request.FeesAndRetainersAmount + request.ReimbursementOfExpensesAmount + request.AdvancesOrOtherPaymentsAmount)
        };

        _ = _decisionsSvc.InitiateWorkflow<PaymentMadeToLobbyingFirmsDs, List<WorkFlowError>>(
            DecisionsWorkflow.PaymentMadeToLobbyingFirmsRuleset,
            Arg.Any<PaymentMadeToLobbyingFirmsDs>(),
            true)
            .Returns([]);

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingFirms>())
            .Returns(transaction);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transaction.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingFirms>(t =>
            t.FilerId == request.FilerId &&
            t.FeesAndRetainersAmount == (Currency)request.FeesAndRetainersAmount &&
            t.ReimbursementOfExpensesAmount == (Currency)request.ReimbursementOfExpensesAmount &&
            t.AdvancesOrOtherPaymentsAmount == (Currency)request.AdvancesOrOtherPaymentsAmount &&
            t.AdvancesOrOtherPaymentsExplanation == request.AdvancesOrOtherPaymentsExplanation &&
            t.Amount == (Currency)(request.FeesAndRetainersAmount + request.ReimbursementOfExpensesAmount + request.AdvancesOrOtherPaymentsAmount)));

        await _transactionRepository.Received(1).AddTransactionToFiling(transaction.Id, request.FilingId);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_WithExistingFirmContact_ShouldUseExistingContact()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            FilingId = 123L,
            FilerId = 456L,
            RegistrationFilingId = 789L,
            FeesAndRetainersAmount = 1000m,
            ReimbursementOfExpensesAmount = 500m,
            AdvancesOrOtherPaymentsAmount = 250m
        };

        long firmFilerId = 555L;
        long existingContactId = 666L;
        long expectedTransactionId = 999L;

        LobbyingFirm selectedFirm = new()
        {
            FilerId = firmFilerId,
            Name = "Test Lobbying Firm",
            StatusId = 1,
            AddressListId = 101L,
            PhoneNumberListId = 102L,
            Email = "<EMAIL>",
            LegislativeSessionId = 1L,
            ResponsibleOfficerTitle = "Director"
        };

        OrganizationContact existingFilerContact = new()
        {
            Id = existingContactId,
            FilerId = request.FilerId,
            ContactFilerId = firmFilerId,
            OrganizationName = "Existing Organization"
        };

        PaymentMadeToLobbyingFirms transaction = new()
        {
            Id = expectedTransactionId,
            FilerId = request.FilerId,
            ContactId = existingContactId,
            FeesAndRetainersAmount = (Currency)request.FeesAndRetainersAmount,
            ReimbursementOfExpensesAmount = (Currency)request.ReimbursementOfExpensesAmount,
            AdvancesOrOtherPaymentsAmount = (Currency)request.AdvancesOrOtherPaymentsAmount,
            Amount = (Currency)(request.FeesAndRetainersAmount + request.ReimbursementOfExpensesAmount + request.AdvancesOrOtherPaymentsAmount)
        };

        _ = _lobbyingFirmRegistrationSvc.GetLobbyingFirm(request.RegistrationFilingId.Value)
            .Returns(Task.FromResult<LobbyingFirmResponseDto?>(new LobbyingFirmResponseDto(selectedFirm)));

        _ = _filerContactSvc.GetFilerContactByFilerIdAndContactFilerId(request.FilerId, firmFilerId)
            .Returns(existingFilerContact);

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingFirms>())
            .Returns(transaction);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _lobbyingFirmRegistrationSvc.Received(1).GetLobbyingFirm(request.RegistrationFilingId.Value);
        _ = await _filerContactSvc.Received(1).GetFilerContactByFilerIdAndContactFilerId(request.FilerId, firmFilerId);
        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingFirms>(t =>
            t.FilerId == request.FilerId &&
            t.ContactId == existingContactId));
        await _transactionRepository.Received(1).AddTransactionToFiling(expectedTransactionId, request.FilingId);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_WithNoExistingContactRelationship_ShouldCreateNewContact()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            FilingId = 123L,
            FilerId = 456L,
            RegistrationFilingId = 789L,
            FeesAndRetainersAmount = 1000m,
            ReimbursementOfExpensesAmount = 500m
        };

        long firmFilerId = 555L;
        long newContactId = 777L;
        long expectedTransactionId = 999L;
        long addressListId = 101L;
        long phoneNumberListId = 102L;

        LobbyingFirm selectedFirm = new()
        {
            FilerId = firmFilerId,
            Name = "Test Lobbying Firm",
            StatusId = 1,
            AddressListId = addressListId,
            PhoneNumberListId = phoneNumberListId,
            Email = "<EMAIL>",
            LegislativeSessionId = 1L,
            ResponsibleOfficerTitle = "Director"
        };

        PaymentMadeToLobbyingFirms transaction = new()
        {
            Id = expectedTransactionId,
            FilerId = request.FilerId,
            ContactId = newContactId,
            FeesAndRetainersAmount = (Currency)request.FeesAndRetainersAmount,
            ReimbursementOfExpensesAmount = (Currency)request.ReimbursementOfExpensesAmount,
            Amount = (Currency)(request.FeesAndRetainersAmount + request.ReimbursementOfExpensesAmount)
        };

        _ = _lobbyingFirmRegistrationSvc.GetLobbyingFirm(request.RegistrationFilingId.Value)
            .Returns(new LobbyingFirmResponseDto(selectedFirm));

        _ = _filerContactSvc.GetFilerContactByFilerIdAndContactFilerId(request.FilerId, firmFilerId)
            .Returns((FilerContact?)null);

        _ = _filerContactSvc.CreateFilerContact(Arg.Any<OrganizationContact>())
            .Returns(newContactId);

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingFirms>())
            .Returns(transaction);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _lobbyingFirmRegistrationSvc.Received(1).GetLobbyingFirm(request.RegistrationFilingId.Value);
        _ = await _filerContactSvc.Received(1).GetFilerContactByFilerIdAndContactFilerId(request.FilerId, firmFilerId);
        _ = await _filerContactSvc.Received(1).CreateFilerContact(Arg.Is<OrganizationContact>(c =>
            c.FilerId == request.FilerId &&
            c.ContactFilerId == firmFilerId &&
            c.OrganizationName == selectedFirm.Name &&
            c.AddressListId == selectedFirm.AddressListId &&
            c.PhoneNumberListId == selectedFirm.PhoneNumberListId));
        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingFirms>(t =>
            t.FilerId == request.FilerId &&
            t.ContactId == newContactId));
        await _transactionRepository.Received(1).AddTransactionToFiling(expectedTransactionId, request.FilingId);
    }

    [Test]
    public async Task CreatePaymentMadeToLobbyingFirmsTransaction_WithPartialAmounts_ShouldCreateTransactionWithCorrectTotal()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            FilingId = 123L,
            FilerId = 456L,
            FilerContactId = 789L,
            FeesAndRetainersAmount = 1000m,
            // ReimbursementOfExpensesAmount is null
            AdvancesOrOtherPaymentsAmount = 250m,
            AdvancesOrOtherPaymentsExplanation = "Test explanation"
        };

        decimal expectedTotalAmount = 1250m; // 1000 + 0 + 250

        PaymentMadeToLobbyingFirms transaction = new()
        {
            Id = 999L,
            FilerId = request.FilerId,
            ContactId = request.FilerContactId,
            FeesAndRetainersAmount = (Currency)request.FeesAndRetainersAmount,
            AdvancesOrOtherPaymentsAmount = (Currency)request.AdvancesOrOtherPaymentsAmount,
            AdvancesOrOtherPaymentsExplanation = request.AdvancesOrOtherPaymentsExplanation,
            Amount = (Currency)expectedTotalAmount
        };

        _ = _transactionRepository.Create(Arg.Any<PaymentMadeToLobbyingFirms>())
            .Returns(transaction);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transaction.Id));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        _ = await _transactionRepository.Received(1).Create(Arg.Is<PaymentMadeToLobbyingFirms>(t =>
            t.Amount == (Currency)expectedTotalAmount));
        await _transactionRepository.Received(1).AddTransactionToFiling(transaction.Id, request.FilingId);
    }

    [Test]
    public void CreatePaymentMadeToLobbyingFirmsTransaction_WithInvalidRegistrationFilingId_ShouldThrowException()
    {
        // Arrange
        PaymentMadeToLobbyingFirmsRequestDto request = new()
        {
            FilingId = 123L,
            FilerId = 456L,
            RegistrationFilingId = 789L,
            FeesAndRetainersAmount = 1000m
        };

        _ = _lobbyingFirmRegistrationSvc.GetLobbyingFirm(request.RegistrationFilingId.Value)
            .Returns(Task.FromResult<LobbyingFirmResponseDto?>(null));

        // Act & Assert
        _ = Assert.ThrowsAsync<InvalidOperationException>(() =>
            _transactionSvc.CreatePaymentMadeToLobbyingFirmsTransaction(request));

        // Verify
        _ = _transactionRepository.DidNotReceive().Create(Arg.Any<PaymentMadeToLobbyingFirms>());
        _ = _transactionRepository.DidNotReceive().AddTransactionToFiling(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    [TestCase(false, false, false, false, false, false, TestName = "All collections null")]
    [TestCase(true, false, true, false, true, false, TestName = "All collections empty")]
    [TestCase(true, true, true, true, true, true, TestName = "All collections populated")]
    [TestCase(true, true, false, false, false, false, TestName = "Only AssemblyBills populated")]
    [TestCase(false, false, true, true, false, false, TestName = "Only SenateBills populated")]
    [TestCase(false, false, false, false, true, true, TestName = "Only AdministrativeActions populated")]
    public async Task CreateOtherPaymentToInfluence_ShouldReturnTransaction_WhenTransactionIsCreatedSuccessfully(
        [Values] bool includeAssemblyBill,
        [Values] bool populateAssemblyBill,
        [Values] bool includeSenateBill,
        [Values] bool populateSenateBill,
        [Values] bool includeAdminActions,
        [Values] bool populateAdminActions)
    {
        // Arrange
        long disclosureFilingId = 456L;
        decimal amount = 250.75m;
        long filerId = 123L;
        long contactId = 789L;
        long paymentCodeId = 1011L;
        long expectedTransactionId = 2024L;

        OtherInfluencePaymentDto dto = new()
        {
            FilerId = filerId,
            FilingId = disclosureFilingId,
            ContactId = contactId,
            PaymentCodeId = paymentCodeId,
            PaymentCodeDescription = "Test note",
            Amount = amount,
            TransactionDate = new DateTime(2023, 1, 1, 0, 0, 0, 0),
            OtherActionsLobbied = "Other",
        };

        if (includeAssemblyBill)
        {
            dto.AssemblyBills = populateAssemblyBill
                ? new() {
                    new() { BillId = 1 },
                    new() { BillId = null }
                }
                : new();
        }
        else
        {
            dto.AssemblyBills = null!;
        }

        if (includeSenateBill)
        {
            dto.SenateBills = populateSenateBill
                ? new() {
                    new() { BillId = 1 },
                    new() { BillId = null }
                }
                : new();
        }
        else
        {
            dto.SenateBills = null!;
        }

        if (includeAdminActions)
        {
            dto.AdministrativeActions = populateAdminActions
                ? new() {
                    new() { AgencyId = 1, AdministrativeAction = "Test Action", AgencyDescription = "Test Agency" },
                    new() { AgencyId = null, AdministrativeAction = null, AgencyDescription = null }
                }
                : new();
        }
        else
        {
            dto.AdministrativeActions = null!;
        }

        OtherPaymentsToInfluence transaction = new()
        {
            Id = expectedTransactionId,
            FilerId = filerId,
            ContactId = contactId,
            PaymentCodeId = paymentCodeId,
            PaymentCodeDescription = dto.PaymentCodeDescription,
            Amount = (Currency)amount,
            TransactionDate = dto.TransactionDate,
            OtherActionsLobbied = dto.OtherActionsLobbied
        };

        _transactionRepository.Create(Arg.Any<OtherPaymentsToInfluence>())
            .Returns(transaction);

        // Act
        Transaction result = await _transactionSvc.CreateOtherPaymentToInfluence(dto);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.FilerId, Is.EqualTo(dto.FilerId));
            Assert.That(result.ContactId, Is.EqualTo(dto.ContactId));
            Assert.That(result.Amount, Is.EqualTo((Currency)dto.Amount));
        });

        await _transactionRepository.Received(1).AddTransactionToFiling(expectedTransactionId, disclosureFilingId);
    }

    [Test]
    public async Task GetOtherPaymentsCumulativeAmountForFilingAndContact_ShouldReturnAnAmount_WhenTransactionsExist()
    {
        // Arrange
        long filingId = 123;
        long contactId = 321;
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);

        CumulativeAmountResponse expectedCumulativeAmount = new() { CumulativeAmount = (Currency)3000 };

        _filingSvc
            .GetLegislativeStartDateForFiling(filingId)
            .Returns(Task.FromResult(legislativeStartDate));

        _transactionRepository
            .GetOtherPaymentsCumulativeAmountForFilingAndContact(filingId, contactId, legislativeStartDate)
            .Returns(Task.FromResult(expectedCumulativeAmount));

        // Act
        CumulativeAmountResponse result = await _transactionSvc.GetOtherPaymentsCumulativeAmountForFilingAndContact(filingId, contactId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(expectedCumulativeAmount));
        });

        await _transactionRepository
            .Received(1)
            .GetOtherPaymentsCumulativeAmountForFilingAndContact(filingId, contactId, legislativeStartDate);
    }

    [Test]
    public async Task EditOtherPaymentToInfluence_WithValidData_ShouldUpdateTransaction()
    {
        // Arrange
        long transactionId = 1;
        decimal amount = 2000m;
        DateTime transactionDate = _dateNow;
        long filerId = 123;
        long contactId = 456;
        long paymentCodeId = 789;
        string paymentCodeDescription = "Updated Description";
        string otherActionsLobbied = "Updated Other Actions";

        OtherInfluencePaymentDto request = new()
        {
            Amount = amount,
            TransactionDate = transactionDate,
            FilerId = filerId,
            ContactId = contactId,
            PaymentCodeId = paymentCodeId,
            PaymentCodeDescription = paymentCodeDescription,
            OtherActionsLobbied = otherActionsLobbied,
            AssemblyBills = [new() { BillId = 101 }],
            SenateBills = [new() { BillId = 201 }],
            AdministrativeActions = [new() { AgencyId = 301, AdministrativeAction = "Test Action" }]
        };

        OtherPaymentsToInfluence existingTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)1000m,
            TransactionDate = _dateNow.AddDays(-1),
            FilerId = 111,
            ContactId = 222,
            PaymentCodeId = 333,
            PaymentCodeDescription = "Old Description",
            OtherActionsLobbied = "Old Other Actions",
            ActionsLobbied =
            [
                new() { BillId = 100 },
                new() { AgencyId = 300 }
            ]
        };

        OtherPaymentsToInfluence updatedTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)amount,
            TransactionDate = transactionDate,
            FilerId = filerId,
            ContactId = contactId,
            PaymentCodeId = paymentCodeId,
            PaymentCodeDescription = paymentCodeDescription,
            OtherActionsLobbied = otherActionsLobbied
        };

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(existingTransaction));
        _transactionRepository.Update(Arg.Any<OtherPaymentsToInfluence>()).Returns(updatedTransaction);

        // Act
        Transaction result = await _transactionSvc.EditOtherPaymentToInfluence(transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(transactionId));
        });

        await _transactionRepository.Received(1).Update(Arg.Is<OtherPaymentsToInfluence>(t =>
            t.Id == transactionId &&
            t.Amount == (Currency)amount &&
            t.TransactionDate == transactionDate &&
            t.FilerId == filerId &&
            t.ContactId == contactId &&
            t.PaymentCodeId == paymentCodeId &&
            t.PaymentCodeDescription == paymentCodeDescription &&
            t.OtherActionsLobbied == otherActionsLobbied &&
            t.ActionsLobbied!.Count == 3 &&
            t.ActionsLobbied!.Any(a => a!.BillId == 101) &&
            t.ActionsLobbied!.Any(a => a!.BillId == 201) &&
            t.ActionsLobbied!.Any(a => a!.AgencyId == 301 && a.AdministrativeAction == "Test Action")
        ));
    }

    [Test]
    public void EditOtherPaymentToInfluence_WithNonexistentTransaction_ShouldThrowKeyNotFoundException()
    {
        // Arrange
        long transactionId = 999;
        OtherInfluencePaymentDto request = new()
        {
            Amount = 1000m,
            TransactionDate = _dateNow,
            FilerId = 123
        };

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(null));

        // Act & Assert
        KeyNotFoundException? exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _transactionSvc.EditOtherPaymentToInfluence(transactionId, request));

        Assert.That(exception!.Message, Is.EqualTo($"Transaction with ID {transactionId} not found."));
    }

    [Test]
    public void EditOtherPaymentToInfluence_WithWrongTransactionType_ShouldThrowInvalidOperationException()
    {
        // Arrange
        long transactionId = 1;
        OtherInfluencePaymentDto request = new()
        {
            Amount = 1000m,
            TransactionDate = _dateNow,
            FilerId = 123
        };

        // Return a different transaction type (not OtherPaymentsToInfluence)
        Expenditure wrongTypeTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)500m,
            TransactionDate = _dateNow,
            FilerId = 123,
            Purpose = "Test Expenditure"
        };

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(wrongTypeTransaction));

        // Act & Assert
        InvalidOperationException? exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _transactionSvc.EditOtherPaymentToInfluence(transactionId, request));

        Assert.That(exception!.Message, Is.EqualTo("Transaction is not of type OtherPaymentsToInfluence"));
    }

    [Test]
    public async Task EditOtherPaymentToInfluence_WithEmptyActionCollections_ShouldClearExistingActions()
    {
        // Arrange
        long transactionId = 1;

        OtherInfluencePaymentDto request = new()
        {
            Amount = 1000m,
            TransactionDate = _dateNow,
            FilerId = 123,
            ContactId = 456,
            PaymentCodeId = 789,
            // Set empty collections (not null)
            AssemblyBills = [],
            SenateBills = [],
            AdministrativeActions = []
        };

        OtherPaymentsToInfluence existingTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)500m,
            TransactionDate = _dateNow.AddDays(-1),
            FilerId = 111,
            ActionsLobbied =
        [
            new() { BillId = 100 },
            new() { AgencyId = 300 }
        ]
        };

        OtherPaymentsToInfluence updatedTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)1000m,
            FilerId = 123
        };

        _transactionRepository.FindById(transactionId).Returns(Task.FromResult<Transaction?>(existingTransaction));
        _transactionRepository.Update(Arg.Any<OtherPaymentsToInfluence>()).Returns(updatedTransaction);

        // Act
        Transaction result = await _transactionSvc.EditOtherPaymentToInfluence(transactionId, request);

        // Assert
        Assert.That(result, Is.Not.Null);

        await _transactionRepository.Received(1).Update(Arg.Is<OtherPaymentsToInfluence>(t =>
            t.ActionsLobbied == null || t.ActionsLobbied.Count == 0
        ));
    }

    /// <summary>
    /// Test to ensure GetLobbyistCampaignContributionTransactionById should return a valid transaction for a specific transaction.
    /// </summary>
    [Test]
    public async Task GetLobbyistCampaignContributionTransactionById_ShouldReturnTransaction_WhenTransactionExist()
    {
        // Arrange
        long transactionId = 1;
        LobbyingCampaignContribution expectedTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)150,
            TransactionDate = _dateNow,
            CreatedBy = 1,
            ModifiedBy = 1,
            NonCommitteeRecipientName = "John"
        };

        _transactionRepository
            .GetLobbyistCampaignContributionTransactionById(transactionId)
            .Returns(Task.FromResult<LobbyingCampaignContribution?>(expectedTransaction));

        // Act
        LobbyingCampaignContribution? result = await _transactionSvc.GetLobbyistCampaignContributionTransactionById(transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(expectedTransaction.Id));
            Assert.That(result.Amount, Is.EqualTo(expectedTransaction.Amount));
            Assert.That(result.NonCommitteeRecipientName, Is.EqualTo(expectedTransaction.NonCommitteeRecipientName));
        });

        await _transactionRepository.Received(1).GetLobbyistCampaignContributionTransactionById(transactionId);
    }

    [Test]
    public async Task GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId_ShouldReturnTransaction_WhenTransactionExist()
    {
        // Arrange
        long transactionId = 1;
        PaymentMadeToLobbyingCoalition expectedTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)150,
            TransactionDate = _dateNow,
            ContactId = 1,
            Contact = new OrganizationContact()
            {
                Id = 1,
                OrganizationName = "Org0",
                FilerId = 1,
            },
            CreatedBy = 1,
            ModifiedBy = 1,
        };

        _transactionRepository
            .GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(transactionId)
            .Returns(Task.FromResult<PaymentMadeToLobbyingCoalition?>(expectedTransaction));

        // Act
        PaymentMadeToLobbyingCoalitionResponse? result = await _transactionSvc.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(expectedTransaction.Id));
            Assert.That(result.AmountThisPeriod, Is.EqualTo(expectedTransaction.Amount.Value));
        });

        await _transactionRepository.Received(1).GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(transactionId);
    }

    [Test]
    public async Task EditLobbyistCampaignContribution_LobbyistCampaignContributionRequestDto_UpdatesTransaction()
    {

        LobbyistCampaignContributionRequestDto dto = new()
        {
            Id = 1,
            Amount = 500.00m,
            TransactionDate = _dateNow,
            FilerId = 123,
            SeparateAccountName = "Account A",
            IsRecipientCommittee = true,
            RecipientCommitteeFilerId = 456,
            IsContributorFiler = true,
            ContributorFilerId = 789
        };

        LobbyingCampaignContribution existingContribution = new()
        {
            Id = 1,
            Amount = (Currency)150,
            TransactionDate = DateTime.UtcNow,
            CreatedBy = 1,
            ModifiedBy = 1,
            NonCommitteeRecipientName = "John"
        };

        LobbyingCampaignContribution updatedContribution = new()
        {
            Id = 1,
            Amount = (Currency)12
            // Updated version returned by repo
        };

        _transactionRepository
            .GetLobbyistCampaignContributionTransactionById(1)
            .Returns(Task.FromResult<LobbyingCampaignContribution?>(existingContribution));


        _transactionRepository.Update(Arg.Any<LobbyingCampaignContribution>())
            .Returns(updatedContribution);

        // Act
        Transaction result = await _transactionSvc.EditLobbyistCampaignContribution(dto);

        // Assert
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(existingContribution.Id));
        });
    }

    [Test]
    public async Task EditLobbyistCampaignContribution_LobbyistCampaignContributionRequestDto_UpdatesTransaction_WhenNonCommiteeRecipient()
    {
        // Arrange
        LobbyistCampaignContributionRequestDto dto = new()
        {
            Id = 1,
            Amount = 500.00m,
            TransactionDate = _dateNow,
            FilerId = 123,
            SeparateAccountName = "Account A",
            IsRecipientCommittee = false,
            NonCommitteeRecipientName = "Recipient",
            IsContributorFiler = false,
            NonFilerContributorName = "Contributor"
        };

        LobbyingCampaignContribution existingContribution = new()
        {
            Id = 1,
            Amount = (Currency)150,
            TransactionDate = DateTime.UtcNow,
            CreatedBy = 1,
            ModifiedBy = 1,
            NonCommitteeRecipientName = "John"
        };

        LobbyingCampaignContribution updatedContribution = new()
        {
            Id = 1,
            Amount = (Currency)12
        };

        _transactionRepository
            .GetLobbyistCampaignContributionTransactionById(1)
            .Returns(Task.FromResult<LobbyingCampaignContribution?>(existingContribution));


        _transactionRepository.Update(Arg.Any<LobbyingCampaignContribution>())
            .Returns(updatedContribution);

        // Act
        Transaction result = await _transactionSvc.EditLobbyistCampaignContribution(dto);

        // Assert
        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(existingContribution.Id));
        });
    }

    [Test]
    public void EditLobbyistCampaignContribution_WithNullId_ThrowsBadRequestException()
    {
        // Arrange
        LobbyistCampaignContributionRequestDto dto = new()
        {
            Id = null, // triggers the 'if' block
            Amount = 500.00m,
            TransactionDate = _dateNow,
            FilerId = 123,
            SeparateAccountName = "Account A",
            IsRecipientCommittee = true,
            RecipientCommitteeFilerId = 456,
            IsContributorFiler = true,
            ContributorFilerId = 789
        };

        // Act & Assert
        BadRequestException? ex = Assert.ThrowsAsync<BadRequestException>(() => _transactionSvc.EditLobbyistCampaignContribution(dto));
        Assert.That(ex!.Message, Is.EqualTo("ID is required for updates"));
    }

    [Test]
    public async Task GetAllTransactionsByFilingAsync_ValidRequest_ReturnsMappedDtos()
    {
        // Arrange
        var id = 1;
        var paymentMadeTransactions = new List<PaymentMade>
        {
            new ()
            {
                Id = 3,
                Amount = (Currency)1m,
                ExpenditureCodeId = 1,
                ExpenditureCodeDescription = "Description",
                Active = true,
                TransactionDate = _dateNow,
                AgentOrIndependentContractorName = "Agent",
            }
        };
        _transactionRepository
            .FindAllTransactionsByFiling<PaymentMade>(Arg.Any<long>(), Arg.Any<bool>())
            .Returns(paymentMadeTransactions);

        // Act
        var result = await _transactionSvc.GetAllTransactionsByFilingAsync<PaymentMade, PaymentMadeByAgentOrIndependentContractorResponseDto>(
                id,
                transaction => new PaymentMadeByAgentOrIndependentContractorResponseDto(id, transaction));

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result, Is.InstanceOf<List<PaymentMadeByAgentOrIndependentContractorResponseDto>>());
            Assert.That(result[0].Id, Is.EqualTo(paymentMadeTransactions[0].Id));
        });
    }

    [Test]
    public async Task GetAllTransactionsByFilingAsync_EmptyTransactions_ReturnsEmptyList()
    {
        // Arrange
        long id = 1;

        _transactionRepository
            .FindAllTransactionsByFiling<PaymentReceived>(id, false)
            .Returns(new List<PaymentReceived>());

        // Act
        var result = await _transactionSvc.GetAllTransactionsByFilingAsync<PaymentReceived, PaymentReceivedResponseDto>(
            id,
            transaction => new PaymentReceivedResponseDto(id, transaction));

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public void DeleteTransactionAsync_NotFoundTransaction_ShouldThrowError()
    {
        // Arrange
        int filingId = 1;
        int transactionId = 1;

        // Act & Assert
        KeyNotFoundException? error = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _transactionSvc.DeleteTransactionAsync(filingId, transactionId));
        Assert.That(error.Message, Is.EqualTo($"Transaction not found. Id={transactionId}"));
    }

    [Test]
    public void DeleteTransactionAsync_DeletedTransaction_DoNothing()
    {
        // Arrange
        int filingId = 1;
        int transactionId = 1;
        PaymentMade transaction = new()
        {
            Id = 1,
            Active = false,
            Amount = (Currency)1m,
            ExpenditureCodeId = 1,
            ExpenditureCodeDescription = "Test",
        };
        _transactionRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<Transaction?>(transaction));

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _transactionSvc.DeleteTransactionAsync(filingId, transactionId));

        _transactionRepository.Received().FindById(Arg.Any<long>());
        _transactionRepository.Received(0).Update(Arg.Any<Transaction>());
    }

    [Test]
    public void DeleteTransactionAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        int filingId = 1;
        int transactionId = 1;
        PaymentMade transaction = new()
        {
            Id = 1,
            Active = true,
            Amount = (Currency)1m,
            ExpenditureCodeId = 1,
            ExpenditureCodeDescription = "Test",
        };
        _transactionRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<Transaction?>(transaction));

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _transactionSvc.DeleteTransactionAsync(filingId, transactionId));

        _transactionRepository.Received().FindById(Arg.Any<long>());
        _transactionRepository.Received().Update(Arg.Any<Transaction>());
        _filingSvc.Received().OnTransactionDeletedAsync(Arg.Any<Transaction>(), Arg.Any<long>());
    }

    [Test]
    public void DeleteTransactionAsync_ValidRequestUpdateSummary_ShouldExecuteSuccessfully()
    {
        // Arrange
        long? transactionId = 100L;
        long filingId = 1L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };
        PaymentReceivedRequest request = new()
        {
            Jurisdiction = "State",
            Position = "Support",
            UnitemizedAmount = 100,
            ContactId = 100L,
        };
        PaymentReceived transaction = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        _ = _transactionRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<Transaction?>(transaction));
        _ = _filingRepository.FindById(Arg.Any<long>()).Returns(filing);

        _ = _filingPeriodRepository.FindById(Arg.Any<long>())
            .Returns(new FilingPeriod() { StartDate = DateTime.UtcNow, EndDate = DateTime.UtcNow, });
        _ = _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>())
            .Returns(new List<SmoCampaignStatement>() { new() { StatusId = 1 } });
        _ = _transactionRepository.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>())
            .Returns(transaction);
        List<PaymentReceived> payments = new() { transaction };
        _ = _transactionRepository.GetSmoCampaignStatementPaymentRecievedByIdAsync(Arg.Any<long>())
            .Returns(transaction);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 99L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 100L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _transactionSvc.DeleteTransactionAsync(filingId, (long)transactionId));

        _transactionRepository.Received().FindById(Arg.Any<long>());
        _transactionRepository.Received().Update(Arg.Any<Transaction>());
        _filingSvc.Received().OnTransactionDeletedAsync(Arg.Any<Transaction>(), Arg.Any<long>());
    }

    [Test]
    public void DeleteTransactionAsync_InvalidFiling_ShouldThrowException()
    {
        // Arrange
        PaymentReceived transaction = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        _ = _transactionRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<Transaction?>(transaction));

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _transactionSvc.DeleteTransactionAsync(1, 1));
        Assert.That(ex.Message, Is.EqualTo($"Filing not found. Id=1"));
    }

    [Test]
    public async Task EditPaymentMadeToLobbyingCoalitionAmount_ValidTransaction_UpdatesAndReturnsEditedTransaction()
    {
        // Arrange
        long transactionId = 100L;
        decimal? amount = 250.75m;

        TransactionResponseDto expectedResponse = new()
        {
            Id = transactionId,
            Valid = true,
            ValidationErrors = []
        };

        PaymentMadeToLobbyingCoalition transactionEntity = new()
        {
            Id = transactionId,
            Amount = (Currency)amount,
        };

        PaymentMadeToLobbyingCoalition updatedTransaction = new() { Amount = (Currency)amount };

        _ = _transactionRepository
            .FindById(transactionId)
            .Returns(Task.FromResult<Transaction?>(transactionEntity));

        _transactionRepository
            .Update(transactionEntity)
            .Returns(updatedTransaction);

        // Act
        TransactionResponseDto result = await _transactionSvc.EditPaymentMadeToLobbyingCoalitionAmount(transactionId, amount);

        // Assert
        _ = await _transactionRepository.Received(1).Update(transactionEntity);

        Assert.That(result, Is.Not.Null);
    }

    #region UpdateTransactionWrapperAsync
    [Test]
    public void UpdateTransactionWrapperAsync_NotFoundFilerContact_ShouldThrowError()
    {
        // Arrange
        PersonReceiving1000OrMore transaction = new()
        {
            Amount = (Currency)1,
        };
        decimal varianceAmount = 1m;
        long filingId = 1L;
        long contactId = 1L;

        _filerContactRepositoryMock.FindById(Arg.Any<long>()).ReturnsNull();
        _filingSvc.OnTransactionUpdatedAsync(Arg.Any<Transaction>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long?>());

        // Act & Assert
        KeyNotFoundException? ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _transactionSvc.UpdateTransactionWrapperAsync(transaction, varianceAmount, filingId, contactId));
        Assert.That(ex?.Message, Is.EqualTo($"Filer Contact not found. Id={contactId}"));
    }

    [Test]
    public void UpdateTransactionWrapperAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        PersonReceiving1000OrMore transaction = new()
        {
            Amount = (Currency)1,
        };
        decimal varianceAmount = 1m;
        long filingId = 1L;
        long contactId = 1L;
        FilerCommitteeContact contact = new()
        {
            Id = 1,
            CommitteeName = "CommitteeName"
        };

        _filerContactRepositoryMock.FindById(Arg.Any<long>()).Returns(contact);
        _filingSvc.OnTransactionUpdatedAsync(Arg.Any<Transaction>(), Arg.Any<decimal>(), Arg.Any<long>(), Arg.Any<long?>());

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _transactionSvc.UpdateTransactionWrapperAsync(transaction, varianceAmount, filingId, contactId));
        _ = _transactionRepository.Received(1).Update(Arg.Any<Transaction>());
    }
    #endregion

    [Test]
    public async Task GetLobbyingAdvertisementTransactionByFilingId_ShouldReturnTransaction_WhenTransactionExist()
    {
        // Arrange
        long filingId = 1;
        long transactionId = 1;
        LobbyingAdvertisement expectedTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)150,
            PublicationDate = _dateNow,
            DistributionMethodId = 1,
            AdditionalInformation = "Additional Information",
            CreatedBy = 1,
            ModifiedBy = 1,
        };

        _transactionRepository
            .GetLobbyingAdvertisementTransactionByFilingId(filingId)
            .Returns(Task.FromResult<LobbyingAdvertisement?>(expectedTransaction));

        // Act
        LobbyingAdvertisement? result = await _transactionSvc.GetLobbyingAdvertisementTransactionByFilingId(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Amount, Is.EqualTo(expectedTransaction.Amount));
        });

        await _transactionRepository.Received(1).GetLobbyingAdvertisementTransactionByFilingId(filingId);
    }

    [Test]
    public async Task CreateLobbyingAdvertisementTransaction_ShouldReturnTransactionId_WhenTransactionIsCreatedSuccessfully()
    {
        // Arrange
        long disclosureFilingId = 456L;
        decimal amount = 250.75m;
        long filerId = 123L;
        long expectedTransactionId = 2024L;

        LobbyingAdvertisementRequestDto dto = new()
        {
            Amount = amount,
            PublicationDate = DateTime.UtcNow,
            FilerId = filerId
        };

        var filingSummary = new FilingSummary
        {
            FilingSummaryTypeId = FilingSummaryType.LobbyingAdvertisementSummary.Id,
            FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
            ToDateAmount = 0,
            PeriodAmount = 0,
        };

        _filingSummaryRepository.GetAllByFilingId(disclosureFilingId)
            .Returns(new List<FilingSummary> { filingSummary });

        _filingSummaryRepository.Update(Arg.Any<FilingSummary>())
            .Returns(Task.FromResult(filingSummary));

        _transactionRepository.Create(Arg.Any<LobbyingAdvertisement>())
            .Returns(new LobbyingAdvertisement { Id = expectedTransactionId, Amount = new Currency(0) });

        _decisionsSvc.InitiateWorkflow<LobbyingAdvertisementDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling72HourReportLobbyingAdvertisement,
            Arg.Any<LobbyingAdvertisementDs>(),
            Arg.Any<bool>())
            .Returns([]);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreateLobbyingAdvertisementTransaction(disclosureFilingId, dto);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
        });

        await _transactionRepository.Received(1).AddTransactionToFiling(expectedTransactionId, disclosureFilingId);
        await _filingSummaryRepository.Received(1).Update(filingSummary);
    }

    [Test]
    public void CreateLobbyingAdvertisementTransaction_ShouldThrow_WhenFilingSummaryNotFound()
    {
        // Arrange
        long filingId = 456L;
        var dto = new LobbyingAdvertisementRequestDto
        {
            Amount = 100,
            PublicationDate = DateTime.UtcNow
        };

        _filingSummaryRepository.GetAllByFilingId(filingId)
            .Returns(new List<FilingSummary>());

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() =>
            _transactionSvc.CreateLobbyingAdvertisementTransaction(filingId, dto));

        Assert.That(ex.Message, Does.Contain("Lobbying Advertisement Filing Summary not found."));
    }

    [Test]
    public async Task CreateLobbyingAdvertisementTransaction_ShouldNotCreateTransaction_WhenValidationFails()
    {
        // Arrange
        long filingId = 456L;
        var dto = new LobbyingAdvertisementRequestDto
        {
            Amount = 100,
            PublicationDate = DateTime.UtcNow
        };

        var filingSummary = new FilingSummary
        {
            FilingSummaryTypeId = FilingSummaryType.LobbyingAdvertisementSummary.Id,
            PeriodAmount = 0m,
            ToDateAmount = 0m
        };

        _filingSummaryRepository.GetAllByFilingId(filingId)
            .Returns(new List<FilingSummary> { filingSummary });

        _decisionsSvc.InitiateWorkflow<LobbyingAdvertisementDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling72HourReportLobbyingAdvertisement,
            Arg.Any<LobbyingAdvertisementDs>(),
            Arg.Any<bool>())
            .Returns(new List<WorkFlowError> { new("test", "test", "test", "test") });

        // Act
        var result = await _transactionSvc.CreateLobbyingAdvertisementTransaction(filingId, dto);

        // Assert
        Assert.That(result.Valid, Is.False);
        await _transactionRepository.DidNotReceive().Create(Arg.Any<LobbyingAdvertisement>());
        await _filingSummaryRepository.DidNotReceive().Update(Arg.Any<FilingSummary>());
    }


    [Test]
    public async Task EditLobbyingAdvertisementTransaction_ShouldReturnTransactionId_WhenTransactionIsCreatedSuccessfully()
    {
        // Arrange
        decimal amount = 250.75m;
        long filerId = 123L;
        long expectedTransactionId = 2024L;

        LobbyingAdvertisementRequestDto dto = new()
        {
            Amount = amount,
            PublicationDate = DateTime.UtcNow,
            TransactionId = expectedTransactionId
        };

        LobbyingAdvertisement transaction = new()
        {
            Id = expectedTransactionId,
            FilerId = filerId,
            Amount = (Currency)amount,
            TransactionDate = dto.PublicationDate,
        };

        _transactionRepository.FindById(Arg.Any<long>())
            .Returns(transaction);

        _ = _decisionsSvc.InitiateWorkflow<LobbyingAdvertisementDs, List<WorkFlowError>>(
            Arg.Is(DecisionsWorkflow.FDLOBFiling72HourReportLobbyingAdvertisement),
            Arg.Any<LobbyingAdvertisementDs>(),
            Arg.Any<bool>())
            .Returns([]);

        // Act
        TransactionResponseDto? result = await _transactionSvc.EditLobbyingAdvertisementTransaction(1, dto);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(expectedTransactionId));
            Assert.That(result.Valid, Is.True);
        });

        // Assert
        await _transactionRepository.Received(1).Update(transaction);
    }

    [Test]
    public async Task EditLobbyingAdvertisementTransaction_ShouldReturnNull_WhenTransactionNotExist()
    {
        // Arrange
        decimal amount = 250.75m;
        long expectedTransactionId = 2024L;

        LobbyingAdvertisementRequestDto dto = new()
        {
            Amount = amount,
            PublicationDate = DateTime.UtcNow,
            TransactionId = expectedTransactionId
        };

        _ = _decisionsSvc.InitiateWorkflow<LobbyingAdvertisementDs, List<WorkFlowError>>(
            Arg.Is(DecisionsWorkflow.FDLOBFiling72HourReportLobbyingAdvertisement),
            Arg.Any<LobbyingAdvertisementDs>(),
            Arg.Any<bool>())
            .Returns([]);

        // Act
        TransactionResponseDto? result = await _transactionSvc.EditLobbyingAdvertisementTransaction(1, dto);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Null);
        });
    }

    [Test]
    public async Task CreateEndOfSessionLobbying_ShouldCreateTransaction_WhenValidationPassesAndNoContactOrCoalition()
    {
        // Arrange
        EndOfSessionLobbyingRequestDto request = new()
        {
            FilingId = 1001,
            FilerId = 2002,
            ContactId = 3003, // simulate existing contact path
            RegistrationFilingId = null, // simulate no coalition
            Amount = 5000m,
            DateLobbyingFirmHired = new DateTime(2025, 4, 10),
            AssemblyBills =
        [
            new ActionsLobbiedRequestDto { BillId = 123 }
        ],
            SenateBills =
        [
            new ActionsLobbiedRequestDto { BillId = 456 }
        ]
        };

        EndOfSessionLobbying createdTransaction = new()
        {
            Id = 999,
            Amount = (Currency)request.Amount,
            DateLobbyingFirmHired = request.DateLobbyingFirmHired,
            FilerId = request.FilerId,
            ContactId = request.ContactId.Value
        };

        // Mock Decisions validation call
        _decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransactionAdditionalData,
            Arg.Any<EndOfSessionLobbyingDs>(),
            true
        ).Returns([]);

        // Mock transaction creation
        _transactionSvc.CreateTransactionWrapper(Arg.Any<EndOfSessionLobbying>(), request.FilingId)
            .Returns(createdTransaction);

        // Mock actions lobbied upsert
        _actionsLobbiedSvc.UpsertActionsLobbiedForTransaction(createdTransaction.Id, Arg.Any<List<ActionsLobbiedRequestDto>>())
            .Returns([]);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreateEndOfSessionLobbying(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(createdTransaction.Id));
        });
    }

    [Test]
    public async Task CreateEndOfSessionLobbying_ShouldCreateTransaction_WhenCoalitionIsRegisteredAndNoExistingContact()
    {
        // Arrange
        EndOfSessionLobbyingRequestDto request = new()
        {
            FilingId = 1001,
            FilerId = 2002,
            ContactId = null, // No direct contact provided
            RegistrationFilingId = 4004, // Firm is registered
            Amount = 7500m,
            DateLobbyingFirmHired = new DateTime(2025, 5, 1, 0, 0, 0, DateTimeKind.Local),
            AssemblyBills =
        [
            new() { BillId = 101 }
        ],
            SenateBills =
        [
            new() { BillId = 202 }
        ]
        };

        long firmFilerId = 5555;
        long createdContactId = 7777;
        long createdTransactionId = 9999;

        LobbyingFirm selectedFirm = new()
        {
            Id = 4004,
            FilerId = firmFilerId,
            Name = "Test Lobbying Firm",
            StatusId = 1,
            Email = "<EMAIL>",
            LegislativeSessionId = 1L,
            AddressListId = 101L,
            PhoneNumberListId = 102L,
            ResponsibleOfficerTitle = "Director"
        };

        LobbyingFirmResponseDto firmResponseDto = new(selectedFirm);

        EndOfSessionLobbying createdTransaction = new()
        {
            Id = createdTransactionId,
            Amount = (Currency)request.Amount,
            DateLobbyingFirmHired = request.DateLobbyingFirmHired,
            FilerId = request.FilerId,
            ContactId = createdContactId
        };

        // Setup validation to pass
        _decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransactionAdditionalData,
            Arg.Any<EndOfSessionLobbyingDs>(),
            true
        ).Returns([]);

        // Setup to get the lobbying firm
        _lobbyingFirmRegistrationSvc.GetLobbyingFirm(request.RegistrationFilingId.Value)
            .Returns(firmResponseDto);

        // Setup - no existing contact relationship
        _filerContactSvc.GetFilerContactByFilerIdAndContactFilerId(request.FilerId, firmFilerId)
            .Returns((FilerContact?)null);

        // Setup - creating a new contact
        _filerContactSvc.CreateFilerContact(Arg.Any<OrganizationContact>())
            .Returns(createdContactId);

        // Setup transaction creation
        _transactionRepository.Create(Arg.Any<EndOfSessionLobbying>())
            .Returns(createdTransaction);

        // Setup actions lobbied creation
        _actionsLobbiedSvc.UpsertActionsLobbiedForTransaction(
            createdTransactionId,
            Arg.Any<List<ActionsLobbiedRequestDto>>())
            .Returns([]);

        // Act
        TransactionResponseDto result = await _transactionSvc.CreateEndOfSessionLobbying(request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(createdTransactionId));
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        // Verify the correct sequence of operations occurred
        await _lobbyingFirmRegistrationSvc.Received(1).GetLobbyingFirm(request.RegistrationFilingId.Value);
        await _filerContactSvc.Received(1).GetFilerContactByFilerIdAndContactFilerId(request.FilerId, firmFilerId);

        // Verify a new contact was created with correct data
        await _filerContactSvc.Received(1).CreateFilerContact(Arg.Is<OrganizationContact>(c =>
            c.FilerId == request.FilerId &&
            c.ContactFilerId == firmFilerId &&
            c.OrganizationName == selectedFirm.Name &&
            c.AddressListId == selectedFirm.AddressListId &&
            c.PhoneNumberListId == selectedFirm.PhoneNumberListId));

        // Verify transaction was created with the contact ID from the newly created contact
        await _transactionRepository.Received(1).Create(Arg.Is<EndOfSessionLobbying>(t =>
            t.FilerId == request.FilerId &&
            t.Amount == (Currency)request.Amount &&
            t.DateLobbyingFirmHired == request.DateLobbyingFirmHired &&
            t.ContactId == createdContactId));

        // Verify the transaction was linked to the filing
        await _transactionRepository.Received(1).AddTransactionToFiling(createdTransactionId, request.FilingId);

        // Verify actions lobbied were created for the transaction
        await _actionsLobbiedSvc.Received(1).UpsertActionsLobbiedForTransaction(
            createdTransactionId,
            Arg.Is<List<ActionsLobbiedRequestDto>>(list =>
                list.Count == 2 &&
                list.Any(a => a.BillId == 101) &&
                list.Any(a => a.BillId == 202)));
    }

    [Test]
    public async Task GetTransactionSummary_ShouldReturnTransactionSummary_WhenFilingExists()
    {
        // Arrange  
        long filingId = 123L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };

        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime legislativeEndDate = new(2023, 6, 30, 0, 0, 0, DateTimeKind.Local);

        List<TransactionSummaryDto> transactions =
        [
            new()
            {
                AmountThisPeriod = 100,
                CumulativeToDate = 100,
                TransactionTypeId = TransactionType.ActivityExpense.Id,
                Name = "Activity Expense",
                FilingSummaryTypeId = TransactionType.ActivityExpense.Id
            },
            new()
            {
                AmountThisPeriod = 200,
                CumulativeToDate = 200,
                TransactionTypeId = TransactionType.LobbyingCampaignContribution.Id,
                Name = "Campaign Contribution",
                FilingSummaryTypeId = TransactionType.LobbyingCampaignContribution.Id
            },
            new()
            {
                AmountThisPeriod = 300,
                CumulativeToDate = 300,
                TransactionTypeId = TransactionType.OtherPaymentsToInfluence.Id,
                Name = "Other Payments To Influence",
                FilingSummaryTypeId = TransactionType.OtherPaymentsToInfluence.Id
            },
            new()
            {
                AmountThisPeriod = 400,
                CumulativeToDate = 400,
                TransactionTypeId = TransactionType.PaymentMadeToLobbyingFirms.Id,
                Name = "Payment Made To Lobbying Firms",
                FilingSummaryTypeId = TransactionType.PaymentMadeToLobbyingFirms.Id
            },
            new()
            {
                AmountThisPeriod = 500,
                CumulativeToDate = 500,
                TransactionTypeId = TransactionType.PaymentMadeToLobbyingCoalition.Id,
                Name = "Payment Made To Lobbying Coalition",
                FilingSummaryTypeId = TransactionType.PaymentMadeToLobbyingCoalition.Id
            },
            new()
            {
                AmountThisPeriod = 600,
                CumulativeToDate = 600,
                TransactionTypeId = TransactionType.PaymentReceiveLobbyingCoalition.Id,
                Name = "Payment Receive Lobbying Coalition",
                FilingSummaryTypeId = TransactionType.PaymentReceiveLobbyingCoalition.Id
            }
        ];

        _filingSvc.GetFiling(filingId).Returns(Task.FromResult<Filing?>(filing));
        _filingSvc.GetLegislativeStartDateForDate(filing.StartDate).Returns(legislativeStartDate);
        _filingSvc.GetLegislativeEndDateForDate(filing.EndDate).Returns(legislativeEndDate);
        _transactionRepository.GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate).Returns(transactions);

        // Act  
        TransactionSummaryResponse result = await _transactionSvc.GetTransactionSummary(filingId);

        // Assert  
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.ActivityExpenseTransactions, Has.Count.EqualTo(1));
            Assert.That(result.CampaignContributionTransactions, Has.Count.EqualTo(1));
            Assert.That(result.OtherPaymentsToInfluenceTransactions, Has.Count.EqualTo(1));
            Assert.That(result.PaymentsToLobbyingFirmsTransactions, Has.Count.EqualTo(1));
            Assert.That(result.PaymentsToLobbyingCoalitionTransactions, Has.Count.EqualTo(1));
            Assert.That(result.PaymentsReceivedByLobbyingCoalitionTransactions, Has.Count.EqualTo(1));
        });

        // Verify correct repositories were called  
        await _filingSvc.Received(1).GetFiling(filingId);
        await _transactionRepository.Received(1).GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate);
    }

    [Test]
    public async Task GetTransactionSummary_ShouldReturnEmptyLists_WhenNoTransactionsExist()
    {
        // Arrange
        long filingId = 456L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };

        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime legislativeEndDate = new(2023, 6, 30, 0, 0, 0, DateTimeKind.Local);

        _filingSvc.GetFiling(filingId).Returns(Task.FromResult<Filing?>(filing));
        _filingSvc.GetLegislativeStartDateForDate(filing.StartDate).Returns(legislativeStartDate);
        _filingSvc.GetLegislativeEndDateForDate(filing.EndDate).Returns(legislativeEndDate);
        _transactionRepository.GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate).Returns([]);

        // Act
        TransactionSummaryResponse result = await _transactionSvc.GetTransactionSummary(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.ActivityExpenseTransactions, Is.Empty);
            Assert.That(result.CampaignContributionTransactions, Is.Empty);
            Assert.That(result.OtherPaymentsToInfluenceTransactions, Is.Empty);
            Assert.That(result.PaymentsToLobbyingFirmsTransactions, Is.Empty);
            Assert.That(result.PaymentsToLobbyingCoalitionTransactions, Is.Empty);
            Assert.That(result.PaymentsReceivedByLobbyingCoalitionTransactions, Is.Empty);
            Assert.That(result.PaymentsPucActivityTransactions, Is.Empty);
            Assert.That(result.PaymentsInHouseLobbyistTransactions, Is.Empty);
        });
    }

    [Test]
    public void GetTransactionSummary_ShouldThrowKeyNotFoundException_WhenFilingNotFound()
    {
        // Arrange
        long filingId = 999L;
        _filingSvc.GetFiling(filingId).Returns(Task.FromResult<Filing?>(null));

        // Act & Assert
        KeyNotFoundException? exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _transactionSvc.GetTransactionSummary(filingId));

        Assert.That(exception!.Message, Is.EqualTo($"Filing with ID {filingId} not found."));
    }

    [Test]
    public async Task GetTransactionSummary_ShouldUseCorrectLegislativeDates_ForEvenYearEndDate()
    {
        // Arrange
        long filingId = 789L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2024-06-30", CultureInfo.InvariantCulture), // Even year end date
        };

        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime legislativeEndDate = new(2024, 6, 30, 0, 0, 0, DateTimeKind.Local);

        _filingSvc.GetFiling(filingId).Returns(Task.FromResult<Filing?>(filing));
        _filingSvc.GetLegislativeStartDateForDate(filing.StartDate).Returns(legislativeStartDate);
        _filingSvc.GetLegislativeEndDateForDate(filing.EndDate).Returns(legislativeEndDate);
        _transactionRepository.GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate)
            .Returns([]);

        // Act
        await _transactionSvc.GetTransactionSummary(filingId);

        // Assert - verify legislative end date was retrieved for even year
        _filingSvc.Received(1).GetLegislativeEndDateForDate(filing.EndDate);
        await _transactionRepository.Received(1).GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate);
    }

    [Test]
    public async Task GetTransactionSummary_ShouldUseFileEndDate_ForOddYearEndDate()
    {
        // Arrange
        long filingId = 789L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture), // Odd year end date
        };

        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime endDate = filing.EndDate;

        _filingSvc.GetFiling(filingId).Returns(Task.FromResult<Filing?>(filing));
        _filingSvc.GetLegislativeStartDateForDate(filing.StartDate).Returns(legislativeStartDate);
        _transactionRepository.GetTransactionsForFilingSummary(filingId, legislativeStartDate, endDate)
            .Returns([]);

        // Act
        await _transactionSvc.GetTransactionSummary(filingId);

        // Assert - verify we used file end date directly
        _filingSvc.DidNotReceive().GetLegislativeEndDateForDate(Arg.Any<DateTime>());
        await _transactionRepository.Received(1).GetTransactionsForFilingSummary(filingId, legislativeStartDate, endDate);
    }

    #region GetSmoCampaignStatementFilingContactSummaryChanges

    [Test]
    public async Task GetSmoCampaignStatementFilingContactSummaryChanges_ValidRequest_ValidResponse()
    {
        // Arrange
        long? transactionId = 100L;
        long filingId = 1L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };
        PaymentReceivedRequest request = new()
        {
            Jurisdiction = "State",
            Position = "Support",
            UnitemizedAmount = 100,
            ContactId = 100L,
        };
        PaymentReceived transaction = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        _ = _filingRepository.FindById(Arg.Any<long>()).Returns(filing);
        _ = _filingPeriodRepository.FindById(Arg.Any<long>())
            .Returns(new FilingPeriod() { StartDate = DateTime.UtcNow, EndDate = DateTime.UtcNow, });
        _ = _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>())
            .Returns(new List<SmoCampaignStatement>() { new() { StatusId = 1 } });
        _ = _transactionRepository.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>())
            .Returns(transaction);
        List<PaymentReceived> payments = new() { transaction };
        _ = _transactionRepository.GetSmoCampaignStatementPaymentRecievedByIdAsync(Arg.Any<long>())
            .Returns(transaction);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 99L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 100L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);

        // Act
        (PaymentReceived? oldTransaction, FilingContactSummary? oldSummary, FilingContactSummary newSummary) =
            await _transactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(oldTransaction, Is.Not.Null);
            Assert.That(oldSummary, Is.Not.Null);
            Assert.That(oldSummary, Is.InstanceOf<FilingContactSummary?>());
            Assert.That(newSummary, Is.Not.Null);
            Assert.That(newSummary, Is.InstanceOf<FilingContactSummary>());
        });
    }

    [Test]
    public async Task GetSmoCampaignStatementFilingContactSummaryChanges_ValidRequestNewTransaction_ValidResponse()
    {
        // Arrange
        long? transactionId = null;
        long filingId = 1L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };
        PaymentReceivedRequest request = new()
        {
            Jurisdiction = "State",
            Position = "Support",
            UnitemizedAmount = 100,
            ContactId = 99L,
        };
        PaymentReceived transaction = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnCandidate = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        _ = _filingPeriodRepository.FindById(Arg.Any<long>())
            .Returns(new FilingPeriod() { StartDate = DateTime.UtcNow, EndDate = DateTime.UtcNow, });
        _ = _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>())
            .Returns(new List<SmoCampaignStatement>() { new() { StatusId = 1 } });
        _ = _transactionRepository.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>())
            .Returns(transaction);
        List<PaymentReceived> payments = new() { transaction };
        _ = _transactionRepository.GetSmoCampaignStatementPaymentRecievedByIdAsync(Arg.Any<long>())
            .Returns(transaction);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 99L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 100L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);

        // Act
        (PaymentReceived? oldTransaction, FilingContactSummary? oldSummary, FilingContactSummary newSummary) =
            await _transactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(oldTransaction, Is.Null);
            Assert.That(oldSummary, Is.Null);
            Assert.That(newSummary, Is.Not.Null);
            Assert.That(newSummary, Is.InstanceOf<FilingContactSummary>());
        });
    }

    [Test]
    public async Task GetSmoCampaignStatementFilingContactSummaryChanges_ValidRequestNoExistingTransactions_ValidResponse()
    {
        // Arrange
        long? transactionId = null;
        long filingId = 1L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };
        PaymentReceivedRequest request = new()
        {
            Jurisdiction = "State",
            Position = "Support",
            UnitemizedAmount = 100,
            ContactId = 99L,
        };
        PaymentReceived transaction = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnCandidate = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 99L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        _ = _filingPeriodRepository.FindById(Arg.Any<long>())
            .Returns(new FilingPeriod() { StartDate = DateTime.UtcNow, EndDate = DateTime.UtcNow, });
        _ = _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>())
            .Returns(new List<SmoCampaignStatement>() { new() { StatusId = 1 } });
        _ = _transactionRepository.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>())
            .Returns(transaction);
        List<PaymentReceived> payments = new() { transaction };
        _ = _transactionRepository.GetSmoCampaignStatementPaymentRecievedByIdAsync(Arg.Any<long>())
            .Returns(transaction);
        //_ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 99L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
        //    .Returns(null as List<PaymentReceived>);
        //_ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 100L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
        //    .Returns(null as List<PaymentReceived>);

        // Act
        (PaymentReceived? oldTransaction, FilingContactSummary? oldSummary, FilingContactSummary newSummary) =
            await _transactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(oldTransaction, Is.Null);
            Assert.That(oldSummary, Is.Null);
            Assert.That(newSummary, Is.Not.Null);
            Assert.That(newSummary, Is.InstanceOf<FilingContactSummary>());
        });
    }


    [Test]
    public async Task GetSmoCampaignStatementFilingContactSummaryChanges_ValidRequestHasOldTransactions_ValidResponse()
    {
        // Arrange
        long? transactionId = 1L;
        long filingId = 1L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };
        PaymentReceivedRequest request = new()
        {
            Jurisdiction = "State",
            Position = "Oppose",
            UnitemizedAmount = 100,
            ContactId = 99L,
        };
        PaymentReceived transaction = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnBallotMeasure = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 0L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        _ = _filingPeriodRepository.FindById(Arg.Any<long>())
            .Returns(new FilingPeriod() { StartDate = DateTime.UtcNow, EndDate = DateTime.UtcNow, });
        _ = _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>())
            .Returns(new List<SmoCampaignStatement>() { new() { StatusId = 1 } });
        _ = _transactionRepository.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>())
            .Returns(transaction);
        List<PaymentReceived> payments = new() { transaction };
        _ = _transactionRepository.GetSmoCampaignStatementPaymentRecievedByIdAsync(Arg.Any<long>())
            .Returns(transaction);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 99L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);
        //_ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), 100L, Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
        //    .Returns(null as List<PaymentReceived>);

        // Act
        (PaymentReceived? oldTransaction, FilingContactSummary? oldSummary, FilingContactSummary newSummary) =
            await _transactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(oldTransaction, Is.Not.Null);
            Assert.That(oldTransaction!.Id, Is.EqualTo(99));
            Assert.That(oldSummary, Is.Null);
            Assert.That(newSummary, Is.Not.Null);
            Assert.That(newSummary, Is.InstanceOf<FilingContactSummary>());
        });
    }


    [Test]
    public async Task GetSmoCampaignStatementFilingContactSummaryChanges_ValidRequestHasNewTransactions_ValidResponse()
    {
        // Arrange
        long? transactionId = 1L;
        long filingId = 1L;
        Filing filing = new()
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = DateTime.Parse("2023-01-01", CultureInfo.InvariantCulture),
            EndDate = DateTime.Parse("2023-06-30", CultureInfo.InvariantCulture),
        };
        PaymentReceivedRequest request = new()
        {
            Jurisdiction = "State",
            Position = "Oppose",
            Amount = 100,
            UnitemizedAmount = 100,
            ContactId = 99L,
            StanceOnCandidate = new()
            {
                CandidateId = 99L,
            }
        };
        PaymentReceived transaction = new()
        {
            Id = 99L,
            ContactId = 99L,
            Amount = (Currency)200,
            DisclosureStanceOnCandidate = new()
            {
                Id = 99L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 0L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Id = 99,
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        PaymentReceived transaction2 = new()
        {
            Id = 1L,
            ContactId = 99L,
            Amount = (Currency)300,
            DisclosureStanceOnCandidate = new()
            {
                Id = 1L,
                CreatedBy = 0L,
                ModifiedBy = 0L,
                Position = "Oppose",
                SubjectId = 0L,
                FilingContactSummaryId = 99L,
                FilingContactSummary = new()
                {
                    Id = 99,
                    Amount = 999,
                    PreviouslyUnitemizedAmount = 99,
                }
            }
        };
        _ = _filingPeriodRepository.FindById(Arg.Any<long>())
            .Returns(new FilingPeriod() { StartDate = DateTime.UtcNow, EndDate = DateTime.UtcNow, });
        _ = _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>())
            .Returns(new List<SmoCampaignStatement>() { new() { StatusId = 1 } });
        _ = _transactionRepository.FindByIdAndFilingId<PaymentReceived>(Arg.Any<long>(), Arg.Any<long>())
            .Returns(transaction);
        List<PaymentReceived> payments = new() { transaction, transaction2 };
        _ = _transactionRepository.GetSmoCampaignStatementPaymentRecievedByIdAsync(Arg.Any<long>())
            .Returns(transaction);
        _ = _transactionRepository.GetMatchingTransactionByFilingId(Arg.Any<List<long>>(), Arg.Any<long>(), Arg.Any<string>(), Arg.Any<long?>(), Arg.Any<long?>())
            .Returns(payments);

        // Act
        (PaymentReceived? oldTransaction, FilingContactSummary? oldSummary, FilingContactSummary newSummary) =
            await _transactionSvc.GetSmoCampaignStatementFilingContactSummaryChanges(filing, request, transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(oldTransaction, Is.Not.Null);
            Assert.That(oldTransaction!.Id, Is.EqualTo(99));
            Assert.That(oldSummary, Is.Not.Null);
            Assert.That(oldSummary, Is.InstanceOf<FilingContactSummary>());
            Assert.That(newSummary, Is.Not.Null);
            Assert.That(newSummary, Is.InstanceOf<FilingContactSummary>());
        });
    }
    #endregion

    #region
    [Test]
    public async Task EditEndOfSessionLobbying_ShouldUpdateTransaction_WhenValidRequest()
    {
        // Arrange
        long transactionId = 1000L;
        EndOfSessionLobbyingRequestDto request = new()
        {
            Amount = 9999m,
            DateLobbyingFirmHired = new DateTime(2025, 5, 1),
            AssemblyBills = [new() { BillId = 1 }],
            SenateBills = [new() { BillId = 2 }]
        };

        EndOfSessionLobbying existingTransaction = new()
        {
            Id = transactionId,
            FilerId = 1234,
            ContactId = 4321,
            Amount = (Currency)100
        };

        EndOfSessionLobbying updatedTransaction = new()
        {
            Id = transactionId,
            Amount = (Currency)request.Amount,
            DateLobbyingFirmHired = request.DateLobbyingFirmHired,
            FilerId = 1234,
            ContactId = 4321
        };

        TransactionResponseDto validatedResult = new()
        {
            ValidationErrors = [],
            Valid = false,
            Id = 0
        };

        List<WorkFlowError> noErrors = [];

        _decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransactionAdditionalData,
            Arg.Any<EndOfSessionLobbyingDs>(),
            true).Returns(noErrors);

        _transactionRepository.FindById(transactionId).Returns(existingTransaction);
        _transactionRepository.Update(Arg.Any<EndOfSessionLobbying>()).Returns(updatedTransaction);
        _actionsLobbiedSvc.UpsertActionsLobbiedForTransaction(transactionId, Arg.Any<List<ActionsLobbiedRequestDto>>()).Returns([]);

        // Act
        TransactionResponseDto result = await _transactionSvc.EditEndOfSessionLobbying(transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        await _transactionRepository.Received(1).FindById(transactionId);
        await _transactionRepository.Received(1).Update(Arg.Is<EndOfSessionLobbying>(t =>
            t.Id == transactionId &&
            t.Amount == (Currency)request.Amount &&
            t.DateLobbyingFirmHired == request.DateLobbyingFirmHired
        ));

        await _actionsLobbiedSvc.Received(1).UpsertActionsLobbiedForTransaction(transactionId,
            Arg.Is<List<ActionsLobbiedRequestDto>>(l =>
                l.Count == 2 &&
                l.Any(a => a.BillId == 1) &&
                l.Any(a => a.BillId == 2)));
    }

    [Test]
    public async Task EditEndOfSessionLobbying_ShouldReturnInvalid_WhenValidationFails()
    {
        // Arrange
        long transactionId = 2000L;
        EndOfSessionLobbyingRequestDto request = new()
        {
            Amount = 10,
            AssemblyBills = [],
            SenateBills = [],
            DateLobbyingFirmHired = null
        };

        List<WorkFlowError> errors = new() { new("test", "test", "test", "test") };

        _decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransactionAdditionalData,
            Arg.Any<EndOfSessionLobbyingDs>(),
            true).Returns(errors);

        // Act
        TransactionResponseDto result = await _transactionSvc.EditEndOfSessionLobbying(transactionId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.Id, Is.EqualTo(null));
            Assert.That(result.ValidationErrors, Is.EquivalentTo(errors));
        });

        await _transactionRepository.DidNotReceive().Update(Arg.Any<EndOfSessionLobbying>());
        await _actionsLobbiedSvc.DidNotReceive().UpsertActionsLobbiedForTransaction(Arg.Any<long>(), Arg.Any<List<ActionsLobbiedRequestDto>>());
    }

    [Test]
    public void EditEndOfSessionLobbying_ShouldThrowNotFound_WhenTransactionDoesNotExist()
    {
        // Arrange
        long transactionId = 2000L;
        EndOfSessionLobbyingRequestDto request = new()
        {
            Amount = 2500m,
            DateLobbyingFirmHired = DateTime.Today,
            AssemblyBills = [],
            SenateBills = []
        };

        _decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransactionAdditionalData,
            Arg.Any<EndOfSessionLobbyingDs>(), true
        ).Returns([]);

        _transactionRepository.FindById(transactionId).Returns((Transaction?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<NotFoundException>(async () =>
            await _transactionSvc.EditEndOfSessionLobbying(transactionId, request));

        Assert.That(ex.Message, Is.EqualTo($"Transaction {transactionId} not found."));
    }


    [Test]
    public void EditEndOfSessionLobbying_ShouldThrowInvalidOperation_WhenTransactionWrongType()
    {
        // Arrange
        long transactionId = 1000L;
        EndOfSessionLobbyingRequestDto request = new()
        {
            Amount = 5000m,
            DateLobbyingFirmHired = DateTime.Today,
            AssemblyBills = [],
            SenateBills = []
        };

        Transaction wrongTypeTransaction = new OtherPaymentsToInfluence
        {
            Id = transactionId,
            FilerId = 1234,
            Amount = (Currency)10
        };

        _decisionsSvc.InitiateWorkflow<EndOfSessionLobbyingDs, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling48HourReportAddNewTransactionAdditionalData,
            Arg.Any<EndOfSessionLobbyingDs>(), true
        ).Returns([]);

        _transactionRepository.FindById(transactionId).Returns(wrongTypeTransaction);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () =>
            await _transactionSvc.EditEndOfSessionLobbying(transactionId, request));

        Assert.That(ex.Message, Is.EqualTo("Transaction is not of type EndOfSessionLobbying"));
    }

    #endregion

    #region
    [Test]
    public async Task GetEndOfSessionLobbyingTransactionById_ShouldReturnDto_WhenTransactionExists()
    {
        // Arrange
        long transactionId = 1234L;
        EndOfSessionLobbyingDto expectedDto = new()
        {
            Id = transactionId,
            Amount = 5000m,
            DateLobbyingFirmHired = new DateTime(2025, 6, 1),
            FilerId = 5678,
            FirmName = "Test Firm"
        };

        _transactionRepository
            .GetAllEndOfSessionLobbyingTransactionById(transactionId)
            .Returns(expectedDto);

        // Act
        EndOfSessionLobbyingDto result = await _transactionSvc.GetEndOfSessionLobbyingTransactionById(transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.Amount, Is.EqualTo(expectedDto.Amount));
            Assert.That(result.DateLobbyingFirmHired, Is.EqualTo(expectedDto.DateLobbyingFirmHired));
            Assert.That(result.FilerId, Is.EqualTo(expectedDto.FilerId));
        });
    }

    [Test]
    public void GetEndOfSessionLobbyingTransactionById_ShouldThrowNotFoundException_WhenTransactionDoesNotExist()
    {
        // Arrange
        long transactionId = 9999L;

        _transactionRepository
            .GetAllEndOfSessionLobbyingTransactionById(transactionId)
            .Returns((EndOfSessionLobbyingDto?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<NotFoundException>(async () =>
            await _transactionSvc.GetEndOfSessionLobbyingTransactionById(transactionId));

        Assert.That(ex.Message, Is.EqualTo($"Transaction for Id={transactionId} not found."));
    }

    #endregion
}

