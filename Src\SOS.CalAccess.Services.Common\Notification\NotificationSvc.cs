using System.Collections.ObjectModel;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Refit;
using SendGrid.Helpers.Errors.Model;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Notifications;
using SOS.CalAccess.Data.UserAccountMaintenance;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.Notification;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Common.Email;
using SOS.CalAccess.Services.Common.Email.Model;
using SOS.CalAccess.Services.Common.SmsMessaging;
using SOS.CalAccess.Services.Common.SmsMessaging.Model;

namespace SOS.CalAccess.Services.Common.Notification;
#pragma warning disable S107 // Methods should not have too many parameters
public class NotificationSvc(
    IEmailSvc emailSvc,
    ISmsMessagingSvc smsSvc,
    IDateTimeSvc dateTimeSvc,
    INotificationMessageRepository notificationMessageRepository,
    IUserNotificationPreferenceRepository notificationPreferenceRepository,
    INotificationTemplateRepository notificationTemplateRepository,
    IFilerUserRepository filerUserRepository,
    ILogger<NotificationSvc> logger) : INotificationSvc
#pragma warning restore S107 // Methods should not have too many parameters
{
    /// <inheritdoc />
    public Task<int> CountActiveUserNotifications(long userId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<int> DeleteNotification(long notificationId)
    {
        var notification = await notificationMessageRepository.FindById(notificationId)
            ?? throw new NotFoundException($"No message found for Id {notificationId}");

        if (notification.NotificationTemplate!.IsActionRequired && notification.ResolvedAt is null)
        {
            return 0;
        }
        else
        {
            return await notificationMessageRepository.Delete(notification);
        }
    }

    /// <summary>
    /// Gets list of all notifications to be populated in the Grid
    /// </summary>
    /// <param name="request">PagedUserDataRequest with information like username, sort column,
    /// search fields, search key, records to skip, records to take</param>
    /// <param name="filerTypeId">Filer Type Id for filterting the notifications</param>
    /// <returns></returns>
    public async Task<PagedDataResponse<NotificationMessageDto>> ListAllNotifications(PagedUserDataRequest request, long filerTypeId)
    {
        var count = await notificationMessageRepository.CountNotificationsByUserAndFilerTypeId(request.Username, filerTypeId);

        var results = new List<NotificationMessageDto>();
        if (count > 0)
        {
            results = await notificationMessageRepository.FindAllNotificationsByUserAndFilerType(request, filerTypeId);
        }

        if (!string.IsNullOrWhiteSpace(request.SearchValue))
        {
            count = results.Count;
        }

        return new PagedDataResponse<NotificationMessageDto>(results, count);
    }

    /// <inheritdoc />
    public Task<List<NotificationMessage>> ListPriorityNotifications(long userId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public Task<List<NotificationMessage>> ListUnreadNotifications(long userId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task ResolveNotification([Body] ResolveNotificationRequest request)
    {
        var activeNotifications = await notificationMessageRepository.FindAllActiveNotificationsByTemplateIdAndFilerId(request.NotificationTemplateId, request.FilerId);

        if (activeNotifications != null && activeNotifications.Count != 0)
        {
            activeNotifications.ForEach(notification => notification.ResolvedAt = dateTimeSvc.GetCurrentDateTime());

            await notificationMessageRepository.UpdateRange(activeNotifications);
        }
    }

    /// <inheritdoc />
    public Task<List<NotificationMessage>> SearchNotifications(NotificationSearchCriteria searchCriteria)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task SendFilerNotification(SendFilerNotificationRequest request)
    {
        NotificationTemplate template = await notificationTemplateRepository.FindById(request.NotificationTemplateId) ?? throw new KeyNotFoundException($"Unable to find NotificationTemplate {request.NotificationTemplateId}");
        var serializedNotificationData = SerializeNotificationData(request.NotificationData);

        var userIds = await filerUserRepository.GetUserIdsByFilerId(request.FilerId);
        if (userIds != null && userIds.Any())
        {
            var emailRecipients = new Collection<CalAccessEmailRecipient>();
            var smsRecipients = new Collection<CalAccessSmsRecipient>();
            foreach (var userId in userIds)
            {
                //For each user recieving the notification create a record in the database
                //future enhancement - use transaction and only trigger DB call once instead of for each record
                var notification = await CreateNotificationMessage(request, userId, serializedNotificationData);

                //Determine if the user should recieve email or sms notifications
                try
                {
                    UserNotificationPreference notificationPreference = await GetNotificationPreferencesForRequest(userId, request.FilerId);

                    if (SmsRequested(notificationPreference, template))
                    {
                        smsRecipients.Add(new()
                        {
                            NotificationId = notification.Id,
                            PhoneNumber = notificationPreference.SmsPhoneNumber!,
                            UserId = userId
                        });
                    }

                    if (EmailRequested(notificationPreference, template))
                    {
                        emailRecipients.Add(new()
                        {
                            NotificationId = notification.Id,
                            EmailAddress = notificationPreference.EmailAddress!,
                            Name = "",
                            UserId = userId
                        });
                    }
                }
                catch (Exception ex)
                {
                    //swallow exception to allow other recipients to recieve notification
                    logger.LogError(ex, "Failed to read notification preferences for user {UserId} to send NotificationTemplateId {NotificationTemplateId} ", userId, request.NotificationTemplateId);
                }
            }

            //send email and sms messages
            await SendSmsNotification(request.FilerId, smsRecipients, template.EnglishTranslation(), request.NotificationData);
            await SendEmailNotification(request.FilerId, emailRecipients, template.EnglishTranslation(), request.NotificationData);
        }
    }

    /// <inheritdoc />
    public async Task SendUserNotification(SendUserNotificationRequest request)
    {
        //retrieve template
        NotificationTemplate template = await notificationTemplateRepository.FindById(request.NotificationTemplateId) ?? throw new KeyNotFoundException($"Unable to find NotificationTemplate {request.NotificationTemplateId}");

        //save to notification to db
        var notification = await CreateNotificationMessage(request);

        //get user notification preferences
        UserNotificationPreference notificationPreference = await GetNotificationPreferencesForRequest(request.UserId, request.FilerId);


        //Send SMS message if preferred
        if (SmsRequested(notificationPreference, template))
        {
            var recipients = new Collection<CalAccessSmsRecipient>{ new() {
                    NotificationId = notification.Id,
                    PhoneNumber = notificationPreference.SmsPhoneNumber!,
                    UserId = request.UserId }
            };
            await SendSmsNotification(request.FilerId, recipients, template.EnglishTranslation(), request.NotificationData);
        }

        //Send email if requested
        if (EmailRequested(notificationPreference, template))
        {
            var recipients = new Collection<CalAccessEmailRecipient>{ new() {
                    NotificationId = notification.Id,
                    EmailAddress = notificationPreference.EmailAddress!,
                    Name = "",
                    UserId = request.UserId }
            };

            await SendEmailNotification(request.FilerId, recipients, template.EnglishTranslation(), request.NotificationData);
        }

    }

    /// <inheritdoc />
    public async Task<NotificationMessageDto?> ViewNotification(long notificationId)
    {
        var notificationMessageDto = await notificationMessageRepository.FindMessageById(notificationId);

        if (notificationMessageDto != null && notificationMessageDto.ViewedAt is null)
        {
            var entity = await notificationMessageRepository.FindById(notificationId);
            if (entity != null)
            {
                _ = await notificationMessageRepository.UpdateProperty(entity, entity => entity.ViewedAt, dateTimeSvc.GetCurrentDateTime());
            }
        }

        return notificationMessageDto;
    }

    /// <inheritdoc />
    public async Task MarkReadOrUnread(long notificationId)
    {
        var msg = await notificationMessageRepository.FindById(notificationId);
        if (msg is not null)
        {
            DateTime? newState = (msg.ViewedAt == null) ? dateTimeSvc.GetCurrentDateTime() : null;

            _ = await notificationMessageRepository.UpdateProperty(msg, m => m.ViewedAt, newState);
        }
    }

    private async Task<UserNotificationPreference> GetNotificationPreferencesForRequest(long userId, long? filerId)
    {
        UserNotificationPreference notificationPreference;
        if (filerId is null)
        {
            //Get default preferences for user
            notificationPreference = await notificationPreferenceRepository.FindNotificationPreferenceByUserId(userId)
                ?? throw new KeyNotFoundException($"No UserNotificationPreferences found for user {userId}");
        }
        else
        {
            //get preferences for user and filer, if no record for filer, fallback and use default preferences for user
            notificationPreference = await notificationPreferenceRepository.FindNotificationPreferenceByUserIdAndFilerId(userId, filerId.Value)
                ?? await notificationPreferenceRepository.FindNotificationPreferenceByUserId(userId)
                ?? throw new KeyNotFoundException($"No UserNotificationPreferences found for user {userId}");
        }

        return notificationPreference;
    }

    private static bool SmsRequested(UserNotificationPreference notificationPreference, NotificationTemplate template)
    {
        if (notificationPreference.SmsPhoneNumber != null)
        {
            if (template.IsPriorityMessage)
            {
                return notificationPreference.ReceivePriorityNotificationsBySms;
            }
            else
            {
                return notificationPreference.ReceiveStandardNotificationsBySms;
            }
        }
        else
        {
            return false;
        }
    }

    private async Task SendSmsNotification(long? filerId, Collection<CalAccessSmsRecipient> recipients, NotificationTemplateTranslation translation, IDictionary<string, string>? notificationData)
    {
        string message = ReplaceVariables(translation.SmsMessage, notificationData);

        var smsRequest = new SmsMessageRequest(
            recipients,
            filerId,
            Message: message
        );

        await smsSvc.SendSmsMessage(smsRequest);
    }

    private static bool EmailRequested(UserNotificationPreference notificationPreference, NotificationTemplate template)
    {
        if (notificationPreference.EmailAddress != null)
        {
            if (template.IsPriorityMessage)
            {
                return notificationPreference.ReceivePriorityNotificationsByEmail;
            }
            else
            {
                return notificationPreference.ReceiveStandardNotificationsByEmail;
            }
        }
        else
        {
            return false;
        }
    }

    private async Task SendEmailNotification(long? filerId, Collection<CalAccessEmailRecipient> recipients, NotificationTemplateTranslation translation, IDictionary<string, string>? notificationData)
    {
        if (string.IsNullOrEmpty(translation.EmailTemplateId))
        {
            string subject = ReplaceVariables(translation.Subject, notificationData);
            string message = ReplaceVariables(translation.Message, notificationData);

            var emailMsgRequest = new EmailMessageRequest(recipients, filerId, subject, message);

            await emailSvc.SendPlainEmail(emailMsgRequest);
        }
        else
        {
            var emailTemplateRequest = new EmailTemplateRequest(recipients, filerId, translation.EmailTemplateId, notificationData);
            await emailSvc.SendTemplatedEmail(emailTemplateRequest);
        }
    }

    private async Task<NotificationMessage> CreateNotificationMessage(SendFilerNotificationRequest request, long userId, string? serializedNotificationData)
    {
        return await CreateNotificationMessage(userId, request.FilerId, request.NotificationTemplateId, request.DueDate, serializedNotificationData);
    }

    private async Task<NotificationMessage> CreateNotificationMessage(SendUserNotificationRequest request)
    {
        return await CreateNotificationMessage(request.UserId, request.FilerId, request.NotificationTemplateId, request.DueDate, SerializeNotificationData(request.NotificationData));
    }

    private async Task<NotificationMessage> CreateNotificationMessage(long userId, long? filerId, long notificationTemplateId, DateTime? dueDate, string? serializedNotificationData)
    {
        var notification = new NotificationMessage()
        {
            UserId = userId,
            FilerId = filerId,
            NotificationTemplateId = notificationTemplateId,
            CreatedAt = dateTimeSvc.GetCurrentDateTime(),
            DueDate = dueDate,
            TemplateData = serializedNotificationData
        };

        return await notificationMessageRepository.Create(notification);
    }

    private static string? SerializeNotificationData(IDictionary<string, string>? notificationData)
    {
        return notificationData != null ? JsonSerializer.Serialize(notificationData) : null;
    }

    private static string ReplaceVariables(string text, IDictionary<string, string>? notificationData)
    {
        if (notificationData != null)
        {
            foreach (var key in notificationData.Keys)
            {
                var pattern = "{{" + key + "}}";
                text = text.Replace(pattern, notificationData[key], StringComparison.CurrentCultureIgnoreCase);
            }
        }
        return text;
    }

    /// <summary>
    /// Gets the number of Unresolved notifications for a user
    /// </summary>
    /// <param name="userId"> Unique Id for a User</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<int> CountUnresolvedeUserNotifications(string userName)
    {
        return await notificationMessageRepository.CountUnresolvedNotificationsByUser(userName);
    }
}
