using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NSubstitute.ReturnsExtensions;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.WebApi.Registrations;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.WebApi.Tests.Registrations;

[TestFixture]
public class SmoRegistrationControllerTests()
{
    private IAuthorizationService _authorizationMock;
    private IRegistrationRepository _registrationRepositoryMock;
    private IRegistrationRegistrationContactRepository _registrationRegistrationContactRepositoryMock;
    private IAttestationRepository _attestationRepositoryMock;
    private IDecisionsSvc _decisionsSvcMock;
    private IAuthorizationSvc _authorizationSvcMock;
    private IFilerSvc _filerSvcMock;
    private INotificationSvc _notificationSvcMock;
    private IUserMaintenanceSvc _userMaintenanceSvcMock;
    private ISmoRegistrationSvc _smoRegistrationSvcMock;
    private IFilingRepository _filingRepositoryMock;
    private IFilingSummaryRepository _filingSummaryRepositoryMock;
    private IFilingPeriodRepository _filingPeriodRepositoryMock;
    private ITransactionRepository _transactionRepositoryMock;
    private IReferenceDataSvc _referenceDataSvc;
    private IRegistrationModelMapper _modelMapperMock;
    private ILinkageSvc _linkageSvcMock;
    private IFilerUserRepository _filerUserRepositoryMock;
    private ILinkageRequestRepository _linkageRequestRepositoryMock;
    private IFilingContactSummaryRepository _filingContactSummaryRepositoryMock;
    private IDateTimeSvc _dateTimeSvcMock;
    private DateTime _dateNow;

    private FilingSharedServicesDependencies _servicesDependencies;
    private FilingSharedRepositoriesDependencies _repositoriesDependencies;

    [SetUp]
    public void Setup()
    {
        _authorizationMock = Substitute.For<IAuthorizationService>();
        _registrationRepositoryMock = Substitute.For<IRegistrationRepository>();
        _registrationRegistrationContactRepositoryMock = Substitute.For<IRegistrationRegistrationContactRepository>();
        _attestationRepositoryMock = Substitute.For<IAttestationRepository>();
        _authorizationSvcMock = Substitute.For<IAuthorizationSvc>();
        _filerSvcMock = Substitute.For<IFilerSvc>();
        _decisionsSvcMock = Substitute.For<IDecisionsSvc>();
        _notificationSvcMock = Substitute.For<INotificationSvc>();
        _userMaintenanceSvcMock = Substitute.For<IUserMaintenanceSvc>();
        _smoRegistrationSvcMock = Substitute.For<ISmoRegistrationSvc>();
        _filingRepositoryMock = Substitute.For<IFilingRepository>();
        _filingSummaryRepositoryMock = Substitute.For<IFilingSummaryRepository>();
        _filingPeriodRepositoryMock = Substitute.For<IFilingPeriodRepository>();
        _transactionRepositoryMock = Substitute.For<ITransactionRepository>();
        _referenceDataSvc = Substitute.For<IReferenceDataSvc>();
        _linkageSvcMock = Substitute.For<ILinkageSvc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _filerUserRepositoryMock = Substitute.For<IFilerUserRepository>();
        _linkageRequestRepositoryMock = Substitute.For<ILinkageRequestRepository>();
        _filingContactSummaryRepositoryMock = Substitute.For<IFilingContactSummaryRepository>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _modelMapperMock = new RegistrationModelMapper(_referenceDataSvc);

        _servicesDependencies = new FilingSharedServicesDependencies(
            _filerSvcMock,
            _decisionsSvcMock,
            _authorizationSvcMock,
            _userMaintenanceSvcMock,
            _notificationSvcMock,
            _referenceDataSvc,
            _linkageSvcMock,
            _dateTimeSvcMock
        );

        _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
            _filingRepositoryMock,
            _filingSummaryRepositoryMock,
            _filingPeriodRepositoryMock,
            _registrationRepositoryMock,
            _attestationRepositoryMock,
            _registrationRegistrationContactRepositoryMock,
            _transactionRepositoryMock,
            _filerUserRepositoryMock,
            _linkageRequestRepositoryMock,
            _filingContactSummaryRepositoryMock
        );
    }

    [Test]
    public void CreateSmoRegistrationPage03_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var request = new SmoContactRequest
        {
            County = "Test",
            Name = "Test",
            FaxNumber = new PhoneNumberDto(),
        };

        _ = _smoRegistrationSvcMock.CreateSmoRegistrationPage03(request);

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await controller.CreateSmoRegistrationPage03(request));
    }

    [Test]
    public void CancelSmoRegistration_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;

        _ = _smoRegistrationSvcMock.CancelSmoRegistration(id);

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await controller.CancelSmoRegistration(id));
    }

    [Test]
    public async Task GetRegistrationFilingById_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var response = new SmoRegistrationResponseDto
        {
            Id = id,
        };

        _smoRegistrationSvcMock.GetRegistrationFilingById(id).Returns(Task.FromResult<SmoRegistrationResponseDto?>(response));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.GetRegistrationFilingById(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(response, Is.Not.Null);
            Assert.That(response, Is.InstanceOf<SmoRegistrationResponseDto>());
            Assert.That(response.Id, Is.EqualTo(id));
        });
    }

    [Test]
    public async Task GetSmoOfficers_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var response = new List<SmoOfficerGridDto>
        {
            new()
            {
                FirstName = "Test",
                LastName = "Test",
                CanAuthorize = true,
            }
        };

        _smoRegistrationSvcMock.GetSmoOfficers(id).Returns(Task.FromResult<IEnumerable<SmoOfficerGridDto>>(response));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.GetSmoOfficers(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(response, Is.Not.Empty);
            Assert.That(response, Is.InstanceOf<IEnumerable<SmoOfficerGridDto>>());
        });
    }

    [Test]
    public async Task GetSmoOfficer_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var registrationId = 1;
        var contactId = 1;
        var response = new SmoRegistrationContactDto
        {
            Id = contactId,
            FirstName = "First",
            LastName = "Last",
        };

        _smoRegistrationSvcMock.GetSmoOfficer(registrationId, contactId).Returns(Task.FromResult(response));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.GetSmoOfficer(registrationId, contactId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(response, Is.Not.Null);
            Assert.That(response, Is.InstanceOf<SmoRegistrationContactDto>());
            Assert.That(response.Id, Is.EqualTo(contactId));
        });
    }

    [Test]
    public void CreateSmoAmendmentRegistrationAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;

        _ = _smoRegistrationSvcMock.CreateSmoAmendmentRegistrationAsync(id);

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await controller.CreateSmoAmendmentRegistrationAsync(id));
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_DoesNotExist_ReturnException()
    {
        {
            // Arrange
            _ = _registrationRepositoryMock.FindCommitteeByIdOrName(Arg.Any<string>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.SearchCommitteeByIdOrName("NotExist");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(0));
        }
    }

    [Test]
    public async Task SearchCommitteeByIdOrName_Found_ReturnList()
    {
        {
            // Arrange
            List<Filer> list = new()
            {
                new()
                {
                    Id = 1,
                    FilerStatusId = FilerStatus.Active.Id,
                    CurrentRegistration = new GeneralPurposeCommittee()
                    {
                        StatusId = RegistrationStatus.Accepted.Id,
                        Name = "Test Committee Alpha",
                        Email = "<EMAIL>",
                        CommitteeType = CommitteeType.GeneralPurpose,
                        JurisdictionCounty = "",
                        JurisdictionActive = "",
                        FinancialInstitutionName = "",
                        FinancialInstitutionAccountNumber = "",
                        FinancialInstitutionPhone = "",
                    },
                },
                new()
                {
                    Id = 2,
                    FilerStatusId = FilerStatus.Active.Id,
                    CurrentRegistration = new GeneralPurposeCommittee()
                    {
                        StatusId = RegistrationStatus.Accepted.Id,
                        Name = "Test Committee Bravo",
                        Email = "<EMAIL>",
                        CommitteeType = CommitteeType.GeneralPurpose,
                        JurisdictionCounty = "",
                        JurisdictionActive = "",
                        FinancialInstitutionName = "",
                        FinancialInstitutionAccountNumber = "",
                        FinancialInstitutionPhone = "",
                    },
                },
            };
            _ = _registrationRepositoryMock.FindCommitteeByIdOrName(Arg.Any<string>()).ReturnsForAnyArgs(list);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.SearchCommitteeByIdOrName("Test");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(list.Count));
        }
    }

    [Test]
    public async Task GetLinkedRecipientCommitteeAsync_Found_ShouldReturnValue()
    {
        // Arrange
        var filerId = 1;
        var filerLink = new FilerLink
        {
            Id = 1,
            FilerId = 1,
            FilerLinkTypeId = 1,
            LinkedEntityId = 1,
            EffectiveDate = _dateNow,
            CreatedBy = 1,
            ModifiedBy = 1,
        };
        var filer = new List<Filer>
        {
            new()
            {
                Id = 1,
                FilerStatusId = FilerStatus.Active.Id,
                CurrentRegistration = new GeneralPurposeCommittee()
                {
                    StatusId = RegistrationStatus.Accepted.Id,
                    Name = "Test Committee Alpha",
                    Email = "<EMAIL>",
                    CommitteeType = CommitteeType.GeneralPurpose,
                    JurisdictionCounty = "",
                    JurisdictionActive = "",
                    FinancialInstitutionName = "",
                    FinancialInstitutionAccountNumber = "",
                    FinancialInstitutionPhone = "",
                },
            },
        };

        _filerSvcMock.GetFilerLinkByFilerIdAndLinkTypeAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<FilerLink?>(filerLink));
        _registrationRepositoryMock.FindCommitteeByIdOrName(Arg.Any<string>()).Returns(Task.FromResult<IEnumerable<Filer>>(filer));
        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act
        var result = await controller.GetLinkedRecipientCommitteeAsync(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<CommitteeSearchResultDto>());
        Assert.That(result.Id, Is.EqualTo(1));
    }

    [Test]
    public async Task GetLinkedRecipientCommitteeAsync_NotFound_ShouldReturnEmptyValue()
    {
        // Arrange
        var filerId = 1;

        _filerSvcMock.GetFilerLinkByFilerIdAndLinkTypeAsync(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();
        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act
        var result = await controller.GetLinkedRecipientCommitteeAsync(filerId);

        // Asserts
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<CommitteeSearchResultDto>());
        Assert.That(result.Id, Is.EqualTo(0));
    }

    [Test]
    public async Task UpdateFilerRoleAsync_NotFound_ShouldNotThrowException()
    {
        // Arrange
        var request = new UpdateFilerUserRoleRequest { };
        var smoRegistration = GenerateSampleSmo(1, "Test");

        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();
        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));

        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act
        await controller.UpdatePrimaryFilerUserRoleAsync(smoRegistration.Id, request);

        // Asserts
        Assert.DoesNotThrowAsync(async () => await controller.UpdatePrimaryFilerUserRoleAsync(smoRegistration.Id, request));
    }

    [TestCaseSource(nameof(FilerTestCases))]
    public async Task UpdateFilerRoleAsync_Found_ShouldUpdateSuccessfully(bool isTreasurer, bool isOfficer, string title)
    {
        // Arrange
        var smoRegistration = GenerateSampleSmo(1, "Test");

        var filerUser = new FilerUserDto
        {
            Id = 1,
            FilerRoleId = 1,
        };
        var request = new UpdateFilerUserRoleRequest
        {
            IsTreasurer = isTreasurer,
            IsOfficer = isOfficer,
            Title = title,
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<FilerUserDto?>(filerUser));
        _ = _filerSvcMock.UpdateFilerUserRoleAsync(Arg.Any<long>(), Arg.Any<long>());
        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act & Asserts
        Assert.DoesNotThrowAsync(async () => await controller.UpdatePrimaryFilerUserRoleAsync(smoRegistration.Id, request));
        await _filerSvcMock.Received(1).UpdateFilerUserRoleAsync(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task UpdateSmoRegistrationPage03_Update_NoErrors()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            _ = _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(registration);
            _ = _registrationRepositoryMock.IsUniqueSmoName(Arg.Any<string>(), Arg.Any<long?>()).Returns(Task.FromResult(true));
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoContact, List<WorkFlowError>>(DecisionsWorkflow.SmoContactRuleSet, Arg.Any<DecisionsSmoContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);
            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);

            SmoContactRequest request = new()
            {
                Name = "Test",
                OrganizationAddress = new()
                {
                    Purpose = "SlateMailerOrganization",
                    Country = "",
                    Street = "1585 Kapiolani Blvd STE 1800",
                    Street2 = "",
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96814"
                },
                IsSameAsOrganizationAddress = true,
                PhoneNumber = new PhoneNumberDto(),
                FaxNumber = new PhoneNumberDto(),
                Email = "test.test.com",
                County = "",
                CheckRequiredFieldsFlag = false,
            };

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.UpdateSmoRegistrationPage03(id, request);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task GetSmoTreasurerPage02_Found_Record()
    {
        {
            var regId = 100;
            var treasurerId = 1;
            var treasurerFirstName = "TFirstName";
            var treasurerLastName = "TLastName";
            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(regId, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(regId, treasurerId, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, treasurerFirstName, treasurerLastName));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(regId, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.GetSmoTreasurerPage02(100);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(treasurerId));
                Assert.That(result.FirstName, Is.EqualTo(treasurerFirstName));
                Assert.That(result.LastName, Is.EqualTo(treasurerLastName));
            });
        }
    }

    [Test]
    public async Task GetSmoRegistrationContactsPage04_Found_ReturnList()
    {
        {
            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(100, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 3, FilerRole.SlateMailerOrg_AssistantTreasurer.Name, "Assistant Treasurer", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.GetSmoRegistrationContactsPage04(100);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(registration.RegistrationRegistrationContacts.Count));
        }
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_AddContact_NoErrors()
    {
        {
            var id = 100;

            // Arrange
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            SmoRegistrationContactDto contact = new()
            {
                RoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                FirstName = "TestFirst",
                MiddleName = "",
                LastName = "LastName",
                Email = "<EMAIL>",
                CanAuthorize = true,
                Title = null,
                PhoneNumber = new PhoneNumberDto()
                {
                    InternationalNumber = false,
                    CountryCode = "1",
                    Number = "1111111",
                    Type = "Phone"
                },
                Address = new AddressDto()
                {
                    Type = "Business",
                    Purpose = "PrincipalOfficer",
                    Country = "United States",
                    Street = "123 Sesame Street",
                    Street2 = null,
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96814",
                },
                CheckRequiredFieldsFlag = true
            };

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationContactsPage05(id, contact, true);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_UpdateTreasurer_NoErrors()
    {
        {
            // Arrange
            var id = 100;

            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_AccountManager.Name, "", true, null, "TFirst", "TLast"));
            _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoRegistrationContactDto contact = new()
            {
                Id = 1,
                RoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                FirstName = "TestFirst",
                MiddleName = "",
                LastName = "LastName",
                Email = "<EMAIL>",
                CanAuthorize = true,
                Title = null,
                PhoneNumber = new PhoneNumberDto()
                {
                    InternationalNumber = false,
                    CountryCode = "1",
                    Number = "1111111",
                    Type = "Phone"
                },
                Address = new AddressDto()
                {
                    Type = "Business",
                    Purpose = "PrincipalOfficer",
                    Country = "United States",
                    Street = "123 Sesame Street",
                    Street2 = null,
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96814",
                },
                CheckRequiredFieldsFlag = true
            };

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationContactsPage05(id, contact);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_AddOfficer_NoErrors()
    {
        {
            // Arrange
            var id = 100;

            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));

            _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));
            SmoRegistrationContactDto contact = new()
            {
                RoleId = FilerRole.SlateMailerOrg_Officer.Id,
                FirstName = "TestFirst",
                MiddleName = "",
                LastName = "LastName",
                Email = "<EMAIL>",
                CanAuthorize = true,
                Title = null,
                PhoneNumber = new PhoneNumberDto()
                {
                    InternationalNumber = false,
                    CountryCode = "1",
                    Number = "1111111",
                    Type = "Phone"
                },
                Address = new AddressDto()
                {
                    Type = "Business",
                    Purpose = "PrincipalOfficer",
                    Country = "United States",
                    Street = "123 Sesame Street",
                    Street2 = null,
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96814",
                },
                CheckRequiredFieldsFlag = true
            };

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };


            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationContactsPage05(id, contact);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_AddAssistantTreasurer_NoErrors()
    {
        {
            var id = 100;

            // Arrange
            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_AssistantTreasurer.Name, FilerRole.SlateMailerOrg_AssistantTreasurer.Name, true, 1, "TFirst", "TLast"));
            _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoRegistrationContactDto contact = new()
            {
                RoleId = FilerRole.SlateMailerOrg_AssistantTreasurer.Id,
                FirstName = "TestFirst",
                MiddleName = "",
                LastName = "LastName",
                Email = "<EMAIL>",
                CanAuthorize = true,
                Title = null,
                PhoneNumber = new PhoneNumberDto()
                {
                    InternationalNumber = false,
                    CountryCode = "1",
                    Number = "1111111",
                    Type = "Phone"
                },
                Address = new AddressDto()
                {
                    Type = "Business",
                    Purpose = "PrincipalOfficer",
                    Country = "United States",
                    Street = "123 Sesame Street",
                    Street2 = null,
                    City = "Honolulu",
                    State = "HI",
                    Zip = "96814",
                },
                CheckRequiredFieldsFlag = true
            };

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };


            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationContactsPage05(id, contact);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_UpdateNoPhoneAddress_NoErrors()
    {
        {
            // Arrange
            var id = 100;

            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast");
            existingTreasurer.RegistrationContact!.AddressList!.Addresses!.RemoveAt(0);
            existingTreasurer.RegistrationContact!.PhoneNumberList!.PhoneNumbers!.RemoveAt(0);
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_AccountManager.Name, "", true, null, "TFirst", "TLast"));
            _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(registration));
            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoRegistrationContactDto contact = new()
            {
                Id = 2,
                RoleId = FilerRole.SlateMailerOrg_Officer.Id,
                FirstName = "TestFirst",
                MiddleName = "",
                LastName = "LastName",
                Email = "<EMAIL>",
                CanAuthorize = true,
                Title = null,
                CheckRequiredFieldsFlag = true
            };

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };


            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);
            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationContactsPage05(id, contact);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationContactsPage05_UpdateTitleAssistantTreasurer_ReturnInvalid()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Officer.Name, "Principal", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_AssistantTreasurer.Name, "", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            SmoRegistrationContactDto contact = new()
            {
                Id = 1,
                RoleId = FilerRole.SlateMailerOrg_Officer.Id,
                FirstName = "TestFirst",
                MiddleName = "",
                LastName = "LastName",
                Email = "<EMAIL>",
                CanAuthorize = true,
                Title = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
                CheckRequiredFieldsFlag = true
            };

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };


            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);
            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationContactsPage05(id, contact);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Has.Member(new WorkFlowError("Title", "ErrGlobal0002", "Validation", $"An assistant treasurer already exists. Please select another title.")));
                Assert.That(result.Valid, Is.EqualTo(false));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_Transfer_NoErrors()
    {
        {
            var id = 100;

            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "ORIG", "TREASURER");
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "", true, null, "ORIG", "PRESIDENT"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoTreasurerTransferDto transfer = new()
            {
                NewTreasurerId = 2,
                PreviousTreasurerKeep = true,
                PreviousTreasurerTitle = "President"
            };

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationTransferTreasurer(id, transfer);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_TransferAssistantTreasurer_noErrors()
    {
        {
            var id = 100;

            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "ORIG", "TREASURER");
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "", true, null, "ORIG", "PRESIDENT"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoTreasurerTransferDto transfer = new()
            {
                NewTreasurerId = 2,
                PreviousTreasurerKeep = true,
                PreviousTreasurerTitle = FilerRole.SlateMailerOrg_AssistantTreasurer.Name,
            };

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationTransferTreasurer(id, transfer);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_TreasurerDontKeep_noErrors()
    {
        {
            var id = 100;

            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "ORIG", "TREASURER");
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "", true, null, "ORIG", "PRESIDENT"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoTreasurerTransferDto transfer = new()
            {
                NewTreasurerId = 2,
                PreviousTreasurerKeep = false,
                PreviousTreasurerTitle = null,
            };

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationTransferTreasurer(id, transfer);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_AssistantTreasurer_noErrors()
    {
        {
            var id = 100;
            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "ORIG", "TREASURER");
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "", true, null, "ORIG", "ASSISTANT"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoTreasurerTransferDto transfer = new()
            {
                NewTreasurerId = 2,
                PreviousTreasurerKeep = true,
                PreviousTreasurerTitle = null,
            };

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationTransferTreasurer(id, transfer);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_InvalidId_Error()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "ORIG", "TREASURER");
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "", true, null, "ORIG", "PRESIDENT"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            SmoTreasurerTransferDto transfer = new()
            {
                NewTreasurerId = 2,
                PreviousTreasurerKeep = true,
                PreviousTreasurerTitle = "President"
            };

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act and Assert
            var ex = Assert.ThrowsAsync<KeyNotFoundException>(
                () => controller.PostSmoRegistrationTransferTreasurer(0, transfer)
            );

            Assert.That(ex.Message, Is.EqualTo($"Registration not Found Id=0"));

        }
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_SameId_Error()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "ORIG", "TREASURER");
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "", true, null, "ORIG", "PRESIDENT"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            SmoTreasurerTransferDto transfer = new()
            {
                NewTreasurerId = 1,
                PreviousTreasurerKeep = true,
                PreviousTreasurerTitle = "President"
            };

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act and Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(
                () => controller.PostSmoRegistrationTransferTreasurer(100, transfer)
            );

            Assert.That(ex.Message, Is.EqualTo($"Transfer to the same person not allowed."));

        }
    }

    [Test]
    public async Task PostSmoRegistrationTransferTreasurer_DontKeep_NoError()
    {
        {
            var id = 100;
            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            RegistrationRegistrationContact existingTreasurer = GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "ORIG", "TREASURER");
            registration.RegistrationRegistrationContacts.Add(existingTreasurer);
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "", true, null, "ORIG", "PRESIDENT"));
            Registration populated = await _registrationRepositoryMock.Create(registration);
            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            SmoTreasurerTransferDto transfer = new()
            {
                NewTreasurerId = 2,
                PreviousTreasurerKeep = false
            };

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.PostSmoRegistrationTransferTreasurer(id, transfer);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });

        }
    }

    [Test]
    public async Task DeleteSmoRegistrationContactsPage06_ValidId_NoErrors()
    {
        {
            var id = 100;
            var users = GenerateUsers();
            var filerUsers = GenerateFilerUsers().Select(FilerUserDto.MapToDto).ToList();

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
            _filerSvcMock.GetFilerUsersAsync(Arg.Any<long>()).Returns(Task.FromResult(filerUsers));

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };


            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.DeleteSmoRegistrationContactsPage06(id, 2);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task DeleteSmoRegistrationContactsPage06_InValidId_Errors()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);
            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act & Assert
            var ex = Assert.ThrowsAsync<KeyNotFoundException>(
                () => controller.DeleteSmoRegistrationContactsPage06(999, 999)
            );

            Assert.That(ex.Message, Is.EqualTo($"Registration Contact not Found Id=999"));
        }
    }

    [Test]
    public async Task DeleteSmoRegistrationContactsPage06_RemoveTreasurer_Errors()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);
            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(
                () => controller.DeleteSmoRegistrationContactsPage06(id, 1)
            );

            Assert.That(ex.Message, Is.EqualTo($"Cannot delete Officer"));
        }
    }
    [Test]
    public async Task GetSmoAuthorizerPage04_Found_ReturnList()
    {
        {
            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(100, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", false, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 3, FilerRole.SlateMailerOrg_AccountManager.Name, "", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.GetSmoAuthorizerPage04(100);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
        }
    }


    [Test]
    public async Task DeleteSmoAuthorizerPage04_ValidId_noErrors()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );
            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);

            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.DeleteSmoAuthorizerPage04(id, 2);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public void CompleteTreasurerAcknowledgementAsync_SufficientPermission_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var filerUser = GenerateFilerUsers()
            .Where(x => x.FilerRoleId == FilerRole.SlateMailerOrg_Treasurer.Id)
            .Select(x => new FilerUserDto
            {
                Id = x.Id,
                FilerId = x.FilerId,
                FilerRoleId = x.FilerRoleId,
                FilerRole = x.FilerRole,
                UserId = x.UserId,
                User = x.User
            })
            .FirstOrDefault();
        var registrationContacts = GenerateRegistrationContacts();

        _registrationRepositoryMock.FindSlateMailerOrganizationById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _filerSvcMock.GetFilerUserByUserIdAsync(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<FilerUserDto?>(filerUser));
        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act & Asserts
        Assert.DoesNotThrowAsync(async () => await controller.CompleteTreasurerAcknowledgementAsync(id));
    }

    [Test]
    public async Task GetTreasurerAcknowledgementContactsAsync_Found_ShouldReturnResult()
    {
        // Arrange
        var id = 1;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act
        var result = await controller.GetTreasurerAcknowledgementContactsAsync(id);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<TreasurerAcknowledgementContactResponseDto>());
        });
    }

    [Test]
    public void SendAcknowledgementNotificationsAsync_Found_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var permissionId = Permission.Registration_SlateMailerOrganization_CompleteTreasurerAcknowledgment.Id;
        var smoRegistration = GenerateSampleSmo(1, "Test");
        var registrationContacts = GenerateRegistrationContacts();
        var users = GenerateUsers();
        var linkageRequests = new List<SendLinkageRequestDto>()
        {
            new()
            {
                RecipientEmail = "<EMAIL>",
                RecipientName = "John Smith",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 3,
            },
            // simulate object missing RecipientEmail
            new()
            {
                RecipientName = "John Smith",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 4,
            },
            // simulate object missing RecipientName
            new()
            {
                RecipientEmail = "<EMAIL>",
                FilerId = 1,
                FilerRoleId = 2,
                RegistrationContactId = 5,
            }
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationBasicById(Arg.Any<long>()).Returns(Task.FromResult<SlateMailerOrganization?>(smoRegistration));
        _registrationRepositoryMock.FindSmoRegistrationContactsById(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<RegistrationRegistrationContact>>(registrationContacts));
        _userMaintenanceSvcMock.GetListUsersByUserNameAsync(Arg.Any<List<string>>()).Returns(Task.FromResult(users));
        _notificationSvcMock.SendUserNotification(Arg.Any<SendUserNotificationRequest>()).Returns(Task.CompletedTask);
        _registrationRegistrationContactRepositoryMock.GetLinkageRequestRecipientsForPermission(Arg.Is<long>(id), Arg.Is(permissionId)).Returns(linkageRequests);
        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act & Asserts
        Assert.DoesNotThrowAsync(async () => await controller.SendAcknowledgementNotificationsAsync(id));
    }

    [Test]
    public async Task DeleteSmoAuthorizerPage04_ValidAuthorizedUser_noErrors()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();


            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_AccountManager.Name, "", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);
            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                 _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.DeleteSmoAuthorizerPage04(id, 2);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
            Assert.Multiple(() =>
            {
                Assert.That(result.Id, Is.EqualTo(id));
                Assert.That(result.ValidationErrors, Is.Empty);
                Assert.That(result.Valid, Is.EqualTo(true));
            });
        }
    }

    [Test]
    public async Task DeleteSmoAuthorizerPage04_InValidId_noErrors()
    {
        {
            var id = 100;

            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();


            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(id, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 1, FilerRole.SlateMailerOrg_Treasurer.Name, "Treasurer", true, 1, "TFirst", "TLast"));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(id, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));

            var expectedResponse = new RegistrationResponseDto
            {
                Id = id,
                Valid = true,
                ValidationErrors = []
            };

            _ = _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationContact, List<WorkFlowError>>(DecisionsWorkflow.SmoTreasurerRuleSet, Arg.Any<DecisionsSmoRegistrationContact>(), Arg.Any<bool>()).ReturnsForAnyArgs([]);

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act & Assert
            var ex = Assert.ThrowsAsync<KeyNotFoundException>(
                () => controller.DeleteSmoAuthorizerPage04(999, 999)
            );

            Assert.That(ex.Message, Is.EqualTo($"Registration Contact not Found Id=999"));
        }
    }

    [Test]
    public async Task DeleteIndividualAuthorizer_ValidId_ReturnsRegistrationId()
    {
        // Arrange
        var contactId = 1;
        var registrationId = 1;
        var mock = GenerateRegistrationContacts();
        var expectedRegistrationContact = mock[0];

        _ = _repositoriesDependencies.RegistrationRepository.GetSmoRegistrationRegistrationContactById(registrationId, contactId)!
            .Returns(Task.FromResult(expectedRegistrationContact));

        _ = _repositoriesDependencies.RegistrationRegistrationContactRepository.Update(expectedRegistrationContact)
            .Returns(Task.FromResult(expectedRegistrationContact));

        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act
        await controller.DeleteIndividualAuthorizer(registrationId, contactId);

        // Assert
        _ = _repositoriesDependencies.RegistrationRegistrationContactRepository.Received(1).Update(Arg.Any<RegistrationRegistrationContact>());
    }

    [Test]
    public void DeleteIndividualAuthorizer_InvalidId_ThrowsKeyNotFoundException()
    {
        // Arrange
        var registrationId = 1;
        var invalidId = 999;

        _ = _repositoriesDependencies.RegistrationRepository.GetSmoRegistrationRegistrationContactById(Arg.Any<long>(), Arg.Any<long>())
            .ThrowsAsync(new KeyNotFoundException($"Registration Contact not Found Id={invalidId}"));

        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(
            () => controller.DeleteIndividualAuthorizer(registrationId, invalidId)
        );

        Assert.That(ex.Message, Is.EqualTo($"Registration Contact not Found Id={invalidId}"));
    }

    [Test]
    public async Task SmoRegistrationController_UpdateRegistration()
    {
        // Arrange
        var id = 1;
        var newStatusId = 2;
        var newActivityLevel = "city";
        var newDateQualified = _dateNow;
        var oldSmoRegistration = new SlateMailerOrganization
        {
            Id = id,
            Name = "TestName",
            StatusId = 1,
            Email = "<EMAIL>",
            County = "",
            ActivityLevel = "oldActivity",
            CampaignCommittee = false,
            QualifiedCommittee = false
        };
        var newSmoRegistration = new SlateMailerOrganization
        {
            Id = id,
            Name = "TestName",
            StatusId = newStatusId,
            Email = "<EMAIL>",
            County = "",
            ActivityLevel = newActivityLevel,
            CampaignCommittee = true,
            QualifiedCommittee = true,
            DateQualified = newDateQualified,
        };
        var request = new SlateMailerOrganizationRequest
        {
            ActivityLevel = newActivityLevel,
            CommitteeId = null,
            IsCampaignCommittee = true,
            IsQualifiedCommittee = true,
            DateQualified = newDateQualified,
            CheckRequiredFieldsFlag = false
        };

        // Mock `_smoRegistrationSvc.UpdateSmoRegistration` to return a valid response
        var expectedResponse = new RegistrationResponseDto
        {
            Id = id,
            Valid = true,
            StatusId = newStatusId,
            ValidationErrors = []
        };

        _smoRegistrationSvcMock.UpdateSmoRegistration(Arg.Any<long>(), Arg.Any<SlateMailerOrganizationRequest>()).Returns(Task.FromResult(expectedResponse));

        // Inject the mocked service into the controller
        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.UpdateSmoRegistration(id, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<RegistrationResponseDto>());
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.Valid, Is.EqualTo(true));
            Assert.That(result.StatusId, Is.EqualTo(newStatusId));
        });
    }

    [Test]
    public void UpdateSmoRegistration_WhenRecordNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        long invalidId = 999;
        var request = new SlateMailerOrganizationRequest { ActivityLevel = "State" };

        _smoRegistrationSvcMock.UpdateSmoRegistration(invalidId, request).ThrowsAsync(new KeyNotFoundException($"Registration not Found Id={invalidId}"));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(
            () => controller.UpdateSmoRegistration(invalidId, request)
        );

        Assert.That(ex.Message, Is.EqualTo($"Registration not Found Id={invalidId}"));
    }

    [Test]
    public async Task UpdateSmoRegistration_WhenDecisionsFail_ReturnsValidationErrors()
    {
        // Arrange
        long id = 1;
        var request = new SlateMailerOrganizationRequest
        {
            ActivityLevel = "City",
            IsCampaignCommittee = true,
            IsQualifiedCommittee = true,
            DateQualified = _dateNow,
            CheckRequiredFieldsFlag = false
        };

        var existingRegistration = new SlateMailerOrganization
        {
            Id = id,
            Name = "TestName",
            StatusId = 1,
            Email = "<EMAIL>",
            County = "",
            ActivityLevel = "oldActivity",
            CampaignCommittee = false,
            QualifiedCommittee = false
        };

        var validationErrors = new List<WorkFlowError>
        {
            new("ActivityLevel", "Err001", "Validation", "Invalid activity level.")
        };

        _registrationRepositoryMock.FindSlateMailerOrganizationById(id).Returns(Task.FromResult<SlateMailerOrganization?>(existingRegistration));

        // Mock decision service to return validation errors
        _decisionsSvcMock.InitiateWorkflow<DecisionsSmoRegistrationDetails, List<WorkFlowError>>(
                DecisionsWorkflow.SmoRegistrationsDetailsRuleSet,
                Arg.Any<DecisionsSmoRegistrationDetails>(),
                Arg.Any<bool>()
            )
            .Returns(Task.FromResult(validationErrors)); // Simulating validation errors

        var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
        var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

        // Act
        var result = await controller.UpdateSmoRegistration(id, request);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
        });
        Assert.That(result.ValidationErrors.First().FieldName, Is.EqualTo("ActivityLevel"));
    }

    [Test]
    public void AttestRegistrationAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var registrationId = 1;

        _ = _smoRegistrationSvcMock.AttestRegistrationAsync(registrationId);

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await controller.AttestRegistrationAsync(registrationId));
    }

    [Test]
    public async Task GetResponsibleOfficerContactsAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var registrationId = 1;
        var expected = new List<SmoRegistrationContactDto>
        {
            new ()
            {
                Id = 1,
            }
        };

        _smoRegistrationSvcMock.GetResponsibleOfficerContactsAsync(registrationId).Returns(Task.FromResult<IEnumerable<SmoRegistrationContactDto>>(expected));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.GetResponsibleOfficerContactsAsync(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<SmoRegistrationContactDto>>());
        });
    }

    [Test]
    public async Task GetRegistrationAttestationAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var registrationId = 1;
        var expected = new SmoRegistrationAttestationResponseDto
        {
            FirstName = "Test",
            LastName = "Test",
            ExecutedAt = _dateNow,
            Title = "Test"
        };

        _smoRegistrationSvcMock.GetRegistrationAttestationAsync(registrationId).Returns(Task.FromResult(expected));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.GetRegistrationAttestationAsync(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationAttestationResponseDto>());
        });
    }

    [Test]
    public void SendRegistrationForAttestationAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var registrationId = 1;
        var request = new SmoRegistrationSendForAttestationRequest
        {
            RegistrationRegistrationContactIds = new List<long> { 1, 2 }
        };

        _ = _smoRegistrationSvcMock.SendRegistrationForAttestationAsync(registrationId, request);

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await controller.SendRegistrationForAttestationAsync(registrationId, request));
    }

    [Test]
    public async Task GetPendingItemsAsync_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var registrationId = 1;
        var expected = new List<PendingItemDto>
        {
            new()
            {
                Item = "Test",
                Status = "Test",
            }
        };

        _smoRegistrationSvcMock.GetPendingItemsAsync(registrationId).Returns(Task.FromResult(expected));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.GetPendingItemsAsync(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<PendingItemDto>>());
        });
    }

    [Test]
    public async Task GetSmoGeneralInformation_ValidId_noErrors()
    {
        {
            var regId = 100;
            var treasurerId = 1;
            var treasurerFirstName = "TFirstName";
            var treasurerLastName = "TLastName";
            using var factory = new DatabaseContextFactory();
            using var context = await factory.CreateContext();

            // Arrange
            _registrationRepositoryMock = new RegistrationRepository(context);
            SlateMailerOrganization registration = GenerateSampleSmo(regId, "Test");
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(regId, treasurerId, FilerRole.SlateMailerOrg_Treasurer.Name, "", true, 1, treasurerFirstName, treasurerLastName));
            registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(regId, 2, FilerRole.SlateMailerOrg_Officer.Name, "President", true, null, "TFirst", "TLast"));
            Registration populated = await _registrationRepositoryMock.Create(registration);

            _servicesDependencies = new FilingSharedServicesDependencies(
                _filerSvcMock,
                _decisionsSvcMock,
                _authorizationSvcMock,
                _userMaintenanceSvcMock,
                _notificationSvcMock,
                _referenceDataSvc,
                _linkageSvcMock,
                _dateTimeSvcMock
            );

            _repositoriesDependencies = new FilingSharedRepositoriesDependencies(
                _filingRepositoryMock,
                _filingSummaryRepositoryMock,
                _filingPeriodRepositoryMock,
                _registrationRepositoryMock,
                _attestationRepositoryMock,
                _registrationRegistrationContactRepositoryMock,
                _transactionRepositoryMock,
                _filerUserRepositoryMock,
                _linkageRequestRepositoryMock,
                _filingContactSummaryRepositoryMock
            );

            var smoRegistrationSvc = new SmoRegistrationSvc(_servicesDependencies, _repositoriesDependencies, _modelMapperMock);
            var controller = new SmoRegistrationController(_authorizationMock, smoRegistrationSvc, _authorizationSvcMock);

            // Act
            var result = await controller.GetSmoGeneralInformationAsync(100);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.Multiple(() =>
            {
                Assert.That(result.RegistrationDetail, Is.Not.Null);
                Assert.That(result.Treasurer, Is.Not.Null);
            });
        }
    }

    [Test]
    public async Task SearchSmoByIdOrNameAsync_ValidRequest_ReturnResult()
    {
        // Arrange
        var query = "1";
        var statusId = 1;
        var response = new List<SmoRegistrationBasicResponseDto>
        {
            new ()
            {
                Id = 1,
            }
        };

        _smoRegistrationSvcMock
            .SearchSmoRegistrationByIdOrNameAsync(Arg.Any<string>(), Arg.Any<long>())
            .Returns(Task.FromResult(response));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.SearchSmoRegistrationByIdOrNameAsync(query, statusId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<SmoRegistrationBasicResponseDto>>());
        });
    }

    #region SmoTerminationForEfilePath
    [Test]
    public void SmoTerminationForEfilePath_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var smo = new EfileSlateMailerOrganizationDto
        {
            SlateMailerOrg = new SlateMailerOrganization
            {
                Name = "Initial Organization",
                Email = "<EMAIL>",
                StatusId = RegistrationStatus.Draft.Id,
                ParentId = null, // Initial filing
                VerificationExecutedAt = _dateNow
            },
            AttesterName = "John Doe",
            AttesterRole = "Treasurer",
            VendorUserId = 1,
            LinkedCommitteeId = 123
        };

        _ = _smoRegistrationSvcMock.SmoTerminationForEfile(smo);

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await controller.SmoTerminationForEfile(smo));
    }
    #endregion
    #region UpdateNoticeOfTerminationSmoRegistrationAsync
    [Test]
    public void UpdateNoticeOfTerminationSmoRegistrationAsync_ValidRequest_ShouldExecuteSuccessfully()
    {
        // Arrange
        var id = 1;
        var terminationPayload = new NoticeOfTerminationRequest
        {
            IsTerminating = true,
            TerminationDate = _dateNow,
            CheckRequiredFields = true
        };

        _ = _smoRegistrationSvcMock.UpdateNoticeOfTerminationSmoRegistrationAsync(Arg.Any<long>(), Arg.Any<NoticeOfTerminationRequest>());

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await controller.UpdateNoticeOfTerminationSmoRegistrationAsync(id, terminationPayload));
    }
    #endregion

    #region IsRegistrationTerminatingAsync
    [Test]
    public async Task IsRegistrationTerminatingAsync_ValidRequest_ReturnResult()
    {
        // Arrange
        var id = 1;
        var isTerminating = true;
        _smoRegistrationSvcMock
            .IsRegistrationTerminatingAsync(Arg.Any<long>())
            .Returns(Task.FromResult(isTerminating));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.IsRegistrationTerminatingAsync(id);

        // Assert
        Assert.That(result, Is.True);
    }
    #endregion

    #region SearchLatestAcceptedSmoRegistrationOfficerByNameAsync
    [Test]
    public async Task SearchLatestAcceptedSmoRegistrationOfficerByNameAsync_ValidRequest_ReturnResult()
    {
        // Arrange
        var id = 1;
        var query = "1";
        var response = new List<SmoRegistrationContactDto>
        {
            new ()
            {
                Id = 1,
            }
        };

        _smoRegistrationSvcMock
            .SearchLatestAcceptedSmoRegistrationOfficerByNameAsync(Arg.Any<long>(), Arg.Any<string>())
            .Returns(Task.FromResult(response));

        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);

        // Act
        var result = await controller.SearchLatestAcceptedSmoRegistrationOfficerByNameAsync(id, query);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<SmoRegistrationContactDto>>());
        });
    }
    #endregion

    #region GetCurrentUserRoleByIdAsync
    [Test]
    public async Task GetCurrentUserRoleByIdAsync_ValidRequest_ReturnResult()
    {
        // Arrange
        var roleId = 91;

        _smoRegistrationSvcMock
            .GetCurrentUserRoleByIdAsync(Arg.Any<long>())
            .Returns(roleId);
        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);


        // Act
        var result = await controller.GetCurrentUserRoleByIdAsync(1L);

        // Assert
        Assert.That(result, Is.EqualTo(roleId));
    }
    #endregion

    #region GetSmoRegistrationFilingSummary
    [Test]
    public async Task GetSmoRegistrationFilingSummary_ValidRequest_ReturnResult()
    {

        // Arrange
        var response = new SmoRegistrationFilingSummaryDto
        {
            Registration = new(),
            FilerName = "Name",
            Officers = [],
            IndividualAuthorizers = []
        };
        _smoRegistrationSvcMock.GetSmoRegistrationFilingSummary(Arg.Any<long>()).Returns(response);
        // Act
        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);
        var result = await controller.GetSmoRegistrationFilingSummary(1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SmoRegistrationFilingSummaryDto>());
        });
    }
    #endregion

    #region HandleRegistrationEditAsync
    [Test]
    public async Task HandleRegistrationEditAsync_ValidRequest_ReturnResult()
    {

        // Arrange
        var response = new SmoRegistrationFilingSummaryDto
        {
            Registration = new(),
            FilerName = "Name",
            Officers = [],
            IndividualAuthorizers = []
        };
        // Act
        var controller = new SmoRegistrationController(_authorizationMock, _smoRegistrationSvcMock, _authorizationSvcMock);
        await controller.HandleRegistrationEditAsync(1);

        // Assert
        await _smoRegistrationSvcMock.Received(1).HandleRegistrationEditAsync(Arg.Any<long>());

    }
    #endregion
    #region Private
    private static IEnumerable<object[]> FilerTestCases()
    {
        yield return new object[] { true, false, "" };
        yield return new object[] { false, true, FilerRole.SlateMailerOrg_AssistantTreasurer.Name };
        yield return new object[] { false, true, FilerRole.SlateMailerOrg_Officer.Name };
        yield return new object[] { false, false, "" };
    }

    /// <summary>
    /// Create a sample registration contact
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
    private static RegistrationRegistrationContact GenerateSampleRegistratonContact(long registrationId, long contactId, string role, string title, bool canAuthorize, long? userId, string firstName, string lastName)
    {
        return new()
        {
            Id = contactId,
            CanAuthorizeSlateMailerContents = canAuthorize,
            CreatedBy = userId ?? 0,
            ModifiedBy = userId ?? 0,
            RegistrationId = registrationId,
            Role = role,
            Title = title,
            RegistrationContact = new()
            {
                FirstName = firstName,
                LastName = lastName,
                MiddleName = string.Empty,
                Email = "<EMAIL>",
                AddressList = new()
                {
                    Addresses = new()
                    {
                        new()
                        {
                            Country = "United States",
                            Street = "Street1",
                            Street2 = "Street2",
                            City = "Honolulu",
                            State = "HI",
                            Zip = "96812",
                            Type = "Residential",
                            Purpose = "PrincipalOfficer",
                        },
                    }
                },
                PhoneNumberList = new()
                {
                    PhoneNumbers = new()
                    {
                        new()
                        {
                            InternationalNumber = false,
                            CountryCode = "1",
                            Number = "1231234",
                            Type = "Phone",
                        }
                    }
                }
            },

            // Default
            CapitalContributionOver10K = false,
            PercentOfOwnership = 100,
            CumulativeCapitalContributions = 0,
            TenPercentOrGreater = false,
        };
    }

    private static SlateMailerOrganization GenerateSampleSmo(long id, string sampleName)
    {
        return new()
        {
            Id = id,
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Base",
            Email = "<EMAIL>",
            FilerId = 1,
            OriginalId = id,
            AddressList = new()
            {
                Addresses = new()
                {
                    new()
                    {
                        Type = "Business",
                        Purpose = "PrincipalOfficer",
                        Country = "United States",
                        Street = "1585 KAPIOLANI BLVD STE 1800",
                        Street2 = null,
                        City = "Honolulu",
                        State = "HI",
                        Zip = "96814",
                    }
                }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                {
                    new()
                    {
                        InternationalNumber = false,
                        CountryCode = "1",
                        Number = "1231234",
                        Type = "Phone",
                    }
                }
            },
            CommitteeId = 1,
            Committee = null,

            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.SlateMailerOrg.Id,
                CurrentRegistration = new GeneralPurposeCommittee()
                {
                    Id = 3,
                    StatusId = RegistrationStatus.Accepted.Id,

                    Name = sampleName,
                    Email = "<EMAIL>",
                    JurisdictionCounty = string.Empty,
                    JurisdictionActive = string.Empty,
                    FinancialInstitutionName = string.Empty,
                    FinancialInstitutionPhone = string.Empty,
                    FinancialInstitutionAccountNumber = string.Empty,
                },
                Users = new()
                {
                    new()
                    {
                        FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                        User = new()
                        {
                            FirstName = "FirstName",
                            LastName = "LastName",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            }
        };
    }

    private static List<FilerUser> GenerateFilerUsers()
    {
        return new List<FilerUser>
        {
             new()
             {
                Id = 1,
                FilerId = 1,
                FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                UserId = 1,
                FilerRole = FilerRole.SlateMailerOrg_Treasurer,
                User = GenerateUsers().FirstOrDefault(x => x.Id == 1)
             }
        };
    }

    private static List<RegistrationRegistrationContact> GenerateRegistrationContacts()
    {
        return new List<RegistrationRegistrationContact>
        {
            new()
            {
                Id = 1,
                Role = FilerRole.SlateMailerOrg_Treasurer.Name,
                Title = "",
                PercentOfOwnership = 1m,
                CapitalContributionOver10K = false,
                CumulativeCapitalContributions = 0m,
                TenPercentOrGreater = false,
                CreatedBy = 0,
                ModifiedBy = 0,
                HasAcknowledged = false,
                RegistrationId = 1,
                CanAuthorizeSlateMailerContents = true,
                RegistrationContact = new RegistrationContact
                {
                    Id = 1,
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "Test",
                    MiddleName = "",
                    UserId = 1,
                }
            }
        };
    }

    private static List<User> GenerateUsers()
    {
        return new List<User>
        {
            new()
            {
                Id = 1,
                FirstName = "Test",
                LastName = "Test",
                EmailAddress = "<EMAIL>",
                EntraOid = "TestOid"
            }
        };
    }
    #endregion
}
