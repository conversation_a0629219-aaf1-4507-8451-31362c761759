@using SOS.CalAccess.FilerPortal.Models.Localization;
@using SOS.CalAccess.FilerPortal.ViewHelpers;
@using SOS.CalAccess.UI.Common.Enums;

@inject IHtmlLocalizer<SharedResources> Localizer
@model SOS.CalAccess.FilerPortal.Models.Registrations.LobbyistEmployerRegistration.LobbyistCertificationViewModel;
@{
    var progressBar = LobbyistRegistrationProgressBarHelper.BuildProgressBar(
        version: Model.Version,
        id: Model.Id,
        type: RegistrationConstants.RegistrationType.LobbyistEmployer,
        currentStep: 2,
        ViewData
    );

    var buttonConfig = new ButtonBarModel
    {
        LeftButtons = new List<ButtonConfig>
        {
            new ()
            {
                Type = ButtonType.Custom,
                HtmlContent = Html.Raw(@"<a href='#'
                                       class='btn btn-outline-primary'
                                       data-bs-toggle='modal'
                                       data-bs-target='#cancelConfirmModal'>
                                       Cancel
                                    </a>"),
            },
            ButtonBarModel.DefaultContinue,
        },
        RightButtons = new List<ButtonConfig>
        {      
        }
    };

    var cancelModal = new CancelConfirmModal(
        Title: SharedLocalizer["Common.CancelConfirmationTitle"].Value,
        Body: SharedLocalizer["Common.CancelConfirmationBody"].Value,
        CloseButtonText: SharedLocalizer["Common.CancelConfirmationClose"].Value,
        SubmitButtonText: SharedLocalizer["Common.CancelConfirmationSubmit"].Value,
        ActionUrl: Url.Action("Step02LobbyistList", "LobbyistEmployerRegistration", new { id = Model.LobbyistEmployerOrLobbyingFirmId }) ?? "",
        Method: "GET"
    );
}


@Html.HiddenFor(m => m.Version)
@Html.StepHeader(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationTitle2)
<h3>@SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationTitle]</h3>

<partial name="_LayoutProgressbar" model="progressBar" />
<div class="form-container">
    <h3> @SharedLocalizer[ResourceConstants.LobbyistEmployerRegistrationInitiateCertifyLobbyistTitle]</h3>

    @Html.TextBlock(SharedLocalizer, ResourceConstants.LobbyistEmployerRegistrationInitiateCertifyLobbyistSummaryText)

    @using (Html.BeginForm("Step02InitiateCertification", "LobbyistEmployerRegistration", FormMethod.Post))
    {
        <partial name="_LobbyistForm" model="Model" />
        <partial name="_ButtonBar" model="buttonConfig" />
    }
</div>
<partial name="_CancelConfirmModal" model="cancelModal" />
<style>
    .form-container {
        width: 90%;
        margin: auto;
        padding: 20px 0px;
    }
</style>
