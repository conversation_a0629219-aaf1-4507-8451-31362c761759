using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.Tests.FilerRegistration.Registrations;

[TestFixture]
public class LobbyistEmployerResponseDtoTest
{
    private LobbyistEmployer _lobbyistEmployer;
    private AddressList _addressList;
    private PhoneNumberList _phoneNumberList;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        // Set up Address List
        var address1 = new Address
        {
            Id = 1,
            Street = "123 Main St",
            Street2 = "Suite 100",
            City = "Sacramento",
            State = "CA",
            Country = "USA",
            Zip = "95814",
            Type = "Business",
            Purpose = "Mailing"
        };

        var address2 = new Address
        {
            Id = 2,
            Street = "456 State St",
            Street2 = "Floor 2",
            City = "Sacramento",
            State = "CA",
            Country = "USA",
            Zip = "95819",
            Type = "Business",
            Purpose = "Physical"
        };

        _addressList = new AddressList
        {
            Id = 100,
            Addresses = new List<Address> { address1, address2 }
        };

        // Set up Phone Number List
        var phone1 = new PhoneNumber
        {
            Id = 1,
            Number = "************",
            Type = "Business"
        };

        var phone2 = new PhoneNumber
        {
            Id = 2,
            Number = "************",
            Type = "Fax"
        };

        _phoneNumberList = new PhoneNumberList
        {
            Id = 200,
            PhoneNumbers = new List<PhoneNumber> { phone1, phone2 }
        };

        // Set up Lobbyist Employer
        _lobbyistEmployer = new LobbyistEmployer
        {
            Id = 1001,
            Name = "Test Lobbyist Employer",
            Email = "<EMAIL>",
            StatusId = 1,
            FilerId = 2001,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Corporation",
            BusinessActivity = "Technology",
            BusinessDescription = "Software Development",
            InterestType = "Business",
            IndustryDescription = "Software Services",
            IndustryPortion = "Government Solutions",
            NumberOfMembers = 50,
            StateLegislatureLobbying = true,
            DateQualified = _dateNow,
            LegislativeSessionId = 3001,
            AddressListId = _addressList.Id,
            AddressList = _addressList,
            PhoneNumberListId = _phoneNumberList.Id,
            PhoneNumberList = _phoneNumberList,
        };
    }

    [Test]
    public void Constructor_ShouldMapBasicProperties()
    {
        // Act
        var dto = new LobbyistEmployerResponseDto(_lobbyistEmployer);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(dto.Id, Is.EqualTo(_lobbyistEmployer.Id));
            Assert.That(dto.Name, Is.EqualTo(_lobbyistEmployer.Name));
            Assert.That(dto.Email, Is.EqualTo(_lobbyistEmployer.Email));
            Assert.That(dto.StatusId, Is.EqualTo(_lobbyistEmployer.StatusId));
            Assert.That(dto.FilerId, Is.EqualTo(_lobbyistEmployer.FilerId));
            Assert.That(dto.Version, Is.EqualTo(_lobbyistEmployer.Version));
            Assert.That(dto.EmployerName, Is.EqualTo(_lobbyistEmployer.EmployerName));
            Assert.That(dto.EmployerType, Is.EqualTo(_lobbyistEmployer.EmployerType));
            Assert.That(dto.BusinessActivity, Is.EqualTo(_lobbyistEmployer.BusinessActivity));
            Assert.That(dto.BusinessDescription, Is.EqualTo(_lobbyistEmployer.BusinessDescription));
            Assert.That(dto.InterestType, Is.EqualTo(_lobbyistEmployer.InterestType));
            Assert.That(dto.IndustryDescription, Is.EqualTo(_lobbyistEmployer.IndustryDescription));
            Assert.That(dto.IndustryPortion, Is.EqualTo(_lobbyistEmployer.IndustryPortion));
            Assert.That(dto.NumberOfMembers, Is.EqualTo(_lobbyistEmployer.NumberOfMembers));
            Assert.That(dto.StateLegislatureLobbying, Is.EqualTo(_lobbyistEmployer.StateLegislatureLobbying));
            Assert.That(dto.DateQualified, Is.EqualTo(_lobbyistEmployer.DateQualified));
            Assert.That(dto.LegislativeSessionId, Is.EqualTo(_lobbyistEmployer.LegislativeSessionId));
        });
    }

    [Test]
    public void Constructor_ShouldMapAddresses()
    {
        // Act
        var dto = new LobbyistEmployerResponseDto(_lobbyistEmployer);

        // Assert
        Assert.That(dto.Addresses, Has.Count.EqualTo(_lobbyistEmployer.AddressList?.Addresses.Count ?? 0));

        if (_lobbyistEmployer.AddressList != null)
        {
            for (int i = 0; i < _lobbyistEmployer.AddressList.Addresses.Count; i++)
            {
                var originalAddress = _lobbyistEmployer.AddressList.Addresses[i];
                var dtoAddress = dto.Addresses[i];

                Assert.Multiple(() =>
                {
                    Assert.That(dtoAddress.Street, Is.EqualTo(originalAddress.Street));
                    Assert.That(dtoAddress.Street2, Is.EqualTo(originalAddress.Street2));
                    Assert.That(dtoAddress.City, Is.EqualTo(originalAddress.City));
                    Assert.That(dtoAddress.State, Is.EqualTo(originalAddress.State));
                    Assert.That(dtoAddress.Zip, Is.EqualTo(originalAddress.Zip));
                    Assert.That(dtoAddress.Country, Is.EqualTo(originalAddress.Country));
                    Assert.That(dtoAddress.Type, Is.EqualTo(originalAddress.Type));
                    Assert.That(dtoAddress.Purpose, Is.EqualTo(originalAddress.Purpose));
                });
            }
        }
    }

    [Test]
    public void Constructor_ShouldMapPhoneNumbers()
    {
        // Act
        var dto = new LobbyistEmployerResponseDto(_lobbyistEmployer);

        // Assert
        Assert.That(dto.PhoneNumbers, Has.Count.EqualTo(_lobbyistEmployer.PhoneNumberList?.PhoneNumbers.Count ?? 0));

        if (_lobbyistEmployer.PhoneNumberList != null)
        {
            for (int i = 0; i < _lobbyistEmployer.PhoneNumberList.PhoneNumbers.Count; i++)
            {
                var originalPhone = _lobbyistEmployer.PhoneNumberList.PhoneNumbers[i];
                var dtoPhone = dto.PhoneNumbers[i];

                Assert.Multiple(() =>
                {
                    Assert.That(dtoPhone.Number, Is.EqualTo(originalPhone.Number));
                    Assert.That(dtoPhone.Type, Is.EqualTo(originalPhone.Type));
                });
            }
        }
    }

    [Test]
    public void Constructor_ShouldMapMemberNames()
    {
        // Act
        var dto = new LobbyistEmployerResponseDto(_lobbyistEmployer);

        // Assert
        Assert.That(dto.MemberNames, Has.Count.EqualTo(_lobbyistEmployer.MemberNames.Count));

        for (int i = 0; i < _lobbyistEmployer.MemberNames.Count; i++)
        {
            var originalMember = _lobbyistEmployer.MemberNames[i];
            var dtoMember = dto.MemberNames[i];

            Assert.That(dtoMember.Name, Is.EqualTo(originalMember.Name));
        }
    }

    [Test]
    public void Constructor_WithNullAddressList_ShouldNotThrowException()
    {
        // Arrange
        _lobbyistEmployer.AddressList = null;

        // Act & Assert
        Assert.DoesNotThrow(() => new LobbyistEmployerResponseDto(_lobbyistEmployer));
    }

    [Test]
    public void Constructor_WithNullPhoneNumberList_ShouldNotThrowException()
    {
        // Arrange
        _lobbyistEmployer.PhoneNumberList = null;

        // Act & Assert
        Assert.DoesNotThrow(() => new LobbyistEmployerResponseDto(_lobbyistEmployer));
    }

    [Test]
    public void Constructor_WithEmptyLists_ShouldReturnEmptyCollections()
    {
        // Arrange
        _lobbyistEmployer.AddressList?.Addresses.Clear();
        _lobbyistEmployer.PhoneNumberList?.PhoneNumbers.Clear();
        _lobbyistEmployer.MemberNames.Clear();

        // Act
        var dto = new LobbyistEmployerResponseDto(_lobbyistEmployer);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(dto.Addresses, Is.Empty);
            Assert.That(dto.PhoneNumbers, Is.Empty);
            Assert.That(dto.MemberNames, Is.Empty);
        });
    }
}
