// <copyright file="IUpdateContact.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Globalization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Contacts;

/// <summary>
/// Command interface for updating a contact.
/// </summary>
public interface IUpdateFilerContact : ICommand<UpdateFilerContactCommand, IResult<FilerContact>>;

/// <summary>
/// Command implementation for updating a contact.
/// </summary>
/// <param name="db">The database context.</param>
/// <param name="decisions">Business logic engine.</param>
/// <param name="auditService">High level audit facilities.</param>
/// <param name="logger">The logger instance.</param>
public sealed class UpdateFilerContact(
    DatabaseContext db,
    IDecisionsService decisions,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc,
    ILogger<UpdateFilerContact> logger) : IUpdateFilerContact
{
    /// <inheritdoc />
    public async ValueTask<IResult<FilerContact>> Execute(
        UpdateFilerContactCommand input,
        CancellationToken cancellationToken = default)
    {
        var filerContact = await db.FilerContacts
            .Include(c => c.PhoneNumberList)
                .ThenInclude(p => p!.PhoneNumbers)
            .Include(c => c.AddressList)
                .ThenInclude(a => a!.Addresses)
            .Include(c => c.EmailAddressList)
                .ThenInclude(a => a!.EmailAddresses)
            .AsSplitQuery()
            .FirstOrDefaultAsync(c => c.Id == input.Id, cancellationToken);

        if (filerContact is null)
        {
            logger.LogWarning("Contact with id {Id} not found", input.Id);
            return new Failure<FilerContact>.NotFound("No contact was found with the requested id");
        }

        if (input.Apply(filerContact).Unwrap(out _, out var failure))
        {
            return failure;
        }

        _ = await decisions.Execute(new("Contact.Update", Context: filerContact), cancellationToken);
        _ = await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Update", filerContact.GetType().Name, filerContact.Id.ToString(CultureInfo.InvariantCulture), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<FilerContact>(filerContact);
    }
}
