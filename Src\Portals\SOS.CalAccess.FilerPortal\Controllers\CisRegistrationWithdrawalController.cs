using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using SOS.CalAccess.FilerPortal.ControllerServices.CisRegistrationWithdrawalCtlSvc;
using SOS.CalAccess.FilerPortal.Models.Localization;
using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.UI.Common;
using SOS.CalAccess.UI.Common.Enums;
using SOS.CalAccess.UI.Common.Localization;
using SOS.CalAccess.UI.Common.Models;
using SOS.CalAccess.UI.Common.Services;

namespace SOS.CalAccess.FilerPortal.Controllers;

/// <summary>
/// Withdraw original and amend Candidate Intention Statement
/// </summary>
public class CisRegistrationWithdrawalController(
     ICisRegistrationWithdrawalCtlSvc cisRegistrationWithdrawalCtlSvc,
     IStringLocalizer<SharedResources> localizer,
     IAuthorizationSvc authorizationSvc,
     IToastService toastService
    ) : Controller
{
    /// <summary>
    /// Common logic to call before loading a view.
    /// </summary>
    /// <param name="context"></param>
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        SetCommonViewData();
    }

    /// <summary>
    /// Redirect to Dashboard
    /// </summary>
    /// <returns>Dashboard View</returns>
    public ActionResult RedirectToDashboard()
    {
        return RedirectToAction("Index", "Dashboard");
    }

    /// <summary>
    /// Handles case where invalid ID is passed (unable to find registration to load)
    /// </summary>
    /// <returns></returns>
    private ActionResult HandleInvalidId()
    {
        toastService.Error(CommonResourceConstants.InvalidId);
        return RedirectToDashboard();
    }

    /// <summary>
    /// Handles case where invalid ID is passed (unable to find registration to load)
    /// </summary>
    /// <returns></returns>
    private ActionResult HandleUnauthorizedAccess()
    {
        toastService.Error(CommonResourceConstants.UnauthorizedToAccessPage);
        return RedirectToDashboard();
    }

    #region Entry Actions
    /// <summary>
    /// Redirect to Page01
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns>Dashboard View</returns>
    [HttpGet]
    public async Task<IActionResult> Index([Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidId();
        }

        var isAuthorized = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Withdraw, User, registrationId: id));
        if (!isAuthorized)
        {
            return HandleUnauthorizedAccess();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.InitializeCisWithdrawal(id);
        if (result.IsOk())
        {
            return RedirectToAction(nameof(Page01), new { Id = result.Unwrap() });
        }
        else
        {
            toastService.Error(result.GetError().Message);
            return RedirectToDashboard();
        }
    }

    /// <summary>
    /// Cancel withdrawal
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns>Dashboard View</returns>
    [HttpPost]
    public async Task<IActionResult> Cancel([Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidId();
        }

        var isAuthorized = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Withdraw, User, registrationId: id));
        if (!isAuthorized)
        {
            toastService.Error(CommonResourceConstants.UnauthorizedActionToast);
            return RedirectToAction(nameof(Index), new { id });
        }

        var result = await cisRegistrationWithdrawalCtlSvc.CancelCisWithdrawal(id);
        if (result.IsError())
        {
            toastService.Error(result.GetError().Message);
            return RedirectToDashboard();
        }

        toastService.Success(localizer[CommonResourceConstants.ToastCanceled]);
        return RedirectToDashboard();
    }

    /// <summary>
    /// Handler for when user starts to edit a form.
    /// Redirects user to predetermined starting page.
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns></returns>
    public async Task<IActionResult> Edit([Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidId();
        }

        var isAuthorized = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Withdraw, User, registrationId: id));
        if (!isAuthorized)
        {
            return HandleUnauthorizedAccess();
        }

        return RedirectToAction(nameof(Page01), new { Id = id });
    }

    /// <summary>
    /// Handler for when user wants to attest a withdrawal.
    /// Redirects user to predetermined starting page.
    /// </summary>
    /// <param name="id">registration id</param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> CompleteAttestation([Required] long id)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidId();
        }

        var isAuthorized = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Attest, User, registrationId: id));
        if (!isAuthorized)
        {
            toastService.Error(CommonResourceConstants.UnauthorizedActionToast);
            return RedirectToDashboard();
        }

        return RedirectToAction(nameof(Page02), new { Id = id });
    }
    #endregion

    #region Page01
    /// <summary>
    /// Loads view for Page01
    /// </summary>
    /// <returns>Page01 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page01(
        [Required] long id,
        CancellationToken cancellation = default)
    {
        if (!ModelState.IsValid)
        {
            return HandleInvalidId();
        }

        var isAuthorized = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Withdraw, User, registrationId: id));
        if (!isAuthorized)
        {
            return HandleUnauthorizedAccess();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page01GetViewModel(id);
        if (result.IsError())
        {
            toastService.Error(result.GetError().Message);
            return RedirectToDashboard();
        }

        return View(result.Unwrap());
    }

    [HttpPost]
    public async Task<IActionResult> Page01(CisRegistrationWithdrawalPage01ViewModel model)
    {
        var isAuthorized = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Withdraw, User, registrationId: model.Id));
        if (!isAuthorized)
        {
            toastService.Error(CommonResourceConstants.UnauthorizedToAccessPage);
            return RedirectToAction(nameof(Page01), new { model.Id });
        }

        if (!ModelState.IsValid)
        {
            return RedirectToAction(nameof(Page01), new { model.Id });
        }

        switch (model.Action)
        {
            case FormAction.Continue:
                return RedirectToAction(nameof(Page02), new { model.Id });
            case FormAction.SaveAndClose:
                return RedirectToDashboard();
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }
    #endregion

    #region Page02
    /// <summary>
    /// Loads view for Page02
    /// </summary>
    /// <returns>Page02 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page02(
        [Required] long id,
        CancellationToken cancellation = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page02GetViewModel(id);
        if (result.IsOk())
        {
            var canAttest = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Attest, User, registrationId: id));
            var model = result.Unwrap();
            model.IsAttest = canAttest;
            return View(model);
        }

        return BadRequest();
    }

    [HttpPost]
    public async Task<IActionResult> Page02(CisRegistrationWithdrawalPage02ViewModel model)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Previous:
                return RedirectToAction(nameof(Page01), new { model.Id });
            case FormAction.Continue:
                return await Page02Continue(model);
            case FormAction.SaveAndClose:
                return Page02SaveAndClose(model);
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }

    private async Task<IActionResult> Page02Continue(CisRegistrationWithdrawalPage02ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page02Submit(model);
        if (result.IsOk())
        {
            return RedirectToAction(nameof(Page03), new { model.Id });
        }

        return View(model);
    }

    private IActionResult Page02SaveAndClose(CisRegistrationWithdrawalPage02ViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        return RedirectToDashboard();
    }
    #endregion

    #region Page03
    /// <summary>
    /// Loads view for Page03
    /// </summary>
    /// <returns>Page03 View</returns>
    [HttpGet]
    public async Task<IActionResult> Page03(
        [Required] long id,
        CancellationToken cancellation = default)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: id));

        if (!ModelState.IsValid)
        {
            return NotFound();
        }

        var result = await cisRegistrationWithdrawalCtlSvc.Page03GetViewModel(id);

        if (result.IsOk())
        {
            var canAttest = await authorizationSvc.IsAuthorized(new(Permission.Registration_Candidate_Attest, User, registrationId: id));
            var model = result.Unwrap();
            model.IsAttest = canAttest;
            return View(model);
        }

        return View();

    }

    [HttpPost]
    public async Task<IActionResult> Page03(CisRegistrationWithdrawalPage03ViewModel model)
    {
        await authorizationSvc.VerifyAuthorization(new AuthorizationRequest(Permission.Registration_Candidate_Edit, User, registrationId: model.Id));

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        switch (model.Action)
        {
            case FormAction.Close:
                return RedirectToDashboard();
            default:
                ModelState.AddModelError(string.Empty, localizer[CommonResourceConstants.InvalidSubmission]);
                return View(model);
        }
    }
    #endregion

    #region Private
    /// <summary>
    /// Sets UI data that is common to all pages in this form.
    /// </summary>
    private void SetCommonViewData()
    {
        ViewData[LayoutConstants.Title] = localizer[ResourceConstants.WithdrawalCisTitle].Value;
        ViewData[LayoutConstants.Breadcrumbs] = new List<Breadcrumb>()
        {
            new(localizer[ResourceConstants.FilerPortalTitle].Value, "/FilerPortal"),
            new(localizer[ResourceConstants.WithdrawalCisBreadcrumb], "/Candidate"),
        };
        ViewData["ProgressItem1Name"] = "1. " + localizer[ResourceConstants.WithdrawalCisStep01].Value;
        ViewData["ProgressItem2Name"] = "2. " + localizer[ResourceConstants.WithdrawalCisStep02].Value;
    }
    #endregion

}
