// <copyright file="SeedData.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using SOS.CalAccess.Data.EntityFramework.Repositories.SeedData;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Data.EntityFramework.Tests.SeedData;

/// <summary>
/// Provides methods to seed data for tests.
/// </summary>
public static class SeedFilingsData
{
    /// <summary>
    /// Prepares the data for testing.
    /// </summary>
    /// <param name="context">The database context to use for seeding data.</param>
    /// <returns>An <see cref="AssertionReferences"/> object containing references to the seeded data.</returns>
    public static async Task<AssertionReferences> PrepareFilingData(this DatabaseContext context)
    {
        var registration = GetRegistration();

        context.CandidateIntentionStatements.Add(registration);

        await context.SaveChangesAsync();

        var filer = new Filer
        {
            CurrentRegistrationId = registration.Id,
            FilerStatusId = FilerStatus.Active.Id,
        };

        registration.Filer = filer;

        await context.SaveChangesAsync();

        var draftFiling = new Filing
        {
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            FilerId = filer.Id,
            StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(-10),
            StatusId = FilingStatus.Draft.Id,
            EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(1),
        };

        var draftAutoApprovableFiling = new Filing
        {
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            FilerId = filer.Id,
            StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(-1),
            StatusId = FilingStatus.Draft.Id,
            EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(1),
        };

        var contact = new IndividualContact()
        {
            FilerId = filer.Id,
            FirstName = "Wile",
            MiddleName = string.Empty,
            LastName = "Coyote",
            Employer = "ACME, Inc.",
            Occupation = "Genius",
            AddressList = new(),
            PhoneNumberList = new(),
        };

        var expenditures = Enumerable.Range(0, 3).Select(_ => new Expenditure
        {
            Amount = (Currency)100,
            Purpose = "Test",
            TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddHours(5),
            FilerId = filer.Id,
            Contact = contact,
        }).ToList();

        var pendingFiling = new Filing
        {
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            FilerId = filer.Id,
            StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            StatusId = FilingStatus.Pending.Id,
            EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(1),
        };

        var originalSubmission = new Filing
        {
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            FilerId = filer.Id,
            StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            StatusId = FilingStatus.Accepted.Id,
            EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(1),
            ApprovedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddHours(1),
        };

        context.Expenditures.AddRange(expenditures);
        context.Filings.AddRange(draftFiling, draftAutoApprovableFiling, pendingFiling, originalSubmission);

        await context.SaveChangesAsync();

        var approvedFiling = new Filing
        {
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            FilerId = filer.Id,
            Version = 1,
            ParentId = originalSubmission.Id,
            StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            StatusId = FilingStatus.Accepted.Id,
            EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddDays(1),
        };

        context.Filings.Add(approvedFiling);
        await context.SaveChangesAsync();

        return new AssertionReferences(
            filer,
            draftFiling,
            pendingFiling,
            approvedFiling,
            draftAutoApprovableFiling,
            originalSubmission,
            expenditures);
    }

    private static CandidateIntentionStatement GetRegistration() => new()
    {
        StatusId = RegistrationStatus.Accepted.Id,
        Name = "Test Candidate",
        ApprovedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        AddressList = SeedValues.GetTestAddressList(),
        PhoneNumberList = SeedValues.GetTestPhoneNumberList(),
    };

    /// <summary>
    /// Represents references to the seeded data for assertions.
    /// </summary>
    public sealed record AssertionReferences(
        Filer Filer,
        Filing DraftFiling,
        Filing PendingFiling,
        Filing ApprovedFiling,
        Filing AutoApprovableFiling,
        Filing OriginalSubmissionForApproved,
        IReadOnlyList<Transaction> Transactions);
}
