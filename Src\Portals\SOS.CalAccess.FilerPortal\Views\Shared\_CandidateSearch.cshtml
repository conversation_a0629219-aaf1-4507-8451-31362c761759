@using SOS.CalAccess.FilerPortal.Models.Registrations

@model CandidateSearchPartialViewModel;
@{
    var candidateName = Model.CandidateName ?? "";
    var label = !string.IsNullOrWhiteSpace(Model.Label) ? Model.Label : "Look up name of candidate";
    var placeholder = !string.IsNullOrWhiteSpace(Model.Placeholder) ? Model.Placeholder : "Enter candidate name";
}

<div class="form-group">
    @Html.InputLabel(SharedLocalizer, "CandidateSearch", label, Model.Required)

    <input type="hidden" id="CandidateId" name="CandidateId" value="@Model.CandidateId" />

    <div id="search1" class="candidate-search">
        <div class="search-container">
            @Html.InputField(SharedLocalizer, "CandidateSearch", "Candidate Search", Model.Required, candidateName, placeholder)
        </div>
    </div>
    @Html.SosValidationMessageFor(SharedLocalizer, m => m.CandidateId)
</div>


<script type="application/javascript">
    const SEARCH_URL = '@Url.Action("SearchCandidateByName", $"{Model.ControllerName}")';
    const INPUT_DEBOUNCE_MS = 200;
    const TEXT_CANDIDATE_INFORMATION = 'Candidate Information'
    const TEXT_MOST_RECENT_REGISTRATIONS = 'Most recent registrations'
    const TEXT_NAME = 'Name';
    const TEXT_SELECT_CANDIDATE = 'Select Candidate';
    const TEXT_NO_RESULTS_FOUND = 'No results found.';

    // Use class to isloate variables from global scope
    class CandidateSearch extends EventTarget {
      activeResultItemElems = [];

      constructor(querySelector) {
        super();
        this.root = document.querySelector(querySelector);
        if (!this.root) {
          throw new Error('Unable to initialize search component.');
        }

        this.resultContainer = document.createElement('div');
        this.resultContainer.classList.add('result-container');
        this.root.appendChild(this.resultContainer);

        const resultAbsolute = document.createElement('div');
        resultAbsolute.classList.add('result-absolute');
        this.resultContainer.replaceChildren(resultAbsolute);

        this.results = document.createElement('div');
        this.results.classList.add('result-left');

        this.resultDetail = document.createElement('div');
        this.resultDetail.classList.add('result-right');

        resultAbsolute.replaceChildren(this.results, this.resultDetail);

        this.searchContainer = this.root.querySelector('.search-container');

        // Create clear icon
        this.clearIcon = document.createElement('i');
        this.clearIcon.classList.add('clear-icon', 'e-icons', 'e-close', 'active');
        this.clearIcon.style.display = "none"; // Initially hidden

        // Create search icon
        this.searchIcon = document.createElement('i');
        this.searchIcon.classList.add('search-icon', 'e-icons', 'bi', 'bi-search', 'active');

        // Append icon
        this.searchContainer.appendChild(this.clearIcon);
        this.searchContainer.appendChild(this.searchIcon);

        this.selection = this.root.querySelector('.candidate-search input');

        this.clearIcon.onclick = () => {
          this.clearValue();
        }

        this.selection.onfocus = () => {
          if (this.selection.value) {
            this.resultContainer.classList.add('active');
          }
        };

        document.addEventListener('mousedown', (e) => {
          const outsideSearch = !e.composedPath().includes(this.selection)
          const outsideResult = !e.composedPath().includes(this.resultContainer)
          if (outsideSearch && outsideResult) {
            this.hideDropdown();
          }
        });

        const debounce = function (func, delay) {
          let timeoutId;
          return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
              func.apply(this, args);
            }, delay);
          }
        }

        this.selection.oninput = debounce((event) => {
          const value = event.target.value;

          // Toggle icons
          this.toggleIcons(value);

          const params = {
              search: value,
          }
          this.ajaxGet(SEARCH_URL, params, (err, data) => {
            this.clearResults();
            this.setResults(this.results, data);
            this.showDropdown();
          })
        }, INPUT_DEBOUNCE_MS);

        // Toggle icons
        this.toggleIcons(this.selection.value);

        this.clearResults();
      }

      on(eventName, callback, options) {
          this.addEventListener(eventName, callback, options);
      }

      ajaxGet(url, params, callback) {
        const xhr = new XMLHttpRequest();
        const urlParams = new URLSearchParams(params);
        const fullUrl = `${url}?${urlParams.toString()}`;
        xhr.open('GET', fullUrl.toString());
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            callback(null, JSON.parse(xhr.responseText));
          }
          else {
            console.error('Request failed', xhr.status, xhr.responseText);
            callback(xhr.responseText);
          }
        }
        xhr.onerror = (e) => {
          callback(e);
        }
        xhr.send();
      }

      ajaxGetMock (url, params, callback) {
        callback(null, MOCK_DATA);
      }

      showDropdown() {
        this.resultContainer.classList.add('active');
      }
      hideDropdown() {
        this.resultContainer.classList.remove('active');
      }

        toggleIcons = (value) => {
        // If there is value, show clear icon
        // If there is no value, show search icon
            if (value) {
                // Show clear icon, hide search icon
                this.clearIcon.style.display = "block";
                this.searchIcon.style.display = "none";
            } else {
                // Hide clear icon, Show search icon
                this.clearIcon.style.display = "none";
                this.searchIcon.style.display = "block";
            }
        }

      setValue(value) {
        this.selection.value = value;
      }

      clearValue() {
        this.selection.value = '';
        this.selection.focus();
        this.toggleIcons();
        this.dispatchEvent(new CustomEvent('clearSelected'));
      }

      clearResults = () => {
        this.results.replaceChildren();
        this.clearResultDetail();
      };
      clearActiveResultItemElems = () => {
        this.activeResultItemElems = [];
      }
      clearSelectedResult = () => {
        this.activeResultItemElems.forEach(resultItem => {
          resultItem.classList.remove('selected');
        })
      };
      resultOnClick = (element, record) => {
        return () => {
          // Remove 'selected' from all other results.
          this.clearSelectedResult();
          element.classList.add('selected');
          this.setResultDetail(record);
        }
      };
      clearResultDetail = () => {
        this.resultDetail.replaceChildren();
      }
      setResultDetail = (record) => {
        // title node
        const detailTitle = document.createElement('div');
        detailTitle.classList.add('result-detail-title');
        detailTitle.innerText = TEXT_CANDIDATE_INFORMATION;

        // content node
        const detailContent = document.createElement('div');
        detailContent.classList.add('result-detail-content');

        const contentGroupName = document.createElement('div');
        contentGroupName.classList.add('result-detail-content-group');

        const nameLabel = document.createElement('strong');
        nameLabel.innerText = TEXT_NAME;
        const nameContent = document.createElement('span');
        nameContent.innerText = record.Name;
        contentGroupName.replaceChildren(nameLabel, nameContent);

        const contentGroupRecent = document.createElement('div');
        contentGroupRecent.classList.add('result-detail-content-group');
        const recentLabel = document.createElement('strong');
        recentLabel.innerText = TEXT_MOST_RECENT_REGISTRATIONS;

        const recentRegElems = record.Registrations.map(reg => {
          const regElem = document.createElement('span');
          regElem.innerText = reg.Office + ' - ' + reg.Election;
          return regElem;
        });
        contentGroupRecent.replaceChildren(recentLabel, ...recentRegElems);

        detailContent.replaceChildren(contentGroupName, contentGroupRecent);

        // action node
        const detailActions = document.createElement('div');
        detailActions.classList.add('result-detail-actions');
        const selectCandidateButton = document.createElement('button');
        selectCandidateButton.classList.add('button', 'btn-primary');
        selectCandidateButton.setAttribute('type', 'button')
        selectCandidateButton.innerText = TEXT_SELECT_CANDIDATE;
        selectCandidateButton.onclick = () => {
          this.dispatchEvent(new CustomEvent('selected', { detail: record }));
          this.setValue(record.Name);
          this.hideDropdown();
        }
        detailActions.replaceChildren(selectCandidateButton);

        this.resultDetail.replaceChildren(detailTitle, detailContent, detailActions);
      }
      createResultItem = (record) => {
        const resultItem = document.createElement('div');
        resultItem.classList.add('result-item');
        resultItem.onclick = this.resultOnClick(resultItem, record);

        const resultDesc = document.createElement('div');
        resultDesc.classList.add('result-description');

        const resultName = document.createElement('div');
        resultName.classList.add('result-name');
        resultName.innerText = record.Name;

        const resultCaption = document.createElement('div');
        resultCaption.classList.add('result-caption');
        resultCaption.innerText = record.LastElection;
        resultDesc.replaceChildren(resultName, resultCaption);

        const resultAction = document.createElement('div');
        resultAction.classList.add('result-action');

        const arrow = document.createElement('span');
        arrow.classList.add('e-icons');
        arrow.classList.add('e-chevron-right');
        resultAction.replaceChildren(arrow);

        resultItem.replaceChildren(resultDesc, resultAction);
        return resultItem;
      };
      noResultsElem = () => {
        const resultItem = document.createElement('div');
        resultItem.classList.add('result-item');
        resultItem.classList.add('disabled');

        const resultDesc = document.createElement('div');
        resultDesc.classList.add('result-description');

        const resultName = document.createElement('div');
        resultName.classList.add('italicized');
        resultName.innerText = TEXT_NO_RESULTS_FOUND;
        resultDesc.replaceChildren(resultName);

        resultItem.replaceChildren(resultDesc);

        return resultItem;
      }
      setResults = (resultsElem, resultData) => {
        this.clearActiveResultItemElems();
        const resultElements = [];
        resultData.forEach((result) => {
          const resultItem = this.createResultItem(result);
          resultElements.push(resultItem);
          this.activeResultItemElems.push(resultItem);
        });

        if (resultData.length === 0) {
          resultElements.push(this.noResultsElem());
        }

        resultsElem.replaceChildren(...resultElements);
      };
    }

    const search = new CandidateSearch('#search1');
    search.on('selected', (e) => {
      if (e.detail) {
        const candidateId = document.getElementById('CandidateId');
        candidateId.value = e.detail.Id;
      }
    })

    // Remove Candidate
    search.on('clearSelected', () => {
        const candidateId = document.getElementById('CandidateId');
        candidateId.value = null;
    })
</script>
