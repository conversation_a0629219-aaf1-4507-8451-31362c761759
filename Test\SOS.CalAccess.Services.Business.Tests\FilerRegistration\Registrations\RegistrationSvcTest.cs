
using NSubstitute;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.FilerRegistration.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.Constants;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;

namespace SOS.CalAccess.Services.Business.Contracts.Tests.FilerRegistration.Registrations;

/// <summary>
/// Unit tests for the <see cref="RegistrationSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(RegistrationSvc))]
public sealed class RegistrationSvcTest
{
    private IRegistrationRepository _registrationRepository;
    private ICandidateRepository _candidateRepository;
    private IMultipurposeOrganizationRepository _multipurposeOrganizationRepository;
    private IRegistrationStatusRepository _registrationStatusRepository;
    private IAttestationRepository _attestationRepository;
    private IDashboardQuerySvc _dashboardQuerySvc;
    private RegistrationSvc _registrationSvc;
    private IDateTimeSvc _dateTimeSvc;

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _registrationRepository = Substitute.For<IRegistrationRepository>();
        _candidateRepository = Substitute.For<ICandidateRepository>();
        _multipurposeOrganizationRepository = Substitute.For<IMultipurposeOrganizationRepository>();
        _registrationStatusRepository = Substitute.For<IRegistrationStatusRepository>();
        _dashboardQuerySvc = Substitute.For<IDashboardQuerySvc>();
        _dateTimeSvc = Substitute.For<IDateTimeSvc>();

        _registrationSvc = new RegistrationSvc(
            _registrationRepository,
            _candidateRepository,
            _multipurposeOrganizationRepository,
            _registrationStatusRepository,
            _attestationRepository,
            _dashboardQuerySvc,
            _dateTimeSvc
        );
    }

    /// <summary>
    /// Test to ensure AddRegistrationAddress adds a valid address.
    /// </summary>
    [Test]
    public async Task AddRegistrationAddress_ShouldCallAddAddress_OnSuccess()
    {
        // Arrange
        var address = new Address
        {
            Street = "123 Main St",
            City = "Somewhere",
            State = "CA",
            Country = "USA",
            Zip = new ZipCode(),
            Type = "Home",
            AddressListId = 1,
            CreatedBy = 1,
            ModifiedBy = 1,
            Purpose = "Candidate"
        };
        var registrationId = 123L;
        _registrationRepository.AddAddress(Arg.Any<Address>(), Arg.Any<long>()).Returns(Task.CompletedTask);

        // Act
        await _registrationSvc.AddRegistrationAddress(address, registrationId);

        // Assert
        await _registrationRepository.Received(1).AddAddress(Arg.Is<Address>(a => a.Street == address.Street), registrationId);
    }

    /// <summary>
    /// Test to ensure AddRegistrationPhoneNumber adds a valid phone number.
    /// </summary>
    [Test]
    public async Task AddRegistrationPhoneNumber_ShouldCallAddPhoneNumber_OnSuccess()
    {
        // Arrange
        var phoneNumber = new PhoneNumber
        {
            Number = "+14445551234",
            Type = "Mobile",
            CreatedBy = 1,
            ModifiedBy = 1
        };
        var registrationId = 123L;
        _registrationRepository.AddPhoneNumber(Arg.Any<PhoneNumber>(), Arg.Any<long>()).Returns(Task.CompletedTask);

        // Act
        await _registrationSvc.AddRegistrationPhoneNumber(phoneNumber, registrationId);

        // Assert
        await _registrationRepository.Received(1).AddPhoneNumber(Arg.Is<PhoneNumber>(p => p.Number == phoneNumber.Number), registrationId);
    }

    /// <summary>
    /// Test to ensure GetAllMultipurposeOrganizations returns list of organization.
    /// </summary>
    [Test]
    public async Task GetAllMultipurposeOrganizations_ShouldReturnMultipurposeOrganizations_OnSuccess()
    {
        // Arrange
        var organizations = new List<MultipurposeOrganization>
            {
                new() { Id = 1, Name = "Org 1", Type = "Non-Profit" },
                new() { Id = 2, Name = "Org 2", Type = "Political" }
            };
        _multipurposeOrganizationRepository.GetAll().Returns(Task.FromResult<IEnumerable<MultipurposeOrganization>>(organizations));

        // Act
        var result = await _registrationSvc.GetAllMultipurposeOrganizations();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.FirstOrDefault().Name, Is.EqualTo("Org 1"));
        });
        await _multipurposeOrganizationRepository.Received(1).GetAll();
    }

    /// <summary>
    /// Test to ensure RemoveRegistrationAddress removes valid address.
    /// </summary>
    [Test]
    public async Task RemoveRegistrationAddress_ShouldCallRemoveAddress_OnSuccess()
    {
        // Arrange
        var addressId = 1L;
        var registrationId = 123L;
        _registrationRepository.RemoveAddress(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.CompletedTask);

        // Act
        await _registrationSvc.RemoveRegistrationAddress(addressId, registrationId);

        // Assert
        await _registrationRepository.Received(1).RemoveAddress(addressId, registrationId);
    }

    /// <summary>
    /// Test to ensure RemoveRegistrationPhoneNumber removes valid phone number.
    /// </summary>
    [Test]
    public async Task RemoveRegistrationPhoneNumber_ShouldCallRemovePhoneNumber_OnSuccess()
    {
        // Arrange
        var phoneNumberId = 1L;
        var registrationId = 123L;
        _registrationRepository.RemovePhoneNumber(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.CompletedTask);

        // Act
        await _registrationSvc.RemoveRegistrationPhoneNumber(phoneNumberId, registrationId);

        // Assert
        await _registrationRepository.Received(1).RemovePhoneNumber(phoneNumberId, registrationId);
    }

    [Test]
    public async Task GetVoluntaryExpenditureCeiling_ShouldReturnData()
    {
        // Arrange
        var id = 1;
        var officeId = 1;
        var electionYear = 2025;
        var fakeData = new ExpenditureExpenseAmount { OfficeId = officeId, PrimarySpecialAmount = (Models.FilerDisclosure.Transactions.Currency)1000.000M };

        _registrationRepository.FindExpenditureExpenseAmount(Arg.Any<long>()).Returns(fakeData);

        // Act
        await _registrationSvc.GetExpenditureExpenseAmountData(id);

        // Assert
        await _registrationRepository.Received(1).FindExpenditureExpenseAmount(id);
    }

    [Test]
    public async Task GetMyRegistrations_Found_ShouldReturnResult()
    {
        // Arrange
        var userId = 1;
        var registrationResponse = new List<RegistrationDashboardDto>
        {
            new()
            {
                Id = 1,
                FilerId = userId,
                Name = "Test",
                Type = RegistrationConstants.RegistrationType.CandidateIntentionStatement,
                OriginalId = 1,
                IsNewCertification = true
            },
            new()
            {
                Id = 2,
                FilerId = userId,
                Name = "Test SMO",
                Type = RegistrationConstants.RegistrationType.SlateMailerOrganization,
                OriginalId = 6,
                IsNewCertification = true
            },
            new()
            {
                Id = 3,
                FilerId = userId,
                Name = "Lobbyist",
                Type = RegistrationConstants.RegistrationType.Lobbyist,
                OriginalId = 7,
                IsNewCertification = false
            }
        };
        _dashboardQuerySvc.GetDashboardRegistrationsForUserAsync(Arg.Any<long>()).Returns(registrationResponse);

        // Act
        var result = await _registrationSvc.GetMyRegistrations(userId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<List<RegistrationDashboardDto>>());
        });
    }
}
