using System.Globalization;
using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerRegistration.Registrations;
using SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Elections;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerRegistration.Elections;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Tests.Common;


namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Registrations;

[TestFixture]
[TestOf(typeof(RegistrationRepository))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class RegistrationRepositoryTests
{
    private IRegistrationRepository _registrationRepository;
    private Address _address;

    [SetUp]
    public async Task SetUp()
    {
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext(x => x.EnableDetailedErrors().EnableDetailedErrors());
        _address = new Address()
        {
            City = "",
            Country = "",
            Purpose = "",
            State = "",
            Street = "",
            Type = "",
            Zip = ""
        };

        _registrationRepository = new RegistrationRepository(context);
    }

    /// <summary>
    /// Verifies that a<see cref = "Registration" /> entity can be created, added to the database,
    /// and successfully saved using the repository's <c>Create</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldCreateRegistrationAndSaveChanges()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext(x => x.EnableDetailedErrors().EnableDetailedErrors());

        _registrationRepository = new RegistrationRepository(context);

        var data = (CandidateIntentionStatement)GetData();
        data.OriginalId = null;

        var result = await _registrationRepository.Create(data);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(data.Id, Is.EqualTo(result.Id));
            Assert.That(data.Id, Is.EqualTo(result.OriginalId));
        });

    }
    /// <summary>
    /// Verifies that a<see cref = "Registration" /> entity can be deleted from the database,
    /// and successfully saved using the repository's <c>Delete</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldDeleteRegistrationAndSaveChanges()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Instantiate the repository
        _registrationRepository = new RegistrationRepository(context);

        // Arrange: Seed initial data into the context
        var data = GetData();

        // Add the entity to the database
        var addedEntity = await _registrationRepository.Create(data);

        // Ensure the entity is added
        Assert.That(addedEntity, Is.Not.Null);
        Assert.That(addedEntity.Id, Is.EqualTo(data.Id));

        // Act: Delete the entity
        var deleteResult = await _registrationRepository.Delete(addedEntity);

        // Assert: Validate the entity is deleted
        Assert.That(deleteResult, "Delete method should return true when successful.");

        var exists = await context.Set<Registration>().AnyAsync(e => e.Id == addedEntity.Id);
        Assert.That(exists, Is.False, "The entity should no longer exist in the database.");
    }

    /// <summary>
    /// Verifies that a specific <see cref="Registration"/> entity can be retrieved from the database
    /// using its unique identifier with the repository's <c>FindById</c> method.database,
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldFindRegistrationtById()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = GetData();

        // Add the entity to the database
        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindById(addedEntity.Id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(addedEntity.Id));
    }

    /// <summary>
    /// Verifies that all <see cref="Registration"/> entities can be retrieved from the database
    /// using the repository's <c>GetAll</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetAllRegistrations()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);

        var data1 = GetData();
        var data2 = GetData("Candidate Two");

        await _registrationRepository.Create(data1);
        await _registrationRepository.Create(data2);

        // Act
        var result = await _registrationRepository.GetAll();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(2), "GetAll should return all entities in the database.");
    }

    /// <summary>
    /// Verifies that a<see cref = "Registration" /> entity can be updated to the database,
    /// and successfully saved using the repository's <c>Update</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>

    [Test]
    public async Task ShouldUpdateRegistrationAndSaveChanges()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = GetData();

        // Add the entity to the database
        var addedEntity = await _registrationRepository.Create(data);

        // Modify a property
        addedEntity.Name = "Updated";
        // Act
        var updatedEntity = await _registrationRepository.Update(addedEntity);

        // Assert
        Assert.That(updatedEntity, Is.Not.Null);
        Assert.That(updatedEntity.Name, Is.EqualTo("Updated"), "The entity should be updated with the new data.");
    }

    [Test]
    public async Task ShouldLinkElectionToRegistration()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        //Arrange
        await context.PrepareElections();
        _registrationRepository = new RegistrationRepository(context);
        CandidateIntentionStatement? newReg = (CandidateIntentionStatement)GetData();
        var electionIdToAdd = 1; //from PrepareElections

        var addedReg = await _registrationRepository.Create(newReg);

        //Act
        var result = await _registrationRepository.LinkElectionToCandidateIntentRegistration(newReg.Id, "state", electionIdToAdd, 1, 1, 1, 1);

        //Assert
        newReg = await _registrationRepository.FindById(newReg.Id) as CandidateIntentionStatement;

        Assert.Multiple(() =>
        {
            Assert.That(newReg?.ElectionRaceId, Is.EqualTo(electionIdToAdd));
            Assert.That(newReg?.ElectionRace, Is.Not.Null);
        });
    }
    [Test]
    public async Task FindCandidateIntentionStatementById_ShouldReturnCorrectResult()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);

        var data = (CandidateIntentionStatement)GetData();
        // Add test data to the in-memory database
        context.Set<CandidateIntentionStatement>().Add(data);
        await context.SaveChangesAsync();

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        var result = await repository.FindCandidateIntentionStatementById(data.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(data.Id));
            Assert.That(result?.Name, Is.EqualTo(data.Name));
            Assert.That(result?.Email, Is.EqualTo(data.Email));
        });
    }
    [Test]
    public async Task FindCandidateIntentionStatementById_ShouldReturnNull_WhenExceptionOccurs()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        var result = await repository.FindCandidateIntentionStatementById(-1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Null);
        });
    }
    [Test]
    public async Task FindCandidateIntentionStatementWithElectionById_ShouldReturnCorrectResult()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);

        var data = (CandidateIntentionStatement)GetData();
        // Add test data to the in-memory database
        context.Set<CandidateIntentionStatement>().Add(data);
        await context.SaveChangesAsync();

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        var result = await repository.FindCandidateIntentionStatementWithElectionById(data.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(data.Id));
            Assert.That(result?.Name, Is.EqualTo(data.Name));
            Assert.That(result?.Email, Is.EqualTo(data.Email));
        });
    }
    [Test]
    public async Task FindCandidateIntentionStatementWithElectionById_ShouldReturnNull_WhenExceptionOccurs()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        var result = await repository.FindCandidateIntentionStatementWithElectionById(-1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Null);
        });
    }
    [Test]
    public async Task FindElectionByRegistrationId_ShouldReturnCorrectResult()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);
        await context.PrepareElections();

        var data = (CandidateIntentionStatement)GetData();
        // Add test data to the in-memory database
        context.Set<CandidateIntentionStatement>().Add(data);
        await context.SaveChangesAsync();

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        await _registrationRepository.LinkElectionToCandidateIntentRegistration(data.Id, "state", 1, 1, 1, 1, 1);
        var result = await repository.FindElectionByRegistrationId(data.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(1));
            Assert.That(result?.Name, Is.EqualTo("Test Election"));
        });
    }
    [Test]
    public async Task FindElectionByRegistrationId_ShouldReturnNull_WhenExceptionOccurs()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        var result = await repository.FindElectionByRegistrationId(-1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Null);
        });
    }
    /// <summary>
    /// Verifies that <see cref="Registration"/> entities can be retrieved from the database
    /// using the repository's <c>GetAll</c> method with a search query.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetRegistrationsBySearchQuery()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);

        var data1 = GetData("John Smith");
        var data2 = GetData("Jane Doe");
        var data3 = GetData("John Doe");

        await _registrationRepository.Create(data1);
        await _registrationRepository.Create(data2);
        await _registrationRepository.Create(data3);

        // Act
        var result = await _registrationRepository.GetAll("John");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2), "Should return only registrations containing 'John' in the name");
            Assert.That(result.All(r => r.Name.Contains("John", StringComparison.OrdinalIgnoreCase)), Is.True);
        });

    }

    /// <summary>
    /// Verifies that <see cref="Registration"/> entities search is case insensitive
    /// when using the repository's <c>GetAll</c> method with a search query.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetRegistrationsBySearchQueryCaseInsensitive()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);

        var data1 = GetData("JOHN SMITH");
        var data2 = GetData("john doe");

        await _registrationRepository.Create(data1);
        await _registrationRepository.Create(data2);

        // Act
        var result = await _registrationRepository.GetAll("john");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2), "Should return registrations containing 'john' regardless of case");
            Assert.That(result.All(r => r.Name.Contains("john", StringComparison.OrdinalIgnoreCase)), Is.True);
        });

    }

    /// <summary>
    /// Verifies that a <see cref="LobbyistEmployer"/> entity can be retrieved from the database
    /// using its unique identifier with the repository's <c>FindLobbyistEmployerById</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldFindLobbyistEmployerById()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = new LobbyistEmployer
        {
            Name = "Test Lobbyist Employer",
            StatusId = RegistrationStatus.Draft.Id,
            AddressList = new AddressList()
            {
                Addresses = new List<Address>()
                {
                    new() {
                        Type ="Residential",
                        City ="City",
                        Purpose = "Candidate",
                        Country="USA",
                        State ="PA",
                        Street="Street",
                        Zip="19355"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList()
            {
                PhoneNumbers = [new PhoneNumber { Number = "**********", Type = "Home", }]
            },
            Email = "<EMAIL>",
            EmployerName = "Test Employer",
            EmployerType = "Type",
            BusinessActivity = "Activity",
            BusinessDescription = "Description",
            InterestType = "Interest",
            NumberOfMembers = 10
        };

        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindLobbyistEmployerById(addedEntity.Id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(addedEntity.Id));
            Assert.That(result.Name, Is.EqualTo("Test Lobbyist Employer"));
            Assert.That(result.AddressList, Is.Not.Null);
            Assert.That(result.PhoneNumberList, Is.Not.Null);
        });
    }

    /// <summary>
    /// Verifies that a <see cref="LobbyingFirm"/> entity can be retrieved from the database
    /// using its unique identifier with the repository's <c>FindLobbyingFirmById</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldFindLobbyingFirmById()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = new LobbyingFirm
        {
            Name = "Test Lobbying Firm",
            StatusId = RegistrationStatus.Draft.Id,
            AddressList = new AddressList()
            {
                Addresses = new List<Address>()
               {
                   new() {
                       Type = "Business",
                       City = "Sacramento",
                       Purpose = "Primary",
                       Country = "USA",
                       State = "CA",
                       Street = "Capitol Avenue",
                       Zip = "95814"
                   }
               }
            },
            PhoneNumberList = new PhoneNumberList()
            {
                PhoneNumbers = [new PhoneNumber { Number = "9165551234", Type = "Business", }]
            },
            Email = "<EMAIL>",
            LegislativeSessionId = 1,
            ResponsibleOfficerTitle = "CEO"
        };

        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindLobbyingFirmById(addedEntity.Id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(addedEntity.Id));
            Assert.That(result.Name, Is.EqualTo("Test Lobbying Firm"));
            Assert.That(result.AddressList, Is.Not.Null);
            Assert.That(result.AddressList?.Addresses, Is.Not.Null);
            Assert.That(result.AddressList?.Addresses.Count, Is.EqualTo(1));
        });
    }

    /// <summary>
    /// Verifies that the <c>FindLobbyingFirmById</c> method returns null when the entity does not exist.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task FindLobbyingFirmById_ShouldReturnNull_WhenIdDoesNotExist()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        long nonExistentId = 9999;

        // Act
        var result = await _registrationRepository.FindLobbyingFirmById(nonExistentId);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task IsUniqueSmoName_ShouldReturnTrue_WhenNameUnique()
    {
        // Arrange
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);

        // Act
        var result = await _registrationRepository.IsUniqueSmoName("Unique Organization");

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public async Task IsUniqueSmoName_ShouldReturnFalse_WhenFindWithParentId()
    {
        // Arrange
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);

        var filer = new Filer
        {
            FilerStatusId = 1,
            FilerTypeId = 3
        };

        var originalRegistration = new SlateMailerOrganization()
        {
            Id = 5,
            Name = "Test Organization",
            StatusId = RegistrationStatus.Submitted.Id,
            Filer = filer,
            Email = "<EMAIL>"
        };

        var amendRegistration = new SlateMailerOrganization()
        {
            Name = "Test Organization",
            StatusId = RegistrationStatus.Accepted.Id,
            Filer = filer,
            Email = "<EMAIL>",
            OriginalId = 1,
            ParentId = 10,
            Parent = new SlateMailerOrganization
            {
                Id = 10,
                Name = "Test 10 Organization",
                StatusId = RegistrationStatus.Submitted.Id,
                Filer = filer,
                Email = "<EMAIL>"
            }
        };

        _ = await _registrationRepository.Create(originalRegistration);
        _ = await _registrationRepository.Create(amendRegistration);

        // Act
        var result = await _registrationRepository.IsUniqueSmoName("Test Organization", 10);

        // Assert
        Assert.That(result, Is.False);
        Assert.That(result, Is.False);
    }

    [Test]
    public void Properties_RegistrationNameRankDto_ShouldBeCorrect()
    {
        // Arrange
        var dto = new RegistrationNameRankDto
        {
            Id = 1,
            ParentId = 2,
            Rank = 3,
            Name = "Sample Name"
        };

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(dto.Id, Is.EqualTo(1));
            Assert.That(dto.ParentId, Is.EqualTo(2));
            Assert.That(dto.Rank, Is.EqualTo(3));
            Assert.That(dto.Name, Is.EqualTo("Sample Name"));
        });
    }

    [Test]
    public async Task FindSlateMailerOrganizationById_Found_ShouldReturnResult()
    {
        // Arrange
        var factory = new DatabaseContextFactory();
        var context = await factory.CreateContext();

        // Instantiate the repository
        _registrationRepository = new RegistrationRepository(context);

        // Get seed data
        var data = GetSmoRegistrationData();

        // Add seed data into the database
        await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindSlateMailerOrganizationById(1);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SlateMailerOrganization>());
            Assert.That(result?.Id, Is.EqualTo(data.Id));
        });
    }

    private static Registration GetData(string optionalCandidateName = "Test Candidate")
    {
        return new CandidateIntentionStatement()
        {
            Name = optionalCandidateName,
            StatusId = RegistrationStatus.Draft.Id,
            OriginalId = 1,
            FirstName = "James",
            LastName = "Bond",
            MiddleName = "S",
            Email = "<EMAIL>",
            IsSameAsCandidateAddress = true,
            AddressList = new AddressList()
            {
                Addresses = new List<Address>() { new() {Type ="Residential", City ="City", Purpose = "Candidate",
                    Country="USA", State ="PA", Street="Street" , Zip="19355"} }
            },
            PhoneNumberList = new PhoneNumberList() { PhoneNumbers = [new PhoneNumber { Number = "**********", Type = "Home", }] },
        };
    }

    /// <summary>
    /// Verifies that a <see cref="Lobbyist"/> entity can be retrieved from the database
    /// using its unique identifier with the repository's <c>FindLobbyistByFilerId</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldFindLobbyistByFilerId()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = new Lobbyist
        {
            Name = "Test Lobbyist",
            StatusId = RegistrationStatus.Draft.Id,
            FilerId = 1,
            AddressList = new AddressList()
            {
                Addresses = new List<Address>()
                {
                    new() {
                        Type ="Residential",
                        City ="City",
                        Purpose = "Candidate",
                        Country="USA",
                        State ="PA",
                        Street="Street",
                        Zip="19355"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList()
            {
                PhoneNumbers = [new PhoneNumber { Number = "**********", Type = "Home", }]
            },
            Email = "<EMAIL>",
            EmployerName = "Test Employer",
        };

        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindLobbyingRegistrationByFilerId<Lobbyist>(1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(addedEntity.Id));
            Assert.That(result.Name, Is.EqualTo("Test Lobbyist"));
            Assert.That(result.AddressList, Is.Not.Null);
            Assert.That(result.PhoneNumberList, Is.Not.Null);
        });
    }

    /// <summary>
    /// Create a sample registration linked to another committee
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
    private static GeneralPurposeCommittee GenerateSampleCommittee(string sampleName)
    {
        return new()
        {
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Base",
            Email = "<EMAIL>",
            JurisdictionCounty = string.Empty,
            JurisdictionActive = string.Empty,
            FinancialInstitutionName = string.Empty,
            FinancialInstitutionPhone = string.Empty,
            FinancialInstitutionAccountNumber = string.Empty,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.RecipientCommittee.Id,
                CurrentRegistration = new GeneralPurposeCommittee()
                {
                    StatusId = RegistrationStatus.Accepted.Id,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    JurisdictionCounty = string.Empty,
                    JurisdictionActive = string.Empty,
                    FinancialInstitutionName = string.Empty,
                    FinancialInstitutionPhone = string.Empty,
                    FinancialInstitutionAccountNumber = string.Empty,
                }
            }
        };
    }

    /// <summary>
    /// Create a sample registration linked to another lobbyist employer
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
    private static LobbyistEmployer GenerateSampleLobbyistEmployer(string sampleName)
    {
        return new()
        {
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Base",
            Email = "<EMAIL>",
            EmployerName = "Employer Name",
            EmployerType = "Employer Type",
            BusinessActivity = "Activity",
            BusinessDescription = "Description",
            InterestType = "Interest Type",
            NumberOfMembers = 10,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.LobbyistEmployer.Id,
                CurrentRegistration = new LobbyistEmployer()
                {
                    StatusId = RegistrationStatus.Accepted.Id,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    EmployerName = "Employer Name",
                    EmployerType = "Employer Type",
                    BusinessActivity = "Activity",
                    BusinessDescription = "Description",
                    InterestType = "Interest Type",
                    NumberOfMembers = 10
                },
            }
        };
    }

    /// <summary>
    /// Create a sample registration linked to another lobbyist employer
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
    private static LobbyingFirm GenerateSampleLobbyingFirm(string sampleName)
    {
        return new()
        {
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Base",
            Email = "<EMAIL>",
            LegislativeSessionId = 1,
            ResponsibleOfficerTitle = "Officer Title",
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.LobbyingFirm.Id,
                CurrentRegistration = new LobbyingFirm()
                {
                    StatusId = RegistrationStatus.Accepted.Id,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    LegislativeSessionId = 1,
                    ResponsibleOfficerTitle = "Officer Title"
                },
            }
        };
    }

    /// <summary>
    /// Create a sample registration linked to another lobbyist employer
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
    private static Lobbyist GenerateSampleLobbyist(string sampleName)
    {
        return new()
        {
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Base",
            Email = "<EMAIL>",
            LegislativeSessionId = 1,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.Lobbyist.Id,
                CurrentRegistration = new Lobbyist()
                {
                    StatusId = RegistrationStatus.Accepted.Id,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    LegislativeSessionId = 1,
                },
            }
        };
    }

    /// <summary>
    /// Create a sample registration linked to another lobbyist employer
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
    private static LobbyistTermination GenerateSampleLobbyistTermination(string sampleName, bool isAccepted)
    {
        return new()
        {
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Base",
            Email = "<EMAIL>",
            LegislativeSessionId = 1,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.Lobbyist.Id,
                CurrentRegistration = new LobbyistTermination()
                {
                    TerminatedAt = new DateTime(2024, 11, 5, 0, 0, 0, DateTimeKind.Unspecified),
                    StatusId = isAccepted ? RegistrationStatus.Accepted.Id : RegistrationStatus.Pending.Id,
                    Name = sampleName,
                    Email = "<EMAIL>",
                    LegislativeSessionId = 1,
                },
            }
        };
    }

    /// <summary>
    /// Tests CommitteeSearch by Name
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindCommitteeByIdOrName_ByName_Record()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var committeeName = "Found Committee";

        GeneralPurposeCommittee registration = GenerateSampleCommittee(committeeName);

        var popuplated = await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindCommitteeByIdOrName(committeeName);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(1));
            Filer filer = result.First();
            Assert.That(result.First().CurrentRegistration!.Name, Is.EqualTo(committeeName));
        });
    }
    /// <summary>
    /// Tests CommitteeSearch by Id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindCommitteeByIdOrName_ById_Record()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var committeeName = "Found Committee";

        GeneralPurposeCommittee registration = GenerateSampleCommittee(committeeName);
        Registration populated = await _registrationRepository.Create(registration);
        if (populated.FilerId == null)
        {
            Assert.Fail();
        }

        long committeeId = populated.FilerId ?? -1;

        // Act
        var result = await _registrationRepository.FindCommitteeByIdOrName(committeeId.ToString("D", CultureInfo.CurrentCulture));


        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(1));
            Filer filer = result.First();

            Assert.That(result.First().CurrentRegistration!.Name, Is.EqualTo(committeeName));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch Not Found
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindCommitteeByIdOrName_ByName_Empty()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var committeeName = "Found Committee";

        GeneralPurposeCommittee registration = GenerateSampleCommittee(committeeName);
        Registration populated = await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindCommitteeByIdOrName(committeeName + "notfound");


        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(0));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch Not Found
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindCommitteeByIdOrName_EmptyString_Empty()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var committeeName = "Found Committee";

        GeneralPurposeCommittee registration = GenerateSampleCommittee(committeeName);
        Registration populated = await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindCommitteeByIdOrName("");


        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(0));
        });
    }

    /// <summary>
    /// Tests Gets Contact
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindSmoRegistrationContactsById_FoundId_Records()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);

        SlateMailerOrganization registration = GenerateSampleSmo(100);
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 1, "Treasurer", true, 1, "TFirst", "TLast"));
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 2, "President", true, null, "TFirst", "TLast"));
        Registration populated = await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindSmoRegistrationContactsById(100);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2));
        });
    }

    /// <summary>
    /// Tests Invalid Id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindSmoRegistrationContactsById_InvalidId_Empty()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);

        SlateMailerOrganization registration = GenerateSampleSmo(100);
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 1, "Treasurer", true, 1, "TFirst", "TLast"));
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 2, "President", true, null, "TFirst", "TLast"));
        Registration populated = await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindSmoRegistrationContactsById(999);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(0));
        });
    }

    /// <summary>
    /// Tests Gets Contact
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task GetSmoRegistrationContactById_FoundId_Record()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);

        SlateMailerOrganization registration = GenerateSampleSmo(100);
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 1, "Treasurer", true, 1, "TFirst", "TLast"));
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(100, 2, "President", true, null, "TFirst", "TLast"));
        Registration populated = await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.GetSmoRegistrationRegistrationContactById(100, 1);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Role, Is.EqualTo("Treasurer"));
        });
    }
    [Test]
    public async Task AddAddress_Should_ThrowNotImplementedExceptionAsync()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);
        Assert.ThrowsAsync<NotImplementedException>(() => _registrationRepository.AddAddress(_address, 1));
    }
    [Test]
    public async Task AddPhoneNumber_Should_ThrowNotImplementedExceptionAsync()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);
        Assert.ThrowsAsync<NotImplementedException>(() => _registrationRepository.AddPhoneNumber(new() { Number = "", Type = "" }, 1));
    }
    [Test]
    public async Task RemoveAddress_Should_ThrowNotImplementedExceptionAsync()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);
        Assert.ThrowsAsync<NotImplementedException>(() => _registrationRepository.RemoveAddress(1, 1));
    }
    [Test]
    public async Task RemovePhoneNumber_Should_ThrowNotImplementedExceptionAsync()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);
        Assert.ThrowsAsync<NotImplementedException>(() => _registrationRepository.RemovePhoneNumber(1, 1));
    }
    [Test]
    public async Task UpdateAddress_Should_ThrowNotImplementedExceptionAsync()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);
        Assert.ThrowsAsync<NotImplementedException>(() => _registrationRepository.UpdateAddress(_address, 1));
    }
    [Test]
    public async Task UpdatePhoneNumber_Should_ThrowNotImplementedExceptionAsync()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);
        Assert.ThrowsAsync<NotImplementedException>(() => _registrationRepository.UpdatePhoneNumber(new() { Number = "", Type = "" }, 1));
    }

    [Test]
    public async Task GetFilerOfLatestAcceptedCisRegistration_ShouldReturnFiler()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        var candidateIntention = new CandidateIntentionStatement
        {
            FilerId = 1,
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Test Candidate",
            Email = "<EMAIL>",
            Filer = new()
            {
                Users = new()
                {
                    new()
                    {
                        Id = 1,
                        UserId = 1,
                    }
                }
            }
        };

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var addedEntity = await _registrationRepository.Create(candidateIntention);

        var result = await _registrationRepository.GetFilerOfLatestAcceptedCisRegistration(1);
        Assert.That(result, Is.Not.Null);
    }
    [Test]
    public async Task GetLatestAcceptedCisRegistrationByFilerId_ShouldReturnCandidateIntentionStatement()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var filerId = 1;
        var filer = new Filer { Id = filerId };

        var oldRegistration = new CandidateIntentionStatement
        {
            Id = 2,
            FilerId = filerId,
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Old Candidate",
            Email = "<EMAIL>",
            Filer = filer,
            OriginalId = 2,
            Version = 1
        };

        var latestRegistration = new CandidateIntentionStatement
        {
            Id = 3,
            FilerId = filerId,
            StatusId = RegistrationStatus.Accepted.Id,
            Name = "Latest Candidate",
            Email = "<EMAIL>",
            Filer = filer,
            OriginalId = oldRegistration.Id,
            Version = 2
        };

        context.CandidateIntentionStatements.AddRange(oldRegistration, latestRegistration);
        await context.SaveChangesAsync();

        filer.CurrentRegistrationId = latestRegistration.Id;
        context.Filers.Add(filer);

        _registrationRepository = new RegistrationRepository(context);

        // Act
        var result = await _registrationRepository.GetLatestAcceptedCisRegistrationByFilerId(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(latestRegistration.Id));
            Assert.That(result.Name, Is.EqualTo("Latest Candidate"));
        });
    }

    [Test]
    public async Task ShouldFindCandidateIntentionStatementByFilerId()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var candidateIntention = new CandidateIntentionStatement
        {
            FilerId = 10,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test Candidate",
            Email = "<EMAIL>",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
            {
                new() {
                    Purpose = "Candidate",
                    Street = "123 Campaign St",
                    City = "Sacramento",
                    State = "CA",
                    Country = "USA",
                    Zip = "95814",
                    Type = "Home"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "**********", Type = "Home" }
            }
            },
            ElectionRace = new ElectionRace
            {
                Election = new Election
                {
                    Name = "General Election",
                    ElectionDate = new DateTime(2024, 11, 5, 0, 0, 0, DateTimeKind.Unspecified),
                    ElectionType = new ElectionType { Name = "General" }
                },
                Office = new Office { Name = "Governor", AgencyName = "TEST Agency" },
                District = new District { Name = "District 1" }
            }
        };

        var addedEntity = await _registrationRepository.Create(candidateIntention);

        // Act
        var result = await _registrationRepository.GetCandidateIntentionStatementWithElectionByFilerId(candidateIntention.FilerId.Value);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Id, Is.EqualTo(addedEntity.Id));
            Assert.That(result.Name, Is.EqualTo("Test Candidate"));
            Assert.That(result.AddressList?.Addresses.Count, Is.GreaterThan(0));
            Assert.That(result.PhoneNumberList?.PhoneNumbers.Count, Is.GreaterThan(0));
            Assert.That(result.ElectionRace?.Election?.ElectionDate, Is.EqualTo(new DateTime(2024, 11, 5, 0, 0, 0, DateTimeKind.Unspecified)));
            Assert.That(result.ElectionRace?.Office?.Name, Is.EqualTo("Governor"));
            Assert.That(result.ElectionRace?.District?.Name, Is.EqualTo("District 1"));
        });
    }
    [Test]
    public async Task ShouldFindRegistrationByFilerId()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var filerId = 1;
        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);

        var data = new CandidateIntentionStatement
        {
            FilerId = 1,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test Candidate",
            Email = "<EMAIL>",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
            {
                new() {
                    Purpose = "Candidate",
                    Street = "123 Campaign St",
                    City = "Sacramento",
                    State = "CA",
                    Country = "USA",
                    Zip = "95814",
                    Type = "Home"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "**********", Type = "Home" }
            }
            },
            ElectionRace = new ElectionRace
            {
                Election = new Election
                {
                    Name = "General Election",
                    ElectionDate = new DateTime(2024, 11, 5, 0, 0, 0, DateTimeKind.Unspecified),
                    ElectionType = new ElectionType { Name = "General" }
                },
                Office = new Office { Name = "Governor", AgencyName = "TEST Agency" },
                District = new District { Name = "District 1" }
            }
        };
        // Add test data to the in-memory database
        context.Set<CandidateIntentionStatement>().Add(data);
        await context.SaveChangesAsync();

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        var result = await repository.GetRegistrationByFilerId(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(data.Id));
            Assert.That(result?.Name, Is.EqualTo(data.Name));
            Assert.That(result?.Email, Is.EqualTo(data.Email));
        });
    }
    [Test]
    public async Task ShouldFindCisRegistrationWithdrawalByFilerId()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var id = 1;
        // Create context with in-memory database
        _registrationRepository = new RegistrationRepository(context);

        var data = new CisWithdrawal
        {
            FilerId = 1,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test Candidate",
            Email = "<EMAIL>",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
            {
                new() {
                    Purpose = "Candidate",
                    Street = "123 Campaign St",
                    City = "Sacramento",
                    State = "CA",
                    Country = "USA",
                    Zip = "95814",
                    Type = "Home"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "**********", Type = "Home" }
            }
            },
        };
        // Add test data to the in-memory database
        context.Set<CisWithdrawal>().Add(data);
        await context.SaveChangesAsync();

        // Create repository instance with context
        var repository = new RegistrationRepository(context);

        // Act
        var result = await repository.GetCisRegistrationWithdrawalById(id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.Id, Is.EqualTo(data.Id));
            Assert.That(result?.Name, Is.EqualTo(data.Name));
            Assert.That(result?.Email, Is.EqualTo(data.Email));
        });
    }
    [Test]
    public async Task FindSmoRegistrationContactsByIdNoTracking_FoundRecord_ReturnResult()
    {
        // Arrange
        var registrationId = 4;

        // Seed data
        var registration = GenerateSampleSmo(registrationId);
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(1, 1, "Treasurer", true, 1, "TFirst", "TLast"));
        registration.RegistrationRegistrationContacts.Add(GenerateSampleRegistratonContact(1, 2, "President", true, null, "TFirst", "TLast"));
        await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindSmoRegistrationContactsByIdNoTracking(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<RegistrationRegistrationContact>>());
        });
    }

    [Test]
    public async Task FindSmoRegistrationContactsByIdNoTracking_NotFoundRecord_ReturnEmptyObject()
    {
        // Arrange
        var registrationId = 1;

        // Act
        var result = await _registrationRepository.FindSmoRegistrationContactsByIdNoTracking(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Empty);
            Assert.That(result, Is.InstanceOf<List<RegistrationRegistrationContact>>());
        });
    }

    [Test]
    public async Task FindExistingSmoAmendmentRegistration_FoundRecord_ReturnResult()
    {
        // Arrange
        var registrationId = 3;
        var parentRegistrationId = 99;
        var statusId = RegistrationStatus.Pending.Id;

        // Seed data
        var registration = GenerateSampleSmo(registrationId, statusId);
        registration.ParentId = parentRegistrationId;
        await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindExistingSmoAmendmentRegistration(parentRegistrationId, statusId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SlateMailerOrganization>());
        });
    }

    [Test]
    public async Task FindExistingSmoAmendmentRegistration_NotFoundRecord_ReturnNull()
    {
        // Arrange
        var parentRegistrationId = 2;
        var statusId = RegistrationStatus.Pending.Id;

        // Act
        var result = await _registrationRepository.FindExistingSmoAmendmentRegistration(parentRegistrationId, statusId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Null);
        });
    }

    [Test]
    public async Task ShouldFindLobbyistById()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = new Lobbyist
        {
            Id = 10,
            Name = "Test Lobbyist",
            StatusId = RegistrationStatus.Draft.Id,
            FilerId = 1,
            AddressList = new AddressList()
            {
                Addresses = new List<Address>()
                {
                    new() {
                        Type ="Residential",
                        City ="City",
                        Purpose = "Candidate",
                        Country="USA",
                        State ="PA",
                        Street="Street",
                        Zip="19355"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList()
            {
                PhoneNumbers = [new PhoneNumber { Number = "**********", Type = "Home", }]
            },
            Email = "<EMAIL>",
            EmployerName = "Test Employer",
        };

        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindLobbyistById(10);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(addedEntity.Id));
            Assert.That(result.Name, Is.EqualTo("Test Lobbyist"));
            Assert.That(result.AddressList, Is.Not.Null);
            Assert.That(result.PhoneNumberList, Is.Not.Null);
        });
    }

    #region
    [Test]
    public async Task FindControlledCommitteesByNameOrId_NotFoundRecord_ReturnEmpty()
    {
        // Arrange

        // Act
        var result = await _registrationRepository.FindControlledCommitteesByNameOrId("");

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<IEnumerable<CandidateControlledCommittee>>());
        });
    }
    [Test]
    public async Task FindControlledCommitteesByNameOrId_FoundRecord_ReturnResult()
    {
        // Arrange
        var statusId = RegistrationStatus.Pending.Id;

        // Act
        var result = await _registrationRepository.FindControlledCommitteesByNameOrId("string");

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<IEnumerable<CandidateControlledCommittee>>());
        });
    }
    [Test]
    public async Task GetControlledCommitteeByFiler()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var committee = new CandidateControlledCommittee
        {
            FilerId = 10,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test Candidate",
            Email = "<EMAIL>",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
            {
                new() {
                    Purpose = "Candidate",
                    Street = "123 Campaign St",
                    City = "Sacramento",
                    State = "CA",
                    Country = "USA",
                    Zip = "95814",
                    Type = "Home"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
                {
                    new() { Number = "**********", Type = "Home" }
                }
            },
            JurisdictionCounty = "",
            JurisdictionActive = "",
            FinancialInstitutionAccountNumber = "",
            FinancialInstitutionName = "",
            FinancialInstitutionPhone = "",
            ElectionOfficeSought = "",
            ElectionDistrictNumber = "",
            ElectionId = 0,
        };

        var addedEntity = await _registrationRepository.Create(committee);
        // Act
        var result = await _registrationRepository.GetControlledCommitteeByFilerId(10);
        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(10));
            Assert.That(result.Name, Is.EqualTo("Test Candidate"));
            Assert.That(result.Email, Is.EqualTo("<EMAIL>"));
        });
    }
    #endregion

    [Test]
    public async Task SearchSmoRegistrationByIdOrName_EmptyQuery_ReturnEmptyResult()
    {
        // Arrange
        var statusId = RegistrationStatus.Pending.Id;

        // Act
        var result = await _registrationRepository.SearchSmoRegistrationByIdOrName(string.Empty, statusId, null);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Empty);
            Assert.That(result, Is.InstanceOf<List<SlateMailerOrganization>>());
        });
    }

    [Test]
    public async Task SearchSmoRegistrationByIdOrName_ValidQuery_ReturnResult()
    {
        // Arrange
        var statusId = RegistrationStatus.Pending.Id;
        var query = "1";
        var userId = 1;

        // Seed data
        var registration = GenerateSampleSmo(6, statusId);
        await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.SearchSmoRegistrationByIdOrName(query, statusId, userId);

        // Assert
        Assert.That(result, Is.InstanceOf<List<SlateMailerOrganization>>());
    }

    [Test]
    public async Task GetSmoRegistrationByFilerId_ValidQuery_ReturnResult()
    {
        // Arrange
        var filerId = 1;

        // Seed data
        var registration = GenerateSampleSmo(5, RegistrationStatus.Accepted.Id);
        registration.FilerId = filerId;
        registration.Version = 1;
        _ = await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.FindSmoRegistrationLatestAcceptedByFilerId(filerId);

        // Assert
        Assert.That(result, Is.InstanceOf<SlateMailerOrganization>());
    }

    #region FindPrimarilyFormedCommitteeByIdOrName
    [Test]
    public async Task FindPrimarilyFormedCommitteeByIdOrName_ShouldReturnEmpty_WhenQueryIsEmpty()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);

        // Act
        var result = await _registrationRepository.FindPrimarilyFormedCommitteeByIdOrName("");

        // Assert
        Assert.That(result, Is.Empty);
    }
    [Test]
    public async Task FindPrimarilyFormedCommitteeByIdOrName_ShouldReturnMatch_ByName()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);

        var committee = new PrimarilyFormedCommittee
        {
            Id = 1,
            Name = "test Match Committee",
            StatusId = 2,
            CommitteeTypeId = CommitteeType.PrimarilyFormed.Id,
            JurisdictionCounty = "County",
            JurisdictionActive = "Jurisdiction",
            FinancialInstitutionName = "Bank",
            FinancialInstitutionPhone = "************",
            FinancialInstitutionAccountNumber = "*********",
            Email = "test",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Purpose = "Business",
                        Type = "Office"
                    }
                }
            },
            Filer = new Filer
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.Candidate.Id,
                Users = new List<FilerUser>
                {
                    new()
                    {
                        FilerRole = FilerRole.RecipientCommittee_Treasurer,
                        User = new User
                        {
                            FirstName = "Test",
                            LastName = "Treasurer",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            }
        };
        await _registrationRepository.Create(committee);

        // Act
        var result = await _registrationRepository.FindPrimarilyFormedCommitteeByIdOrName("t");

        // Assert
        Assert.That(result.Count, Is.EqualTo(1));
    }
    #endregion

    [Test]
    public async Task FindLobbyingRegistrationByFilerId_ShouldReturnEntity_WhenEntityExists()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);

        var lobbyist = new Lobbyist
        {
            FilerId = 100,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test Lobbyist",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
            {
                new() {
                    Purpose = "Business",
                    Street = "123 Lobby St",
                    City = "Sacramento",
                    State = "CA",
                    Country = "USA",
                    Zip = "95814",
                    Type = "Office"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "9165551234", Type = "Business" }
            }
            },
            Email = "<EMAIL>"
        };

        _ = await _registrationRepository.Create(lobbyist);

        // Act
        var result = await _registrationRepository.FindLobbyingRegistrationByFilerId<Lobbyist>(100);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(100));
            Assert.That(result.Name, Is.EqualTo("Test Lobbyist"));
            Assert.That(result.AddressList, Is.Not.Null);
            Assert.That(result.PhoneNumberList, Is.Not.Null);
        });
    }

    [Test]
    public async Task FindLobbyingRegistrationByFilerId_ShouldReturnNull_WhenEntityDoesNotExist()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);

        // Act
        var result = await _registrationRepository.FindLobbyingRegistrationByFilerId<Lobbyist>(999);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetCisByFilerId_ShouldReturnCandidateStatementData()

    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        _registrationRepository = new RegistrationRepository(context);

        var registration = GenerateCandidateIntentionStatment();

        await _registrationRepository.Create(registration);

        // Act
        var result = await _registrationRepository.GetCisByFilerId(10);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetCommitteeByFilerId_ReturnsExpectedCommittee()
    {
        // Arrange
        var id = 2L;
        var filerId = 1L;
        var committee = new PrimarilyFormedCommittee
        {
            Id = id,
            Name = "test Match Committee",
            FilerId = filerId,
            StatusId = 2,
            CommitteeTypeId = CommitteeType.PrimarilyFormed.Id,
            JurisdictionCounty = "County",
            JurisdictionActive = "Jurisdiction",
            FinancialInstitutionName = "Bank",
            FinancialInstitutionPhone = "************",
            FinancialInstitutionAccountNumber = "*********",
            Email = "test",
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Purpose = "Business",
                        Type = "Office"
                    }
                }
            },
            Filer = new Filer
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.Candidate.Id,
                Users = new List<FilerUser>
                {
                    new()
                    {
                        FilerRole = FilerRole.RecipientCommittee_Treasurer,
                        User = new User
                        {
                            FirstName = "Test",
                            LastName = "Treasurer",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            }
        };
        await _registrationRepository.Create(committee);

        // Act
        var result = await _registrationRepository.GetCommitteeByFilerId(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.FilerId, Is.EqualTo(filerId));
            Assert.That(result!.Id, Is.EqualTo(id));
        });
    }

    #region Lobbyist Registration
    /// <summary>
    /// Verifies that a<see cref = "Registration" /> entity can be created, added to the database,
    /// and successfully saved using the repository's <c>CreateLobbyist</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldCreateLobbyistRegistrationAndSaveChanges()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext(x => x.EnableDetailedErrors().EnableDetailedErrors());

        _registrationRepository = new RegistrationRepository(context);

        var data = new Lobbyist
        {
            Name = "Test",
            StatusId = 1,
            OriginalId = 1,
            SelfRegister = true,
        };

        var result = await _registrationRepository.CreateLobbyist(data);
        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(data.Id, Is.EqualTo(result.Id));
            Assert.That(data.Id, Is.EqualTo(result.OriginalId));
        });

    }

    /// <summary>
    /// Tests CommitteeSearch by Name
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistEmployerOrLobbyingFirmByIdOrName_ByName_Record()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var name = "Found Lobbyist Employer or Lobbying Firm";

        LobbyistEmployer registration1 = GenerateSampleLobbyistEmployer(name);
        LobbyingFirm registration2 = GenerateSampleLobbyingFirm(name);

        await _registrationRepository.Create(registration1);
        await _registrationRepository.Create(registration2);

        // Act
        var result = await _registrationRepository.FindLobbyistEmployerOrLobbyingFirmByIdOrName(name);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.ElementAt(0).CurrentRegistration!.Name, Is.EqualTo(name));
            Assert.That(result.ElementAt(1).CurrentRegistration!.Name, Is.EqualTo(name));
        });
    }
    /// <summary>
    /// Tests CommitteeSearch by Id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistEmployerOrLobbyingFirmByIdOrName_ById_Record()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var name = "Found Lobbyist Employer or Lobbying Firm";

        LobbyistEmployer registration1 = GenerateSampleLobbyistEmployer(name);

        Registration populated = await _registrationRepository.Create(registration1);
        if (populated.FilerId == null)
        {
            Assert.Fail();
        }

        long lobbyistEmployerId = populated.FilerId ?? -1;

        // Act
        var result = await _registrationRepository.FindLobbyistEmployerOrLobbyingFirmByIdOrName(lobbyistEmployerId.ToString("D", CultureInfo.CurrentCulture));

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(1));
            Assert.That(result.First().CurrentRegistration!.Name, Is.EqualTo(name));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch Not Found
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistEmployerOrLobbyingFirmByIdOrName_ByName_Empty()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        var name = "Found Lobbyist Employer or Lobbying Firm";

        LobbyistEmployer registration1 = GenerateSampleLobbyistEmployer(name);
        LobbyingFirm registration2 = GenerateSampleLobbyingFirm(name);

        await _registrationRepository.Create(registration1);
        await _registrationRepository.Create(registration2);

        // Act
        var result = await _registrationRepository.FindLobbyistEmployerOrLobbyingFirmByIdOrName(name + "notfound");


        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(0));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch Not Found
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistEmployerOrLobbyingFirmByIdOrName_EmptyString_Empty()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var name = "Found Lobbyist Employer or Lobbying Firm";

        LobbyistEmployer registration1 = GenerateSampleLobbyistEmployer(name);
        LobbyingFirm registration2 = GenerateSampleLobbyingFirm(name);

        await _registrationRepository.Create(registration1);
        await _registrationRepository.Create(registration2);

        // Act
        var result = await _registrationRepository.FindLobbyistEmployerOrLobbyingFirmByIdOrName("");


        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(0));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch by Name
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistByIdOrName_ByName_Record()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);
        var name = "Found Lobbyist";

        Lobbyist registration1 = GenerateSampleLobbyist(name);
        LobbyistTermination registration2 = GenerateSampleLobbyistTermination(name, false);

        await _registrationRepository.Create(registration1);
        await _registrationRepository.Create(registration2);

        // Act
        var result = await _registrationRepository.FindLobbyistByIdOrName("Found Lobbyist");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(1));
            Assert.That(result.ElementAt(0).CurrentRegistration!.Name, Is.EqualTo(name));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch by Id
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistByIdOrName_ById_Record()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);
        var name = "Found Lobbyist";

        LobbyistTermination registration1 = GenerateSampleLobbyistTermination(name, true);

        Registration populated = await _registrationRepository.Create(registration1);
        if (populated.FilerId == null)
        {
            Assert.Fail();
        }

        long lobbyistEmployerId = populated.FilerId ?? -1;

        // Act
        var result = await _registrationRepository.FindLobbyistByIdOrName(lobbyistEmployerId.ToString("D", CultureInfo.CurrentCulture));

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(1));
            Assert.That(result.First().CurrentRegistration!.Name, Is.EqualTo(name));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch Not Found
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistByIdOrName_ByName_Empty()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        var name = "Found Lobbyist";

        Lobbyist registration1 = GenerateSampleLobbyist(name);

        await _registrationRepository.Create(registration1);

        // Act
        var result = await _registrationRepository.FindLobbyistByIdOrName(name + "notfound");


        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(0));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch Not Found
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistByIdOrName_EmptyString_Empty()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var name = "Found Lobbyist";

        Lobbyist registration1 = GenerateSampleLobbyist(name);

        await _registrationRepository.Create(registration1);

        // Act
        var result = await _registrationRepository.FindLobbyistByIdOrName("");


        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(0));
        });
    }

    /// <summary>
    /// Tests CommitteeSearch Not Found
    /// </summary>
    /// <returns></returns>
    [Test]
    public async Task FindLobbyistEmployerByName_ShouldReturnRecord()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var name = "Test Lobbyist Employer";

        LobbyistEmployer registration1 = GenerateSampleLobbyistEmployer(name);

        await _registrationRepository.Create(registration1);

        // Act
        var result = await _registrationRepository.FindLobbyistEmployerByName(name);


        // Assert
        Assert.That(result, Is.Not.Null);
    }

    private static LobbyistWithdrawal GenerateLobbyistWithdrawal()
    {
        return new LobbyistWithdrawal
        {
            Name = "Test Lobbyist Withdrawal",
            StatusId = RegistrationStatus.Draft.Id,
            FilerId = 10,
            AddressList = new AddressList()
            {
                Addresses = new List<Address>()
                {
                    new() {
                        Type ="Residential",
                        City ="City",
                        Purpose = "Candidate",
                        Country="USA",
                        State ="PA",
                        Street="Street",
                        Zip="19355"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList()
            {
                PhoneNumbers = [new PhoneNumber { Number = "**********", Type = "Home", }]
            },
            Email = "<EMAIL>",
        };
    }

    private static LobbyistTermination GenerateLobbyistTermination()
    {
        return new LobbyistTermination()
        {
            Id = 1,
            LegislativeSessionId = 1,
            FirstName = "FirstName",
            MiddleName = "MiddleName",
            LastName = "LastName",
            Name = "Name",
            Email = "Email",
            FilerId = 1,
            StatusId = RegistrationStatus.Accepted.Id,
            TerminatedAt = DateTime.UtcNow,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                {
                    new()
                    {
                        Street = "123 Main St",
                        City = "Sacramento",
                        State = "CA",
                        Zip = "95814",
                        Country = "USA",
                        Purpose = "Business",
                        Type = "Office"
                    }
                }
            },
            PhoneNumberList = new PhoneNumberList
            {
                Id = 1,
                PhoneNumbers = new List<PhoneNumber>
                {
                    new()
                    {
                        Id = 1,
                        CountryCode = "+1",
                        CountryId = 1,
                        Number = "8081234567",
                        Type = "Work"
                    }
                }
            }
        };
    }

    [Test]
    public async Task FindLobbyistWithdrawalById_ShouldReturnRecord()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = GenerateLobbyistWithdrawal();

        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindLobbyistWithdrawalById(data.Id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result?.Id, Is.EqualTo(addedEntity.Id));
            Assert.That(result?.Name, Is.EqualTo(addedEntity.Name));
        });
    }

    [Test]
    public async Task GetRegistrationDiscriminatorById_ShouldReturnRecord()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = GenerateLobbyistWithdrawal();

        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.GetRegistrationDiscriminatorById(data.Id);

        // Assert
        Assert.That(result, Is.EqualTo(nameof(LobbyistWithdrawal)));
    }

    [Test]
    public async Task FindLobbyistTerminationById_ShouldReturnRecord()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        var data = GenerateLobbyistTermination();

        var addedEntity = await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindLobbyistTerminationById(data.Id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.InstanceOf<LobbyistTermination>());
    }

    [Test]
    public async Task FindLobbyistRegistrationByEmployer_ReturnNoRecord()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        long id = -1;

        // Act
        var result = await _registrationRepository.FindLobbyistRegistrationByEmployer(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<List<Lobbyist>>());
            Assert.That(result?.Count, Is.EqualTo(0));
        });
    }

    [Test]
    public async Task FindLobbyistRegistrationByEmployer_ShouldReturnRecord()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        var name = "Found Lobbyist Employer or Lobbying Firm";
        var filerId = 100;

        LobbyistEmployer employer = GenerateSampleLobbyistEmployer(name);
        employer.FilerId = filerId;
        employer.Filer = new Filer
        {
            Id = filerId,
            FilerStatusId = 1,
            FilerTypeId = 1,
            FilerLinks = new List<FilerLink>
            {
                new() {
                    Id = 1,
                    FilerId = filerId,
                    FilerLinkTypeId = 1,
                    LinkedEntityId = 2,
                    EffectiveDate = DateTime.Now,
                    CreatedBy = 1,
                    ModifiedBy = 1,
                }
            }
        };


        // Arrange
        _registrationRepository = new RegistrationRepository(context);
        long id = -1;

        // Act
        var result = await _registrationRepository.FindLobbyistRegistrationByEmployer(id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<List<Lobbyist>>());
            Assert.That(result?.Count, Is.EqualTo(0));
        });
    }

    [Test]
    public async Task FindListOfRegistrationsByFilerId_ReturnsRegistrations_WhenFilerIdExists()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);

        var filerId = 123L;
        var registration1 = new Lobbyist
        {
            Id = 1,
            FilerId = filerId,
            Name = "Lobbyist 1",
            StatusId = 3
        };
        var registration2 = new Lobbyist
        {
            Id = 2,
            FilerId = filerId,
            Name = "Lobbyist 2",
            StatusId = 3
        };

        context.Set<Lobbyist>().Add(registration1);
        context.Set<Lobbyist>().Add(registration2);
        await context.SaveChangesAsync();

        // Act
        var result = await _registrationRepository.FindListOfRegistrationsByFilerId<Lobbyist>(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Has.Count.EqualTo(2));
            Assert.That(result[0]?.Name, Is.EqualTo("Lobbyist 1"));
            Assert.That(result[1]?.Name, Is.EqualTo("Lobbyist 2"));
        });
    }

    [Test]
    public async Task FindListOfRegistrationsByFilerId_ReturnsEmptyList_WhenNoRegistrationsFound()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);

        var filerId = 999L;
        // No registrations added for this filer ID

        // Act
        var result = await _registrationRepository.FindListOfRegistrationsByFilerId<Lobbyist>(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.Empty);
        });
    }

    [Test]
    public async Task FindListOfRegistrationsByFilerId_IncludesLegislativeSession_ForLobbyistTypes()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        _registrationRepository = new RegistrationRepository(context);

        var filerId = 1L;
        var legislativeSession = new LegislativeSession
        {
            Id = 1,
            Name = "Quarterly"
        };
        context.Set<LegislativeSession>().Add(legislativeSession);
        await context.SaveChangesAsync();

        var registration = new Lobbyist
        {
            Id = 1,
            FilerId = filerId,
            Name = "Lobbyist",
            LegislativeSessionId = 1,
            LegislativeSession = legislativeSession,
            StatusId = 3
        };
        context.Set<Lobbyist>().Add(registration);
        await context.SaveChangesAsync();

        // Act
        var result = await _registrationRepository.FindListOfRegistrationsByFilerId<Lobbyist>(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result[0]?.LegislativeSession, Is.Not.Null);
            Assert.That(result[0]?.LegislativeSession?.Id, Is.EqualTo(1));
        });
    }

    #endregion

    #region FindSlateMailerOrganizationWithParentById
    [Test]
    public async Task FindSlateMailerOrganizationWithParentById_Found_ShouldReturnResult()
    {
        // Arrange
        // Get seed data
        var registrationId = 7;
        var data = GenerateSampleSmo(registrationId);

        // Add seed data into the database
        await _registrationRepository.Create(data);

        // Act
        var result = await _registrationRepository.FindSlateMailerOrganizationWithParentById(registrationId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<SlateMailerOrganization>());
            Assert.That(result!.Id, Is.EqualTo(data.Id));
            Assert.That(result.Parent, Is.Not.Null);
            Assert.That(result.Parent, Is.InstanceOf<SlateMailerOrganization>());
        });
    }
    #endregion

    #region Private
    private static SlateMailerOrganization GetSmoRegistrationData() => new()
    {
        Id = 1,
        Email = "<EMAIL>",
        Name = "Test",
        StatusId = 1,
        AddressList = new AddressList
        {
            Id = 1,
        },
        PhoneNumberList = new PhoneNumberList
        {
            Id = 1,
        },
        RegistrationRegistrationContacts = new List<RegistrationRegistrationContact>
        {
            new() {
                Id = 1,
                CapitalContributionOver10K = true,
                PercentOfOwnership = 1m,
                CumulativeCapitalContributions = 1m,
                Role = "",
                TenPercentOrGreater = false,
                CreatedBy = 1,
                ModifiedBy = 1,
                Title = "TestTitle",
            }
        },
        Filer = new Filer
        {
            Id = 1,
            FilerStatusId = 1,
            FilerTypeId = 1,
            FilerLinks = new List<FilerLink>
            {
                new() {
                    Id = 1,
                    FilerId = 1,
                    FilerLinkTypeId = 1,
                    LinkedEntityId = 2,
                    EffectiveDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                    CreatedBy = 1,
                    ModifiedBy = 1,
                }
            }
        },
    };

    /// <summary>
    /// Create a sample registration contact
    /// </summary>
    /// <param name="sampleName"></param>
    /// <returns></returns>
    private static RegistrationRegistrationContact GenerateSampleRegistratonContact(long registrationId, long contactId, string role, bool canAuthorize, long? userId, string firstName, string lastName)
    {
        return new()
        {
            Id = contactId,
            CanAuthorizeSlateMailerContents = canAuthorize,
            CreatedBy = userId ?? 0,
            ModifiedBy = userId ?? 0,
            RegistrationId = registrationId,
            Role = role,
            Title = "TestTitle",
            RegistrationContact = new()
            {
                FirstName = firstName,
                LastName = lastName,
                MiddleName = string.Empty,
                Email = "<EMAIL>",
                AddressList = new()
                {
                    Addresses = new()
                    {
                        new()
                        {
                            Country = "United States",
                            Street = "Street1",
                            Street2 = "Street2",
                            City = "Honolulu",
                            State = "HI",
                            Zip = "96812",
                            Type = "Residential",
                            Purpose = "PrincipalOfficer",
                        },
                    }
                },
                PhoneNumberList = new()
                {
                    PhoneNumbers = new()
                    {
                        new()
                        {
                            InternationalNumber = false,
                            CountryCode = "1",
                            Extension = "808",
                            Number = "1231234",
                            Type = "Phone",
                        }
                    }
                }
            },

            // Default
            CapitalContributionOver10K = false,
            PercentOfOwnership = 100,
            CumulativeCapitalContributions = 0,
            TenPercentOrGreater = false,
        };
    }

    private static SlateMailerOrganization GenerateSampleSmo(long id, long statusId = 3, long parentId = 99)
    {
        return new()
        {
            Id = id,
            StatusId = statusId,
            Name = "Base",
            Email = "<EMAIL>",
            OriginalId = 1,
            ParentId = 2,
            Filer = new()
            {
                FilerStatusId = FilerStatus.Active.Id,
                FilerTypeId = FilerType.SlateMailerOrg.Id,
                Users = new()
                {
                    new()
                    {
                        FilerRoleId = FilerRole.SlateMailerOrg_Treasurer.Id,
                        User = new()
                        {
                            FirstName = "FirstName",
                            LastName = "LastName",
                            EmailAddress = "<EMAIL>",
                            EntraOid = "TestOid"
                        }
                    }
                }
            },
            AddressList = new()
            {
                Addresses = new()
                    {
                        new()
                        {
                            Country = "United States",
                            Street = "Street1",
                            Street2 = "Street2",
                            City = "Honolulu",
                            State = "HI",
                            Zip = "96812",
                            Type = "Residential",
                            Purpose = "PrincipalOfficer",
                        },
                    }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                    {
                        new()
                        {
                            InternationalNumber = false,
                            CountryCode = "1",
                            Extension = "808",
                            Number = "1231234",
                            Type = "Phone",
                        }
                    }
            },
            Parent = new SlateMailerOrganization
            {
                Id = parentId,
                Email = "<EMAIL>",
                Name = "Test",
                StatusId = 3,
            }
        };
    }

    private static CandidateIntentionStatement GenerateCandidateIntentionStatment()
    {
        return new CandidateIntentionStatement
        {
            FilerId = 10,
            StatusId = RegistrationStatus.Draft.Id,
            Name = "Test Candidate",
            Email = "<EMAIL>",
            CommitteeTypeId = CommitteeType.PrimarilyFormed.Id,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
            {
                new() {
                    Purpose = "Candidate",
                    Street = "123 Campaign St",
                    City = "Sacramento",
                    State = "CA",
                    Country = "USA",
                    Zip = "95814",
                    Type = "Home"
                }
            }
            },
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
            {
                new() { Number = "**********", Type = "Home" }
            }
            },
            ElectionRace = new ElectionRace
            {
                Election = new Election
                {
                    Name = "General Election",
                    ElectionDate = new DateTime(2024, 11, 5, 0, 0, 0, DateTimeKind.Unspecified),
                    ElectionType = new ElectionType { Name = "General" }
                },
                Office = new Office { Name = "Governor", AgencyName = "TEST Agency" },
                District = new District { Name = "District 1" }
            },
            Filer = new Filer
            {
                Id = 10,
                Users = new List<FilerUser> { new() { FilerRole = FilerRole.RecipientCommittee_Treasurer, FilerId = 10 } }
            }
        };
    }
    #endregion
}

