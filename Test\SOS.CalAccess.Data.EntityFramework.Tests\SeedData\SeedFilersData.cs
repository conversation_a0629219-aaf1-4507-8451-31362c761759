// <copyright file="SeedFilersData.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>


using SOS.CalAccess.Data.EntityFramework.Repositories.SeedData;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Filers;

/// <summary>
/// Static helper class for context initialization and seeding data during tests.
/// </summary>
public static class SeedFilersData
{

    /// <summary>
    /// Prepares the data for testing by seeding the database with a registration and associated filer.
    /// </summary>
    /// <param name="context">The database context to use for seeding data.</param>
    /// <param name ="status">optional status for the registration.</param>
    /// <returns>An <see cref="AssertionReferences"/> object containing references to the seeded data.</returns>
    public static async Task<AssertionReferences> PrepareFilersData(this DatabaseContext context, long? status = default)
    {
        var registration = GetRegistration(status);

        context.Registrations.Add(registration);

        await context.SaveChangesAsync();

        var filer = new Filer
        {
            CurrentRegistrationId = registration.Id,
            FilerStatusId = FilerStatus.Active.Id,
        };

        registration.Filer = filer;

        await context.SaveChangesAsync();

        return new AssertionReferences { CurrentRegistrationId = registration.Id, FilerId = filer.Id, };
    }
    private static CandidateIntentionStatement GetRegistration(long? status = null) => new()
    {
        Name = "Test Candidate",
        StatusId = status ?? RegistrationStatus.Accepted.Id,
        ApprovedAt = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        AddressList = SeedValues.GetTestAddressList(),
        PhoneNumberList = SeedValues.GetTestPhoneNumberList(),
    };

    /// <summary>
    /// Class for holding the output and reference items from a data seeding process
    /// that can be used for asserting test conditions.
    /// </summary>
    public sealed class AssertionReferences
    {
        /// <summary>
        /// Gets or sets the current registration id.
        /// </summary>
        public long CurrentRegistrationId { get; set; }

        /// <summary>
        /// Gets or sets the filer id.
        /// </summary>
        public long FilerId { get; set; }
    }
}
