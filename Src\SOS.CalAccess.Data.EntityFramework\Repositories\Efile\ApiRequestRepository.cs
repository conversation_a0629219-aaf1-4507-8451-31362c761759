using SOS.CalAccess.Data.Efile;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Efile;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Efile;

/// <inheritdoc />
public class ApiRequestRepository(DatabaseContext dbContext, IDateTimeSvc dateTimeSvc) :
    Repository<ApiRequest, int>(dbContext), IApiRequestRepository
{
    public Task<List<ApiRequest>> SearchApiRequests(ApiRequestSearchCriteria criteria)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<ApiRequest> UpdateStatusOfApiRequests(int id, int statusId, List<ApiError> errors)
    {
        var apiRequest = await dbContext.ApiRequest.FindAsync(id)
                        ?? throw new ArgumentException($"API Request {id} is null");
        apiRequest.Status = await dbContext.ApiRequestStatus.FindAsync(statusId)
                            ?? throw new ArgumentException($"API Request status {statusId} is not found");
        apiRequest.ApiRequestStatusId = statusId;
        apiRequest.ModifiedAt = dateTimeSvc.GetCurrentDateTime();
        if (errors != null && errors.Count != 0)
        {
            foreach (var error in errors)
            {
                dbContext.ApiError.Add(new ApiError
                {
                    ApiRequestId = id,
                    ApiErrorCode = error.ApiErrorCode,
                    ApiErrorDescription = error.ApiErrorDescription,
                    ApiErrorField = error.ApiErrorField,
                });
            }
        }
        await dbContext.SaveChangesAsync();
        return apiRequest;
    }

    /// <inheritdoc />
    public async Task UpdateFilingIdOfApiRequests(int id, string filingId)
    {
        try
        {
            var apiRequest = await dbContext.ApiRequest.FindAsync(id)
                         ?? throw new ArgumentException($"request Id {id} not found");
            apiRequest.FilingId = filingId ?? throw new ArgumentException("Invalid filingid provided.");
            apiRequest.ModifiedAt = dateTimeSvc.GetCurrentDateTime();
            await dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(ex.Message);
        }
    }
}
