using System.Linq.Expressions;
using System.Reflection;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using SendGrid.Helpers.Errors.Model;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Contacts;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Filings;
using SOS.CalAccess.Data.Contracts.FilerDisclosure.Transactions;
using SOS.CalAccess.Data.Contracts.FilerRegistration;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Filers;
using SOS.CalAccess.Data.Contracts.FilerRegistration.Registrations;
using SOS.CalAccess.Data.Contracts.UserAccountMaintenance;
using SOS.CalAccess.Data.FilerDisclosure.Filings;
using SOS.CalAccess.Data.FilerRegistration.Filers;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Models.UserAccountMaintenance;
using SOS.CalAccess.Services.Business.Authorization;
using SOS.CalAccess.Services.Business.FilerDisclosure.Contacts.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;
using SOS.CalAccess.Services.Business.Notifications;
using SOS.CalAccess.Services.Business.Notifications.Models;
using SOS.CalAccess.Services.Business.UserAccountMaintenance;
using SOS.CalAccess.Services.Common.BusinessRules;
using SOS.CalAccess.Services.Common.BusinessRules.Models;
using Address = SOS.CalAccess.Models.Common.Address;

namespace SOS.CalAccess.Services.Business.Tests.FilerDisclosure.Filings;

/// <summary>
/// Unit tests for the <see cref="FilingSvc"/> class.
/// </summary>
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
[Parallelizable(ParallelScope.All)]
[TestFixture]
[TestOf(typeof(FilingSvc))]
public sealed class FilingSvcTest
{
    private IFilingRepository _filingRepository;
    private IFilingPeriodRepository _filingPeriodRepository;
    private IFilingStatusRepository _filingStatusRepository;
    private IUserRepository _userRepository;
    private IFilingTypeRepository _filingTypeRepository;
    private IFilingRelatedFilerRepository _filingRelatedFilerRepository;
    private IFilingSummaryRepository _filingSummaryRepository;
    private IFilingSummaryStatusRepository _filingSummaryStatusRepository;
    private ITransactionRepository _transactionRepository;
    private IFilerUserRepository _filerUserRepository;
    private IFilerSvc _filerSvc;
    private ITransactionSvc _transactionSvc;
    private IDecisionsSvc _decisionsSvc;
    private IAuthorizationSvc _authorizationSvc;
    private INotificationSvc _notificationSvc;
    private IUserMaintenanceSvc _userMaintenanceSvc;
    private IActionsLobbiedSvc _actionsLobbiedSvc;
    private FilingSvcDependencies _dependencies;
    private FilingSvc _filingSvc;
    private IFilerContactRepository _filerContactRepository;
    private IAttestationRepository _attestationRepository;
    private IFilerRepository _filerRepository;
    private FilingSharedServicesDependencies _servicesDependencies;
    private IReferenceDataSvc _refDataSvc;
    private ILinkageSvc _linkageSvcMock;
    private ITransactionHelperSvc _transactionHelperSvc;
    private IRegistrationRepository _registrationRepository;
    private IFilingContactSummaryRepository _filingContactSummaryRepositoryMock;
    private IDateTimeSvc _dateTimeSvcMock;
    private DateTime _dateNow;

    private static readonly long _filingId = 123L;
    private static readonly long _statusId = FilingStatus.Submitted.Id;
    private static readonly bool _isMemberOfLobbyingCoalition = true;
    private static readonly Currency _totalPaymentsToInHouseLobbyists = (Currency)100m;
    private static readonly Currency _totalOverheadExpense = (Currency)100m;
    private static readonly Currency _totalUnderThresholdPayments = (Currency)1000m;
    private static readonly bool _diligenceStatementVerified = true;
    private static readonly LobbyistEmployerReport _lobbyistEmployerReport = new()
    {
        Id = _filingId,
        StatusId = _statusId,
        FilerId = 1,
        FilingPeriodId = 1,
        StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddMonths(-1),
        EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
        DiligenceStatementVerified = _diligenceStatementVerified,
        IsMemberOfLobbyingCoalition = _isMemberOfLobbyingCoalition,
        TotalPaymentsToInHouseLobbyists = _totalPaymentsToInHouseLobbyists,
        TotalOverheadExpense = _totalOverheadExpense,
        TotalUnderThresholdPayments = _totalUnderThresholdPayments,
        SubmittedDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
    };
    private static readonly string[] expected = new[] { "ext-1", "ext-2" };

    /// <summary>
    /// Sets up the unit tests for this fixture.
    /// </summary>
    [SetUp]
    public void SetUp()
    {
        _filingRepository = Substitute.For<IFilingRepository>();
        _filingPeriodRepository = Substitute.For<IFilingPeriodRepository>();
        _filingStatusRepository = Substitute.For<IFilingStatusRepository>();
        _filingTypeRepository = Substitute.For<IFilingTypeRepository>();
        _filingRelatedFilerRepository = Substitute.For<IFilingRelatedFilerRepository>();
        _filingSummaryRepository = Substitute.For<IFilingSummaryRepository>();
        _filingSummaryStatusRepository = Substitute.For<IFilingSummaryStatusRepository>();
        _transactionRepository = Substitute.For<ITransactionRepository>();
        _userRepository = Substitute.For<IUserRepository>();
        _filerUserRepository = Substitute.For<IFilerUserRepository>();
        _filerSvc = Substitute.For<IFilerSvc>();
        _transactionSvc = Substitute.For<ITransactionSvc>();
        _decisionsSvc = Substitute.For<IDecisionsSvc>();
        _authorizationSvc = Substitute.For<IAuthorizationSvc>();
        _notificationSvc = Substitute.For<INotificationSvc>();
        _userMaintenanceSvc = Substitute.For<IUserMaintenanceSvc>();
        _actionsLobbiedSvc = Substitute.For<IActionsLobbiedSvc>();
        _filerContactRepository = Substitute.For<IFilerContactRepository>();
        _filerRepository = Substitute.For<IFilerRepository>();
        _attestationRepository = Substitute.For<IAttestationRepository>();
        _refDataSvc = Substitute.For<IReferenceDataSvc>();
        _linkageSvcMock = Substitute.For<ILinkageSvc>();
        _transactionHelperSvc = Substitute.For<ITransactionHelperSvc>();
        _dateTimeSvcMock = Substitute.For<IDateTimeSvc>();
        _registrationRepository = Substitute.For<IRegistrationRepository>();
        _filingContactSummaryRepositoryMock = Substitute.For<IFilingContactSummaryRepository>();
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _dependencies = new FilingSvcDependencies(
            _filingRepository,
            _filingPeriodRepository,
            _filingStatusRepository,
            _filingTypeRepository,
            _filingRelatedFilerRepository,
            _filingSummaryRepository,
            _filingSummaryStatusRepository,
            _filerContactRepository,
            _attestationRepository,
            _filerRepository,
            _registrationRepository,
            _filingContactSummaryRepositoryMock
        );

        _servicesDependencies = new FilingSharedServicesDependencies(
            _filerSvc,
            _decisionsSvc,
            _authorizationSvc,
            _userMaintenanceSvc,
            _notificationSvc,
            _refDataSvc,
            _linkageSvcMock,
            _dateTimeSvcMock
        );

        _filingSvc = new FilingSvc(
            _dependencies,
            _servicesDependencies,
            _transactionRepository,
            _filerUserRepository,
            _actionsLobbiedSvc,
            _transactionHelperSvc,
            _dateTimeSvcMock,
            _filerContactRepository
        );
    }

    /// <summary>
    /// Test to ensure GetAllFilingPeriods should return valid Filings periods.
    /// </summary>
    [Test]
    public async Task GetAllFilingPeriods_ReturnsFilingPeriods_WhenFilingPeriodsExist()
    {
        // Arrange
        var mockFilingPeriods = new List<FilingPeriod>
        {
            new FilingPeriod
            {
                Id = 1,
                Name = "Quarter 1",
                Description = "Q1 Filing",
                StartDate = _dateNow.AddMonths(-3),
                EndDate = _dateNow,
                DueDate = _dateNow.AddMonths(1),
                ItemThreshold = 1000,
                ElectionCycleId = 1,
                CreatedBy = 1,
                ModifiedBy = 1,
            },
            new FilingPeriod
            {
                Id = 2,
                Name = "Quarter 2",
                Description = "Q2 Filing",
                StartDate = _dateNow,
                EndDate = _dateNow.AddMonths(3),
                DueDate = _dateNow.AddMonths(4),
                ItemThreshold = 1500,
                ElectionCycleId = 2,
                CreatedBy = 2,
                ModifiedBy = 2,
            }
        };

        _filingPeriodRepository.GetAll().Returns(mockFilingPeriods);

        // Act
        var result = await _filingSvc.GetAllFilingPeriods();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(mockFilingPeriods.Count));
            Assert.That(result.First().Id, Is.EqualTo(mockFilingPeriods[0].Id));
            Assert.That(result.Skip(1).First().Name, Is.EqualTo(mockFilingPeriods[1].Name));
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilings should return valid Filings list for a specific filer.
    /// </summary>
    [Test]
    public async Task GetAllFilings_ReturnsFilings_ForGivenFilerId()
    {
        // Arrange
        var filerId = 123L;
        var expectedFilings = new List<Filing>
        {
            new Filing { Id = 1, FilerId = filerId, FilingPeriodId = 1, FilingTypeId = 1, StatusId = 1 },
            new Filing { Id = 2, FilerId = filerId, FilingPeriodId = 1, FilingTypeId = 2, StatusId = 2 }
        };

        _filingRepository.GetAllByFilerId(filerId).Returns(expectedFilings);

        // Act
        var result = await _filingSvc.GetAllFilings(filerId);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result.Count(), Is.EqualTo(expectedFilings.Count));
            Assert.That(result.First().FilerId, Is.EqualTo(filerId));
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilingStatuses should return a valid FilingStatus list.
    /// </summary>
    [Test]
    public async Task GetAllFilingStatuses_ReturnsAllStatuses()
    {
        // Arrange
        var expectedStatuses = new List<FilingStatus>
        {
            FilingStatus.Draft,
            FilingStatus.Submitted,
            FilingStatus.Accepted,
            FilingStatus.Cancelled,
            FilingStatus.Incomplete,
            FilingStatus.Pending
        };
        _filingStatusRepository.GetAll().Returns(expectedStatuses);

        // Act
        var result = await _filingSvc.GetAllFilingStatuses();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(6));
            Assert.That(result.First(), Is.EqualTo(FilingStatus.Draft));
            Assert.That(result.Skip(1).First(), Is.EqualTo(FilingStatus.Submitted));
            Assert.That(result.Skip(2).First(), Is.EqualTo(FilingStatus.Accepted));
            Assert.That(result.Skip(3).First(), Is.EqualTo(FilingStatus.Cancelled));
            Assert.That(result.Skip(4).First(), Is.EqualTo(FilingStatus.Incomplete));
            Assert.That(result.Skip(5).First(), Is.EqualTo(FilingStatus.Pending));
        });
    }


    /// <summary>
    /// Test to ensure GetLobbyistEmployerReport returns a report when it exists.
    /// </summary>
    [Test]
    public async Task GetLobbyistEmployerReport_ShouldReturnReport_WhenReportExists()
    {
        // Arrange
        _filingRepository.FindById(_filingId).Returns(_lobbyistEmployerReport);

        // Act
        var result = await _filingSvc.GetLobbyistEmployerReport(_filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(_lobbyistEmployerReport.Id));
    }

    /// <summary>
    /// Test to ensure GetLobbyistEmployerReport returns null when the report does not exist.
    /// </summary>
    [Test]
    public async Task GetLobbyistEmployerReport_ShouldReturnNull_WhenReportDoesNotExist()
    {
        // Arrange
        long filingId = 123;

        _filingRepository.FindById(filingId).Returns((Filing)null);

        // Act
        var result = await _filingSvc.GetLobbyistEmployerReport(filingId);

        // Assert
        Assert.That(result, Is.Null);
    }

    /// <summary>
    /// Test to ensure UpdateLobbyistEmployerReport updates the report when it exists.
    /// </summary>
    [Test]
    public async Task UpdateLobbyistEmployerReport_ShouldUpdateReport_WhenReportExists()
    {
        // Arrange
        _filingRepository.FindById(_filingId).Returns(_lobbyistEmployerReport);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerReport(_filingId, _totalPaymentsToInHouseLobbyists);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.TotalPaymentsToInHouseLobbyists, Is.EqualTo(_totalPaymentsToInHouseLobbyists));
        await _filingRepository.Received(1).Update(_lobbyistEmployerReport);
    }

    /// <summary>
    /// Test to ensure UpdateLobbyistEmployerReport returns null when the report does not exist.
    /// </summary>
    [Test]
    public async Task UpdateLobbyistEmployerReport_ShouldReturnNull_WhenReportDoesNotExist()
    {
        // Arrange
        long filingId = 123;
        decimal totalPayments = 1000m;

        var filing = new Filing
        {
            Id = 12,
            StatusId = FilingStatus.Draft.Id
        };

        _filingRepository.FindById(filingId).Returns(filing);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerReport(filingId, totalPayments);

        // Assert
        Assert.That(result, Is.Null);
    }

    /// <summary>
    /// Test to ensure UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition updates the report when it exists.
    /// </summary>
    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ShouldUpdateReport_WhenReportExists()
    {
        // Arrange
        _filingRepository.FindById(_filingId).Returns(_lobbyistEmployerReport);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(_filingId, _isMemberOfLobbyingCoalition);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result?.IsMemberOfLobbyingCoalition, Is.EqualTo(true));
        });
        await _filingRepository.Received(1).Update(_lobbyistEmployerReport);
    }

    /// <summary>
    /// Test to ensure UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition returns null when the report does not exist.
    /// </summary>
    [Test]
    public async Task UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition_ShouldReturnNull_WhenReportDoesNotExist()
    {
        // Arrange
        long filingId = 123;
        bool isMemberOfLobbyingCoalition = true;

        _filingRepository.FindById(filingId).Returns(null as Filing);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(filingId, isMemberOfLobbyingCoalition);

        // Assert
        Assert.That(result, Is.Null);
    }

    /// <summary>
    /// Test to ensure SubmitLobbyistEmployerReport returns valid response when report exists.
    /// </summary>
    [Test]
    public async Task SubmitLobbyistEmployerReport_ShouldReturnValidResponse_WhenReportExists()
    {
        // Arrange
        var diligenceVerified = true;
        long filingId = 123;
        long filerId = 1;
        long filingPeriodId = 1;
        var report = new LobbyistEmployerReport
        {
            Id = filingId,
            StatusId = FilingStatus.Accepted.Id,
            FilerId = filerId,
            FilingPeriodId = filingPeriodId,
            StartDate = _dateNow.AddMonths(-1),
            EndDate = _dateNow,
            DiligenceStatementVerified = diligenceVerified,
            SubmittedDate = _dateNow
        };
        _filingRepository.FindById(filingId).Returns(report);

        var mockMajorDonor = new DecisionsMajorDonorSelectionLobbyistEmployerReport
        {
            IsContributionContainedInDisclosure = true,
            FiledByDonorOrCommittee = string.Empty,
            DonorOrCommittee = string.Empty
        };

        GetMockLobbyistEmployerTransactions();

        var expectedErrors = new List<WorkFlowError>()
        {
        };
        var decisionResponse = new DecisionsSubmitLobbyistEmployerReportResponse { Result = expectedErrors };

        _servicesDependencies.DecisionsSvc
            .InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
                DecisionsWorkflow.SubmitLobbyistEmployerReportRuleset,
                Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
                true)
            .Returns(Task.FromResult(decisionResponse));

        // Act
        var result = await _filingSvc.SubmitLobbyistEmployerReport(filingId, diligenceVerified);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Valid, Is.True);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.DiligenceStatementVerified, Is.EqualTo(diligenceVerified));
            Assert.That(result.SubmittedDate, Is.Not.Null);
            Assert.That(mockMajorDonor, Is.Not.Null);
        });
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_ShouldUpdateFuturePendingReports_WhenTheyExist()
    {
        // Arrange
        var diligenceVerified = true;
        long filingId = 123;
        long filerId = 1;
        long filingPeriodId = 1;

        var now = _dateNow;
        var startDate = now.AddMonths(-1);
        var endDate = now;

        var currentReport = new LobbyistEmployerReport
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id,
            FilerId = filerId,
            FilingPeriodId = filingPeriodId,
            StartDate = startDate,
            EndDate = endDate,
            DiligenceStatementVerified = null,
            FilingSummaries = new List<FilingSummary>
            {
                new ()
                {
                    FilingSummaryTypeId = 1,
                    PeriodAmount = 100,
                    ToDateAmount = 100
                }
            }
        };

        var futureReport = new LobbyistEmployerReport
        {
            Id = 999,
            StatusId = FilingStatus.Draft.Id,
            FilerId = filerId,
            FilingPeriodId = filingPeriodId,
            StartDate = endDate.AddDays(1),
            EndDate = endDate.AddMonths(1),
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            FilingSummaries = new List<FilingSummary>
            {
                new ()
                {
                    FilingSummaryTypeId = 1,
                    PeriodAmount = 0,
                    ToDateAmount = 50
                }
            }
        };

        _filingRepository.FindById(filingId).Returns(currentReport);
        _filingRepository.GetAllByFilerId(filerId)
            .Returns(new List<LobbyistEmployerReport> { currentReport, futureReport });

        GetMockLobbyistEmployerTransactions();

        var expectedErrors = new List<WorkFlowError>()
        {
        };

        var decisionResponse = new DecisionsSubmitLobbyistEmployerReportResponse([])
        {
            Result = expectedErrors,
            Errors = new List<WorkFlowError>(),
            ListOfNotifications = new List<NotificationTrigger>(),
            Status = "Accepted"
        };

        _servicesDependencies.DecisionsSvc
            .InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
                DecisionsWorkflow.SubmitLobbyistEmployerReportRuleset,
                Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
                true)
            .Returns(Task.FromResult(decisionResponse));

        // Act
        var result = await _filingSvc.SubmitLobbyistEmployerReport(filingId, diligenceVerified);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(futureReport.FilingSummaries.First().ToDateAmount, Is.EqualTo(150));
        });
        await _filingRepository.Received().Update(futureReport);
    }

    /// <summary>
    /// Test to ensure SubmitLobbyistReport returns valid response when report exists.
    /// </summary>
    [Test]
    public async Task SubmitLobbyistReport_ShouldReturnValidResponse_WhenReportExists()
    {
        // Arrange
        var filingId = 123;
        var diligenceStatementVerified = true;
        var executedDate = _dateNow;

        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = 1,
            FilingPeriod = new FilingPeriod { EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified) },
            StatusId = FilingStatus.Pending.Id,
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
            StartDate = new DateTime(2024, 12, 01, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        var activityExpenses = new List<ActivityExpense>
        {
            new() {
                Id = 1,
                TransactionDate = new DateTime(2024, 10, 1, 0, 0, 0, 0),
                Notes = "Expense for event hosting",
                ActivityExpenseTypeId = 101,
                CreditCardCompanyName = "Visa",
                ActivityDescription = "Catering for lobby event",
                MonetaryTypeId = MonetaryType.Credit.Id,
                Amount = new Currency(1200.00m),
                FilerId = 501,
                ContactId = 601,
                TransactionReportablePersons = new List<TransactionReportablePerson>
                {
                    new() {
                        TransactionId = 1,
                        FilerContactId = 2002,
                        Name = "John Public",
                        OfficialPosition = "Legislative Analyst",
                        OfficialPositionDescription = "Analyzes legislation",
                        Agency = "State Assembly"
                    }
                },
                Contact = new IndividualContact()
                {
                    Id = 1001,
                    FilerId = 2001,
                    CreatedBy = 1,
                    ModifiedBy = 1,
                    Employer = "Tech Corp",
                    Occupation = "Engineer",
                    FirstName = "Alice",
                    MiddleName = "B.",
                    LastName = "Smith",
                    AddressListId = 3001,
                    PhoneNumberListId = 4001,
                    EmailAddressListId = 5001,
                    ContactFilerId = 6001,
                    FilerContactType = FilerContactType.Individual,
                    AddressList = new AddressList
                    {
                        Addresses = new List<Address>
                        {
                            new()
                            {
                                Street = "street",
                                Street2 = "street2",
                                City = "city",
                                State = "CA",
                                Zip = "12345",
                                Country = "US",
                                Type = "home",
                                Purpose = "purpose"
                            }
                        }
                    }
                }
            }
        };

        var campaignContributions = new List<LobbyingCampaignContribution>
        {
            new() {
                TransactionDate = new DateTime(2024, 11, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                Amount = new Currency(2500.00m),
                ContributorFilerId = 701,
                FilerId = 801,
                RecipientFilerId = 901,
                SeparateAccountName = "General Fund"
            }
        };

        _filingRepository.FindById(filingId).Returns(lobbyistReport);

        _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId).Returns(activityExpenses);
        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId).Returns(campaignContributions);

        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
            DecisionsWorkflow.SubmitLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(new DecisionsSubmitLobbyistReportResponse([])
        {
            Result = [],
            IsLateFiling = false
        });
        // Act
        var result = await _filingSvc.SubmitLobbyistReport(filingId, diligenceStatementVerified);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Valid, Is.True);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.DiligenceStatementVerified, Is.EqualTo(diligenceStatementVerified));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }
    /// <summary>
    /// Test to ensure SubmitLobbyistEmployerReport returns null when the report does not exist.
    /// </summary>
    [Test]
    public async Task SubmitLobbyistEmployerReport_ShouldReturnNull_WhenReportDoesNotExist()
    {
        // Arrange
        long filingId = 123;

        _filingRepository.FindById(filingId).Returns(null as Filing);

        // Act
        var result = await _filingSvc.SubmitLobbyistEmployerReport(filingId, true);

        // Assert
        Assert.That(result, Is.Null);
    }

    /// <summary>
    /// Test to ensure SubmitLobbyistReport returns null when the report does not exist.
    /// </summary>
    [Test]
    public async Task SubmitLobbyistReport_ShouldReturnNull_WhenReportDoesNotExist()
    {
        // Arrange
        long filingId = 123;

        _filingRepository.FindById(filingId).Returns(null as Filing);

        // Act
        var result = await _filingSvc.SubmitLobbyistReport(filingId, true);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task SubmitLobbyistReport_ShouldHandleOrganizationContactCorrectly()
    {
        // Arrange
        long filingId = 456;
        var filingPeriodId = 1;

        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id,
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
            StartDate = new DateTime(2024, 12, 01, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        var filingPeriod = new FilingPeriod { EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified) };

        var activityExpenses = new List<ActivityExpense>
    {
        new()
        {
            Id = 2,
            TransactionDate = _dateNow.AddDays(-10),
            Notes = "Event Sponsorship",
            ActivityExpenseTypeId = 200,
            CreditCardCompanyName = "Mastercard",
            ActivityDescription = "Sponsored community event",
            MonetaryTypeId = MonetaryType.Credit.Id,
            Amount = new Currency(1500),
            FilerId = 800,
            ContactId = 900,
            TransactionReportablePersons = new List<TransactionReportablePerson>
            {
                new()
                {
                    TransactionId = 2,
                    FilerContactId = 2010,
                    Name = "Jane Agency",
                    OfficialPosition = "Director",
                    OfficialPositionDescription = "Agency Head",
                    Agency = "City Government"
                }
            },
            Contact = new OrganizationContact
            {
                Id = 900,
                FilerId = 800,
                OrganizationName = "Helping Hands Org",
                FilerContactType = FilerContactType.Organization,
                AddressList = new AddressList
                    {
                        Addresses = new List<Address>
                        {
                            new()
                            {
                                Street = "street",
                                City = "city",
                                State = "CA",
                                Zip = "12345",
                                Country = "US",
                                Type = "home",
                                Purpose = "purpose"
                            }
                        }
                    }
            }
        }
    };

        var campaignContributions = new List<LobbyingCampaignContribution>
    {
        new()
        {
            TransactionDate = _dateNow.AddDays(-5),
            Amount = new Currency(2000),
            FilerId = 800,
            ContributorFilerId = 1000,
            RecipientFilerId = 1100
        }
    };

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _filingPeriodRepository.FindById(filingPeriodId).Returns(filingPeriod);
        _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId).Returns(activityExpenses);
        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId).Returns(campaignContributions);

        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
            DecisionsWorkflow.SubmitLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(new DecisionsSubmitLobbyistReportResponse([])
        {
            IsLateFiling = false
        });

        // Act
        var result = await _filingSvc.SubmitLobbyistReport(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Valid, Is.True);
        Assert.Multiple(() =>
        {
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.DiligenceStatementVerified, Is.EqualTo(true));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }

    [Test]
    public async Task SubmitLobbyistReport_ShouldReturnInvalidAndUpdateStatusToIncomplete_WhenDecisionResponseHasNonFatalErrors()
    {
        // Arrange
        long filingId = 123;
        var filingPeriodId = 1;

        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Pending.Id
        };

        var filingPeriod = new FilingPeriod { EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0) };

        var error = new WorkFlowError("", "", "", "");

        var activityExpenses = new List<ActivityExpense> { new() {
            Id = 1,
            TransactionDate = _dateNow,
            Notes = "Note",
            ActivityExpenseTypeId = 1,
            CreditCardCompanyName = "Visa",
            ActivityDescription = "Desc",
            MonetaryTypeId = MonetaryType.Credit.Id,
            Amount = new Currency(10.00m),
            FilerId = 1,
            ContactId = 1,
            TransactionReportablePersons = new List<TransactionReportablePerson>
            {
                new() { TransactionId = 1, FilerContactId = 2, Name = "Rep", Agency = "Gov", OfficialPosition = "Senator" }
            },
            Contact = new IndividualContact()
                    {
                        Id = 1001,
                        FilerId = 2001,
                        CreatedBy = 1,
                        ModifiedBy = 1,
                        Employer = "Tech Corp",
                        Occupation = "Engineer",
                        FirstName = "Alice",
                        MiddleName = "B.",
                        LastName = "Smith",
                        AddressListId = 3001,
                        PhoneNumberListId = 4001,
                        EmailAddressListId = 5001,
                        ContactFilerId = 6001,
                        FilerContactType = FilerContactType.Individual,
                        AddressList = new AddressList
                        {
                            Addresses = new List<Address>
                            {
                                new()
                                {
                                    Street = "street",
                                    Street2 = "street2",
                                    City = "city",
                                    State = "CA",
                                    Zip = "12345",
                                    Country = "US",
                                    Type = "home",
                                    Purpose = "purpose"
                                }
                            }
                        }
                    }
        }};

        var campaignContributions = new List<LobbyingCampaignContribution> { new() {
            TransactionDate = _dateNow,
            Amount = new Currency(100),
            FilerId = 1,
            ContributorFilerId = 2,
            RecipientFilerId = 3
        }};

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _filingPeriodRepository.FindById(filingPeriodId).Returns(filingPeriod); _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId).Returns(activityExpenses);
        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId).Returns(campaignContributions);

        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
            DecisionsWorkflow.SubmitLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(new DecisionsSubmitLobbyistReportResponse([error])
        {
            IsLateFiling = false
        });

        // Act
        var result = await _filingSvc.SubmitLobbyistReport(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(2));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Incomplete.Id));
        });
    }

    [Test]
    public async Task SubmitLobbyistReport_ShouldReturnInvalidAndNotUpdateFilingStatus_WhenDecisionResponseHasFatalErrors()
    {
        // Arrange
        long filingId = 123;
        var filingPeriodId = 1;

        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id
        };

        var filingPeriod = new FilingPeriod { EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0) };

        var error = new WorkFlowError("", "", "Fatal", "");

        var activityExpenses = new List<ActivityExpense> { new() {
            Id = 1,
            TransactionDate = _dateNow,
            Notes = "Note",
            ActivityExpenseTypeId = 1,
            CreditCardCompanyName = "Visa",
            ActivityDescription = "Desc",
            MonetaryTypeId = MonetaryType.Credit.Id,
            Amount = new Currency(10.00m),
            FilerId = 1,
            ContactId = 1,
            TransactionReportablePersons = new List<TransactionReportablePerson>
            {
                new() { TransactionId = 1, FilerContactId = 2, Name = "Rep", Agency = "Gov", OfficialPosition = "Senator" }
            },
            Contact = new IndividualContact()
                    {
                        Id = 1001,
                        FilerId = 2001,
                        CreatedBy = 1,
                        ModifiedBy = 1,
                        Employer = "Tech Corp",
                        Occupation = "Engineer",
                        FirstName = "Alice",
                        MiddleName = "B.",
                        LastName = "Smith",
                        AddressListId = 3001,
                        PhoneNumberListId = 4001,
                        EmailAddressListId = 5001,
                        ContactFilerId = 6001,
                        FilerContactType = FilerContactType.Individual,
                        AddressList = new AddressList
                        {
                            Addresses = new List<Address>
                            {
                                new()
                                {
                                    Street = "street",
                                    Street2 = "street2",
                                    City = "city",
                                    State = "CA",
                                    Zip = "12345",
                                    Country = "US",
                                    Type = "home",
                                    Purpose = "purpose"
                                }
                            }
                        }
                    }
        }};

        var campaignContributions = new List<LobbyingCampaignContribution> { new() {
            TransactionDate = _dateNow,
            Amount = new Currency(100),
            FilerId = 1,
            ContributorFilerId = 2,
            RecipientFilerId = 3
        }};

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _filingPeriodRepository.FindById(filingPeriodId).Returns(filingPeriod); _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId).Returns(activityExpenses);
        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId).Returns(campaignContributions);

        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
            DecisionsWorkflow.SubmitLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(new DecisionsSubmitLobbyistReportResponse([error])
        {
            IsLateFiling = false
        });

        // Act
        var result = await _filingSvc.SubmitLobbyistReport(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(2));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
        });
    }

    [Test]
    public void PopulateActivityExpenseDecisionServicePayee_ShouldThrow_WhenContactIsNull()
    {
        // Arrange
        FilerContact? contact = null;

        // Act & Assert
        var exception = Assert.Throws<ArgumentNullException>(() =>
            FilingSvc.PopulateActivityExpenseDecisionServicePayee(contact));

        Assert.That(exception, Is.Not.Null);
        Assert.That(exception.ParamName, Is.EqualTo("contact"));
    }

    [Test]
    public void PopulateActivityExpenseDecisionServicePayee_ShouldReturnContact_ForIndividualContactType()
    {
        // Arrange
        var contact = new IndividualContact()
        {
            FirstName = "name",
            MiddleName = "name",
            LastName = "name",
            Occupation = "occupation",
            Employer = "employer",
            AddressList = new AddressList
            {
                Addresses = new List<Address>()
                {
                    new()
                    {
                        Street = "street",
                        City = "city",
                        State = "CA",
                        Zip = "12345",
                        Country = "US",
                        Type = "home",
                        Purpose = "purpose"
                    }
                }
            }
        };

        // Act
        var result = FilingSvc.PopulateActivityExpenseDecisionServicePayee(contact);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FirstName, Is.Not.Null);
            Assert.That(result.LastName, Is.Not.Null);
            Assert.That(result, Is.TypeOf<DecisionsSubmitActivityExpensePayee>());
        });
    }

    [Test]
    public void PopulateActivityExpenseDecisionServicePayee_ShouldReturnContact_ForOrganizationContactType()
    {
        // Arrange
        var contact = new OrganizationContact()
        {
            OrganizationName = "name",
            AddressList = new AddressList
            {
                Addresses = new List<Address>()
                {
                    new()
                    {
                        Street = "street",
                        City = "city",
                        State = "CA",
                        Zip = "12345",
                        Country = "US",
                        Type = "home",
                        Purpose = "purpose"
                    }
                }
            }
        };

        // Act
        var result = FilingSvc.PopulateActivityExpenseDecisionServicePayee(contact);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.OrganizationName, Is.Not.Null);
            Assert.That(result, Is.TypeOf<DecisionsSubmitActivityExpensePayee>());
        });
    }

    [Test]
    public void PopulateActivityExpenseDecisionServicePayee_ShouldReturnNull_ForUnsupportedFilerContactType()
    {
        // Arrange
        var contact = new CommitteeContact()
        {
            CommitteeName = "name",
            CommitteeType = "type",
            AddressList = new AddressList
            {
                Addresses = new List<Address>()
                {
                    new()
                    {
                        Street = "street",
                        City = "city",
                        State = "CA",
                        Zip = "12345",
                        Country = "US",
                        Type = "home",
                        Purpose = "purpose"
                    }
                }
            }
        };

        // Act
        var result = FilingSvc.PopulateActivityExpenseDecisionServicePayee(contact);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public void PopulateActivityExpenseDecisionServicePayee_ShouldHandleNullAddress()
    {
        // Arrange
        var contact = new IndividualContact
        {
            FirstName = "John",
            MiddleName = "Q",
            LastName = "Doe",
            Employer = "employer",
            Occupation = "occupation",
            AddressList = null
        };

        // Act
        var result = FilingSvc.PopulateActivityExpenseDecisionServicePayee(contact);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FirstName, Is.EqualTo("John"));
            Assert.That(result.MiddleName, Is.EqualTo("Q"));
            Assert.That(result.LastName, Is.EqualTo("Doe"));
            Assert.That(result.ContactType, Is.EqualTo(FilerContactType.Individual.Name));
            Assert.That(result.Address1, Is.Not.Null);
            Assert.That(result.Address1?.Street, Is.Null);
            Assert.That(result.Address1?.City, Is.Null);
            Assert.That(result.Address1?.Country, Is.Null);
            Assert.That(result.Address1?.State, Is.Null);
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerCampaignContributions_ReturnsReport_WhenFilingExists()
    {
        // Arrange
        _filingRepository.FindById(_filingId).Returns(_lobbyistEmployerReport);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerCampaignContributions(_filingId, true, 1);

        // Assert
        Assert.That(result, Is.Not.Null);
        await _filingRelatedFilerRepository.Received(1).AddFilingRelatedFiler(_filingId, 1);
        await _filingRepository.Received(1).Update(_lobbyistEmployerReport);
    }

    [Test]
    public async Task UpdateLobbyistEmployerCampaignContributions_DeletesRelatedFilers_WhenContributionsInExistingStatementsIsFalse()
    {
        // Arrange
        _filingRepository.FindById(_filingId).Returns(_lobbyistEmployerReport);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerCampaignContributions(_filingId, false, null);

        // Assert
        Assert.That(result, Is.Not.Null);
        await _filingRelatedFilerRepository.Received(1).DeleteAllFilingRelatedFilerforFiling(_filingId);
        await _filingRepository.Received(1).Update(_lobbyistEmployerReport);
    }

    [Test]
    public async Task UpdateLobbyistEmployerCampaignContributions_ReturnsNull_WhenFilingDoesNotExist()
    {
        // Arrange
        var filingId = 1L;
        _filingRepository.FindById(filingId).Returns(null as LobbyistEmployerReport);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerCampaignContributions(_filingId, true, 123L);

        // Assert
        Assert.That(result, Is.Null);
        await _filingRelatedFilerRepository.DidNotReceive().AddFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());
        await _filingRepository.DidNotReceive().Update(Arg.Any<LobbyistEmployerReport>());
    }

    //[Test]
    //public async Task CreateLobbyistReport_ExistingReportWithNoSummaries_AddsSummaries()
    //{
    //    // Arrange
    //    var filerId = 1L;
    //    var filingPeriodId = 2L;
    //    var startDate = new DateTime(2023, 3, 5, 0, 0, 0, DateTimeKind.Utc);
    //    var endDate = new DateTime(2023, 5, 6, 0, 0, 0, DateTimeKind.Utc);

    //    var filingPeriod = new FilingPeriod
    //    {
    //        Id = filingPeriodId,
    //        StartDate = startDate,
    //        EndDate = endDate
    //    };

    //    var filingSummaryStatuses = new List<FilingSummaryStatus>
    //    {
    //        new(1, "Not Started")
    //    };

    //    var existingReport = new LobbyistReport
    //    {
    //        FilerId = filerId,
    //        FilingTypeId = FilingType.LobbyistReport.Id,
    //        StatusId = FilingStatus.Draft.Id,
    //        FilingPeriodId = filingPeriodId,
    //        StartDate = startDate,
    //        EndDate = endDate,
    //        FilingSummaries = new List<FilingSummary>()
    //    };

    //    var previousReports = new List<Filing>
    //    {
    //        new LobbyistReport
    //        {
    //            FilerId = filerId,
    //            StatusId = FilingStatus.Accepted.Id,
    //            FilingSummaries = new List<FilingSummary>
    //            {
    //                new() { FilingSummaryTypeId = FilingSummaryType.CampaignContributionSummary.Id, PeriodAmount = 100, ToDateAmount = 100 },
    //                new() { FilingSummaryTypeId = FilingSummaryType.ActivityExpenseSummary.Id, PeriodAmount = 200, ToDateAmount = 200 }
    //            },
    //            StartDate = startDate.AddMonths(-1),
    //            EndDate = startDate.AddDays(-1)
    //        }
    //    };

    //    _filingRepository.GetAllByFilerId(filerId)
    //        .Returns([.. previousReports, .. new[] { existingReport }]);

    //    _filingPeriodRepository.FindById(filingPeriodId)
    //        .Returns(filingPeriod);

    //    _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
    //        .Returns(filingSummaryStatuses);

    //    _filingRepository.Update(Arg.Any<Filing>())!
    //        .Returns(args => args[0] as Filing);

    //    // Act
    //    var result = await _filingSvc.CreateLobbyistReport(filerId, filingPeriodId);

    //    // Assert
    //    Assert.Multiple(() =>
    //    {
    //        Assert.That(result, Is.Not.Null, "Result should not be null");
    //        Assert.That(result?.FilingSummaries, Is.Not.Null, "Summaries should not be null");
    //        Assert.That(result?.FilingSummaries, Has.Count.EqualTo(2), "Should have 2 summary types");

    //        var contributionSummary = result?.FilingSummaries.FirstOrDefault(fs =>
    //            fs.FilingSummaryTypeId == FilingSummaryType.CampaignContributionSummary.Id);
    //        var expenseSummary = result?.FilingSummaries.FirstOrDefault(fs =>
    //            fs.FilingSummaryTypeId == FilingSummaryType.ActivityExpenseSummary.Id);

    //        Assert.That(contributionSummary, Is.Not.Null, "Contribution summary should exist");
    //        Assert.That(expenseSummary, Is.Not.Null, "Expense summary should exist");

    //        Assert.That(contributionSummary?.ToDateAmount, Is.EqualTo(100), "Contribution to-date amount should match");
    //        Assert.That(expenseSummary?.ToDateAmount, Is.EqualTo(200), "Expense to-date amount should match");
    //    });

    //    await _filingRepository.Received(1).Update(Arg.Is<Filing>(f =>
    //        f.FilingSummaries.Count == 2));
    //}

    [Test]
    public async Task CreateLobbyistEmployerReport_ExistingReportWithNoSummaries_AddsSummaries()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriodId = 2L;
        var startDate = new DateTime(2023, 3, 5, 0, 0, 0, 0);
        var endDate = new DateTime(2023, 5, 6, 0, 0, 0, 0);

        var filingPeriod = new FilingPeriod
        {
            Id = filingPeriodId,
            StartDate = startDate,
            EndDate = endDate
        };

        var filingSummaryStatuses = new List<FilingSummaryStatus>
        {
            new(1, "Not Started")
        };

        var summaryTypes = new[]
        {
            FilingSummaryType.ActionsLobbiedSummary.Id,
            FilingSummaryType.CampaignContributionSummary.Id,
            FilingSummaryType.ActivityExpenseSummary.Id,
            FilingSummaryType.ToLobbyingCoalitionSummary.Id,
            FilingSummaryType.RecieveLobbyingCoalitionSummary.Id,
            FilingSummaryType.MadeToLobbyingFirmsSummary.Id,
            FilingSummaryType.LobbyistReportsSummary.Id,
            FilingSummaryType.PucActivitySummary.Id,
            FilingSummaryType.OtherPaymentsToInfluenceSummary.Id,
            FilingSummaryType.PaymentsToInHouseLobbyists.Id
        };

        var previousSubmittedReport = new LobbyistEmployerReport
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            FilingPeriodId = filingPeriodId,
            StartDate = startDate.AddDays(-1),
            EndDate = endDate.AddDays(-1),
            FilingSummaries = summaryTypes.Select(id => new FilingSummary
            {
                FilingSummaryTypeId = id,
                PeriodAmount = id * 10,
                ToDateAmount = id * 10
            }).ToList()
        };

        var existingEmptyReport = new LobbyistEmployerReport
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StatusId = FilingStatus.Draft.Id,
            FilingPeriodId = filingPeriodId,
            StartDate = startDate,
            EndDate = endDate,
            FilingSummaries = new List<FilingSummary>()
        };

        _filingRepository.GetAllByFilerId(filerId)
            .Returns([previousSubmittedReport, existingEmptyReport]);

        _filingPeriodRepository.FindById(filingPeriodId)
            .Returns(filingPeriod);

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
            .Returns(filingSummaryStatuses);

        _filingRepository.Update(Arg.Any<Filing>())!
            .Returns(args => args[0] as Filing);

        // Act
        var result = await _filingSvc.CreateLobbyistEmployerReport(filerId, filingPeriodId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null");
            Assert.That(result?.FilingSummaries, Is.Not.Null, "Filing summaries should not be null");
            Assert.That(result?.FilingSummaries, Has.Count.EqualTo(10), "Should have 10 summary types");

            foreach (var summaryType in summaryTypes)
            {
                var summary = result?.FilingSummaries.FirstOrDefault(s => s.FilingSummaryTypeId == summaryType);
                Assert.That(summary, Is.Not.Null, $"Summary for type {summaryType} should exist");
                Assert.That(summary?.ToDateAmount, Is.EqualTo(summaryType * 10), $"ToDateAmount for type {summaryType} should match");
                Assert.That(summary?.PeriodAmount, Is.EqualTo(0), $"PeriodAmount for type {summaryType} should be 0");
                Assert.That(summary?.FilingSummaryStatusId, Is.EqualTo(1), "Status ID should be 'Not Started'");
            }
        });

        await _filingRepository.Received(1).Update(Arg.Is<Filing>(f =>
            f.FilingSummaries.Count == 10));
    }



    [Test]
    public async Task CreateLobbyistEmployerReport_UpdatesExistingDraftReport()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriodId = 2L;
        var startDate = new DateTime(2023, 3, 5, 0, 0, 0, 0);
        var endDate = new DateTime(2023, 5, 6, 0, 0, 0, 0);

        var filingPeriod = new FilingPeriod
        {
            Id = filingPeriodId,
            StartDate = startDate,
            EndDate = endDate
        };

        var filingSummaryStatuses = new List<FilingSummaryStatus>
        {
            new(1, "Not Started")
        };

        var existingReport = new LobbyistEmployerReport
        {
            Id = 123,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StatusId = FilingStatus.Draft.Id,
            FilingPeriodId = filingPeriodId,
            StartDate = startDate,
            EndDate = endDate,
            FilingSummaries = new List<FilingSummary>
            {
                new()
                {
                    FilingSummaryTypeId = FilingSummaryType.ActivityExpenseSummary.Id,
                    PeriodAmount = 100,
                    ToDateAmount = 500
                }
            }
        };

        var previousAcceptedReport = new LobbyistEmployerReport
        {
            Id = 100,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            FilingPeriodId = 1,
            StartDate = startDate.AddMonths(-3),
            EndDate = startDate.AddDays(-1),
            FilingSummaries = new List<FilingSummary>
            {
                new() { FilingSummaryTypeId = FilingSummaryType.ActivityExpenseSummary.Id, PeriodAmount = 200, ToDateAmount = 200 },
                new() { FilingSummaryTypeId = FilingSummaryType.CampaignContributionSummary.Id, PeriodAmount = 300, ToDateAmount = 300 }
            }
        };

        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing> { existingReport, previousAcceptedReport });
        _ = _filingPeriodRepository.FindById(filingPeriodId).Returns(filingPeriod);
        _ = _filingSummaryStatusRepository.GetAllFilingSummaryStatuses().Returns(filingSummaryStatuses);

        // Act
        var result = await _filingSvc.CreateLobbyistEmployerReport(filerId, filingPeriodId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null");
            Assert.That(result.Id, Is.EqualTo(existingReport.Id), "Should return existing report");
            Assert.That(result.FilingSummaries, Has.Count.EqualTo(1), "Should keep existing summaries");

            var summary = result.FilingSummaries.First();
            Assert.That(summary.FilingSummaryTypeId, Is.EqualTo(FilingSummaryType.ActivityExpenseSummary.Id));
            Assert.That(summary.PeriodAmount, Is.EqualTo(100));
            Assert.That(summary.ToDateAmount, Is.EqualTo(500));
        });

        // Should not update or create since existing report with summaries exists
        _ = await _filingRepository.DidNotReceive().Create(Arg.Any<Filing>());
        _ = await _filingRepository.DidNotReceive().Update(Arg.Any<Filing>());
    }


    [Test]
    public void GetFilingSummaryTypeForTransactionType_ActivityExpense_ReturnsActivityExpenseSummary()
    {
        // Act
        var result = FilingSvc.GetFilingSummaryTypeForTransactionType(TransactionType.ActivityExpense.Id);

        // Assert
        Assert.That(result, Is.EqualTo(FilingSummaryType.ActivityExpenseSummary.Id));
    }

    [Test]
    public void GetFilingSummaryTypeForTransactionType_UnknownType_ReturnsNull()
    {
        // Act
        var result = FilingSvc.GetFilingSummaryTypeForTransactionType(-1);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task OnTransactionCreated_WithNullFilingId_DoesNothing()
    {
        // Arrange
        var transaction = new LobbyingCampaignContribution { Amount = (Currency)100 };

        // Act
        await _filingSvc.OnTransactionCreated(transaction, null);

        // Assert
        await _filingSummaryRepository.DidNotReceive().GetAllByFilingId(Arg.Any<long>());
    }

    [Test]
    public async Task OnTransactionCreated_WithValidTransaction_UpdatesSummary()
    {
        // Arrange
        var transaction = new ActivityExpense { Amount = (Currency)100 };
        var filingId = 1L;
        var existingSummary = new FilingSummary
        {
            FilingSummaryTypeId = FilingSummaryType.ActivityExpenseSummary.Id,
            PeriodAmount = 50,
            ToDateAmount = 50
        };

        _filingSummaryRepository.GetAllByFilingId(filingId)
            .Returns(new List<FilingSummary> { existingSummary });

        // Act
        await _filingSvc.OnTransactionCreated(transaction, filingId);

        // Assert
        await _filingSummaryRepository.Received(1).Update(Arg.Is<FilingSummary>(fs =>
            fs.FilingSummaryTypeId == FilingSummaryType.ActivityExpenseSummary.Id &&
            fs.PeriodAmount == 150 &&
            fs.ToDateAmount == 150));
    }

    [Test]
    public async Task SubmitLobbyistReport_ShouldUpdateFuturePendingReports()
    {
        // Arrange
        long filingId = 789;
        long filerId = 999;

        var now = _dateNow;
        var endDate = now.Date;

        var filingSummaryTypeId = 1;

        var submittedReport = new LobbyistReport
        {
            Id = filingId,
            FilerId = filerId,
            StartDate = now.AddMonths(-2),
            EndDate = endDate,
            FilingPeriodId = 1,
            FilingPeriod = new FilingPeriod { EndDate = endDate },
            StatusId = FilingStatus.Draft.Id,
            FilingSummaries = new List<FilingSummary>
            {
                new() { FilingSummaryTypeId = filingSummaryTypeId, ToDateAmount = 200, PeriodAmount = 100 }
            }
        };

        var futureReport = new LobbyistReport
        {
            Id = 790,
            FilerId = filerId,
            StartDate = endDate.AddDays(1),
            EndDate = endDate.AddMonths(1),
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistReport.Id,
            FilingSummaries = new List<FilingSummary>
            {
                new() { FilingSummaryTypeId = filingSummaryTypeId, ToDateAmount = 50, PeriodAmount = 20 }
            }
        };

        var activityExpenses = new List<ActivityExpense>
        {
            new()
            {
                TransactionDate = now.AddDays(-10),
                Amount = new Currency(1000),
                MonetaryTypeId = MonetaryType.Credit.Id,
                ContactId = 100,
                Contact = new OrganizationContact
                {
                    Id = 100,
                    FilerContactType = FilerContactType.Organization,
                    OrganizationName = "Some Org",
                    AddressList = new AddressList
                    {
                        Addresses = new List<Address>
                        {
                            new()
                            {
                                Street = "street",
                                Street2 = "street2",
                                City = "city",
                                State = "CA",
                                Zip = "12345",
                                Country = "US",
                                Type = "home",
                                Purpose = "purpose"
                            }
                        }
                    }
                },
                TransactionReportablePersons = new List<TransactionReportablePerson>
                {
                    new()
                    {
                        TransactionId = 1,
                        FilerContactId = 2,
                        Name = "Agency Person",
                        Amount = new Currency(1000)
                    }
                }
            }
        };

        var campaignContributions = new List<LobbyingCampaignContribution>
        {
            new()
            {
                TransactionDate = now.AddDays(-5),
                Amount = new Currency(500),
                FilerId = filerId,
                ContributorFilerId = 1000,
                RecipientFilerId = 1100
            }
        };

        _filingRepository.FindById(filingId).Returns(submittedReport);
        _filingRepository.GetAllByFilerId(filerId).Returns(new List<LobbyistReport> { submittedReport, futureReport });
        _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId).Returns(activityExpenses);
        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId).Returns(campaignContributions);

        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
            DecisionsWorkflow.SubmitLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(new DecisionsSubmitLobbyistReportResponse([]) { IsLateFiling = false });

        // Act
        var result = await _filingSvc.SubmitLobbyistReport(filingId, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(futureReport.FilingSummaries.First().ToDateAmount, Is.EqualTo(150m));
        });
    }

    #region Cancel filing

    [Test]
    public void CancelFiling_ShouldUpdateStatusToCanceled_WhenFilingIsDraft()
    {
        // Arrange
        long id = 1;
        var draftFiling = new Filing
        {
            Id = id,
            StatusId = FilingStatus.Draft.Id
        };

        _ = _filingRepository.FindById(id).Returns(draftFiling);
        _ = _filingRepository.UpdateProperty(
            Arg.Any<Filing>(),
            Arg.Any<Expression<Func<Filing, int>>>(),
            Arg.Any<int>()
        ).Returns(draftFiling);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _filingSvc.CancelFiling(id));

        _filingRepository.Received().Update(Arg.Any<Filing>());
    }

    [Test]
    public void CancelFiling_ShouldThrowKeyNotFoundException_WhenFilingDoesNotExist()
    {
        // Arrange
        long id = 1;
        _filingRepository.FindById(id).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _filingSvc.CancelFiling(id));

        Assert.That(ex!.Message, Does.Contain($"Filing with ID {id} not found."));
    }

    [Test]
    public void CancelFiling_ShouldThrowInvalidOperationException_WhenFilingIsNotDraft()
    {
        // Arrange
        long id = 1;
        var submittedFiling = new Filing
        {
            Id = id,
            StatusId = FilingStatus.Submitted.Id // Not Pending
        };

        _filingRepository.FindById(id).Returns(submittedFiling);

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(async () => await _filingSvc.CancelFiling(id));

        Assert.That(ex!.Message, Does.Contain($"Cannot cancel a filing that is not in 'Draft' status"));
    }

    [Test]
    public async Task UpdateLobbyistEmployerLumpSums_ShouldReturnNull_WhenFilingIsNotLobbyistEmployerReport()
    {
        // Arrange
        var otherFiling = new Filing
        {
            Id = _filingId,
            StatusId = FilingStatus.Submitted.Id // Not Pending
        };
        _filingRepository.FindById(_filingId).Returns(otherFiling);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerLumpSums(_filingId, _totalOverheadExpense, _totalUnderThresholdPayments);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public void UpdateLobbyistEmployerReportDto_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var dto = new WebApi.Filings.UpdateLobbyistEmployerReportDto
        {
            Id = 1,
            TotalOverheadExpense = 150.75m,
            TotalUnderThresholdPayments = 249.25m
        };

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(dto.TotalOverheadExpense, Is.EqualTo(150.75m));
            Assert.That(dto.TotalUnderThresholdPayments, Is.EqualTo(249.25m));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerLumpSums_ShouldSetLumpSumProperties_WhenDecisionWorkflowReturnsNoErrors()
    {
        // Arrange
        _filingRepository.FindById(_filingId).Returns(_lobbyistEmployerReport);

        _decisionsSvc.InitiateWorkflow<DecisionsLumpSumPayments, List<WorkFlowError>>(
            DecisionsWorkflow.LumpSumPaymentsRuleset, Arg.Any<DecisionsLumpSumPayments>(), true)
            .Returns(new List<WorkFlowError>());

        // Act
        var dto = await _filingSvc.UpdateLobbyistEmployerLumpSums(
          _filingId,
          (decimal)_totalOverheadExpense,
          (decimal)_totalUnderThresholdPayments
        );

        // Assert
        Assert.That(dto, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(dto.Valid, Is.True);
            Assert.That(dto.ValidationErrors, Is.Empty);
            Assert.That(dto.TotalOverheadExpense, Is.EqualTo(_totalOverheadExpense), "should mirror the input overhead");
            Assert.That(dto.TotalUnderThresholdPayments, Is.EqualTo(_totalUnderThresholdPayments), "should mirror the input under-threshold");
            Assert.That(_lobbyistEmployerReport.TotalOverheadExpense, Is.EqualTo(_totalOverheadExpense));
            Assert.That(_lobbyistEmployerReport.TotalUnderThresholdPayments, Is.EqualTo(_totalUnderThresholdPayments));
        });

        await _filingRepository.Received(1).Update(_lobbyistEmployerReport);
    }

    [Test]
    public async Task UpdateLobbyistEmployerLumpSums_ShouldReturnDtoWithErrors_WhenDecisionWorkflowReturnsErrors()
    {
        // Arrange
        _filingRepository.FindById(_filingId).Returns(_lobbyistEmployerReport);

        var errors = new List<WorkFlowError>
        {
            new("E1", "FieldA", "TypeX", "Oops A"),
            new("E2", "FieldB", "TypeY", "Oops B")
        };

        _decisionsSvc
          .InitiateWorkflow<DecisionsLumpSumPayments, List<WorkFlowError>>(
             DecisionsWorkflow.LumpSumPaymentsRuleset,
             Arg.Any<DecisionsLumpSumPayments>(),
             true
          )
          .Returns(errors);

        // Act
        var dto = await _filingSvc.UpdateLobbyistEmployerLumpSums(
          _filingId,
          (decimal)_totalOverheadExpense,
          (decimal)_totalUnderThresholdPayments
        );

        // Assert
        Assert.That(dto, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(dto.Valid, Is.False);
            Assert.That(dto.ValidationErrors, Is.EqualTo(errors));
            Assert.That(_lobbyistEmployerReport.TotalOverheadExpense, Is.EqualTo(_totalOverheadExpense));
            Assert.That(_lobbyistEmployerReport.TotalUnderThresholdPayments, Is.EqualTo(_totalUnderThresholdPayments));
        });

        await _filingRepository.DidNotReceive().Update(Arg.Any<LobbyistEmployerReport>());
    }

    #endregion

    #region Cancel Lobbyist Employer Report

    [Test]
    public async Task CancelLobbyistEmployerReport_ShouldUpdateStatusToCancelled_WhenReportExists()
    {
        // Arrange
        long filingId = 123;
        var report = new LobbyistEmployerReport
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id,
            FilerId = 1
        };

        _ = _filingRepository.FindById(filingId).Returns(report);
        _ = _filingRepository.UpdateProperty(
            Arg.Any<Filing>(),
            Arg.Any<Expression<Func<Filing, long>>>(),
            FilingStatus.Cancelled.Id
        ).Returns(report);

        // Act
        var result = await _filingSvc.CancelLobbyistEmployerReport(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(report));

        await _filingRepository.Received(1).UpdateProperty(
            report,
            Arg.Any<Expression<Func<Filing, long>>>(),
            FilingStatus.Cancelled.Id);
    }

    [Test]
    public async Task CancelLobbyistEmployerReport_ShouldReturnNull_WhenReportDoesNotExist()
    {
        // Arrange
        long filingId = 123;
        _ = _filingRepository.FindById(filingId).Returns((Filing?)null);

        // Act
        var result = await _filingSvc.CancelLobbyistEmployerReport(filingId);

        // Assert
        Assert.That(result, Is.Null);
        _ = await _filingRepository.DidNotReceive().UpdateProperty(
            Arg.Any<Filing>(),
            Arg.Any<Expression<Func<Filing, long>>>(),
            Arg.Any<long>());
    }

    [Test]
    public async Task CancelLobbyistEmployerReport_ShouldReturnNull_WhenFilingIsNotLobbyistEmployerReport()
    {
        // Arrange
        long filingId = 123;
        var otherFiling = new Filing
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id
        };

        _ = _filingRepository.FindById(filingId).Returns(otherFiling);

        // Act
        var result = await _filingSvc.CancelLobbyistEmployerReport(filingId);

        // Assert
        Assert.That(result, Is.Null);
        _ = await _filingRepository.DidNotReceive().UpdateProperty(
            Arg.Any<Filing>(),
            Arg.Any<Expression<Func<Filing, long>>>(),
            Arg.Any<long>());
    }

    [Test]
    public void CancelLobbyistEmployerReport_ShouldThrowHttpRequestException_WhenReportIsNotDraft()
    {
        // Arrange
        long filingId = 123;
        var submittedReport = new LobbyistEmployerReport
        {
            Id = filingId,
            StatusId = FilingStatus.Submitted.Id
        };

        _ = _filingRepository.FindById(filingId).Returns(submittedReport);

        // Act & Assert
        var ex = Assert.ThrowsAsync<HttpRequestException>(async () =>
            await _filingSvc.CancelLobbyistEmployerReport(filingId));

        Assert.Multiple(() =>
        {
            Assert.That(ex!.Message, Does.Contain("Cannot cancel a report that is not in 'Draft' status"));
            Assert.That(ex.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.UnprocessableEntity));
        });
    }

    #endregion

    #region Cancel Lobbyist Report

    [Test]
    public async Task CancelLobbyistReport_ShouldUpdateStatusToCancelled_WhenReportExists()
    {
        // Arrange
        long filingId = 123;
        var report = new LobbyistReport
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id,
            FilerId = 1
        };

        _ = _filingRepository.FindById(filingId).Returns(report);
        _ = _filingRepository.UpdateProperty(
            Arg.Any<Filing>(),
            Arg.Any<Expression<Func<Filing, long>>>(),
            FilingStatus.Cancelled.Id
        ).Returns(report);

        // Act
        var result = await _filingSvc.CancelLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.EqualTo(report));

        _ = await _filingRepository.Received(1).UpdateProperty(
            report,
            Arg.Any<Expression<Func<Filing, long>>>(),
            FilingStatus.Cancelled.Id);
    }

    [Test]
    public async Task CancelLobbyistReport_ShouldReturnNull_WhenReportDoesNotExist()
    {
        // Arrange
        long filingId = 123;
        _ = _filingRepository.FindById(filingId).Returns((Filing?)null);

        // Act
        var result = await _filingSvc.CancelLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Null);
        _ = await _filingRepository.DidNotReceive().UpdateProperty(
            Arg.Any<Filing>(),
            Arg.Any<Expression<Func<Filing, long>>>(),
            Arg.Any<long>());
    }

    [Test]
    public async Task CancelLobbyistReport_ShouldReturnNull_WhenFilingIsNotLobbyistReport()
    {
        // Arrange
        long filingId = 123;
        var otherFiling = new Filing
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id
        };

        _ = _filingRepository.FindById(filingId).Returns(otherFiling);

        // Act
        var result = await _filingSvc.CancelLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Null);
        _ = await _filingRepository.DidNotReceive().UpdateProperty(
            Arg.Any<Filing>(),
            Arg.Any<Expression<Func<Filing, long>>>(),
            Arg.Any<long>());
    }

    [Test]
    public void CancelLobbyistReport_ShouldThrowHttpRequestException_WhenReportIsNotDraft()
    {
        // Arrange
        long filingId = 123;
        var submittedReport = new LobbyistReport
        {
            Id = filingId,
            StatusId = FilingStatus.Submitted.Id
        };

        _ = _filingRepository.FindById(filingId).Returns(submittedReport);

        // Act & Assert
        var ex = Assert.ThrowsAsync<HttpRequestException>(async () =>
            await _filingSvc.CancelLobbyistReport(filingId));

        Assert.Multiple(() =>
        {
            Assert.That(ex!.Message, Does.Contain("Cannot cancel a report that is not in 'Draft' status"));
            Assert.That(ex.StatusCode, Is.EqualTo(System.Net.HttpStatusCode.UnprocessableEntity));
        });
    }

    #endregion

    #region CreateOfficeHolderCandidateShortFormFiling

    [Test]
    public async Task CreateOfficeHolderCandidateShortFormFiling_CreatesFiling()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriodId = 1L;

        var expectedFiling = new Filing
        {
            Id = 1,
            FilerId = filerId,
            FilingPeriodId = filingPeriodId,
            Version = 0,
            StatusId = FilingStatus.Draft.Id,
            OriginalId = 0,
            ParentId = 0,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id
        };

        _filingRepository.Create(Arg.Is<Filing>(f =>
            f.FilerId == filerId &&
            f.FilingPeriodId == filingPeriodId &&
            f.StatusId == FilingStatus.Draft.Id &&
            f.FilingTypeId == FilingType.OfficeHolderCandidateShortForm.Id)).Returns(expectedFiling);

        // Act
        var result = await _filingSvc.CreateOfficeHolderCandidateShortFormFiling(filerId, filingPeriodId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.FilingPeriodId, Is.EqualTo(filingPeriodId));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
            Assert.That(result.FilingTypeId, Is.EqualTo(FilingType.OfficeHolderCandidateShortForm.Id));
        });

        await _filingRepository.Received(1).Create(Arg.Any<Filing>());
    }

    #endregion

    #region GetFilingById

    [Test]
    public async Task GetFilingById_ReturnsFiling_WhenExists()
    {
        // Arrange
        var filingId = 42L;
        var expectedFiling = new Filing
        {
            Id = filingId,
            FilerId = 1,
            FilingPeriodId = 100,
            StatusId = FilingStatus.Pending.Id,
            FilingTypeId = FilingType.OfficeHolderCandidateShortForm.Id
        };

        _filingRepository
            .GetFilingById(filingId)
            .Returns(Task.FromResult<Filing?>(expectedFiling));

        // Act
        var result = await _filingSvc.GetFilingById(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result?.Id, Is.EqualTo(filingId));
        await _filingRepository.Received(1).GetFilingById(filingId);
    }

    [Test]
    public async Task GetFilingById_ReturnsNull_WhenFilingNotFound()
    {
        // Arrange
        var filingId = 999L;
        _filingRepository
            .GetFilingById(filingId)
            .Returns(Task.FromResult<Filing?>(null));

        // Act
        var result = await _filingSvc.GetFilingById(filingId);

        // Assert
        Assert.That(result, Is.Null);
        await _filingRepository.Received(1).GetFilingById(filingId);
    }

    #endregion

    [Test]
    public async Task GetFilingReports_ShouldReturnReports()
    {
        // Arrange
        var userId = 1L;
        var filerUsers = new List<FilerUser>
        {
            new() { FilerId = 1, UserId = userId },
            new() { FilerId = 2, UserId = userId }
        };

        var filingReports = new List<FilingReportDto>
        {
            new ()
            {
                Id = 1,
                CreatedDate = _dateNow,
                StartDate = _dateNow.AddDays(-1),
                EndDate = _dateNow.AddDays(1),
                Name = "Report 1",
                Status = FilingStatus.Draft.Name,
                Version = 1,
                TypeId  = FilingType.OfficeHolderCandidateShortForm.Id,
            },
            new ()
            {
                Id = 2,
                CreatedDate = _dateNow,
                StartDate = _dateNow.AddDays(-1),
                EndDate = null,
                Name = "Report",
                Status = FilingStatus.Submitted.Name,
                Version = 0,
                TypeId  = FilingType.OfficeHolderCandidateShortForm.Id,
            }
        };

        _ = _authorizationSvc.GetInitiatingUserId().Returns(userId);
        _ = _filerUserRepository.FindFilerUsersByUserId(userId).Returns(filerUsers);
        _ = _dependencies.FilingRepository.GetFilingReportsByFilerIds(Arg.Any<IEnumerable<long>>()).Returns(filingReports);

        // Act
        var result = await _filingSvc.GetFilingReports();

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));

            var firstReport = result.First();
            Assert.That(firstReport.Id, Is.EqualTo(1));
            Assert.That(firstReport.ReportPeriod, Is.EqualTo($"{filingReports[0].StartDate?.Date.ToShortDateString()} - {filingReports[0].EndDate?.Date.ToShortDateString()}"));

            var secondReport = result.Skip(1).First();
            Assert.That(secondReport.Id, Is.EqualTo(2));
            Assert.That(secondReport.ReportPeriod, Is.EqualTo("N/A"));
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilingPeriodsForFiler returns correct periods with HasFiling property set.
    /// </summary>
    [Test]
    public async Task GetAllFilingPeriodsForFiler_ReturnsAllPeriodsWithCorrectHasFilingFlag_WhenFilingsExist()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriods = new List<FilingPeriod>
        {
            new() {
                Id = 1,
                Name = "Period 1",
                StartDate = _dateNow.AddMonths(-2),
                EndDate = _dateNow.AddMonths(-1),
                DueDate = _dateNow
            },
            new()
            {
                Id = 2,
                Name = "Period 2",
                StartDate = _dateNow.AddMonths(-1),
                EndDate = _dateNow,
                DueDate = _dateNow.AddMonths(1)
            },
            new()
            {
                Id = 3,
                Name = "Period 3",
                StartDate = _dateNow,
                EndDate = _dateNow.AddMonths(1),
                DueDate = _dateNow.AddMonths(2)
            }
        };

        var filings = new List<Filing>
        {
            new() { Id = 1, FilerId = filerId, FilingPeriodId = 1, FilingTypeId = 1, StatusId = 1 },
            new() { Id = 2, FilerId = filerId, FilingPeriodId = 3, FilingTypeId = 2, StatusId = 1 }
        };

        _ = _filingPeriodRepository.GetAll().Returns(filingPeriods);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(filings);

        // Act
        var result = await _filingSvc.GetAllFilingPeriodsForFiler(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(3));

            var resultList = result.ToList();
            Assert.That(resultList[0].Id, Is.EqualTo(1));
            Assert.That(resultList[0].HasFiling, Is.False);

            Assert.That(resultList[1].Id, Is.EqualTo(2));
            Assert.That(resultList[1].HasFiling, Is.False);

            Assert.That(resultList[2].Id, Is.EqualTo(3));
            Assert.That(resultList[2].HasFiling, Is.False);
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilingPeriodsForFiler returns all periods with HasFiling=false when filer has no filings.
    /// </summary>
    [Test]
    public async Task GetAllFilingPeriodsForFiler_ReturnsAllPeriodsWithNoFilings_WhenFilerHasNoFilings()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriods = new List<FilingPeriod>
    {
        new()
        {
            Id = 1,
            Name = "Period 1",
            StartDate = _dateNow.AddMonths(-2),
            EndDate = _dateNow.AddMonths(-1),
            DueDate = _dateNow
        },
        new()
        {
            Id = 2,
            Name = "Period 2",
            StartDate = _dateNow.AddMonths(-1),
            EndDate = _dateNow,
            DueDate = _dateNow.AddMonths(1)
        }
    };

        _ = _filingPeriodRepository.GetAll().Returns(filingPeriods);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing>());

        // Act
        var result = await _filingSvc.GetAllFilingPeriodsForFiler(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));

            var resultList = result.ToList();
            Assert.That(resultList[0].Id, Is.EqualTo(1));
            Assert.That(resultList[0].HasFiling, Is.False);

            Assert.That(resultList[1].Id, Is.EqualTo(2));
            Assert.That(resultList[1].HasFiling, Is.False);
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilingPeriodsForFiler filters filings by filing type when specified.
    /// </summary>
    [Test]
    public async Task GetAllFilingPeriodsForFiler_FiltersFilingsByType_WhenFilingTypeIdSpecified()
    {
        // Arrange
        var filerId = 1L;
        var filingTypeId = 2L;
        var filingPeriods = new List<FilingPeriod>
        {
            new() { Id = 1, Name = "Period 1", StartDate = _dateNow.AddMonths(-2) },
            new() { Id = 2, Name = "Period 2", StartDate = _dateNow.AddMonths(-1) },
            new() { Id = 3, Name = "Period 3", StartDate = _dateNow }
        };

        var filings = new List<Filing>
        {
            new() { Id = 1, FilerId = filerId, FilingPeriodId = 1, FilingTypeId = 1, StatusId = 1 },
            new() { Id = 2, FilerId = filerId, FilingPeriodId = 2, FilingTypeId = 2, StatusId = 1 },
            new() { Id = 3, FilerId = filerId, FilingPeriodId = 3, FilingTypeId = 2, StatusId = 1 }
        };

        _ = _filingPeriodRepository.GetAll().Returns(filingPeriods);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(filings);

        // Act
        var result = await _filingSvc.GetAllFilingPeriodsForFiler(filerId, filingTypeId, 4);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(3));

            var resultList = result.ToList();
            Assert.That(resultList[0].Id, Is.EqualTo(1));
            Assert.That(resultList[0].HasFiling, Is.True);

            Assert.That(resultList[1].Id, Is.EqualTo(2));
            Assert.That(resultList[1].HasFiling, Is.True);

            Assert.That(resultList[2].Id, Is.EqualTo(3));
            Assert.That(resultList[2].HasFiling, Is.True);
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilingPeriodsForFiler properly orders results by StartDate.
    /// </summary>
    [Test]
    public async Task GetAllFilingPeriodsForFiler_OrdersResultsByStartDate()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriods = new List<FilingPeriod>
    {
        new() { Id = 1, Name = "Period C", StartDate = _dateNow.AddMonths(1) },
        new() { Id = 2, Name = "Period A", StartDate = _dateNow.AddMonths(-1) },
        new() { Id = 3, Name = "Period B", StartDate = _dateNow }
    };

        _ = _filingPeriodRepository.GetAll().Returns(filingPeriods);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing>());

        // Act
        var result = await _filingSvc.GetAllFilingPeriodsForFiler(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(3));

            var resultList = result.ToList();
            Assert.That(resultList[0].Id, Is.EqualTo(2)); // Period A (earliest start date)
            Assert.That(resultList[1].Id, Is.EqualTo(3)); // Period B (middle start date)
            Assert.That(resultList[2].Id, Is.EqualTo(1)); // Period C (latest start date)
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilingPeriodsForFiler handles filings with null FilingPeriodId.
    /// </summary>
    [Test]
    public async Task GetAllFilingPeriodsForFiler_HandlesNullFilingPeriodIds()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriods = new List<FilingPeriod>
        {
            new() { Id = 1, Name = "Period 1", StartDate = _dateNow.AddMonths(-1) },
            new() { Id = 2, Name = "Period 2", StartDate = _dateNow }
        };

        var filings = new List<Filing>
        {
            new() { Id = 1, FilerId = filerId, FilingPeriodId = 1, FilingTypeId = 1, StatusId = 1 },
            new() { Id = 2, FilerId = filerId, FilingPeriodId = null, FilingTypeId = 1, StatusId = 1 } // Filing with null period ID
        };

        _ = _filingPeriodRepository.GetAll().Returns(filingPeriods);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(filings);

        // Act
        var result = await _filingSvc.GetAllFilingPeriodsForFiler(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));

            var resultList = result.ToList();
            Assert.That(resultList[0].Id, Is.EqualTo(1));
            Assert.That(resultList[0].HasFiling, Is.True);

            Assert.That(resultList[1].Id, Is.EqualTo(2));
            Assert.That(resultList[1].HasFiling, Is.True);
        });
    }

    /// <summary>
    /// Test to ensure GetAllFilingPeriodsForFiler for lobbyist filters filing periods correctly
    /// Should filter periods by registration from submitted at start of year to end of legislative session
    /// </summary>
    [Test]
    public async Task GetAllFilingPeriodsForFiler_Lobbyist_FiltersPeriodsCorrectly()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriods = new List<FilingPeriod>
        {
            new() { Id = 1, Name = "Period 1", StartDate = _dateNow.AddMonths(-1) },
            new() { Id = 2, Name = "Period 2", StartDate = _dateNow },
            new() { Id = 3, Name = "Period 3", StartDate = new DateTime(_dateNow.Year, 1, 1, 0, 0, 0, DateTimeKind.Local).AddDays(-1) }, //Should filter out where start date is before start of year
            new() { Id = 3, Name = "Period 4", EndDate = _filingSvc.GetLegislativeEndDateForDate(_dateNow).AddDays(1) } //Should filter out where end date is after end of legislative session
        };
        var lobbyistRegistration = new Lobbyist()
        {
            Name = "First Last",
            StatusId = RegistrationStatus.Accepted.Id,
            SubmittedAt = _dateNow
        };

        var filings = new List<Filing>
        {
            new() { Id = 1, FilerId = filerId, FilingPeriodId = 1, FilingTypeId = FilingType.LobbyistReport.Id, StatusId = 1 },
            new() { Id = 2, FilerId = filerId, FilingPeriodId = null, FilingTypeId = FilingType.LobbyistReport.Id, StatusId = 1 }
        };

        _ = _filingPeriodRepository.GetAll().Returns(filingPeriods);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(filings);
        _ = _registrationRepository.FindLobbyingRegistrationByFilerId<Lobbyist>(filerId).Returns(lobbyistRegistration);

        // Act
        var result = await _filingSvc.GetAllFilingPeriodsForFiler(filerId, FilingType.LobbyistReport.Id);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));

            var resultList = result.ToList();
            Assert.That(resultList[0].Id, Is.EqualTo(1));
            Assert.That(resultList[0].HasFiling, Is.True);

            Assert.That(resultList[1].Id, Is.EqualTo(2));
            Assert.That(resultList[1].HasFiling, Is.True);
        });
    }


    [Test]
    public async Task SendForAttestationLobbyistReport_ShouldReturnValidResponse_WhenNoErrors()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id,
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
            StartDate = new DateTime(2024, 12, 01, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        var decisionResponse = new DecisionsSendForAttestationLobbyistReportResponse([])
        {
            Result = [],
            IsLateFiling = false,
            ListOfNotifications = [new NotificationTrigger(true, 1, _dateNow.AddDays(1))]
        };

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSendForAttestationLobbyistReportResponse>(
            DecisionsWorkflow.SendForAttestationLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }

    [Test]
    public async Task SendForAttestationLobbyistReport_ShouldReturnValidResponse_WhenNoNotifications()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id,
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
            StartDate = new DateTime(2024, 12, 01, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        var decisionResponse = new DecisionsSendForAttestationLobbyistReportResponse([])
        {
            Result = [],
            IsLateFiling = false,
            ListOfNotifications = new List<NotificationTrigger> { new(false, null, null) }
        };

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSendForAttestationLobbyistReportResponse>(
            DecisionsWorkflow.SendForAttestationLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }


    [Test]
    public async Task SendForAttestationLobbyistReport_ShouldReturnValidResponse_WhenNotificationFlagAndTemplateId_AreNotAvailable()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id,
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
            StartDate = new DateTime(2024, 12, 01, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        var decisionResponse = new DecisionsSendForAttestationLobbyistReportResponse([])
        {
            Result = [],
            IsLateFiling = false,
            ListOfNotifications = null
        };

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSendForAttestationLobbyistReportResponse>(
            DecisionsWorkflow.SendForAttestationLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }

    [Test]
    public async Task SendForAttestationLobbyistReport_ShouldReturnValidResponse_WithNotifications()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id,
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
            StartDate = new DateTime(2024, 12, 01, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        var decisionResponse = new DecisionsSendForAttestationLobbyistReportResponse([])
        {
            Result = [],
            IsLateFiling = false,
            ListOfNotifications = new List<NotificationTrigger> { new(true, 1, null) }
        };

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSendForAttestationLobbyistReportResponse>(
            DecisionsWorkflow.SendForAttestationLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(decisionResponse);


        // Act
        var result = await _filingSvc.SendForAttestationLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });

        await _notificationSvc.Received(1).SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }

    [Test]
    public async Task SendForAttestationLobbyistReport_ShouldReturnInvalidResponse_WhenErrorsExist()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var lobbyistReport = new LobbyistReport
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id
        };

        var decisionErrors = new List<WorkFlowError>
        {
            new("ContactType", "ErrGlobal0002", "Validation", "invalid"),
            new("CheckRequiredFields", "ErrGlobal0002", "Validation", "invalid"),
        };

        var decisionResponse = new DecisionsSendForAttestationLobbyistReportResponse([])
        {
            Result = decisionErrors,
            IsLateFiling = false,
        };

        _filingRepository.FindById(filingId).Returns(lobbyistReport);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSendForAttestationLobbyistReportResponse>(
            DecisionsWorkflow.SendForAttestationLobbyistReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistReport>(),
            true
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(3));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
        });

        await _filingRepository.DidNotReceive().Update(Arg.Any<LobbyistReport>());
        await _notificationSvc.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }

    [Test]
    public async Task SendForAttestationLobbyistReport_ShouldReturnNull_WhenFilingDoesNotExist()
    {
        // Arrange
        var filingId = 123L;
        _filingRepository.FindById(filingId).Returns((Filing?)null);

        // Act
        var result = await _filingSvc.SendForAttestationLobbyistReport(filingId);

        // Assert
        Assert.That(result, Is.Null);
        await _filingRepository.DidNotReceive().Update(Arg.Any<LobbyistReport>());
        await _notificationSvc.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }


    #region 72h Report


    [Test]
    public async Task GetAllLegislativeSessions_ReturnsCorrectSessions()
    {
        // Arrange
        var mockSessions = new List<LegislativeSession>
        {
            new() { Id = 1, Name = "Session 2025", StartDate = _dateNow, EndDate = _dateNow.AddMonths(6) },
            new() { Id = 2, Name = "Session 2026", StartDate = _dateNow.AddMonths(7), EndDate = _dateNow.AddMonths(18) }
        };

        _filingRepository.GetAllLegislativeSessions().Returns(Task.FromResult(mockSessions));

        // Act
        var result = await _filingSvc.GetAllLegislativeSessions();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.First().Name, Is.EqualTo("Session 2025"));
            Assert.That(result.Last().Name, Is.EqualTo("Session 2026"));
        });
    }

    [Test]
    public async Task GetAllLegislativeSessions_ReturnsEmpty_WhenNoSessions()
    {
        // Arrange
        _filingRepository.GetAllLegislativeSessions().Returns(Task.FromResult(new List<LegislativeSession>()));
        // Act
        var result = await _filingSvc.GetAllLegislativeSessions();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(0));
    }
    [Test]
    public async Task GetReport72H_ShouldReturnReport_WhenFound()
    {
        // Arrange
        long reportId = 1;
        var report = new Report72H
        {
            Id = reportId,
            FilerId = 123,
            ParentId = 10,
            StatusId = FilingStatus.Submitted.Id,
            SubmittedDate = _dateNow,
            Version = 1,
            OtherActionsLobbied = "test",
        };

        var report72HReponse = new Report72HResponseDto
        {
            Id = report.Id,
            FilerId = report.FilerId,
            ParentId = report.ParentId,
            Status = report.StatusId,
            FilerName = report.Filer?.CurrentRegistration?.Name,
            SubmittedDate = report.SubmittedDate,
            Version = report.Version,
            OtherActionsLobbied = report.OtherActionsLobbied,
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };

        _ = _filingRepository.FindById(reportId).Returns(report);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(1).Returns(actionsLobbiedResponse);

        // Act
        var result = await _filingSvc.GetReport72H(reportId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(reportId));
            Assert.That(result.OtherActionsLobbied, Is.EqualTo("test"));
            Assert.That(result.AgencyActions, Is.Not.Null);
        });
    }

    [Test]
    public void GetReport72H_ShouldThrow_WhenNotFound()
    {
        // Arrange
        _ = _filingRepository.FindById(Arg.Any<long>()).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _filingSvc.GetReport72H(999));
        Assert.That(ex?.Message, Does.Contain("72H Report not found"));
    }

    [Test]
    public async Task UpdateReport72HActionsLobbied_ShouldUpdateAndReturnDto_WhenValid()
    {
        // Arrange
        long reportId = 1;
        var request = new Report72HActionsLobbiedRequestDto
        {
            OtherActionsLobbied = "updated",
            AdministrativeActions = new List<ActionsLobbiedRequestDto>
            {
                new() { AgencyId = 123, OfficialPositionDescription = "Test Action" }
            },
            AssemblyBills = new List<ActionsLobbiedRequestDto>(),
            SenateBills = new List<ActionsLobbiedRequestDto>()
        };

        var report = new Report72H
        {
            Id = reportId,
            FilerId = 200,
            StatusId = FilingStatus.Draft.Id,
            ParentId = 1,
            Version = 1,
            SubmittedDate = _dateNow
        };

        _filingRepository.FindById(reportId).Returns(report);
        _dependencies.FilingSummaryRepository.GetAllByFilingId(reportId).Returns(new List<FilingSummary>
        {
            new ()
            {
                FilingSummaryTypeId = FilingSummaryType.ActionsLobbiedSummary.Id,
                PeriodAmount = 0,
                ToDateAmount = null,
                Id = 1,
                FilingId = 1,
                FilingSummaryStatus = new FilingSummaryStatus(1, "Not Started")
            }
        });

        _decisionsSvc.InitiateWorkflow<DecisionsReport72HActionsLobbied, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBFiling72HourReportActionsLobbied,
            Arg.Any<DecisionsReport72HActionsLobbied>(),
            true).Returns(new List<WorkFlowError>());

        _actionsLobbiedSvc
            .UpsertActionsLobbiedForFiling(reportId, Arg.Any<List<ActionsLobbiedRequestDto>>())
            .Returns(new List<ActionsLobbiedResponseDto>());

        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(Arg.Any<long>()).Returns(new ActionsLobbiedByEntityResponse());

        _filingRepository.Update(Arg.Any<Report72H>()).Returns(report);

        // Act
        var result = await _filingSvc.UpdateReport72HActionsLobbied(reportId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.FilingId, Is.EqualTo(reportId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.OtherActionsLobbied, Is.EqualTo("updated"));
        });

        await _filingRepository.Received(1).Update(Arg.Is<Report72H>(r => r.OtherActionsLobbied == "updated"));
    }


    [Test]
    public void UpdateReport72HActionsLobbied_ShouldThrow_WhenNotFound()
    {
        // Arrange
        _ = _filingRepository.FindById(Arg.Any<long>()).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() =>
            _filingSvc.UpdateReport72HActionsLobbied(1, new Report72HActionsLobbiedRequestDto()));
        Assert.That(ex?.Message, Does.Contain("72H Report not found"));
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedBills_ShouldReturnFormattedValidationErrors()
    {
        // Arrange
        var request = new ValidateReport72HActionsLobbiedRequestDto
        {
            ActionsLobbied = new List<ActionsLobbiedRequestDto>
        {
            new()
            {
                OfficialPositionId = 1,
                OfficialPositionDescription = "Support"
            }
        }
        };

        var decisionsErrors = new List<WorkFlowError>
        {
            new("OfficialPosition0", "Code", "Validation", "Position is invalid"),
        };

        _decisionsSvc
            .InitiateWorkflow<List<DecisionsReport72HActionsLobbiedBills>, List<WorkFlowError>>(
                DecisionsWorkflow.FDLOBFiling72HourReportActionsLobbiedBills,
                Arg.Any<List<DecisionsReport72HActionsLobbiedBills>>(),
                true)
            .Returns(decisionsErrors);

        // Act
        var result = await _filingSvc.ValidateReport72HActionsLobbiedBills(request);

        // Assert
        Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result.ValidationErrors[0].FieldName, Is.EqualTo("OfficialPosition0"));
            Assert.That(result.ValidationErrors[0].Message, Is.EqualTo("Position is invalid"));
        });
    }

    [Test]
    public async Task ValidateReport72HActionsLobbiedAgencies_ShouldReturnFormattedValidationErrors()
    {
        // Arrange
        var request = new ValidateReport72HActionsLobbiedRequestDto
        {
            ActionsLobbied = new List<ActionsLobbiedRequestDto>
        {
            new()
            {
                AgencyId = 42,
                AgencyDescription = "Test Agency",
                AdministrativeAction = "Action",
                OfficialPositionId = 2,
                OfficialPositionDescription = "Neutral"
            }
        }
        };

        var decisionsErrors = new List<WorkFlowError>
        {
            new("AgencyName0", "Code", "Validation", "Agency name is required"),
        };

        _decisionsSvc
            .InitiateWorkflow<List<DecisionsReport72HActionsLobbiedAgencies>, List<WorkFlowError>>(
                DecisionsWorkflow.FDLOBFiling72HourReportActionsLobbiedAgencies,
                Arg.Any<List<DecisionsReport72HActionsLobbiedAgencies>>(),
                true)
            .Returns(decisionsErrors);

        // Act
        var result = await _filingSvc.ValidateReport72HActionsLobbiedAgencies(request);

        // Assert
        Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result.ValidationErrors[0].FieldName, Is.EqualTo("AgencyName0"));
            Assert.That(result.ValidationErrors[0].Message, Is.EqualTo("Agency name is required"));
        });
    }

    [Test]
    public async Task SendForAttestationReport72H_ShouldReturnValidResponse_WhenNoErrors()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var report72H = new Report72H
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id
        };

        var decisionResponse = new DecisionsSendForAttestationReport72HResponse([])
        {
            Result = []
        };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };

        var lobbyingAdvertisement = new LobbyingAdvertisement
        {
            PublicationDate = DateTime.UtcNow,
            DistributionMethodId = 1,
            DistributionMethodDescription = "Testing",
            Amount = (Currency)10
        };

        _filingRepository.FindById(filingId).Returns(report72H);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);
        _transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(lobbyingAdvertisement);
        _decisionsSvc.InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(
            DecisionsWorkflow.SendForAttestationReport72HRuleset,
            Arg.Any<DecisionsSendForAttestationReport72H>(),
            false
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, new List<long> { 1 });

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }

    [Test]
    public async Task SendForAttestationReport72H_ShouldReturnValidResponse_WhenNoNotifications()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var report72H = new Report72H
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id
        };

        var decisionResponse = new DecisionsSendForAttestationReport72HResponse([])
        {
            Result = []
        };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };

        var lobbyingAdvertisement = new LobbyingAdvertisement
        {
            PublicationDate = DateTime.UtcNow,
            DistributionMethodId = 1,
            DistributionMethodDescription = "Testing",
            Amount = (Currency)10
        };

        _filingRepository.FindById(filingId).Returns(report72H);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);
        _transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(lobbyingAdvertisement);
        _decisionsSvc.InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(
            DecisionsWorkflow.SendForAttestationReport72HRuleset,
            Arg.Any<DecisionsSendForAttestationReport72H>(),
            false
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, new List<long> { 1 });

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }

    [Test]
    public async Task SendForAttestationReport72H_ShouldReturnInvalidResponse_WhenErrorsExist()
    {
        // Arrange
        const long filingId = 123L;
        const long filingPeriodId = 1L;
        var report72H = new Report72H
        {
            Id = filingId,
            FilingPeriodId = filingPeriodId,
            StatusId = FilingStatus.Draft.Id
        };

        var decisionErrors = new List<WorkFlowError>
        {
            new("ContactType", "ErrGlobal0002", "Validation", "invalid"),
            new("CheckRequiredFields", "ErrGlobal0002", "Validation", "invalid"),
        };

        var decisionResponse = new DecisionsSendForAttestationReport72HResponse([])
        {
            Result = decisionErrors
        };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };

        var lobbyingAdvertisement = new LobbyingAdvertisement
        {
            PublicationDate = DateTime.UtcNow,
            DistributionMethodId = 1,
            DistributionMethodDescription = "Testing",
            Amount = (Currency)10
        };

        _filingRepository.FindById(filingId).Returns(report72H);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);
        _transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(lobbyingAdvertisement);
        _decisionsSvc.InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(
            DecisionsWorkflow.SendForAttestationReport72HRuleset,
            Arg.Any<DecisionsSendForAttestationReport72H>(),
            false
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, new List<long> { 1 });

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(2));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
        });

        await _filingRepository.DidNotReceive().Update(Arg.Any<Report72H>());
        await _notificationSvc.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }

    [Test]
    public async Task SendForAttestationReport72H_ShouldReturnNull_WhenFilingDoesNotExist()
    {
        // Arrange
        var filingId = 123L;
        _filingRepository.FindById(filingId).Returns((Filing?)null);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, new List<long> { 1 });

        // Assert
        Assert.That(result, Is.Null);
        await _filingRepository.DidNotReceive().Update(Arg.Any<Report72H>());
        await _notificationSvc.DidNotReceive().SendFilerNotification(Arg.Any<SendFilerNotificationRequest>());
    }
    [Test]
    public async Task SendForAttestationReport72H_ShouldIncludeOtherSubject_WhenOtherActionsLobbiedIsNotNull()
    {
        // Arrange
        const long filingId = 123L;
        var report72H = new Report72H
        {
            Id = filingId,
            FilingPeriodId = 1L,
            StatusId = FilingStatus.Draft.Id,
            OtherActionsLobbied = "Test other lobbying activities"
        };

        var decisionResponse = new DecisionsSendForAttestationReport72HResponse([])
        {
            Result = []
        };

        var actionsLobbied = new List<ActionsLobbied?>
        {
            new()
            {
                AgencyId = 101,
                AdministrativeAction = "Administrative Test",
                OfficialPositionId = 2,
                OfficialPositionDescription = "Support"
            }
        };

        report72H.ActionsLobbied = actionsLobbied;

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            SenateBillActions = [],
            AssemblyBillActions = [],
            AgencyActions = new List<ActionsLobbiedResponseDto>
            {
                new ActionsLobbiedResponseDto
                {
                    AgencyId = 10,
                    AdministrativeAction = "Test Admin Action",
                    OfficialPositionId = 3,
                    OfficialPositionDescription = "Oppose"
                }
            }
        };

        var lobbyingAdvertisement = new LobbyingAdvertisement
        {
            PublicationDate = DateTime.UtcNow,
            DistributionMethodId = 1,
            DistributionMethodDescription = "Print",
            Amount = (Currency)10
        };

        _filingRepository.FindById(filingId).Returns(report72H);
        _transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(lobbyingAdvertisement);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        DecisionsSendForAttestationReport72H? capturedInput = null;

        _decisionsSvc
            .InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(
                DecisionsWorkflow.SendForAttestationReport72HRuleset,
                Arg.Do<DecisionsSendForAttestationReport72H>(input => capturedInput = input),
                false
            )
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, new List<long>());

        // Assert
        Assert.That(capturedInput, Is.Not.Null);
        Assert.That(capturedInput!.ActionsLobbied[0].LobbyingAdvertisementSubjects, Does.Contain("Other"));
    }

    [Test]
    public async Task SendForAttestationReport72H_ShouldIncludeAdministrativeSubject_WhenAgencyActionsExist()
    {
        // Arrange
        const long filingId = 456L;

        var report72H = new Report72H
        {
            Id = filingId,
            FilingPeriodId = 1L,
            StatusId = FilingStatus.Draft.Id,
            OtherActionsLobbied = null // not needed for this case
        };

        var actionsLobbied = new List<ActionsLobbied?>
        {
            new()
            {
                AgencyId = 101,
                AdministrativeAction = "Administrative Test",
                OfficialPositionId = 2,
                OfficialPositionDescription = "Support"
            }
        };

        report72H.ActionsLobbied = actionsLobbied;

        var decisionResponse = new DecisionsSendForAttestationReport72HResponse([])
        {
            Result = []
        };

        var lobbyingAdvertisement = new LobbyingAdvertisement
        {
            PublicationDate = DateTime.UtcNow,
            DistributionMethodId = 3,
            DistributionMethodDescription = "Online",
            Amount = (Currency)300
        };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            SenateBillActions = new List<ActionsLobbiedResponseDto>
            {
                new()
                {
                    AgencyId = 10,
                    AdministrativeAction = "Test Admin Action",
                    OfficialPositionId = 3,
                    OfficialPositionDescription = "Oppose"
                }
            },
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>
            {
                new()
                {
                    AgencyId = 10,
                    AdministrativeAction = "Test Admin Action",
                    OfficialPositionId = 3,
                    OfficialPositionDescription = "Oppose"
                }
            },
            AgencyActions = new List<ActionsLobbiedResponseDto>
            {
                new()
                {
                    AgencyId = 10,
                    AdministrativeAction = "Test Admin Action",
                    OfficialPositionId = 3,
                    OfficialPositionDescription = "Oppose"
                }
            }
        };

        _filingRepository.FindById(filingId).Returns(report72H);
        _transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filingId).Returns(lobbyingAdvertisement);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        DecisionsSendForAttestationReport72H? capturedInput = null;

        _decisionsSvc.InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(
            DecisionsWorkflow.SendForAttestationReport72HRuleset,
            Arg.Do<DecisionsSendForAttestationReport72H>(input => capturedInput = input),
            false
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, new List<long>());

        // Assert
        Assert.That(capturedInput, Is.Not.Null);
        Assert.That(capturedInput!.ActionsLobbied[0].LobbyingAdvertisementSubjects, Does.Contain("Administrative"));
    }

    [Test]
    public async Task SendForAttestationReport72H_ShouldSetMinPublicationDate_WhenLobbyingAdIsNull()
    {
        // Arrange
        const long filingId = 789L;

        var report72H = new Report72H
        {
            Id = filingId,
            FilingPeriodId = 1L,
            StatusId = FilingStatus.Draft.Id,
            OtherActionsLobbied = null
        };

        var actionsLobbied = new List<ActionsLobbied?>
        {
            new()
            {
                AgencyId = 101,
                AdministrativeAction = "Administrative Test",
                OfficialPositionId = 2,
                OfficialPositionDescription = "Support"
            }
        };

        report72H.ActionsLobbied = actionsLobbied;

        var decisionResponse = new DecisionsSendForAttestationReport72HResponse([]) { Result = [] };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            SenateBillActions = [],
            AssemblyBillActions = [],
            AgencyActions = new List<ActionsLobbiedResponseDto>
            {
                new ActionsLobbiedResponseDto
                {
                    AgencyId = 10,
                    AdministrativeAction = "Test Admin Action",
                    OfficialPositionId = 3,
                    OfficialPositionDescription = "Oppose"
                }
            }
        };

        _filingRepository.FindById(filingId).Returns(report72H);
        _transactionRepository.GetLobbyingAdvertisementTransactionByFilingId(filingId)
            .Returns((LobbyingAdvertisement?)null);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        DecisionsSendForAttestationReport72H? capturedInput = null;

        _decisionsSvc
            .InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(
                DecisionsWorkflow.SendForAttestationReport72HRuleset,
                Arg.Do<DecisionsSendForAttestationReport72H>(input => capturedInput = input),
                false
            ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, new List<long>());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result?.Id, Is.EqualTo(filingId));
            Assert.That(result?.Valid, Is.True);
            Assert.That(result?.ValidationErrors, Is.Empty);
        });
    }

    #endregion

    #region 48h Report

    [Test]
    public async Task GetReport48H_ShouldReturnReport_WhenFound()
    {
        // Arrange
        long reportId = 1;
        var report = new Report48H
        {
            Id = reportId,
            FilerId = 123,
            ParentId = 10,
            StatusId = FilingStatus.Submitted.Id,
            SubmittedDate = _dateNow,
            Version = 1,
        };


        _ = _filingRepository.FindById(reportId).Returns(report);

        // Act
        var result = await _filingSvc.GetReport48H(reportId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(reportId));
        });
    }

    [Test]
    public void GetReport48H_ShouldThrow_WhenNotFound()
    {
        // Arrange
        _ = _filingRepository.FindById(Arg.Any<long>()).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _filingSvc.GetReport48H(999));
        Assert.That(ex?.Message, Does.Contain("48H Report not found"));
    }

    [Test]
    public async Task CreateReport48H_NoExistingReport_CreatesNewReport()
    {
        // Arrange
        var filerId = 1L;
        var filingPeriodId = 2L;

        var filingPeriod = new FilingPeriod
        {
            Id = filingPeriodId,
            StartDate = new DateTime(2023, 3, 5, 0, 0, 0, 0),
            EndDate = new DateTime(2023, 5, 6, 0, 0, 0, 0)
        };

        var filingSummaryStatuses = new List<FilingSummaryStatus>
        {
            new(1, "Not Started")
        };

        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing>());
        _ = _filingPeriodRepository.FindById(filingPeriodId).Returns(filingPeriod);
        _ = _filingSummaryStatusRepository.GetAllFilingSummaryStatuses().Returns(filingSummaryStatuses);
        _ = _filingRepository.Create(Arg.Any<Filing>())!.Returns(args => args[0] as Filing);

        // Act
        var result = await _filingSvc.CreateReport48H(filerId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null");
            Assert.That(result?.FilerId, Is.EqualTo(filerId), "FilerId should match");
            Assert.That(result?.Status, Is.EqualTo(FilingStatus.Draft.Id), "Status should be Draft");
        });

        _ = await _filingRepository.Received(1).Create(Arg.Any<Filing>());
        _ = await _filingRepository.Received(1).Update(Arg.Any<Filing>());
    }

    [Test]
    public async Task SubmitReport48H_ShouldReturnValidResponse_WhenNoValidationErrors()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var diligenceVerified = true;
        var report = new Report48H
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            DiligenceStatementVerified = null,
            SubmittedDate = null
        };

        var endOfSessionTransactions = new List<EndOfSessionLobbyingDto>
        {
            new()
            {
                FirmName = "Test",
                Amount = new Currency(100),
                Contact = new OrganizationContact()
                {
                    OrganizationName = "Test"
                }
            }
        };

        _ = _filingRepository.FindById(filingId).Returns(report);

        _transactionRepository.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId).Returns(endOfSessionTransactions);

        var decisionResponse = new DecisionsSubmitReport48HResponse
        {
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>(),
            Status = "Submitted"
        };

        _ = _decisionsSvc.InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourReportSubmitReport,
            Arg.Any<DecisionsSubmitReport48H>(),
            true).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SubmitReport48H(filingId, diligenceVerified);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(result.DiligenceStatementVerified, Is.EqualTo(diligenceVerified));
            Assert.That(result.SubmittedDate, Is.Not.Null);
        });
    }

    [Test]
    public async Task SubmitReport48H_ShouldReturnInvalidAndUpdateStatusToIncomplete_WhenDecisionResponseHasNonFatalErrors()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var diligenceVerified = true;
        var report = new Report48H
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            DiligenceStatementVerified = null,
            SubmittedDate = null
        };

        var error = new WorkFlowError("", "", "", "");

        var endOfSessionTransactions = new List<EndOfSessionLobbyingDto>
        {
            new()
            {
                FirmName = "Test",
                Amount = new Currency(100),
                Contact = new OrganizationContact()
                {
                    OrganizationName = "Test"
                }
            }
        };

        _ = _filingRepository.FindById(filingId).Returns(report);

        _transactionRepository.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId).Returns(endOfSessionTransactions);

        _ = _decisionsSvc.InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourReportSubmitReport,
            Arg.Any<DecisionsSubmitReport48H>(),
            true).Returns(new DecisionsSubmitReport48HResponse { Errors = [error] });

        // Act
        var result = await _filingSvc.SubmitReport48H(filingId, diligenceVerified);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
            Assert.That(result!.StatusId, Is.EqualTo(FilingStatus.Incomplete.Id));
        });
    }

    [Test]
    public async Task SubmitReport48H_ShouldReturnInvalidAndNotUpdateFilingStatus_WhenDecisionResponseHasFatalErrors()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var diligenceVerified = true;
        var report = new Report48H
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            DiligenceStatementVerified = null,
            SubmittedDate = null
        };

        var error = new WorkFlowError("", "", "Fatal", "");

        var endOfSessionTransactions = new List<EndOfSessionLobbyingDto>
        {
            new()
            {
                FirmName = "Test",
                Amount = new Currency(100),
                Contact = new OrganizationContact()
                {
                    OrganizationName = "Test"
                }
            }
        };

        _ = _filingRepository.FindById(filingId).Returns(report);

        _transactionRepository.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId).Returns(endOfSessionTransactions);

        _ = _decisionsSvc.InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourReportSubmitReport,
            Arg.Any<DecisionsSubmitReport48H>(),
            true).Returns(new DecisionsSubmitReport48HResponse { Errors = [error] });

        // Act
        var result = await _filingSvc.SubmitReport48H(filingId, diligenceVerified);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
            Assert.That(result!.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
        });
    }

    [Test]
    public async Task SubmitReport48H_ShouldUseAmendWorkflow_WhenFilingVersionIsGreaterThanZero()
    {
        // Arrange
        var filingId = 321L;
        var filerId = 654L;
        var diligenceVerified = true;
        var report = new Report48H
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            DiligenceStatementVerified = null,
            SubmittedDate = null,
            Version = 1 // <-- triggers amendment path
        };

        var endOfSessionTransactions = new List<EndOfSessionLobbyingDto>
    {
        new()
        {
            FirmName = "Test",
            Amount = new Currency(100),
            Contact = new OrganizationContact
            {
                OrganizationName = "Test"
            }
        }
    };

        _filingRepository.FindById(filingId).Returns(report);
        _transactionRepository.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId).Returns(endOfSessionTransactions);

        var decisionResponse = new DecisionsSubmitReport48HResponse
        {
            Errors = [],
            Notifications = [],
            Status = "Submitted"
        };

        _decisionsSvc
            .InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
                DecisionsWorkflow.FDLOBFiling48HourAmendReportSubmission, // this is what we are validating
                Arg.Any<DecisionsSubmitReport48H>(),
                true)
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SubmitReport48H(filingId, diligenceVerified);

        Assert.Multiple(() =>
        {
            // Assert
            Assert.That(result?.Valid, Is.True);
            Assert.That(result?.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
        });
        await _decisionsSvc.Received(1).InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourAmendReportSubmission,
            Arg.Any<DecisionsSubmitReport48H>(),
            true);
    }


    [Test]
    public async Task HandleReport48HToDecisionInput_ShouldIncludeLegislativeNumbers_WhenTransactionIdMatchesContactId2()
    {
        // Arrange
        var filingId = 789;
        var contactId = 123;

        var filing = new Filing { Id = filingId, StatusId = 1, AmendmentExplanation = "Test" };

        var actionsLobbied = new List<ActionsLobbied?>
        {
            new() { TransactionId = 1, BillId = 4567 },
            new() { TransactionId = 1, BillId = null },
            new() { TransactionId = 999, BillId = 1111 },
        };

        var transaction = new EndOfSessionLobbyingDto
        {
            FirmName = "Test Firm",
            Amount = 10000m,
            DateLobbyingFirmHired = new DateTime(2024, 7, 1),
            Contact = new OrganizationContact { Id = contactId, OrganizationName = "Name" },
            ActionsLobbied = actionsLobbied
        };

        _transactionRepository
            .GetAllEndOfSessionLobbyingTransactionsForFiling(filingId)
            .Returns(new List<EndOfSessionLobbyingDto> { transaction });

        _filerContactRepository
            .GetFilerContactById(contactId)
            .Returns(new OrganizationContact
            {
                Id = contactId,
                OrganizationName = "Name",
                PhoneNumberList = new PhoneNumberList
                {
                    PhoneNumbers = new List<PhoneNumber> { new() { Type = "Business", Number = "555-1234" } }
                },
                AddressList = new AddressList
                {
                    Addresses = new List<Address>
                    {
                        new()
                        {
                            City = "Sacramento",
                            Street = "123 Main St",
                            Street2 = "Suite 100",
                            Zip = "95814",
                            State = "CA",
                            Country = "USA",
                            Type = "Business",
                            Purpose = "Purpose"
                        }
                    }
                }
            });

        // Act
        var result = await _filingSvc.HandleReport48HToDecisionInput(filing);

        // Assert
        var tx = result.AddNewTransaction.AddNewTransaction.First();
        Assert.That(tx.LegislativeNumbers, Is.Not.Null);
        Assert.That(tx.LegislativeNumbers, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(tx.LegislativeNumbers.First(), Is.EqualTo("4567"));
            Assert.That(result.AmendmentExplanation.AmendmentExplanation, Is.EqualTo("Test"));
        });
    }

    [Test]
    public async Task HandleReport48HToDecisionInput_ShouldOmitPhoneAndAddress_WhenContactIsNull()
    {
        // Arrange
        var filing = new Filing { Id = 1, StatusId = 1, AmendmentExplanation = null };
        var transaction = new EndOfSessionLobbyingDto
        {
            FirmName = "Firm X",
            Amount = 1000m,
            Contact = null,
            ActionsLobbied = null
        };

        _transactionRepository
            .GetAllEndOfSessionLobbyingTransactionsForFiling(filing.Id)
            .Returns([transaction]);

        // Act
        var result = await _filingSvc.HandleReport48HToDecisionInput(filing);

        // Assert
        var tx = result.AddNewTransaction.AddNewTransaction.First();
        Assert.Multiple(() =>
        {
            Assert.That(tx.PhoneNumber, Is.Null.Or.Empty);
            Assert.That(tx.Address1, Is.Null);
            Assert.That(result.AmendmentExplanation.AmendmentExplanation, Is.Null);
        });
    }

    [Test]
    public async Task HandleReport48HToDecisionInput_ShouldMapFirmHiringDateAndAmountCorrectly()
    {
        var filing = new Filing { Id = 2, StatusId = 1 };
        var transaction = new EndOfSessionLobbyingDto
        {
            FirmName = "Firm Y",
            Amount = 2500m,
            DateLobbyingFirmHired = new DateTime(2024, 10, 1)
        };

        _transactionRepository
            .GetAllEndOfSessionLobbyingTransactionsForFiling(filing.Id)
            .Returns([transaction]);

        var result = await _filingSvc.HandleReport48HToDecisionInput(filing);
        var tx = result.AddNewTransaction.AddNewTransaction.First();

        Assert.Multiple(() =>
        {
            Assert.That(tx.Amount, Is.EqualTo(2500m));
            Assert.That(tx.FirmHiringDate, Is.EqualTo(new DateTime(2024, 10, 1)));
        });
    }

    [Test]
    public async Task HandleReport48HToDecisionInput_ShouldHandleMissingAddressOrPhoneGracefully()
    {
        var filing = new Filing { Id = 3, StatusId = 1 };
        var contactId = 456;

        var transaction = new EndOfSessionLobbyingDto
        {
            FirmName = "Firm Z",
            Amount = 5000m,
            Contact = new OrganizationContact { Id = contactId, OrganizationName = "test" }
        };

        _transactionRepository
            .GetAllEndOfSessionLobbyingTransactionsForFiling(filing.Id)
            .Returns([transaction]);

        _filerContactRepository
            .GetFilerContactById(contactId)
            .Returns(new OrganizationContact
            {
                Id = contactId,
                OrganizationName = "Name",
                AddressList = null,
                PhoneNumberList = null
            });

        var result = await _filingSvc.HandleReport48HToDecisionInput(filing);
        var tx = result.AddNewTransaction.AddNewTransaction.First();

        Assert.That(tx.Address1, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(tx.Address1.City, Is.Null); // all should be null
            Assert.That(tx.PhoneNumber, Is.EqualTo(string.Empty));
        });
    }
    #endregion

    #region Filing Amendment
    [Test]
    public void CreateFilingAmendmentAsync_NotFoundRegistration_ShouldThrowError()
    {
        // Arrange
        var filingId = 1;

        _filingRepository.FindEntireFilingById(Arg.Any<long>()).ReturnsNull();

        // Act & Asserts
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _filingSvc.CreateFilingAmendmentAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo($"Filing not found. Id={filingId}"));
    }

    [Test]
    public void CreateFilingAmendmentAsync_FoundNotAcceptedStatus_ReturnResult()
    {
        // Arrange
        var filingId = 1;
        var sampleFiling = _lobbyistEmployerReport;
        sampleFiling.StatusId = FilingStatus.Draft.Id;
        _filingRepository.FindEntireFilingById(Arg.Any<long>()).Returns(Task.FromResult<Filing?>(_lobbyistEmployerReport));

        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => _filingSvc.CreateFilingAmendmentAsync(filingId));
        Assert.That(ex.Message, Is.EqualTo("Only Accepted filings can be amended."));
    }

    [Test]
    public async Task CreateFilingAmendmentAsync_FoundAmendmentExisting_ReturnResult()
    {
        // Arrange
        var filingId = 1;
        var amendmentFilingId = 2;
        var lobbyistReport = GenerateSampleLobbyistReport(filingId);
        var amendmentLobbyistReport = GenerateSampleLobbyistReport(amendmentFilingId, FilingStatus.Draft.Id);

        _filingRepository.FindEntireFilingById(Arg.Any<long>()).Returns(Task.FromResult<Filing?>(lobbyistReport));
        _filingRepository.FindExistingFilingAmendment(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<Filing?>(amendmentLobbyistReport));

        // Act
        var result = await _filingSvc.CreateFilingAmendmentAsync(filingId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<FilingResponseDto>());
            Assert.That(result.Id, Is.EqualTo(amendmentFilingId));
        });
    }

    [Test]
    public async Task CreateFilingAmendmentAsync_ValidRequest_ShouldCreateAmendmentSuccessfully()
    {
        // Arrange
        var filingId = 1;
        var amendmentFilingId = 2;
        var lobbyistReport = GenerateSampleLobbyistReport(filingId);
        var amendmentLobbyistReport = GenerateSampleLobbyistReport(amendmentFilingId, FilingStatus.Draft.Id);

        _filingRepository.FindEntireFilingById(Arg.Any<long>()).Returns(Task.FromResult<Filing?>(lobbyistReport));
        _filingRepository.FindExistingFilingAmendment(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();
        _filingRepository.Create(Arg.Any<Filing>()).Returns(Task.FromResult<Filing>(amendmentLobbyistReport));
        _transactionRepository.Create(Arg.Any<Transaction>()).Returns(Task.FromResult(amendmentLobbyistReport.FilingTransactions[0].Transaction!));
        _transactionRepository.AddTransactionToFiling(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.CompletedTask);
        _filingRepository.GetFilingById(Arg.Any<long>()).Returns(Task.FromResult<Filing?>(amendmentLobbyistReport));
        // Act
        var result = await _filingSvc.CreateFilingAmendmentAsync(filingId);

        // Asserts
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<FilingResponseDto>());
            Assert.That(result.Id, Is.EqualTo(amendmentFilingId));
        });
    }

    #endregion
    [Test]
    public async Task OnTransactionDeletedAsync_NotFoundSummaryType_ShouldDoNothing()
    {
        // Arrange
        var filingId = 1;
        var transaction = new MonetaryContribution
        {
            Id = 1,
            Amount = (Currency)1m,
        };

        // Act
        await _filingSvc.OnTransactionDeletedAsync(transaction, filingId);

        // Assert
        _ = _filingSummaryRepository.Received(0).GetAllByFilingId(Arg.Any<long>());
        _ = _filingSummaryRepository.Received(0).Update(Arg.Any<FilingSummary>());
    }

    [Test]
    public async Task CreateFilingAmendmentAsync_ShouldCloneFilingRelatedFilers()
    {
        // Arrange
        var originalId = 123L;
        var parentId = originalId;
        var filerId = 456L;
        var relatedFilerId1 = 789L;
        var relatedFilerId2 = 790L;

        var originalFiling = new Filing
        {
            Id = originalId,
            StatusId = FilingStatus.Accepted.Id,
            OriginalId = originalId,
            FilerId = filerId,
            Version = 1,
            FilingRelatedFilers = new List<FilingRelatedFiler>
        {
            new() { Id = 1, FilingId = originalId, FilerId = relatedFilerId1, Active = true, Filer = new Filer { Id = relatedFilerId1 } },
            new() { Id = 2, FilingId = originalId, FilerId = relatedFilerId2, Active = true, Filer = new Filer { Id = relatedFilerId2 } }
        }
        };

        _filingRepository.FindEntireFilingById(originalId).Returns(originalFiling);
        _filingRepository.FindExistingFilingAmendment(originalId, FilingStatus.Draft.Id).ReturnsNull();

        var newFilingId = 999L;
        var newAmendment = new Filing
        {
            Id = newFilingId,
            ParentId = parentId,
            OriginalId = originalId,
            Version = 2,
            StatusId = FilingStatus.Draft.Id,
            FilingRelatedFilers = new List<FilingRelatedFiler>
            {
                new() { Id = 0, FilingId = newFilingId, FilerId = relatedFilerId1, Active = true },
                new() { Id = 0, FilingId = newFilingId, FilerId = relatedFilerId2, Active = true }
            }
        };

        _filingRepository.Create(Arg.Any<Filing>()).Returns(callInfo =>
        {
            var filing = callInfo.Arg<Filing>();
            filing.Id = newFilingId;
            return filing;
        });

        _filingRepository.GetFilingById(newFilingId).Returns(newAmendment);

        // Act
        var result = await _filingSvc.CreateFilingAmendmentAsync(originalId);

        // Assert
        await _filingRepository.Received(1).FindEntireFilingById(originalId);
        await _filingRepository.Received(1).Create(Arg.Is<Filing>(f =>
            f.ParentId == parentId &&
            f.OriginalId == originalId &&
            f.Version == 2 &&
            f.StatusId == FilingStatus.Draft.Id &&
            f.FilingRelatedFilers != null &&
            f.FilingRelatedFilers.Count == 2 &&
            f.FilingRelatedFilers.All(frf => frf.Id == 0) && // All IDs should be reset to 0
            f.FilingRelatedFilers.Any(frf => frf.FilerId == relatedFilerId1) &&
            f.FilingRelatedFilers.Any(frf => frf.FilerId == relatedFilerId2)
        ));

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(newFilingId));
            Assert.That(result.Version, Is.EqualTo(2));
        });
    }

    [Test]
    public async Task CreateFilingAmendmentAsync_ShouldCloneFilingSummariesIncludingNonRegisteredLobbyists()
    {
        // Arrange
        var originalFiling = new Filing
        {
            Id = 100,
            StatusId = FilingStatus.Accepted.Id,
            Version = 1,
            FilingSummaries = new List<FilingSummary>
            {
                new()
                {
                    Id = 201,
                    FilingSummaryTypeId = FilingSummaryType.PaymentsToInHouseLobbyists.Id,
                    FilingSummaryStatusId = FilingSummaryStatus.Completed.Id,
                    PeriodAmount = 1000m,
                    ToDateAmount = 5000m,
                    NonRegisteredLobbyists = new List<DisclosureFilingNonRegisteredLobbyist>
                    {
                        new()
                        {
                            Id = 301,
                            FirstName = "John",
                            LastName = "Doe",
                            FilingSummaryId = 201
                        },
                        new()
                        {
                            Id = 302,
                            FirstName = "Jane",
                            LastName = "Smith",
                            FilingSummaryId = 201
                        }
                    }
                },
                new()
                {
                    Id = 202,
                    FilingSummaryTypeId = FilingSummaryType.CampaignContributionSummary.Id,
                    FilingSummaryStatusId = FilingSummaryStatus.Completed.Id,
                    PeriodAmount = 2000m,
                    ToDateAmount = 7000m,
                    NonRegisteredLobbyists = new List<DisclosureFilingNonRegisteredLobbyist>()
                }
            },
            FilingTransactions = new List<FilingTransaction>(),
            ActionsLobbied = new List<ActionsLobbied?>(),
            FilingRelatedFilers = new List<FilingRelatedFiler>()
        };

        _filingRepository.FindEntireFilingById(100).Returns(originalFiling);
        _filingRepository.FindExistingFilingAmendment(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();
        _authorizationSvc.GetInitiatingUserId().Returns(999);
        _filingRepository.Create(Arg.Any<Filing>()).Returns(callInfo =>
        {
            var filing = callInfo.Arg<Filing>();
            filing.Id = 105; // Give it a new ID
            return filing;
        });

        var newAmendment = new Filing
        {
            Id = 105,
            ParentId = 100,
            OriginalId = 100,
            Version = 2,
            StatusId = FilingStatus.Draft.Id
        };

        _filingRepository.GetFilingById(105).Returns(newAmendment);

        // Act
        var result = await _filingSvc.CreateFilingAmendmentAsync(100);

        // Assert
        await _filingRepository.Received(1).FindEntireFilingById(100);
        await _filingRepository.Received(1).FindExistingFilingAmendment(100, FilingStatus.Draft.Id);

        await _filingRepository.Received(1).Create(Arg.Do<Filing>(filing =>
        {
            Assert.Multiple(() =>
            {
                Assert.That(filing.OriginalId, Is.EqualTo(100));
                Assert.That(filing.ParentId, Is.EqualTo(100));
                Assert.That(filing.Version, Is.EqualTo(2));
                Assert.That(filing.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
                Assert.That(filing.FilingSummaries, Has.Count.EqualTo(3)); // 2 original + 1 amendment explanation
            });

            // Verify the amendment explanation filing summary was added
            var amendmentExplanation = filing.FilingSummaries.SingleOrDefault(fs =>
                fs.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id);
            Assert.That(amendmentExplanation, Is.Not.Null);
            Assert.That(amendmentExplanation.FilingSummaryStatusId, Is.EqualTo(FilingSummaryStatus.NotStarted.Id));

            // Verify non-registered lobbyists were cloned
            var paymentsToInHouseLobbyistsSummary = filing.FilingSummaries.SingleOrDefault(fs =>
                fs.FilingSummaryTypeId == FilingSummaryType.PaymentsToInHouseLobbyists.Id);
            Assert.That(paymentsToInHouseLobbyistsSummary, Is.Not.Null);
            Assert.That(paymentsToInHouseLobbyistsSummary.NonRegisteredLobbyists, Has.Count.EqualTo(2));

            // Verify non-registered lobbyist properties
            var lobbyist1 = paymentsToInHouseLobbyistsSummary.NonRegisteredLobbyists[0];
            Assert.Multiple(() =>
            {
                Assert.That(lobbyist1.Id, Is.EqualTo(0)); // ID should be reset to 0
                Assert.That(lobbyist1.FilingSummaryId, Is.EqualTo(0)); // FilingSummaryId should be reset to 0
                Assert.That(lobbyist1.FirstName, Is.EqualTo("John"));
                Assert.That(lobbyist1.LastName, Is.EqualTo("Doe"));
            });

            var lobbyist2 = paymentsToInHouseLobbyistsSummary.NonRegisteredLobbyists[1];
            Assert.Multiple(() =>
            {
                Assert.That(lobbyist2.Id, Is.EqualTo(0));
                Assert.That(lobbyist2.FilingSummaryId, Is.EqualTo(0));
                Assert.That(lobbyist2.FirstName, Is.EqualTo("Jane"));
                Assert.That(lobbyist2.LastName, Is.EqualTo("Smith"));
            });

            // Verify other filing summary was cloned correctly
            var campaignContributionSummary = filing.FilingSummaries.SingleOrDefault(fs =>
                fs.FilingSummaryTypeId == FilingSummaryType.CampaignContributionSummary.Id);
            Assert.That(campaignContributionSummary, Is.Not.Null);
            Assert.That(campaignContributionSummary.NonRegisteredLobbyists, Is.Empty);
        }));

        Assert.That(result.Id, Is.EqualTo(105));
    }

    [Test]
    public void CloneFilingSummaries_ClonesAllSummariesExceptAmendmentExplanation_AndResetsIds()
    {
        // Arrange
        var methodInfo = typeof(FilingSvc).GetMethod("CloneFilingSummaries",
            BindingFlags.NonPublic | BindingFlags.Static) ?? throw new InvalidOperationException("The method 'CloneFilingSummaries' could not be found.");

        var originalSummaries = new List<FilingSummary>
        {
            new()
            {
                Id = 1,
                FilingId = 100,
                FilingSummaryTypeId = FilingSummaryType.ActivityExpenseSummary.Id,
                FilingSummaryStatusId = FilingSummaryStatus.InProgress.Id,
                PeriodAmount = 500m,
                ToDateAmount = 1000m,
                NonRegisteredLobbyists = new List<DisclosureFilingNonRegisteredLobbyist>
                {
                    new()
                    {
                        Id = 50,
                        FilingSummaryId = 1,
                        FirstName = "Test",
                        LastName = "Lobbyist"
                    }
                }
            },
            new()
            {
                Id = 2,
                FilingId = 100,
                FilingSummaryTypeId = FilingSummaryType.CampaignContributionSummary.Id,
                FilingSummaryStatusId = FilingSummaryStatus.Completed.Id,
                PeriodAmount = 1500m,
                ToDateAmount = 3000m
            },
            new()
            {
                Id = 3,
                FilingId = 100,
                FilingSummaryTypeId = FilingSummaryType.AmendmentExplanation.Id,
                FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
                PeriodAmount = 0m,
                ToDateAmount = 0m
            }
        };

        // Act
        var result = methodInfo.Invoke(null, [originalSummaries]) as List<FilingSummary>;

        // Assert
        Assert.That(result, Is.Not.Null);

        // Should have 2 summaries (not 3) because AmendmentExplanation should be filtered out
        Assert.That(result!, Has.Count.EqualTo(2));

        Assert.Multiple(() =>
        {
            Assert.That(result.All(fs => fs.Id == 0), Is.True);
            Assert.That(result.All(fs => fs.FilingId == 0), Is.True);
            Assert.That(result.All(fs => fs.Filing == null), Is.True);
        });

        var firstSummary = result.First();
        Assert.Multiple(() =>
        {
            Assert.That(firstSummary.FilingSummaryTypeId, Is.EqualTo(FilingSummaryType.ActivityExpenseSummary.Id));
            Assert.That(firstSummary.PeriodAmount, Is.EqualTo(500m));
            Assert.That(firstSummary.ToDateAmount, Is.EqualTo(1000m));
            Assert.That(firstSummary.NonRegisteredLobbyists, Is.Not.Null);
            Assert.That(firstSummary.NonRegisteredLobbyists, Has.Count.EqualTo(1));
            Assert.That(firstSummary.NonRegisteredLobbyists[0].Id, Is.EqualTo(0));
            Assert.That(firstSummary.NonRegisteredLobbyists[0].FilingSummaryId, Is.EqualTo(0));
            Assert.That(firstSummary.NonRegisteredLobbyists[0].FirstName, Is.EqualTo("Test"));
            Assert.That(firstSummary.NonRegisteredLobbyists[0].LastName, Is.EqualTo("Lobbyist"));
        });

        var secondSummary = result[1];
        Assert.Multiple(() =>
        {
            Assert.That(secondSummary.FilingSummaryTypeId, Is.EqualTo(FilingSummaryType.CampaignContributionSummary.Id));
            Assert.That(secondSummary.PeriodAmount, Is.EqualTo(1500m));
            Assert.That(secondSummary.ToDateAmount, Is.EqualTo(3000m));
            Assert.That(result.Any(fs => fs.FilingSummaryTypeId == FilingSummaryType.AmendmentExplanation.Id), Is.False);
        });
    }

    [Test]
    public async Task OnTransactionDeletedAsync_NotFoundFilingSummary_ShouldDoNothing()
    {
        // Arrange
        var filingId = 1;
        var transaction = new PaymentMade
        {
            Id = 1,
            Amount = (Currency)1m,
            ExpenditureCodeId = 1,
            ExpenditureCodeDescription = "Description",
        };

        var filingSummaries = new List<FilingSummary>
        {
            new ()
            {
                Id = 1,
                FilingSummaryTypeId = 1,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            }
        };
        _filingSummaryRepository.GetAllByFilingId(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<FilingSummary>>(filingSummaries));

        // Act
        await _filingSvc.OnTransactionDeletedAsync(transaction, filingId);

        // Assert
        _ = _filingSummaryRepository.Received(1).GetAllByFilingId(Arg.Any<long>());
        _ = _filingSummaryRepository.Received(0).Update(Arg.Any<FilingSummary>());
    }

    [TestCaseSource(nameof(OnTransactionDeletedAsyncTestCases))]
    public async Task OnTransactionDeletedAsync_ValidRequest_ShouldExecuteSuccessfully(Transaction transaction)
    {
        // Arrange
        var filingId = 1;
        var filingSummaries = new List<FilingSummary>
        {
            new ()
            {
                Id = 1,
                FilingSummaryTypeId = FilingSummaryType.PaymentReceivedSummary.Id,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            },
            new ()
            {
                Id = 2,
                FilingSummaryTypeId = FilingSummaryType.PaymentMadeSummary.Id,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            },
            new ()
            {
                Id = 3,
                FilingSummaryTypeId = FilingSummaryType.PaymentMadeByAgentOrIndependentContractorSummary.Id,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            },
            new ()
            {
                Id = 4,
                FilingSummaryTypeId = FilingSummaryType.PersonReceiving1000OrMoreSummary.Id,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            },
        };
        _filingSummaryRepository.GetAllByFilingId(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<FilingSummary>>(filingSummaries));

        // Act
        await _filingSvc.OnTransactionDeletedAsync(transaction, filingId);

        // Assert
        _ = _filingSummaryRepository.Received(1).GetAllByFilingId(Arg.Any<long>());
        _ = _filingSummaryRepository.Received(1).Update(Arg.Any<FilingSummary>());
    }

    #region LobbyistEmployerActionsLobbied

    [Test]
    public async Task ValidateLobbyistEmployerActionsLobbiedAgencies_ShouldReturnFormattedValidationErrors()
    {
        // Arrange
        var request = new List<ActionsLobbiedRequestDto>
        {
            new()
            {
                AgencyId = 42,
                AgencyDescription = "Test Agency",
                AdministrativeAction = "Action",
            }
        };

        var decisionsErrors = new List<WorkFlowError>
        {
            new("AgencyName0", "Code", "Validation", "Agency name is required"),
        };

        _decisionsSvc
            .InitiateWorkflow<List<DecisionsLobbyistEmployerReportActionsLobbiedAgencies>, List<WorkFlowError>>(
                DecisionsWorkflow.LobbyistEmployerReportActionsLobbiedAgenciesRuleSet,
                Arg.Any<List<DecisionsLobbyistEmployerReportActionsLobbiedAgencies>>(),
                true)
            .Returns(decisionsErrors);

        // Act
        var result = await _filingSvc.ValidateLobbyistEmployerActionsLobbiedAgencies(request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result[0].FieldName, Is.EqualTo("AgencyName0"));
            Assert.That(result[0].Message, Is.EqualTo("Agency name is required"));
        });
    }

    [Test]
    public async Task UpdateLobbyistEmployerReportActionsLobbied_ShouldUpdateAndReturnDto_WhenValid()
    {
        // Arrange
        long reportId = 1;
        var request = new LobbyistEmployerActionsLobbiedRequest
        {
            OtherActionsLobbied = "updated",
            AdministrativeActions = new List<ActionsLobbiedRequestDto>
            {
                new() { AgencyId = 123, OfficialPositionDescription = "Test Action" }
            },
            AssemblyBills = new List<ActionsLobbiedRequestDto>(),
            SenateBills = new List<ActionsLobbiedRequestDto>()
        };

        var report = new LobbyistEmployerReport
        {
            Id = reportId,
            FilerId = 200,
            StatusId = FilingStatus.Draft.Id,
            ParentId = 1,
            Version = 1,
            SubmittedDate = _dateNow
        };

        _filingRepository.FindById(reportId).Returns(report);
        _dependencies.FilingSummaryRepository.GetAllByFilingId(reportId).Returns(new List<FilingSummary>
        {
            new ()
            {
                FilingSummaryTypeId = FilingSummaryType.ActionsLobbiedSummary.Id,
                PeriodAmount = 0,
                ToDateAmount = null,
                Id = 1,
                FilingId = 1,
                FilingSummaryStatus = new FilingSummaryStatus(1, "Not Started")
            }
        });

        _decisionsSvc.InitiateWorkflow<DecisionsLobbyistEmployerReportActionsLobbied, List<WorkFlowError>>(
            DecisionsWorkflow.LobbyistEmployerReportActionsLobbiedRuleSet,
            Arg.Any<DecisionsLobbyistEmployerReportActionsLobbied>(),
            true).Returns(new List<WorkFlowError>());

        _actionsLobbiedSvc
            .UpsertActionsLobbiedForFiling(reportId, Arg.Any<List<ActionsLobbiedRequestDto>>())
            .Returns(new List<ActionsLobbiedResponseDto>());

        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(Arg.Any<long>()).Returns(new ActionsLobbiedByEntityResponse());

        _filingRepository.Update(Arg.Any<LobbyistEmployerReport>()).Returns(report);

        // Act
        var result = await _filingSvc.UpdateLobbyistEmployerReportActionsLobbied(reportId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.FilingId, Is.EqualTo(reportId));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.OtherActionsLobbied, Is.EqualTo("updated"));
        });

        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r => r.OtherActionsLobbied == "updated"));
    }

    [Test]
    public void UpdateLobbyistEmployerReportActionsLobbied_ShouldThrow_WhenNotFound()
    {
        // Arrange
        _filingRepository.FindById(Arg.Any<long>()).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() =>
            _filingSvc.UpdateLobbyistEmployerReportActionsLobbied(1, new LobbyistEmployerActionsLobbiedRequest()));
        Assert.That(ex?.Message, Does.Contain("Lobbyist Employer Report not found"));
    }

    [Test]
    public async Task GetActionsLobbiedSummaryForFiling_ShouldReturnResponse_WhenFilingExists()
    {
        // Arrange
        long filingId = 1;
        var filing = new LobbyistEmployerReport
        {
            StatusId = 1,
            Id = filingId,
            OtherActionsLobbied = "Test Actions"
        };

        var filingSummaries = new List<FilingSummary>
        {
            new()
            {
                FilingId = filingId,
                FilingSummaryTypeId = FilingSummaryType.ActionsLobbiedSummary.Id,
                PeriodAmount = 0,
                ToDateAmount = 0
            }
        };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };

        _filingRepository.FindById(filingId).Returns(filing);
        _filingSummaryRepository.GetAllByFilingId(filingId).Returns(filingSummaries);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        // Act
        var result = await _filingSvc.GetActionsLobbiedSummaryForFiling(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.FilingId, Is.EqualTo(filingId));
            Assert.That(result.OtherActionsLobbied, Is.EqualTo("Test Actions"));
            Assert.That(result.AdministrativeActions, Is.Not.Null);
        });
    }

    [Test]
    public void GetActionsLobbiedSummaryForFiling_ShouldThrow_WhenFilingIsNull()
    {
        // Arrange
        long filingId = 999;
        _filingRepository.FindById(filingId).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _filingSvc.GetActionsLobbiedSummaryForFiling(filingId));
        Assert.That(ex?.Message, Does.Contain($"Report not found. Id={filingId}"));
    }

    [Test]
    public void GetActionsLobbiedSummaryForFiling_ShouldThrow_WhenFilingActionsLobbiedIsNull()
    {
        // Arrange
        long filingId = 1;
        var filing = new LobbyistEmployerReport
        {
            Id = filingId,
            OtherActionsLobbied = "Test Actions",
            StatusId = 1
        };

        _filingRepository.FindById(filingId).Returns(filing);
        _filingSummaryRepository.GetAllByFilingId(filingId).Returns(new List<FilingSummary>());

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _filingSvc.GetActionsLobbiedSummaryForFiling(filingId));
        Assert.That(ex?.Message, Does.Contain($"Actions Lobbied Filing Summary not found for Filing Id={filingId}"));
    }

    #endregion

    private static IEnumerable<object[]> OnTransactionDeletedAsyncTestCases()
    {
        yield return new object[] { GenerateTransactions().FirstOrDefault(x => x.Id == 1)! };
        yield return new object[] { GenerateTransactions().FirstOrDefault(x => x.Id == 2)! };
        yield return new object[] { GenerateTransactions().FirstOrDefault(x => x.Id == 3)! };
        yield return new object[] { GenerateTransactions().FirstOrDefault(x => x.Id == 4)! };
    }

    private static List<Transaction> GenerateTransactions()
    {
        return new List<Transaction>
        {
            new PaymentReceived
            {
                Id = 1,
                Amount = (Currency)1m,
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            },
            new PaymentMade
            {
                Id = 2,
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Amount = (Currency)1m,
                ExpenditureCodeId = 1,
                ExpenditureCodeDescription = "Description",

            },
            new PaymentMade
            {
                Id = 3,
                Amount = (Currency)1m,
                ExpenditureCodeDescription = "Description",
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                ExpenditureCodeId = 1,
                AgentOrIndependentContractorName = "AgentName",
            },
            new PersonReceiving1000OrMore
            {
                Id = 4,
                Active = true,
                TransactionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
                Amount = (Currency)1m,
            }
        };
    }

    [Test]
    public async Task UpdatePucActivityPayment_WorkflowValidAndFilingExists_ReturnsValidResponse()
    {
        // Arrange
        var filingId = 100;
        var request = new UpdatePucActivityPaymentRequestDto()
        {
            TotalPaymentsPucActivity = 1500
        };

        var filing = new Filing { Id = filingId, TotalPaymentsPucActivity = 0, StatusId = 1 };

        _decisionsSvc
            .InitiateWorkflow<DecisionsTotalPaymentsPucActivity, List<WorkFlowError>>(
                DecisionsWorkflow.FDLOBTransactionLobbyistEmployerReportPUCActivity,
                Arg.Any<DecisionsTotalPaymentsPucActivity>(),
                Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _filingRepository.GetFilingById(filingId)
            .Returns(filing);

        _filingRepository.Update(Arg.Any<Filing>())
            .Returns(filing);

        // Act
        var result = await _filingSvc.UpdatePucActivityPayment(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(filing.TotalPaymentsPucActivity, Is.EqualTo(1500));
        });
    }

    [Test]
    public async Task UpdatePucActivityPayment_WorkflowReturnsErrors_ReturnsInvalidResponse()
    {
        // Arrange
        var filingId = 200;
        var request = new UpdatePucActivityPaymentRequestDto
        {
            TotalPaymentsPucActivity = 999
        };

        var workflowErrors = new List<WorkFlowError>
        {
            new("123", "123", "TotalPaymentsPucActivity", "Invalid value")
        };

        _decisionsSvc
            .InitiateWorkflow<DecisionsTotalPaymentsPucActivity, List<WorkFlowError>>(
                DecisionsWorkflow.FDLOBTransactionLobbyistEmployerReportPUCActivity,
                Arg.Any<DecisionsTotalPaymentsPucActivity>(),
                Arg.Any<bool>())
            .Returns(workflowErrors);

        // Act
        var result = await _filingSvc.UpdatePucActivityPayment(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EquivalentTo(workflowErrors));
        });

        await _filingRepository.DidNotReceive().GetFilingById(Arg.Any<long>());
        await _filingRepository.DidNotReceive().Update(Arg.Any<Filing>());
    }

    [Test]
    public void UpdatePucActivityPayment_FilingNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        var filingId = 300;
        var request = new UpdatePucActivityPaymentRequestDto
        {
            TotalPaymentsPucActivity = 2500
        };

        _decisionsSvc
            .InitiateWorkflow<DecisionsTotalPaymentsPucActivity, List<WorkFlowError>>(
                DecisionsWorkflow.FDLOBTransactionLobbyistEmployerReportPUCActivity,
                Arg.Any<DecisionsTotalPaymentsPucActivity>(),
                Arg.Any<bool>())
            .Returns(new List<WorkFlowError>());

        _filingRepository.GetFilingById(filingId)
            .Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.UpdatePucActivityPayment(filingId, request));

        Assert.That(ex!.Message, Does.Contain($"Filing not found Id={filingId}"));
    }

    [Test]
    public async Task GetCumulativePeriodStartByFilingId_ReturnsLegislativeStartDate_WhenNoEarlierFilingsExist()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var filingDate = new DateTime(2023, 5, 1, 0, 0, 0, 0); // Date in an odd year
        var expectedLegislativeStartDate = new DateTime(2023, 1, 1, 0, 0, 0, 0);

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = filingDate,
            EndDate = filingDate.AddMonths(3),
            StatusId = FilingStatus.Draft.Id
        };

        _ = _filingRepository.GetFilingById(filingId).Returns(filing);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing> { filing });

        // Act
        var result = await _filingSvc.GetCumulativePeriodStartByFilingId(filingId);

        // Assert
        Assert.That(result, Is.EqualTo(expectedLegislativeStartDate));
    }

    [Test]
    public async Task GetCumulativePeriodStartByFilingId_ReturnsEarliestFilingStartDate_WhenEarlierFilingsExist()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var currentFilingDate = new DateTime(2023, 5, 1, 0, 0, 0, 0);

        // Earlier filing in the same legislative session
        var earliestFilingDate = new DateTime(2023, 2, 15, 0, 0, 0, 0);
        var legislativeStartDate = new DateTime(2023, 1, 1, 0, 0, 0, 0);

        var currentFiling = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = currentFilingDate,
            EndDate = currentFilingDate.AddMonths(3),
            StatusId = FilingStatus.Draft.Id
        };

        var earliestFiling = new Filing
        {
            Id = 124L,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = earliestFilingDate,
            EndDate = earliestFilingDate.AddMonths(2),
            StatusId = FilingStatus.Accepted.Id
        };

        var inBetweenFiling = new Filing
        {
            Id = 125L,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = earliestFilingDate.AddMonths(1),
            EndDate = currentFilingDate,
            StatusId = FilingStatus.Accepted.Id
        };

        _ = _filingRepository.GetFilingById(filingId).Returns(currentFiling);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing> { currentFiling, earliestFiling, inBetweenFiling });

        // Act
        var result = await _filingSvc.GetCumulativePeriodStartByFilingId(filingId);

        // Assert
        Assert.That(result, Is.EqualTo(earliestFilingDate));
    }

    [Test]
    public async Task GetCumulativePeriodStartByFilingId_ReturnsLegislativeStartDate_WhenOnlyNonAcceptedFilingsExist()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var filingDate = new DateTime(2024, 5, 1, 0, 0, 0, 0); // Date in an even year
        var expectedLegislativeStartDate = new DateTime(2023, 1, 1, 0, 0, 0, 0); // Legislative start is previous odd year

        var currentFiling = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = filingDate,
            EndDate = filingDate.AddMonths(3),
            StatusId = FilingStatus.Draft.Id
        };

        var earlierDraftFiling = new Filing
        {
            Id = 124L,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = filingDate.AddMonths(-1),
            EndDate = filingDate,
            // Not Accepted status
            StatusId = FilingStatus.Draft.Id
        };

        _ = _filingRepository.GetFilingById(filingId).Returns(currentFiling);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing> { currentFiling, earlierDraftFiling });

        // Act
        var result = await _filingSvc.GetCumulativePeriodStartByFilingId(filingId);

        // Assert
        Assert.That(result, Is.EqualTo(expectedLegislativeStartDate));
    }

    [Test]
    public async Task GetCumulativePeriodStartByFilingId_ReturnsLegislativeStartDate_WhenOnlyIncompleteRelevantFilingExists()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var filingDate = new DateTime(2024, 5, 1, 0, 0, 0, 0);
        var expectedLegislativeStartDate = new DateTime(2023, 1, 1, 0, 0, 0, 0);

        var currentFiling = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = filingDate,
            EndDate = filingDate.AddMonths(3),
            StatusId = FilingStatus.Draft.Id
        };

        // Filing of different type (shouldn't be considered)
        var differentTypeFiling = new Filing
        {
            Id = 124L,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StartDate = filingDate.AddMonths(-2),
            EndDate = filingDate.AddMonths(-1),
            StatusId = FilingStatus.Accepted.Id
        };

        _ = _filingRepository.GetFilingById(filingId).Returns(currentFiling);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing> { currentFiling, differentTypeFiling });

        // Act
        var result = await _filingSvc.GetCumulativePeriodStartByFilingId(filingId);

        // Assert
        Assert.That(result, Is.EqualTo(expectedLegislativeStartDate));
    }

    [Test]
    public async Task GetCumulativePeriodStartByFilingId_ReturnsEarliestAcceptedFilingDate_FromCorrectLegislativePeriod()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var currentYear = 2023;
        var filingDate = new DateTime(currentYear, 6, 1, 0, 0, 0, 0);

        var earliestFilingDate = new DateTime(currentYear, 1, 15, 0, 0, 0, 0);
        var previousSessionDate = new DateTime(currentYear - 2, 5, 1, 0, 0, 0, 0);

        var currentFiling = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = filingDate,
            EndDate = filingDate.AddMonths(3),
            StatusId = FilingStatus.Draft.Id
        };

        var earliestFiling = new Filing
        {
            Id = 124L,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = earliestFilingDate,
            EndDate = earliestFilingDate.AddMonths(2),
            StatusId = FilingStatus.Accepted.Id
        };

        // This filing is from previous legislative session - should be ignored
        var previousSessionFiling = new Filing
        {
            Id = 125L,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StartDate = previousSessionDate,
            EndDate = previousSessionDate.AddMonths(3),
            StatusId = FilingStatus.Accepted.Id
        };

        _ = _filingRepository.GetFilingById(filingId).Returns(currentFiling);
        _ = _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing> { currentFiling, earliestFiling, previousSessionFiling });

        // Act
        var result = await _filingSvc.GetCumulativePeriodStartByFilingId(filingId);

        // Assert
        Assert.That(result, Is.EqualTo(earliestFilingDate));
    }

    [Test]
    public void GetCumulativePeriodStartByFilingId_ThrowsKeyNotFoundException_WhenFilingNotFound()
    {
        // Arrange
        var filingId = 999L;
        _ = _filingRepository.GetFilingById(filingId).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () => await _filingSvc.GetCumulativePeriodStartByFilingId(filingId));
        Assert.That(ex?.Message, Does.Contain($"Filing not found. Id={filingId}"));
    }

    [Test]
    public async Task GetActionsLobbiedSummaryForFiling_ShouldReturnResponse_WhenReportIs72H()
    {
        // Arrange
        long filingId = 72;
        var report72H = new Report72H
        {
            Id = filingId,
            StatusId = 1,
            OtherActionsLobbied = "72H Emergency Lobbying"
        };

        var filingSummaries = new List<FilingSummary>
        {
            new()
            {
                Id = 123,
                FilingId = filingId,
                FilingSummaryTypeId = FilingSummaryType.ActionsLobbiedSummary.Id,
                PeriodAmount = 0,
                ToDateAmount = 0,
                FilingSummaryStatus = new FilingSummaryStatus(1, "Draft")
            }
        };

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>
            {
                new()
            },
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>
            {
                new()
            },
            SenateBillActions = new List<ActionsLobbiedResponseDto>
            {
                new()
            }
        };

        _filingRepository.FindById(filingId).Returns(report72H);
        _filingSummaryRepository.GetAllByFilingId(filingId).Returns(filingSummaries);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        // Act
        var result = await _filingSvc.GetActionsLobbiedSummaryForFiling(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.FilingId, Is.EqualTo(filingId));
            Assert.That(result.OtherActionsLobbied, Is.EqualTo("72H Emergency Lobbying"));
            Assert.That(result.AdministrativeActions, Is.Not.Null);
            Assert.That(result.AssemblyBillActions, Is.Not.Null);
            Assert.That(result.SenateBillActions, Is.Not.Null);
            Assert.That(result.FilingSummaryId, Is.EqualTo(123));
            Assert.That(result.Valid, Is.True);
            Assert.That(result.FilingSummaryStatus?.Name, Is.EqualTo("Draft"));
        });
    }

    [Test]
    public async Task SendForAttestationReport48H_ShouldReturnValidResponse_WhenNoErrors()
    {
        // Arrange
        const long filingId = 123L;
        var request = new Report48HSendForAttestationRequest
        {
            SelectedResponsibleOfficerIds = new List<long> { 101, 102 }
        };

        var report48H = new Report48H
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id
        };

        var decisionResponse = new DecisionsSubmitReport48HResponse
        {
            Errors = new List<WorkFlowError>(),
            Notifications = new List<NotificationTrigger>
        {
            new(true, 1, DateTime.UtcNow.AddDays(1))
        }
        };

        _filingRepository.FindById(filingId).Returns(report48H);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourReportSendForAttestation,
            Arg.Any<DecisionsSubmitReport48H>(),
            true
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport48H(filingId, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
        });

        await _filingRepository.Received(1).Update(Arg.Is<Report48H>(r => r.StatusId == FilingStatus.Pending.Id));
        await _notificationSvc.Received(2).SendUserNotification(Arg.Any<SendUserNotificationRequest>());
    }

    [Test]
    public async Task SendForAttestationReport48H_ShouldReturnInvalidResponse_WhenValidationErrorsExist()
    {
        // Arrange
        const long filingId = 123L;
        var request = new Report48HSendForAttestationRequest
        {
            SelectedResponsibleOfficerIds = new List<long> { 101, 102 }
        };

        var report48H = new Report48H
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id
        };

        var validationErrors = new List<WorkFlowError>
    {
        new("field1", "code1", "error", "message1"),
        new("field2", "code2", "error", "message2")
    };

        var decisionResponse = new DecisionsSubmitReport48HResponse
        {
            Errors = validationErrors
        };

        _filingRepository.FindById(filingId).Returns(report48H);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourReportSendForAttestation,
            Arg.Any<DecisionsSubmitReport48H>(),
            true
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport48H(filingId, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(2));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id)); // Should not change status
        });

        await _filingRepository.DidNotReceive().Update(Arg.Any<Report48H>());
        await _notificationSvc.DidNotReceive().SendUserNotification(Arg.Any<SendUserNotificationRequest>());
    }

    [Test]
    public void SendForAttestationReport48H_ShouldThrowKeyNotFoundException_WhenReportNotFound()
    {
        // Arrange
        const long filingId = 123L;
        var request = new Report48HSendForAttestationRequest
        {
            SelectedResponsibleOfficerIds = new List<long> { 101, 102 }
        };

        _filingRepository.FindById(filingId).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.SendForAttestationReport48H(filingId, request));

        Assert.That(ex.Message, Does.Contain($"48H Report not found Id={filingId}"));
    }

    [Test]
    public async Task SendForAttestationReport48H_ShouldUseAmendWorkflow_WhenFilingVersionGreaterThanZero()
    {
        // Arrange
        var filingId = 1234L;
        var filerId = 5678L;

        var report = new Report48H
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            Version = 1 // <-- triggers the amend workflow
        };

        var request = new Report48HSendForAttestationRequest
        {
            SelectedResponsibleOfficerIds = new List<long> { 1001, 1002 }
        };

        var endOfSessionTransactions = new List<EndOfSessionLobbyingDto>
    {
        new()
        {
            FirmName = "Test",
            Amount = new Currency(200),
            Contact = new OrganizationContact { OrganizationName = "Test Org" }
        }
    };

        var decisionResponse = new DecisionsSubmitReport48HResponse
        {
            Errors = [],
            Notifications = new List<NotificationTrigger>()
        };

        _filingRepository.FindById(filingId).Returns(report);
        _transactionRepository.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId).Returns(endOfSessionTransactions);

        _decisionsSvc
            .InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
                DecisionsWorkflow.FDLOBFiling48HourAmendReportSendForAttestation,
                Arg.Any<DecisionsSubmitReport48H>(),
                true)
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport48H(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        await _decisionsSvc.Received(1).InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourAmendReportSendForAttestation,
            Arg.Any<DecisionsSubmitReport48H>(),
            true);

        await _filingRepository.Received(1).Update(Arg.Is<Report48H>(r => r.StatusId == FilingStatus.Pending.Id));
    }


    [Test]
    public async Task GetResponsibleOfficers_ReturnsExpectedOfficers_WhenFilingTypeIsReport72H()
    {
        // Arrange
        long filingId = 100;
        long filerId = 200;

        var filingType = new FilingType { Name = FilingType.Report72h.Name };
        var filing = new Filing
        {
            FilerId = filerId,
            StartDate = _dateNow,
            StatusId = FilingStatus.Pending.Id,
            EndDate = _dateNow.AddDays(1),
            FilingTypeId = FilingType.Report72h.Id,
            FilingType = FilingType.Report72h
        };

        var officerUser = new FilerUser
        {
            Id = 1,
            FilerRole = new FilerRole { Id = 1, Description = "test", Name = FilerRole.Disclosure_Lobbying72H_Attest_ResponsibleOfficer.Name },
            User = new User { FirstName = "Jane", LastName = "Doe", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        var authorizedUser = new FilerUser
        {
            Id = 2,
            FilerRole = new FilerRole { Id = 2, Description = "test", Name = FilerRole.Disclosure_Lobbying72H_Attest_AccountManager.Name },
            User = new User { FirstName = "John", LastName = "Smith", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        var unrelatedUser = new FilerUser
        {
            Id = 3,
            FilerRole = new FilerRole { Id = 3, Description = "test", Name = "UnrelatedRole" },
            User = new User { FirstName = "Ghost", LastName = "User", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        _filingRepository.GetFilingById(filingId).Returns(filing);
        _filerUserRepository.FindFilerUsersByFilerId(filerId)
            .Returns(new List<FilerUser> { officerUser, authorizedUser, unrelatedUser });

        // Act
        var result = await _filingSvc.GetResponsibleOfficers(filingId);

        // Assert
        var resultList = result.ToList();
        Assert.Multiple(() =>
        {
            Assert.That(resultList, Has.Count.EqualTo(2));
            Assert.That(resultList.Any(x => x.FirstName == "Jane" && x.LastName == "Doe"), Is.True);
            Assert.That(resultList.Any(x => x.FirstName == "John" && x.LastName == "Smith"), Is.True);
            Assert.That(resultList.All(x => x.Role == FilerRole.Disclosure_Lobbying72H_Attest_AccountManager.Name ||
                                            x.Role == FilerRole.Disclosure_Lobbying72H_Attest_ResponsibleOfficer.Name), Is.True);
        });
    }

    [Test]
    public async Task GetResponsibleOfficers_ReturnsExpectedOfficers_WhenFilingTypeIsReport48H()
    {
        // Arrange
        long filingId = 100;
        long filerId = 200;

        var filingType = new FilingType { Name = FilingType.Report48h.Name };
        var filing = new Filing
        {
            FilerId = filerId,
            StartDate = _dateNow,
            StatusId = FilingStatus.Pending.Id,
            EndDate = _dateNow.AddDays(1),
            FilingTypeId = FilingType.Report48h.Id,
            FilingType = FilingType.Report48h
        };

        var officerUser = new FilerUser
        {
            Id = 1,
            FilerRole = new FilerRole { Id = 1, Description = "test", Name = FilerRole.Disclosure_Lobbying48H_Attest_ResponsibleOfficer.Name },
            User = new User { FirstName = "Jane", LastName = "Doe", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        var authorizedUser = new FilerUser
        {
            Id = 2,
            FilerRole = new FilerRole { Id = 2, Description = "test", Name = FilerRole.Disclosure_Lobbying48H_Attest_AccountManager.Name },
            User = new User { FirstName = "John", LastName = "Smith", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        var unrelatedUser = new FilerUser
        {
            Id = 3,
            FilerRole = new FilerRole { Id = 3, Description = "test", Name = "UnrelatedRole" },
            User = new User { FirstName = "Ghost", LastName = "User", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        _filingRepository.GetFilingById(filingId).Returns(filing);
        _filerUserRepository.FindFilerUsersByFilerId(filerId)
            .Returns(new List<FilerUser> { officerUser, authorizedUser, unrelatedUser });

        // Act
        var result = await _filingSvc.GetResponsibleOfficers(filingId);

        // Assert
        var resultList = result.ToList();
        Assert.Multiple(() =>
        {
            Assert.That(resultList, Has.Count.EqualTo(2));
            Assert.That(resultList.Any(x => x.FirstName == "Jane" && x.LastName == "Doe"), Is.True);
            Assert.That(resultList.Any(x => x.FirstName == "John" && x.LastName == "Smith"), Is.True);
            Assert.That(resultList.All(x => x.Role == FilerRole.Disclosure_Lobbying48H_Attest_AccountManager.Name ||
                                            x.Role == FilerRole.Disclosure_Lobbying48H_Attest_ResponsibleOfficer.Name), Is.True);
        });
    }

    [Test]
    public async Task GetResponsibleOfficers_ReturnsExpectedOfficers_WhenFilingTypeIsLobbyistEmployerReport()
    {
        // Arrange
        long filingId = 100;
        long filerId = 200;

        var filingType = new FilingType { Name = FilingType.LobbyistEmployerReport.Name };
        var filing = new Filing
        {
            FilerId = filerId,
            StartDate = _dateNow,
            StatusId = FilingStatus.Pending.Id,
            EndDate = _dateNow.AddDays(1),
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            FilingType = FilingType.LobbyistEmployerReport
        };

        var officerUser = new FilerUser
        {
            Id = 1,
            FilerRole = new FilerRole { Id = 1, Description = "test", Name = FilerRole.Disclosure_LobbyistEmployer_Attest_ResponsibleOfficer.Name },
            User = new User { FirstName = "Jane", LastName = "Doe", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        var authorizedUser = new FilerUser
        {
            Id = 2,
            FilerRole = new FilerRole { Id = 2, Description = "test", Name = FilerRole.Disclosure_LobbyistEmployer_Attest_AccountManager.Name },
            User = new User { FirstName = "John", LastName = "Smith", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        var unrelatedUser = new FilerUser
        {
            Id = 3,
            FilerRole = new FilerRole { Id = 3, Description = "test", Name = "UnrelatedRole" },
            User = new User { FirstName = "Ghost", LastName = "User", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        _filingRepository.GetFilingById(filingId).Returns(filing);
        _filerUserRepository.FindFilerUsersByFilerId(filerId)
            .Returns(new List<FilerUser> { officerUser, authorizedUser, unrelatedUser });

        // Act
        var result = await _filingSvc.GetResponsibleOfficers(filingId);

        // Assert
        var resultList = result.ToList();
        Assert.Multiple(() =>
        {
            Assert.That(resultList, Has.Count.EqualTo(2));
            Assert.That(resultList.Any(x => x.FirstName == "Jane" && x.LastName == "Doe"), Is.True);
            Assert.That(resultList.Any(x => x.FirstName == "John" && x.LastName == "Smith"), Is.True);
            Assert.That(resultList.All(x => x.Role == FilerRole.Disclosure_LobbyistEmployer_Attest_AccountManager.Name ||
                                            x.Role == FilerRole.Disclosure_LobbyistEmployer_Attest_ResponsibleOfficer.Name), Is.True);
        });
    }

    [Test]
    public void GetResponsibleOfficers_ThrowsException_WhenFilingNotFound()
    {
        // Arrange
        long filingId = 999;
        _filingRepository.GetFilingById(filingId).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() => _filingSvc.GetResponsibleOfficers(filingId));
        Assert.That(ex!.Message, Is.EqualTo($"Disclosure Filing Report not found Id={filingId}"));
    }

    [Test]
    public async Task GetResponsibleOfficers_ReturnsEmpty_WhenNoAuthorizedRolesMatch()
    {
        // Arrange
        long filingId = 100;
        long filerId = 200;

        var filing = new Filing
        {
            FilerId = filerId,
            StartDate = _dateNow,
            StatusId = FilingStatus.Pending.Id,
            EndDate = _dateNow.AddDays(1),
            FilingTypeId = FilingType.AdHocFiling.Id,
            FilingType = FilingType.AdHocFiling
        };

        var unrelatedUser = new FilerUser
        {
            Id = 1,
            FilerRole = new FilerRole { Id = 1, Description = "test", Name = "SomeOtherRole" },
            User = new User { FirstName = "X", LastName = "Y", EmailAddress = "<EMAIL>", EntraOid = "1" }
        };

        _filingRepository.GetFilingById(filingId).Returns(filing);
        _filerUserRepository.FindFilerUsersByFilerId(filerId).Returns(new List<FilerUser> { unrelatedUser });

        // Act
        var result = await _filingSvc.GetResponsibleOfficers(filingId);

        // Assert
        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetResponsibleOfficers_ExcludesUsersWithNullUserOrRole()
    {
        // Arrange
        long filingId = 101;
        long filerId = 202;

        var filing = new Filing
        {
            FilerId = filerId,
            StartDate = _dateNow,
            StatusId = FilingStatus.Pending.Id,
            EndDate = _dateNow.AddDays(1),
            FilingTypeId = FilingType.Report72h.Id,
            FilingType = FilingType.Report72h,
            Id = filingId
        };

        var validUser = new FilerUser
        {
            Id = 1,
            FilerRole = new FilerRole { Id = 1, Description = "test", Name = FilerRole.Disclosure_Lobbying72H_Attest_ResponsibleOfficer.Name },
            User = new User { FirstName = "Valid", LastName = "User", EmailAddress = "<EMAIL>", EntraOid = "1" },
            FilerId = filerId
        };

        var nullUser = new FilerUser
        {
            Id = 2,
            FilerRole = new FilerRole { Id = 1, Description = "test", Name = FilerRole.Disclosure_Lobbying72H_Attest_ResponsibleOfficer.Name },
            User = null,
            FilerId = filerId
        };

        var nullRole = new FilerUser
        {
            Id = 3,
            FilerRole = null,
            User = new User { FirstName = "Roleless", LastName = "User", EmailAddress = "<EMAIL>", EntraOid = "1" },
            FilerId = filerId
        };

        _filingRepository.GetFilingById(filingId).Returns(filing);
        _filerUserRepository.FindFilerUsersByFilerId(filerId).Returns(new List<FilerUser> { validUser, nullUser, nullRole });

        // Act
        var result = await _filingSvc.GetResponsibleOfficers(filingId);

        // Assert
        var list = result.ToList();
        Assert.Multiple(() =>
        {
            Assert.That(list, Has.Count.EqualTo(2));
            Assert.That(list.First().FirstName, Is.EqualTo("Valid"));
        });
    }

    [Test]
    public async Task SendForAttestationReport48H_ShouldHandleEmptyNotifications()
    {
        // Arrange
        const long filingId = 123L;
        var request = new Report48HSendForAttestationRequest
        {
            SelectedResponsibleOfficerIds = new List<long> { 101 }
        };

        var report48H = new Report48H
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id
        };

        var decisionResponse = new DecisionsSubmitReport48HResponse
        {
            Errors = new List<WorkFlowError>(),
            // No notifications
            Notifications = null
        };

        _filingRepository.FindById(filingId).Returns(report48H);
        _decisionsSvc.InitiateWorkflow<DecisionsSubmitReport48H, DecisionsSubmitReport48HResponse>(
            DecisionsWorkflow.FDLOBFiling48HourReportSendForAttestation,
            Arg.Any<DecisionsSubmitReport48H>(),
            true
        ).Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SendForAttestationReport48H(filingId, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Pending.Id));
        });

        await _filingRepository.Received(1).Update(Arg.Any<Report48H>());
        await _notificationSvc.DidNotReceive().SendUserNotification(Arg.Any<SendUserNotificationRequest>());
    }

    [Test]
    public async Task AddAssociatedFilerUserByFiling_ShouldCreateNewFilerUser_WhenDoesNotExist()
    {
        // Arrange
        const long filingId = 123L;
        const long filerId = 456L;
        const long userId = 789L;
        const long filerRoleId = 10L;

        var request = new AddAssociatedFilerUserByFilingRequest
        {
            UserId = userId,
            FilerRoleId = filerRoleId
        };

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = 1
        };

        var newFilerUser = new FilerUser
        {
            Id = 999L,
            FilerId = filerId,
            UserId = userId,
            FilerRoleId = filerRoleId
        };

        _filingRepository.FindById(filingId).Returns(filing);
        _filerSvc.GetFilerUserByUserIdAsync(filerId, userId).ReturnsNull();

        // Act
        var result = await _filingSvc.AddAssociatedFilerUserByFiling(filingId, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.UserId, Is.EqualTo(userId));
            Assert.That(result.FilerRoleId, Is.EqualTo(filerRoleId));
        });

        await _filerSvc.Received(1).AddFilerUserAsync(Arg.Is<FilerUser>(fu =>
            fu.FilerId == filerId &&
            fu.UserId == userId &&
            fu.FilerRoleId == filerRoleId));

        await _filerSvc.DidNotReceive().UpdateFilerUserRoleAsync(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task AddAssociatedFilerUserByFiling_ShouldUpdateExistingFilerUser_WhenExists()
    {
        // Arrange
        const long filingId = 123L;
        const long filerId = 456L;
        const long userId = 789L;
        const long filerRoleId = 10L;
        const long existingFilerUserId = 888L;
        const long existingFilerRoleId = 5L;

        var request = new AddAssociatedFilerUserByFilingRequest
        {
            UserId = userId,
            FilerRoleId = filerRoleId
        };

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = 1
        };

        var existingFilerUser = new FilerUserDto
        {
            Id = existingFilerUserId,
            FilerId = filerId,
            UserId = userId,
            FilerRoleId = existingFilerRoleId
        };

        _filingRepository.FindById(filingId).Returns(filing);
        _filerSvc.GetFilerUserByUserIdAsync(filerId, userId).Returns(existingFilerUser);
        _filerSvc.UpdateFilerUserRoleAsync(existingFilerUserId, filerRoleId).Returns(Task.CompletedTask);

        // Act
        var result = await _filingSvc.AddAssociatedFilerUserByFiling(filingId, request);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.UserId, Is.EqualTo(userId));
            Assert.That(result.FilerRoleId, Is.EqualTo(filerRoleId));
        });

        await _filerSvc.DidNotReceive().AddFilerUserAsync(Arg.Any<FilerUser>());
        await _filerSvc.Received(1).UpdateFilerUserRoleAsync(existingFilerUserId, filerRoleId);
    }

    [Test]
    public void AddAssociatedFilerUserByFiling_ShouldThrowKeyNotFoundException_WhenFilingNotFound()
    {
        // Arrange
        const long filingId = 123L;
        var request = new AddAssociatedFilerUserByFilingRequest
        {
            UserId = 789L,
            FilerRoleId = 10L
        };

        _filingRepository.FindById(filingId).Returns((Filing?)null);

        // Act & Assert
        var ex = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.AddAssociatedFilerUserByFiling(filingId, request));

        Assert.That(ex.Message, Does.Contain($"Filing not found with Id={filingId}"));
    }
    [Test]
    public async Task SendForAttestationReport72H_ReturnsValidReport_WhenWorkflowIsSuccessful()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var responsibleOfficerIds = new List<long> { 1, 2 };

        var report = new Report72H
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = 0
        };

        var workflowResult = new DecisionsSendForAttestationReport72HResponse([])
        {
            Result = [],
            Notifications = new List<NotificationTrigger>
            {
                new (true, 1, _dateNow)
            }
        };

        _filingRepository.FindById(filingId).Returns(report);

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        _decisionsSvc.InitiateWorkflow<DecisionsSendForAttestationReport72H, DecisionsSendForAttestationReport72HResponse>(
            DecisionsWorkflow.SendForAttestationReport72HRuleset,
            Arg.Any<DecisionsSendForAttestationReport72H>(),
            false
        ).Returns(Task.FromResult(workflowResult));

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, responsibleOfficerIds);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        // Verify update was called
        await _filingRepository.Received(1).Update(Arg.Is<Report72H>(r =>
            r.Id == filingId &&
            r.StatusId == FilingStatus.Pending.Id &&
            r.SubmittedDate != null));
    }

    [Test]
    public async Task SendForAttestationReport72H_ReturnsNull_WhenFilingIsNotReport72H()
    {
        // Arrange
        var filingId = 123;
        var responsibleOfficerIds = new List<long> { 1 };

        var otherFiling = new Filing
        {
            FilerId = 1,
            StartDate = _dateNow,
            StatusId = FilingStatus.Pending.Id,
            EndDate = _dateNow.AddDays(1),
            FilingTypeId = FilingType.AdHocFiling.Id,
        }; // Not a Report72H

        _filingRepository.FindById(filingId).Returns(otherFiling);

        // Act
        var result = await _filingSvc.SendForAttestationReport72H(filingId, responsibleOfficerIds);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task CreateReport72H_ShouldCreateReportWithCorrectDefaults()
    {
        // Arrange
        const long filerId = 1001L;

        var filingSummaryStatuses = new List<FilingSummaryStatus>
        {
            new(1, FilingSummaryStatus.NotStarted.Name ),
            new(2, "In Progress" )
        };

        var expectedCreatedReport = new Report72H
        {
            Id = 999,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.Report72h.Id,
            CreatedBy = filerId
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses().Returns(filingSummaryStatuses);

        _filingRepository.Create(Arg.Any<Report72H>())
            .Returns(callInfo =>
            {
                var report = callInfo.Arg<Report72H>();
                report.Id = 999;
                return report;
            });

        // Act
        var result = await _filingSvc.CreateReport72H(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(999));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
        });

        var capturedFiling = Arg.Is<Report72H>(f =>
            f.FilerId == filerId &&
            f.StatusId == FilingStatus.Draft.Id &&
            f.FilingTypeId == FilingType.Report72h.Id &&
            f.FilingSummaries.Count == 2 &&
            f.FilingSummaries.All(s => s.FilingSummaryStatusId == filingSummaryStatuses[0].Id)
        );

        await _filingRepository.Received(1).Create(capturedFiling);
    }

    [Test]
    public async Task CreateReport72H_ShouldHandleMissingNotStartedStatus()
    {
        // Arrange
        const long filerId = 1001L;

        var filingSummaryStatuses = new List<FilingSummaryStatus>
        {
            new(2, "In Progress" )
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses().Returns(filingSummaryStatuses);

        _filingRepository.Create(Arg.Any<Report72H>())
            .Returns(callInfo =>
            {
                var report = callInfo.Arg<Report72H>();
                report.Id = 100;
                return report;
            });

        // Act
        var result = await _filingSvc.CreateReport72H(filerId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(100));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
        });

        await _filingRepository.Received(1).Create(Arg.Is<Report72H>(f =>
            f.FilingSummaries.All(s => s.FilingSummaryStatusId == null)
        ));
    }

    [Test]
    public async Task CreateLobbyistReport_WithNullFilingPeriod_CreatesNewReport()
    {
        // Arrange
        const long filerId = 123;
        var filingSummaryStatuses = new List<FilingSummaryStatus> { new(1, "Not Started") };
        var createdReport = new LobbyistReport
        {
            Id = 456,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistReport.Id
        };
        var updatedReport = new LobbyistReport
        {
            Id = 456,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistReport.Id,
            OriginalId = 456
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
            .Returns(filingSummaryStatuses);
        _filingRepository.Create(Arg.Any<LobbyistReport>())
            .Returns(createdReport);
        _filingRepository.Update(Arg.Any<LobbyistReport>())
            .Returns(updatedReport);

        // Act
        var result = await _filingSvc.CreateLobbyistReport(filerId, null);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(456));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
            Assert.That(result.FilingTypeId, Is.EqualTo(FilingType.LobbyistReport.Id));
            Assert.That(result.OriginalId, Is.EqualTo(456));
            Assert.That(result.StartDate, Is.EqualTo(DateTime.MinValue));
            Assert.That(result.EndDate, Is.EqualTo(DateTime.MinValue));
        });

        await _filingRepository.Received(1).Create(Arg.Is<LobbyistReport>(r =>
            r.FilerId == filerId &&
            r.StatusId == FilingStatus.Draft.Id &&
            r.FilingTypeId == FilingType.LobbyistReport.Id &&
            r.FilingPeriodId == null &&
            r.StartDate == DateTime.MinValue &&
            r.EndDate == DateTime.MinValue &&
            r.FilingSummaries.Count == 2));

        await _filingRepository.Received(1).Update(Arg.Is<LobbyistReport>(r =>
            r.Id == 456 && r.OriginalId == 456));
    }

    [Test]
    public async Task CreateLobbyistReport_WithNullFilingPeriod_CreatesCorrectFilingSummaries()
    {
        // Arrange
        const long filerId = 123;
        var filingSummaryStatuses = new List<FilingSummaryStatus> { new(1, "Not Started") };
        var expectedSummaryTypes = new[]
        {
            FilingSummaryType.CampaignContributionSummary.Id, FilingSummaryType.ActivityExpenseSummary.Id
        };
        var createdReport = new LobbyistReport
        {
            Id = 456,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistReport.Id
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
            .Returns(filingSummaryStatuses);
        _filingRepository.Create(Arg.Any<LobbyistReport>())
            .Returns(createdReport);
        _filingRepository.Update(Arg.Any<LobbyistReport>())
            .Returns(createdReport);

        // Act
        await _filingSvc.CreateLobbyistReport(filerId, null);

        // Assert
        await _filingRepository.Received(1).Create(Arg.Is<LobbyistReport>(r =>
            VerifyFilingSummaries(r.FilingSummaries, expectedSummaryTypes, filingSummaryStatuses[0].Id)));
    }

    [Test]
    public async Task CreateLobbyistReport_WithNullFilingPeriod_HandlesNoNotStartedStatus()
    {
        // Arrange
        const long filerId = 123;
        var filingSummaryStatuses = new List<FilingSummaryStatus> { new(1, "Not Started") };
        var createdReport = new LobbyistReport
        {
            Id = 456,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistReport.Id
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
            .Returns(filingSummaryStatuses);
        _filingRepository.Create(Arg.Any<LobbyistReport>())
            .Returns(createdReport);
        _filingRepository.Update(Arg.Any<LobbyistReport>())
            .Returns(createdReport);

        // Act
        var result = await _filingSvc.CreateLobbyistReport(filerId, null);

        // Assert
        Assert.That(result, Is.Not.Null);
    }



    [Test]
    public async Task CreateReport48H_ShouldSetOriginalIdToNewFilingId_AfterCreation()
    {
        // Arrange
        var filerId = 123L;
        var newReportId = 789L;

        var filingSummaryStatuses = new List<FilingSummaryStatus>
        {
            new(1, FilingSummaryStatus.NotStarted.Name)
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses().Returns(filingSummaryStatuses);

        var createdReport = new Report48H
        {
            Id = newReportId,
            StatusId = FilingStatus.Draft.Id,
            CreatedBy = filerId,
            FilerId = filerId,
            FilingTypeId = FilingType.Report48h.Id,
            FilingSummaries = new List<FilingSummary>
        {
            new()
            {
                FilingSummaryTypeId = FilingSummaryType.EndOfSessionLobbyingSummary.Id,
                PeriodAmount = 0,
                ToDateAmount = 0,
                FilingSummaryStatusId = 1,
                NoActivityToReport = false
            }
        }
        };

        // Updated report after setting OriginalId
        var updatedReport = new Report48H
        {
            Id = newReportId,
            StatusId = FilingStatus.Draft.Id,
            CreatedBy = filerId,
            FilerId = filerId,
            FilingTypeId = FilingType.Report48h.Id,
            FilingSummaries = createdReport.FilingSummaries,
            OriginalId = newReportId
        };

        _filingRepository.Create(Arg.Any<Report48H>()).Returns(createdReport);
        _filingRepository.Update(Arg.Any<Report48H>()).Returns(updatedReport);

        // Act
        var result = await _filingSvc.CreateReport48H(filerId);

        // Assert
        await _filingRepository.Received(1).Create(Arg.Is<Report48H>(r =>
            r.FilerId == filerId &&
            r.FilingTypeId == FilingType.Report48h.Id &&
            r.StatusId == FilingStatus.Draft.Id));

        await _filingRepository.Received(1).Update(Arg.Is<Report48H>(r =>
            r.Id == newReportId &&
            r.OriginalId == newReportId)); // Verify OriginalId was set to the new filing Id

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(newReportId));
    }

    [Test]
    public async Task HandleLobbyistEmployerReportToDecisionInput_ShouldMapAllSectionsCorrectly()
    {
        // Arrange
        var filingId = 123L;
        var filing = new LobbyistEmployerReport
        {
            Id = filingId,
            TotalPaymentsPucActivity = 1000,
            TotalOverheadExpense = new Currency(10),
            TotalUnderThresholdPayments = new Currency(20),
            TotalPaymentsToInHouseLobbyists = new Currency(30),
            OtherActionsLobbied = "Other actions",
            StatusId = 1
        };

        GetMockLobbyistEmployerTransactions();

        _dependencies.FilingRepository.FindById(Arg.Any<long>()).Returns(filing);

        var expectedStartDate = DateTime.UtcNow;
        _filingPeriodRepository.GetFilingPeriodStartDateForFiling(Arg.Any<long>())
            .Returns(Task.FromResult(expectedStartDate));

        var expectedErrors = new List<WorkFlowError>()
        {
        };
        var decisionResponse = new DecisionsSubmitLobbyistEmployerReportResponse { Result = expectedErrors };

        _servicesDependencies.DecisionsSvc
            .InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
                DecisionsWorkflow.SubmitLobbyistEmployerReportRuleset,
                Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
                true)
            .Returns(Task.FromResult(decisionResponse));

        // Act
        var result = await _filingSvc.SubmitLobbyistEmployerReport(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Is.Empty);
        });
    }

    [Test]
    public async Task HandleLobbyistEmployerReportToDecisionInput_ShouldHandleNullsAndReturnErrorCollections()
    {
        // Arrange
        var filingId = 456L;
        var filing = new LobbyistEmployerReport
        {
            Id = filingId,
            TotalPaymentsPucActivity = new Currency(10),
            TotalOverheadExpense = new Currency(10),
            TotalUnderThresholdPayments = new Currency(10),
            TotalPaymentsToInHouseLobbyists = new Currency(10),
            OtherActionsLobbied = null,
            StatusId = 1,
        };

        _dependencies.FilingRepository.FindById(Arg.Any<long>()).Returns(filing);

        _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId).Returns(new List<ActivityExpense>());
        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId).Returns(new List<LobbyingCampaignContribution>());
        _transactionRepository.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, Arg.Any<DateTime>()).Returns(new List<PaymentMadeToLobbyingCoalitionResponse>());
        _transactionHelperSvc.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId, Arg.Any<DateTime>()).Returns(new List<PaymentReceiveLobbyingCoalitionResponse>());
        _transactionRepository.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, Arg.Any<DateTime>()).Returns(new List<OtherPaymentsToInfluenceResponse>());
        _transactionHelperSvc.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filingId, Arg.Any<DateTime>()).Returns(new List<PaymentMadeToLobbyingFirmsResponse>());
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(new ActionsLobbiedByEntityResponse());

        var expectedStartDate = DateTime.UtcNow;
        _filingPeriodRepository.GetFilingPeriodStartDateForFiling(Arg.Any<long>())
            .Returns(Task.FromResult(expectedStartDate));

        var fatalError = new WorkFlowError(
            FieldName: "SomeField",
            ErrorCode: "ERR001",
            ErrorType: "fatal",
            Message: "Invalid data"
        );

        var expectedErrors = new List<WorkFlowError>()
        {
            fatalError
        };
        var decisionResponse = new DecisionsSubmitLobbyistEmployerReportResponse { Errors = expectedErrors };

        _servicesDependencies.DecisionsSvc
            .InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
                DecisionsWorkflow.SubmitLobbyistEmployerReportRuleset,
                Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
                true)
            .Returns(Task.FromResult(decisionResponse));

        // Act
        var result = await _filingSvc.SubmitLobbyistEmployerReport(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.Not.Null);
            Assert.That(result.ValidationErrors, Has.Count.EqualTo(1));
            Assert.That(result.ValidationErrors[0].ErrorType, Is.EqualTo("fatal"));
            Assert.That(result.ValidationErrors[0].ErrorCode, Is.EqualTo("ERR001"));
            Assert.That(result.ValidationErrors[0].FieldName, Is.EqualTo("SomeField"));
            Assert.That(result.ValidationErrors[0].Message, Is.EqualTo("Invalid data"));
        });
    }

    #region OnTransactionUpdatedAsync
    [Test]
    public void OnTransactionUpdatedAsync_FilingIdIsNull_ShouldDoNothingAndReturn()
    {
        // Arrange
        var transaction = new PersonReceiving1000OrMore
        {
            Amount = (Currency)1
        };
        var varianceAmount = 1m;

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _filingSvc.OnTransactionUpdatedAsync(transaction, varianceAmount, null, null));
        _ = _filingSummaryRepository.Received(0).Update(Arg.Any<FilingSummary>());
    }

    [Test]
    public async Task SubmitLobbyistReportForEfile_DoesNotCreateReport_WhenFatalValidationErrorsExist()
    {
        // Arrange
        var filerId = 999;
        var filingPeriodId = 1;

        var filer = new Filer { Id = filerId };
        var lobbyistReport = new LobbyistReport
        {
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTransactions = new List<FilingTransaction>(),
            FilingSummaries = new List<FilingSummary>()
        };

        var submission = new LobbyistReportDto
        {
            LobbyistReport = lobbyistReport,
            ReportingStartDate = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            ReportingEndDate = new DateTime(2025, 3, 31, 0, 0, 0, DateTimeKind.Utc),
            CampaignContributions = new List<LobbyingCampaignContribution>(),
            ActivityExpenses = new List<ActivityExpense>(),
            FilerContacts = new List<FilerContact>(),
            Attestation = new Attestation()
        };

        var fatalError = new WorkFlowError(
        FieldName: "SomeField",
        ErrorCode: "ERR001",
        ErrorType: "fatal",
        Message: "Invalid data"
        );

        var decisionsResponse = new DecisionsSubmitLobbyistReportResponse(new List<WorkFlowError> { fatalError })
        {
            IsLateFiling = false,
            Status = "Rejected",
            ListOfNotifications = new List<NotificationTrigger>()
        };

        _filerRepository.FindById(filerId).Returns(filer);
        _filingPeriodRepository
            .GetFilingPeriodByStartDateAndEndDate(submission.ReportingStartDate, submission.ReportingEndDate)
            .Returns(filingPeriodId);

        _decisionsSvc
            .InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSubmitLobbyistReport>(),
                Arg.Is(true))
            .Returns(decisionsResponse);

        // Act
        var result = await _filingSvc.SubmitLobbyistReportForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.ValidationErrors, Is.Not.Empty);
            Assert.That(result.ValidationErrors.Any(e => e.ErrorType.Equals("fatal", StringComparison.OrdinalIgnoreCase)), Is.True);
        });

        await _filingRepository.DidNotReceive().Create(Arg.Any<LobbyistReport>());
        await _attestationRepository.DidNotReceive().Create(Arg.Any<Attestation>());
    }

    [Test]
    public async Task SubmitLobbyistReportForEfile_CreatesReport_WhenSubmissionIsValid()
    {
        // Arrange
        var filerId = 999;
        var filingPeriodId = 1;

        var filer = new Filer { Id = filerId };
        var lobbyistReport = new LobbyistReport
        {
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTransactions = new List<FilingTransaction>()
        };

        var submission = new LobbyistReportDto
        {
            LobbyistReport = lobbyistReport,
            ReportingStartDate = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            ReportingEndDate = new DateTime(2025, 3, 31, 0, 0, 0, DateTimeKind.Utc),
            CampaignContributions = new List<LobbyingCampaignContribution>(),
            ActivityExpenses = new List<ActivityExpense>(),
            FilerContacts = new List<FilerContact>(),
            Attestation = new Attestation()
        };
        var filingPeriod = new FilingPeriod
        {
            Id = filingPeriodId,
            Name = "Q1",
            StartDate = submission.ReportingStartDate,
            EndDate = submission.ReportingEndDate
        };

        var filingSummaries = new List<FilingSummary>
        {
            new() {
                FilingSummaryTypeId = 1001,
                PeriodAmount = 0,
                ToDateAmount = 0
            }
        };

        lobbyistReport.FilingSummaries = filingSummaries;
        lobbyistReport.FilingPeriod = filingPeriod;
        var workflowErrors = new List<WorkFlowError>();

        var decisionsResponse = new DecisionsSubmitLobbyistReportResponse(workflowErrors)
        {
            IsLateFiling = false,
            Status = "Approved",
            ListOfNotifications = new List<NotificationTrigger>()
        };

        _filerRepository.FindById(filerId).Returns(filer);
        _filingPeriodRepository.GetFilingPeriodByStartDateAndEndDate(submission.ReportingStartDate, submission.ReportingEndDate)
            .Returns(filingPeriodId);


        _decisionsSvc
            .InitiateWorkflow<DecisionsSubmitLobbyistReport, DecisionsSubmitLobbyistReportResponse>(
                Arg.Any<DecisionsWorkflow>(),
                Arg.Any<DecisionsSubmitLobbyistReport>(),
                Arg.Is(true))
            .Returns(decisionsResponse);

        _filingRepository.Create(Arg.Any<LobbyistReport>()).Returns(callInfo =>
        {
            var report = callInfo.Arg<LobbyistReport>();
            report.Id = 45678;
            return report;
        });
        var method = typeof(FilingSvc).GetMethod(
        "CalculateLegislativeSessionToDateAmountsBySummaryType",
        BindingFlags.NonPublic | BindingFlags.Instance
        );

        long[] summaryTypeIds = [1001];

        // Assuming 'method' is a valid MethodInfo
        var task = method?.Invoke(_filingSvc, new object[] { filerId, summaryTypeIds, filingPeriod })
            as Task<Dictionary<long, decimal>>
            ?? throw new InvalidOperationException("Method invocation failed or returned unexpected type.");

        var result = await task;


        // Act
        var results = await _filingSvc.SubmitLobbyistReportForEfile(submission);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(results, Is.Not.Null);
            Assert.That(results.Id, Is.EqualTo(45678));
            Assert.That(results.ValidationErrors, Is.Empty);
        });

        await _attestationRepository.Received(1).Create(Arg.Is<Attestation>(a => a.FilingId == 45678));
        await _filingRepository.Received(1).Create(Arg.Any<LobbyistReport>());
    }


    [Test]
    public void OnTransactionUpdatedAsync_NotFoundFilingSummaryType_ShouldDoNothingAndReturn()
    {
        // Arrange
        var transaction = new Expenditure
        {
            Amount = (Currency)1,
            Purpose = "purpose",
        };
        var varianceAmount = 1m;
        var filingId = 1L;

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _filingSvc.OnTransactionUpdatedAsync(transaction, varianceAmount, filingId, null));
        _ = _filingSummaryRepository.Received(0).Update(Arg.Any<FilingSummary>());
    }

    [Test]
    public void OnTransactionUpdatedAsync_NotFoundFilingSummary_ShouldDoNothingAndReturn()
    {
        // Arrange
        var transaction = new PersonReceiving1000OrMore
        {
            Amount = (Currency)1,
        };
        var varianceAmount = 1m;
        var filingId = 1L;
        var filingSummaries = GenerateFilingSummaries().Where(x => x.FilingSummaryTypeId != FilingSummaryType.PersonReceiving1000OrMoreSummary.Id);

        _filingSummaryRepository.GetAllByFilingId(Arg.Any<long>()).Returns(filingSummaries);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _filingSvc.OnTransactionUpdatedAsync(transaction, varianceAmount, filingId, null));
        _ = _filingSummaryRepository.Received(0).Update(Arg.Any<FilingSummary>());
    }

    [Test]
    public void OnTransactionUpdatedAsync_FoundSummaryType_ShouldUpdateSuccessfully()
    {
        // Arrange
        var transaction = new PersonReceiving1000OrMore
        {
            Amount = (Currency)1,
        };
        var varianceAmount = 1m;
        var filingId = 1L;
        var filingSummaries = GenerateFilingSummaries();

        _filingSummaryRepository.GetAllByFilingId(Arg.Any<long>()).Returns(filingSummaries);

        // Act & Assert
        Assert.DoesNotThrowAsync(async () => await _filingSvc.OnTransactionUpdatedAsync(transaction, varianceAmount, filingId, null));
        _ = _filingSummaryRepository.Received(1).Update(Arg.Any<FilingSummary>());
    }

    // Helper to call the private method
    private Task<Dictionary<string, FilerContact>> InvokeFindAndUpdate(
        long filerId,
        IEnumerable<FilerContact> contacts)
    {
        var mi = typeof(FilingSvc)
                 .GetMethod(
                   "FindAndUpdateExistingFilerContactsByExternalId",
                   BindingFlags.NonPublic | BindingFlags.Instance
                 )!;

        return (Task<Dictionary<string, FilerContact>>)mi
            .Invoke(_filingSvc, new object[] { filerId, contacts })!;
    }

    [Test]
    public async Task FindAndUpdate_Should_Update_Existing_And_Add_New()
    {
        // Arrange
        const long filerId = 999;


        // 1) incoming that matches an existing
        var incoming1 = new IndividualContact
        {
            ExternalId = "ext-1",
            FirstName = "Alice",
            MiddleName = "B.",
            LastName = "Smith",
            AddressListId = 3001,
            PhoneNumberListId = 4001,
            EmailAddressListId = 5001,
            ContactFilerId = 6001,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                                {
                                    new()
                                    {
                                        Street = "street",
                                        Street2 = "street2",
                                        City = "city",
                                        State = "CA",
                                        Zip = "12345",
                                        Country = "US",
                                        Type = "home",
                                        Purpose = "purpose"
                                    }
                                }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                                {
                                    new()
                                    {
                                        InternationalNumber = false,
                                        CountryCode = "1",
                                        Number = "1231234",
                                        Type = "Phone",
                                    }
                                }
            }
        };
        var existing = new IndividualContact
        {
            ExternalId = "ext-1",
            FirstName = "Alice",
            MiddleName = "B.",
            LastName = "Smith",
            AddressListId = 3001,
            PhoneNumberListId = 4001,
            EmailAddressListId = 5001,
            ContactFilerId = 6001,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                                {
                                    new()
                                    {
                                        Street = "street",
                                        Street2 = "street2",
                                        City = "city",
                                        State = "CA",
                                        Zip = "12345",
                                        Country = "US",
                                        Type = "home",
                                        Purpose = "purpose"
                                    }
                                }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                                {
                                    new()
                                    {
                                        InternationalNumber = false,
                                        CountryCode = "1",
                                        Number = "1231234",
                                        Type = "Phone",
                                    }
                                }
            }
        };

        _dependencies.FilerContactRepository.GetFilerContactByFilerIdAndExternalId(filerId, "ext-1")
      .Returns(existing);

        // 2) incoming that has no existing
        var incoming2 = new IndividualContact
        {
            ExternalId = "ext-2",
            FirstName = "A",
            MiddleName = "B.",
            LastName = "Smith",
            AddressListId = 3002,
            PhoneNumberListId = 4003,
            EmailAddressListId = 500,
            ContactFilerId = 600,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                                {
                                    new()
                                    {
                                        Street = "street",
                                        Street2 = "street2",
                                        City = "city",
                                        State = "CA",
                                        Zip = "1234",
                                        Country = "US",
                                        Type = "home",
                                        Purpose = "test"
                                    }
                                }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                                {
                                    new()
                                    {
                                        InternationalNumber = false,
                                        CountryCode = "1",
                                        Number = "123134",
                                        Type = "Phone",
                                    }
                                }
            }
        };

        _dependencies.FilerContactRepository.GetFilerContactByFilerIdAndExternalId(filerId, "ext-2")
        .Returns((FilerContact?)null);

        var list = new[] { incoming1, incoming2 };

        // Act
        var result = await InvokeFindAndUpdate(filerId, list);

        // Assert
        Assert.Multiple(() =>
        {
            // check keys
            Assert.That(result.Keys, Is.EquivalentTo(expected));

            // mapping for existing should be the stubbed instance
            Assert.That(result["ext-1"], Is.SameAs(existing));

            // mapping for new should be the incoming instance
            Assert.That(result["ext-2"], Is.SameAs(incoming2));
        });
    }

    [Test]
    public async Task FindAndUpdate_SkipsNullsAndNoExternalId()
    {
        // Arrange
        const long filerId = 42;

        FilerContact? nullContact = null;

        var noExt = new IndividualContact
        {
            ExternalId = null,
        };

        var valid = new IndividualContact
        {
            ExternalId = "ext-3",
            FirstName = "Alice",
            MiddleName = "B.",
            LastName = "Smith",
            AddressListId = 3001,
            PhoneNumberListId = 4001,
            EmailAddressListId = 5001,
            ContactFilerId = 6001,
            AddressList = new AddressList
            {
                Addresses = new List<Address>
                                {
                                    new()
                                    {
                                        Street = "street",
                                        Street2 = "street2",
                                        City = "city",
                                        State = "CA",
                                        Zip = "12345",
                                        Country = "US",
                                        Type = "home",
                                        Purpose = "purpose"
                                    }
                                }
            },
            PhoneNumberList = new()
            {
                PhoneNumbers = new()
                                {
                                    new()
                                    {
                                        InternationalNumber = false,
                                        CountryCode = "1",
                                        Number = "1231234",
                                        Type = "Phone",
                                    }
                                }
            }
        };

        _dependencies.FilerContactRepository.GetFilerContactByFilerIdAndExternalId(filerId, "ext-3")
         .Returns((FilerContact?)null);

        // Act
        var result = await InvokeFindAndUpdate(
            filerId,
            new FilerContact[] { nullContact!, noExt, valid }
        );

        // Assert
        Assert.That(result, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result.ContainsKey("ext-3"), Is.True);
            Assert.That(result["ext-3"], Is.SameAs(valid));
        });
    }

    [Test]
    public async Task GetFilersAssociatedUserWithFilingType_WhenFilingTypeIsLobbyistReport_ReturnsLobbyistFilers()
    {
        // Arrange
        long userId = 123;
        long filingTypeId = FilingType.LobbyistReport.Id;

        // Create test filers with different filer types
        var filerUsers = new List<FilerUser>
    {
        new() { UserId = userId, Filer = new Filer { Id = 1, FilerTypeId = FilerType.Lobbyist.Id } },
        new() { UserId = userId, Filer = new Filer { Id = 2, FilerTypeId = FilerType.LobbyistEmployer.Id } },
        new() { UserId = userId, Filer = new Filer { Id = 3, FilerTypeId = FilerType.Lobbyist.Id } },
    };

        _filerUserRepository.FindFilerUsersByUserId(userId).Returns(filerUsers);
        _filingTypeRepository.FindById(filingTypeId).Returns(new FilingType { Id = filingTypeId, Name = FilingType.LobbyistReport.Name });

        // Act
        var result = await _filingSvc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task GetFilersAssociatedUserWithFilingType_WhenFilingTypeIsNotLobbyistReport_ReturnsLobbyistEmployerFilers()
    {
        // Arrange
        long userId = 123;
        long filingTypeId = FilingType.LobbyistEmployerReport.Id;

        var filerUsers = new List<FilerUser>
    {
        new() { UserId = userId, Filer = new Filer { Id = 1, FilerTypeId = FilerType.Lobbyist.Id } },
        new() { UserId = userId, Filer = new Filer { Id = 2, FilerTypeId = FilerType.LobbyistEmployer.Id } },
        new() { UserId = userId, Filer = new Filer { Id = 3, FilerTypeId = FilerType.LobbyistEmployer.Id } },
    };

        _filerUserRepository.FindFilerUsersByUserId(userId).Returns(filerUsers);
        _filingTypeRepository.FindById(filingTypeId).Returns(new FilingType { Id = filingTypeId, Name = "SomeOtherType" });

        // Act
        var result = await _filingSvc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId);

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public void GetFilersAssociatedUserWithFilingType_WhenNoFilersFoundForUser_ThrowsKeyNotFoundException()
    {
        // Arrange
        long userId = 123;
        long filingTypeId = FilingType.LobbyistReport.Id;

        _filerUserRepository.FindFilerUsersByUserId(userId).ReturnsNull();

        // Act & Assert
        Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId));
    }

    [Test]
    public void GetFilersAssociatedUserWithFilingType_WhenFilingTypeNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        long userId = 123;
        long filingTypeId = 999;

        var filerUsers = new List<FilerUser>
    {
        new() { UserId = userId, Filer = new Filer { Id = 1, FilerTypeId = FilerType.Lobbyist.Id } }
    };

        _filerUserRepository.FindFilerUsersByUserId(userId).Returns(filerUsers);
        _filingTypeRepository.FindById(filingTypeId).ReturnsNull();

        // Act & Assert
        Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId));
    }

    [Test]
    public async Task GetFilersAssociatedUserWithFilingType_ReturnsEmptyCollection_WhenNoMatchingFilerTypesExist()
    {
        // Arrange
        long userId = 123;
        long filingTypeId = FilingType.LobbyistReport.Id;

        var filerUsers = new List<FilerUser>
    {
        new() { UserId = userId, Filer = new Filer { Id = 1, FilerTypeId = FilerType.LobbyistEmployer.Id } },
        new() { UserId = userId, Filer = new Filer { Id = 2, FilerTypeId = FilerType.LobbyistEmployer.Id } },
    };

        _filerUserRepository.FindFilerUsersByUserId(userId).Returns(filerUsers);
        _filingTypeRepository.FindById(filingTypeId).Returns(new FilingType { Id = filingTypeId, Name = FilingType.LobbyistReport.Name });

        // Act
        var result = await _filingSvc.GetFilersAssociatedUserWithFilingType(userId, filingTypeId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Empty);
    }

    [Test]
    public void UpdateFilingCustomPeriod_ShouldThrowKeyNotFoundException_WhenFilingNotFound()
    {
        // Arrange
        var filingId = 123L;
        var request = new UpdateFilingCustomPeriodRequest
        {
            EndDate = null,
            StartDate = null,
            FilingPeriodId = 456L
        };

        _filingRepository.GetFilingById(filingId).ReturnsNull();

        // Act & Assert
        var exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.UpdateFilingCustomPeriod(filingId, request));

        Assert.That(exception.Message, Does.Contain("Filing not found"));
    }


    [Test]
    public async Task CreateLobbyistEmployerReport_WithNullFilingPeriod_CreatesNewReport()
    {
        // Arrange
        const long filerId = 123;
        var filingSummaryStatuses = new List<FilingSummaryStatus>
    {
        new(1, "Not Started")
    };
        var createdReport = new LobbyistEmployerReport
        {
            Id = 456,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id
        };
        var updatedReport = new LobbyistEmployerReport
        {
            Id = 456,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            OriginalId = 456
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
            .Returns(filingSummaryStatuses);
        _filingRepository.Create(Arg.Any<LobbyistEmployerReport>())
            .Returns(createdReport);
        _filingRepository.Update(Arg.Any<LobbyistEmployerReport>())
            .Returns(updatedReport);

        // Act
        var result = await _filingSvc.CreateLobbyistEmployerReport(filerId, null);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(456));
            Assert.That(result.FilerId, Is.EqualTo(filerId));
            Assert.That(result.StatusId, Is.EqualTo(FilingStatus.Draft.Id));
            Assert.That(result.FilingTypeId, Is.EqualTo(FilingType.LobbyistEmployerReport.Id));
            Assert.That(result.OriginalId, Is.EqualTo(456));
            Assert.That(result.StartDate, Is.EqualTo(DateTime.MinValue));
            Assert.That(result.EndDate, Is.EqualTo(DateTime.MinValue));
        });

        await _filingRepository.Received(1).Create(Arg.Is<LobbyistEmployerReport>(r =>
            r.FilerId == filerId &&
            r.StatusId == FilingStatus.Draft.Id &&
            r.FilingTypeId == FilingType.LobbyistEmployerReport.Id &&
            r.FilingPeriodId == null &&
            r.StartDate == DateTime.MinValue &&
            r.EndDate == DateTime.MinValue &&
            r.FilingSummaries.Count > 0));

        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r =>
            r.Id == 456 && r.OriginalId == 456));
    }

    [Test]
    public async Task CreateLobbyistEmployerReport_WithNullFilingPeriod_CreatesCorrectFilingSummaries()
    {
        // Arrange
        const long filerId = 123;
        var filingSummaryStatuses = new List<FilingSummaryStatus>
    {
        new(1, "Not Started")
    };
        var expectedSummaryTypes = new[]
        {
        FilingSummaryType.ActionsLobbiedSummary.Id,
        FilingSummaryType.CampaignContributionSummary.Id,
        FilingSummaryType.ActivityExpenseSummary.Id,
        FilingSummaryType.ToLobbyingCoalitionSummary.Id,
        FilingSummaryType.RecieveLobbyingCoalitionSummary.Id,
        FilingSummaryType.MadeToLobbyingFirmsSummary.Id,
        FilingSummaryType.LobbyistReportsSummary.Id,
        FilingSummaryType.PucActivitySummary.Id,
        FilingSummaryType.OtherPaymentsToInfluenceSummary.Id,
        FilingSummaryType.PaymentsToInHouseLobbyists.Id
    };
        var createdReport = new LobbyistEmployerReport
        {
            Id = 456,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id
        };

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
            .Returns(filingSummaryStatuses);
        _filingRepository.Create(Arg.Any<LobbyistEmployerReport>())
            .Returns(createdReport);
        _filingRepository.Update(Arg.Any<LobbyistEmployerReport>())
            .Returns(createdReport);

        // Act
        await _filingSvc.CreateLobbyistEmployerReport(filerId, null);

        // Assert
        await _filingRepository.Received(1).Create(Arg.Is<LobbyistEmployerReport>(r =>
            VerifyFilingSummaries(r.FilingSummaries, expectedSummaryTypes, filingSummaryStatuses[0].Id)));
    }

    [Test]
    public async Task CreateLobbyistReport_WithInvalidFilingPeriod_ThrowsArgumentException()
    {
        // Arrange
        const long filerId = 123;
        const long filingPeriodId = 456;
        var existingLobbyistReport = new LobbyistReport
        {
            StatusId = 1
        };

        _filingRepository.GetDraftFilingByFilerAndPeriodAndType(filerId, filingPeriodId, FilingType.LobbyistReport.Id)
            .Returns(existingLobbyistReport);

        _filingPeriodRepository.FindById(filingPeriodId).Returns((FilingPeriod)null);

        // Act & Assert
        var exception = Assert.ThrowsAsync<ArgumentNullException>(async () =>
            await _filingSvc.CreateLobbyistReport(filerId, filingPeriodId));
    }

    [Test]
    public async Task CreateLobbyistReport_WithValidFilingPeriod_CalculatesAmountsAndUpdatesSummaries()
    {
        // Arrange
        const long filerId = 123;
        const long filingPeriodId = 456;

        var filingPeriod = new FilingPeriod
        {
            Id = filingPeriodId,
            StartDate = new DateTime(2023, 1, 1),
            EndDate = new DateTime(2023, 3, 31)
        };

        var existingLobbyistReport = new LobbyistReport
        {
            Id = 789,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            FilingPeriodId = filingPeriodId,
            FilingSummaries = new List<FilingSummary>(),
            StatusId = 1
        };

        var filingSummaryStatus = new FilingSummaryStatus(1, "NotStarted");

        _filingRepository.GetDraftFilingByFilerAndPeriodAndType(filerId, filingPeriodId, FilingType.LobbyistReport.Id)
            .Returns(existingLobbyistReport);

        _filingPeriodRepository.FindById(filingPeriodId).Returns(filingPeriod);

        _filingSummaryStatusRepository.GetAllFilingSummaryStatuses()
            .Returns(new List<FilingSummaryStatus> { filingSummaryStatus });

        // Mock the previous reports amounts calculation
        var expectedAmounts = new Dictionary<long, decimal>
    {
        { FilingSummaryType.CampaignContributionSummary.Id, 0m },
        { FilingSummaryType.ActivityExpenseSummary.Id, 0m }
    };

        // Act
        var result = await _filingSvc.CreateLobbyistReport(filerId, filingPeriodId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.SameAs(existingLobbyistReport));
            Assert.That(result.FilingSummaries, Has.Count.EqualTo(2));

            var campaignSummary = result.FilingSummaries.FirstOrDefault(s =>
                s.FilingSummaryTypeId == FilingSummaryType.CampaignContributionSummary.Id);
            Assert.That(campaignSummary, Is.Not.Null);
            Assert.That(campaignSummary!.ToDateAmount, Is.EqualTo(expectedAmounts[FilingSummaryType.CampaignContributionSummary.Id]));

            var activitySummary = result.FilingSummaries.FirstOrDefault(s =>
                s.FilingSummaryTypeId == FilingSummaryType.ActivityExpenseSummary.Id);
            Assert.That(activitySummary, Is.Not.Null);
            Assert.That(activitySummary!.ToDateAmount, Is.EqualTo(expectedAmounts[FilingSummaryType.ActivityExpenseSummary.Id]));
        });

        await _filingRepository.Received(1).Update(existingLobbyistReport);
    }

    // Helper method to verify filing summaries
    private static bool VerifyFilingSummaries(
        ICollection<FilingSummary> summaries,
        IEnumerable<long> expectedTypeIds,
        long? expectedStatusId)
    {
        if (summaries.Count != expectedTypeIds.Count())
        {
            return false;
        }

        foreach (var typeId in expectedTypeIds)
        {
            if (!summaries.Any(s =>
                s.FilingSummaryTypeId == typeId &&
                s.PeriodAmount == 0 &&
                s.ToDateAmount == 0 &&
                s.FilingSummaryStatusId == expectedStatusId &&
                s.NoActivityToReport == false))
            {
                return false;
            }
        }

        return true;
    }

    #endregion

    #region Private
    private static LobbyistReport GenerateSampleLobbyistReport(long id, long statusId = 3)
    {
        return new()
        {
            Id = id,
            ParentId = id + 1,
            StatusId = statusId,
            FilerId = 1,
            FilingPeriodId = 1,
            StartDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local).AddMonths(-1),
            EndDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            SubmittedDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            FilingSummaries = new List<FilingSummary>
            {
                new ()
                {
                    Id = 1,
                    FilingSummaryTypeId = 1,
                    PeriodAmount = 1m,
                    ToDateAmount = 1m,
                }
            },
            FilingTransactions = new List<FilingTransaction>()
            {
                new()
                {
                    FilingId = id,
                    TransactionId = 2,
                    Transaction = new ActivityExpense()
                    {
                        Id = 1,
                        TransactionDate = new DateTime(2024, 10, 1, 0, 0, 0, 0),
                        Notes = "Expense for event hosting",
                        ActivityExpenseTypeId = 101,
                        CreditCardCompanyName = "Visa",
                        ActivityDescription = "Catering for lobby event",
                        MonetaryTypeId = 2,
                        Amount = new Currency(1200.00m),
                        FilerId = 501,
                        ContactId = 1001,
                        ActionsLobbied = new List<ActionsLobbied?>
                        {
                            new()
                            {
                                BillId = 1,
                                AgencyDescription = "testing"
                            }
                        },
                        TransactionReportablePersons = new List<TransactionReportablePerson>
                        {
                            new()
                            {
                                TransactionId = 1,
                                FilerContactId = 2002,
                                Name = "John Public",
                                OfficialPosition = "Legislative Analyst",
                                OfficialPositionDescription = "Analyzes legislation",
                                Agency = "State Assembly"
                            }
                        },
                        Contact = new IndividualContact()
                        {
                            Id = 1001,
                            FilerId = 2001,
                            CreatedBy = 1,
                            ModifiedBy = 1,
                            Employer = "Tech Corp",
                            Occupation = "Engineer",
                            FirstName = "Alice",
                            MiddleName = "B.",
                            LastName = "Smith",
                            AddressListId = 3001,
                            PhoneNumberListId = 4001,
                            EmailAddressListId = 5001,
                            ContactFilerId = 6001,
                            AddressList = new AddressList
                            {
                                Addresses = new List<Address>
                                {
                                    new()
                                    {
                                        Street = "street",
                                        Street2 = "street2",
                                        City = "city",
                                        State = "CA",
                                        Zip = "12345",
                                        Country = "US",
                                        Type = "home",
                                        Purpose = "purpose"
                                    }
                                }
                            },
                            PhoneNumberList = new()
                            {
                                PhoneNumbers = new()
                                {
                                    new()
                                    {
                                        InternationalNumber = false,
                                        CountryCode = "1",
                                        Number = "1231234",
                                        Type = "Phone",
                                    }
                                }
                            }
                        }
                    }
                },
                new()
                {
                    FilingId = id,
                    TransactionId = 2,
                    Transaction = new ActivityExpense()
                    {
                        Id = 1,
                        TransactionDate = new DateTime(2024, 10, 1, 0, 0, 0, 0),
                        Notes = "Expense for event hosting",
                        ActivityExpenseTypeId = 101,
                        CreditCardCompanyName = "Visa",
                        ActivityDescription = "Catering for lobby event",
                        MonetaryTypeId = 2,
                        Amount = new Currency(1200.00m),
                        FilerId = 501,
                        ContactId = 1001,
                        Contact = new IndividualContact()
                        {
                            Id = 1002,
                            FilerId = 2001,
                            CreatedBy = 1,
                            ModifiedBy = 1,
                            Employer = "Tech Corp",
                            Occupation = "Engineer",
                            FirstName = "Alice",
                            MiddleName = "B.",
                            LastName = "Smith",
                            AddressListId = 3001,
                            PhoneNumberListId = 4001,
                            EmailAddressListId = 5001,
                            ContactFilerId = 6001,
                        }
                    }
                }
            }
        };
    }

    private static List<FilingSummary> GenerateFilingSummaries()
    {
        return new List<FilingSummary>()
        {
            new()
            {
                Id = 1,
                FilingId = 1,
                FilingSummaryTypeId = FilingSummaryType.PaymentReceivedSummary.Id,
                FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            },
            new()
            {
                Id = 2,
                FilingId = 1,
                FilingSummaryTypeId = FilingSummaryType.PaymentReceivedSummary.Id,
                FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            },
            new()
            {
                Id = 3,
                FilingId = 1,
                FilingSummaryTypeId = FilingSummaryType.PersonReceiving1000OrMoreSummary.Id,
                FilingSummaryStatusId = FilingSummaryStatus.NotStarted.Id,
                PeriodAmount = 1m,
                ToDateAmount = 1m,
            },
        };
    }

    [Test]
    public async Task CalculateLegislativeSessionToDateAmountsBySummaryType_ReturnsCorrectSums()
    {
        // Arrange
        const long filerId = 1;
        var filingSummaryTypes = new long[] { 1, 2, 3 };
        var now = _dateNow;
        var filingPeriod = new FilingPeriod
        {
            StartDate = now.AddDays(-30),
            EndDate = now
        };
        // 1) Accepted, within range
        var inRange1 = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = filingPeriod.StartDate.AddDays(1),
            EndDate = filingPeriod.StartDate.AddDays(5),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 1, PeriodAmount = 100m,ToDateAmount=0 },
            new() { FilingSummaryTypeId = 2, PeriodAmount = 200m ,ToDateAmount=0}
        }
        };
        // 2) Incomplete, within range
        var inRange2 = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Incomplete.Id,
            StartDate = filingPeriod.StartDate.AddDays(10),
            EndDate = filingPeriod.EndDate.AddDays(-1),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 1, PeriodAmount = 300m , ToDateAmount = 0},
            new() { FilingSummaryTypeId = 3, PeriodAmount = 400m   ,ToDateAmount=0 }
        }
        };
        // 3) Accepted but *before* the legislative start date → should be ignored
        var beforeSession = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = filingPeriod.StartDate.AddYears(-1),
            EndDate = filingPeriod.StartDate.AddDays(-1),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 1, PeriodAmount = 9999m , ToDateAmount = 0}
        }
        };
        // 4) Different filer → should be ignored
        var otherFiler = new Filing
        {
            FilerId = 2,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = filingPeriod.StartDate.AddDays(1),
            EndDate = filingPeriod.StartDate.AddDays(2),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 1, PeriodAmount = 123m , ToDateAmount = 0}
        }
        };

        _filingRepository
            .GetAllByFilerId(filerId)
            .Returns(new[] { inRange1, inRange2, beforeSession, otherFiler });

        // Act
        // Use reflection since the method is private:
        var method = typeof(FilingSvc)
            .GetMethod(
                "CalculateLegislativeSessionToDateAmountsBySummaryType",
                BindingFlags.NonPublic | BindingFlags.Instance
            );
        var task = (Task<Dictionary<long, decimal>>)method?.Invoke(
     _filingSvc,
     new object[] { filerId, filingSummaryTypes, filingPeriod }
 )!;
        var result = await task;

        // Assert
        // Expected sums:
        //  type 1 → 100 + 300 = 400
        //  type 2 → 200
        //  type 3 → 400
        Assert.Multiple(() =>
        {
            Assert.That(result, Contains.Key(1L), "should contain key=1");
            Assert.That(result[1], Is.EqualTo(400m), "sum for type 1");

            Assert.That(result, Contains.Key(2L), "should contain key=2");
            Assert.That(result[2], Is.EqualTo(200m), "sum for type 2");

            Assert.That(result, Contains.Key(3L), "should contain key=3");
            Assert.That(result[3], Is.EqualTo(400m), "sum for type 3");
        });
    }

    [Test]
    public async Task CalculateLegislativeSessionToDateAmountsBySummaryType_ExcludesFilingsEndingAtPeriodEnd()
    {
        // Arrange
        const long filerId = 1;
        var filingSummaryTypes = new long[] { 1L };
        var now = _dateNow;

        // 30-day window
        var filingPeriod = new FilingPeriod
        {
            StartDate = now.AddDays(-30),
            EndDate = now
        };

        // This filing has EndDate == filingPeriod.EndDate → should be excluded.
        var atEndDateFiling = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = filingPeriod.StartDate.AddDays(1),
            EndDate = filingPeriod.EndDate,
            FilingSummaries = new List<FilingSummary>
        {
            // even though we set both PeriodAmount *and* ToDatePeriod,
            // only PeriodAmount is ever summed—and here it must be ignored.
            new() {
                FilingSummaryTypeId = 1,
                PeriodAmount        = 100m,
                ToDateAmount        = 999m
            }
        }
        };

        // Stub repository to return only our edge‐case filing
        _filingRepository
            .GetAllByFilerId(filerId)
            .Returns(new[] { atEndDateFiling });

        // Act
        var method = typeof(FilingSvc)
            .GetMethod(
                "CalculateLegislativeSessionToDateAmountsBySummaryType",
                BindingFlags.NonPublic | BindingFlags.Instance
            );

        var task = (Task<Dictionary<long, decimal>>)method!
            .Invoke(_filingSvc, new object[] { filerId, filingSummaryTypes, filingPeriod })!;
        var result = await task;

        // Assert
        // We still get an entry for type=1, but its sum is zero because the only filing
        // was excluded by the EndDate < period.EndDate check.
        Assert.Multiple(() =>
        {
            Assert.That(result, Contains.Key(1L), "key for summary type 1 must be present");
            Assert.That(result[1L], Is.EqualTo(0m), "no filings should be summed");
        });
    }

    [Test]
    public async Task CalculateLegislativeSessionToDateAmountsBySummaryType_FiltersByStatusFilerAndType()
    {
        // Arrange
        const long filerId = 1;
        var filingSummaryTypes = new long[] { 1L, 2L };
        var periodStart = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var periodEnd = new DateTime(2025, 6, 1, 0, 0, 0, DateTimeKind.Utc);
        var filingPeriod = new FilingPeriod
        {
            StartDate = periodStart,
            EndDate = periodEnd
        };

        // 1) Good: Accepted, correct type, correct filer, within dates → contributes 100 to type=1
        var goodAccepted = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = periodStart.AddDays(1),
            EndDate = periodStart.AddDays(2),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 1L, PeriodAmount = 100m, ToDateAmount = 0m }
        }
        };

        // 2) Good: Incomplete, correct type, correct filer, within dates → contributes 50 to type=2
        var goodIncomplete = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Incomplete.Id,
            StartDate = periodStart.AddDays(3),
            EndDate = periodStart.AddDays(4),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 2L, PeriodAmount = 50m, ToDateAmount = 0m }
        }
        };

        // 3) Wrong status (neither Accepted nor Incomplete) → should be excluded
        var wrongStatus = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = 0,  // not Accepted.Id or Incomplete.Id
            StartDate = periodStart.AddDays(5),
            EndDate = periodStart.AddDays(6),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 1L, PeriodAmount = 999m, ToDateAmount = 0m }
        }
        };

        // 4) Wrong type → should be excluded
        var wrongType = new Filing
        {
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id + 1, // some other type
            StatusId = FilingStatus.Accepted.Id,
            StartDate = periodStart.AddDays(7),
            EndDate = periodStart.AddDays(8),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 2L, PeriodAmount = 888m, ToDateAmount = 0m }
        }
        };

        // 5) Wrong filer → should be excluded
        var wrongFiler = new Filing
        {
            FilerId = 2,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = periodStart.AddDays(9),
            EndDate = periodStart.AddDays(10),
            FilingSummaries = new List<FilingSummary>
        {
            new() { FilingSummaryTypeId = 1L, PeriodAmount = 777m, ToDateAmount = 0m }
        }
        };

        _filingRepository
            .GetAllByFilerId(filerId)
            .Returns(new[] { goodAccepted, goodIncomplete, wrongStatus, wrongType, wrongFiler });

        // Act
        var method = typeof(FilingSvc)
            .GetMethod(
                "CalculateLegislativeSessionToDateAmountsBySummaryType",
                BindingFlags.NonPublic | BindingFlags.Instance
            );
        var task = (Task<Dictionary<long, decimal>>)method!
            .Invoke(_filingSvc, new object[] { filerId, filingSummaryTypes, filingPeriod })!;
        var result = await task;

        // Assert
        Assert.Multiple(() =>
        {
            // Only the two "good" filings contribute
            Assert.That(result[1L], Is.EqualTo(100m), "Type 1 should sum only the Accepted filing");
            Assert.That(result[2L], Is.EqualTo(50m), "Type 2 should sum only the Incomplete filing");
        });
    }

    [Test]
    [TestCase("1-USA", "5551234567", ExpectedResult = "15551234567")]
    [TestCase("44-UK", "2071234567", ExpectedResult = "442071234567")]
    [TestCase(null, "5551234567", ExpectedResult = "")]
    [TestCase("1-USA", null, ExpectedResult = "")]
    [TestCase(null, null, ExpectedResult = "")]
    public string FormatFilerContactDecisionsPhoneNumber_ReturnsExpectedFormat(string? countryCode, string? number)
    {
        // Arrange
        var contact = new OrganizationContact
        {
            PhoneNumberList = new PhoneNumberList
            {
                PhoneNumbers = new List<PhoneNumber>
                {
                    new() { Type = "Phone", CountryCode = countryCode, Number = number ?? "" }
                }
            },
            OrganizationName = "Irrelevent"
        };

        var method = typeof(FilingSvc) // Replace with the class that defines FormatPhoneNumberForValidation
            .GetMethod("FormatFilerContactDecisionsPhoneNumber", BindingFlags.NonPublic | BindingFlags.Static);

        // Act
        var result = method?.Invoke(null, new object[] { contact, "Phone" }) as string;

        // Assert
        Assert.That(result, Is.Not.Null); // Protects against method returning null instead of string.Empty
        return result!;
    }

    #endregion

    // Example mock data for Address
    private static Address GetMockAddress() => new Address
    {
        Street = "123 Street",
        City = "Honolulu",
        State = "HI",
        Zip = "96868",
        Country = "USA",
        Type = "Business",
        Purpose = "Candidate"
    };

    private void GetMockLobbyistEmployerTransactions()
    {
        var filingId = 123L;

        var activityExpenses = new List<ActivityExpense>
        {
            new ActivityExpense
            {
                Amount = new Currency(10),
                Contact = new IndividualContact
                {
                    FirstName = "John",
                    LastName = "Doe",
                    AddressList = new AddressList
                    {
                        Addresses =
                        [
                            GetMockAddress()
                        ]
                    }
                },
                TransactionReportablePersons = new List<TransactionReportablePerson>
                {
                    new TransactionReportablePerson
                    {
                        TransactionId = 1,
                        FilerContactId = 2,
                        Name = "Person1",
                        OfficialPosition = "Position1",
                        OfficialPositionDescription = "Desc1",
                        Agency = "Agency1",
                        Amount = new Currency(10)
                    }
                }
            }
        };

        var campaignContributions = new List<LobbyingCampaignContribution>
        {
            new LobbyingCampaignContribution
            {
                TransactionDate = DateTime.UtcNow,
                Amount = new Currency(100),
                RecipientFilerId = 1,
                FilerId = 2,
                ContributorFiler = new Filer(),
                NonCommitteeRecipientName = "NonComm",
                ContributorFilerId = 3,
                NonFilerContributorName = "NonFiler",
                SeparateAccountName = "Account",
            }
        };

        var paymentMadeToLobbyingCoalition = new List<PaymentMadeToLobbyingCoalitionResponse>
        {
            new PaymentMadeToLobbyingCoalitionResponse
            {
                CumulativeAmount = 10, AmountThisPeriod = 5, CoalitionName = "Coalition"
            }
        };

        var paymentReceivedByCoalition = new List<PaymentReceiveLobbyingCoalitionResponse>
        {
            new()
            {
                Contact = new OrganizationContactResponseDto(new OrganizationContact()
                {
                    FilerContactType = FilerContactType.Organization,
                    FirstName = "OrgFirst",
                    LastName = "OrgLast",
                    OrganizationName = "OrgName",
                    EmailAddressList = new EmailAddressList
                    {
                        EmailAddresses =
                        [
                            new EmailAddress { Email = "<EMAIL>" }
                        ]
                    },
                    AddressList = new AddressList
                    {
                        Addresses =
                        [
                            GetMockAddress()
                        ]
                    }
                }),
                CoalitionName = "Coalition",
                AmountThisPeriod = new Currency(10),
                CumulativeAmount = new Currency(10)
            },
            new()
            {
                Contact = new IndividualContactResponseDto(new IndividualContact()
                {
                    FilerContactType = FilerContactType.Individual,
                    FirstName = "IndividualFirst",
                    LastName = "IndividualLast",
                    EmailAddressList = new EmailAddressList
                    {
                        EmailAddresses =
                        [
                            new EmailAddress { Email = "<EMAIL>" }
                        ]
                    },
                    AddressList = new AddressList
                    {
                        Addresses =
                        [
                            GetMockAddress()
                        ]
                    }
                }),
                CoalitionName = "Coalition",
                AmountThisPeriod = new Currency(10),
                CumulativeAmount = new Currency(10)
            },
            new()
            {
                Contact = new CandidateContactResponseDto(new CandidateContact()
                {
                    FilerContactType = FilerContactType.Candidate,
                    FirstName = "candidateFirst",
                    LastName = "candidateLast",
                    EmailAddressList = new EmailAddressList
                    {
                        EmailAddresses =
                        [
                            new EmailAddress { Email = "<EMAIL>" }
                        ]
                    },
                    AddressList = new AddressList
                    {
                        Addresses =
                        [
                            GetMockAddress()
                        ]
                    },
                    District = "district",
                    Jurisdiction = "Jurisdiction",
                    OfficeSought = "OfficeSought"
                }),
                CoalitionName = "Coalition",
                AmountThisPeriod = new Currency(10),
                CumulativeAmount = new Currency(10)
            },
            new()
            {
                Contact = new CommitteeContactResponseDto(new CommitteeContact()
                {
                    FilerContactType = FilerContactType.Committee,
                    FirstName = "committeeFirst",
                    LastName = "committeeLast",
                    EmailAddressList = new EmailAddressList
                    {
                        EmailAddresses =
                        [
                            new EmailAddress { Email = "<EMAIL>" }
                        ]
                    },
                    AddressList = new AddressList
                    {
                        Addresses =
                        [
                            GetMockAddress()
                        ]
                    },
                    CommitteeType = "CommitteeType"
                }),
                CoalitionName = "Coalition",
                AmountThisPeriod = new Currency(10),
                CumulativeAmount = new Currency(10)
            },
            new()
            {
                Contact = new FilerCommitteeContactResponseDto(new FilerCommitteeContact()
                {
                    FilerContactType = FilerContactType.Filer,
                    FirstName = "FilerCommitteeFirst",
                    LastName = "FilerCommitteeLast",
                    EmailAddressList = new EmailAddressList
                    {
                        EmailAddresses =
                        [
                            new EmailAddress { Email = "<EMAIL>" }
                        ]
                    },
                    AddressList = new AddressList
                    {
                        Addresses =
                        [
                            GetMockAddress()
                        ]
                    },
                }),
                CoalitionName = "Coalition",
                AmountThisPeriod = new Currency(10),
                CumulativeAmount = new Currency(10)
            }
        };

        var otherPayments = new List<OtherPaymentsToInfluenceResponse>
        {
            new()
            {
                PaymentCodeDescription = "Desc",
                Amount = 20,
                PaymentCodeId = 1,
                OtherActionsLobbied = "Other",
                CumulativeAmount = new Currency(10)
            }
        };

        var paymentMadeLobbyingFirm = new List<PaymentMadeToLobbyingFirmsResponse>
        {
            new()
            {
                FirmName = "Firm",
                FeesAndRetainersAmount = 1,
                ReimbursementOfExpensesAmount = 2,
                AdvancesOrOtherPaymentsAmount = 3,
                AdvancesOrOtherPaymentsExplanation = "Adv",
                Contact = new ContactResponseDto(new OrganizationContact
                {
                    AddressList = new AddressList
                    {
                        Addresses =
                        [
                            GetMockAddress()
                        ]
                    },
                    OrganizationName = "Organication"
                }),
                AmountThisPeriod = new Currency(10),
                CumulativeAmount = new Currency(10)
            }
        };

        var actionsLobbied = new ActionsLobbiedByEntityResponse
        {
            AgencyActions =
                [new ActionsLobbiedResponseDto { AdministrativeAction = "Action", AgencyName = "Agency" }],
            AssemblyBillActions = [new ActionsLobbiedResponseDto { BillNumber = "AB1" }],
            SenateBillActions = [new ActionsLobbiedResponseDto { BillNumber = "SB1" }]
        };

        _transactionRepository.GetAllActivityExpenseTransactionsForFiling(filingId).Returns(activityExpenses);
        _transactionRepository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId)
            .Returns(campaignContributions);
        _transactionRepository.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, Arg.Any<DateTime>())
            .Returns(paymentMadeToLobbyingCoalition);
        _transactionHelperSvc
            .GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId, Arg.Any<DateTime>())
            .Returns(paymentReceivedByCoalition);
        _transactionRepository.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, Arg.Any<DateTime>())
            .Returns(otherPayments);
        _transactionHelperSvc.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filingId, Arg.Any<DateTime>())
            .Returns(paymentMadeLobbyingFirm);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbied);
    }

    #region UpdateContributionsInExistingStatements Tests

    [Test]
    public async Task UpdateContributionsInExistingStatements_ValidInput_UpdatesAndReturnsSuccessful()
    {
        // Arrange
        var filingId = 123L;
        var contributionsInExistingStatements = true;
        var relatedFilerIds = new List<long> { 1, 2, 3 };

        var mockReport = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = 456L,
            StatusId = FilingStatus.Draft.Id,
            ContributionsInExistingStatements = false
        };

        _filingRepository.FindById(filingId)
            .Returns(mockReport);

        var decisionResponse = new List<WorkFlowError>();

        _decisionsSvc.InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            DecisionsWorkflow.ContributionsInExistingStatementsRuleset,
            Arg.Any<DecisionsContributionsInExistingStatements>(),
            true)
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.UpdateContributionsInExistingStatements(filingId, contributionsInExistingStatements, relatedFilerIds);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilingId, Is.EqualTo(filingId));
        });

        // Verify related filers were deleted and new ones added
        await _filingRelatedFilerRepository.Received(1).DeleteAllFilingRelatedFilerforFiling(filingId);

        // Verify each related filer was added
        foreach (var relatedFilerId in relatedFilerIds)
        {
            await _filingRelatedFilerRepository.Received(1).AddFilingRelatedFiler(filingId, relatedFilerId);
        }

        // Verify filing was updated since ContributionsInExistingStatements changed
        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r =>
            r.Id == filingId && r.ContributionsInExistingStatements == contributionsInExistingStatements));
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ValidationErrors_ReturnsInvalidResponse()
    {
        // Arrange
        var filingId = 123L;
        var contributionsInExistingStatements = true;
        var relatedFilerIds = new List<long> { 1, 2, 3 };

        var mockReport = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = 456L,
            StatusId = FilingStatus.Draft.Id,
            ContributionsInExistingStatements = false
        };

        _filingRepository.FindById(filingId)
            .Returns(mockReport);

        var validationErrors = new List<WorkFlowError>
        {
            new("field1", "code1", "error", "Error message 1")
        };

        _decisionsSvc.InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            DecisionsWorkflow.ContributionsInExistingStatementsRuleset,
            Arg.Any<DecisionsContributionsInExistingStatements>(),
            true)
            .Returns(validationErrors);

        // Act
        var result = await _filingSvc.UpdateContributionsInExistingStatements(filingId, contributionsInExistingStatements, relatedFilerIds);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EqualTo(validationErrors));
            Assert.That(result.FilingId, Is.EqualTo(filingId));
        });

        // Verify no filer operations occurred
        await _filingRelatedFilerRepository.DidNotReceive().DeleteAllFilingRelatedFilerforFiling(Arg.Any<long>());
        await _filingRelatedFilerRepository.DidNotReceive().AddFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());

        // Verify filing was not updated
        await _filingRepository.DidNotReceive().Update(Arg.Any<LobbyistEmployerReport>());
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_ContributionsFalse_DeletesRelatedFilersOnly()
    {
        // Arrange
        var filingId = 123L;
        var contributionsInExistingStatements = false;
        var relatedFilerIds = new List<long>(); // Empty since contributions are false

        var mockReport = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = 456L,
            StatusId = FilingStatus.Draft.Id,
            ContributionsInExistingStatements = true // Different from input to trigger update
        };

        _filingRepository.FindById(filingId)
            .Returns(mockReport);

        var decisionResponse = new List<WorkFlowError>();

        _decisionsSvc.InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            DecisionsWorkflow.ContributionsInExistingStatementsRuleset,
            Arg.Any<DecisionsContributionsInExistingStatements>(),
            true)
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.UpdateContributionsInExistingStatements(filingId, contributionsInExistingStatements, relatedFilerIds);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilingId, Is.EqualTo(filingId));
        });

        // Verify related filers were deleted
        await _filingRelatedFilerRepository.Received(1).DeleteAllFilingRelatedFilerforFiling(filingId);

        // Verify no filers were added
        await _filingRelatedFilerRepository.DidNotReceive().AddFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());

        // Verify filing was updated
        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r =>
            r.Id == filingId && r.ContributionsInExistingStatements == contributionsInExistingStatements));
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_FilingNotFound_ReturnsNull()
    {
        // Arrange
        var filingId = 999L;
        var contributionsInExistingStatements = true;
        var relatedFilerIds = new List<long> { 1, 2, 3 };

        // Act
        var result = await _filingSvc.UpdateContributionsInExistingStatements(filingId, contributionsInExistingStatements, relatedFilerIds);

        // Assert
        Assert.That(result, Is.Null);

        // Verify no operations occurred
        await _decisionsSvc.DidNotReceive().InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsContributionsInExistingStatements>(), Arg.Any<bool>());
        await _filingRelatedFilerRepository.DidNotReceive().DeleteAllFilingRelatedFilerforFiling(Arg.Any<long>());
        await _filingRelatedFilerRepository.DidNotReceive().AddFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_NotLobbyistEmployerReport_ReturnsNull()
    {
        // Arrange
        var filingId = 123L;
        var contributionsInExistingStatements = true;
        var relatedFilerIds = new List<long> { 1, 2, 3 };

        // Return a different type of filing
        var nonLobbyistEmployerReport = new Filing
        {
            Id = filingId,
            FilerId = 456L,
            StatusId = FilingStatus.Draft.Id
        };

        _filingRepository.FindById(filingId)
            .Returns(nonLobbyistEmployerReport);

        // Act
        var result = await _filingSvc.UpdateContributionsInExistingStatements(filingId, contributionsInExistingStatements, relatedFilerIds);

        // Assert
        Assert.That(result, Is.Null);

        // Verify no operations occurred
        await _decisionsSvc.DidNotReceive().InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            Arg.Any<DecisionsWorkflow>(), Arg.Any<DecisionsContributionsInExistingStatements>(), Arg.Any<bool>());
        await _filingRelatedFilerRepository.DidNotReceive().DeleteAllFilingRelatedFilerforFiling(Arg.Any<long>());
        await _filingRelatedFilerRepository.DidNotReceive().AddFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());
        await _filingRepository.DidNotReceive().Update(Arg.Any<Filing>());
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_NullsHandledGracefully()
    {
        // Arrange
        var filingId = 123L;
        bool? contributionsInExistingStatements = null;
        List<long> relatedFilerIds = new List<long>();

        var mockReport = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = 456L,
            StatusId = FilingStatus.Draft.Id,
            ContributionsInExistingStatements = true
        };

        _filingRepository.FindById(filingId)
            .Returns(mockReport);

        var decisionResponse = new List<WorkFlowError>();

        _decisionsSvc.InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            DecisionsWorkflow.ContributionsInExistingStatementsRuleset,
            Arg.Any<DecisionsContributionsInExistingStatements>(),
            true)
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.UpdateContributionsInExistingStatements(filingId, contributionsInExistingStatements, relatedFilerIds);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilingId, Is.EqualTo(filingId));
        });

        // Verify related filers were deleted (since null/false contributions should clear related filers)
        await _filingRelatedFilerRepository.Received(1).DeleteAllFilingRelatedFilerforFiling(filingId);

        // Verify no filers were added
        await _filingRelatedFilerRepository.DidNotReceive().AddFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());

        // Verify filing was updated with false value for ContributionsInExistingStatements
        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r =>
            r.Id == filingId && r.ContributionsInExistingStatements == false));
    }

    [Test]
    public async Task UpdateContributionsInExistingStatements_NoStatusChange_SkipsUpdate()
    {
        // Arrange
        var filingId = 123L;
        var contributionsInExistingStatements = false;
        var relatedFilerIds = new List<long>();

        var mockReport = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = 456L,
            StatusId = FilingStatus.Draft.Id,
            ContributionsInExistingStatements = false
        };

        _filingRepository.FindById(filingId)
            .Returns(mockReport);

        var decisionResponse = new List<WorkFlowError>();

        _decisionsSvc.InitiateWorkflow<DecisionsContributionsInExistingStatements, List<WorkFlowError>>(
            DecisionsWorkflow.ContributionsInExistingStatementsRuleset,
            Arg.Any<DecisionsContributionsInExistingStatements>(),
            true)
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.UpdateContributionsInExistingStatements(filingId, contributionsInExistingStatements, relatedFilerIds);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(result.FilingId, Is.EqualTo(filingId));
        });

        // Verify related filers were deleted
        await _filingRelatedFilerRepository.Received(1).DeleteAllFilingRelatedFilerforFiling(filingId);

        // Verify no filers were added
        await _filingRelatedFilerRepository.DidNotReceive().AddFilingRelatedFiler(Arg.Any<long>(), Arg.Any<long>());

        // Verify filing was NOT updated since value didn't change
        await _filingRepository.DidNotReceive().Update(Arg.Any<LobbyistEmployerReport>());
    }

    #endregion

    #region SearchFilers Tests
    [Test]
    public async Task SearchFilers_ValidQuery_ReturnsMatchingFilers()
    {
        // Arrange
        var query = "Doe";
        var filerTypeId = 1L;

        var mockFilers = new List<Filer>
        {
            new() {
                Id = 1,
                FilerTypeId = filerTypeId,
                FilerType = FilerType.MajorDonor,
                CurrentRegistration = new CandidateIntentionStatement
                {
                    Name = "John Doe",
                    StatusId = RegistrationStatus.Accepted.Id
                }
            },
            new() {
                Id = 2,
                FilerTypeId = filerTypeId,
                FilerType = FilerType.MajorDonor,
                CurrentRegistration = new CandidateIntentionStatement
                {
                    Name = "Jane Doe",
                    StatusId = RegistrationStatus.Accepted.Id
                }
            }
        };

        _filerRepository.SearchFilers(query, filerTypeId)
            .Returns(mockFilers);

        // Act
        var result = await _filingSvc.SearchFilers(query, filerTypeId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(2));

        var resultList = result.ToList();
        Assert.Multiple(() =>
        {
            Assert.That(resultList[0].Id, Is.EqualTo(1));
        });

        await _filerRepository.Received(1).SearchFilers(query, filerTypeId);
    }

    [Test]
    public async Task SearchFilers_EmptyQuery_ReturnsEmptyResult()
    {
        // Arrange
        var query = "";
        var filerTypeId = 1L;

        // Act
        var result = await _filingSvc.SearchFilers(query, filerTypeId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(0));

        await _filerRepository.DidNotReceive().SearchFilers(Arg.Any<string>(), Arg.Any<long>());
    }


    [Test]
    public async Task UpdateFilingCustomPeriod_WithCustomDates_UpdatesFilingWithCustomDates()
    {
        // Arrange
        long filingId = 123;
        long filerId = 456;
        var startDate = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Utc);
        var endDate = new DateTime(2023, 6, 30, 0, 0, 0, DateTimeKind.Utc);

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StartDate = _dateNow,
            EndDate = _dateNow.AddDays(30),
            StatusId = 1
        };

        var request = new UpdateFilingCustomPeriodRequest
        {
            FilingPeriodId = null,
            StartDate = startDate,
            EndDate = endDate
        };
        var lobbyistRegistration = new LobbyistEmployer
        {
            Name = "First Last",
            StatusId = RegistrationStatus.Accepted.Id,
            SubmittedAt = _dateNow,
            EmployerName = "Name",
            EmployerType = "1",
            BusinessActivity = "Foo",
            BusinessDescription = "Foo",
            InterestType = "1",
            NumberOfMembers = 0
        };
        _ = _registrationRepository.FindLobbyingRegistrationByFilerId<LobbyistEmployer>(filerId).Returns(lobbyistRegistration);

        _filingRepository.FindById(filingId).Returns(filing);
        _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing>());


        var expectedStatuses = new List<FilingStatus>
        {
            FilingStatus.Draft,
            FilingStatus.Submitted,
            FilingStatus.Accepted,
            FilingStatus.Cancelled,
            FilingStatus.Incomplete,
            FilingStatus.Pending
        };
        _filingStatusRepository.GetAll().Returns(expectedStatuses);

        var filingPeriods = new List<FilingPeriod>
        {
            new() { Id = 1, Name = "Period 1", StartDate = _dateNow.AddMonths(-1) },
            new() { Id = 2, Name = "Period 2", StartDate = _dateNow },
            new()
            {
                Id = 3,
                Name = "Period 3",
                StartDate = new DateTime(_dateNow.Year, 1, 1, 0, 0, 0, DateTimeKind.Local).AddDays(-1)
            }, //Should filter out where start date is before start of year
            new()
            {
                Id = 3,
                Name = "Period 4",
                EndDate = _filingSvc.GetLegislativeEndDateForDate(_dateNow).AddDays(1)
            } //Should filter out where end date is after end of legislative session
        };

        _ = _filingPeriodRepository.GetAll().Returns(filingPeriods);
        _ = _dependencies.FilingRepository.GetFilingById(Arg.Any<long>()).Returns(filing);

        // Act
        var result = await _filingSvc.UpdateFilingCustomPeriod(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(filingId));
            Assert.That(result.StartDate, Is.EqualTo(startDate));
            Assert.That(result.EndDate, Is.EqualTo(endDate));
        });
    }

    [Test]
    public void UpdateFilingCustomPeriod_FilingNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        long filingId = 999;
        var request = new UpdateFilingCustomPeriodRequest { FilingPeriodId = 1, StartDate = null, EndDate = null };

        _filingRepository.FindById(filingId).Returns((Filing)null);

        // Act & Assert
        var exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.UpdateFilingCustomPeriod(filingId, request));

        Assert.That(exception.Message, Does.Contain("Filing not found"));
    }

    [Test]
    public void UpdateFilingCustomPeriod_FilingPeriodNotFound_ThrowsKeyNotFoundException()
    {
        // Arrange
        long filingId = 123;
        long filerId = 456;
        long invalidFilingPeriodId = 999;

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = 1
        };

        var request = new UpdateFilingCustomPeriodRequest
        {
            FilingPeriodId = invalidFilingPeriodId,
            StartDate = null,
            EndDate = null
        };

        _filingRepository.FindById(filingId).Returns(filing);


        // Act & Assert
        var exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.UpdateFilingCustomPeriod(filingId, request));

        Assert.That(exception.Message, Does.Contain("Filing not found. Id=123"));
    }

    [Test]
    public void UpdateFilingCustomPeriod_InvalidDateRange_ThrowsArgumentException()
    {
        // Arrange
        long filingId = 123;
        long filerId = 456;
        var startDate = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Utc);
        var endDate = new DateTime(2023, 6, 30, 0, 0, 0, DateTimeKind.Utc);

        var filing = new Filing
        {
            Id = filingId,
            FilerId = filerId,
            FilingTypeId = FilingType.LobbyistReport.Id,
            StatusId = 1
        };

        var request = new UpdateFilingCustomPeriodRequest
        {
            FilingPeriodId = null,
            StartDate = startDate,
            EndDate = endDate
        };

        _filingRepository.FindById(filingId).Returns(filing);
        _filingRepository.GetAllByFilerId(filerId).Returns(new List<Filing>());
        var lobbyistRegistration = new Lobbyist()
        {
            Name = "First Last",
            StatusId = RegistrationStatus.Accepted.Id,
            SubmittedAt = _dateNow
        };
        _ = _registrationRepository.FindLobbyingRegistrationByFilerId<Registration>(filerId).Returns(lobbyistRegistration);

        // Act & Assert
        var exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await _filingSvc.UpdateFilingCustomPeriod(Arg.Any<long>(), request));
    }



    #endregion

    #region GetRelatedFilersByFilingId Tests

    [Test]
    public async Task GetRelatedFilersByFilingId_ValidFilingId_ReturnsRelatedFilers()
    {
        // Arrange
        var filingId = 123L;

        var mockRelatedFilers = new List<FilingRelatedFiler>
        {
            new() {
                FilingId = filingId,
                FilerId = 1,
                Filer = new Filer
                {
                    FilerType = FilerType.MajorDonor,
                    CurrentRegistration = new CandidateIntentionStatement
                    {
                        Name = "John Doe",
                        StatusId = RegistrationStatus.Accepted.Id
                    }
                }
            },
            new() {
                FilingId = filingId,
                FilerId = 2,
                Filer = new Filer
                {
                    FilerType = FilerType.MajorDonor,
                    CurrentRegistration = new CandidateIntentionStatement
                    {
                        Name = "John Doe",
                        StatusId = RegistrationStatus.Accepted.Id
                    }
                }
            },
            new() {
                FilingId = filingId,
                FilerId = 3,
                 Filer = new Filer
                {
                    FilerType = FilerType.MajorDonor,
                    CurrentRegistration = new CandidateIntentionStatement
                    {
                        Name = "John Doe",
                        StatusId = RegistrationStatus.Accepted.Id
                    }
                }
            }
        };

        _filingRelatedFilerRepository.GetRelatedFilersByFilingId(filingId)
            .Returns(mockRelatedFilers);

        // Act
        var result = await _filingSvc.GetRelatedFilersByFilingId(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(3));
        var resultList = result.ToList();
        Assert.Multiple(() =>
        {
            Assert.That(resultList[0].Id, Is.EqualTo(1));
            Assert.That(resultList[1].Id, Is.EqualTo(2));
            Assert.That(resultList[2].Id, Is.EqualTo(3));
        });

        await _filingRelatedFilerRepository.Received(1).GetRelatedFilersByFilingId(filingId);
    }

    [Test]
    public async Task GetRelatedFilersByFilingId_NoRelatedFilers_ReturnsEmptyResult()
    {
        // Arrange
        var filingId = 123L;

        var emptyRelatedFilers = new List<FilingRelatedFiler>();
        _filingRelatedFilerRepository.GetRelatedFilersByFilingId(filingId)
            .Returns(emptyRelatedFilers);

        // Act
        var result = await _filingSvc.GetRelatedFilersByFilingId(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(0));

        await _filingRelatedFilerRepository.Received(1).GetRelatedFilersByFilingId(filingId);
        await _filerRepository.DidNotReceive().FindById(Arg.Any<long>());
    }

    #endregion
    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_ReturnsValidReport_WhenWorkflowIsSuccessful()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var responsibleOfficerIds = new List<long> { 1, 2 };

        var report = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = 0
        };

        var workflowResult = new DecisionsSubmitLobbyistEmployerReportResponse([])
        {
            Result = [],
            Notifications = new List<NotificationTrigger>
            {
                new (true, 1, _dateNow)
            }
        };

        _filingRepository.FindById(filingId).Returns(report);

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
            DecisionsWorkflow.SendForAttestationLobbyistEmployerReportRuleset,
            Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
            true
        ).Returns(Task.FromResult(workflowResult));

        // Act
        var result = await _filingSvc.SendForAttestationReportLobbyistEmployer(filingId, responsibleOfficerIds);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        // Verify update was called
        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r =>
            r.Id == filingId &&
            r.StatusId == FilingStatus.Pending.Id &&
            r.SubmittedDate != null));
    }

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_WithAmendmentVersion_UsesAmendmentWorkflow_AndReturnsValidReport()
    {
        // Arrange
        var filingId = 123L;
        var filerId = 456L;
        var responsibleOfficerIds = new List<long> { 10, 20 };

        var report = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = 0,
            Version = 1 // version > 0 triggers amendment workflow
        };

        var workflowResult = new DecisionsSubmitLobbyistEmployerReportResponse([])
        {
            Result = [],
            Notifications = new List<NotificationTrigger>
            {
                new(true, 99, DateTime.UtcNow)
            }
        };

        _filingRepository.FindById(filingId).Returns(report);
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(new ActionsLobbiedByEntityResponse());

        _decisionsSvc.InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
            DecisionsWorkflow.FD02LobbyistEmployerAmendmentReportSendforAttestation,
            Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
            true
        ).Returns(Task.FromResult(workflowResult));

        // Act
        var result = await _filingSvc.SendForAttestationReportLobbyistEmployer(filingId, responsibleOfficerIds);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
        });

        // Verify update was called
        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r =>
            r.Id == filingId &&
            r.StatusId == FilingStatus.Pending.Id &&
            r.SubmittedDate != null));
    }

    [Test]
    public async Task SendForAttestationReportLobbyistEmployer_ReturnsNull_WhenFilingIsNotReportLobbyistEmployer()
    {
        // Arrange
        var filingId = 123;
        var responsibleOfficerIds = new List<long> { 1 };

        var otherFiling = new Filing
        {
            FilerId = 1,
            StartDate = _dateNow,
            StatusId = FilingStatus.Pending.Id,
            EndDate = _dateNow.AddDays(1),
            FilingTypeId = FilingType.AdHocFiling.Id,
        }; // Not a ReportLobbyistEmployer

        _filingRepository.FindById(filingId).Returns(otherFiling);

        // Act
        var result = await _filingSvc.SendForAttestationReportLobbyistEmployer(filingId, responsibleOfficerIds);

        // Assert
        Assert.That(result, Is.Null);
    }

    [Test]
    public async Task GetAllFilingPeriodsForLobbying_WithExistingPeriods_ReturnsMappedDtosOrdered()
    {
        var filerId = 1L;
        var filingId = 2L;
        var session = new LegislativeSession
        {
            Id = 10,
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, DateTimeKind.Utc),
            Name = "Quarterly"
        };
        var periods = new List<FilingPeriod>
        {
            new()
            {
                Id = 1,
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 3, 31, 0, 0, 0, DateTimeKind.Utc)
            },
            new()
            {
                Id = 2,
                StartDate = new DateTime(2024, 4, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 6, 30, 0, 0, 0, DateTimeKind.Utc)
            }
        };
        var filings = new List<Filing>
        {
            new()
            {
                Id = 3,
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 3, 31, 0, 0, 0, DateTimeKind.Utc),
                StatusId = FilingStatus.Accepted.Id
            }
        };

        var lobbyist = new Lobbyist()
        {
            Id = 1,
            FilerId = 1,
            Name = "Name",
            Email = "Email",
            StatusId = RegistrationStatus.Accepted.Id,
            SelfRegister = false
        };

        _ = _dependencies.FilingRepository.GetAllByFilerId(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<Filing>>(filings));
        _ = _dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(Arg.Any<long>())
            .Returns(lobbyist);
        _ = _dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(Arg.Any<long>()).Returns(nameof(Lobbyist));
        _ = _dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<Lobbyist>(Arg.Any<long>()).Returns(
            Task.FromResult(new List<Lobbyist?>
            {
                lobbyist
            }));
        _dependencies.FilingPeriodRepository.GetFilingPeriodsByLegislativeSessionIds(Arg.Any<List<long>>())
            .Returns(Task.FromResult(periods));

        var result = await _filingSvc.GetAllFilingPeriodsForLobbying(filerId, null, filingId);

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result.First().Id, Is.EqualTo(1));
            Assert.That(result.Last().Id, Is.EqualTo(2));
            Assert.That(result.All(p => p.HasFiling == (p.Id == 1)));
        });
    }

    [Test]
    public async Task GetAllFilingPeriodsForLobbying_NoLegislativeSessions_ReturnsEmpty()
    {
        var filerId = 1L;
        _dependencies.FilingRepository.GetAllByFilerId(filerId)
            .Returns(Task.FromResult<IEnumerable<Filing>>(new List<Filing>()));
        _dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(filerId)
            .Returns((Registration?)null);
        _dependencies.FilingPeriodRepository.GetFilingPeriodsByLegislativeSessionIds(Arg.Any<List<long>>())
            .Returns(Task.FromResult(new List<FilingPeriod>()));

        var result = await _filingSvc.GetAllFilingPeriodsForLobbying(filerId);

        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetAllFilingPeriodsForLobbying_NoPeriods_GeneratesPeriods()
    {
        var filerId = 1L;
        var session = new LegislativeSession
        {
            Id = 10,
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, DateTimeKind.Utc),
            Name = "Quarterly"
        };
        var filings = new List<Filing>();
        var lobbyistEmployer = new LobbyistEmployer
        {
            Id = 1,
            Name = "Test Lobbyist Employer",
            Email = "<EMAIL>",
            StatusId = RegistrationStatus.Accepted.Id,
            FilerId = filerId,
            Version = 1,
            EmployerName = "Test Employer",
            EmployerType = "Type",
            BusinessActivity = "Activity",
            BusinessDescription = "Description",
            InterestType = "Interest",
            IndustryDescription = "Industry Description",
            IndustryPortion = "Portion",
            NumberOfMembers = 10,
            StateLegislatureLobbying = true,
            DateQualified = DateTime.Now,
            LegislativeSessionId = 1,
            LegislativeSession = session,
            AddressList = new AddressList(),
            PhoneNumberList = new PhoneNumberList(),
        };

        _dependencies.FilingRepository.GetAllByFilerId(filerId).Returns(Task.FromResult<IEnumerable<Filing>>(filings));
        _dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(Arg.Any<long>())
            .Returns(lobbyistEmployer);
        _dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(Arg.Any<long>()).Returns(nameof(LobbyistEmployer));
        _dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<LobbyistEmployer>(Arg.Any<long>()).Returns(
            Task.FromResult(new List<LobbyistEmployer?>
            {
                lobbyistEmployer
            }));
        _dependencies.FilingPeriodRepository.GetFilingPeriodsByLegislativeSessionIds(Arg.Any<List<long>>())
            .Returns(Task.FromResult(new List<FilingPeriod>()));
        _dependencies.FilingPeriodRepository.Create(Arg.Any<FilingPeriod>())
            .Returns(Task.FromResult(new FilingPeriod { Id = 99 }));

        var result = await _filingSvc.GetAllFilingPeriodsForLobbying(filerId);

        Assert.That(result, Is.Not.Empty);
    }

    [Test]
    public async Task GetAllFilingPeriodsForLobbying_WithTerminatedOrWithdrawnDate_SetsIsRemovedCorrectly()
    {
        var session = new LegislativeSession
        {
            Id = 10,
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            EndDate = new DateTime(2024, 12, 31, 0, 0, 0, DateTimeKind.Utc),
            Name = "Quarterly"
        };
        var periods = new List<FilingPeriod>
        {
            new()
            {
                Id = 1,
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2024, 3, 31, 0, 0, 0, DateTimeKind.Utc)
            },
            new()
            {
                Id = 2,
                StartDate = new DateTime(2026, 4, 1, 0, 0, 0, DateTimeKind.Utc),
                EndDate = new DateTime(2026, 6, 30, 0, 0, 0, DateTimeKind.Utc)
            }
        };
        var filings = new List<Filing>();
        var lobbyist = new Lobbyist()
        {
            Id = 1,
            FilerId = 1,
            Name = "Name",
            Email = "Email",
            StatusId = RegistrationStatus.Accepted.Id,
            SelfRegister = false,
            WithdrawnAt = new DateTime(2026, 4, 1, 0, 0, 0, DateTimeKind.Utc)
        };

        _dependencies.FilingRepository.GetAllByFilerId(Arg.Any<long>()).Returns(Task.FromResult<IEnumerable<Filing>>(filings));
        _dependencies.RegistrationRepository.FindLobbyingRegistrationByFilerId<Registration>(Arg.Any<long>())
            .Returns(lobbyist);
        _dependencies.RegistrationRepository.GetRegistrationDiscriminatorById(Arg.Any<long>()).Returns(nameof(Lobbyist));
        _dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<Lobbyist>(Arg.Any<long>()).Returns(
            Task.FromResult(
                new List<Lobbyist?> { lobbyist }
            )
        );

        _dependencies.FilingPeriodRepository.GetFilingPeriodsByLegislativeSessionIds(Arg.Any<List<long>>())
            .Returns(Task.FromResult(periods));

        var withdrawnDate = new DateTime(2024, 2, 1, 0, 0, 0, DateTimeKind.Utc);
        _dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<LobbyistTermination>(Arg.Any<long>())
            .Returns(Task.FromResult(new List<LobbyistTermination?>()));
        _dependencies.RegistrationRepository.FindListOfRegistrationsByFilerId<LobbyistWithdrawal>(Arg.Any<long>())
            .Returns(
                Task.FromResult(new List<LobbyistWithdrawal?>
                {
                    new()
                    {
                        StatusId = FilingStatus.Accepted.Id,
                        WithdrawnAt = withdrawnDate,
                        Name = "test"
                    }
                }));

        var result = await _filingSvc.GetAllFilingPeriodsForLobbying(Arg.Any<long>());

        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Any(p => p.IsRemoved), Is.True);
        });
    }

    [Test]
    public async Task UpdateFilingCustomPeriod_WithValidFilingPeriodId_UpdatesFilingWithPeriodDates()
    {
        var filing = new Filing
        {
            Id = 1,
            FilerId = 2,
            FilingTypeId = 3,
            StatusId = 0
        };
        var filingPeriod = new FilingPeriodDto
        {
            Id = 1,
            StartDate = new DateTime(2024, 1, 1),
            EndDate = new DateTime(2024, 12, 31)
        };
        var request = new UpdateFilingCustomPeriodRequest { FilingPeriodId = filingPeriod.Id };

        _dependencies.FilingRepository.GetFilingById(filing.Id).Returns(Task.FromResult<Filing?>(filing));
        _dependencies.FilingRepository.GetAllByFilerId(filing.FilerId)
            .Returns(Task.FromResult<IEnumerable<Filing>>(new List<Filing>()));

        var mockFilings = new List<Filing> { new Filing
            {
                Id = 1,
                FilerId = 1,
                StatusId = 1
            }
        };
        var mockPeriods = new List<FilingPeriod> { new FilingPeriod { Id = 1, StartDate = DateTime.UtcNow, EndDate = DateTime.UtcNow.AddDays(1) } };

        _dependencies.FilingRepository.GetAllByFilerId(1)
            .Returns(Task.FromResult<IEnumerable<Filing>>(mockFilings));

        _dependencies.FilingPeriodRepository.GetAll()
            .Returns(Task.FromResult<IEnumerable<FilingPeriod>>(mockPeriods));

        var result = await _filingSvc.UpdateFilingCustomPeriod(filing.Id, request);

        Assert.That(result, Is.Not.Null);

    }

    [Test]
    public void UpdateFilingCustomPeriod_WithEndDateBeforeStartDate_ThrowsArgumentException()
    {
        var filing = new Filing
        {
            Id = 1,
            FilerId = 2,
            FilingTypeId = 3,
            StatusId = 1
        };
        var request = new UpdateFilingCustomPeriodRequest
        {
            StartDate = new DateTime(2024, 5, 1),
            EndDate = new DateTime(2024, 4, 1)
        };

        _dependencies.FilingRepository.GetFilingById(filing.Id).Returns(Task.FromResult<Filing?>(filing));
        _dependencies.FilingRepository.GetAllByFilerId(filing.FilerId)
            .Returns(Task.FromResult<IEnumerable<Filing>>(new List<Filing>()));

        var ex = Assert.ThrowsAsync<ArgumentException>(async () =>
            await _filingSvc.UpdateFilingCustomPeriod(filing.Id, request));

        Assert.That(ex!.Message, Does.Contain("End date must be after start date."));
    }

    [Test]
    public void UpdateFilingCustomPeriod_WithOverlappingFiling_ThrowsArgumentException()
    {
        var filing = new Filing
        {
            Id = 1,
            FilerId = 2,
            FilingTypeId = 3,
            StatusId = 1
        };
        var overlappingFiling = new Filing
        {
            Id = 99,
            FilingTypeId = 3,
            StartDate = new DateTime(2024,
                1,
                1),
            EndDate = new DateTime(2024,
                6,
                1),
            StatusId = 1
        };
        var request = new UpdateFilingCustomPeriodRequest
        {
            StartDate = new DateTime(2024, 1, 15),
            EndDate = new DateTime(2024, 5, 15)
        };

        _dependencies.FilingRepository.GetFilingById(filing.Id).Returns(Task.FromResult<Filing?>(filing));
        _dependencies.FilingRepository.GetAllByFilerId(filing.FilerId)
            .Returns(Task.FromResult<IEnumerable<Filing>>(new List<Filing> { overlappingFiling }));


        var ex = Assert.ThrowsAsync<ArgumentException>(async () =>
            await _filingSvc.UpdateFilingCustomPeriod(filing.Id, request));

        Assert.That(ex!.Message, Does.Contain("overlaps with Filing: 99"));
    }

    #region UpdateFilingContactSummaryIfApplicableAsync
    [Test]
    public async Task UpdateFilingContactSummaryIfApplicableAsync_NotFoundContactSummary_ShouldCreateNewRecord()
    {
        // Arrange
        var filingId = 1L;
        var transaction = new PersonReceiving1000OrMore
        {
            Id = 1,
            Amount = (Currency)1m,
            ContactId = 1L,
        };
        var amount = 1m;
        var filing = new SmoCampaignStatement
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id,
            StartDate = _dateNow,
            FilerId = 1L,
        };
        var previousStatements = new List<SmoCampaignStatement>
        {
            new()
            {
                Id = 2L,
                StatusId = FilingStatus.Accepted.Id,
            }
        };
        var cumulativeAmount = 10m;

        _filingContactSummaryRepositoryMock.FindByFilingIdAndContactId(Arg.Any<long>(), Arg.Any<long>()).ReturnsNull();
        _filingRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<Filing?>(filing));
        _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>()).Returns(Task.FromResult(previousStatements));
        _transactionRepository.SumTransactionAmountsByFilingsAndContact(Arg.Any<List<long>>(), Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(cumulativeAmount));
        var methodInfo = typeof(FilingSvc).GetMethod("UpdateFilingContactSummaryIfApplicableAsync", BindingFlags.Instance | BindingFlags.NonPublic)!;

        // Act
        var task = methodInfo.Invoke(_filingSvc, [filingId, transaction, amount, null])! as Task;
        await task!;

        // Assert
        _ = _filingContactSummaryRepositoryMock.Received(1).Create(Arg.Any<FilingContactSummary>());
    }

    [Test]
    public async Task UpdateFilingContactSummaryIfApplicableAsync_FoundContactSummary_ShouldUpdateRecord()
    {
        // Arrange
        var filingId = 1L;
        var transaction = new PersonReceiving1000OrMore
        {
            Id = 1,
            Amount = (Currency)1m,
            ContactId = 1L,
        };
        var amount = 1m;
        var filing = new SmoCampaignStatement
        {
            Id = filingId,
            StatusId = FilingStatus.Draft.Id,
            StartDate = _dateNow,
            FilerId = 1L,
        };
        var previousStatements = new List<SmoCampaignStatement>
        {
            new()
            {
                Id = 2L,
                StatusId = FilingStatus.Accepted.Id,
            }
        };
        var cumulativeAmount = 10m;
        var filingContactSummary = new FilingContactSummary
        {
            Id = 1,
            Amount = 2m,
            DisclosureFilingId = filingId,
        };
        var originalContactId = 2L;

        _filingContactSummaryRepositoryMock.FindByFilingIdAndContactId(Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult<FilingContactSummary?>(filingContactSummary));
        _filingRepository.FindById(Arg.Any<long>()).Returns(Task.FromResult<Filing?>(filing));
        _filingRepository.FindPreviousSmoCampaignStatementsInCalendarYear(Arg.Any<long>(), Arg.Any<DateTime>()).Returns(Task.FromResult(previousStatements));
        _transactionRepository.SumTransactionAmountsByFilingsAndContact(Arg.Any<List<long>>(), Arg.Any<long>(), Arg.Any<long>()).Returns(Task.FromResult(cumulativeAmount));
        var methodInfo = typeof(FilingSvc).GetMethod("UpdateFilingContactSummaryIfApplicableAsync", BindingFlags.Instance | BindingFlags.NonPublic)!;

        // Act
        var task = methodInfo.Invoke(_filingSvc, [filingId, transaction, amount, originalContactId])! as Task;
        await task!;

        // Assert
        _ = _filingContactSummaryRepositoryMock.Received().Update(Arg.Any<FilingContactSummary>());
    }
    #endregion

    #region UpdateAmendmentExplanation
    [Test]
    public void UpdateAmendmentExplanation_FilingNotFound_ThrowsKeyNotFoundException()
    {
        _dependencies.FilingRepository.FindById(Arg.Any<long>())
            .Returns(Task.FromResult<Filing?>(null));

        var request = new UpdateAmendmentExplanationRequest();

        var ex = Assert.ThrowsAsync<KeyNotFoundException>(() =>
            _filingSvc.UpdateAmendmentExplanation(1, request));

        Assert.That(ex.Message, Is.EqualTo("Filing not found. Id=1"));
    }

    [Test]
    public void UpdateAmendmentExplanation_FilingNotAmendment_ThrowsInvalidOperationException()
    {
        var filing = new Filing { Id = 1, Version = 0, StatusId = 1 };
        _dependencies.FilingRepository.FindById(filing.Id)
            .Returns(Task.FromResult<Filing?>(filing));

        var request = new UpdateAmendmentExplanationRequest();

        var ex = Assert.ThrowsAsync<InvalidOperationException>(() =>
            _filingSvc.UpdateAmendmentExplanation(filing.Id, request));

        Assert.That(ex.Message, Is.EqualTo("Only filing amendments have amendment explanations"));
    }

    [Test]
    public async Task UpdateAmendmentExplanation_NoWorkflow_UpdatesAndReturnsValid()
    {
        var filing = new Filing { Id = 1, Version = 1, StatusId = 1 }; // not a Report48H subclass
        _dependencies.FilingRepository.FindById(filing.Id)
            .Returns(Task.FromResult<Filing?>(filing));

        var request = new UpdateAmendmentExplanationRequest { AmendmentExplanation = "Update text" };

        var result = await _filingSvc.UpdateAmendmentExplanation(filing.Id, request);

        await _dependencies.FilingRepository.Received(1).Update(filing);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.FilingId, Is.EqualTo(filing.Id));
        });
    }

    [Test]
    public async Task UpdateAmendmentExplanation_WithWorkflow_ValidRequest_UpdatesAndReturnsValid()
    {
        var filing = new Report48H { Id = 1, Version = 1, StatusId = 1 }; // matches workflow case
        _dependencies.FilingRepository.FindById(filing.Id)
            .Returns(Task.FromResult<Filing?>(filing));

        var request = new UpdateAmendmentExplanationRequest { AmendmentExplanation = "Update text" };

        _servicesDependencies.DecisionsSvc.InitiateWorkflow<UpdateAmendmentExplanationRequest, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBLobbyistEmployer5000FirmAmendReport02AmendmentExplanation,
            request,
            true
        ).Returns(Task.FromResult(new List<WorkFlowError>()));

        var result = await _filingSvc.UpdateAmendmentExplanation(filing.Id, request);

        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(filing.AmendmentExplanation, Is.EqualTo("Update text"));
        });
        await _dependencies.FilingRepository.Received(1).Update(filing);
    }

    [Test]
    public async Task UpdateAmendmentExplanation_WithWorkflow_InvalidRequest_ReturnsErrorsAndDoesNotUpdate()
    {
        var filing = new Report48H { Id = 1, Version = 1, StatusId = 1 };
        _dependencies.FilingRepository.FindById(filing.Id)
            .Returns(Task.FromResult<Filing?>(filing));

        var request = new UpdateAmendmentExplanationRequest { AmendmentExplanation = "Bad input" };
        var errors = new List<WorkFlowError> { new("test", "test", "test", "test") };

        _servicesDependencies.DecisionsSvc.InitiateWorkflow<UpdateAmendmentExplanationRequest, List<WorkFlowError>>(
            DecisionsWorkflow.FDLOBLobbyistEmployer5000FirmAmendReport02AmendmentExplanation,
            request,
            true
        ).Returns(Task.FromResult(errors));

        var result = await _filingSvc.UpdateAmendmentExplanation(filing.Id, request);

        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.False);
            Assert.That(result.ValidationErrors, Is.EqualTo(errors));
        });
        await _dependencies.FilingRepository.DidNotReceive().Update(filing);
    }

    [Test]
    public async Task UpdateAmendmentExplanation_WithLobbyistEmployerReport_ShouldUseCorrectWorkflow()
    {
        // Arrange
        var filingId = 123L;
        var amendmentExplanation = "This is an amendment explanation for lobbyist employer report";
        var request = new UpdateAmendmentExplanationRequest { AmendmentExplanation = amendmentExplanation };

        var filing = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = 456L,
            OriginalId = 100L,
            FilingTypeId = FilingType.LobbyistEmployerReport.Id,
            StatusId = FilingStatus.Draft.Id,
            Version = 1
        };

        _filingRepository.FindById(filingId).Returns(filing);
        _filingRepository.Update(Arg.Any<Filing>()).Returns(filing);

        _servicesDependencies.DecisionsSvc
            .InitiateWorkflow<UpdateAmendmentExplanationRequest, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FDLOBLobbyistEmployerAmendReportAmendmentExplanation,
                request,
                true)
            .Returns(new StandardDecisionsSubmissionResponse
            {
                Errors = new List<WorkFlowError>()
            });

        // Act
        var result = await _filingSvc.UpdateAmendmentExplanation(filingId, request);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Valid, Is.True);
            Assert.That(filing.AmendmentExplanation, Is.EqualTo(amendmentExplanation));
        });

        await _servicesDependencies.DecisionsSvc
            .Received(1)
            .InitiateWorkflow<UpdateAmendmentExplanationRequest, StandardDecisionsSubmissionResponse>(
                DecisionsWorkflow.FDLOBLobbyistEmployerAmendReportAmendmentExplanation,
                Arg.Any<UpdateAmendmentExplanationRequest>(),
                true);
    }

    [Test]
    public async Task SubmitLobbyistEmployerReport_ShouldUseAmendmentWorkflow_WhenFilingVersionGreaterThanZero()
    {
        // Arrange
        var filingId = 1234L;
        var filerId = 5678L;

        var report = new LobbyistEmployerReport
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id,
            Version = 1
        };

        var decisionResponse = new DecisionsSubmitLobbyistEmployerReportResponse
        {
            Errors = []
        };

        _filingRepository.FindById(filingId).Returns(report);

        var actionsLobbiedResponse = new ActionsLobbiedByEntityResponse
        {
            AgencyActions = new List<ActionsLobbiedResponseDto>(),
            AssemblyBillActions = new List<ActionsLobbiedResponseDto>(),
            SenateBillActions = new List<ActionsLobbiedResponseDto>()
        };
        _actionsLobbiedSvc.GetActionsLobbiedByFilingId(filingId).Returns(actionsLobbiedResponse);

        _decisionsSvc
            .InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
                DecisionsWorkflow.FD02LobbyistEmployerAmendmentReportAttestation,
                Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
                true)
            .Returns(decisionResponse);

        // Act
        var result = await _filingSvc.SubmitLobbyistEmployerReport(filingId, true);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Valid, Is.True);
            Assert.That(result.ValidationErrors, Is.Empty);
            Assert.That(report.StatusId, Is.EqualTo(FilingStatus.Accepted.Id));
            Assert.That(report.DiligenceStatementVerified, Is.True);
        });

        await _decisionsSvc.Received(1).InitiateWorkflow<DecisionsSubmitLobbyistEmployerReport, DecisionsSubmitLobbyistEmployerReportResponse>(
            DecisionsWorkflow.FD02LobbyistEmployerAmendmentReportAttestation,
            Arg.Any<DecisionsSubmitLobbyistEmployerReport>(),
            true);

        await _filingRepository.Received(1).Update(Arg.Is<LobbyistEmployerReport>(r => r.StatusId == FilingStatus.Accepted.Id));
    }

    #endregion
}
