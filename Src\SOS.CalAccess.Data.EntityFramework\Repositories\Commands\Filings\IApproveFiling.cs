// <copyright file="IApproveFiling.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Messaging;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Filings;

/// <summary>
/// Command interface for approving a filing.
/// </summary>
public interface IApproveFiling
    : ICommand<IApproveFiling.WithId, IResult<Filing>>, ICommand<Filing, IResult<Filing>>
{
    /// <summary>
    /// Contains the id of the filing to approve.
    /// </summary>
    sealed record WithId(long Id);
}

/// <summary>
/// Command implementation for approving a filing.
/// </summary>
/// <param name="db">Database context.</param>
/// <param name="messaging">External communications handling.</param>
public sealed class ApproveFiling(
    DatabaseContext db,
    IMessagingHub messaging,
    IDateTimeSvc dateTimeSvc) : IApproveFiling
{
    private const string NotificationContent = "A Filing with id {0} has been approved.";

    /// <inheritdoc />
    public async ValueTask<IResult<Filing>> Execute(
        IApproveFiling.WithId input,
        CancellationToken cancellationToken = default)
    {
        var filing = await db.Filings
            .FirstOrDefaultAsync(f => f.Id == input.Id, cancellationToken);

        if (filing is null)
        {
            return new Failure<Filing>.NotFound("No filing was found with the specified id.");
        }

        return await Execute(filing, cancellationToken);
    }

    /// <inheritdoc />
    public async ValueTask<IResult<Filing>> Execute(
        Filing filing,
        CancellationToken cancellationToken = default)
    {
        if (filing.StatusId != FilingStatus.Pending.Id)
        {
            return new Failure<Filing>.InvalidState(
                (string)FilingStatus.Pending,
                filing.StatusId,
                "Filing must be in a pending state to be approved.");
        }

        filing.StatusId = FilingStatus.Accepted.Id;
        filing.ApprovedAt = dateTimeSvc.GetCurrentDateTime();

        await db.SaveChangesAsync(cancellationToken);

        var content = string.Format(NotificationContent, filing.ParentId);
        await messaging.Execute(content, cancellationToken);

        return new Success<Filing>(filing);
    }
}
