// <copyright file="IAuditService.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using SOS.CalAccess.Data.EntityFramework.Audit.Attribution;
using SOS.CalAccess.Data.EntityFramework.Audit.Sinks;
using SOS.CalAccess.Foundation.Utils;

namespace SOS.CalAccess.Data.EntityFramework.Audit;

/// <summary>
/// Interface for a service that allows direct access to auditing actions
/// directly, without going through the low-level data changes intercepting implementation
/// and possibly sending logs to a dedicated <see cref="IAuditLogSink"/> instance with a bespoke implementation.
/// </summary>
public interface IAuditService
{
    /// <summary>
    /// Asynchronously audit and record an action.
    /// </summary>
    /// <param name="action">The action to audit.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>A <see cref="ValueTask"/> representing the completion of the operation.</returns>
    public ValueTask LogAction(IAuditableAction action, CancellationToken cancellationToken = default);
}

/// <summary>
/// Default implementation for <see cref="IAuditService"/>.
/// </summary>
/// <param name="attributionProvider">The source for attributing action authorship.</param>
/// <param name="logSink">The destination to route any created logs to.</param>
public sealed class AuditService(
    IActionAttributionProvider attributionProvider,
    IDateTimeSvc dateTimeSvc,
    [FromKeyedServices(AuditService.DedicatedSink)] IAuditLogSink logSink) : IAuditService
{
    /// <summary>
    /// Key to lookup a dedicated sink to use for high-level logs.
    /// </summary>
    internal const string DedicatedSink = nameof(DedicatedSink);

    /// <inheritdoc/>
    public async ValueTask LogAction(
        IAuditableAction action, CancellationToken cancellationToken = default)
    {
        await logSink.ConsumeLogs([new(action, dateTimeSvc, attributedTo: attributionProvider.Performer)], cancellationToken);
    }
}
