// <copyright file="ICreateTransaction.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>
using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;

/// <summary>
/// Command processor interface for creating a new transaction.
/// </summary>
public interface ICreateTransaction : ICommand<ICreateTransactionCommand, IResult<Transaction>>;

/// <summary>
/// Command processor implementation for creating a new transaction.
/// </summary>
/// <param name="db">Database context.</param>
/// <param name="decisions">Business logic engine.</param>
/// <param name="auditService">High level audit facilities.</param>
public sealed class CreateTransaction(
    DatabaseContext db,
    IDecisionsService decisions,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc) : ICreateTransaction
{
    /// <inheritdoc />
    public async ValueTask<IResult<Transaction>> Execute(
        ICreateTransactionCommand input, CancellationToken cancellationToken = default)
    {
        var contact = await db.FilerContacts
            .Where(c => c.Id == input.Data.ContactId && c.FilerId == input.Data.FilerId)
            .FirstOrDefaultAsync(cancellationToken);

        if (contact is not { } validContact)
        {
            return new Failure<Transaction>.DependencyFailed("Required contact reference was not found");
        }

        var transaction = input.Build(validContact);

        _ = await decisions.Execute(new("Transaction.Create", Context: transaction), cancellationToken);

        db.Transactions.Add(transaction);

        await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Create", transaction.GetType().Name, transaction.Id.ToString(), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<Transaction>(transaction);
    }
}
