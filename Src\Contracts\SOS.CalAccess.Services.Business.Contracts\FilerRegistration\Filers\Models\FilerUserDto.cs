using SOS.CalAccess.Models.Authorization;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Models.UserAccountMaintenance;

namespace SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;

/// <summary>
/// Filer User DTO.
/// </summary>
public class FilerUserDto
{
    /// <summary>
    /// Gets or sets ID of Filer User
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Gets or sets ID of Filer
    /// </summary>
    public long FilerId { get; set; }

    /// <summary>
    /// Gets or sets ID of User
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// Gets or sets User object
    /// </summary>
    public User? User { get; set; }

    /// <summary>
    /// Gets or sets ID of Filer Role
    /// </summary>
    public long FilerRoleId { get; set; }

    /// <summary>
    /// Gets or sets Filer Role object
    /// </summary>
    public FilerRole? FilerRole { get; set; }

    /// <summary>
    /// Gets or sets Name of Filer
    /// </summary>
    public string? FilerName { get; set; }

    public static FilerUserDto MapToDto(FilerUser filerUser)
    {
        return new FilerUserDto
        {
            Id = filerUser.Id,
            FilerId = filerUser.FilerId,
            FilerRoleId = filerUser.FilerRoleId,
            UserId = filerUser.UserId,
            FilerRole = filerUser.FilerRole,
            User = filerUser.User is null ? null : new User
            {
                Id = filerUser.User.Id,
                EmailAddress = filerUser.User.EmailAddress,
                FirstName = filerUser.User.FirstName,
                LastName = filerUser.User.LastName,
                UserName = filerUser.User.UserName,
                EntraOid = filerUser.User.EntraOid ?? "",
            },
            FilerName = filerUser.Filer?.CurrentRegistration?.Name ?? ""
        };
    }
}

