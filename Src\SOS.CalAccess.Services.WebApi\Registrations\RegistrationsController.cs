// <copyright file="RegistrationsController.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using System.Globalization;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Registrations;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations;
using SOS.CalAccess.Services.WebApi.Authorization.ThirdParty.Decisions;
using SOS.CalAccess.Services.WebApi.Shared;



namespace SOS.CalAccess.Services.WebApi.Registrations;

/// <summary>
/// API controller responsible for routing requests related to registrations.
/// </summary>
[Route("api/[controller]")]
[ApiController]
[ApiConventionType(typeof(DefaultApiConventions))]
[AllowAnonymous]
public sealed class RegistrationsController(
    IAuthorizationService authorization,
    ICandidateIntentionRegistrationSvc candidateIntentionRegistrationSvc,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc) : AuthorizationAwareControllerBase(authorization)
{

    /// <summary>
    /// Cancels a currently draft registration.
    /// </summary>
    /// <param name="id">The target registration id.</param>
    /// <param name="registrationSvc">Registrations service.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpPost("{id:long}/cancel", Name = nameof(CancelRegistration))]
    [ProducesDefaultResponseType]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task CancelRegistration(
        [FromRoute] long id,
        [FromServices] IRegistrationSvc registrationSvc,
        CancellationToken cancellationToken = default)
    {
        var result = await registrationSvc.CancelRegistration(id);
        return;
    }

    /// <summary>
    /// Gets a list of all committees.
    /// </summary>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("Committees/GetAll")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<IReadOnlyList<RegistrationItemResponse>>> GetAllCommittees(
        [FromServices] IRegistrationSvc registrationSvc,
        CancellationToken cancellationToken = default)
    {
        var committees = await registrationSvc.GetAllCommitteesRegistrations();
        var response = committees.Select(c => new RegistrationItemResponse(c)).ToList() as IReadOnlyList<RegistrationItemResponse>;
        return Ok(response);
    }

    /// <summary>
    /// Gets a list of all committees.
    /// </summary>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("Lobbying/GetLobbyistEmployerEntityRegistration")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<RegistrationItemResponse>> GetLobbyistEmployerEntityRegistration(
        [FromServices] IRegistrationSvc registrationSvc,
        CancellationToken cancellationToken = default)
    {
        var id = User.FindFirstValue(ClaimTypes.NameIdentifier);
        var lobbyistEmployerRegistration = await registrationSvc.GetLobbyistEmployerEntityRegistration(long.Parse(id!, CultureInfo.InvariantCulture));
        var response = lobbyistEmployerRegistration is not null ? new RegistrationItemResponse(lobbyistEmployerRegistration) : null;
        return response is not null ? response : NotFound();
    }

    /// <summary>
    /// Gets a list of all lobbyist registrations.
    /// </summary>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("Lobbying/GetLobbyistEntityRegistration")]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    [AllowAnonymous]
    public async Task<ActionResult<RegistrationItemResponse>> GetLobbyistEntityRegistration(
        [FromServices] IRegistrationSvc registrationSvc,
        CancellationToken cancellationToken = default)
    {
        var id = User.FindFirstValue(ClaimTypes.NameIdentifier);
        var lobbyistRegistration = await registrationSvc.GetLobbyistEntityRegistration(long.Parse(id!, CultureInfo.InvariantCulture));
        var response = lobbyistRegistration is not null ? new RegistrationItemResponse(lobbyistRegistration) : null;
        return response is not null ? response : NotFound();
    }

    /// <summary>
    /// Retrieves a list of all current Registrations.
    /// </summary>
    /// <param name="getAllRegistrations">Command handler.</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet(Name = nameof(GetRegistrations))]
    public async Task<ActionResult<RegistrationListResponse>> GetRegistrations(
        [FromServices] IGetAllRegistrations getAllRegistrations,
        CancellationToken cancellationToken = default)
    {
        await ThrowIfRequirementNotMet([RulesAllowAction.ReadAll<Registration>()]);

        await auditService.LogAction(BusinessAction.ReadAll<Registration>(dateTimeSvc), cancellationToken);

        var registrations = await getAllRegistrations.Execute(cancellationToken);

        RegistrationListResponse response =
            new(registrations.Select(u => new RegistrationItemResponse(u)).ToList());

        return Ok(response);
    }

    /// <summary>
    /// Retrieve
    /// </summary>
    /// <param name="registrationSvc">Registration svc</param>
    /// <param name="cancellationToken">Cancellation source.</param>
    /// <returns>An <see cref="ActionResult"/> with the result of the request.</returns>
    [HttpGet("self", Name = nameof(GetMyRegistrations))]
    [ApiConventionMethod(typeof(DefaultApiConventions), nameof(DefaultApiConventions.Get))]
    public async Task<ActionResult<List<RegistrationDashboardDto>>> GetMyRegistrations(
        [FromServices] IRegistrationSvc registrationSvc,
        CancellationToken cancellationToken = default)
    {
        var userToken = User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (userToken != null)
        {

            long userId = long.Parse(userToken);
            var registrations = await registrationSvc.GetMyRegistrations(userId);
            return Ok(registrations);
        }
        else
        {
            return UnprocessableEntity();
        }
    }
}
