using Refit;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerRegistration.Registrations;
using SOS.CalAccess.Services.Business.FilerDisclosure.Filings.Models;
using SOS.CalAccess.Services.Business.FilerDisclosure.Transactions.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Filers.Models;
using SOS.CalAccess.Services.Business.FilerRegistration.Registrations.Models;

namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// The FilingSvc defines the contract for the Filing Service. The service is responsible for managing filings, including creating, updating, submitting, and amending filings.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// The service assumes that the necessary repositories are available for accessing and manipulating filing data.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The business function of this service is to handle the lifecycle of filings, including creation, modification, submission, and retrieval of filing information.
/// </p>
/// <h4>Software Features</h4>
/// <ul>
/// <li>FD-01: Enter an Activity Report</li>
/// <li>FD-02: Modify an Activity Report</li>
/// <li>FD-03: Upload an Activity Report</li>
/// </ul>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this operation.
/// </p>
/// | Service                        | Operation                      | Description                  |
/// | ------------------------------ | ------------------------------ | ---------------------------- |
/// | IFilingRepository              | Create                         | Creates a new filing         |
/// | IFilingRepository              | Update                         | Updates an existing filing   |
/// | IFilingRepository              | Submit                         | Submits a filing             |
/// | IFilingRepository              | FindById                       | Retrieves a filing by ID     |
/// | IFilingRepository              | GetAllByFilerId                | Retrieves all filings for a specific filer |
/// | IFilingPeriodRepository        | GetAll                         | Retrieves all filing periods |
/// | IFilingStatusRepository        | GetAll                         | Retrieves all filing statuses|
/// | IFilingTypeRepository          | GetAll                         | Retrieves all filing types   |
/// | IDecisionsSvc                  | InitiateWorkflow               | Initiates a workflow         |
/// | IAuditSvc                      | RecordAuditEvent               | Records an audit event       |
#endregion

/// <summary>
/// Interface for the Filing Service.
/// </summary>
public interface IFilingSvc
{
    /// <summary>
    /// Creates a new filing.
    /// </summary>
    /// <param name="filing">The filing to create.</param>
    /// <returns>The ID of the created filing.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="CreateFiling()"];
    /// IFilingSvc => IFilingRepository [label="Create()"];
    /// IFilingRepository >> IFilingSvc [label="return long"];
    /// IFilingSvc >> Actor [label="return long"];
    /// \endmsc
    Task<long> CreateFiling(Filing filing);

    /// <summary>
    /// Updates an existing filing.
    /// </summary>
    /// <param name="filing">The filing to update.</param>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="UpdateFiling()"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return"];
    /// IFilingSvc >> Actor [label="return"];
    /// \endmsc
    Task UpdateFiling(Filing filing);

    /// <summary>
    /// Submits a filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing to submit.</param>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IDecisionsSvc [label="IDecision \n Svc"], IFilingRepository [label="IFiling \n Repository"], IAuditSvc;
    /// Actor => IFilingSvc [label="SubmitFiling()"];
    /// IFilingSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IFilingSvc [label="return TOutput"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return"];
    /// IFilingSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IFilingSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IFilingSvc >> Actor [label="return"];
    /// \endmsc
    Task SubmitFiling(long filingId);

    /// <summary>
    /// Initiates the amendment of a filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing to amend.</param>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="InitiateAmendFiling()"];
    /// IFilingSvc => IFilingRepository [label="Create()"];
    /// IFilingRepository >> IFilingSvc [label="return"];
    /// IFilingSvc >> Actor [label="return"];
    /// \endmsc
    Task InitiateAmendFiling(long filingId);

    /// <summary>
    /// Amends a filing.
    /// </summary>
    /// <param name="newFiling">The new filing data.</param>
    /// <param name="filingId">The ID of the filing to amend.</param>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IDecisionsSvc [label="IDecisions \n Svc"], IFilingRepository [label="IFiling \n Repository"], IAuditSvc;
    /// Actor => IFilingSvc [label="AmendFiling()"];
    /// IFilingSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IDecisionsSvc >> IFilingSvc [label="return TOutput"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return"];
    /// IFilingSvc => IAuditSvc [label="RecordAuditEvent()"];
    /// IFilingSvc => IDecisionsSvc [label="InitiateWorkflow()"];
    /// IFilingSvc >> Actor [label="return"];
    /// \endmsc
    Task AmendFiling(Filing newFiling, long filingId);


    /// <summary>
    /// Gets a filing.
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <returns>returns filing entity</returns>
    Task<Filing?> GetFilingById(long id);

    /// <summary>
    /// Cancels a filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing to cancel.</param>
    /// <returns>returns filing entity</returns>
    Task<Filing?> CancelFiling(long filingId);


    /// <summary>
    /// Updates the status of a filing.
    /// </summary>
    /// <param name="status">The new status of the filing.</param>
    /// <param name="filingId">The ID of the filing to update.</param>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="UpdateFilingStatus()"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return"];
    /// IFilingSvc >> Actor [label="return"];
    /// \endmsc
    Task UpdateFilingStatus(FilingStatus status, long filingId);

    /// <summary>
    /// Gets a filing by ID.
    /// </summary>
    /// <param name="filingId">The ID of the filing to retrieve.</param>
    /// <returns>The filing with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetFiling()"];
    /// IFilingSvc => IFilingRepository [label="FindById()"];
    /// IFilingRepository >> IFilingSvc [label="return Filing"];
    /// IFilingSvc >> Actor [label="return Filing"];
    /// \endmsc
    Task<Filing?> GetFiling(long filingId);

    /// <summary>
    /// Gets all filings for a specific filer.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>A collection of filings for the specified filer.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetAllFilings()"];
    /// IFilingSvc => IFilingRepository [label="GetAllByFilerId()"];
    /// IFilingRepository >> IFilingSvc [label="return IEnumerable<Filing>"];
    /// IFilingSvc >> Actor [label="return IEnumerable<Filing>"];
    /// \endmsc
    Task<IEnumerable<Filing>> GetAllFilings(long filerId);

    /// <summary>
    /// Gets a filing by ID.
    /// </summary>
    /// <param name="filingId">The ID of the filing to retrieve.</param>
    /// <returns>The filing with the specified ID.</returns>
    /// \msc
    /// Actor, IFilingSvc [label="IFiling\nSvc"], IFilingRepository [label="IFiling\nRepository"];
    /// Actor => IFilingSvc [label="GetAllFilings()"];
    /// IFilingSvc => IFilingRepository [label="GetAll()"];
    /// IFilingRepository >> IFilingSvc [label="\nreturn IEnumerable<Filing>"];
    /// IFilingSvc >> Actor [label="\nreturn IEnumerable<Filing>"];
    /// \endmsc
    [Get(GetAllFilingsPath)]
    Task<IEnumerable<Filing?>> GetAllFilings();
    const string GetAllFilingsPath = "/Filings/GetAllFilings";


    /// <summary>
    /// Gets all filing periods.
    /// </summary>
    /// <returns>A collection of all filing periods.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingPeriodRepository [label="IFiling \n Period \n Repository"];
    /// Actor => IFilingSvc [label="GetAllFilingPeriods()"];
    /// IFilingSvc => IFilingPeriodRepository [label="GetAll()"];
    /// IFilingPeriodRepository >> IFilingSvc [label="return IEnumerable<FilingPeriod>"];
    /// IFilingSvc >> Actor [label="return IEnumerable<FilingPeriod>"];
    /// \endmsc
    Task<IEnumerable<FilingPeriod>> GetAllFilingPeriods();

    /// <summary>
    /// Gets all filing periods for a specific filer.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <param name="filingTypeId">Optional filing type ID.</param>
    /// <param name="filingId"></param>
    /// <returns>A collection of all filing periods.</returns>
    /// 
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingPeriodRepository [label="IFiling \n Period \n Repository"];
    /// Actor => IFilingSvc [label="GetAllFilingPeriods()"];
    /// IFilingSvc => IFilingPeriodRepository [label="GetAll()"];
    /// IFilingPeriodRepository >> IFilingSvc [label="return IEnumerable<FilingPeriod>"];
    /// IFilingSvc >> Actor [label="return IEnumerable<FilingPeriod>"];
    /// \endmsc
    [Get("/api/Filings/{filerId}/FilingPeriods")]
    Task<IEnumerable<FilingPeriodDto>> GetAllFilingPeriodsForFiler(long filerId, long? filingTypeId = null, long? filingId = null);

    /// <summary>
    /// Gets all filing periods for lobbying for a specific filer.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <param name="filingTypeId">Optional filing type ID.</param>
    /// <param name="filingId">Optional filing ID.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains a collection of <see cref="FilingPeriodDto"/> for the specified filer and lobbying context.
    /// </returns>
    [Get("/api/Filing/Filer/{filerId}/LobbyingFilingPeriods")]
    Task<IEnumerable<FilingPeriodDto>> GetAllFilingPeriodsForLobbying(long filerId, long? filingTypeId = null, long? filingId = null);

    /// <summary>
    /// Gets all filing statuses.
    /// </summary>
    /// <returns>A collection of all filing statuses.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingStatusRepository [label="IFiling \n Status \n Repository"];
    /// Actor => IFilingSvc [label="GetAllFilingStatuses()"];
    /// IFilingSvc => IFilingStatusRepository [label="GetAll()"];
    /// IFilingStatusRepository >> IFilingSvc [label="return IEnumerable<FilingStatus>"];
    /// IFilingSvc >> Actor [label="return IEnumerable<FilingStatus>"];
    /// \endmsc
    Task<IEnumerable<FilingStatus>> GetAllFilingStatuses();

    /// <summary>
    /// Gets all filing types.
    /// </summary>
    /// <returns>A collection of all filing types.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingTypeRepository [label="IFiling \n Type \n Repository"];
    /// Actor => IFilingSvc [label="GetAllFilingTypes()"];
    /// IFilingSvc => IFilingTypeRepository [label="GetAll()"];
    /// IFilingTypeRepository >> IFilingSvc [label="return IEnumerable<FilingType>"];
    /// IFilingSvc >> Actor [label="return IEnumerable<FilingType>"];
    /// \endmsc
    Task<IEnumerable<FilingType>> GetAllFilingTypes();

    /// <summary>
    /// Creates a new lobbyist employer filing.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>The filing with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="CreateLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="Create()"];
    /// IFilingRepository >> IFilingSvc [label="return Filing"];
    /// IFilingSvc >> Actor [label="return Filing"];
    /// \endmsc
    Task<Filing> CreateLobbyistEmployerReport(long filerId, long? filingPeriodId);

    /// <summary>
    /// Creates a new OfficeHolderCandidateShortForm filing.
    /// </summary>
    /// <param name="filerId"></param>
    /// <param name="filingPeriodId"></param>
    /// <returns>The filing with the specified ID.</returns>
    Task<Filing> CreateOfficeHolderCandidateShortFormFiling(long? filerId, long? filingPeriodId);

    /// <summary>
    /// Gets a lobbyist employer report by filing ID.
    /// </summary>
    /// <param name="filingId">The ID of the filing to retrieve.</param>
    /// <returns>The lobbyist employer report with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="FindById()"];
    /// IFilingRepository >> IFilingSvc [label="return LobbyistEmployerReport"];
    /// IFilingSvc >> Actor [label="return LobbyistEmployerReport"];
    /// \endmsc
    Task<LobbyistEmployerReport?> GetLobbyistEmployerReport(long filingId);

    /// <summary>
    /// Creates a new lobbyist employer filing.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>The filing with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="CreateLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="Create()"];
    /// IFilingRepository >> IFilingSvc [label="return Filing"];
    /// IFilingSvc >> Actor [label="return Filing"];
    /// \endmsc
    Task<Filing> CreateLobbyistReport(long filerId, long? filingPeriodId);

    /// <summary>
    /// Gets a lobbyist employer report by filing ID.
    /// </summary>
    /// <param name="filingId">The ID of the filing to retrieve.</param>
    /// <returns>The lobbyist employer report with the specified ID.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="FindById()"];
    /// IFilingRepository >> IFilingSvc [label="return LobbyistEmployerReport"];
    /// IFilingSvc >> Actor [label="return LobbyistEmployerReport"];
    /// \endmsc
    Task<LobbyistReport?> GetLobbyistReport(long filingId);

    /// <summary>
    /// Gets the cumulative period start date for a report by filing ID.
    /// Cumulative Period Start: Jan 1 of the odd year OR the first day of the first reporting period submitted by the filer for each legislative session.
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <returns>The cumulative period start date.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetCumulativePeriodStartByFilingId()"];
    /// IFilingSvc => IFilingRepository [label="Get()"];
    /// IFilingRepository >> IFilingSvc [label="return DateTime"];
    /// IFilingSvc >> Actor [label="return DateTime"];
    /// \endmsc
    Task<DateTime> GetCumulativePeriodStartByFilingId(long id);

    /// <summary>
    /// Updates a lobbyist employer report.
    /// </summary>
    /// <param name="filingId">The ID of the filing to update.</param>
    /// <param name="totalPaymentsToInHouseLobbyists">The total payments to in-house lobbyists.</param>
    /// <returns>The updated lobbyist employer report.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="UpdateLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return LobbyistEmployerReport"];
    /// IFilingSvc >> Actor [label="return LobbyistEmployerReport"];
    /// \endmsc
    Task<UpdateLobbyistEmployerReportResponseDto?> UpdateLobbyistEmployerReport(long filingId, decimal? totalPaymentsToInHouseLobbyists);

    /// <summary>
    /// Updates the lump sum amounts in a lobbyist employer report.
    /// </summary>
    /// <param name="filingId">The filing ID.</param>
    /// <param name="totalOverheadExpense">Total overhead expense.</param>
    /// <param name="totalUnderThresholdPayments">Total under threshold payments.</param>
    /// <returns>The updated report or null if not found.</returns>
    Task<UpdateLobbyistEmployerLumpSumResponseDto?> UpdateLobbyistEmployerLumpSums(long filingId, decimal? totalOverheadExpense, decimal? totalUnderThresholdPayments);

    /// <summary>
    /// Updates a lobbyist employer report.
    /// </summary>
    /// <param name="filingId">The ID of the filing to update.</param>
    /// <param name="isMemberOfLobbyingCoalition">Is memeber of a lobbying coalition.</param>
    /// <returns>The updated lobbyist employer report.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="UpdateLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return LobbyistEmployerReport"];
    /// IFilingSvc >> Actor [label="return LobbyistEmployerReport"];
    /// \endmsc
    Task<LobbyistEmployerReport?> UpdateLobbyistEmployerPaymentsMadeToLobbyingCoalition(long filingId, bool? isMemberOfLobbyingCoalition);

    /// <summary>
    /// Cancels a draft lobbyist employer filing.
    /// </summary>
    /// <param name="filingId">The ID of the filer.</param>
    /// <returns>The filing that was canceled.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="CancelLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="Create()"];
    /// IFilingRepository >> IFilingSvc [label="return Filing"];
    /// IFilingSvc >> Actor [label="return Filing"];
    /// \endmsc
    [Put(CancelLobbyistEmployerReportPath)]
    Task<LobbyistEmployerReport?> CancelLobbyistEmployerReport(long filingId);
    const string CancelLobbyistEmployerReportPath = "/api/Filers/{filingId}/Filings/LobbyistEmployerReport/Cancel";

    /// <summary>
    /// Cancels a draft lobbyist filing.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>The filing that was canceled.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="CancelLobbyistReport()"];
    /// IFilingSvc => IFilingRepository [label="Create()"];
    /// IFilingRepository >> IFilingSvc [label="return Filing"];
    /// IFilingSvc >> Actor [label="return Filing"];
    /// \endmsc
    Task<LobbyistReport?> CancelLobbyistReport(long filerId);

    /// <summary>
    /// Update lobbyist employer filing status to submited if all data is valid.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>The filing that was update.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetLegislativeStartDateForFiling()"];
    /// IFilingSvc => IFilingRepository [label="Get()"];
    /// IFilingRepository >> IFilingSvc [label="return DateTime"];
    /// IFilingSvc >> Actor [label="return DateTime"];
    /// \endmsc
    Task<ValidatedLobbyistEmployerReport?> SubmitLobbyistEmployerReport(long filingId, bool? diligenceStatementVerified);

    /// <summary>
    /// Get actions lobbied in a lobbyist employer report.
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <returns>The response.</returns>
    Task<ActionsLobbiedSummaryResponse> GetActionsLobbiedSummaryForFiling(long id);

    /// <summary>
    /// Updates actions lobbied in a lobbyist employer report.
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <param name="request">The request containing actions lobbied data.</param>
    /// <returns>The updated response.</returns>
    Task<ActionsLobbiedSummaryResponse> UpdateLobbyistEmployerReportActionsLobbied(long id, LobbyistEmployerActionsLobbiedRequest request);

    /// <summary>
    /// Validates a list of actions lobbied for bills.
    /// </summary>
    /// <param name="request">The list of actions lobbied to validate.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains a list of 
    /// <see cref="WorkFlowError"/> objects, where each list corresponds to validation errors 
    /// for a specific action lobbied.
    /// </returns>
    Task<List<WorkFlowError>> ValidateLobbyistEmployerActionsLobbiedAgencies(List<ActionsLobbiedRequestDto> request);

    /// <summary>
    /// Update lobbyist employer filing status to submited if all data is valid.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>The filing that was update.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetLegislativeStartDateForFiling()"];
    /// IFilingSvc => IFilingRepository [label="Get()"];
    /// IFilingRepository >> IFilingSvc [label="return DateTime"];
    /// IFilingSvc >> Actor [label="return DateTime"];
    /// \endmsc
    Task<SubmitLobbyingReportResponse?> SubmitLobbyistReport(long filingId, bool? diligenceStatementVerified);

    /// <summary>
    /// Sends a lobbyist report for attestation.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>The validated lobbyist report.</returns>
    /// 
    Task<SubmitLobbyingReportResponse?> SendForAttestationLobbyistReport(long filingId);

    /// <summary>
    /// Get legislative start date for a filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>The legislative start date for the filing.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="GetLegislativeStartDateForFiling()"];
    /// IFilingSvc => IFilingRepository [label="Get()"];
    /// IFilingRepository >> IFilingSvc [label="return DateTime"];
    /// IFilingSvc >> Actor [label="return DateTime"];
    /// \endmsc
    Task<DateTime> GetLegislativeStartDateForFiling(long filingId);

    /// <summary>
    /// Updates a lobbyist employer report.
    /// </summary>
    /// <param name="filingId">The ID of the filing to update.</param>
    /// <param name="contributionsInExistingStatements">Are there contributions contained in existing disclosure statement.</param>
    /// <returns>The updated lobbyist employer report.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="UpdateLobbyistEmployerReport()"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return LobbyistEmployerReport"];
    /// IFilingSvc >> Actor [label="return LobbyistEmployerReport"];
    /// \endmsc
    Task<LobbyistEmployerReport?> UpdateLobbyistEmployerCampaignContributions(long filingId, bool? contributionsInExistingStatements, long? relatedFilerId);

    /// <summary>
    /// Updates contributions in existing statements for a lobbyist employer report.
    /// </summary>
    /// <param name="filingId">The ID of the filing to update.</param>
    /// <param name="contributionsInExistingStatements">Whether contributions are in existing statements.</param>
    /// <param name="relatedFilerIds">List of related filer IDs to associate with the filing.</param>
    /// <returns>The updated lobbyist employer report with validation results.</returns>
    ///
    /// \msc
    /// Actor, IFilingSvc [label="IFiling \n Svc"], IFilingRepository [label="IFiling \n Repository"];
    /// Actor => IFilingSvc [label="UpdateContributionsInExistingStatements()"];
    /// IFilingSvc => IFilingRepository [label="Update()"];
    /// IFilingRepository >> IFilingSvc [label="return LobbyistEmployerReport"];
    /// IFilingSvc >> Actor [label="return UpdateContributionsInExistingStatementsResponseDto"];
    /// \endmsc
    [Put(UpdateContributionsInExistingStatementsPath)]
    Task<UpdateContributionsInExistingStatementsResponseDto?> UpdateContributionsInExistingStatements(long filingId, bool? contributionsInExistingStatements, List<long> relatedFilerIds);
    const string UpdateContributionsInExistingStatementsPath = "/api/Filings/ContributionsInExistingStatements/{filingId}";

    /// <summary>
    /// Gets all filing summaries for a specific filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of filing summaries for the specified filing.</returns>
    /// \endmsc
    Task<IEnumerable<FilingSummary>> GetAllFilingSummariesByFilingId(long filingId);

    /// <summary>
    /// Runs task to update filingSummary for created transaction
    /// </summary>
    /// <param name="transaction">Transaction recently created.</param>
    /// <returns>Void.</returns>
    /// \endmsc
    Task OnTransactionCreated(Transaction transaction, long? disclosureFilingId);

    /// <summary>
    /// Runs task to update filingSummary for updated transaction
    /// </summary>
    /// <param name="originalTransaction">Original transaction to be updated.</param>
    /// <param name="updatedTransaction">Transaction recently updated.</param>
    /// <returns>Void.</returns>
    /// \endmsc
    Task OnTransactionUpdated(Transaction originalTransaction, Transaction updatedTransaction, long? disclosureFilingId);

    DateTime GetLegislativeStartDateForDate(DateTime startDate);
    DateTime GetLegislativeEndDateForDate(DateTime endDate);

    /// <summary>
    /// Gets report data for Disclosure.
    /// </summary>
    /// <returns>A collection of report datas of Disclosure.</returns>
    /// \endmsc
    Task<IEnumerable<FilingReportGridDto>> GetFilingReports();

    /// <summary>
    /// Runs task to create a 72h report filing.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>The filing with the specified Id.</returns>
    /// \endmsc
    Task<FilingResponseDto> CreateReport72H(long filerId);

    /// <summary>
    /// Gets a 72h report by filing ID.
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <returns>The filing with the specified Id.</returns>
    /// \endmsc
    Task<Report72HResponseDto> GetReport72H(long id);

    /// <summary>
    /// Update a 72h report.
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <param name="id">The ID of the filing.</param>
    /// <returns>The filing with the specified Id.</returns>
    /// \endmsc
    Task<ActionsLobbiedSummaryResponse> UpdateReport72HActionsLobbied(long id, Report72HActionsLobbiedRequestDto request);

    /// <summary>
    /// Validates a list of actions lobbied for bills.
    /// </summary>
    /// <param name="request">The list of actions lobbied to validate.</param>
    /// <returns>Returns a list of list of validation errors seperated by the record the action lobbied they correspond to.</returns>
    Task<ValidateReport72HActionsLobbiedResponseDto> ValidateReport72HActionsLobbiedBills(ValidateReport72HActionsLobbiedRequestDto request);

    /// <summary>
    /// Validates a list of actions lobbied for agencies.
    /// </summary>
    /// <param name="request">The list of actions lobbied to validate.</param>
    /// <returns>Returns a list of list of validation errors seperated by the record the action lobbied they correspond to.</returns>
    Task<ValidateReport72HActionsLobbiedResponseDto> ValidateReport72HActionsLobbiedAgencies(ValidateReport72HActionsLobbiedRequestDto request);
    /// <summary>
    /// Runs task to get all Legistlative Sessions.
    /// </summary>
    /// <returns>The filing with the list.</returns>
    /// \endmsc
    Task<IEnumerable<LegislativeSession>> GetAllLegislativeSessions();

    /// <summary>
    /// Perform actions after the transaction is soft-deleted - Update filing summary amount
    /// </summary>
    /// <param name="transaction">Transaction that is soft-deleted</param>
    /// <param name="filingId">ID of Filing to which the transaction belongs</param>
    /// <returns></returns>
    Task OnTransactionDeletedAsync(Transaction transaction, long filingId);

    /// <summary>
    /// Runs task to create a 48h report filing.
    /// </summary>
    /// <param name="filerId">The ID of the filer.</param>
    /// <returns>The filing with the specified Id.</returns>
    /// \endmsc
    Task<FilingResponseDto> CreateReport48H(long filerId);

    /// <summary>
    /// Gets a 48h report by filing ID.
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <returns>The filing with the specified Id.</returns>
    /// \endmsc
    Task<Report48HResponseDto> GetReport48H(long id);

    /// <summary>
    ///  Submits a Lobbyist Report for approval
    /// </summary>
    /// <param name="submission">A complete lobbyist Report (form 615), including attestation</param>
    /// <returns></returns>
    [Post(SubmitLobbyistReportForEfilePath)]
    Task<RegistrationResponseDto> SubmitLobbyistReportForEfile(LobbyistReportDto submission);
    const string SubmitLobbyistReportForEfilePath = "/api/Filing/LobbyistReport/Submit";

    /// <summary>
    /// Updates the total payments amount of PUC Activity.
    /// </summary>
    /// <param name="id">The Filing ID.</param>
    /// <param name="request">The total payment amount.</param>
    /// <returns></returns>
    Task<UpdatePucActivityPaymentResponseDto> UpdatePucActivityPayment(long id, UpdatePucActivityPaymentRequestDto request);

    /// <summary>
    /// Submit a 48h report by filing ID.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>The filing that was update.</returns>
    /// \endmsc
    Task<SubmitLobbyingReportResponse?> SubmitReport48H(long filingId, bool? diligenceStatementVerified);

    /// <summary>
    /// Sends a 48-hour report for attestation.
    /// </summary>
    /// <param name="filingId">The ID of the filing to send for attestation.</param>
    /// <param name="request">The request containing selected responsible officer IDs.</param>
    /// <returns>
    /// A <see cref="SubmitLobbyingReportResponse"/> representing the result of the attestation process.
    /// </returns>
    Task<SubmitLobbyingReportResponse> SendForAttestationReport48H(long filingId, Report48HSendForAttestationRequest request);

    /// <summary>
    /// Adds an associated filer user by filing.
    /// </summary>
    /// <param name="filingId">The ID of the filing to associate the user with.</param>
    /// <param name="byFilingRequest">The request containing the user and filer role information.</param>
    /// <returns>
    /// A <see cref="FilerUserDto"/> representing the associated filer user.
    /// </returns>
    Task<FilerUserDto> AddAssociatedFilerUserByFiling(long filingId, AddAssociatedFilerUserByFilingRequest byFilingRequest);

    /// <summary>
    /// Creates Filing Amendment
    /// </summary>
    /// <param name="id">The ID of the filing.</param>
    /// <returns>The new filing amendment.</returns>
    /// \endmsc
    Task<FilingResponseDto> CreateFilingAmendmentAsync(long id);

    /// <summary>
    /// Perform actions after the transaction is updated
    /// </summary>
    /// <param name="transaction">Transaction to update</param>
    /// <param name="varianceAmount">Variance amount during the change</param>
    /// <param name="disclosureFilingId">ID of disclosure filing that transaction belongs to</param>
    /// <param name="originalContactId">ID of the original contact</param>
    /// <returns></returns>
    Task OnTransactionUpdatedAsync(Transaction transaction, decimal varianceAmount, long? disclosureFilingId, long? originalContactId);

    /// <summary>
    /// Runs task to send a 72h report filing for attestation.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <param name="selectedResponsibleOfficerIds">The List of selected user ID/officer ID to be sent notification.</param>
    /// <returns></returns>
    /// \endmsc
    [Get(SendForAttestationReport72HPath)]
    Task<ValidatedReport72H?> SendForAttestationReport72H(long filingId, List<long> selectedResponsibleOfficerIds);
    const string SendForAttestationReport72HPath = "/Filings/SendForAttestationReport72H/{filingId}";

    /// <summary>
    /// Get a collection of Users that is responsbile for Attestation
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <returns>A collection of Users</returns>
    [Get(GetResponsibleOfficersPath)]
    Task<IEnumerable<ReponsibleOfficerDto>> GetResponsibleOfficers(long filingId);
    const string GetResponsibleOfficersPath = "/Filings/GetResponsibleOfficers/{filingId}";

    [Get(GetFilersAssociatedUserPath)]
    Task<IEnumerable<FilerUserDto>> GetFilersAssociatedUserWithFilingType(long userId, long filingTypeId);
    const string GetFilersAssociatedUserPath = "/Filings/GetFilersAssociatedUser/{userId}/filingType/{filingTypeId}";

    /// <summary>
    /// Gets all filings by filer type
    /// </summary>
    /// <param name="filerTypeId">filer type ID</param>
    /// <returns>IEnumerable of Filing</returns>
    /// \msc
    /// Actor, IFilingSvc [label="IFiling\nSvc"], IFilingRepository [label="IFiling\nRepository"];
    /// Actor => IFilingSvc [label="GetAllFilingsByFilerType()"];
    /// IFilingSvc => IFilingRepository [label="GetAllFilingsByFilerType()"];
    /// IFilingRepository >> IFilingSvc [label="\nreturn IEnumerable<Filing>"];
    /// IFilingSvc >> Actor [label="\nreturn IEnumerable<Filing>"];
    /// \endmsc
    [Get(GetAllFilingsByFilerTypePath)]
    Task<IEnumerable<Filing?>> GetAllFilingsByFilerType(long filerTypeId);
    const string GetAllFilingsByFilerTypePath = "/Filings/GetAllFilingsByFilerType";


    /// <summary>
    /// Gets all filings by filer status
    /// </summary>
    /// <param name="filerStatusId">filer status ID</param>
    /// <returns>IEnumerable of Filing</returns>
    /// \msc
    /// Actor, IFilingSvc [label="IFiling\nSvc"], IFilingRepository [label="IFiling\nRepository"];
    /// Actor => IFilingSvc [label="GetAllFilingsByFilerStatus()"];
    /// IFilingSvc => IFilingRepository [label="GetAllFilingsByFilerStatus()"];
    /// IFilingRepository >> IFilingSvc [label="\nreturn IEnumerable<Filing>"];
    /// IFilingSvc >> Actor [label="\nreturn IEnumerable<Filing>"];
    /// \endmsc
    [Get(GetAllFilingsByFilerStatusPath)]
    Task<IEnumerable<Filing?>> GetAllFilingsByFilerStatus(long filerStatusId);
    const string GetAllFilingsByFilerStatusPath = "/Filings/GetAllFilingsByFilerStatus";

    /// <summary>
    /// Gets all late filings by filer status
    /// </summary>
    /// <param name="filerStatusId">filer status ID</param>
    /// <returns>IEnumerable of Filing</returns>
    /// \msc
    /// Actor, IFilingSvc [label="IFiling\nSvc"], IFilingRepository [label="IFiling\nRepository"];
    /// Actor => IFilingSvc [label="GetAllLateFilings()"];
    /// IFilingSvc => IFilingRepository [label="GetAllLateFilings()"];
    /// IFilingRepository >> IFilingSvc [label="\nreturn IEnumerable<Filing>"];
    /// IFilingSvc >> Actor [label="\nreturn IEnumerable<Filing>"];
    /// \endmsc
    [Get(GetAllLateFilingsPath)]
    Task<IEnumerable<Filing?>> GetAllLateFilings(long filerStatusId);
    const string GetAllLateFilingsPath = "/Filings/GetAllLateFilings";

    /// <summary>
    /// Searches for filers by name or ID, filtered by filer type, and returns data suitable for AutoComplete components.
    /// </summary>
    /// <param name="query">The search query (name or ID)</param>
    /// <param name="filerTypeId">The filer type ID to filter by</param>
    /// <returns>A collection of FilerSearchDto objects for AutoComplete components</returns>
    [Get(SearchFilersPath)]
    Task<IEnumerable<FilerSearchDto>> SearchFilers(string query, long filerTypeId);
    const string SearchFilersPath = "/api/SearchFilers";

    /// <summary>
    /// Gets related filers associated with a specific filing for contributions in existing statements.
    /// </summary>
    /// <param name="filingId">The ID of the filing to retrieve related filers for.</param>
    /// <returns>A collection of FilerSearchDto objects representing related filers.</returns>
    [Get(GetRelatedFilersByFilingIdPath)]
    Task<IEnumerable<FilerSearchDto>> GetRelatedFilersByFilingId(long filingId);
    const string GetRelatedFilersByFilingIdPath = "/api/Filings/{filingId}/RelatedFilers";


    /// <summary>
    /// Runs task to send a Lobbyist Employer report filing for attestation.
    /// </summary>
    /// <param name="filingId">The ID of the filing.</param>
    /// <param name="selectedResponsibleOfficerIds">The List of selected user ID/officer ID to be sent notification.</param>
    /// <returns></returns>
    /// \endmsc
    [Get(SendForAttestationReportLobbyistEmployerPath)]
    Task<ValidatedLobbyistEmployerReport?> SendForAttestationReportLobbyistEmployer(long filingId, List<long> selectedResponsibleOfficerIds);
    const string SendForAttestationReportLobbyistEmployerPath = "/Filings/SendForAttestationReportLobbyistEmployer/{filingId}";

    /// <summary>
    /// Updates the custom period of a filing.
    /// </summary>
    /// <param name="id">The ID of the filing to update.</param>
    /// <param name="request">The request containing the new custom period data.</param>
    /// <returns>
    /// A <see cref="FilingResponseDto"/> representing the updated filing response.
    /// </returns>
    [Patch("/Filings/UpdateFilingCustomPeriod/{id}")]
    Task<FilingResponseDto> UpdateFilingCustomPeriod(long id, UpdateFilingCustomPeriodRequest request);

    /// <summary>
    /// Updates the custom period of a filing.
    /// </summary>
    /// <param name="id">The ID of the filing to update.</param>
    /// <param name="request">The request containing the amendment explanation.</param>
    /// <returns>
    /// A <see cref="UpdateAmendmentExplanationResponse"/> representing the updated filing response.
    /// </returns>
    [Patch(UpdateAmendmentExplanationPath)]
    Task<UpdateAmendmentExplanationResponse> UpdateAmendmentExplanation(long id, UpdateAmendmentExplanationRequest request);
    const string UpdateAmendmentExplanationPath = "/Filings/UpdateAmendmentExplanation/{id}";

}
