using System.Globalization;
using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Repositories.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.Common;
using SOS.CalAccess.Models.FilerDisclosure;
using SOS.CalAccess.Models.FilerDisclosure.Contacts;
using SOS.CalAccess.Models.FilerDisclosure.Filings;
using SOS.CalAccess.Models.FilerDisclosure.Filings.Lobbying;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;
using SOS.CalAccess.Models.FilerDisclosure.Transactions.ActivityExpense;
using SOS.CalAccess.Models.FilerRegistration;
using SOS.CalAccess.Models.FilerRegistration.Filers;
using SOS.CalAccess.Tests.Common;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.Filerdisclosure.Transactions;

/// <summary>
/// Contains unit tests for the <see cref="TransactionRepository"/> class.
/// </summary>
[TestFixture]
[TestOf(typeof(TransactionRepository))]
[FixtureLifeCycle(LifeCycle.InstancePerTestCase)]
public class TransactionRepositoryTests
{
    private TransactionRepository _repository;


    /// <summary>
    /// Verifies that a<see cref = "Transaction" /> entity can be created, added to the database,
    /// and successfully saved using the repository's <c>Create</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldCreateTransactionAndSaveChanges()
    {
        using DatabaseContextFactory factory = new();
        using DatabaseContext context = await factory.CreateContext();

        _repository = new TransactionRepository(context);

        Transaction data = GetData();

        Transaction result = await _repository.Create(data);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(data.Id, Is.EqualTo(result.Id));
        });
    }

    /// <summary>
    /// Verifies that a<see cref = "Transaction" /> entity can be deleted from the database,
    /// and successfully saved using the repository's <c>Delete</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldDeleteTransactionAndSaveChanges()
    {
        using DatabaseContextFactory factory = new();
        using DatabaseContext context = await factory.CreateContext();

        // Instantiate the repository
        _repository = new TransactionRepository(context);

        // Arrange: Seed initial data into the context
        Transaction data = GetData();

        // Add the entity to the database
        Transaction addedEntity = await _repository.Create(data);

        // Ensure the entity is added
        Assert.That(addedEntity, Is.Not.Null);
        Assert.That(addedEntity.Id, Is.EqualTo(data.Id));

        // Act: Delete the entity
        bool deleteResult = await _repository.Delete(addedEntity);

        // Assert: Validate the entity is deleted
        Assert.That(deleteResult, "Delete method should return true when successful.");

        bool exists = await context.Set<Transaction>().AnyAsync(e => e.Id == addedEntity.Id);
        Assert.That(exists, Is.False, "The entity should no longer exist in the database.");
    }

    /// <summary>
    /// Verifies that a specific <see cref="Transaction"/> entity can be retrieved from the database
    /// using its unique identifier with the repository's <c>FindById</c> method.database,   
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldFindTransactiontById()
    {
        using DatabaseContextFactory factory = new();
        using DatabaseContext context = await factory.CreateContext();

        // Arrange
        _repository = new TransactionRepository(context);
        Transaction data = GetData();

        // Add the entity to the database
        Transaction addedEntity = await _repository.Create(data);

        // Act
        Transaction? result = await _repository.FindById(addedEntity.Id);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Id, Is.EqualTo(addedEntity.Id));
    }

    /// <summary>
    /// Verifies that all <see cref="Transaction"/> entities can be retrieved from the database
    /// using the repository's <c>GetAll</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetAllTransactions()
    {
        using DatabaseContextFactory factory = new();
        using DatabaseContext context = await factory.CreateContext();

        // Arrange
        _repository = new TransactionRepository(context);

        Transaction data1 = GetData();
        Transaction data2 = GetData(100);

        await _repository.Create(data1);
        await _repository.Create(data2);

        // Act
        IEnumerable<Transaction> result = await _repository.GetAll();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(2), "GetAll should return all entities in the database.");
    }

    /// <summary>
    /// Verifies that a<see cref = "Transaction" /> entity can be updated to the database,
    /// and successfully saved using the repository's <c>Update</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>

    [Test]
    public async Task ShouldUpdateTransactionAndSaveChanges()
    {
        using DatabaseContextFactory factory = new();
        using DatabaseContext context = await factory.CreateContext();

        // Arrange
        _repository = new TransactionRepository(context);
        Transaction data = GetData();

        // Add the entity to the database
        Transaction addedEntity = await _repository.Create(data);

        // Modify a property
        addedEntity.Amount = (Currency)100.0M;
        // Act
        Transaction updatedEntity = await _repository.Update(addedEntity);

        // Assert
        Assert.That(updatedEntity, Is.Not.Null);
        Assert.That(updatedEntity.Amount, Is.EqualTo((Currency)100.0M), "The entity should be updated with the new data.");
    }


    private static InKindContribution GetData(decimal optionalAmount = 0)
    {
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        return new InKindContribution() { Amount = (Currency)optionalAmount, TransactionDate = date };
    }

    /// <summary>
    /// Verifies that all <see cref="Transaction"/> entities can be retrieved from the database
    /// using the repository's <c>GetAll</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling()
    {
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        long filingId = 1L;
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);

        // Create disclosure filings for the test
        Filing filing1 = new()
        {
            Id = 1,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified) },
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        Filing filing2 = new()
        {
            Id = 2,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified) },
            StartDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        // Create organization contacts for the test
        OrganizationContact organization1 = new()
        {
            Id = 101,
            FilerId = 999, // The filer who created this contact
            ContactFilerId = 1001, // The filer this contact represents
            OrganizationName = "Lobbying Coalition A"
        };

        OrganizationContact organization2 = new()
        {
            Id = 102,
            FilerId = 999,
            ContactFilerId = 1002,
            OrganizationName = "Lobbying Coalition B"
        };

        context.Set<Filing>().AddRange(filing1, filing2);
        context.Set<FilerContact>().AddRange(organization1, organization2);
        context.PaymentMadeToLobbyingCoalition.AddRange(
            new PaymentMadeToLobbyingCoalition
            {
                Id = 1,
                FilerId = 999,
                ContactId = 101, // Reference to organization1
                Amount = new Currency(500),
                Contact = organization1,
                FilingTransactions = [new() { FilingId = 1, TransactionId = 1, Filing = filing1 }]
            },
            new PaymentMadeToLobbyingCoalition
            {
                Id = 2,
                FilerId = 999,
                ContactId = 101, // Same contact as first transaction
                Amount = new Currency(200),
                Contact = organization1,
                FilingTransactions = [new() { FilingId = 1, TransactionId = 2, Filing = filing1 }]
            },
            new PaymentMadeToLobbyingCoalition
            {
                Id = 3,
                FilerId = 999,
                ContactId = 102, // Different contact
                Amount = new Currency(300),
                Contact = organization2,
                FilingTransactions = [new() { FilingId = 1, TransactionId = 3, Filing = filing1 }]
            },
            new PaymentMadeToLobbyingCoalition
            {
                Id = 4,
                FilerId = 999,
                ContactId = 101, // Same as first contact again
                Amount = new Currency(100),
                Contact = organization1,
                FilingTransactions = [new() { FilingId = 2, TransactionId = 4, Filing = filing2 }]
            }
        );

        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        IEnumerable<PaymentMadeToLobbyingCoalitionResponse> result = await repository.GetAllPaymentMadeToLobbyingCoalitionTransactionsForFiling(filingId, legislativeStartDate);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            // Should have two results - one for each Contact aggregation
            Assert.That(result.Count(), Is.EqualTo(2));
            // Check that we have the expected contact groupings with correct coalition names
            Assert.That(result.Any(r => r.FilerId == 1001 && r.CoalitionName == "Lobbying Coalition A" && r.CumulativeAmount == new Currency(700m)), Is.True);
            Assert.That(result.Any(r => r.FilerId == 1002 && r.CoalitionName == "Lobbying Coalition B" && r.CumulativeAmount == new Currency(300m)), Is.True);
            // Also check the AmountThisPeriod values
            Assert.That(result.Any(r => r.FilerId == 1001 && r.AmountThisPeriod == new Currency(700m)), Is.True);
            Assert.That(result.Any(r => r.FilerId == 1002 && r.AmountThisPeriod == new Currency(300m)), Is.True);
        });
    }

    /// <summary>
    /// Verifies that all <see cref="OtherPaymentsToInfluence"/> entities can be retrieved from the database
    /// using the repository's <c>GetAllOtherPaymentsToInfluenceTransactionsForFiling</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetAllOtherPaymentToInfluenceTransactionsForFiling()
    {
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        long filingId = 1L;
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);

        // Create disclosure filings for the test
        Filing filing1 = new()
        {
            Id = 1,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0) },
            StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        Filing filing2 = new()
        {
            Id = 2,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2022, 1, 1, 0, 0, 0, 0) },
            StartDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
        };

        context.Set<Filing>().AddRange(filing1, filing2);
        context.OtherPaymentsToInfluence.AddRange(
            new OtherPaymentsToInfluence
            {
                Id = 1,
                FilerId = 999,
                PaymentCodeId = 1,
                ContactId = 101, // Reference to organization1
                Amount = new Currency(500),
                FilingTransactions = [new() { FilingId = 1, TransactionId = 1, Filing = filing1 }]
            },
            new OtherPaymentsToInfluence
            {
                Id = 2,
                FilerId = 999,
                PaymentCodeId = 1,
                ContactId = 101,
                Amount = new Currency(200),
                FilingTransactions = [new() { FilingId = 1, TransactionId = 2, Filing = filing1 }]
            },
            new OtherPaymentsToInfluence
            {
                Id = 3,
                FilerId = 999,
                PaymentCodeId = 1,
                ContactId = 102, // Different contact
                Amount = new Currency(300),
                FilingTransactions = [new() { FilingId = 1, TransactionId = 3, Filing = filing1 }]
            },
            new OtherPaymentsToInfluence
            {
                Id = 4,
                FilerId = 999,
                ContactId = 101, // Same as first contact again
                Amount = new Currency(100),
                FilingTransactions = [new() { FilingId = 2, TransactionId = 4, Filing = filing2 }]
            }
        );

        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        IEnumerable<OtherPaymentsToInfluenceResponse> result = await repository.GetAllOtherPaymentsToInfluenceTransactionsForFiling(filingId, legislativeStartDate);

        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Count(), Is.EqualTo(3));

            // Transaction 1 (Contact 101, Amount 500)
            Assert.That(result.Any(r =>
                r.Amount == new Currency(500) &&
                r.CumulativeAmount == new Currency(700)), Is.True);

            // Transaction 2 (Contact 101, Amount 200)
            Assert.That(result.Any(r =>
                r.Amount == new Currency(200) &&
                r.CumulativeAmount == new Currency(700)), Is.True);

            // Transaction 3 (Contact 102, Amount 300)
            Assert.That(result.Any(r =>
                r.Amount == new Currency(300) &&
                r.CumulativeAmount == new Currency(300)), Is.True);
        });
    }

    [Test]
    public async Task GetOtherPaymentsToInfluenceTransactionById_ValidTransactionId_ReturnsTransaction()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        long transactionId = 789;
        long contactId = 456;
        var transaction = new OtherPaymentsToInfluence()
        {
            Id = transactionId,
            ContactId = contactId,
            Amount = (Currency)10,
            Contact = new IndividualContact() { Id = contactId },
            ActionsLobbied = new List<ActionsLobbied?>()
            {
                new()
                {
                    Id = 1
                }
            },
            FilingTransactions = new List<FilingTransaction>
            {
                new() { TransactionId = transactionId, Filing = new Filing { StatusId = 1 } }
            }
        };

        context.Set<OtherPaymentsToInfluence>().Add(transaction);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetOtherPaymentsToInfluenceTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.ContactId, Is.EqualTo(contactId));
            Assert.That(result.FilingTransactions, Has.Count.EqualTo(1));
            Assert.That(result.FilingTransactions.First().TransactionId, Is.EqualTo(transactionId));
        });
    }

    [Test]
    public async Task GetOtherPaymentsToInfluenceTransactionById_ThrowError()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        long transactionId = 1;

        var repository = new TransactionRepository(context);

        // Act & Assert
        var exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await repository.GetOtherPaymentsToInfluenceTransactionById(transactionId));

        Assert.That(exception!.Message, Is.EqualTo($"Not found Other payment to influence transaction {transactionId}"));
    }


    [Test]
    public async Task ShouldGetAllEndOfSessionLobbyingTransactionsForFiling()
    {
        // Arrange
        long filingId = 123L;
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        DatabaseContext context = new(options);
        _ = await context.Database.EnsureCreatedAsync();

        // Create filing for the test
        Filing filing = new()
        {
            Id = filingId,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Local) }
        };

        // Create contacts for the test
        OrganizationContact organizationContact = new()
        {
            Id = 101,
            FilerId = 999,
            ContactFilerId = 1001,
            OrganizationName = "Lobbying Firm A"
        };

        OrganizationContact individualContact = new()
        {
            Id = 102,
            FilerId = 999,
            ContactFilerId = 1002,
            OrganizationName = "Lobbying Firm B"
        };

        // Create EndOfSessionLobbying transactions
        List<EndOfSessionLobbying> transactions =
        [
            new()
            {
                Id = 1,
                FilerId = 999,
                ContactId = organizationContact.Id,
                Contact = organizationContact,
                Amount = new Currency(500),
                DateLobbyingFirmHired = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Local),
                FilingTransactions = [new() { FilingId = filingId, TransactionId = 1, Filing = filing }]
            },
            new()
            {
                Id = 2,
                FilerId = 999,
                ContactId = individualContact.Id,
                Contact = individualContact,
                Amount = new Currency(300),
                DateLobbyingFirmHired = new DateTime(2023, 2, 15, 0, 0, 0, DateTimeKind.Local),
                FilingTransactions = [new() { FilingId = filingId, TransactionId = 2, Filing = filing }]
            },
            // Transaction associated with a different filing (should be excluded)
            new()
            {
                Id = 3,
                FilerId = 999,
                ContactId = organizationContact.Id,
                Contact = organizationContact,
                Amount = new Currency(200),
                DateLobbyingFirmHired = new DateTime(2023, 3, 10, 0, 0, 0, DateTimeKind.Local),
                FilingTransactions = [new() { FilingId = 999, TransactionId = 3 }]
            }
        ];

        _ = context.Set<Filing>().Add(filing);
        context.Set<FilerContact>().AddRange(organizationContact, individualContact);
        context.EndOfSessionLobbying.AddRange(transactions);
        _ = await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        IEnumerable<EndOfSessionLobbyingDto> result = await repository.GetAllEndOfSessionLobbyingTransactionsForFiling(filingId);
        List<EndOfSessionLobbyingDto> resultList = result.ToList();

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            // Should have two results (only transactions for the given filing)
            Assert.That(resultList, Has.Count.EqualTo(2));

            // Verify first transaction data
            EndOfSessionLobbyingDto? firstTransaction = resultList.FirstOrDefault(r => r.FilerId == 1001);
            Assert.That(firstTransaction, Is.Not.Null);
            Assert.That(firstTransaction!.FirmName, Is.EqualTo("Lobbying Firm A"));
            Assert.That(firstTransaction.Amount, Is.EqualTo(500));
            Assert.That(firstTransaction.DateLobbyingFirmHired, Is.EqualTo(new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Local)));

            // Verify second transaction data
            EndOfSessionLobbyingDto? secondTransaction = resultList.FirstOrDefault(r => r.FilerId == 1002);
            Assert.That(secondTransaction, Is.Not.Null);
            Assert.That(secondTransaction!.FirmName, Is.EqualTo("Lobbying Firm B"));
            Assert.That(secondTransaction.Amount, Is.EqualTo(300));
            Assert.That(secondTransaction.DateLobbyingFirmHired, Is.EqualTo(new DateTime(2023, 2, 15, 0, 0, 0, DateTimeKind.Local)));
        });
    }

    /// <summary>
    /// Verifies that all <see cref="Transaction"/> entities can be retrieved from the database
    /// using the repository's <c>GetAll</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetAllPaymentMadeToLobbyingFirmsTransactionsForFiling()
    {
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        long filingId = 1L;
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);

        // Create disclosure filings for the test
        Filing filing1 = new()
        {
            Id = 1,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0) }
        };

        Filing filing2 = new()
        {
            Id = 2,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2022, 1, 1, 0, 0, 0, 0) }
        };

        // Create organization contacts for the test
        OrganizationContact organization1 = new()
        {
            Id = 101,
            FilerId = 999, // The filer who created this contact
            ContactFilerId = 1001, // The filer this contact represents
            OrganizationName = "Lobbying Coalition A"
        };

        OrganizationContact organization2 = new()
        {
            Id = 102,
            FilerId = 999,
            ContactFilerId = 1002,
            OrganizationName = "Lobbying Coalition B"
        };

        context.Set<Filing>().AddRange(filing1, filing2);
        context.Set<FilerContact>().AddRange(organization1, organization2);
        context.PaymentMadeToLobbyingFirms.AddRange(
            new PaymentMadeToLobbyingFirms
            {
                Id = 1,
                FilerId = 999,
                ContactId = 101, // Reference to organization1
                Amount = new Currency(500),
                Contact = organization1,
                FilingTransactions = [new() { FilingId = 1, TransactionId = 1, Filing = filing1 }]
            },
            new PaymentMadeToLobbyingFirms
            {
                Id = 2,
                FilerId = 999,
                ContactId = 101, // Same contact as first transaction
                Amount = new Currency(200),
                Contact = organization1,
                FilingTransactions = [new() { FilingId = 1, TransactionId = 2, Filing = filing1 }]
            },
            new PaymentMadeToLobbyingFirms
            {
                Id = 3,
                FilerId = 999,
                ContactId = 102, // Different contact
                Amount = new Currency(300),
                Contact = organization2,
                FilingTransactions = [new() { FilingId = 1, TransactionId = 3, Filing = filing1 }]
            },
            new PaymentMadeToLobbyingFirms
            {
                Id = 4,
                FilerId = 999,
                ContactId = 101, // Same as first contact again
                Amount = new Currency(100),
                Contact = organization1,
                FilingTransactions = [new() { FilingId = 2, TransactionId = 4, Filing = filing2 }]
            }
        );

        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        IEnumerable<PaymentMadeToLobbyingFirms> result = await repository.GetAllPaymentMadeToLobbyingFirmsTransactionsForFiling(filingId, legislativeStartDate);

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(3));
    }

    [Test]
    public async Task GetTransactionsForFilingSummary_ShouldReturnCorrectData_ForValidFiling()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        long filerId = 1001L;
        long filingId = 1L;
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime legislativeEndDate = new(2024, 12, 31, 0, 0, 0, DateTimeKind.Local);

        DateTime currentFilingPeriodStartDate = new(2023, 7, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime currentFilingPeriodEndDate = new(2023, 9, 30, 0, 0, 0, DateTimeKind.Local);
        FilingPeriod currentFilingPeriod = new()
        {
            Id = 1,
            StartDate = currentFilingPeriodStartDate,
            EndDate = currentFilingPeriodEndDate
        };

        DateTime previousFilingPeriodStartDate = new(2023, 4, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime previousFilingPeriodEndDate = new(2023, 6, 30, 0, 0, 0, DateTimeKind.Local);
        FilingPeriod previousFilingPeriod = new()
        {
            Id = 2,
            StartDate = currentFilingPeriodStartDate,
            EndDate = currentFilingPeriodEndDate
        };

        // Create contacts
        OrganizationContact contact1 = new()
        {
            Id = 101,
            ContactFilerId = 201,
            OrganizationName = "Test Organization"
        };

        IndividualContact contact2 = new()
        {
            Id = 102,
            ContactFilerId = 202,
            FirstName = "John",
            LastName = "Doe"
        };

        // Create multiple filings with different transaction types
        LobbyistEmployerReport currentFiling = new()
        {
            Id = filingId,
            OriginalId = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = currentFilingPeriodStartDate,
            EndDate = currentFilingPeriodEndDate,
            FilingPeriod = currentFilingPeriod,
            FilingPeriodId = currentFilingPeriod.Id,
            TotalPaymentsPucActivity = new Currency(2000),
            TotalPaymentsToInHouseLobbyists = new Currency(1500)
        };

        LobbyistEmployerReport previousFiling = new()
        {
            Id = 2,
            OriginalId = 2,
            FilerId = filerId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = previousFilingPeriodStartDate,
            EndDate = previousFilingPeriodEndDate,
            FilingPeriod = previousFilingPeriod,
            FilingPeriodId = previousFilingPeriod.Id,
            TotalPaymentsPucActivity = new Currency(1000),
            TotalPaymentsToInHouseLobbyists = new Currency(800)
        };

        // Create transaction types
        long lobbyingFirmPaymentType = TransactionType.PaymentMadeToLobbyingFirms.Id;
        long activityExpenseType = TransactionType.ActivityExpense.Id;

        // Create transactions for current filing
        PaymentMadeToLobbyingFirms transaction1 = new()
        {
            Id = 1,
            FilerId = filerId,
            ContactId = contact1.Id,
            Contact = contact1,
            Amount = new Currency(500),
            TransactionDate = date,
            FilingTransactions =
        [
            new() { FilingId = filingId, TransactionId = 1, Filing = currentFiling }
        ]
        };

        ActivityExpense transaction2 = new()
        {
            Id = 2,
            FilerId = filerId,
            ContactId = contact2.Id,
            Contact = contact2,
            Amount = new Currency(300),
            TransactionDate = date,
            FilingTransactions =
        [
            new() { FilingId = filingId, TransactionId = 2, Filing = currentFiling }
        ]
        };

        // Create transactions for previous filing
        PaymentMadeToLobbyingFirms transaction3 = new()
        {
            Id = 3,
            FilerId = filerId,
            ContactId = contact1.Id,
            Contact = contact1,
            Amount = new Currency(400),
            TransactionDate = date.AddMonths(-3),
            FilingTransactions =
        [
            new() { FilingId = 2, TransactionId = 3, Filing = previousFiling }
        ]
        };

        ActivityExpense transaction4 = new()
        {
            Id = 4,
            FilerId = filerId,
            ContactId = contact2.Id,
            Contact = contact2,
            Amount = new Currency(200),
            TransactionDate = date.AddMonths(-3),
            FilingTransactions =
        [
            new() { FilingId = 2, TransactionId = 4, Filing = previousFiling }
        ]
        };

        // Add all entities to database
        context.Set<FilingPeriod>().AddRange(currentFilingPeriod, previousFilingPeriod);
        context.Set<FilerContact>().AddRange(contact1, contact2);
        context.Set<Filing>().AddRange(currentFiling, previousFiling);
        context.Transactions.AddRange(transaction1, transaction2, transaction3, transaction4);
        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        IEnumerable<TransactionSummaryDto> result = await repository.GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate);
        List<TransactionSummaryDto> resultList = result.ToList();

        // Assert
        Assert.That(resultList, Is.Not.Null);
        Assert.Multiple(() =>
        {
            // Check that we have the expected number of items
            // We expect: 
            // - 2 transaction types (across 2 contacts)
            // - PUC Activity summary
            // - In-house lobbyists summary
            Assert.That(resultList, Has.Count.EqualTo(4), "Should return four summary items");

            // Verify regular transaction summaries
            var lobbyingFirmSummary = resultList.FirstOrDefault(r => r.TransactionTypeId == lobbyingFirmPaymentType);

            Assert.That(lobbyingFirmSummary, Is.Not.Null, "Lobbying firm payment summary should be included");
            Assert.That(lobbyingFirmSummary!.Name, Is.EqualTo("Test Organization (201)"), "Contact name should include organization name and ContactFilerId in parentheses");
            Assert.That(lobbyingFirmSummary!.AmountThisPeriod, Is.EqualTo(500), "This period amount for lobbying firm is incorrect");
            Assert.That(lobbyingFirmSummary.CumulativeToDate, Is.EqualTo(900), "Cumulative amount for lobbying firm is incorrect");

            var activityExpenseSummary = resultList.FirstOrDefault(r => r.TransactionTypeId == activityExpenseType);

            Assert.That(activityExpenseSummary, Is.Not.Null, "Activity expense summary should be included");
            Assert.That(activityExpenseSummary!.Name, Is.EqualTo("John Doe (202)"), "Contact name should include first and last name and ContactFilerId in parentheses");
            Assert.That(activityExpenseSummary!.AmountThisPeriod, Is.EqualTo(300), "This period amount for activity expense is incorrect");
            Assert.That(activityExpenseSummary.CumulativeToDate, Is.EqualTo(500), "Cumulative amount for activity expense is incorrect");

            // Verify PUC Activity summary
            TransactionSummaryDto? pucSummary = resultList.FirstOrDefault(r => r.FilingSummaryTypeId == FilingSummaryType.PucActivitySummary.Id);
            Assert.That(pucSummary, Is.Not.Null, "PUC Activity summary should be included");
            Assert.That(pucSummary!.AmountThisPeriod, Is.EqualTo(2000), "PUC Activity This Period amount is incorrect");
            Assert.That(pucSummary.CumulativeToDate, Is.EqualTo(3000), "PUC Activity Cumulative amount is incorrect");
            Assert.That(pucSummary.Name, Is.EqualTo("Total Payments"), "PUC Activity name is incorrect");

            // Verify In-House Lobbyists summary
            TransactionSummaryDto? inHouseSummary = resultList.FirstOrDefault(r => r.FilingSummaryTypeId == FilingSummaryType.PaymentsToInHouseLobbyists.Id);
            Assert.That(inHouseSummary, Is.Not.Null, "In-House Lobbyists summary should be included");
            Assert.That(inHouseSummary!.AmountThisPeriod, Is.EqualTo(1500), "In-House Lobbyists This Period amount is incorrect");
            Assert.That(inHouseSummary.CumulativeToDate, Is.EqualTo(2300), "In-House Lobbyists Cumulative amount is incorrect");
            Assert.That(inHouseSummary.Name, Is.EqualTo("Total Payments"), "In-House Lobbyists name is incorrect");
        });
    }

    [Test]
    public async Task GetTransactionsForFilingSummary_ShouldHandleSpecialCases()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        int filerId = 1001;
        int filingId = 5; // Current filing ID
        DateTime legislativeStartDate = new(2022, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime legislativeEndDate = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);

        // Create filing period
        FilingPeriod filingPeriod = new()
        {
            StartDate = legislativeStartDate,
            EndDate = legislativeEndDate
        };

        // Create current filing (as LobbyistEmployerReport)
        LobbyistEmployerReport currentFiling = new()
        {
            Id = filingId,
            FilerId = filerId,
            StatusId = FilingStatus.Draft.Id, // Draft status (would normally be excluded, but should be included as it's current)
            StartDate = new DateTime(2022, 6, 1, 0, 0, 0, DateTimeKind.Local),
            EndDate = new DateTime(2022, 6, 30, 0, 0, 0, DateTimeKind.Local),
            FilingPeriod = filingPeriod,
            TotalPaymentsPucActivity = new Currency(2500),
            TotalPaymentsToInHouseLobbyists = new Currency(1500)
        };

        // Other filing with PUC activity to check cumulative
        LobbyistEmployerReport otherFiling = new()
        {
            Id = 6,
            FilerId = filerId,
            StatusId = FilingStatus.Accepted.Id,
            StartDate = new DateTime(2022, 5, 1, 0, 0, 0, DateTimeKind.Local),
            EndDate = new DateTime(2022, 5, 31, 0, 0, 0, DateTimeKind.Local),
            FilingPeriod = filingPeriod,
            TotalPaymentsPucActivity = new Currency(1000),
            TotalPaymentsToInHouseLobbyists = new Currency(500)
        };

        // Add filings to context
        context.Set<Filing>().AddRange(currentFiling, otherFiling);
        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        IEnumerable<TransactionSummaryDto> result = await repository.GetTransactionsForFilingSummary(filingId, legislativeStartDate, legislativeEndDate);
        List<TransactionSummaryDto> resultList = result.ToList();

        // Assert
        Assert.Multiple(() =>
        {
            // Check PUC Activity summary
            TransactionSummaryDto? pucSummary = resultList.FirstOrDefault(r => r.FilingSummaryTypeId == FilingSummaryType.PucActivitySummary.Id);
            Assert.That(pucSummary, Is.Not.Null, "PUC Activity summary should be included");
            Assert.That(pucSummary!.AmountThisPeriod, Is.EqualTo(2500), "PUC Activity This Period amount is incorrect");
            Assert.That(pucSummary.CumulativeToDate, Is.EqualTo(3500), "PUC Activity Cumulative amount is incorrect");

            // Check In-House Lobbyists summary
            TransactionSummaryDto? inHouseSummary = resultList.FirstOrDefault(r => r.FilingSummaryTypeId == FilingSummaryType.PaymentsToInHouseLobbyists.Id);
            Assert.That(inHouseSummary, Is.Not.Null, "In-House Lobbyists summary should be included");
            Assert.That(inHouseSummary!.AmountThisPeriod, Is.EqualTo(1500), "In-House Lobbyists This Period amount is incorrect");
            Assert.That(inHouseSummary.CumulativeToDate, Is.EqualTo(2000), "In-House Lobbyists Cumulative amount is incorrect");

            // Verify the current filing (with Draft status) is included
            Assert.That(resultList, Is.Not.Empty, "Result should not be empty when current filing is Draft status");
        });
    }

    [Test]
    public async Task GetTransactionsForFilingSummary_ShouldThrowException_WhenFilingNotFound()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        int nonExistentFilingId = 999;
        DateTime legislativeStartDate = new(2022, 1, 1, 0, 0, 0, DateTimeKind.Local);
        DateTime legislativeEndDate = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Local);

        TransactionRepository repository = new(context);

        // Act & Assert
        KeyNotFoundException? exception = Assert.ThrowsAsync<KeyNotFoundException>(async () =>
            await repository.GetTransactionsForFilingSummary(nonExistentFilingId, legislativeStartDate, legislativeEndDate));

        Assert.That(exception!.Message, Does.Contain($"Filing with ID {nonExistentFilingId} not found"));
    }

    /// <summary>
    /// Verifies that all <see cref="MonetaryType"/> entities can be retrieved from the database
    /// using the repository's <c>GetAllMonetaryTypes</c> method.
    /// </summary>
    /// <returns>A task representing the asynchronous operation of the test.</returns>
    [Test]
    public async Task ShouldGetAllMonetaryTypes()
    {
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();
        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        IEnumerable<MonetaryType> result = await repository.GetAllMonetaryTypes();

        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(2));
    }

    [Test]
    public async Task GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling_Returns_CorrectData()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        long filingId = 1L;
        DateTime legislativeStartDate = new(2024, 1, 1, 0, 0, 0, 0);
        Filing filing = new()
        {
            Id = filingId,
            StatusId = 1,
            FilingPeriod = new FilingPeriod { StartDate = new DateTime(2024, 1, 1, 0, 0, 0, 0) }
        };

        OrganizationContact organizationContact = new() { OrganizationName = "Test Coalition" };
        IndividualContact individualContact = new() { Employer = "DataHouse", FirstName = "John", LastName = "Doe", MiddleName = "Middle", Occupation = "Engineer" };

        List<PaymentReceiveLobbyingCoalition> paymentTransactions =
        [
            new() {
                Id = 1,
                Contact = organizationContact,
                ContactId = 1,
                Amount = new Currency(100),
                FilingTransactions = [new() { FilingId = filingId, Filing = filing }],
            },
            new()
            {
                Id = 2,
                Contact = individualContact,
                ContactId = 1,
                Amount = new Currency(200),
                FilingTransactions = [new() { FilingId = filingId, Filing = filing }],
            },
            new()
            {
                Id = 3,
                Contact = individualContact,
                ContactId = 1,
                Amount = new Currency(200),
                FilingTransactions = [new() { FilingId = filingId, Filing = filing }],
            }
        ];

        context.PaymentReceiveLobbyingCoalition.AddRange(paymentTransactions);

        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        IEnumerable<PaymentReceiveLobbyingCoalition> result = await repository.GetAllPaymentReceivedLobbyingCoalitionTransactionsForFiling(filingId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Count(), Is.EqualTo(3));
    }

    [Test]
    public async Task GetAllActivityExpenseTransactionsForFiling_ReturnsExpectedTransactions()
    {
        // Arrange
        long filingId = 123L;
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        AddressList addressList = new()
        {
            Addresses =
            [
                new()
                {
                    Street = "street",
                    Street2 = "street2",
                    City = "city",
                    State = "CA",
                    Zip = "12345",
                    Country = "US",
                    Type = "home",
                    Purpose = "purpose"
                }
            ]
        };
        OrganizationContact organizationContact = new() { OrganizationName = "Test Coalition", AddressList = addressList };
        IndividualContact individualContact = new() { Employer = "DataHouse", FirstName = "John", LastName = "Doe", MiddleName = "Middle", Occupation = "Engineer", AddressList = addressList };

        ActivityExpense transaction1 = new()
        {
            Id = 1,
            Contact = organizationContact,
            FilingTransactions = [new() { FilingId = filingId }],
            TransactionReportablePersons = [],
            Amount = new Currency(200)
        };

        ActivityExpense transaction2 = new()
        {
            Id = 2,
            Contact = individualContact,
            FilingTransactions = [new() { FilingId = filingId }],
            TransactionReportablePersons = [],
            Amount = new Currency(200)
        };

        ActivityExpense unrelatedTransaction = new()
        {
            Id = 3,
            Contact = individualContact,
            FilingTransactions = [new() { FilingId = 999 }],
            TransactionReportablePersons = [],
            Amount = new Currency(200)
        };

        context.Transactions.AddRange(transaction1, transaction2, unrelatedTransaction);
        _ = await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        IEnumerable<ActivityExpense> result = await repository.GetAllActivityExpenseTransactionsForFiling(filingId);

        // Assert
        List<ActivityExpense> resultList = result.ToList();
        Assert.That(resultList, Has.Count.EqualTo(2));
        Assert.Multiple(() =>
        {
            Assert.That(resultList[0].Id, Is.EqualTo(1));
            Assert.That(resultList[1].Id, Is.EqualTo(2));
            Assert.That(resultList[0].Contact, Is.Not.Null);
            Assert.That(resultList[1].Contact, Is.Not.Null);
            Assert.That(resultList[0].Contact?.AddressList, Is.Not.Null);
            Assert.That(resultList[1].Contact?.AddressList, Is.Not.Null);
        });

    }

    [Test]
    public async Task GetOtherPaymentsCumulativeAmount_ShouldReturnCorrectAmountForContactAndFiling()
    {
        // Arrange
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);
        long contactId = 1L;
        long filingId = 1L;

        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        // Create test data
        OtherPaymentsToInfluence otherPayment = new()
        {
            Id = 1,
            ContactId = contactId,
            Amount = (Currency)1000,
            FilingTransactions =
            [
                new ()
                {
                    Filing = new ()
                    {
                        Id = filingId,
                        FilingPeriod = new FilingPeriod { StartDate = legislativeStartDate.AddDays(1) },
                        StatusId = 1,
                        StartDate = legislativeStartDate.AddDays(1)
                    }
                }
            ]
        };

        context.Transactions.AddRange(otherPayment);

        _ = await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        CumulativeAmountResponse result = await repository.GetOtherPaymentsCumulativeAmountForFilingAndContact(
            filingId, contactId, legislativeStartDate);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.CumulativeAmount, Is.EqualTo(1000));
    }

    [Test]
    public async Task GetOtherPaymentsCumulativeAmount_ShouldSumMultiplePayments()
    {
        // Arrange
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);
        long contactId = 1L;
        long filingId = 1L;

        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
        .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
        .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        // Create test data
        List<OtherPaymentsToInfluence> payments =
        [
            new ()
            {
                Id = 1,
                ContactId = contactId,
                Amount = (Currency)1000,
                FilingTransactions =
                [
                    new ()
                    {
                        Filing = new ()
                        {
                            FilingPeriod = new FilingPeriod { StartDate = legislativeStartDate.AddDays(1) },
                            StatusId = 1
                        }
                    }
                ]
            },
            new ()
            {
                Id = 2,
                ContactId = contactId,
                Amount = (Currency)2000,
                FilingTransactions =
                [
                    new ()
                    {
                        Filing = new ()
                        {
                            FilingPeriod = new FilingPeriod { StartDate = legislativeStartDate.AddDays(2) },
                            StatusId = 1,
                            StartDate = legislativeStartDate.AddDays(2)
                        }
                    }
                ]
            }
        ];

        context.Transactions.AddRange(payments);

        _ = await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        CumulativeAmountResponse result = await repository.GetOtherPaymentsCumulativeAmountForFilingAndContact(
            filingId, contactId, legislativeStartDate);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.CumulativeAmount, Is.EqualTo(2000));
    }

    [Test]
    public async Task GetOtherPaymentsCumulativeAmount_ShouldReturnZeroWhenNoMatches()
    {
        // Arrange
        DateTime legislativeStartDate = new(2023, 1, 1, 0, 0, 0, 0);
        long contactId = 1L;
        long filingId = 1L;

        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        context.Transactions.AddRange();

        _ = await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        CumulativeAmountResponse result = await repository.GetOtherPaymentsCumulativeAmountForFilingAndContact(
            filingId, contactId, legislativeStartDate);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.CumulativeAmount, Is.EqualTo(0));
    }

    [Test]
    public async Task GetLobbyistCampaignContributionTransactionById_ReturnsExpectedTransaction()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        long transactionId = 1L;
        long filingId = 1L;

        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique DB per test
            .Options;

        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        FilingTransaction filingTransaction = new()
        {
            FilingId = filingId,
            TransactionId = transactionId,
        };
        LobbyingCampaignContribution transaction = new()
        {
            Id = transactionId,
            Amount = new Currency(300),
            NonCommitteeRecipientName = "Some Name",
            TransactionDate = date,
            RecipientFiler = null,
            ContributorFiler = null,
            FilingTransactions = [new() { FilingId = filingId }]
        };

        context.Transactions.Add(transaction);
        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        LobbyingCampaignContribution? result = await repository.GetLobbyistCampaignContributionTransactionById(transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(transactionId));
            Assert.That(result.Amount, Is.EqualTo(transaction.Amount));
            Assert.That(result.RecipientFiler, Is.Null);
        });
    }

    [Test]
    public async Task GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId_ReturnsExpectedTransaction()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        long transactionId = 1L;
        long filingId = 1L;

        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique DB per test
            .Options;

        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        FilingTransaction filingTransaction = new()
        {
            FilingId = filingId,
            TransactionId = transactionId,
        };
        PaymentMadeToLobbyingCoalition transaction = new()
        {
            Id = transactionId,
            Amount = new Currency(300),
            TransactionDate = date,
            FilingTransactions = [new() { FilingId = filingId }],
            ContactId = 101
        };

        context.Transactions.Add(transaction);
        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        PaymentMadeToLobbyingCoalition? result = await repository.GetPaymentMadeToLobbyingCoalitionTransactionsByTransactionId(transactionId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Id, Is.EqualTo(transactionId));
            Assert.That(result.Amount, Is.EqualTo(transaction.Amount));
        });
    }

    #region FindAllTransactionsByFiling
    [Test]
    public async Task FindAllTransactionsByFiling_PaymentReceived_ReturnsFilteredTransactions()
    {
        // Arrange
        long filingId = 1L;
        List<PaymentReceived> paymentReceiveds =
        [
            new()
            {
                Id = 1,
                Amount = (Currency)1m,
                Active = true,
                FilingTransactions =
                [
                    new()
                    {
                        Id = 1,
                        FilingId = filingId,
                    }
                ]
            }
        ];

        // Create database context
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        // Save to database
        await context.Set<PaymentReceived>().AddRangeAsync(paymentReceiveds);
        await context.SaveChangesAsync();
        TransactionRepository repository = new(context);

        // Act
        List<PaymentReceived> result = await repository.FindAllTransactionsByFiling<PaymentReceived>(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<List<PaymentReceived>>());
            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result[0].Id, Is.EqualTo(1));
        });
    }

    [Test]
    public async Task FindAllTransactionsByFiling_PaymentMade_WithAgent_ReturnsFilteredTransactions()
    {
        // Arrange
        int filingId = 1;
        List<PaymentMade> paymentMades =
        [
            new()
            {
                Id = 2,
                Amount = (Currency)1m,
                Active = true,
                ExpenditureCodeId = 1,
                ExpenditureCodeDescription = "Description",
                AgentOrIndependentContractorName = "Agent",
                FilingTransactions =
                [
                    new()
                    {
                        Id = 1,
                        FilingId = filingId,
                    }
                ]
            }
        ];
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);

        // Save to database
        await context.Set<PaymentMade>().AddRangeAsync(paymentMades);
        await context.SaveChangesAsync();
        TransactionRepository repository = new(context);

        // Act
        List<PaymentMade> result = await repository.FindAllTransactionsByFiling<PaymentMade>(filingId, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<List<PaymentMade>>());
            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result[0].Id, Is.EqualTo(2));
        });
    }

    [Test]
    public async Task FindAllTransactionsByFiling_PersonReceiving1000_ReturnsFilteredTransactions()
    {
        // Arrange
        int filingId = 1;
        List<PersonReceiving1000OrMore> personReceiving1000 =
        [
            new()
            {
                Id = 2,
                Amount = (Currency)1m,
                Active = true,
                FilingTransactions =
                [
                    new()
                    {
                        Id = 1,
                        FilingId = filingId,
                    }
                ],
                Contact = new IndividualContact
                {
                    Id = 1,
                    FilingContactSummary = new FilingContactSummary
                    {
                        Id = 1,
                        Amount = 1m,
                        FilerContactId = 1,
                        DisclosureFilingId = filingId,
                    }
                }
            }
        ];
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);

        // Save to database
        await context.Set<PersonReceiving1000OrMore>().AddRangeAsync(personReceiving1000);
        await context.SaveChangesAsync();
        TransactionRepository repository = new(context);

        // Act
        List<PersonReceiving1000OrMore> result = await repository.FindAllTransactionsByFiling<PersonReceiving1000OrMore>(filingId, true);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<List<PersonReceiving1000OrMore>>());
            Assert.That(result, Has.Count.EqualTo(1));
            Assert.That(result[0].Id, Is.EqualTo(2));
        });
    }
    #endregion

    #region FindByIdAndFilingId
    [Test]
    public async Task FindByIdAndFilingId_PaymentReceived_ShouldReturnTransaction()
    {
        // Arrange
        long id = 1L;
        long filingId = 1L;
        List<PaymentReceived> paymentReceiveds =
        [
            new()
            {
                Id = id,
                Amount = (Currency)1m,
                Active = true,
                FilingTransactions =
                [
                    new()
                    {
                        Id = 1,
                        FilingId = filingId,
                    }
                ]
            }
        ];

        // Create database context
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDB_{Guid.NewGuid()}")
            .Options;
        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        // Save to database
        await context.Set<PaymentReceived>().AddRangeAsync(paymentReceiveds);
        await context.SaveChangesAsync();
        TransactionRepository repository = new(context);

        // Act
        PaymentReceived? result = await repository.FindByIdAndFilingId<PaymentReceived>(id, filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.InstanceOf<PaymentReceived>());
            Assert.That(result?.Id, Is.EqualTo(id));
        });
    }
    #endregion

    [Test]
    public async Task GetLobbyingAdvertisementTransactionByFilingId_ReturnsExpectedTransaction()
    {
        // Arrange
        var date = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        long transactionId = 1L;
        long filingId = 1L;

        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique DB per test
            .Options;

        await using DatabaseContext context = new(options);
        await context.Database.EnsureCreatedAsync();

        FilingTransaction filingTransaction = new()
        {
            FilingId = filingId,
            TransactionId = transactionId,
        };
        LobbyingAdvertisement transaction = new()
        {
            Id = transactionId,
            Amount = new Currency(300),
            PublicationDate = date,
            DistributionMethodId = 1,
            LegislatorId = null,
            DistributionMethodDescription = "Description",
            AdditionalInformation = null,
            FilingTransactions = [filingTransaction]
        };

        context.Transactions.Add(transaction);
        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        LobbyingAdvertisement? result = await repository.GetLobbyingAdvertisementTransactionByFilingId(filingId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result!.Amount, Is.EqualTo(transaction.Amount));
        });
    }

    [Test]
    public void GetContactNameWithId_ShouldReturnEmptyString_WhenContactIsNull()
    {
        // Arrange
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        var context = new DatabaseContext(options);
        var repository = new TransactionRepository(context);

        var methodInfo = typeof(TransactionRepository).GetMethod("GetContactNameWithId",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        // Act
        var result = methodInfo!.Invoke(null, new object?[] { null }) as string;

        // Assert
        Assert.That(result, Is.EqualTo(string.Empty), "GetContactNameWithId should return empty string when contact is null");
    }

    [Test]
    public async Task GetAllByFilingId_ReturnsExpectedTransactions()
    {
        // Arrange
        var filingId = 123L;
        var otherFilingId = 456L;

        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        var context = new DatabaseContext(options);
        await context.Database.EnsureCreatedAsync();

        // Create filing for the test
        var filing = new Filing() { Id = filingId, StatusId = 1 };
        var otherFiling = new Filing() { Id = otherFilingId, StatusId = 1 };

        // Create test transactions with different FilingTransactions
        var transaction1 = new InKindContribution()
        {
            Id = 1,
            Amount = new Currency(100),
            TransactionDate = DateTime.UtcNow,
            FilingTransactions = new List<FilingTransaction> { new() { FilingId = filingId, TransactionId = 1 } },
            ActionsLobbied = new List<ActionsLobbied> { new() { TransactionId = 1, BillId = 10 } }!
        };

        var transaction2 = new InKindContribution()
        {
            Id = 2,
            Amount = new Currency(200),
            TransactionDate = DateTime.UtcNow,
            FilingTransactions = new List<FilingTransaction> { new() { FilingId = filingId, TransactionId = 2 } },
            ActionsLobbied = new List<ActionsLobbied> { new() { TransactionId = 2, AgencyId = 20 } }!
        };

        var transaction3 = new InKindContribution()
        {
            Id = 3,
            Amount = new Currency(300),
            TransactionDate = DateTime.UtcNow,
            FilingTransactions = new List<FilingTransaction> { new() { FilingId = otherFilingId, TransactionId = 3 } }
        };

        context.Set<Filing>().AddRange(filing, otherFiling);
        context.Transactions.AddRange(transaction1, transaction2, transaction3);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetAllByFilingId(filingId);
        var resultList = result.ToList();

        // Assert
        Assert.That(resultList, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(resultList, Has.Count.EqualTo(2), "Should only return transactions with the specified filing ID");

            Assert.That(resultList.Any(t => t.Id == 1), Is.True, "Transaction 1 should be included");
            Assert.That(resultList.Any(t => t.Id == 2), Is.True, "Transaction 2 should be included");
            Assert.That(resultList.Any(t => t.Id == 3), Is.False, "Transaction 3 should not be included");
        });
    }

    [Test]
    public async Task ShouldGetAllLobbyistEmployerCampaignContributionTransactionsForFiling()
    {
        // Arrange
        var filingId = 1L;
        var otherFilingId = 2L;

        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}") // Unique instance for isolation
            .Options;
        var context = new DatabaseContext(options);
        await context.Database.EnsureCreatedAsync();

        // Create test data
        var recipient1 = new Filer
        {
            Id = 101,
            CurrentRegistrationId = 201
        };

        var recipient2 = new Filer
        {
            Id = 102,
            CurrentRegistrationId = 202
        };

        // Create transactions with different dates
        var transaction1 = new LobbyingCampaignContribution
        {
            Id = 1,
            RecipientFilerId = 101,
            RecipientFiler = recipient1,
            Amount = new Currency(100),
            TransactionDate = DateTime.UtcNow.AddDays(-1),
            FilingTransactions = new List<FilingTransaction>
        {
            new FilingTransaction { FilingId = filingId, TransactionId = 1 }
        }
        };

        var transaction2 = new LobbyingCampaignContribution
        {
            Id = 2,
            RecipientFilerId = 102,
            RecipientFiler = recipient2,
            Amount = new Currency(200),
            TransactionDate = DateTime.UtcNow,
            FilingTransactions = new List<FilingTransaction>
        {
            new FilingTransaction { FilingId = filingId, TransactionId = 2 }
        }
        };

        // Transaction for a different filing - should not be included in results
        var transaction3 = new LobbyingCampaignContribution
        {
            Id = 3,
            RecipientFilerId = 101,
            RecipientFiler = recipient1,
            Amount = new Currency(300),
            TransactionDate = DateTime.UtcNow.AddDays(-2),
            FilingTransactions = new List<FilingTransaction>
        {
            new FilingTransaction { FilingId = otherFilingId, TransactionId = 3 }
        }
        };

        context.Filers.AddRange(recipient1, recipient2);
        context.Transactions.AddRange(transaction1, transaction2, transaction3);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetAllLobbyistEmployerCampaignContributionTransactionsForFiling(filingId);
        var resultList = result.ToList();

        // Assert
        Assert.That(resultList, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(resultList, Has.Count.EqualTo(2), "Should only return transactions for the specified filing ID");
            Assert.That(resultList[0].Id, Is.EqualTo(2), "First transaction should be the most recent");
            Assert.That(resultList[1].Id, Is.EqualTo(1), "Second transaction should be the older one");
            Assert.That(resultList[0].RecipientFiler, Is.Not.Null, "RecipientFiler should be included");
            Assert.That(resultList[1].RecipientFiler, Is.Not.Null, "RecipientFiler should be included");
            Assert.That(resultList.Any(t => t.Id == 3), Is.False, "Transaction for other filing should not be included");
        });
    }

    #region FindAllFilingTransactionsByFilingIdAsync
    [Test]
    public async Task FindAllFilingTransactionsByFilingIdAsync_ShouldReturnResult()
    {
        // Arrange
        var filingId = 1L;
        var filingTransaction = new List<FilingTransaction>
        {
            new()
            {
                Id = 1,
                FilingId = filingId,
                Transaction = new PaymentReceived
                {
                    Id = 1,
                    Amount = (Currency)0m,
                },
            }
        };

        // Initialize db context
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        var context = new DatabaseContext(options);

        // Seed data
        context.FilingsTransactions.AddRange(filingTransaction);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.FindAllFilingTransactionsByFilingId(filingId);

        // Arrange
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<FilingTransaction>>());
        });
    }
    #endregion

    [Test]
    public async Task ShouldReturnCumulativeAmountForPaymentMadeToLobbyingFirms()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        var contactId = 123L;
        var legislativeStartDate = DateTime.Parse("2022-01-01", CultureInfo.InvariantCulture);

        context.Set<PaymentMadeToLobbyingFirms>().Add(new PaymentMadeToLobbyingFirms
        {
            ContactId = contactId,
            Amount = (Currency)1000,
            FilingTransactions = new List<FilingTransaction>
            {
                new()
                {
                    Filing = new Filing
                    {
                        StatusId = 1,
                        FilingPeriod = new FilingPeriod { StartDate = legislativeStartDate.AddDays(1) },
                        StartDate = legislativeStartDate.AddDays(1)
                    }
                }
            }
        });

        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetCumulativeAmountPaymentMadeToLobbyingFirms(
            new List<long> { contactId }, legislativeStartDate);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result[0].ContactId, Is.EqualTo(contactId));
            Assert.That(result[0].CumulativeAmount, Is.EqualTo(1000));
        });
    }
    [Test]
    public async Task ShouldReturnCumulativeAmountForPaymentReceiveLobbyingCoalition()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        // Arrange
        var contactId = 456L;
        var legislativeStartDate = DateTime.Parse("2022-01-01", CultureInfo.InvariantCulture);

        context.Set<PaymentReceiveLobbyingCoalition>().Add(new PaymentReceiveLobbyingCoalition
        {
            ContactId = contactId,
            Amount = (Currency)1500,
            FilingTransactions = new List<FilingTransaction>
        {
            new()
            {
                Filing = new Filing
                {
                    StatusId = 1,
                    FilingPeriod = new FilingPeriod { StartDate = legislativeStartDate.AddDays(1) },
                    StartDate = legislativeStartDate.AddDays(1)
                }
            }
        }
        });

        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetCumulativeAmountPaymentReceiveLobbyingCoalition(
            new List<long> { contactId }, legislativeStartDate);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Has.Count.EqualTo(1));
        Assert.Multiple(() =>
        {
            Assert.That(result[0].ContactId, Is.EqualTo(contactId));
            Assert.That(result[0].CumulativeAmount, Is.EqualTo(1500));
        });
    }

    [Test]
    public async Task GetCumulativeAmountPaymentMadeToLobbyingFirms_ShouldReturnEmptyListWhenContactIdsIsNull()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var repository = new TransactionRepository(context);

        var result = await repository.GetCumulativeAmountPaymentMadeToLobbyingFirms(null, DateTime.UtcNow);

        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetCumulativeAmountPaymentReceiveLobbyingCoalition_ShouldReturnEmptyListWhenContactIdsIsNull()
    {
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();

        var repository = new TransactionRepository(context);

        var result = await repository.GetCumulativeAmountPaymentReceiveLobbyingCoalition(null, DateTime.UtcNow);

        Assert.That(result, Is.Empty);
    }

    [Test]
    public async Task GetPaymentReceivedLobbyingCoalitionTransactionById_ValidTransactionId_ReturnsTransaction()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        long transactionId = 789;
        long contactId = 456;
        var transaction = new PaymentReceiveLobbyingCoalition()
        {
            Id = transactionId,
            ContactId = contactId,
            Amount = (Currency)10,
            Contact = new IndividualContact() { Id = contactId },
            FilingTransactions = new List<FilingTransaction>
            {
                new() { TransactionId = transactionId, Filing = new Filing { StatusId = 1 } }
            }
        };

        context.Set<PaymentReceiveLobbyingCoalition>().Add(transaction);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(transactionId));
            Assert.That(result.ContactId, Is.EqualTo(contactId));
            Assert.That(result.Contact, Is.Not.Null);
            Assert.That(result!.Contact!.Id, Is.EqualTo(contactId));
            Assert.That(result.FilingTransactions, Has.Count.EqualTo(1));
            Assert.That(result.FilingTransactions.First().TransactionId, Is.EqualTo(transactionId));
        });
    }

    [Test]
    public async Task GetPaymentReceivedLobbyingCoalitionTransactionById_ReturnsNull()
    {
        // Arrange
        using var factory = new DatabaseContextFactory();
        using var context = await factory.CreateContext();
        long transactionId = 1;

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetPaymentReceivedLobbyingCoalitionTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Null);
    }

    #region SumTransactionAmountsByFilingsAndContact
    [Test]
    public async Task SumTransactionAmountsByFilingsAndContact_ValidRequest_ShouldReturnResult()
    {
        // Arrange
        var filingIds = new List<long>
        {
            1L,
            2L
        };
        var contactId = 1L;
        var transactionTypeId = TransactionType.PersonReceiving1000OrMore.Id;
        var filingTransaction = new List<FilingTransaction>
        {
            new()
            {
                Id = 1,
                FilingId = 1L,
                Transaction = new PersonReceiving1000OrMore
                {
                    Id = 1,
                    ContactId = contactId,
                    Amount = (Currency)1m,
                },
            },
            new()
            {
                Id = 2,
                FilingId = 1L,
                Transaction = new PersonReceiving1000OrMore
                {
                    Id = 2,
                    ContactId = contactId,
                    Amount = (Currency)2m,
                },
            },
            new()
            {
                Id = 3,
                FilingId = 1L,
                Transaction = new PersonReceiving1000OrMore
                {
                    Id = 3,
                    ContactId = contactId,
                    Amount = (Currency)2m,
                    Active = false
                },
            }
        };

        // Initialize db context
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        var context = new DatabaseContext(options);

        // Seed data
        context.FilingsTransactions.AddRange(filingTransaction);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.SumTransactionAmountsByFilingsAndContact(filingIds, contactId, transactionTypeId);

        // Arrange
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.EqualTo(3m));
        });
    }
    #endregion

    #region GetSmoCampaignStatementPaymentRecievedByIdAsync
    [Test]
    public async Task GetSmoCampaignStatementPaymentRecievedByIdAsync_ShouldReturnResult()
    {
        // Arrange
        var filingId = 1L;
        var payments = new List<PaymentReceived>
        {
            new()
            {
                Id = 1,
                Amount = new Currency(100),
                DisclosureStanceOnCandidate = new()
                {
                    Position = "Support",
                    CreatedBy = 0L,
                    ModifiedBy = 0L,
                }
            }
        };

        // Initialize db context
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        var context = new DatabaseContext(options);

        // Seed data
        context.PaymentReceived.AddRange(payments);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetSmoCampaignStatementPaymentRecievedByIdAsync(filingId);

        // Arrange
        Assert.That(result, Is.InstanceOf<PaymentReceived>());
    }
    #endregion

    #region GetMatchingTransactionByFilingId
    [Test]
    public async Task GetMatchingTransactionByFilingId_ShouldReturnResult()
    {
        // Arrange
        var filingId = 1L;
        var payments = new List<PaymentReceived>
        {
            new()
            {
                Id = 1L,
                FilingTransactions = new()
                {
                    new()
                    {
                        Id = 1L,
                        FilingId = filingId,
                    }
                },
                ContactId = 1L,
                Amount = new Currency(100),
                DisclosureStanceOnCandidate = new()
                {
                    Position = "Support",
                    CreatedBy = 0L,
                    ModifiedBy = 0L,
                    SubjectId = 1L,
                    FilingContactSummary = new()
                    {
                        Id = 1L,
                        Amount = 200,
                        PreviouslyUnitemizedAmount = 100,
                        FilerContactId = 1L,
                    }
                }
            }
        };
        List<long> validFilingIds = new() { 1L };

        // Initialize db context
        var options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
            .Options;
        var context = new DatabaseContext(options);

        // Seed data
        context.PaymentReceived.AddRange(payments);
        await context.SaveChangesAsync();

        var repository = new TransactionRepository(context);

        // Act
        var result = await repository.GetMatchingTransactionByFilingId(validFilingIds, 1L, "Support", 1L, null);

        // Arrange
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Empty);
            Assert.That(result, Is.InstanceOf<List<PaymentReceived>>());
        });
    }
    #endregion

    [Test]
    public async Task ShouldGetEndOfSessionLobbyingTransactionById_WhenTransactionExists()
    {
        // Arrange
        DbContextOptions<DatabaseContext> options = new DbContextOptionsBuilder<DatabaseContext>()
            .UseInMemoryDatabase(databaseName: $"TestDb_{Guid.NewGuid()}")
            .Options;

        await using DatabaseContext context = new(options);

        long transactionId = 1;
        long filingId = 100;
        long contactFilerId = 555;
        string organizationName = "Test Firm";

        OrganizationContact contact = new()
        {
            Id = 10,
            ContactFilerId = contactFilerId,
            OrganizationName = organizationName,
            Filer = new Filer { CurrentRegistrationId = 9999 }
        };

        Filing filing = new()
        {
            Id = filingId,
            StatusId = 1
        };

        EndOfSessionLobbying transaction = new()
        {
            Id = transactionId,
            Amount = new Currency(2500),
            DateLobbyingFirmHired = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
            Contact = contact,
            ContactId = contact.Id,
            FilingTransactions = new List<FilingTransaction>
            {
                new() { Filing = filing, FilingId = filing.Id, TransactionId = transactionId }
            },
            ActionsLobbied = new List<ActionsLobbied> { new() { TransactionId = 1, BillId = 10 } }!
        };

        context.Add(transaction);
        await context.SaveChangesAsync();

        TransactionRepository repository = new(context);

        // Act
        EndOfSessionLobbyingDto? result = await repository.GetAllEndOfSessionLobbyingTransactionById(transactionId);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result!.Id, Is.EqualTo(transactionId));
            Assert.That(result.Amount, Is.EqualTo(2500));
            Assert.That(result.DateLobbyingFirmHired, Is.EqualTo(new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)));
            Assert.That(result.FilingId, Is.EqualTo(filingId));
            Assert.That(result.FirmName, Is.EqualTo(organizationName));
            Assert.That(result.FilerId, Is.EqualTo(contactFilerId));
            Assert.That(result.RegistrationId, Is.EqualTo(9999));
            Assert.That(result.ActionsLobbied, Is.Not.Empty);
        });
    }


}
