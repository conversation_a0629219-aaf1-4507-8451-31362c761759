using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Moq;
using SOS.CalAccess.FilerPortal.Controllers;
using SOS.CalAccess.FilerPortal.ControllerServices;
using SOS.CalAccess.FilerPortal.Generated;
using FilingType = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;

namespace SOS.CalAccess.FilerPortal.Tests.Controllers;

[TestFixture]
public class TemporaryDashboardControllerTests
{
    private DisclosureTemporaryDashboardController _controller;
    private Mock<IDisclosureCtlSvc> _disclosureCtlSvcMock;
    private Mock<ITempDataDictionaryFactory> _tempDataFactoryMock;
    private DateTime _dateNow;

    [SetUp]
    public void SetUp()
    {
        var tempDataMock = new Mock<ITempDataDictionary>();
        var httpContextMock = new Mock<HttpContext>();
        _tempDataFactoryMock = new Mock<ITempDataDictionaryFactory>();
        _tempDataFactoryMock.Setup(x => x.GetTempData(httpContextMock.Object)).Returns(tempDataMock.Object);
        tempDataMock.Setup(td => td["ParentId"]).Returns("");
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);

        _disclosureCtlSvcMock = new Mock<IDisclosureCtlSvc>(); ;
        _controller = new DisclosureTemporaryDashboardController(_disclosureCtlSvcMock.Object)
        {
            TempData = tempDataMock.Object
        };
    }

    [TearDown]
    public void TearDown()
    {
        _controller.Dispose();
    }

    [Test]
    public async Task Index_ShouldReturnView_WithExpectedModel()
    {
        // Arrange
        var filingReports = new List<FilingReportGridDto>()
        {
            new (_dateNow, 1, true, "Report 1", "02/03/2025 - 05/05/2026", "Pending", 1)
        };

        _disclosureCtlSvcMock.Setup(x => x.GetDisclosureFilingReports(It.IsAny<CancellationToken>())).ReturnsAsync(filingReports);

        // Act
        var result = await _controller.Index() as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
    }

    [Test]
    public async Task Delete_WithValidId_ShouldCallCancelFiling_AndReturnOk()
    {
        // Arrange
        long filingId = 123;
        var filingsApiMock = new Mock<IFilingsApi>();
        _ = filingsApiMock.Setup(x => x.CancelFiling(filingId, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.Delete(filingId, filingsApiMock.Object) as StatusCodeResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.StatusCode, Is.EqualTo(200));
        filingsApiMock.Verify(x => x.CancelFiling(filingId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task Delete_WithInvalidModelState_ShouldReturnBadRequest()
    {
        // Arrange
        long filingId = 123;
        var filingsApiMock = new Mock<IFilingsApi>();
        _controller.ModelState.AddModelError("test", "test error");

        // Act
        var result = await _controller.Delete(filingId, filingsApiMock.Object) as BadRequestObjectResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.StatusCode, Is.EqualTo(400));
        filingsApiMock.Verify(x => x.CancelFiling(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task Edit_With470_ShouldRedirectToForm470Controller()
    {
        // Arrange
        long filingId = 123;
        var filing = new FilingItemResponse(
            "Amendment",
            _dateNow,
            456,
            "Test Filer",
            1,
            "Draft",
            "OfficeHolderCandidateShortForm",
            filingId,
            1,
            null,
            1,
            _dateNow.AddMonths(-1),
            1,
            null,
            1,
            1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);
        // Act
        var result = await _controller.Edit(filingId, 1) as RedirectToActionResult;
        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Edit"));
            Assert.That(result.ControllerName, Is.EqualTo("Form470"));
        });
    }

    [Test]
    public async Task Edit_WithSmo_ShouldRedirectToSmoCampaignStatementController()
    {
        // Arrange
        long filingId = 123;
        var filing = new FilingItemResponse(
            "Amendment",
            _dateNow,
            456,
            "Test Filer",
            1,
            "Draft",
            "SlateMailerOrganization",
            1,
            filingId,
            null,
            1,
            _dateNow.AddMonths(-1),
            1,
            null,
            1,
            1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);
        // Act
        var result = await _controller.Edit(filingId) as RedirectToActionResult;
        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Edit"));
            Assert.That(result.ControllerName, Is.EqualTo("SmoCampaignStatement"));
        });
    }

    [Test]
    public async Task Edit_WithOfficeHolderCandidateSupplement_ShouldRedirectToForm470SController()
    {
        // Arrange
        long filingId = 123;
        var filing = new FilingItemResponse(
            "OfficeHolderCandidateSupplement",
            _dateNow,
            456,
            "Test Filer",
            1,
            "Draft",
            "OfficeHolderCandidateSupplement",
            1,
            filingId,
            null,
            1,
            _dateNow.AddMonths(-1),
            1,
            null,
            1,
            1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Edit(filingId) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.RouteValues, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Edit"));
            Assert.That(result.ControllerName, Is.EqualTo("Form470S"));
            Assert.That(result.RouteValues["id"], Is.EqualTo(filingId));
        });
    }

    [Test]
    public async Task Edit_WithValidId_ShouldGetFilingById_AndRedirectToDisclosureIndex()
    {
        // Arrange
        long filingId = 123;
        var filing = new FilingItemResponse(
            id: 123,
            endDate: _dateNow,
            filerId: 456,
            parentId: null,
            startDate: _dateNow.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "Test Filing Type",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Edit(filingId) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("Disclosure"));
            Assert.That(result.RouteValues, Is.Not.Null);
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(filing.FilerId));
            Assert.That(result.RouteValues["filingId"], Is.EqualTo(filing.Id));
            Assert.That(result.RouteValues["filingStatus"], Is.EqualTo(filing.Status));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(filing.FilingType));
        });

        _disclosureCtlSvcMock.Verify(x => x.GetFilingById(filingId, cancellationToken), Times.Once);
    }

    [Test]
    public async Task Edit_WithValidId_ShouldGetFilingByIdWithParentId_AndRedirectToAmendDisclosureIndex()
    {
        // Arrange
        long filingId = 123;
        var filing = new FilingItemResponse(
            "Amendment",
            DateTime.UtcNow,
            456,
            "Test Filer",
            1,
            "Draft",
            "Form 700",
            filingId,
            1,
            1,
            1,
            _dateNow.AddMonths(-1),
            1,
            null,
            1,
            1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Edit(filingId) as RedirectToActionResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Index"));
            Assert.That(result.ControllerName, Is.EqualTo("AmendDisclosure"));
            Assert.That(result.RouteValues, Is.Not.Null);
            Assert.That(result.RouteValues!["filerId"], Is.EqualTo(filing.FilerId));
            Assert.That(result.RouteValues["filingId"], Is.EqualTo(filing.Id));
            Assert.That(result.RouteValues["filingStatus"], Is.EqualTo(filing.Status));
            Assert.That(result.RouteValues["reportType"], Is.EqualTo(filing.FilingType));
        });

        _disclosureCtlSvcMock.Verify(x => x.GetFilingById(filingId, cancellationToken), Times.Once);
    }

    [Test]
    public async Task Edit_WithInvalidModelState_ShouldReturnNotFound()
    {
        // Arrange
        long filingId = 123;
        _controller.ModelState.AddModelError("test", "test error");

        // Act
        var result = await _controller.Edit(filingId) as NotFoundResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        _disclosureCtlSvcMock.Verify(x => x.GetFilingById(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
    }
    #region Amend
    [Test]
    public async Task Amend_WithForm470_ShouldRedirectToForm470Controller()
    {
        // Arrange
        long filingId = 123;
        var filing = new FilingItemResponse(
            "Amendment",
            _dateNow,
            456,
            "Test Filer",
            1,
            "Draft",
            "OfficeHolderCandidateShortForm",
            filingId,
            1,
            null,
            1,
            _dateNow.AddMonths(-1),
            1,
            null,
            1,
            1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);
        // Act
        var result = await _controller.Amend(filingId) as RedirectToActionResult;
        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result.ActionName, Is.EqualTo("Amend"));
            Assert.That(result.ControllerName, Is.EqualTo("Form470"));
        });
    }

    [Test]
    public async Task Amend_InvalidModelState_ReturnNotFound()
    {
        // Arrange
        long filingId = 123;
        _controller.ModelState.AddModelError("Key", "Error");

        // Act
        var result = await _controller.Amend(filingId);
        //Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<NotFoundResult>());
            _disclosureCtlSvcMock.Verify(x => x.GetFilingById(It.IsAny<long>(), It.IsAny<CancellationToken>()), Times.Never);
        });
    }

    [Test]
    public async Task Amend_Default_ReturnView()
    {
        // Arrange

        long filingId = 123;
        var filing = new FilingItemResponse(
            id: 123,
            endDate: _dateNow,
            filerId: 456,
            parentId: null,
            startDate: _dateNow.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: "Test Filing Type",
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Amend(filingId) as ViewResult;

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.InstanceOf<ViewResult>());

        });
        _disclosureCtlSvcMock.Verify(x => x.GetFilingById(filingId, cancellationToken), Times.Once);

    }

    [Test]
    public async Task Amend_Report72h_RedirectsToCreateFilingAmendment_Report72h()
    {
        // Arrange

        long filingId = 123;
        var filing = new FilingItemResponse(
            id: 123,
            endDate: _dateNow,
            filerId: 456,
            parentId: null,
            startDate: _dateNow.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.Report72h.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Amend(filingId);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("CreateFilingAmendment"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Filing"));
            Assert.That(redirect?.RouteValues?["parentId"], Is.EqualTo(filingId));
            Assert.That(redirect?.RouteValues?["reportType"], Is.EqualTo(CalAccess.Models.FilerDisclosure.Filings.FilingType.Report72h.Name));
        });
    }

    [Test]
    public async Task Amend_Report48h_RedirectsToCreateFilingAmendment_Report48h()
    {
        // Arrange
        long filingId = 456;
        var filing = new FilingItemResponse(
            id: filingId,
            endDate: _dateNow,
            filerId: 789,
            parentId: null,
            startDate: _dateNow.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.Report48h.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Amend(filingId);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("CreateFilingAmendment"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Filing"));
            Assert.That(redirect?.RouteValues?["parentId"], Is.EqualTo(filingId));
            Assert.That(redirect?.RouteValues?["reportType"], Is.EqualTo(CalAccess.Models.FilerDisclosure.Filings.FilingType.Report48h.Name));
        });
    }

    [Test]
    public async Task Amend_Lobbyist_RedirectsToCreateFilingAmendment_Lobbyist()
    {
        // Arrange
        long filingId = 123;
        var filing = new FilingItemResponse(
            id: filingId,
            endDate: DateTime.Now,
            filerId: 456,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Amend(filingId);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("CreateFilingAmendment"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Filing"));
            Assert.That(redirect.RouteValues?["parentId"], Is.EqualTo(filingId));
            Assert.That(redirect.RouteValues?["reportType"], Is.EqualTo(FilingType.LobbyistReport.Name));
        });
    }

    [Test]
    public async Task Amend_LobbyistEmployer_RedirectsToCreateFilingAmendment_LobbyistEmployer()
    {
        // Arrange
        long filingId = 456;
        var filing = new FilingItemResponse(
            id: filingId,
            endDate: DateTime.Now,
            filerId: 789,
            parentId: null,
            startDate: DateTime.Now.AddMonths(-1),
            status: 2,
            filerName: "Test Filer",
            submittedDate: null,
            version: 1,
            filingType: FilingType.LobbyistEmployerReport.Name,
            filingStatus: "Test Filing Status",
            amendmentExplanation: string.Empty,
            legislativeSessionId: 1,
            totalPaymentsPucActivity: 1,
            filingPeriodId: 1,
            filingTypeId: 1
        );

        var cancellationToken = CancellationToken.None;
        _ = _disclosureCtlSvcMock
            .Setup(x => x.GetFilingById(filingId, cancellationToken))
            .ReturnsAsync(filing);

        // Act
        var result = await _controller.Amend(filingId);

        // Assert
        var redirect = result as RedirectToActionResult;
        Assert.That(redirect, Is.Not.Null);
        Assert.Multiple(() =>
        {
            Assert.That(redirect.ActionName, Is.EqualTo("CreateFilingAmendment"));
            Assert.That(redirect.ControllerName, Is.EqualTo("Filing"));
            Assert.That(redirect.RouteValues?["parentId"], Is.EqualTo(filingId));
            Assert.That(redirect.RouteValues?["reportType"], Is.EqualTo(FilingType.LobbyistEmployerReport.Name));
        });
    }
    #endregion
}
