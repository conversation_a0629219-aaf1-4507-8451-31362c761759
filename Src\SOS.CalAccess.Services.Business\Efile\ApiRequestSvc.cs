using SOS.CalAccess.Data.Efile;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.Efile;
using SOS.CalAccess.Services.Business.Efile.Model;

namespace SOS.CalAccess.Services.Business.Efile;

/// <inheritdoc />
public class ApiRequestSvc : IApiRequestSvc
{
    private readonly IApiRequestRepository _repository;
    private readonly IDateTimeSvc _dateTimeSvc;

    public ApiRequestSvc(IApiRequestRepository repository, IDateTimeSvc dateTimeSvc)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _dateTimeSvc = dateTimeSvc ?? throw new ArgumentNullException(nameof(dateTimeSvc));
    }

    /// <inheritdoc />
    public async Task<ApiRequest> CreateNewApiRequest(ApiRequest request)
    {
        request.ApiRequestStatusId = (int)ApiRequestStatusNames.Received;
        request.ReceivedAt = _dateTimeSvc.GetCurrentDateTime();
        return await _repository.Create(request);
    }

    /// <inheritdoc />
    public Task<ApiRequest> GetRequestStatus(int submissionId)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<ApiRequest> InvalidateRequest(int requestId, List<ApiError> errors)
    {
        var apiRequestStatusId = (int)ApiRequestStatusNames.RejectedForSchemaViolation;
        return await _repository.UpdateStatusOfApiRequests(requestId, apiRequestStatusId, errors);
    }

    /// <inheritdoc />
    public async Task<ApiRequest> QueueApiRequestForProcessing(int requestId)
    {
        var status = (int)ApiRequestStatusNames.Queued;
        return await _repository.UpdateStatusOfApiRequests(requestId, status, new List<ApiError>());
    }

    /// <inheritdoc />
    public Task<List<ApiRequest>> SearchRequests(ApiRequestSearchCriteria searchCriteria)
    {
        throw new NotImplementedException();
    }

    /// <inheritdoc />
    public async Task<ApiRequest> ProcessRequest(int requestId)
    {
        var status = (int)ApiRequestStatusNames.Processing;
        return await _repository.UpdateStatusOfApiRequests(requestId, status, new List<ApiError>());
    }

    /// <inheritdoc />
    public async Task<ApiRequest> HoldRequestForReview(int requestId, List<ApiError> message)
    {
        var status = (int)ApiRequestStatusNames.HeldForReview;
        return await _repository.UpdateStatusOfApiRequests(requestId, status, message);
    }

    /// <inheritdoc />
    public async Task<ApiRequest> RejectRequestForBusinessRuleViolation(int requestId, List<ApiError> errors)
    {
        var status = (int)ApiRequestStatusNames.RejectedForBusinessRuleViolation;
        return await _repository.UpdateStatusOfApiRequests(requestId, status, errors);
    }

    /// <inheritdoc />
    public async Task<ApiRequest> RejectRequestForSystemError(int requestId, List<ApiError> errors)
    {
        var status = (int)ApiRequestStatusNames.SystemError;
        return await _repository.UpdateStatusOfApiRequests(requestId, status, errors);
    }

    /// <inheritdoc />
    public async Task<ApiRequest> AcceptRequest(int requestId, string filingId)
    {
        var status = (int)ApiRequestStatusNames.Accepted;
        await _repository.UpdateFilingIdOfApiRequests(requestId, filingId);
        return await _repository.UpdateStatusOfApiRequests(requestId, status, new List<ApiError>());
    }

    /// <inheritdoc />
    public Task<APISubmissionDetailDto> GetSubmissionDetail(int submissionId)
    {
        throw new NotImplementedException();
    }
}
