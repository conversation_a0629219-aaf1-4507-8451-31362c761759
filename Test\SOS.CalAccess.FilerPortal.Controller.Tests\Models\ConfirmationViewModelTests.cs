using SOS.CalAccess.FilerPortal.Models.Registrations;
using SOS.CalAccess.FilerPortal.Models.SharedModels;
using SOS.CalAccess.UI.Common.Enums;

namespace SOS.CalAccess.FilerPortal.Tests.Models;

[TestFixture]
[TestOf(nameof(ConfirmationViewModel))]
internal sealed class ConfirmationViewModelTests : IDisposable
{
    private ConfirmationViewModel _viewModel;
    private PendingItemSharedViewModel _pendingItemModel;
    private DateTime _dateNow;

    // Setup method to initialize test data
    [SetUp]
    public void SetUp()
    {
        _dateNow = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local);
        _viewModel = new ConfirmationViewModel
        {
            Id = 1,
            PendingItems = new List<PendingItemSharedViewModel>
            { },
            ExecutedOn = _dateNow
        };
        _pendingItemModel = new PendingItemSharedViewModel
        {
            Item = "Item",
            Status = "Status"
        };
    }

    // Teardown method to clean up after tests
    [TearDown]
    public void TearDown()
    {
        _viewModel = null;
        _pendingItemModel = null;
    }

    // Test the PendingItems property
    [Test]
    public void ConfirmationViewModel_Should_SetAndGet_PendingItems()
    {
        // Arrange
        var expectedItems = new List<PendingItemSharedViewModel>
        { };

        // Act
        _viewModel.PendingItems = expectedItems;

        // Assert
        Assert.That(_viewModel.PendingItems, Is.EqualTo(expectedItems));
    }

    // Test the ExecutedOn property
    [Test]
    public void ConfirmationViewModel_Should_SetAndGet_ExecutedOn()
    {
        // Arrange
        var expectedExecutedOn = _dateNow.AddMinutes(-10);

        // Act
        _viewModel.ExecutedOn = expectedExecutedOn;

        // Assert
        Assert.That(_viewModel.ExecutedOn, Is.EqualTo(expectedExecutedOn));
    }

    // Test handling of null or empty PendingItems
    [Test]
    public void ConfirmationViewModel_Should_Handle_EmptyPendingItems()
    {
        // Arrange
        _viewModel.PendingItems = new List<PendingItemSharedViewModel>();

        // Act & Assert
        Assert.That(_viewModel.PendingItems, Is.Empty);
    }

    // Test for default ExecutedOn (if applicable)
    [Test]
    public void ConfirmationViewModel_Should_Handle_DefaultExecutedOn()
    {
        // Act
        var defaultExecutedOn = _viewModel.ExecutedOn;

        // Assert
        Assert.That(defaultExecutedOn, Is.Not.EqualTo(default(DateTime)));
    }

    // Test the PendingItemSharedViewModel Item property
    [Test]
    public void PendingItem_Should_SetAndGet_Item()
    {
        string expectedItem = "Item";
        _pendingItemModel.Item = expectedItem;
        Assert.That(_pendingItemModel.Item, Is.EqualTo(expectedItem));
    }

    // Test the PendingItemSharedViewModel Status property
    [Test]
    public void PendingItem_Should_SetAndGet_Status()
    {
        string expectedStatus = "Status";
        _pendingItemModel.Status = expectedStatus;
        Assert.That(_pendingItemModel.Status, Is.EqualTo(expectedStatus));
    }

    [Test]
    public void CanSetAndGet_AnticipatesSpendingOrReceivingOverXXXX_FormAction_Id()
    {
        var model = new ConfirmationViewModel
        {
            Id = 987,
            Action = FormAction.Close,
            AnticipatesSpendingOrReceivingOverXXXX = "yes"
        };

        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.EqualTo(987));
            Assert.That(model.Action, Is.EqualTo(FormAction.Close));
            Assert.That(model.AnticipatesSpendingOrReceivingOverXXXX, Is.EqualTo("yes"));
        });
    }

    [Test]
    public void DefaultValues_ShouldBeNullOrDefault()
    {
        var model = new ConfirmationViewModel();

        Assert.Multiple(() =>
        {
            Assert.That(model.Id, Is.Null);
            Assert.That(model.Action, Is.Null);
            Assert.That(model.AnticipatesSpendingOrReceivingOverXXXX, Is.Null);
            Assert.That(model.PendingItems, Is.Null);
            Assert.That(model.IsSubmission, Is.Null);
            Assert.That(model.ExecutedOn, Is.EqualTo(default(DateTime)));
        });
    }

    // Dispose method (if required)
    public void Dispose()
    {
        // Clean up any resources if necessary
    }
}
