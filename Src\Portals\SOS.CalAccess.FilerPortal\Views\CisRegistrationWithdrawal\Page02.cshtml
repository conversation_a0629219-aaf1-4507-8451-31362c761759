@using System.Text.Json
@using SOS.CalAccess.FilerPortal.Models.Localization
@using SOS.CalAccess.FilerPortal.Models.Registrations.CisRegistrationWithdrawal
@using SOS.CalAccess.Models.Common
@using SOS.CalAccess.Models.FilerRegistration.Registrations
@using SOS.CalAccess.UI.Common.Models
@using SOS.CalAccess.UI.Common.Enums
@inject IHtmlLocalizer<SharedResources> Localizer

@model CisRegistrationWithdrawalPage02ViewModel
@{
    var progressItem1Name = ViewData["ProgressItem1Name"]?.ToString() ?? "";
    var progressItem2Name = ViewData["ProgressItem2Name"]?.ToString() ?? "";
    var progressItems = new List<ProgressItem>
    {
        new ProgressItem(progressItem1Name, true, false),
        new ProgressItem(progressItem2Name, false, true)
    };
    var progressBar = new ProgressBar(progressItems);

    var continueBtnResourceConstant = Model.IsAttest
        ? ResourceConstants.Attest : ResourceConstants.SendForAttestation;
}

<partial name="_LayoutProgressbar" model="progressBar" />

@Html.StepHeader(SharedLocalizer, ResourceConstants.WithdrawalCisPage02Header)

@using (Html.BeginForm("Page02", "CisRegistrationWithdrawal", FormMethod.Post))
{
    @Html.HiddenFor(m => m.IsAttest)

    @if (Model.IsAttest)
    {
        @Html.CheckBoxFor(
            Localizer,
            m => m.IsCertify,
            Localizer[ResourceConstants.WithdrawalCisPage02Checkbox].Value
        )

        <hr />
        <div class="d-flex justify-content-between">
            <div class="fw-bold">@Localizer[ResourceConstants.CandidateName].Value</div>
            <div class="fw-light">@Html.StringHtml(Model.Name)</div>
        </div>
        <hr />
        <div class="d-flex justify-content-between">
            <div class="fw-bold">@Localizer[ResourceConstants.ExecutedOn].Value</div>
            <div class="fw-light">@Model.ExecutedOn?.ToString("MM/dd/yyyy")</div>
        </div>
        <hr />
    }
    else
    {
        <h4 class="my-4">@Localizer[ResourceConstants.WithdrawalCisPage02NonCandBody01].Value</h4>
        <p class="mb-4">@Localizer[ResourceConstants.WithdrawalCisPage02NonCandBody02].Value</p>
        <p class="mb-4">@Localizer[ResourceConstants.WithdrawalCisPage02NonCandBody03].Value</p>
    }

    <div class="pdf-preview">
        <a href="#" class="btn btn-link mb-5 d-flex align-items-center" style="text-decoration: none">
            <span class="e-icons e-export-pdf e-large"></span>
            <span class="ms-2">@Localizer[ResourceConstants.PreviewPdf].Value</span>
        </a>
    </div>

    <div class=" mb-3 bottombuttonscontainer">
        <div class="d-flex justify-content-between">
            <div>
                <button type="submit" class="btn btn-outline-primary" name="Action" value="@FormAction.Previous">
                    @Localizer[ResourceConstants.Previous].Value
                </button>
                <button id="submitButton"
                        type="submit"
                        class="btn btn-primary me-2"
                        name="Action"
                        value="@FormAction.Continue"
                        @(Model.IsAttest ? "disabled" : null)>
                    @Localizer[continueBtnResourceConstant].Value
                </button>
            </div>
            <div>
                <partial name="_CancelDraftButton" model="Model.Id" />
                <button type="submit" class="btn btn-outline-primary me-2" name="Action" value="@FormAction.SaveAndClose">
                    @Localizer[ResourceConstants.SaveAndClose].Value
                </button>
            </div>
        </div>
    </div>
}


<script>
    document.addEventListener("DOMContentLoaded", function () {
        var checkbox = document.getElementById("@Html.IdFor(m => m.IsCertify)");
        var submitButton = document.getElementById("submitButton");

        if (checkbox && submitButton) {
            submitButton.disabled = !checkbox.checked;

            checkbox.addEventListener("change", function () {
                submitButton.disabled = !this.checked;
            });
        }
    });
</script>
