using SOS.CalAccess.Models.FilerDisclosure.Filings;

namespace SOS.CalAccess.Data.FilerDisclosure.Filings;

#region Design Notes
/// <h4>Design Description</h4>
/// <p>
/// Defines database interactions for the FilingPeriod table.
/// </p>
/// <p>
/// Architectural Design: This repository represents a Data Service invoked by Business Services.
/// </p>
/// <h4>Assumptions</h4>
/// <p>
/// Every entity has an Id field containing a unique identifier that can be used to retrieve a single record.
/// </p>
/// <h4>Business Function</h4>
/// <p>
/// The purpose of this repository is to handle data persistence functions acting as an intermediary between business logic and data storage.
/// </p>
/// <h4>Feature</h4>
/// <p>
/// <ul>
/// <li>FD-01: Enter an Activity Report</li>
/// <li>FD-02: Modify an Activity Report</li>
/// <li>FD-03: Upload an Activity Report</li>
/// </ul>
/// </p>
/// <h4>Referenced Services</h4>
/// <p>
/// This section lists and describes the services and operations referenced by this repository.
/// </p>
/// | Service                 | Operation                    | Description                         |
/// | ----------------------- | ---------------------------- | ----------------------------------- |
/// | N/A | N/A | N/A |
#endregion

public interface IFilingPeriodRepository : IRepository<FilingPeriod, long>
{
    Task<DateTime> GetFilingPeriodStartDateForFiling(long filingId);

    /// <summary>
    /// Find all unreported filing periods for SMO campaign statement
    /// </summary>
    /// <param name="filerId">ID of the filer</param>
    /// <returns>A collection of unreported filing period</returns>
    Task<List<FilingPeriod>> FindAllUnreportedFilingPeriodsSmoCampaignStatementByFiler(long filerId);

    // <summary>
    /// Find filing period id for given dates
    /// </summary>
    /// <param name="startDate"> ReportingPeriod startdate/param>
    /// <param name="endDate"> ReportingPeriod enddate/param>
    /// <returns> filing period ID</returns>
    Task<long> GetFilingPeriodByStartDateAndEndDate(DateTime startDate, DateTime endDate);

    /// <summary>
    /// Retrieves all filing periods associated with a specific legislative session.
    /// </summary>
    /// <param name="ids">The list of unique identifiers of the legislative session.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains a list of <see cref="FilingPeriod"/> objects
    /// associated with the specified legislative session.
    /// </returns>
    Task<List<FilingPeriod>> GetFilingPeriodsByLegislativeSessionIds(List<long> ids);

}
