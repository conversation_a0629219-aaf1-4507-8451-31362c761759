@using System.Text.Json
@model OtherPaymentViewModel
@using Microsoft.AspNetCore.Mvc.Localization
@using FilingSummaryTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingSummaryType;
@using FilingTypeModel = SOS.CalAccess.Models.FilerDisclosure.Filings.FilingType;
@inject IHtmlLocalizer<SharedResources> Localizer

@{
    var localizerRef = (string label) => Localizer["FilerPortal.Transaction.NewOtherPayment." + label].Value;
    ViewData[LayoutConstants.Title] = localizerRef("Title");
    var previousPage = Url.Action("NewOtherPaymentPayee", "Transaction", new { filerId = Model.FilerId, filingId = Model.FilingId, returnUrl = Model.ReturnUrl, reportType = ViewBag.ReportType });
    var address = $"{@Model.Contacts![0].Street}{(string.IsNullOrEmpty(Model.Contacts![0].Street2) ? "" : " " + Model.Contacts![0].Street2) + ", "}{@Model.Contacts![0].City}, {@Model.Contacts![0].State}, {@Model.Contacts![0].ZipCode}";
    var returnUrl = Model.ReturnUrl ?? Url.Action("Index", "Filer", new { Action = "" });
    var popupModel = new ActionsLobbiedMultiSelectPopupViewModel
            {
                OfficialPositions = new Dictionary<long, string>(),
                ShowOfficialPosition = false
            };
    var billTablesModel = new LegislativeBillTablesViewModel
        {
            LegislationAssyBillGridModel = Model.LegislationAssyBillGridModel,
            LegislationSenateBillGridModel = Model.LegislationSenateBillGridModel,
            PopupModel = null
        };

    // Grab payment code reference for displaying/hiding additional form fields
    var advertisingCode = Model.PaymentCodes.Where(type => type.Value == "Advertising (A)").Select(type => type.Key).FirstOrDefault();
    var otherCode = Model.PaymentCodes.Where(type => type.Value == "Other (O)").Select(type => type.Key).FirstOrDefault();
}

<h1>@localizerRef("Title")</h1>
<h2>@localizerRef("Header")</h2>
<div class="p-5 mt-4 d-flex border border-gray w-100">
    <div class="d-flex flex-column w-100">
        <h2>@localizerRef("AboutThePayment")</h2>
        <p>@localizerRef("Subtitle")</p>

        <table class="mb-3 table border-bottom border-gray">
            <thead class="border-gray">
                <tr>
                    <th scope="col">@localizerRef("NameOfPayee")</th>
                    <th scope="col">@localizerRef("PayeeType")</th>
                    <th scope="col">@localizerRef("Address")</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>@Model.Contacts![0].DisplayName()</td>
                    <td>@Model.Contacts![0].GetContactType()</td>
                    <td>@address</td>
                </tr>
            </tbody>
        </table>

        @using (Html.BeginForm("NewOtherPayment", "Transaction", FormMethod.Post))
        {
            @Html.HiddenFor(m => m.FilerId)
            @Html.HiddenFor(m => m.FilingId)
            @Html.HiddenFor(m => m.ContactId)
            @Html.HiddenFor(m => m.ReturnUrl)
            @Html.HiddenFor(m => m.ReportType)
            @Html.HiddenFor(m => m.TransactionId)

            <div class="mb-4 col-lg-4">
                @Html.Dropdown(SharedLocalizer, "PaymentCode", localizerRef("PaymentCode"), Model.PaymentCodes, Model.PaymentCode.ToString() ?? "", Localizer["Common.Select"].Value, null, false)
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "PaymentCodeId", localizerRef("PaymentCode"))
            </div>

            <div class="mb-3 col-lg-12" id="paymentCodeDescriptionContainer" aria-hidden="true" style="display: none;">
                @Html.TextAreaFor(Localizer, m => m.PaymentCodeDescription!, localizerRef("Description"), "", null, false)
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "OtherPaymentDescription", localizerRef("Description"))
            </div>

            <div class="mb-4 col-lg-12" id="advertCheckboxes" aria-hidden="true" style="display: none;">
                <p>@localizerRef("AdvertCheckboxes")</p>
                @Html.CheckBoxFor(SharedLocalizer, m => m.AdvertAdminActions, localizerRef("AdminActions"), false)
                @Html.CheckBoxFor(SharedLocalizer, m => m.AdvertLegislation, localizerRef("Legislation"), false)
                @Html.CheckBoxFor(SharedLocalizer, m => m.AdvertOther, localizerRef("Other"), false)
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "LobbyingAdvertisementSubjects", localizerRef("AdvertCheckboxes"))
            </div>

            <div class="mb-4 col-lg-12" id="legislationSection" aria-hidden="true" style="display: none;">
                <h3 class="mb-4 pb-2 border-bottom border-gray">@localizerRef("Legislation")</h3>
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "ErrFD0822.1.1", "")
                <partial name="_LegBillTables" model="@billTablesModel" />
            </div>

            <div class="mb-4 col-lg-12" id="adminSection" aria-hidden="true" style="display: none;">
                <h3 class="mb-4 pb-2 border-bottom border-gray">@localizerRef("AdminActions")</h3>
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "ErrFD0822.1.2", "")
                <div class="mb-3">
                    <a data-bs-toggle="modal"
                       data-bs-target="#agencySelectionModal"
                       class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus-circle-fill"></i>
                        @Localizer["Common.AddNew"]
                    </a>

                    <partial name="_AgencyMultiSelectPopup" model="@popupModel"/>
                </div>
                <div class="mb-4">
                    @await Html.PartialAsync("_SmallGrid", Model.AdministrativeActionGridModel)
                </div>
            </div>

            <div class="mb-4 col-lg-12" id="advertOtherActionsLobbiedContainer" style="display: none;">
                <h3 class="mb-4 pb-2 border-bottom border-gray">@localizerRef("Other")</h3>
                <div>
                    @Html.TextAreaFor(Localizer, m => m.AdvertOtherActionsLobbied!, localizerRef("OtherActionsLobbied"), "", null, false)
                    @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "OtherActionDescription", localizerRef("Other"))
                </div>
            </div>

            <div class="mb-4 col-lg-4">
                @Html.LabelFor(m => m.Amount, localizerRef("Amount"), new { @class = "form-label" })
                @Html.EJS().NumericTextBox("Amount").Format("c2").Value(Model.Amount).FloatLabelType(Syncfusion.EJ2.Inputs.FloatLabelType.Auto).ShowSpinButton(false).HtmlAttributes(new Dictionary<string, object> { { "aria-labelledby", "Amount" } }).Change("amountChanged").Render()
                @HtmlHelpers.RenderValidationError(Model.Messages.Validations, "QuarterlyAmount", localizerRef("Amount"))
            </div>

            <div class="mb-4 col-lg-12 d-flex flex-column">
                @Html.LabelFor(m => m.CumulativeAmount, localizerRef("CumulativeAmount"), new { @class = "form-label" })
                <div class="col-lg-4">
                    @Html.EJS().NumericTextBox("CumulativeAmount").Format("c2").Value(Model.CumulativeAmount).ShowSpinButton(false).HtmlAttributes(new Dictionary<string, object> { { "style", "cursor: not-allowed;" }, { "aria-labelledby", "CumulativeAmount" } }).Readonly(true).Render()
                </div>  
            </div>

            <div class="d-flex flex-row justify-content-between mt-4">
                <div class="d-flex flex-row">
                    <a href="@previousPage" class="btn btn-primary btn-sm me-2 text-white">@Localizer["Common.Previous"]</a>
                    <button type="submit" class="btn btn-warning btn-sm ms-auto text-white">@Localizer["Common.Save"]</button>
                </div>
                <div class="d-flex flex-row">
                    <a class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#cancelConfirmModal">@Localizer["Common.Cancel"]</a>
                </div>
            </div>
        }
    </div>
</div>

@{
    var cancelModal = new CancelConfirmModal(
        Title: "Confirm Cancellation",
        Body: "Are you sure you want to cancel? Any unsaved changes will be lost.",
        CloseButtonText: "No, Stay Here",
        SubmitButtonText: "Yes, Cancel",
        ActionUrl: returnUrl!
    );
}
<partial name="_CancelConfirmModal" model="cancelModal" />

<script>
    // Update cumulative amount field based on value entered into Amount
    function amountChanged(args) {
        var amount = args.value || 0;
        var baseCumulativeAmount = @(
            Model.TransactionId == null
            ? (Model.CumulativeAmount ?? 0)
            : 0
            );
        var cumulativeObj = document.getElementById('CumulativeAmount').ej2_instances[0];
        cumulativeObj.value = baseCumulativeAmount + amount;
    }
</script>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const paymentCode = document.getElementById("PaymentCode");
        const paymentCodeDescriptionContainer = document.getElementById("paymentCodeDescriptionContainer");
        const paymentCodeDescription = document.getElementById("PaymentCodeDescription");
        const advertCheckboxes = document.getElementById("advertCheckboxes");
        const advertAdminActions = document.getElementById("AdvertAdminActions");
        const advertLegislation = document.getElementById("AdvertLegislation");
        const advertOther = document.getElementById("AdvertOther");
        const adminSection = document.getElementById("adminSection");
        const legislationSection = document.getElementById("legislationSection");
        const advertOtherActionsLobbiedContainer = document.getElementById("advertOtherActionsLobbiedContainer");
        const advertOtherActionsLobbied = document.getElementById("AdvertOtherActionsLobbied");
        const advertising = "@advertisingCode";
        const other = "@otherCode";


        function toggleFields() {

            if (paymentCode.value === other) {
                paymentCodeDescriptionContainer.style.display = "block";
                paymentCodeDescriptionContainer.setAttribute("aria-hidden", paymentCode.value !== other);
            } else {
                paymentCodeDescriptionContainer.style.display = "none";
                paymentCodeDescriptionContainer.setAttribute("aria-hidden", paymentCode.value !== other);

                // Clear the description field if user switches to another payment code
                paymentCodeDescription.value = "";
            }

            if (paymentCode.value === advertising) {
                advertCheckboxes.style.display = "block";
                advertCheckboxes.setAttribute("aria-hidden", paymentCode.value !== advertising);
            } else {
                advertCheckboxes.style.display = "none";
                advertCheckboxes.setAttribute("aria-hidden", paymentCode.value !== advertising);

                // Reset the advertisement fields if user switches to another payment code
                advertAdminActions.checked = false;
                advertLegislation.checked = false;
                advertOther.checked = false;
                advertOtherActionsLobbied.value = "";
            }

            if (advertAdminActions.checked) {
                adminSection.style.display = "block";
                adminSection.setAttribute("aria-hidden", !advertAdminActions.checked);
            } else {
                adminSection.style.display = "none";
                adminSection.setAttribute("aria-hidden", !advertAdminActions.checked);
            }

            if (advertLegislation.checked) {
                legislationSection.style.display = "block";
                legislationSection.setAttribute("aria-hidden", !advertLegislation.checked);
            } else {
                legislationSection.style.display = "none";
                legislationSection.setAttribute("aria-hidden", !advertLegislation.checked);
            }

            if (advertOther.checked) {
                advertOtherActionsLobbiedContainer.style.display = "block";
                advertOtherActionsLobbiedContainer.setAttribute("aria-hidden", !advertOther.checked);
            } else {
                advertOtherActionsLobbiedContainer.style.display = "none";
                advertOtherActionsLobbiedContainer.setAttribute("aria-hidden", !advertOther.checked);

                // Clear the text field if user unchecks Other
                advertOtherActionsLobbied.value = "";
            }
        }
        toggleFields();

        paymentCode.addEventListener("change", toggleFields);
        advertAdminActions.addEventListener("change", toggleFields);
        advertLegislation.addEventListener("change", toggleFields);
        advertOther.addEventListener("change", toggleFields);
    });

    function handleSelectedAgencies(agencies) {
        addAdministrativeAction(agencies);
    }

    function addAdministrativeAction(agencies) {
        fetch('/Transaction/AddAdministrativeAction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(agencies)
        })
            .then(res => res.json())
            .then(data => {
                const grid = document.getElementById("Grid_AdministrativeAction")?.ej2_instances?.[0];
                if (grid) {
                    grid.dataSource = data;
                    grid.refresh();
                }
            });
    }
</script>
