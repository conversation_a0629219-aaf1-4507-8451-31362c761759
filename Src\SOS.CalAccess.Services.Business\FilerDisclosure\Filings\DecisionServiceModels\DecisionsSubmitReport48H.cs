namespace SOS.CalAccess.Services.Business.FilerDisclosure.Filings.DecisionServiceModels;
public class DecisionsSubmitReport48H
{
    public required AmendmentExplanationWrapper AmendmentExplanation { get; set; }
    public required AddNewTransactionWrapper AddNewTransaction { get; set; }
}

public class AmendmentExplanationWrapper
{
    public string? AmendmentExplanation { get; set; }
}

public class AddNewTransactionWrapper
{
    public required List<AddNewTransaction> AddNewTransaction { get; set; }
}

public class AddNewTransaction
{
    public string? LobbyingFirmName { get; set; }
    public string? PhoneNumber { get; set; }
    public AddNewTransactionAddress? Address1 { get; set; }
    public DateTime? FirmHiringDate { get; set; }
    public List<string?>? LegislativeNumbers { get; set; }
    public decimal? Amount { get; set; }
}

public class AddNewTransactionAddress
{
    public string? City { get; set; }
    public string? Street { get; set; }
    public string? Street2 { get; set; }
    public string? Zip { get; set; }
    public string? Type { get; set; }
    public string? Country { get; set; }
    public string? State { get; set; }
    public bool? IsMailingAddress { get; set; }
}
