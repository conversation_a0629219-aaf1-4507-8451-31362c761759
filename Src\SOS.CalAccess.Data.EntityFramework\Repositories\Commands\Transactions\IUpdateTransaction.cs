// <copyright file="IUpdateTransaction.cs" company="MapLight">
// Copyright (c) MapLight. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Data.EntityFramework.Audit;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations;
using SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Operations.Decisions;
using SOS.CalAccess.Foundation.Utils;
using SOS.CalAccess.Models.FilerDisclosure.Transactions;

namespace SOS.CalAccess.Data.EntityFramework.Repositories.Commands.Transactions;

/// <summary>
/// Command processor interface for creating a new transaction.
/// </summary>
public interface IUpdateTransaction : ICommand<IUpdateTransactionCommand, IResult<Transaction>>;

/// <summary>
/// Command processor implementation for creating a new transaction.
/// </summary>
/// <param name="db">Database context.</param>
/// <param name="decisions">Business logic engine.</param>
/// <param name="auditService">High level audit facilities.</param>
public sealed class UpdateTransaction(
    DatabaseContext db,
    IDecisionsService decisions,
    IAuditService auditService,
    IDateTimeSvc dateTimeSvc) : IUpdateTransaction
{
    /// <inheritdoc />
    public async ValueTask<IResult<Transaction>> Execute(
        IUpdateTransactionCommand input, CancellationToken cancellationToken = default)
    {
        var transaction = await db.Transactions
            .FirstOrDefaultAsync(t => t.Id == input.Id, cancellationToken);

        if (transaction is not { } validTransaction)
        {
            return new Failure<Transaction>.NotFound("Requested target transaction was not found");
        }

        var contact = await db.FilerContacts
            .FirstOrDefaultAsync(c => c.Id == input.Data.ContactId, cancellationToken);

        if (contact is not { } validContact)
        {
            return new Failure<Transaction>.DependencyFailed("Required contact reference was not found");
        }

        _ = await decisions.Execute(new("Transaction.Update", Context: transaction), cancellationToken);

        if (input.Apply(validTransaction, validContact).Unwrap(out var updated, out var failure))
        {
            return failure;
        }

        await db.SaveChangesAsync(cancellationToken);

        var action = new BusinessAction("Update", transaction.GetType().Name, transaction.Id.ToString(), dateTimeSvc.GetCurrentDateTime());
        await auditService.LogAction(action, cancellationToken);

        return new Success<Transaction>(updated);
    }
}
