@using SOS.CalAccess.Foundation.Logging;
@using SOS.CalAccess.Foundation.Utils
@inject IDateTimeSvc DateTimeSvc
@model ErrorViewModel
@{
    ViewData["Title"] = "Error";
}

<h1 class="text-danger">Error.</h1>
<h2 class="text-danger">An error occurred while processing your request.</h2>

@if (Model.ShowRequestId)
{
    <p>
        <strong>Request ID:</strong> <code>@Model.RequestId</code>
    </p>
}

<p>CorrelationId: <code>@LoggingContext.GetValue(LoggingConstants.CorrelationId)?.ToString()</code></p>
<p>UserId: <code>@LoggingContext.GetValue(LoggingConstants.InitiatingUserId)?.ToString()</code></p>
<p>Timestamp: <code>@DateTimeSvc.GetCurrentDateTime().ToString("s")</code></p>
