using Microsoft.EntityFrameworkCore;
using SOS.CalAccess.Models.FilerRegistration.Elections;

namespace SOS.CalAccess.Data.EntityFramework.Tests.Repositories.FilerRegistration.Elections;
public static class SeedElectionsData
{
    private static Election Templated(string electionName, string year = "2025", long electionId = 1, string districtName = "Test District 1", string officeName = "Test Office")
    {
        return new()
        {
            Name = electionName,
            ElectionDate = new DateTime(2025, 6, 1, 12, 0, 0, DateTimeKind.Local),
            Id = electionId,
            ElectionType = new ElectionType() { Name = "Primary" },
        };
    }

    public static async Task PrepareElections(this DatabaseContext context)
    {
        var office = await context.Set<Office>().FirstOrDefaultAsync(o => o.Id == 1);
        if (office == null)
        {
            office = new Office { Id = 1, Name = "testOffice" };
            context.Set<Office>().Add(office);
        }

        var district = await context.Set<District>().FirstOrDefaultAsync(d => d.Id == 1);
        if (district == null)
        {
            district = new District { Id = 1, Name = "testDistrict" };
            context.Set<District>().Add(district);
        }

        await context.SaveChangesAsync();

        var electionRace = await context.Set<ElectionRace>().FirstOrDefaultAsync(er => er.DistrictId == 1 && er.ElectionId == 1 && er.OfficeId == 1);
        if (electionRace == null)
        {
            electionRace = new ElectionRace { DistrictId = 1, ElectionId = 1, OfficeId = 1 };
            context.ElectionRaces.Add(electionRace);
        }

        var election = await context.Set<Election>().FirstOrDefaultAsync(e => e.Name == "Test Election");
        if (election == null)
        {
            election = Templated(electionName: "Test Election");
            context.Elections.Add(election);
        }
        else
        {
            context.Attach(election);
        }

        var election2 = Templated(electionName: "Test Election 2", "2025", 2, districtName: "Test District 2", officeName: "Test Office 2");
        context.Elections.Add(election2);
        List<PoliticalParty> parties = [new PoliticalParty() { Name = "Test Party 1" }, new PoliticalParty() { Name = "Test Party 2" }];

        context.Set<PoliticalParty>().AddRange(parties);

        await context.SaveChangesAsync();
    }
}
